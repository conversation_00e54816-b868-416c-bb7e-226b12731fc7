import importlib.util
import json
import time
import redis
import os
import threading
import signal
from typing import List, Dict, Any, Callable, Optional
import sys
import codecs
from collections import defaultdict
from contextlib import contextmanager

# 信号冗余
Signal_Redundancy = 4

# 设置UTF-8编码环境
os.environ['PYTHONIOENCODING'] = 'utf-8'
# 修复Windows下的编码问题
if sys.platform.startswith('win'):
    try:
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
    except AttributeError:
        # 如果buffer不存在，使用其他方法
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 添加启动日志
print("[选股引擎] ========== 开始启动 ==========")
print(f"[选股引擎] 当前工作目录: {os.getcwd()}")
print(f"[选股引擎] Python版本: {sys.version}")
print(f"[选股引擎] 脚本路径: {__file__}")
print(f"[选股引擎] 命令行参数: {sys.argv}")
print("[选股引擎] ================================")

class PerformanceTracker:
    """性能跟踪器 - 用于监控各个阶段的耗时"""
    
    def __init__(self):
        self.stage_times = defaultdict(list)  # 各阶段耗时记录
        self.current_stage = None
        self.stage_start_time = None
        self.total_start_time = time.time()
        
    @contextmanager
    def track_stage(self, stage_name: str):
        """上下文管理器，用于跟踪单个阶段的耗时"""
        start_time = time.time()
        self.current_stage = stage_name
        self.stage_start_time = start_time
        
        try:
            yield
        finally:
            end_time = time.time()
            duration = end_time - start_time
            self.stage_times[stage_name].append(duration)
            
            # 实时输出性能信息
            print(f"[性能跟踪] {stage_name}: {duration:.3f}秒")
    
    def track_symbol_performance(self, symbol_code: str, kline_fetch_time: float, calculation_time: float):
        """跟踪单个品种的性能 - 保持为正常输出"""
        print(f"[性能跟踪] 品种 {symbol_code}:")
        print(f"  - K线获取耗时: {kline_fetch_time:.3f}秒")
        print(f"  - 形态计算耗时: {calculation_time:.3f}秒")
        print(f"  - 总耗时: {kline_fetch_time + calculation_time:.3f}秒")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能统计摘要"""
        summary = {}
        total_time = time.time() - self.total_start_time
        
        for stage_name, times in self.stage_times.items():
            if times:
                summary[stage_name] = {
                    'count': len(times),
                    'total_time': sum(times),
                    'avg_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times)
                }
        
        summary['total_elapsed'] = total_time
        return summary
    
    def print_performance_summary(self):
        """打印性能统计摘要 - 保持为正常输出"""
        summary = self.get_performance_summary()
        
        print("\n" + "="*60)
        print("[性能跟踪] ========== 性能统计摘要 ==========")
        print(f"[性能跟踪] 总运行时间: {summary['total_elapsed']:.3f}秒")
        
        for stage_name, stats in summary.items():
            if stage_name != 'total_elapsed':
                print(f"[性能跟踪] {stage_name}:")
                print(f"  - 执行次数: {stats['count']}")
                print(f"  - 总耗时: {stats['total_time']:.3f}秒")
                print(f"  - 平均耗时: {stats['avg_time']:.3f}秒")
                print(f"  - 最短耗时: {stats['min_time']:.3f}秒")
                print(f"  - 最长耗时: {stats['max_time']:.3f}秒")
                print(f"  - 占总时间比例: {(stats['total_time']/summary['total_elapsed']*100):.1f}%")
        
        print("="*60 + "\n")

class StockSelectionEngine:
    def __init__(self, config: Dict[str, Any]):
        """
        初始化选股引擎
        
        Args:
            config: 配置字典，包含以下字段:
                - api_base_url: API基础URL
                - candidate_type: 候选类型 (0=A股, 1=期货, 2=美股)
                - strategy_module_path: 策略模块路径
                - threshold: 阈值
                - values: 目标形态值列表
                - fetch_k_count: 获取K线数量
                - test_mode: 是否为测试模式
                - redis_host: Redis主机
                - redis_port: Redis端口
                - redis_db: Redis数据库
        """
        # 初始化性能跟踪器
        self.performance_tracker = PerformanceTracker()
        
        # 测试模式标志
        self.test_mode = config.get('test_mode', False)
        
        self.candidate_type = config.get('candidate_type', 0)
        self.strategy_module_path = config.get('strategy_module')

        self.threshold = config.get('threshold', 0)
        self.values = config.get('values', [])
        # Debug prints added in __init__
        print(f"[选股引擎] 接收到的参数:")
        print(f"  - test_mode: {self.test_mode}")
        print(f"  - candidate_type: {self.candidate_type}")
        print(f"  - strategy_module_path: {self.strategy_module_path}")
        print(f"  - threshold: {self.threshold}")
        print(f"  - values: {self.values}")
        print(f"  - values类型: {type(self.values)}")
        print(f"  - values长度: {len(self.values) if self.values else 0}")
        

        self.fetch_k_count = len(self.values) + Signal_Redundancy

        self.tdx_host = config.get('tdx_host', 'localhost')
        self.tdx_port = config.get('tdx_port', 5000)

        # Redis配置
        self.task_id = config.get('task_id')
        self.redis_host = config.get('redis_host', 'localhost')
        self.redis_port = config.get('redis_port', 6379)
        self.redis_password = config.get('redis_password')
        self.redis_db = config.get('redis_db', 0)
        
        # 初始化Redis连接
        self.redis_client = None
        self.control_pubsub = None
        self.stop_requested = False
        if self.task_id:
            try:
                self.redis_client = redis.StrictRedis(
                    host=self.redis_host,
                    port=self.redis_port,
                    password=self.redis_password,
                    db=self.redis_db,
                    decode_responses=True
                )
                print(f"[选股引擎] Redis连接成功: {self.redis_host}:{self.redis_port}")
                
                # 初始化控制信号监听
                self.control_pubsub = self.redis_client.pubsub()
                self.control_pubsub.subscribe(f'stock_selection_control:{self.task_id}')
                print(f"[选股引擎] 已订阅控制频道: stock_selection_control:{self.task_id}")
                
            except Exception as e:
                print(f"[选股引擎] Redis连接失败: {e}")
        
        # 动态加载策略模块
        self.strategy_module = self.load_strategy_module()
        
        # API配置（从config.json获取）
        self.api_base_url = f"http://{self.tdx_host}:{self.tdx_port}"
        
        print(f"[选股引擎] API服务地址: {self.api_base_url}")
        
        # 进度状态
        self.total_symbols = 0
        self.processed_symbols = 0
        self.selected_symbols = []
        self.last_progress_percent = 0  # 记录上次报告的进度百分比
        
        # 启动控制信号监听线程
        if self.control_pubsub:
            self.control_thread = threading.Thread(target=self._listen_for_control_signals, daemon=True)
            self.control_thread.start()
            print(f"[选股引擎] 控制信号监听线程已启动")
    
    def _listen_for_control_signals(self):
        """监听Redis控制信号"""
        print(f"[选股引擎] 开始监听控制信号...")
        try:
            if self.control_pubsub:
                # 使用非阻塞方式监听消息
                while True:
                    try:
                        # 使用get_message()非阻塞方式获取消息
                        message = self.control_pubsub.get_message(timeout=0.1)  # 100ms超时
                        if message and message['type'] == 'message':
                            try:
                                data = json.loads(message['data'])
                                print(f"[选股引擎] 收到控制信号: {data}")
                                
                                if data.get('action') == 'stop':
                                    print(f"[选股引擎] 收到停止信号，准备中止选股...")
                                    self.stop_requested = True
                                    # 发送中止结果
                                    self._send_stop_result()
                                    break
                            except json.JSONDecodeError as e:
                                print(f"[选股引擎] 解析控制信号失败: {e}")
                            except Exception as e:
                                print(f"[选股引擎] 处理控制信号失败: {e}")
                        
                        # 检查是否收到停止请求（通过其他方式）
                        if self.stop_requested:
                            print(f"[选股引擎] 检测到停止请求，准备中止选股...")
                            self._send_stop_result()
                            break
                            
                    except Exception as e:
                        print(f"[选股引擎] 监听控制信号时出错: {e}")
                        break
        except Exception as e:
            print(f"[选股引擎] 控制信号监听线程异常: {e}")
        finally:
            print(f"[选股引擎] 控制信号监听线程结束")
    
    def _send_stop_result(self):
        """发送中止结果到Redis"""
        try:
            if not self.redis_client or not self.task_id:
                return
            
            # 构建中止结果数据
            stop_result = {
                'taskId': self.task_id,
                'action': 'stopped',
                'selectedSymbols': self.selected_symbols,
                'totalSymbols': self.total_symbols,
                'processedSymbols': self.processed_symbols,
                'selectedCount': len(self.selected_symbols),
                'executionTime': time.time(),
                'timestamp': time.time()
            }
            
            # 发送到完成频道
            channel = f'stock_selection_complete:{self.task_id}'
            self.redis_client.publish(channel, json.dumps(stop_result))
            print(f"[选股引擎] 已发送中止结果到Redis: {channel}")
            print(f"[选股引擎] 中止结果数据: {json.dumps(stop_result)}")
            
        except Exception as e:
            print(f"[选股引擎] 发送中止结果失败: {e}")
    
    def _check_stop_requested(self):
        """检查是否收到停止请求"""
        return self.stop_requested
    
    def load_strategy_module(self):
        """动态加载策略模块"""
        if not self.strategy_module_path:
            raise ValueError("策略模块路径未指定")

        # 策略模块名称映射
        strategy_name_mapping = {
            'shape_matching': 'pattern_strategy.py',
            'pattern_strategy': 'pattern_strategy.py',
            'ma_cross': 'ma_cross_strategy.py'
        }
        
        # 根据策略模块名称获取文件名
        strategy_name = self.strategy_module_path
        if strategy_name in strategy_name_mapping:
            strategy_name = strategy_name_mapping[strategy_name]
        elif not strategy_name.endswith('.py'):
            strategy_name += '.py'
        
        # 构建完整的文件路径
        strategies_dir = os.path.join(os.path.dirname(__file__), 'strategies')
        module_path = os.path.join(strategies_dir, strategy_name)
        
        if not os.path.exists(module_path):
            raise FileNotFoundError(f"策略模块文件不存在: {module_path}")

        try:
            # 动态导入策略模块
            spec = importlib.util.spec_from_file_location(
                "strategy_module", 
                module_path
            )
            if spec is None:
                raise RuntimeError("无法创建模块规范")

            strategy_module = importlib.util.module_from_spec(spec)
            if spec.loader is None:
                raise RuntimeError("模块加载器为空")

            spec.loader.exec_module(strategy_module)

            # 验证策略模块接口
            if not hasattr(strategy_module, 'select_stock'):
                raise ValueError("策略模块必须包含 select_stock 函数")

            return strategy_module

        except Exception as e:
            raise RuntimeError(f"加载策略模块失败: {str(e)}")
    
    def report_progress(self, stage: str, current: Optional[int] = None, total: Optional[int] = None, 
                       selected_count: Optional[int] = None, symbol_info: Optional[str] = None):
        """报告进度"""
        # 计算进度百分比
        progress_percent = 0
        if total and total > 0:
            progress_percent = int((current or 0) * 100 / total)
        
        # 检查是否需要发送Redis进度通知（每1%发送一次，或者首次发送）
        should_send_redis = False
        if progress_percent > self.last_progress_percent or (current and current <= 1):
            should_send_redis = True
            self.last_progress_percent = progress_percent
        
        # 构建进度数据
        progress_data = {
            'taskId': self.task_id,
            'stage': stage,
            'current': current,
            'total': total,
            'selectedCount': selected_count,
            'symbolInfo': symbol_info,
            'progress': progress_percent,
            'timestamp': time.time()
        }
        
        # 发送Redis进度通知
        if should_send_redis and self.redis_client and self.task_id:
            try:
                channel = f'stock_selection_progress:{self.task_id}'
                self.redis_client.publish(channel, json.dumps(progress_data))
                print(f"[选股引擎] 发送Redis进度通知: {progress_percent}% - {stage}")
                print(f"[选股引擎] Redis频道: {channel}")
                print(f"[选股引擎] Redis数据: {json.dumps(progress_data)}")
            except Exception as e:
                print(f"[选股引擎] 发送Redis进度通知失败: {e}")
        else:
            print(f"[选股引擎] 跳过Redis进度通知 - should_send_redis: {should_send_redis}, redis_client: {self.redis_client is not None}, task_id: {self.task_id}")
        
        # 调用原有的进度回调
        #if self.progress_callback:
        #    self.progress_callback(progress_data)
        # 使用 Redis 方式更新进度，不使用回调
        
        # 打印进度信息
        if current and total:
            print(f"[选股引擎] 进度: {progress_percent}% ({current}/{total}) - {stage}")
        else:
            print(f"[选股引擎] 进度: {stage}")
    
    def get_candidate_symbols(self) -> List[Dict[str, Any]]:
        """根据候选类型获取候选品种列表"""
        try:
            self.report_progress('获取候选品种列表')
            
            # 导入tdx_kline_lib模块
            import sys
            import os
            
            # 添加父目录到Python路径，以便导入tdx_kline_lib
            parent_dir = os.path.dirname(os.path.dirname(__file__))
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            
            from tdx_kline_lib import get_stock_list, get_future_list, get_us_stock_list
            
            if self.candidate_type == 0:  # A股
                # 只获取上证和深证股票，不包括创业板、科创板等
                print(f"[选股引擎] 请求上海股票列表")
                sh_symbols = get_stock_list(market_filter='sh')
                print(f"[选股引擎] 获取到上海股票 {len(sh_symbols)} 只")
                
                print(f"[选股引擎] 请求深圳股票列表")
                sz_symbols = get_stock_list(market_filter='sz')
                print(f"[选股引擎] 获取到深圳股票 {len(sz_symbols)} 只")
                
                # 合并上证和深证股票
                symbols = sh_symbols + sz_symbols
                print(f"[选股引擎] 合并后总计 {len(symbols)} 只股票")
                
            elif self.candidate_type == 1:  # 期货
                print(f"[选股引擎] 请求期货列表")
                symbols = get_future_list()
                
            elif self.candidate_type == 2:  # 美股
                print(f"[选股引擎] 请求美股列表")
                symbols = get_us_stock_list()
                
            else:
                raise ValueError(f"不支持的候选类型: {self.candidate_type}")
            
            self.total_symbols = len(symbols)
            print(f"[选股引擎] 获取到 {self.total_symbols} 个候选品种")
            
            return symbols
            
        except Exception as e:
            print(f"[选股引擎] 获取候选品种失败: {str(e)}")
            return []
    

    
    def get_klines(self, symbol: Dict[str, Any], period: str = '1D') -> List[Dict[str, Any]]:
        """获取品种K线数据"""
        with self.performance_tracker.track_stage(f"获取K线数据_{symbol['code']}"):
            try:
                # 导入tdx_kline_lib模块
                import sys
                import os
                
                # 添加父目录到Python路径，以便导入tdx_kline_lib
                parent_dir = os.path.dirname(os.path.dirname(__file__))
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                
                from tdx_kline_lib import get_stock_kline
                
                # 确定需要的K线数量
                required_klines = self.fetch_k_count
                
                # 直接调用库函数获取K线数据
                klines = get_stock_kline(
                    symbol=symbol['code'],
                    period=period,
                    market=symbol.get('market', 'STOCK'),
                    exchange=symbol.get('exchange', ''),
                    k_count=required_klines
                )
                
                # 添加K线数据调试信息
                if klines:
                    print(f"[选股引擎] 获取到 {len(klines)} 根K线数据")
                    # 显示第一根和最后一根K线的日期（北京时间）
                    first_kline = klines[0]
                    last_kline = klines[-1]
                    
                    # 转换时间戳为北京时间
                    import datetime
                    first_time = datetime.datetime.fromtimestamp(first_kline.get('time', 0))
                    last_time = datetime.datetime.fromtimestamp(last_kline.get('time', 0))
                    
                    print(f"[选股引擎] 第一根K线日期: {first_time.strftime('%Y-%m-%d %H:%M:%S')} (北京时间)")
                    print(f"[选股引擎] 最后一根K线日期: {last_time.strftime('%Y-%m-%d %H:%M:%S')} (北京时间)")
                    print(f"[选股引擎] K线时间跨度: {(last_time - first_time).days} 天")
                else:
                    print(f"[选股引擎] 未获取到K线数据")
                
                return klines
                    
            except Exception as e:
                print(f"[选股引擎] 获取K线数据失败 {symbol['code']}: {str(e)}")
                return []

    def run_test_mode(self) -> List[str]:
        """运行测试模式 - 模拟进度更新从1%到100%"""
        import time

        print("[选股引擎] 进入测试模式，开始模拟进度更新")

        # 模拟总共100个品种需要处理
        total_symbols = 100
        selected_count = 0

        # 模拟选中的品种
        test_symbols = [
            {'code': '000001', 'name': '平安银行'},
            {'code': '000002', 'name': '万科A'},
            {'code': '000858', 'name': '五粮液'},
            {'code': '600036', 'name': '招商银行'},
            {'code': '600519', 'name': '贵州茅台'}
        ]

        # 从1%到100%，每秒递增1%
        for progress in range(1, 101):
            # 检查是否收到停止请求
            if self._check_stop_requested():
                print(f"[选股引擎] 测试模式收到停止请求，中止选股")
                # 在发送中止结果前，先设置选中的品种
                self.selected_symbols = test_symbols[:selected_count]
                self.total_symbols = total_symbols
                self.processed_symbols = progress
                # 发送中止结果
                self._send_stop_result()
                return [symbol['code'] for symbol in test_symbols[:selected_count]]
            
            current_symbol = progress

            # 每20%选中一个品种
            if progress % 20 == 0 and selected_count < len(test_symbols):
                selected_symbol = test_symbols[selected_count]
                selected_count += 1
                symbol_info = f"{selected_symbol['code']} - {selected_symbol['name']} (已选中)"
                print(f"[选股引擎] 测试模式选中品种: {symbol_info}")
            else:
                symbol_info = f"TEST{progress:03d} - 测试品种{progress}"

            # 报告进度
            self.report_progress(
                stage=f'测试进度更新 ({progress}%)',
                current=current_symbol,
                total=total_symbols,
                selected_count=selected_count,
                symbol_info=symbol_info
            )

            print(f"[选股引擎] 测试模式进度: {progress}% - {symbol_info}")

            # 等待1秒
            time.sleep(1)

        # 最终报告完成
        self.report_progress(
            stage='测试完成',
            current=total_symbols,
            total=total_symbols,
            selected_count=selected_count,
            symbol_info='测试模式完成'
        )

        print(f"[选股引擎] 测试模式完成，共选中 {selected_count} 个品种")

        # 设置选中的品种
        self.selected_symbols = test_symbols[:selected_count]
        self.total_symbols = total_symbols
        self.processed_symbols = total_symbols
        
        # 发送完成结果到Redis
        self._send_completion_result()
        
        # 返回选中的品种代码
        return [symbol['code'] for symbol in test_symbols[:selected_count]]

    def run_selection(self) -> List[str]:
        """执行选股轮询"""
        with self.performance_tracker.track_stage("整体选股过程"):
            print(f"[选股引擎] 开始执行选股，候选类型: {self.candidate_type}")
            
            # 检查是否为测试模式
            if self.test_mode:
                return self.run_test_mode()
            
            self.report_progress('开始选股')
            
            # 获取候选品种
            with self.performance_tracker.track_stage("获取候选品种列表"):
                symbols = self.get_candidate_symbols()
                print(f"[选股引擎] 获取到 {len(symbols)} 个候选品种")
            
            self.selected_symbols = []
            self.processed_symbols = 0
            self.total_symbols = len(symbols)  # 设置总数量
            self.last_progress_percent = 0  # 重置进度百分比
            
            # 发送初始进度更新（0%）
            if symbols:
                self.report_progress('开始处理品种', current=0, total=len(symbols))
            
            for i, symbol in enumerate(symbols):
                # 检查是否收到停止请求
                if self._check_stop_requested():
                    print(f"[选股引擎] 收到停止请求，中止选股处理")
                    # 发送中止结果
                    self._send_stop_result()
                    return [s['code'] for s in self.selected_symbols]
                
                try:
                    # 跟踪单个品种的处理性能
                    symbol_start_time = time.time()
                    kline_fetch_time = 0
                    calculation_time = 0
                    
                    # 获取K线数据
                    kline_start_time = time.time()
                    klines = self.get_klines(symbol)
                    kline_fetch_time = time.time() - kline_start_time

                    print(f"[选股引擎] 获取到K线数据: {len(klines)} 条")
                    
                    if not klines:
                        print(f"[选股引擎] 跳过品种 {symbol['code']} - 无K线数据")
                        continue
                    
                    # 检查是否收到停止请求
                    if self._check_stop_requested():
                        print(f"[选股引擎] 收到停止请求，中止选股处理")
                        # 发送中止结果
                        self._send_stop_result()
                        return [s['code'] for s in self.selected_symbols]
                    
                    # 使用策略模块进行选股判断
                    if self.strategy_module and hasattr(self.strategy_module, 'select_stock'):
                        try:
                            print(f"[选股引擎] 开始形态计算 - 品种: {symbol['code']}, 阈值: {self.threshold}, 目标形态: {self.values}")
                            
                            # 跟踪形态计算性能
                            calc_start_time = time.time()
                            # 调用策略模块的选股函数
                            is_selected = self.strategy_module.select_stock(klines, self.threshold, self.values)
                            calculation_time = time.time() - calc_start_time
                            
                            if is_selected:
                                self.selected_symbols.append(symbol)
                                print(f"[选股引擎] 选中品种: {symbol['code']} - {symbol['name']}")
                            else:
                                print(f"[选股引擎] 品种 {symbol['code']} 未通过形态筛选")
                            
                        except Exception as e:
                            print(f"[选股引擎] 策略模块选股失败 {symbol['code']}: {str(e)}")
                            continue
                    else:
                        # 如果没有策略模块，使用简单的阈值判断
                        if len(klines) >= len(self.values):
                            # 简单的示例逻辑：如果K线数量足够，就选中
                            self.selected_symbols.append(symbol)
                            print(f"[选股引擎] 选中品种: {symbol['code']} - {symbol['name']}")
                    
                    # 记录单个品种的性能
                    symbol_total_time = time.time() - symbol_start_time
                    self.performance_tracker.track_symbol_performance(
                        symbol['code'], 
                        kline_fetch_time, 
                        calculation_time
                    )
                    
                    # 更新进度
                    self.processed_symbols += 1  # 正确递增
                    self.report_progress(
                        stage=f'处理品种 {symbol["code"]}',
                        current=self.processed_symbols,
                        total=len(symbols),
                        selected_count=len(self.selected_symbols),
                        symbol_info=f"{symbol['code']} - {symbol['name']}"
                    )
                    
                except Exception as e:
                    print(f"[选股引擎] 处理品种 {symbol['code']} 时出错: {str(e)}")
                    continue
            
            # 选股完成，发送最终结果
            print(f"[选股引擎] 选股完成，共选中 {len(self.selected_symbols)} 个品种")
            self._send_completion_result()
            
            # 打印性能统计摘要
            # self.performance_tracker.print_performance_summary()
            
            return [symbol['code'] for symbol in self.selected_symbols]
    
    def _send_completion_result(self):
        """发送完成结果到Redis"""
        try:
            if not self.redis_client or not self.task_id:
                return
            
            # 构建完成结果数据
            completion_result = {
                'taskId': self.task_id,
                'action': 'completed',
                'selectedSymbols': self.selected_symbols,
                'totalSymbols': self.total_symbols,
                'processedSymbols': self.processed_symbols,
                'selectedCount': len(self.selected_symbols),
                'executionTime': time.time(),
                'timestamp': time.time()
            }
            
            # 发送到完成频道
            channel = f'stock_selection_complete:{self.task_id}'
            self.redis_client.publish(channel, json.dumps(completion_result))
            print(f"[选股引擎] 已发送完成结果到Redis: {channel}")
            print(f"[选股引擎] 完成结果数据: {json.dumps(completion_result)}")
            
        except Exception as e:
            print(f"[选股引擎] 发送完成结果失败: {e}")


    
    def get_selection_result(self) -> Dict[str, Any]:
        """获取选股结果"""
        return {
            'taskId': self.task_id,
            'selectedSymbols': self.selected_symbols,
            'totalSymbols': self.total_symbols,
            'processedSymbols': self.processed_symbols,
            'selectedCount': len(self.selected_symbols),
            'executionTime': time.time()
        }


# 主程序逻辑
if __name__ == '__main__':
    print("[选股引擎] 开始解析命令行参数...")
    
    try:
        # 读取config.json配置文件
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config.json')
        print(f"[选股引擎] 配置文件路径: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            global_config = json.load(f)
        
        # 获取python_service配置（app.py运行在这个端口）
        python_service_config = global_config.get('python_service', {})
        tdx_host = python_service_config.get('host', 'localhost')
        tdx_port = python_service_config.get('port', 5000)
        
        # 获取 Redis 配置
        redis_config = global_config.get('redis', {})
        redis_host = redis_config.get('host', 'localhost')
        redis_port = redis_config.get('port', 6379)
        redis_password = redis_config.get('password', None)
        redis_db = redis_config.get('db', 0)

        print(f"[选股引擎] 从config.json获取API配置: {tdx_host}:{tdx_port}")
    
        # 如果有命令行参数，解析JSON配置
        param_json = sys.argv[1]
        parameter = json.loads(param_json)
        print(f"[选股引擎] 从命令行参数获取配置: {parameter}")
        
        # 添加API配置
        parameter['tdx_host'] = tdx_host
        parameter['tdx_port'] = tdx_port
        parameter['redis_host'] = redis_host
        parameter['redis_port'] = redis_port
        parameter['redis_password'] = redis_password
        parameter['redis_db'] = redis_db
        
        # 创建选股引擎实例
        print("[选股引擎] 创建选股引擎实例...")
        engine = StockSelectionEngine(parameter)
        
        # 执行选股
        print("[选股引擎] 开始执行选股...")
        selected_symbols = engine.run_selection()
        
        # 输出结果
        result = engine.get_selection_result()
        print(f"[选股引擎] 选股完成，结果: {result}")
        
        # 将结果输出到stdout，供Node.js读取
        print(f"RESULT:{json.dumps(result)}")
        
        # 等待控制线程处理停止信号（如果有的话）
        if hasattr(engine, 'control_thread') and engine.control_thread.is_alive():
            print("[选股引擎] 等待控制线程处理停止信号...")
            engine.control_thread.join(timeout=2)  # 最多等待2秒
        
    except Exception as e:
        print(f"[选股引擎] 执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 