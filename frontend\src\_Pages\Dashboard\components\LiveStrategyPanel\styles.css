/* 实盘管理页面样式 */

/* 统计卡片样式 */
.live-strategy-stat-card {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
  transition: all 0.3s;
  height: 100%;
}

.live-strategy-stat-card:hover {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

/* 卡片内容布局 */
.stat-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 卡片标题行 */
.stat-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.stat-card-icon {
  margin-right: 8px;
  font-size: 14px;
}

.stat-card-title {
  font-size: 13px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.65);
}

/* 卡片数值 */
.stat-card-value {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 8px;
}

.stat-card-suffix {
  font-size: 14px;
  margin-left: 4px;
  font-weight: normal;
  opacity: 0.8;
}

/* 时间值特殊样式 */
.time-value {
  font-size: 14px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.65);
}

/* 卡片底部文字 */
.stat-card-footer {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: auto;
  margin-bottom: 4px;
}

/* 刷新按钮 */
.refresh-button {
  margin-top: auto;
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
}

/* 主卡片样式 */
.live-strategy-main-card {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

.live-strategy-main-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  min-height: 40px;
  padding: 0 16px;
}

.live-strategy-main-card .ant-card-head-title {
  font-weight: 600;
  padding: 10px 0;
  font-size: 14px;
}

.live-strategy-main-card .ant-card-extra {
  padding: 8px 0;
}

.live-strategy-main-card .ant-card-body {
  padding: 12px 16px;
}

/* 表格样式 */
.live-strategy-table .ant-table-thead > tr > th {
  background-color: #f5f7fa;
  font-weight: 600;
  border-bottom: 1px solid #d9d9d9;
  color: rgba(0, 0, 0, 0.85);
  padding: 8px 12px;
  font-size: 13px;
  letter-spacing: 0.5px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.06);
  height: 40px;
}

.live-strategy-table .ant-table-tbody > tr:hover > td {
  background-color: #f5f7fa;
}

/* 移除表格行悬停时的蓝色边框 */
.live-strategy-table .ant-table-tbody > tr.ant-table-row:hover > td {
  outline: none !important;
  border-color: #f0f0f0 !important;
}

.live-strategy-table .ant-table-cell-fix-left,
.live-strategy-table .ant-table-cell-fix-right {
  outline: none !important;
}

.live-strategy-table .ant-table-row {
  transition: all 0.3s;
  height: 44px;
}

.live-strategy-table .table-row-light {
  background-color: #ffffff;
}

.live-strategy-table .table-row-dark {
  background-color: #fafafa;
}

/* 表格行边框 */
.live-strategy-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s;
}

/* 表格行悬停效果 - 移除阴影和突出效果 */
.live-strategy-table .ant-table-tbody > tr:hover {
  box-shadow: none;
  z-index: auto;
  position: relative;
}

.live-strategy-table .ant-table-cell {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.live-strategy-table .ant-table-container {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
  box-shadow: none;
}

/* 表头行样式 */
.live-strategy-table .ant-table-thead > tr {
  height: 48px;
}

/* 表头文字居中 */
.live-strategy-table .ant-table-thead > tr > th {
  text-align: center;
}

/* 表头单元格分隔线 */
.live-strategy-table .ant-table-thead > tr > th:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  width: 1px;
  background-color: #e8e8e8;
}

/* 状态标签样式 */
.status-tag-running {
  font-weight: 600;
  border: 1px solid #52c41a;
  background-color: #f6ffed;
  color: #52c41a;
}

.status-tag-stopped {
  font-weight: 600;
  border: 1px solid #d9d9d9;
  background-color: #fafafa;
  color: rgba(0, 0, 0, 0.65);
}

.status-tag-error {
  font-weight: 600;
  border: 1px solid #f5222d;
  background-color: #fff1f0;
  color: #f5222d;
}

/* 操作按钮样式 */
.action-button {
  border: 1px solid #d9d9d9;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 收益率样式 */
.return-value-positive {
  font-weight: 600;
  color: #52c41a;
}

.return-value-negative {
  font-weight: 600;
  color: #f5222d;
}

/* 添加策略按钮 */
.add-strategy-button {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.045);
  height: 28px;
  font-size: 12px;
  padding: 0 8px 0 6px;
}

.add-strategy-button:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.25);
}

.add-strategy-button .anticon {
  font-size: 12px;
}

/* 展开行样式 */
.expanded-row-container {
  padding: 0 8px 16px 8px;
  background-color: #fafafa;
  border-radius: 2px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.expanded-card {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 展开行的表格行样式 */
.live-strategy-table .ant-table-expanded-row > td {
  background-color: #fafafa;
  padding: 0;
  border-bottom: 1px solid #f0f0f0;
}

/* 展开行的边框样式 */
.live-strategy-table .ant-table-expanded-row-fixed {
  margin: 0 -8px;
  padding: 8px 8px 0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

/* 日志时间轴样式 */
.strategy-log-timeline {
  margin-top: 8px;
  padding-left: 8px;
}

.strategy-log-timeline .ant-timeline-item {
  padding-bottom: 12px;
}

.strategy-log-timeline .ant-timeline-item-tail {
  border-left: 2px solid #f0f0f0;
}

.log-item {
  display: flex;
  flex-direction: column;
}

.log-time {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 2px;
}

.log-message {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.85);
}

/* 详情表格样式 */
.detail-table .ant-table-thead > tr > th {
  background-color: #f5f7fa;
  font-weight: 600;
  font-size: 12px;
  padding: 6px 8px;
  height: 32px;
}

.detail-table .ant-table-tbody > tr > td {
  padding: 6px 8px;
  font-size: 12px;
}

/* 展开/收起按钮样式 */
.expand-button {
  color: #1890ff;
  background-color: transparent;
  border: none;
  padding: 4px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.expand-button:hover {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.65);
}

.expand-button:focus {
  outline: none;
  background-color: transparent;
}

/* 操作按钮样式 */
.action-button {
  border: 1px solid #d9d9d9;
  background-color: #fff;
  color: rgba(0, 0, 0, 0.65);
  height: 24px;
  width: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  color: #1890ff;
  border-color: #d9d9d9;
}

.action-button:focus {
  outline: none;
  border-color: #d9d9d9;
}
