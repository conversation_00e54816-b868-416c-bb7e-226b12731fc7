const { DataTypes } = require('sequelize');

// 形态配置主表
const Shapes = {
  shapeId: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '形态名称'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  klineData: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'K线OHLC数据数组，JSON格式：[{time, open, high, low, close}, ...]'
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  }
};

// 形态配置详情表
const ShapeDetails = {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  shapeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '形态ID'
  },
  indicatorType: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '指标类型'
  },
  indicatorParam: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: '指标参数'
  },
  lineName: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '指标线名称'
  },
  values: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: '归一化后的值'
  },
  weight: {
    type: DataTypes.FLOAT,
    allowNull: false,
    comment: '权重'
  }
};

// 定义模型关联
const defineAssociations = (ShapesModel, ShapeDetailsModel) => {
  ShapesModel.hasMany(ShapeDetailsModel, {
    foreignKey: 'shapeId',
    sourceKey: 'shapeId'
  });
  ShapeDetailsModel.belongsTo(ShapesModel, {
    foreignKey: 'shapeId',
    targetKey: 'shapeId'
  });
};

/**
 * 策略类型映射
 * 1 = 多因子策略（MultiFactors）
 * 如有新类型，按需扩展
 */
const STRATEGY_TYPE_MAP = {
  PORTFOLIO: 'portfolio',
  // 未来可扩展更多类型
};

module.exports = {
  Shapes,
  ShapeDetails,
  defineAssociations,
  STRATEGY_TYPE_MAP
};
