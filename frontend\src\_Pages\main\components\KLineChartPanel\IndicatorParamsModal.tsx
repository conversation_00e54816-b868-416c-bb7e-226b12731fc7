import React, { useEffect, useState } from 'react';
import { Modal, Form, InputNumber, Space, Typography, Button } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { EventBus } from '@/events/eventBus';
import { IndicatorEvents } from '@/events/events';
import { IndicatorType } from '@/shared_types/market';

const { Title } = Typography;

interface IndicatorConfig {
  indicatorId: string;
  wrapperId: string;
  type: IndicatorType;
  params: Record<string, number>;
}

interface IndicatorParamsModalProps {
  visible: boolean;
  onClose: () => void;
  paneId: string;
  indicators: IndicatorConfig[];
}

const IndicatorParamsModal: React.FC<IndicatorParamsModalProps> = ({
  visible,
  onClose,
  paneId,
  indicators
}) => {
  const [form] = Form.useForm();
  const [indicatorsToDelete, setIndicatorsToDelete] = useState<string[]>([]);
  
  // 当参数变化时更新表单
  useEffect(() => {
    if (visible && indicators.length > 0) {
      // 构建表单初始值
      const initialValues: Record<string, number> = {};
      indicators.forEach(indicator => {
        Object.entries(indicator.params).forEach(([key, value]) => {
          initialValues[`${indicator.wrapperId}_${key}`] = value;
        });
      });
      form.setFieldsValue(initialValues);
      // 重置删除状态
      setIndicatorsToDelete([]);
    }
  }, [visible, indicators, form]);

  // 处理删除标记
  const toggleDelete = (indicatorId: string) => {
    setIndicatorsToDelete(prev => {
      if (prev.includes(indicatorId)) {
        return prev.filter(id => id !== indicatorId);
      } else {
        return [...prev, indicatorId];
      }
    });
  };

  // 处理表单提交
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理要删除的指标
      indicatorsToDelete.forEach(indicatorId => {
        EventBus.emit(IndicatorEvents.Types.REMOVE_INDICATOR, {
          indicatorId
        } as any);
      });
      
      // 为每个未删除的指标更新参数
      indicators.forEach(indicator => {
        if (!indicatorsToDelete.includes(indicator.indicatorId)) {
          const params: Record<string, number> = {};
          Object.keys(indicator.params).forEach(key => {
            params[key] = values[`${indicator.wrapperId}_${key}`];
          });
          
          // 发送参数更新事件
          EventBus.emit(IndicatorEvents.Types.INDICATOR_PARAMS_EDITED, {
            wrapperId: indicator.wrapperId,
            indicatorId: indicator.indicatorId,
            params
          } as any);
        }
      });
      
      onClose();
    } catch (error) {
      console.error('[指标参数] 表单验证失败:', error);
    }
  };

  return (
    <Modal
      title="指标参数配置"
      open={visible}
      onOk={handleOk}
      onCancel={onClose}
      width={400}
    >
      <Form form={form} layout="vertical">
        {indicators.map(indicator => (
          <div key={indicator.wrapperId}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              backgroundColor: 'rgba(0, 0, 0, 0.02)',
              padding: '4px 8px',
              marginBottom: '8px',
              borderRadius: '4px'
            }}>
              <Title 
                level={5} 
                style={{ 
                  margin: 0,
                  textDecoration: indicatorsToDelete.includes(indicator.indicatorId) ? 'line-through' : 'none',
                  opacity: indicatorsToDelete.includes(indicator.indicatorId) ? 0.5 : 1
                }}
              >
                {indicator.type}
              </Title>
              <Button 
                type="text"
                icon={<DeleteOutlined />}
                onClick={() => toggleDelete(indicator.indicatorId)}
                style={{
                  color: indicatorsToDelete.includes(indicator.indicatorId) ? '#ff4d4f' : 'inherit'
                }}
              />
            </div>
            <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
              {Object.entries(indicator.params).map(([key, value]) => (
                <Form.Item
                  key={key}
                  label={key}
                  name={`${indicator.wrapperId}_${key}`}
                  rules={[{ required: true, message: '请输入参数值' }]}
                >
                  <InputNumber 
                    style={{ width: '100%' }} 
                    disabled={indicatorsToDelete.includes(indicator.indicatorId)}
                  />
                </Form.Item>
              ))}
            </Space>
          </div>
        ))}
      </Form>
    </Modal>
  );
};

export default IndicatorParamsModal; 