const path = require('path');
const fs = require('fs');
const fsp = require('fs').promises;
const { spawn } = require('child_process');
const { Group, GroupStrategy, GroupPosition, LiveStrategy } = require('../models');
const { sequelize } = require('../database');
const GroupConfigGenerator = require('./GroupConfigGenerator');
const yaml = require('js-yaml');
const axios = require('axios');

// 品种计数器持久化路径
const SYMBOL_COUNTER_PATH = path.join(process.cwd(), 'live_trading', 'symbol_counter.json');

/**
 * 品种计数器 - 管理品种的引用计数
 * 使用单例模式确保全局唯一
 */
class SymbolCounter {
  constructor() {
    this.counters = {};
    this.loaded = false;
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!SymbolCounter.instance) {
      SymbolCounter.instance = new SymbolCounter();
    }
    return SymbolCounter.instance;
  }

  /**
   * 从文件加载计数器状态
   */
  async load() {
    try {
      if (this.loaded) return;
      
      // 确保目录存在
      const dir = path.dirname(SYMBOL_COUNTER_PATH);
      await fsp.mkdir(dir, { recursive: true });
      
      try {
        const data = await fsp.readFile(SYMBOL_COUNTER_PATH, 'utf8');
        this.counters = JSON.parse(data);
        console.log(`[品种计数器] 成功加载计数器状态，包含${Object.keys(this.counters).length}个品种`);
      } catch (error) {
        if (error.code !== 'ENOENT') {
          console.error(`[品种计数器] 加载计数器状态失败:`, error);
        }
        this.counters = {};
        console.log(`[品种计数器] 初始化空计数器`);
      }
      
      this.loaded = true;
    } catch (error) {
      console.error(`[品种计数器] 初始化失败:`, error);
      this.counters = {};
      this.loaded = true;
    }
  }

  /**
   * 保存计数器状态到文件
   */
  async save() {
    try {
      await fsp.writeFile(SYMBOL_COUNTER_PATH, JSON.stringify(this.counters, null, 2), 'utf8');
      console.log(`[品种计数器] 成功保存计数器状态，包含${Object.keys(this.counters).length}个品种`);
    } catch (error) {
      console.error(`[品种计数器] 保存计数器状态失败:`, error);
    }
  }

  /**
   * 增加品种的引用计数
   * @param {string} symbol - 品种代码
   */
  async incrementSymbol(symbol) {
    await this.load();
    
    if (!this.counters[symbol]) {
      this.counters[symbol] = 0;
    }
    
    this.counters[symbol]++;
    console.log(`[品种计数器] 品种${symbol}计数增加为${this.counters[symbol]}`);
    
    await this.save();
    return this.counters[symbol];
  }

  /**
   * 减少品种的引用计数
   * @param {string} symbol - 品种代码
   */
  async decrementSymbol(symbol) {
    await this.load();
    
    if (!this.counters[symbol]) {
      console.log(`[品种计数器] 品种${symbol}不存在于计数器中`);
      return 0;
    }
    
    this.counters[symbol]--;
    console.log(`[品种计数器] 品种${symbol}计数减少为${this.counters[symbol]}`);
    
    // 如果计数为0或负数，则删除该品种
    if (this.counters[symbol] <= 0) {
      delete this.counters[symbol];
      console.log(`[品种计数器] 品种${symbol}已从计数器中移除`);
    }
    
    await this.save();
    return this.counters[symbol] || 0;
  }

  /**
   * 批量更新品种计数
   * @param {string[]} symbols - 需要增加计数的品种列表
   * @param {string[]} removedSymbols - 需要减少计数的品种列表
   */
  async batchUpdate(symbols = [], removedSymbols = []) {
    await this.load();
    
    // 增加新品种的计数
    for (const symbol of symbols) {
      if (!this.counters[symbol]) {
        this.counters[symbol] = 0;
      }
      this.counters[symbol]++;
    }
    
    // 减少移除品种的计数
    for (const symbol of removedSymbols) {
      if (this.counters[symbol]) {
        this.counters[symbol]--;
        
        // 如果计数为0或负数，则删除该品种
        if (this.counters[symbol] <= 0) {
          delete this.counters[symbol];
        }
      }
    }
    
    console.log(`[品种计数器] 批量更新完成，当前包含${Object.keys(this.counters).length}个品种`);
    await this.save();
  }

  /**
   * 获取所有活跃品种（计数>0）
   */
  async getActiveSymbols() {
    await this.load();
    return Object.keys(this.counters);
  }
}

/**
 * 向上查找backend目录的相对路径
 * @param {string} startDir - 开始查找的目录
 * @param {number} maxDepth - 最大查找深度，默认10层
 * @returns {string|null} backend目录的相对路径，如果找不到返回null
 */
function findBackendRelativePath(startDir, maxDepth = 10) {
  let current = startDir;
  let relativePath = '';
  
  for (let i = 0; i < maxDepth; i++) {
    if (path.basename(current) === 'backend') {
      return relativePath || './';
    }
    
    const parent = path.dirname(current);
    if (parent === current) { // 已到达根目录
      break;
    }
    
    relativePath = path.join('..', relativePath);
    current = parent;
  }
  
  return null;
}

/**
 * 更新config.yaml中的路径字段
 * @param {string} configContent - 原始配置内容
 * @param {string} currentDir - 当前工作目录
 * @returns {string} 更新后的配置内容
 */
function updateConfigPaths(configContent, currentDir) {
  try {
    const config = yaml.load(configContent);
    
    // 查找backend的相对路径
    const backendRelativePath = findBackendRelativePath(currentDir);
    if (!backendRelativePath) {
      console.warn(`[组合管理] 无法找到backend目录，使用原始配置`);
      return configContent;
    }
    
    console.log(`[组合管理] 找到backend相对路径: ${backendRelativePath}`);
    
    // 构造到common目录的相对路径
    const commonPath = path.join(backendRelativePath, '_Providers', '_Python', 'strategy', 'common');
    console.log(`[组合管理] common目录相对路径: ${commonPath}`);
    
    // 构造到storage目录的相对路径
    const storagePath = path.join(backendRelativePath, '_Providers', '_Python', 'strategy', 'storage');
    console.log(`[组合管理] storage目录相对路径: ${storagePath}`);
    
    // 更新basefiles下的路径
    if (config.basefiles) {
      if (config.basefiles.commodity) {
        config.basefiles.commodity = path.join(commonPath, 'stk_comms.json');
      }
      if (config.basefiles.holiday) {
        config.basefiles.holiday = path.join(commonPath, 'holidays.json');
      }
      if (config.basefiles.session) {
        config.basefiles.session = path.join(commonPath, 'sessions.json');
      }
      console.log(`[组合管理] 更新basefiles路径完成`);
    }
    
    // 更新env下的fees路径
    if (config.env && config.env.fees) {
      config.env.fees = path.join(commonPath, 'fees.json');
      console.log(`[组合管理] 更新env.fees路径完成`);
    }
    
    // 更新data.store.path路径
    if (config.data && config.data.store && config.data.store.path) {
      config.data.store.path = storagePath;
      console.log(`[组合管理] 更新data.store.path路径完成`);
    }
    
    return yaml.dump(config, { indent: 2 });
    
  } catch (error) {
    console.error(`[组合管理] 更新配置路径失败: ${error.message}`);
    return configContent; // 出错时返回原始内容
  }
}

/**
 * 用户组合管理器 - Wonder Trader组合概念的实现
 * 对前端完全透明，保持现有API接口不变
 */
class LiveGroupManager {
  constructor(userId, groupId) {
    this.userId = userId;
    this.groupId = groupId;  // 保存组合ID
    this.userPath = path.join(process.cwd(), 'live_trading', 'groups', `user_${userId}`);
    this.groupPath = path.join(this.userPath, `group_${groupId}`);
    this.engineProcess = null;
    this.group = null; // 数据库中的组合记录
    this.configGenerator = new GroupConfigGenerator();
    
    console.log(`[组合管理] 初始化用户${userId}的组合${groupId}管理器`);
    console.log(`[组合管理] 工作目录: ${process.cwd()}`);
    console.log(`[组合管理] 用户目录: ${this.userPath}`);
    console.log(`[组合管理] 组合目录: ${this.groupPath}`);
  }

  /**
   * 初始化用户组合（如果不存在则创建）
   */
  async initialize() {
    try {
      console.log(`[组合管理] 初始化用户${this.userId}的组合${this.groupId}...`);
      console.log(`[组合管理] 用户目录: ${this.userPath}`);
      console.log(`[组合管理] 组合目录: ${this.groupPath}`);
      
      // 查找指定ID的组合
      let group = await Group.findOne({
        where: { 
          groupid: this.groupId,
          userid: this.userId 
        }
      });

      if (!group) {
        throw new Error(`组合${this.groupId}不存在或不属于用户${this.userId}`);
      } else {
        console.log(`[组合管理] 找到用户${this.userId}的组合${this.groupId}`);
      }

      this.group = group;
      
      // 确保目录结构存在
      await this._ensureDirectoryStructure();
      
      return group;
    } catch (error) {
      console.error(`[组合管理] 初始化用户${this.userId}组合失败:`, error);
      throw error;
    }
  }

  /**
   * 启动策略（前端视角：启动单个策略）
   * 实际实现：透明重启整个组合
   */
  async startStrategy(strategyId, token) {
    try {
      console.log(`[组合管理] 用户${this.userId}请求启动策略: ${strategyId}`);
      
      // 1. 检查策略是否存在
      const strategy = await LiveStrategy.findByPk(strategyId);
      if (!strategy) {
        throw new Error(`策略${strategyId}不存在`);
      }

      // 2. 保存当前运行策略的状态
      await this._saveAllStrategyStates();

      // 3. 将策略添加到组合配置
      await this._addStrategyToGroup(strategyId);

      // 4. 直接启动组合引擎（内部会调用getConfigFiles生成配置）
      await this._startGroupEngine(token);

      // 5. 更新策略状态
      await strategy.update({ 
        status: 'running',
        startTime: new Date()
      });

      //输出启动成功的时间，使用北京时间
      console.log(`[组合管理] 策略${strategyId}启动成功，启动时间：${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`);
      return { status: 'success', message: '策略启动成功' };

    } catch (error) {
      console.error(`[组合管理] 启动策略${strategyId}失败:`, error);
      throw error;
    }
  }

  /**
   * 停止策略（前端视角：停止单个策略）
   * 修改：不重启组合引擎，只标记策略为停止状态
   */
  async stopStrategy(strategyId) {
    try {
      console.log(`[组合管理] 用户${this.userId}请求停止策略: ${strategyId}`);

      // 1. 保存当前运行策略的状态
      await this._saveAllStrategyStates();
      
      // 2. 获取要停止的策略的品种信息
      const strategyToStop = await LiveStrategy.findByPk(strategyId);
      if (!strategyToStop || !strategyToStop.yaml) {
        throw new Error(`策略${strategyId}或其YAML配置不存在`);
      }
      const strategyConfig = yaml.load(strategyToStop.yaml);
      const symbolsToRemove = strategyConfig.universe || [];
      const tradingType = strategyConfig.trading_type || 'etf'; // 获取交易类型

      // 3. 更新品种计数器和全局contracts.json
      const symbolCounter = SymbolCounter.getInstance();
      await symbolCounter.batchUpdate([], symbolsToRemove); // 第二个参数是移除列表
      
      // 使用最新的全市场活跃品种列表来更新文件
      const activeSymbols = await symbolCounter.getActiveSymbols();
      const contractsContent = this._generateContractsContent(activeSymbols, tradingType);
      
      const backendRelativePath = findBackendRelativePath(__dirname);
      if (!backendRelativePath) throw new Error('无法找到backend目录');
      const liveDTContractsPath = path.join(__dirname, backendRelativePath, '_Providers', '_Python', 'strategy', 'portfolio', 'live_DT', 'contracts.json');
      await fsp.writeFile(liveDTContractsPath, contractsContent, 'utf8');
      console.log(`[组合管理] 已更新live_DT/contracts.json，剩余${activeSymbols.length}个品种`);
      
      // 4. 重启数据引擎以应用新的品种列表
      await this._restartDataEngine();

      // 5. 从组合配置移除策略
      await this._removeStrategyFromGroup(strategyId);

      // 6. 直接更新策略状态为停止
      await strategyToStop.update({ 
        status: 'stopped',
        stopTime: new Date()
      });

      console.log(`[组合管理] 策略${strategyId}停止成功`);
      return { status: 'success', message: '策略停止成功' };

    } catch (error) {
      console.error(`[组合管理] 停止策略${strategyId}失败:`, error);
      throw error;
    }
  }

  /**
   * 获取策略持仓（前端视角：单个策略的持仓）
   * 实际实现：从组合持仓中筛选该策略的数据
   */
  async getStrategyPositions(strategyId) {
    try {
      // 获取组合的所有持仓
      const groupPositions = await GroupPosition.findAll({
        where: { groupid: this.group.groupid }
      });

      // TODO: 这里需要根据策略的品种配置来筛选持仓
      // 暂时返回所有持仓，后续需要根据策略配置筛选
      return groupPositions.map(pos => ({
        symbol: pos.symbol,
        position: parseFloat(pos.position),
        avgPrice: parseFloat(pos.avgPrice || 0),
        marketValue: parseFloat(pos.marketValue || 0),
        unrealizedPnl: parseFloat(pos.unrealizedPnl || 0),
        realizedPnl: parseFloat(pos.realizedPnl || 0),
        lastUpdateTime: pos.lastUpdateTime
      }));

    } catch (error) {
      console.error(`[组合管理] 获取策略${strategyId}持仓失败:`, error);
      throw error;
    }
  }

  /**
   * 获取策略盈亏（前端视角：单个策略的盈亏）
   * 实际实现：从组合盈亏中计算该策略的盈亏
   */
  async getStrategyPnl(strategyId) {
    try {
      // TODO: 这里需要根据策略的权重和持仓来计算策略级别的盈亏
      // 暂时返回组合总盈亏，后续需要按策略权重分配
      return {
        totalPnl: parseFloat(this.group.totalPnl || 0),
        unrealizedPnl: 0, // 需要从持仓计算
        realizedPnl: 0,   // 需要从交易记录计算
        returnRate: 0     // 需要根据初始资金计算
      };

    } catch (error) {
      console.error(`[组合管理] 获取策略${strategyId}盈亏失败:`, error);
      throw error;
    }
  }

  /**
   * 获取组合状态
   */
  async getGroupStatus() {
    if (!this.group) {
      await this.initialize();
    }
    
    await this.group.reload();
    return {
      id: this.group.groupid,
      status: this.group.status,
      enginePid: this.group.enginePid,
      currentCapital: parseFloat(this.group.currentCapital || this.group.initialCapital),
      totalPnl: parseFloat(this.group.totalPnl || 0),
      startTime: this.group.startTime,
      lastUpdateTime: this.group.lastUpdateTime
    };
  }

  /**
   * 生成配置文件内容（核心逻辑，供run和debug共用）
   */
  async generateConfigContents(strategyId, strategyYaml, token = '') {
    try {
      console.log(`[组合管理] 为策略${strategyId}生成配置文件内容...`);

      // 解析YAML
      const yaml = require('js-yaml');
      const strategy = yaml.load(strategyYaml);

      // 获取策略基本信息
      const { name = 'Unknown', universe = [], trading_type = 'stock', parameters = {} } = strategy;
      
      // 获取策略的initialCapital
      const liveStrategy = await LiveStrategy.findByPk(strategyId);
      const initialCapital = liveStrategy ? liveStrategy.initialCapital : 100000;
      
      console.log(`[组合管理] 策略${strategyId}初始资金: ${initialCapital}`);

      // --- 修复：动态确定traderId ---
      const channelInfo = await this._getTradingChannelFromStrategies(liveStrategy.accountId, token);
      // 根据通道类型确定trader的ID，这必须与_generateTradersContent中的逻辑一致
      const traderId = channelInfo.type === 'easytrader_ths' ? 'easytrader_ths' : 'openctp';
      console.log(`[组合管理] 动态确定交易通道ID (traderId): ${traderId}`);
      // --- 修复结束 ---

      // 创建配置文件内容
      const configFiles = {};

      // 1. 生成主配置文件 config.yaml
      configFiles['config.yaml'] = this._generateMainConfigContent(trading_type);

      // 2. 生成品种列表 contracts.json
      configFiles['contracts.json'] = this._generateContractsContent(universe, trading_type);

      // 3. 生成执行器配置 executers.yaml
      configFiles['executers.yaml'] = this._generateExecutersContent(initialCapital, traderId);

      // 4. 生成解析器配置 tdparsers.yaml
      configFiles['tdparsers.yaml'] = await this._generateParsersContent();

      // 5. 生成交易通道配置 tdtraders.yaml
      configFiles['tdtraders.yaml'] = await this._generateTradersContent(liveStrategy.accountId, token);

      // 6. 生成过滤器配置 filters.yaml
      configFiles['filters.yaml'] = this._generateFiltersContent();
      
      // 7. 生成交易策略配置 actpolicy.yaml
      configFiles['actpolicy.yaml'] = this._generateActPolicyContent();

      // 8. 生成数据引擎配置 dtcfg.yaml
      configFiles['dtcfg.yaml'] = await this._generateDataKitContent(trading_type);

      // 9. 生成数据引擎日志配置 logcfgdt.yaml
      configFiles['logcfgdt.yaml'] = this._generateDataKitLogContent();

      // 10. 添加原始策略YAML
      configFiles['strategy.yaml'] = strategyYaml;

      return configFiles;

    } catch (error) {
      console.error(`[组合管理] 生成配置文件内容失败:`, error);
      return {
        'error.txt': `生成配置文件失败: ${error.message}`
      };
    }
  }

  /**
   * 获取配置文件内容（用于调试）
   */
  async getConfigFiles(strategyId, token = '') {
    try {
      console.log(`[组合管理] 获取策略${strategyId}的调试配置...`);

      // 获取策略信息
      const { LiveStrategy, GroupStrategy } = require('../models');
      const strategy = await LiveStrategy.findByPk(strategyId);

      if (!strategy) {
        throw new Error(`策略${strategyId}不存在`);
      }
      
      // 查找该策略所在的所有组合
      const groupStrategies = await GroupStrategy.findAll({
        where: { groupid: this.groupId },
        include: [{
          model: LiveStrategy,
          as: 'strategy'
        }]
      });
      
      // 使用通用函数收集组合中所有策略的universe和交易类型
      const { universe, tradingType } = await this._collectUniverseFromStrategies(false);
      
      // 如果组合中有多个策略，使用收集到的品种
      if (groupStrategies.length > 1) {
        console.log(`[组合管理] 发现组合中有${groupStrategies.length}个策略，使用收集到的品种...`);
        
        // 修改策略的YAML，替换universe和trading_type
        const strategyConfig = yaml.load(strategy.yaml);
        strategyConfig.universe = universe;
        strategyConfig.trading_type = tradingType;
        const modifiedYaml = yaml.dump(strategyConfig);
        
        // 使用修改后的YAML生成配置
        const configFiles = await this.generateConfigContents(strategyId, modifiedYaml, token);
        console.log(`[组合管理] 使用组合中所有策略的品种生成配置完成`);
        return configFiles;
      } else {
        // 只有一个策略，使用原始YAML
        const configFiles = await this.generateConfigContents(strategyId, strategy.yaml, token);
        console.log(`[组合管理] 调试配置生成完成`);
        return configFiles;
      }

    } catch (error) {
      console.error(`[组合管理] 获取调试配置失败:`, error);
      return {
        'error.txt': `获取调试配置失败: ${error.message}`
      };
    }
  }

  /**
   * 更新数据引擎配置
   */
  async updateDataEngineConfig() {
    try {
      console.log(`[组合管理] 开始更新数据引擎配置...`);
      
      // 1. 获取交易类型
      const tradingType = await this._getTradingTypeFromStrategies();
      
      // 2. 生成新的数据引擎配置
      const dtConfig = await this._generateDataKitContent(tradingType);
      
      // 3. 保存配置文件
      const dtConfigPath = path.join(this.groupPath, 'dtcfg.yaml');
      await fsp.writeFile(dtConfigPath, yaml.dump(dtConfig), 'utf8');
      
      console.log(`[组合管理] 数据引擎配置已更新: ${dtConfigPath}`);
      
      // 4. 重启数据引擎
      await this._restartDataEngine();
      
      console.log(`[组合管理] 数据引擎配置更新完成`);
      return true;
      
    } catch (error) {
      console.error(`[组合管理] 更新数据引擎配置失败:`, error);
      throw error;
    }
  }

  /**
   * 重启数据引擎
   * 1. 更新live_DT目录下的contracts.json
   * 2. 重启runDT.py进程
   */
  async _restartDataEngine() {
    return new Promise((resolve, reject) => {
      try {
        console.log(`[组合管理] 开始重启数据引擎...`);
        
        const backendRelativePath = findBackendRelativePath(__dirname);
        if (!backendRelativePath) {
          throw new Error('无法找到backend目录');
        }
        
        const liveDTDir = path.join(__dirname, backendRelativePath, '_Providers', '_Python', 'strategy', 'portfolio', 'live_DT');
        console.log(`[组合管理] 数据引擎目录: ${liveDTDir}`);
        
        const runDTPath = path.join(liveDTDir, 'runDT.py');
        try {
          fs.accessSync(runDTPath);
          console.log(`[组合管理] 找到runDT.py: ${runDTPath}`);
        } catch (error) {
          throw new Error(`runDT.py不存在: ${runDTPath}`);
        }
        
        console.log(`[组合管理] 查找并终止现有的runDT.py进程...`);
        const { exec, spawn } = require('child_process');
        
        const command = process.platform === 'win32'
          ? 'tasklist /FI "IMAGENAME eq python.exe" /V /FO CSV'
          : 'ps -ef | grep runDT.py';

        exec(command, (error, stdout) => {
          if (error) {
            console.error(`[组合管理] 查找Python进程失败: ${error.message}`);
            // 即使查找失败，也继续尝试启动新进程
          }
          
          let pidsToKill = [];
          if (process.platform === 'win32') {
            const lines = stdout.split('\n');
            for (const line of lines) {
              // 匹配包含 "runDT.py" 的窗口标题
              if (line.includes('runDT.py')) {
                const match = line.match(/"(\d+)"/);
                if (match && match[1]) {
                  pidsToKill.push(parseInt(match[1], 10));
                }
              }
            }
          } else {
            // Linux/macOS
            const lines = stdout.split('\n');
            for (const line of lines) {
              if (line.includes('runDT.py') && !line.includes('grep')) {
                const parts = line.trim().split(/\s+/);
                if (parts.length > 1) {
                  pidsToKill.push(parseInt(parts[1], 10));
                }
              }
            }
          }

          const killPromises = pidsToKill.map(pid => {
            return new Promise((res) => {
              console.log(`[组合管理] 找到runDT.py进程，PID: ${pid}，准备终止...`);
              const killCommand = process.platform === 'win32' ? `taskkill /PID ${pid} /F` : `kill -9 ${pid}`;
              exec(killCommand, (killError) => {
                if (killError) {
                  console.error(`[组合管理] 终止进程PID ${pid} 失败: ${killError.message}`);
                } else {
                  console.log(`[组合管理] 成功终止runDT.py进程，PID: ${pid}`);
                }
                res(); // 无论成功与否都解决
              });
            });
          });

          Promise.all(killPromises).then(async () => {
            if (pidsToKill.length > 0) {
              console.log(`[组合管理] 所有旧进程已处理，等待2秒后启动新进程...`);
              await new Promise(res => setTimeout(res, 2000));
            } else {
              console.log(`[组合管理] 未找到运行中的runDT.py进程`);
            }
            
            console.log(`[组合管理] 启动新的runDT.py进程...`);
            const pythonProcess = spawn('python', ['runDT.py'], {
              cwd: liveDTDir,
              detached: true,
              stdio: 'ignore'
            });
            pythonProcess.unref();
            
            console.log(`[组合管理] 新的runDT.py进程已启动，PID: ${pythonProcess.pid}`);
            console.log(`[组合管理] 数据引擎重启流程已完成`);
            resolve(true);
          });
        });
        
      } catch (error) {
        console.error(`[组合管理] 重启数据引擎失败:`, error);
        reject(error);
      }
    });
  }
  
  /**
   * 从组合中收集所有策略的品种和交易类型
   * @param {boolean} onlyActive - 是否只收集活跃状态的策略
   * @returns {Promise<{universe: string[], tradingType: string}>} - 品种列表和交易类型
   */
  async _collectUniverseFromStrategies(onlyActive = true) {
    try {
      // 查询条件
      const whereCondition = { groupid: this.groupId };
      if (onlyActive) {
        whereCondition.status = 'active';
      }
      
      // 获取组合中的策略
      const groupStrategies = await GroupStrategy.findAll({
        where: whereCondition,
        include: [{
          model: LiveStrategy,
          as: 'strategy'
        }]
      });
      
      // 收集品种和交易类型
      const allSymbols = new Set();
      let tradingType = 'etf'; // 默认交易类型
      
      // 遍历所有策略，收集交易品种和确定交易类型
      for (const gs of groupStrategies) {
        if (gs.strategy && gs.strategy.yaml) {
          const strategyConfig = yaml.load(gs.strategy.yaml);
          const strategyUniverse = strategyConfig.universe || [];
          const strategyType = strategyConfig.trading_type || 'etf';
          
          // 将策略的品种添加到集合中（自动去重）
          strategyUniverse.forEach(symbol => allSymbols.add(symbol));
          
          // 确定交易类型（优先级：CPT > FUTURES > STOCK > ETF）
          if (strategyType.toLowerCase().includes('cpt') || strategyType.toLowerCase().includes('crypto')) {
            tradingType = 'cpt';
          } else if (tradingType !== 'cpt' && (strategyType.toLowerCase().includes('fut') || strategyType.toLowerCase().includes('future'))) {
            tradingType = 'futures';
          } else if (tradingType !== 'cpt' && tradingType !== 'futures' && strategyType.toLowerCase().includes('stock')) {
            tradingType = 'stock';
          }
        }
      }
      
      // 将Set转换为数组
      const universe = Array.from(allSymbols);
      console.log(`[组合管理] 收集到${onlyActive ? '活跃' : '所有'}策略的品种，共${universe.length}个，交易类型: ${tradingType}`);
      
      return { universe, tradingType };
    } catch (error) {
      console.error(`[组合管理] 收集策略品种失败:`, error);
      return { universe: [], tradingType: 'etf' };
    }
  }

  /**
   * 更新live_DT目录下的contracts.json
   * 使用当前组合中所有策略的品种列表
   */
  async _updateLiveDTContracts(universe, tradingType) {
    try {
      console.log(`[组合管理] 更新live_DT目录下的contracts.json...`);

      // 1. 更新品种计数器
      const symbolCounter = SymbolCounter.getInstance();
      // 注意：这里需要根据是启动还是停止来决定是增加还是减少
      // 暂时假设是增加
      await symbolCounter.batchUpdate(universe, []); 

      // 2. 获取所有活跃品种
      const activeSymbols = await symbolCounter.getActiveSymbols();
      
      console.log(`[组合管理] 品种计数器更新完毕，当前总活跃品种: ${activeSymbols.length}`);

      // 3. 生成contracts.json内容
      const contractsContent = this._generateContractsContent(activeSymbols, tradingType);
      
      // 4. 获取live_DT目录路径
      const backendRelativePath = findBackendRelativePath(__dirname);
      if (!backendRelativePath) {
        throw new Error('无法找到backend目录');
      }
      
      const liveDTDir = path.join(__dirname, backendRelativePath, '_Providers', '_Python', 'strategy', 'portfolio', 'live_DT');
      const liveDTContractsPath = path.join(liveDTDir, 'contracts.json');
      
      // 5. 保存contracts.json到live_DT目录
      await fsp.writeFile(liveDTContractsPath, contractsContent, 'utf8');
      
      console.log(`[组合管理] 成功更新live_DT目录下的contracts.json: ${liveDTContractsPath}`);
      console.log(`[组合管理] 包含${activeSymbols.length}个活跃品种`);
      
      return true;
      
    } catch (error) {
      console.error(`[组合管理] 更新live_DT目录下的contracts.json失败:`, error);
      return false;
    }
  }

  /**
   * 从策略配置中获取交易类型
   */
  async _getTradingTypeFromStrategies() {
    try {
      // 获取组合中的所有策略
      const groupStrategies = await GroupStrategy.findAll({
        where: { groupid: this.groupId },
        include: [{
          model: LiveStrategy,
          as: 'strategy'
        }]
      });

      // 遍历策略，获取交易类型
      const types = new Set();
      for (const gs of groupStrategies) {
        if (gs.strategy && gs.strategy.yaml) {
          const config = yaml.load(gs.strategy.yaml);
          const type = config.trading_type || 'FUTURES';
          types.add(type.toUpperCase());
        }
      }

      // 如果有多个类型，优先级：CPT > FUTURES > STOCK
      if (types.has('CPT')) return 'CPT';
      if (types.has('FUTURES')) return 'FUTURES';
      if (types.has('STOCK')) return 'STOCK';
      
      return 'FUTURES'; // 默认期货
      
    } catch (error) {
      console.error(`[组合管理] 获取交易类型失败:`, error);
      return 'FUTURES'; // 出错时默认期货
    }
  }

  /**
   * 停止组合引擎
   */
  async _stopGroupEngine() {
    try {
      console.log(`[组合管理] 开始停止组合引擎...`);
      
      // 如果没有进程ID，则直接返回
      if (!this.group.enginePid) {
        console.log(`[组合管理] 组合引擎未运行，无需停止`);
        return;
      }
      
      // 调用策略服务器的停止端点
      const config = require('../config.json');
      const strategyService = config.strategy_service;
      const baseURL = `http://${strategyService.host}:${strategyService.port}`;
      
      const axiosInstance = axios.create({
        baseURL: baseURL,
        timeout: 10000, // 10秒超时
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      // 使用项目ID来停止进程
      const response = await axiosInstance.post('/strategy/live/stop', {
        project_id: `live_project_${this.groupId}`,
        pid: this.group.enginePid
      });
      
      if (response.data.success) {
        console.log(`[组合管理] 策略服务器已成功停止组合引擎，PID: ${this.group.enginePid}`);
      } else {
        console.warn(`[组合管理] 策略服务器停止组合引擎失败: ${response.data.error}`);
        
        // 如果策略服务器停止失败，尝试直接终止进程
        try {
          process.kill(this.group.enginePid, 'SIGTERM');
          console.log(`[组合管理] 已直接停止引擎进程: ${this.group.enginePid}`);
        } catch (killError) {
          console.warn(`[组合管理] 直接停止引擎进程失败: ${killError.message}`);
        }
      }
    } catch (error) {
      console.error(`[组合管理] 停止组合引擎出错: ${error.message}`);
      
      // 出错时尝试直接终止进程
      if (this.group.enginePid) {
        try {
          process.kill(this.group.enginePid, 'SIGTERM');
          console.log(`[组合管理] 已直接停止引擎进程: ${this.group.enginePid}`);
        } catch (killError) {
          console.warn(`[组合管理] 直接停止引擎进程失败: ${killError.message}`);
        }
      }
    }
    
    // 无论如何，更新组合状态
    await this.group.update({ 
      status: 'stopped',
      enginePid: null,
      stopTime: new Date()
    });
  }

  /**
   * 停止整个组合（实盘项目）
   * 1. 停止所有策略
   * 2. 退订组合使用的所有品种
   * 3. 停止组合引擎
   */
  async stopGroup() {
    try {
      console.log(`[组合管理] 开始停止组合${this.groupId}...`);
      
      // 1. 获取组合中的所有活跃策略的品种
      const { universe, tradingType } = await this._collectUniverseFromStrategies(true);
      const symbolsToUnsubscribe = universe;
      
      console.log(`[组合管理] 组合${this.groupId}使用了${symbolsToUnsubscribe.length}个品种，准备退订`);
      
      // 2. 保存当前运行策略的状态
      await this._saveAllStrategyStates();
      
      // 3. 停止组合引擎进程
      await this._stopGroupEngine();
      
      // 4. 将所有策略标记为停止
      const groupStrategies = await GroupStrategy.findAll({
        where: { groupid: this.groupId, status: 'active' },
        include: [{ model: LiveStrategy, as: 'strategy' }]
      });
      for (const gs of groupStrategies) {
        await gs.update({ status: 'inactive' });
        if (gs.strategy) {
          await gs.strategy.update({ status: 'stopped', stopTime: new Date() });
        }
      }
      
      // 5. 更新全局品种列表并重启数据引擎
      if (symbolsToUnsubscribe.length > 0) {
        const symbolCounter = SymbolCounter.getInstance();
        await symbolCounter.batchUpdate([], symbolsToUnsubscribe); // 批量减少品种计数
        
        const activeSymbols = await symbolCounter.getActiveSymbols();
        const contractsContent = this._generateContractsContent(activeSymbols, tradingType);
        
        const backendRelativePath = findBackendRelativePath(__dirname);
        if (!backendRelativePath) throw new Error('无法找到backend目录');
        const liveDTContractsPath = path.join(__dirname, backendRelativePath, '_Providers', '_Python', 'strategy', 'portfolio', 'live_DT', 'contracts.json');
        
        await fsp.writeFile(liveDTContractsPath, contractsContent, 'utf8');
        console.log(`[组合管理] 已更新live_DT/contracts.json，全市场剩余${activeSymbols.length}个品种`);
        
        await this._restartDataEngine(); // 重启数据引擎
      }
      
      console.log(`[组合管理] 组合${this.groupId}已停止`);
      return { status: 'success', message: `组合已停止，退订了${symbolsToUnsubscribe.length}个品种` };
      
    } catch (error) {
      console.error(`[组合管理] 停止组合${this.groupId}失败:`, error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 生成主配置文件内容 - 读取tmpl模板并更新路径
   */
  _generateMainConfigContent(tradingType) {
    try {
      // 查找backend目录的相对路径
      const backendRelativePath = findBackendRelativePath(__dirname);
      if (!backendRelativePath) {
        throw new Error('无法找到backend目录');
      }

      // 构造到模板文件的路径
      const templatePath = path.join(__dirname, backendRelativePath, '_Providers', '_Python', 'strategy', 'portfolio', 'tmpl', 'portfolio_0', 'config.yaml');

      console.log(`[组合管理] 读取config.yaml模板: ${templatePath}`);

      // 读取模板文件内容
      const configContent = fs.readFileSync(templatePath, 'utf8');
      console.log(`[组合管理] 成功读取config.yaml模板，长度: ${configContent.length}`);

      // 更新配置文件中的路径（这里使用this.groupPath作为目标目录）
      const updatedContent = updateConfigPaths(configContent, this.groupPath);
      
      return updatedContent;

    } catch (error) {
      console.error(`[组合管理] 读取config.yaml模板失败: ${error.message}`);

      // 如果读取模板失败，返回一个基本的配置作为后备
      const backendRelativePath = findBackendRelativePath(this.groupPath) || '../../../..';
      const commonPath = path.join(backendRelativePath, '_Providers', '_Python', 'strategy', 'common');
      const storagePath = path.join(backendRelativePath, '_Providers', '_Python', 'strategy', 'storage');
      
      const fallbackConfig = {
        basefiles: {
          commodity: path.join(commonPath, 'stk_comms.json'),
          contract: 'contracts.json',
          holiday: path.join(commonPath, 'holidays.json'),
          session: path.join(commonPath, 'sessions.json')
        },
        data: {
          store: {
            module: 'WtDataStorage',
            path: storagePath
          }
        },
        env: {
          name: 'cta',
          fees: path.join(commonPath, 'fees.json'),
          filters: 'filters.yaml',
          product: {
            session: 'TRADING'
          }
        },
        executers: 'executers.yaml',
        parsers: 'tdparsers.yaml',
        traders: 'tdtraders.yaml',
        bspolicy: 'actpolicy.yaml'
      };

      console.log(`[组合管理] 使用后备配置`);
      return yaml.dump(fallbackConfig, { indent: 2 });
    }
  }

  /**
   * 生成品种列表内容
   * 按照官方格式规范生成contracts.json
   */
  _generateContractsContent(universe, tradingType) {
    // 创建符合官方格式的合约信息对象
    const contracts = {
      'SSE': {},  // 上海证券交易所
      'SZSE': {}  // 深圳证券交易所
    };

    // 遍历所有股票代码，生成符合格式的合约信息
    for (const code of universe) {
      // 处理可能的交易所前缀
      let exchg = 'SSE'; // 默认上交所
      let cleanCode = code;
      
      if (code.includes('.')) {
        const parts = code.split('.');
        cleanCode = parts[parts.length - 1]; // 获取代码部分
        
        // 根据前缀判断交易所
        const prefix = parts[0].toUpperCase();
        if (prefix === 'SZ' || prefix === 'SZSE') {
          exchg = 'SZSE';
        } else if (prefix === 'SH' || prefix === 'SSE') {
          exchg = 'SSE';
        }
      } else {
        // 没有交易所前缀时，根据代码特征判断
        if (cleanCode.startsWith('0') || cleanCode.startsWith('3') || cleanCode.startsWith('2')) {
          exchg = 'SZSE';
        } else if (cleanCode.startsWith('6') || cleanCode.startsWith('5') || cleanCode.startsWith('1')) {
          exchg = 'SSE';
        }
      }

      // 生成合约名称（简单使用代码作为名称）
      const contractName = cleanCode;
      
      // 确定品种类型
      let product = '';
      if (cleanCode.startsWith('0')) product = 'STK'; // 普通股票
      else if (cleanCode.startsWith('3')) product = 'STK'; // 创业板（也属于股票类型）
      else if (cleanCode.startsWith('6')) product = 'STK'; // 普通股票
      else if (cleanCode.startsWith('5')) product = 'ETF'; // ETF
      else if (cleanCode.startsWith('1')) product = 'ETF'; // 债券也归类为ETF
      else if (cleanCode.startsWith('2')) product = 'STK'; // 中小板（已并入主板）
      else product = 'STK'; // 默认股票类型
      
      // 如果交易类型是虚拟货币，则使用CPT作为product
      if (tradingType.toLowerCase().includes('cpt') || tradingType.toLowerCase().includes('crypto')) {
        product = 'CPT';
      }
      
      // 生成符合官方格式的合约信息
      contracts[exchg][cleanCode] = {
        "name": contractName,   // 名称
        "code": cleanCode,      // 代码
        "exchg": exchg,         // 交易所
        "product": product,     // 品种
        "maxlimitqty": 1000,    // 限价单单笔最大委托数量
        "maxmarketqty": 500     // 市价单单笔最大委托数量
      };
    }

    // 根据交易类型进行特殊处理
    if (tradingType.toLowerCase().includes('fut') || tradingType.toLowerCase().includes('future')) {
      // 清空现有交易所，使用期货交易所
      contracts = {
        'CFFEX': {}, // 中金所
        'SHFE': {},  // 上期所
        'DCE': {},   // 大商所
        'CZCE': {},  // 郑商所
        'INE': {}    // 能源中心
      };
      
      // 根据代码特征判断期货品种所属交易所和品种
      for (const code of universe) {
        let cleanCode = code;
        if (code.includes('.')) {
          cleanCode = code.split('.').pop();
        }
        
        // 根据品种前缀判断交易所
        let exchg = 'SHFE'; // 默认上期所
        let product = cleanCode.substring(0, 2); // 默认取前两个字符作为品种
        
        // 中金所品种：IC、IF、IH、TS、TF、T
        if (['IC', 'IF', 'IH', 'TS', 'TF', 'T'].some(p => cleanCode.startsWith(p))) {
          exchg = 'CFFEX';
          product = cleanCode.match(/^[A-Za-z]+/)[0]; // 提取字母部分作为品种
        } 
        // 上期所品种：cu、al、zn、pb、ni、sn等
        else if (['cu', 'al', 'zn', 'pb', 'ni', 'sn', 'au', 'ag', 'rb', 'wr', 'hc', 'ss', 'bu', 'ru', 'fu', 'sp'].some(p => cleanCode.toLowerCase().startsWith(p))) {
          exchg = 'SHFE';
          product = cleanCode.match(/^[A-Za-z]+/)[0].toLowerCase(); // 提取字母部分作为品种
        }
        // 大商所品种：a、b、m、y、p、c等
        else if (['a', 'b', 'm', 'y', 'p', 'c', 'cs', 'jd', 'fb', 'bb', 'pp', 'v', 'l', 'j', 'jm'].some(p => cleanCode.toLowerCase().startsWith(p))) {
          exchg = 'DCE';
          product = cleanCode.match(/^[A-Za-z]+/)[0].toLowerCase(); // 提取字母部分作为品种
        }
        // 郑商所品种：SR、CF、RM等，注意郑商所合约代码格式特殊
        else if (['SR', 'CF', 'RM', 'TA', 'MA', 'FG', 'OI', 'ZC', 'SF', 'SM', 'UR', 'AP', 'CJ', 'CY'].some(p => cleanCode.toUpperCase().startsWith(p))) {
          exchg = 'CZCE';
          product = cleanCode.match(/^[A-Za-z]+/)[0].toUpperCase(); // 提取字母部分作为品种
        }
        // 能源中心品种：SC、LU等
        else if (['SC', 'LU', 'NR'].some(p => cleanCode.toUpperCase().startsWith(p))) {
          exchg = 'INE';
          product = cleanCode.match(/^[A-Za-z]+/)[0].toUpperCase(); // 提取字母部分作为品种
        }
        
        // 生成期货合约信息
        contracts[exchg][cleanCode] = {
          "name": cleanCode,      // 名称就是代码
          "code": cleanCode,      // 代码
          "exchg": exchg,         // 交易所
          "product": product,     // 品种
          "maxlimitqty": 20,      // 限价单单笔最大委托数量
          "maxmarketqty": 10      // 市价单单笔最大委托数量
        };
      }
    } else if (tradingType.toLowerCase().includes('cpt') || tradingType.toLowerCase().includes('crypto')) {
      // 虚拟货币类型
      contracts = {
        'BINANCE': {}, // 币安交易所
        'HUOBI': {},   // 火币交易所
        'OKEX': {},    // OK交易所
        'GATE': {},    // Gate交易所
        'OTHER': {}    // 其他交易所
      };
      
      // 处理虚拟货币代码
      for (const code of universe) {
        let cleanCode = code;
        let exchg = 'BINANCE'; // 默认币安交易所
        
        // 处理可能包含交易所前缀的代码，如 BINANCE.BTCUSDT
        if (code.includes('.')) {
          const parts = code.split('.');
          cleanCode = parts[parts.length - 1];
          
          // 根据前缀确定交易所
          const prefix = parts[0].toUpperCase();
          if (['BINANCE', 'BN'].includes(prefix)) {
            exchg = 'BINANCE';
          } else if (['HUOBI', 'HB'].includes(prefix)) {
            exchg = 'HUOBI';
          } else if (['OKEX', 'OK'].includes(prefix)) {
            exchg = 'OKEX';
          } else if (['GATE', 'GT'].includes(prefix)) {
            exchg = 'GATE';
          } else {
            exchg = 'OTHER';
          }
        }
        
        // 获取币种名称，默认使用代码作为名称
        const name = cleanCode;
        
        // 虚拟货币统一使用CPT作为品种类型
        const product = 'CPT';
        
        // 生成虚拟货币合约信息
        contracts[exchg][cleanCode] = {
          "name": name,           // 名称
          "code": cleanCode,      // 代码
          "exchg": exchg,         // 交易所
          "product": product,     // 品种，统一为CPT
          "maxlimitqty": 100,     // 限价单单笔最大委托数量
          "maxmarketqty": 50      // 市价单单笔最大委托数量
        };
      }
    }

    console.log(`[组合管理] 生成合约信息：${JSON.stringify(contracts, null, 2).substring(0, 200)}...`);
    return JSON.stringify(contracts, null, 2);
  }

  /**
   * 生成执行器配置内容
   * @param {number} initialCapital - 初始资金
   * @param {string} traderId - 交易通道ID (例如 'openctp' 或 'easytrader_ths')
   */
  _generateExecutersContent(initialCapital, traderId = 'openctp') {
    const yaml = require('js-yaml');

    // 计算比例系数 scale
    // 基准资金为10万，如果initialCapital是10万，则scale为1，其他金额按比例计算
    const baseCapital = 100000; // 基准资金10万
    const scale = initialCapital ? (initialCapital / baseCapital) : 1;
    
    console.log(`[组合管理] 执行器scale计算: initialCapital=${initialCapital}, baseCapital=${baseCapital}, scale=${scale}`);

    const config = {
      executers: [
        {
          active: true,
          id: `exec_${traderId}`, // ID也设为动态
          trader: traderId,      // 使用传入的traderId
          scale: scale, // 使用计算出的scale值
          local: true,
          policy: {
            default: {
              name: 'WtExeFact.WtMinImpactExeUnit',
              offset: 0,
              expire: 5,
              pricemode: 1,
              span: 500,
              byrate: false,
              lots: 1,
              rate: 0
            }
          }
        }
      ]
    };

    return yaml.dump(config, { indent: 2 });
  }

  /**
   * 生成解析器配置内容
   * @param {string} tradingType - 交易类型
   */
  async _generateParsersContent(tradingType) {
    const yaml = require('js-yaml');
    
    // 配置UDP接收器来接收DataKit广播的数据
    const parserConfig = {
      parsers: [{
        active: true,
        id: 'parser1',
        module: 'ParserUDP',
        host: '127.0.0.1',
        bport: 9001,       // 广播端口
        sport: 3997,       // 查询端口
        filter: ''
      }]
    };

    return yaml.dump(parserConfig, { indent: 2 });
  }

  /**
   * 根据accountId获取交易通道信息
   * @param {string} channelId - 交易通道ID
   * @param {string} token - 用户JWT token
   * @returns {Promise<Object>} 交易通道信息
   */
  async _getTradingChannelFromStrategies(channelId, token = '') {
      try {
        console.log(`[组合管理] 尝试获取交易通道信息，channelId: ${channelId}`);
        
        if (!channelId) {
          console.log(`[组合管理] 未提供channelId，使用默认OpenCTP配置`);
          return { id: 'openctp', type: 'openctp' };
        }
  
        // 根据设计要求：使用liveStrategyHandler的getTradingChannels函数获取交易通道列表，然后对比channelId获取channelType
        const { getTradingChannels } = require('../_Handlers/liveStrategyHandler');
        
        console.log(`[组合管理] 查询用户的交易通道配置，channelId: ${channelId}`);
        
        // 1. 从token中解析用户信息获取username
        let username = null;
        if (token) {
          try {
            const jwt = require('jsonwebtoken');
            const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
            
            // 处理Bearer前缀
            const cleanToken = token.startsWith('Bearer ') ? token.slice(7) : token;
            
            // 解析JWT token获取用户信息
            const decoded = jwt.verify(cleanToken, JWT_SECRET);
            username = decoded.username;
            
            console.log(`[组合管理] 从token解析到用户名: ${username}`);
          } catch (jwtError) {
            console.error(`[组合管理] JWT token解析失败: ${jwtError.message}`);
          }
        }
        
        if (!username) {
          console.log(`[组合管理] 无法从token获取用户名，使用默认OpenCTP配置`);
          return { id: 'openctp', type: 'openctp' };
        }
        
        // 2. 通过username获取用户的交易通道列表
        const tradingChannels = await getTradingChannels(username);
        
        let tradingConfig = null;
        
        if (tradingChannels && tradingChannels.length > 0) {
          // 3. 在交易通道列表中查找匹配的channelId
          const matchedChannel = tradingChannels.find(channel => channel.id === channelId);
          
          if (matchedChannel) {
            console.log(`[组合管理] 找到匹配的交易通道: ${JSON.stringify(matchedChannel)}`);
            // 4. 从匹配记录中获取channelType
            tradingConfig = {
              channelType: matchedChannel.type,
              id: matchedChannel.id,
              name: matchedChannel.name,
              status: matchedChannel.status
            };
          } else {
            console.log(`[组合管理] 在交易通道列表中未找到channelId=${channelId}的匹配项`);
          }
        } else {
          console.log(`[组合管理] 获取交易通道列表失败或列表为空`);
        }
        
        if (tradingConfig) {
          const channelType = tradingConfig.channelType;
          console.log(`[组合管理] 找到交易通道配置: channelId=${channelId}, channelType=${channelType}`);
          
          // 根据channelType决定配置类型
          if (channelType === 'easytrader_ths') {
            return {
              id: channelId,
              type: 'easytrader_ths',
              token: token || '' // 如果有保存的token
            };
          } else if (channelType === 'openctp') {
            return {
              id: channelId,
              type: 'openctp'
            };
          } else if (channelType === 'miniQMT') {
            return {
              id: channelId,
              type: 'miniQMT'
            };
          } else {
            console.log(`[组合管理] 未知的channelType: ${channelType}，使用默认OpenCTP配置`);
            return {
              id: 'openctp',
              type: 'openctp'
            };
          }
        } else {
          console.log(`[组合管理] 未找到channelId=${channelId}的交易通道配置，使用默认OpenCTP配置`);
          return {
            id: 'openctp',
            type: 'openctp'
          };
        }
        
      } catch (error) {
        console.error(`[组合管理] 获取交易通道信息失败: ${error.message}`);
        // 出错时返回默认的OpenCTP配置
        return { id: 'openctp', type: 'openctp' };
      }
    }

  /**
   * 生成交易通道配置内容
   */
  async _generateTradersContent(accountId, token = '') {
    const yaml = require('js-yaml');

    // 获取用户的交易配置
    const userTradingConfig = await this._getUserTradingConfig() || {};
    
    // 获取交易通道信息 - 保留此调用以备将来调试或扩展，但不再用于切换逻辑
    const channelInfo = await this._getTradingChannelFromStrategies(accountId, token);
    console.log(`[组合管理] 获取到交易通道信息（仅供参考）:`, channelInfo);

    let traderEntry;
    
    // 强制使用OpenCTP配置
    console.log(`[组合管理] 强制使用OpenCTP交易通道配置`);

    // 尝试从config.json中读取openctp_presets.trader配置
    let openctp_trader_presets = {};
    try {
      // 使用统一的backend路径查找方式
      const backendRelativePath = findBackendRelativePath(__dirname);
      if (!backendRelativePath) {
        throw new Error('无法找到backend目录');
      }
      
      const configPath = path.join(__dirname, backendRelativePath, '_Providers', '_Python', 'config.json');
      console.log(`[组合管理] 尝试加载trader配置文件: ${configPath}`);
      
      const configContent = await fsp.readFile(configPath, 'utf8');
      const config = JSON.parse(configContent);
      
      if (config.openctp_presets && config.openctp_presets.trader) {
        openctp_trader_presets = config.openctp_presets.trader;
        console.log(`[组合管理] 成功从config.json加载OpenCTP trader预设配置`);
      }
    } catch (error) {
      console.error(`[组合管理] 加载OpenCTP trader预设配置失败: ${error.message}`);
    }

    // 创建交易通道条目，确保所有字段都存在
    traderEntry = {
      active: true,
      id: 'openctp',
      module: 'TraderCTP',
      savedata: true,
      quick: true,
      ctpmodule: 'tts_thosttraderapi_se',
      // 确保所有字段都存在，即使为空值
      front: '',
      broker: '',
      appid: '',
      authcode: '',
      pass: '',
      user: ''
    };
    
    // 优先使用用户配置，注意处理空值
    if (userTradingConfig.frontAddress !== undefined) traderEntry.front = userTradingConfig.frontAddress;
    if (userTradingConfig.broker !== undefined) traderEntry.broker = userTradingConfig.broker;
    if (userTradingConfig.appid !== undefined) traderEntry.appid = userTradingConfig.appid;
    if (userTradingConfig.authcode !== undefined) traderEntry.authcode = userTradingConfig.authcode;
    if (userTradingConfig.password !== undefined) traderEntry.pass = userTradingConfig.password;
    if (userTradingConfig.username !== undefined) traderEntry.user = userTradingConfig.username;
    
    // 如果用户配置中缺少某项，尝试使用预设配置
    if (!('front' in traderEntry) && openctp_trader_presets.front) traderEntry.front = openctp_trader_presets.front;
    if (!('broker' in traderEntry) && openctp_trader_presets.broker) traderEntry.broker = openctp_trader_presets.broker;
    if (!('appid' in traderEntry) && openctp_trader_presets.appid) traderEntry.appid = openctp_trader_presets.appid;
    if (!('authcode' in traderEntry) && openctp_trader_presets.authcode) traderEntry.authcode = openctp_trader_presets.authcode;
    if (!('pass' in traderEntry) && openctp_trader_presets.pass) traderEntry.pass = openctp_trader_presets.pass;
    if (!('user' in traderEntry) && openctp_trader_presets.user) traderEntry.user = openctp_trader_presets.user;

    console.log(`[组合管理] 生成trader配置: ${JSON.stringify(traderEntry)}`);
    
    const config = {
      traders: [traderEntry]
    };

    return yaml.dump(config, { indent: 2 });
  }

  /**
   * 生成过滤器配置内容
   */
  _generateFiltersContent() {
    const yaml = require('js-yaml');

    const config = {
      code_filters: {},
      strategy_filters: {}
    };

    return yaml.dump(config, { indent: 2 });
  }

  /**
   * 生成交易行为策略配置内容
   */
  _generateActPolicyContent() {
    const yaml = require('js-yaml');

    const config = {
      default: {
        order: [
          {
            action: 'close',
            limit: 0
          },
          {
            action: 'open',
            limit: 0
          }
        ]
      }
    };

    return yaml.dump(config, { indent: 2 });
  }

  /**
   * 获取用户的交易配置
   */
  async _getUserTradingConfig() {
    try {
      const { UserTradingConfig } = require('../models');

      // 查询用户的OpenCTP配置
      const config = await UserTradingConfig.findOne({
        where: {
          userId: this.userId,
          channelType: 'openctp',
          isActive: true
        }
      });

      if (config) {
        // 返回用户配置，包括空值
        const userConfig = {
          frontAddress: config.frontAddress,
          username: config.username,
          password: config.password
        };
        
        // 只添加非undefined的值，确保空字符串等值被保留
        if (config.broker !== undefined) userConfig.broker = config.broker;
        if (config.appid !== undefined) userConfig.appid = config.appid;
        if (config.authcode !== undefined) userConfig.authcode = config.authcode;
        
        console.log(`[组合管理] 获取到用户${this.userId}的OpenCTP配置: ${JSON.stringify(userConfig, (k, v) => k === 'password' ? '******' : v)}`);
        return userConfig;
      } else {
        console.log(`[组合管理] 用户${this.userId}未配置OpenCTP，返回空配置`);
        return {}; // 返回空对象，不设置任何默认值
      }
    } catch (error) {
      console.error(`[组合管理] 获取用户交易配置失败:`, error);
      return {}; // 出错时也返回空对象，不设置默认值
    }
  }

  /**
   * 确保用户组合目录结构存在
   */
  async _ensureDirectoryStructure() {
    try {
      console.log(`[组合管理] 确保用户${this.userId}目录结构存在...`);

      // 创建用户目录和组合目录
      const dirs = [
        path.join(process.cwd(), 'live_trading'),
        path.join(process.cwd(), 'live_trading', 'groups'),
        this.userPath,
        this.groupPath
      ];

      for (const dir of dirs) {
        try {
          await fsp.mkdir(dir, { recursive: true });
          console.log(`[组合管理] 创建目录(如果不存在): ${dir}`);
        } catch (error) {
          if (error.code !== 'EEXIST') {
            throw error;
          }
          console.log(`[组合管理] 目录已存在: ${dir}`);
        }
      }

      // 创建日志目录
      const logDir = path.join(process.cwd(), 'live_trading', 'logs');
      try {
        await fsp.mkdir(logDir, { recursive: true });
        console.log(`[组合管理] 创建日志目录(如果不存在): ${logDir}`);
      } catch (error) {
        if (error.code !== 'EEXIST') {
          throw error;
        }
        console.log(`[组合管理] 日志目录已存在: ${logDir}`);
      }

      console.log(`[组合管理] 目录结构检查完成`);
    } catch (error) {
      console.error(`[组合管理] 创建目录结构失败:`, error);
      throw error;
    }
  }

  /**
   * 保存所有运行中策略的状态
   */
  async _saveAllStrategyStates() {
    console.log(`[组合管理] 保存用户${this.userId}所有策略状态...`);
    // TODO: 调用Wonder Trader的状态保存接口
    // 或者通过策略文件的状态保存机制
    
    // 暂时模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 100));
    console.log(`[组合管理] 策略状态保存完成`);
  }

  /**
   * 将策略添加到组合配置
   */
  async _addStrategyToGroup(strategyId) {
    try {
      // 检查策略是否已在组合中
    const existing = await GroupStrategy.findOne({
      where: { 
          groupid: this.groupId,
          strategyid: strategyId 
      }
    });

    if (!existing) {
      await GroupStrategy.create({
          groupid: this.groupId,
          strategyid: strategyId,
        status: 'active'
      });
        console.log(`[组合管理] 策略${strategyId}已添加到组合${this.groupId}`);
    } else {
      // 如果已存在但状态为inactive，则激活
        if (existing.status !== 'active') {
        await existing.update({ status: 'active' });
        console.log(`[组合管理] 策略${strategyId}已激活`);
      }
      }
    } catch (error) {
      console.error(`[组合管理] 添加策略到组合失败:`, error);
      throw error;
    }
  }

  /**
   * 从组合配置移除策略
   */
  async _removeStrategyFromGroup(strategyId) {
    try {
    const groupStrategy = await GroupStrategy.findOne({
      where: { 
          groupid: this.groupId,
          strategyid: strategyId 
      }
    });

    if (groupStrategy) {
      await groupStrategy.update({ status: 'inactive' });
        console.log(`[组合管理] 策略${strategyId}已从组合${this.groupId}移除`);
      }
    } catch (error) {
      console.error(`[组合管理] 从组合中移除策略失败:`, error);
      throw error;
    }
  }

  /**
   * 启动组合引擎
   */
  async _startGroupEngine(token) {
    try {
      console.log(`[组合管理] 开始启动组合引擎...`);

      // 1. 生成组合的全部配置文件
      const configResult = await this._generateGroupConfig(token);
      if (!configResult.success) {
        throw new Error('配置文件生成失败');
      }
      
      // 2. 更新 live_DT 目录下的 contracts.json 并重启 runDT.py
      console.log(`[组合管理] 更新数据引擎 contracts.json 并重启 runDT.py...`);
      // _updateLiveDTContracts 内部会调用 symbolCounter 来获取全市场活跃品种
      await this._updateLiveDTContracts(configResult.universe, configResult.tradingType); 
      await this._restartDataEngine(); // 等待数据引擎重启完成
      
      // 3. 调用策略服务器的运行端点，移交控制权
      const config = require('../config.json');
      const strategyService = config.strategy_service;
      const baseURL = `http://${strategyService.host}:${strategyService.port}`;
      
      const axiosInstance = axios.create({
        baseURL: baseURL,
        timeout: 20000, // 20秒超时
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // 只传递组合路径和项目ID，由Python端负责运行
      const response = await axiosInstance.post('/strategy/live/run', {
        group_path: this.groupPath,
        project_id: `live_project_${this.groupId}`
      });

      if (!response.data.success) {
        throw new Error(`策略服务器启动失败: ${response.data.error}`);
      }

      // 保存进程ID到数据库
      if (response.data.pid) {
        await this.group.update({ 
          status: 'running',
          enginePid: response.data.pid,
          startTime: new Date()
        });
        console.log(`[组合管理] 组合引擎启动完成，PID: ${response.data.pid}`);
      } else {
        throw new Error('策略服务器未返回有效的进程PID');
      }

      return response.data;

    } catch (error) {
      console.error(`[组合管理] 启动组合引擎失败:`, error);
      if (error.response) {
        console.error('[组合管理] 策略服务器响应错误:', error.response.data);
      } else if (error.request) {
        console.error('[组合管理] 请求策略服务器无响应:', error.request);
      }
      throw error;
    }
  }

  /**
   * 生成组合配置文件
   */
  async _generateGroupConfig(token = '') {
    try {
      console.log(`[组合管理] 生成组合配置文件...`);
      console.log(`[组合管理] 配置文件将保存到目录: ${this.groupPath}`);

      // 获取组合中的所有策略
      const groupStrategies = await GroupStrategy.findAll({
        where: { groupid: this.groupId },
        include: [
          {
            model: LiveStrategy,
            as: 'strategy'
          }
        ]
      });

      if (groupStrategies.length === 0) {
        console.log(`[组合管理] 组合中没有策略，不生成配置文件`);
        return false;
      }

      // 获取第一个策略的策略ID (不是策略对象本身)
      const mainStrategyId = groupStrategies[0].strategyid;
      
      // 用策略ID到live_strategies表获取详细信息
      const mainStrategy = await LiveStrategy.findByPk(mainStrategyId);
      
      console.log(`[组合管理] 主策略详情: ${JSON.stringify(mainStrategy)}`);

      if (!mainStrategy) {
        throw new Error(`未找到策略${mainStrategyId}的详细信息`);
      }
      
      const initialCapital = mainStrategy.initialCapital || 100000;
      const channelId = mainStrategy.accountId; // 这就是channelId (注意驼峰命名)
      
      // 调试信息
      console.log(`[组合管理] 调试 - mainStrategy.accountId 的值: '${mainStrategy.accountId}'`);
      console.log(`[组合管理] 调试 - mainStrategy.accountId 的类型: ${typeof mainStrategy.accountId}`);
      console.log(`[组合管理] 调试 - channelId 的值: '${channelId}'`);
      console.log(`[组合管理] 调试 - channelId 的类型: ${typeof channelId}`);
      
      console.log(`[组合管理] 使用策略${mainStrategyId}，channelId: ${channelId}，初始资金: ${initialCapital}`);
      
      // 直接调用getConfigFiles获取所有配置文件内容
      console.log(`[组合管理] 获取配置文件内容`);
      const configFiles = await this.getConfigFiles(mainStrategyId, token);
      
      // 使用通用函数收集组合中所有策略的universe和交易类型
      const { universe, tradingType } = await this._collectUniverseFromStrategies(false);
      
      console.log(`[组合管理] 收集到组合中所有策略的品种，共${universe.length}个，交易类型: ${tradingType}`);
      
      // 解析主策略YAML，用于获取其他配置（保留原有逻辑以便后续使用）
      const strategyConfig = yaml.load(mainStrategy.yaml);
      
      // 创建配置文件目录
      await fsp.mkdir(this.groupPath, { recursive: true });
      console.log(`[组合管理] 创建配置目录: ${this.groupPath}`);

      // 写入所有配置文件
      const configPath = path.join(this.groupPath, 'config.yaml');
      await fsp.writeFile(configPath, configFiles['config.yaml']);
      console.log(`[组合管理] 保存配置文件: ${configPath}`);

      const contractsPath = path.join(this.groupPath, 'contracts.json');
      await fsp.writeFile(contractsPath, configFiles['contracts.json']);
      console.log(`[组合管理] 保存合约文件: ${contractsPath}`);

      const executersPath = path.join(this.groupPath, 'executers.yaml');
      await fsp.writeFile(executersPath, configFiles['executers.yaml']);
      console.log(`[组合管理] 保存执行器配置: ${executersPath}`);

      const parsersPath = path.join(this.groupPath, 'tdparsers.yaml');
      await fsp.writeFile(parsersPath, configFiles['tdparsers.yaml']);
      console.log(`[组合管理] 保存行情解析器配置: ${parsersPath}`);

      const tradersPath = path.join(this.groupPath, 'tdtraders.yaml');
      await fsp.writeFile(tradersPath, configFiles['tdtraders.yaml']);
      console.log(`[组合管理] 保存交易通道配置: ${tradersPath}`);

      const filtersPath = path.join(this.groupPath, 'filters.yaml');
      await fsp.writeFile(filtersPath, configFiles['filters.yaml']);
      console.log(`[组合管理] 保存过滤器配置: ${filtersPath}`);

      const actPolicyPath = path.join(this.groupPath, 'actpolicy.yaml');
      await fsp.writeFile(actPolicyPath, configFiles['actpolicy.yaml']);
      console.log(`[组合管理] 保存交易行为策略配置: ${actPolicyPath}`);

      // 为每个策略生成YAML文件
      for (let i = 0; i < groupStrategies.length; i++) {
        const groupStrategy = groupStrategies[i];
        // 获取策略详细信息
        const strategy = await LiveStrategy.findByPk(groupStrategy.strategyid);
        if (strategy) {
          const strategyNumber = i + 1; // 编号从1开始
          const strategyPath = path.join(this.groupPath, `strategy_${strategyNumber}.yaml`);
          await fsp.writeFile(strategyPath, strategy.yaml);
          console.log(`[组合管理] 保存策略配置: ${strategyPath}`);
        }
      }

      // 拷贝 run.py 模板文件 - 使用统一路径查找
      const backendRelativePath = findBackendRelativePath(__dirname);
      if (backendRelativePath) {
        const templateDir = path.join(__dirname, backendRelativePath, '_Providers', '_Python', 'strategy', 'portfolio', 'tmpl', 'portfolio_0');
        const runTemplate = path.join(templateDir, 'run.py');
        const runTarget = path.join(this.groupPath, 'run.py');

        try {
          await fsp.copyFile(runTemplate, runTarget);
          console.log(`[组合管理] 拷贝启动脚本: ${runTarget}`);
        } catch (error) {
          console.error(`[组合管理] 拷贝run.py失败: ${error.message}`);
          // 检查模板文件是否存在
          try {
            await fsp.access(runTemplate);
            console.error(`[组合管理] 模板文件存在但拷贝失败: ${runTemplate}`);
          } catch {
            console.error(`[组合管理] 模板文件不存在: ${runTemplate}`);
          }
        }

        // 拷贝 logcfg.yaml 模板文件
        const logcfgTemplate = path.join(templateDir, 'logcfg.yaml');
        const logcfgTarget = path.join(this.groupPath, 'logcfg.yaml');

        try {
          await fsp.copyFile(logcfgTemplate, logcfgTarget);
          console.log(`[组合管理] 拷贝日志配置: ${logcfgTarget}`);
        } catch (error) {
          console.error(`[组合管理] 拷贝logcfg.yaml失败: ${error.message}`);
        }
      } else {
        console.error(`[组合管理] 无法找到backend目录，跳过模板文件拷贝`);
      }

      // 创建日志目录
      const logsDir = path.join(this.groupPath, 'logs');
      try {
        await fsp.mkdir(logsDir, { recursive: true });
        console.log(`[组合管理] 创建日志目录: ${logsDir}`);
      } catch (error) {
        console.error(`[组合管理] 创建日志目录失败: ${error.message}`);
      }

      console.log(`[组合管理] 组合配置文件生成完成，保存在目录: ${this.groupPath}`);
      return { success: true, universe, tradingType };
    } catch (error) {
      console.error(`[组合管理] 生成组合配置文件失败:`, error);
      return { success: false };
    }
  }

  /**
   * 生成数据引擎配置内容
   */
  async _generateDataKitContent(tradingType) {
    const yaml = require('js-yaml');

    // 尝试从config.json中读取openctp_presets.parser配置
    let openctp_presets = {
      broker: '',
      code: '',
      front: '',
      user: '',
      pass: '',
      ctpmodule: ''
    };

    try {
      // 使用统一的backend路径查找方式
      const backendRelativePath = findBackendRelativePath(__dirname);
      if (!backendRelativePath) {
        throw new Error('无法找到backend目录');
      }
      
      const configPath = path.join(__dirname, backendRelativePath, '_Providers', '_Python', 'config.json');
      console.log(`[组合管理] 尝试加载配置文件: ${configPath}`);
      
      const configContent = await fsp.readFile(configPath, 'utf8');
      const config = JSON.parse(configContent);
      
      if (config.openctp_presets && config.openctp_presets.parser) {
        openctp_presets = config.openctp_presets.parser;
        console.log(`[组合管理] 成功从config.json加载OpenCTP预设配置`);
      } else {
        console.warn(`[组合管理] config.json中未找到openctp_presets.parser配置`);
      }
    } catch (error) {
      console.error(`[组合管理] 加载OpenCTP预设配置失败: ${error.message}`);
    }

    // 根据交易类型决定使用哪种解析器
    let parserEntry;
    
    // 如果是虚拟货币类型，使用共享内存接口，否则使用OpenCTP
    if (tradingType.toLowerCase().includes('cpt') || tradingType.toLowerCase().includes('crypto')) {
      // 虚拟货币使用共享内存接口
      parserEntry = {
        active: true,
        id: 'parser0',
        module: 'ParserShm',
        path: '../../../ext_data_engine/exchange.membin',
        gpsize: 1000,
        check_span: 2
      };
      
      console.log(`[组合管理] 使用共享内存行情接口配置(虚拟货币)`);
    } else {
      // 其他所有类型(股票、ETF、期货等)使用OpenCTP接口
      parserEntry = {
        active: true,
        id: 'parser',
        module: 'ParserCTP',
        // 确保所有字段都存在，即使为空值
        broker: '',
        code: '',
        front: '',
        user: '',
        pass: '',
        ctpmodule: ''
      };
      
      // 使用config.json中的配置覆盖默认空值
      if (openctp_presets.broker !== undefined) parserEntry.broker = openctp_presets.broker;
      if (openctp_presets.code !== undefined) parserEntry.code = openctp_presets.code;
      if (openctp_presets.front !== undefined) parserEntry.front = openctp_presets.front;
      if (openctp_presets.user !== undefined) parserEntry.user = openctp_presets.user;
      if (openctp_presets.pass !== undefined) parserEntry.pass = openctp_presets.pass;
      if (openctp_presets.ctpmodule !== undefined) parserEntry.ctpmodule = openctp_presets.ctpmodule;
      
      console.log(`[组合管理] 使用OpenCTP行情接口配置(${tradingType})`);
    }

    const config = {
      parsers: [parserEntry],
      writer: {
        module: 'WtDtStorage',
        async: tradingType.toLowerCase().includes('stock'),  // 股票推荐异步，期货推荐同步
        groupsize: 20,
        path: '../data_storage',
        savelog: false
      },
      broadcaster: {
        active: true,
        bport: 3997,
        broadcast: [{
          host: '127.0.0.1',
          port: 9001,
          type: 2
        }]
      }
    };

    return yaml.dump(config, { indent: 2 });
  }

  /**
   * 生成数据引擎日志配置内容
   */
  _generateDataKitLogContent() {
    const yaml = require('js-yaml');

    const config = {
      dyn_pattern: '%d{%Y.%m.%d %H:%M:%S} [%p] <%t> %m%n',
      level: 'debug',
      sinks: [
        {
          filename: 'DtLogs/DataKit.log',
          pattern: '%d{%Y.%m.%d %H:%M:%S} [%p] <%t> %m%n',
          truncate: false,
          type: 'daily_file_sink'
        },
        {
          pattern: '%^%d{%H:%M:%S} [%p] <%t> %m%n%$',
          type: 'ostream_sink'
        }
      ]
    };

    return yaml.dump(config, { indent: 2 });
  }
}

module.exports = LiveGroupManager;
