# 预先理解










frontend/src/_Dispatchers/ChartDispatcher/index.ts
- 需要完全理解画线管理逻辑
- 添加 myOverlayIds 变量
- 修改 loadDrawingLines, saveDrawingLines 等方法

frontend/src/events/events.ts
- 检查和更新画线相关的事件定义
- 可能需要简化一些不再需要的事件类型

frontend/src/_Pages/main/components/DrawingTools/index.tsx
- 理解画线工具组件的实现
- 确保画线完成时正确调用回调并记录 overlayId

frontend/src/shared_types/chart.ts
- 更新画线相关的类型定义
- 添加新的接口定义

frontend/drawingLogicReadme.md
- 新添加的逻辑说明文档，作为实现参考

backend/models/DrawingLine.js
- 修改数据库模型定义
- 更新字段结构

backend/_Handlers/chartHandler.js
- 简化 API 处理逻辑
- 只保留获取和保存两个接口，删除其他画线相关的接口例如增加画线，删除单个画线等


需要理解的关键逻辑：
frontend/drawingLogicReadme.md 中的完整设计
原前端的画线工具实现（DrawingTools 组件）
原后端的数据存储结构
原前后端的数据交互格式

修改顺序建议：
更新数据库模型
修改后端 API 处理逻辑
更新前端类型定义

# 画线工具逻辑说明

## 前端内部变量
```typescript
// 记录当前周期创建的 overlayId
myOverlayIds: Set<string>
```

## 数据结构定义

### 前端数据结构
```typescript
// 后端返回的画线记录
interface DrawingLineRecord {
  userId: number;
  symbol: string;      // JSON字符串格式
  interval: string;    // K线周期
  overlays: any[];     // overlay对象数组
}

// API请求响应
interface GetDrawingLinesResponse {
  success: boolean;
  data: DrawingLineRecord[];
}

interface SaveDrawingLinesRequest {
  symbol: Symbol;
  interval: string;
  overlays: any[];     // overlay对象数组
}

interface SaveDrawingLinesResponse {
  success: boolean;
}
```

### 后端数据模型
```javascript
const DrawingLine = sequelize.define('DrawingLine', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  symbol: {
    type: DataTypes.STRING,
    allowNull: false
  },
  interval: {
    type: DataTypes.STRING,
    allowNull: false
  },
  overlays: {
    type: DataTypes.JSON,
    allowNull: false
  }
});
```

## 前端逻辑

### 1. 画线加载流程
1. 清空 myOverlayIds 数组（数组元素就是 overlayId）
2. 获取该品种所有周期的画线记录
3. 遍历所有记录：
   - 如果是当前周期的记录：
     - 调用 createOverlay 创建画线
     - 记录生成的 overlayId 到 myOverlayIds
   - 如果是其他周期的记录：
     - 判断是否可以在当前周期显示
     - 如果可以显示，调用 createOverlay 创建画线（不记录 overlayId）

### 2. 画线创建流程
1. 用户使用画线工具创建新画线
2. 画线完成时的回调中：
   - 获取新创建的 overlay 的 id
   - 将 id 添加到 myOverlayIds

### 3. 画线保存流程
1. 调用 chart.getOverlays() 获取所有当前显示的 overlays
2. 筛选出 myOverlayIds 中包含的 overlays
3. 将筛选后的 overlays 数组连同当前 symbol 和 interval 发送到后端保存

## 后端逻辑

### 1. 获取画线
1. 接收请求参数：market, exchange, code
2. 构建 symbol 字符串
3. 查询数据库获取该品种所有周期的画线记录
4. 返回记录数组

### 2. 保存画线
1. 接收请求数据：symbol, interval, overlays数组
2. 构建 symbol 字符串
3. 使用 upsert 更新或创建记录
4. 返回成功状态

## 前后端交互

### API 接口

1. 获取画线

GET /chart/drawinglines?market=xxx&exchange=xxx&code=xxx
Response: { success: boolean, data: DrawingLineRecord[] }

2. 保存画线

POST /chart/drawinglines
Body: { symbol: Symbol, interval: string, overlays: any[] }
Response: { success: boolean }

### 数据流转说明

1. 画线加载：
   - 前端请求指定品种的所有画线记录
   - 后端返回所有周期的画线记录
   - 前端根据周期规则决定显示哪些画线

2. 画线保存：
   - 前端收集当前周期创建的所有画线
   - 将画线数组作为一个整体发送给后端
   - 后端将整个数组作为一条记录保存

## 跨周期显示规则

1. 较小周期的画线可以显示在较大周期上
2. 较大周期的画线不能显示在较小周期上
3. 周期大小关系：
   - 1分钟 < 5分钟 < 15分钟 < 30分钟 < 1小时 < 4小时 < 日线 < 周线

## 注意事项

1. myOverlayIds 只记录当前周期创建的画线
2. 其他周期的画线虽然可能显示，但不记录其 ID
3. 保存时只保存当前周期创建的画线
4. 每个品种每个周期在数据库中只有一条记录
5. 画线记录中的 overlays 数组包含完整的 overlay 对象