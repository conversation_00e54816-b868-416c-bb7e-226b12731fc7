#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths配置管理

该模块提供了配置管理功能，用于加载和保存配置。
"""

import os
import json
import logging

logger = logging.getLogger("EasyTrader-THS-Config")

class Config:
    """配置管理类"""
    
    def __init__(self, config_file=None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        self.config_file = config_file or os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            'config.json'
        )
        self.config = self._get_default_config()
        self.load()
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            # 客户端配置
            "client": {
                "username": "",
                "backend_url": "http://localhost:8000",
                "local_port": 8888,
                "heartbeat_interval": 60,
                "ths_path": ""
            },
            
            # 服务端配置
            "server": {
                "api_prefix": "/api/easytrader_ths",
                "cleanup_interval": 300,
                "client_timeout": 600
            },
            
            # 适配器配置
            "adapter": {
                "timeout": 10,
                "log_dir": "logs"
            }
        }
    
    def load(self):
        """加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 递归更新配置
                    self._update_config(self.config, loaded_config)
                logger.info(f"已加载配置文件: {self.config_file}")
            except Exception as e:
                logger.error(f"加载配置文件失败: {str(e)}")
        else:
            logger.warning(f"配置文件不存在: {self.config_file}，将使用默认配置")
            self.save()
    
    def save(self):
        """保存配置"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            logger.info(f"已保存配置文件: {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")
            return False
    
    def get(self, section, key=None, default=None):
        """
        获取配置
        
        Args:
            section: 配置节，如'client', 'server', 'adapter'
            key: 配置键，如果为None则返回整个节
            default: 默认值，如果配置不存在则返回该值
        
        Returns:
            配置值
        """
        if section not in self.config:
            return default
        
        if key is None:
            return self.config[section]
        
        return self.config[section].get(key, default)
    
    def set(self, section, key, value):
        """
        设置配置
        
        Args:
            section: 配置节，如'client', 'server', 'adapter'
            key: 配置键
            value: 配置值
        
        Returns:
            bool: 是否设置成功
        """
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
        return self.save()
    
    def _update_config(self, target, source):
        """
        递归更新配置
        
        Args:
            target: 目标配置
            source: 源配置
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_config(target[key], value)
            else:
                target[key] = value

# 全局配置实例
config = Config()
