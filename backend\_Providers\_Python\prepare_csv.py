# backend/_Providers/_Python/prepare_csv.py

import os
import json
import pandas as pd
import datetime
import struct
import time
import pytz
import sys # Import sys for stderr
from typing import List, Dict, Optional, Callable

# === 新增: 查找项目根目录和 root 路径替换工具 ===
def find_project_root():
    """
    向上查找 backend 目录，返回其上一级目录（即项目根目录）的绝对路径。
    """
    import os
    current = os.path.abspath(os.path.dirname(__file__))
    while True:
        if os.path.isdir(os.path.join(current, 'backend')):
            return current
        parent = os.path.dirname(current)
        if parent == current:
            raise RuntimeError("未找到 backend 目录，无法定位项目根目录")
        current = parent


def replace_root_prefix(path):
    """
    如果 path 以 root/ 开头，则将 root 替换为项目根目录绝对路径。
    否则原样返回。
    """
    import os
    if path.startswith('root/'):
        project_root = find_project_root()
        rel_path = path[5:]
        return os.path.normpath(os.path.join(project_root, rel_path))
    return path

# --- Default Configuration ---
DEFAULT_TIMEZONE = 'Asia/Shanghai'

# --- Mappings ---
# ETF代码前缀列表
ETF_PREFIXES = ['51', '511', '513', '518', '56', '588', '15', '16', '159', '1590']

def is_etf(numeric_code: str) -> bool:
    """判断代码（纯数字部分）是否为ETF"""
    return any(numeric_code.startswith(prefix) for prefix in ETF_PREFIXES)

# WonderTrader Exchange to TDX Market Subdirectory
TDX_MARKET_MAP = {
    'SSE': 'sh',  # 上海交易所
    'SZSE': 'sz', # 深圳交易所
    'BSE': 'bj',  # 北京交易所
    'CFFEX': 'ds', # 中金所
    'SHFE': 'ds',  # 上期所
    'DCE': 'ds',   # 大商所
    'CZCE': 'ds',  # 郑商所
    'INE': 'ds',   # 上海国际能源交易中心
    'HKEX': 'ds',  # 港交所
    'NASDAQ': 'ds', # 纳斯达克
    'NYSE': 'ds', # 纽约证券交易所
    'AMEX': 'ds', # 美国证券交易所
}

# 交易所对应的通达信市场代码映射
TDX_MARKET_CODE_MAP = {
    'CFFEX': '47', # 股指期货
    'SHFE': '30',  # 上海期货
    'DCE': '29',   # 大连商品
    'CZCE': '28',  # 郑州商品
    'INE': '30',   # 按上海期货处理
    'HKEX': '31',  # 香港主板
    'NASDAQ': '74', # 纳斯达克
    'NYSE': '74', # 纽约证券交易所
    'AMEX': '74', # 美国证券交易所
}

# WonderTrader Period to TDX Subdir and File Extension
WT_PERIOD_MAP = {
    '1d': {'subdir': 'lday', 'ext': '.day', 'wt_suffix': '_d'},
    'min5': {'subdir': 'fzline', 'ext': '.lc5', 'wt_suffix': '_m5'},
    '5m': {'subdir': 'fzline', 'ext': '.lc5', 'wt_suffix': '_m5'},
    # 'min1': {'subdir': 'minline', 'ext': '.lc1', 'wt_suffix': '_m1'},
    # '1m': {'subdir': 'minline', 'ext': '.lc1', 'wt_suffix': '_m1'},
}

# --- Configuration Loading ---
def load_config() -> Optional[Dict]:
    """Loads configuration from config.json"""
    config = None
    try:
        script_dir = os.path.dirname(__file__)
        config_path = os.path.join(script_dir, 'config.json')
        # print(f"[DEBUG] Attempting to load config from: {config_path}") # Removed debug

        if not os.path.exists(config_path):
            print(f"[ERROR] prepare_csv: config.json not found at: {config_path}", file=sys.stderr)
            return None

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        # print(f"[INFO] prepare_csv: Successfully loaded config from: {config_path}") # Removed info
        return config

    except FileNotFoundError:
        print(f"[ERROR] prepare_csv: config.json not found at expected location.", file=sys.stderr)
        return None
    except json.JSONDecodeError as jde:
         print(f"[ERROR] prepare_csv: Error decoding config.json: {jde}", file=sys.stderr)
         return None
    except Exception as e:
        print(f"[ERROR] prepare_csv: Failed to load configuration: {e}", file=sys.stderr)
        # Add traceback print for debugging if needed:
        # import traceback
        # traceback.print_exc()
        return None


def read_tdx_day_data(file_path: str, mytz: pytz.BaseTzInfo, numeric_code: str) -> List[Dict]:
    """读取通达信日线数据 (.day)"""
    print(f"[数据准备] 读取日线文件: {file_path}")
    if not os.path.exists(file_path):
        print(f"[数据准备] 日线文件不存在: {file_path}")
        return []

    klines = []
    try:
        with open(file_path, 'rb') as f:
            buffer = f.read()
            record_size = 32
            record_count = len(buffer) // record_size

            for i in range(record_count):
                pos = i * record_size
                if pos + record_size > len(buffer):
                    print(f"[数据准备] 文件末尾记录不完整: {file_path}, 索引 {i}")
                    break

                # 读取日期 (4字节整型)
                date_value = int.from_bytes(buffer[pos:pos+4], byteorder='little')
                year = date_value // 10000
                month = (date_value % 10000) // 100
                day = date_value % 100

                if not (1990 <= year <= datetime.datetime.now().year + 1 and 1 <= month <= 12 and 1 <= day <= 31):
                    print(f"[数据准备] 无效日期值 {date_value}，跳过记录 {i}")
                    continue

                try:
                    dt_aware = mytz.localize(datetime.datetime(year, month, day))
                    timestamp = int(dt_aware.timestamp())
                except (ValueError, OverflowError, pytz.exceptions.InvalidTimeError) as ts_e:
                    print(f"[数据准备] 无法创建时间戳，日期 {year}-{month}-{day}，错误: {ts_e}")
                    continue

                try:
                    # 所有价格都是整型值，需要除以100
                    # 读取开盘价 (4字节整型)
                    divisor = 1000.0 if is_etf(numeric_code) else 100.0
                    open_price = int.from_bytes(buffer[pos+4:pos+8], byteorder='little') / divisor
                    # 读取最高价 (4字节整型)
                    high_price = int.from_bytes(buffer[pos+8:pos+12], byteorder='little') / divisor
                    # 读取最低价 (4字节整型)
                    low_price = int.from_bytes(buffer[pos+12:pos+16], byteorder='little') / divisor
                    # 读取收盘价 (4字节整型)
                    close_price = int.from_bytes(buffer[pos+16:pos+20], byteorder='little') / divisor

                    # 读取成交额 (4字节浮点数)
                    amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
                    # 读取成交量 (4字节整型)
                    volume = int.from_bytes(buffer[pos+24:pos+28], byteorder='little')

                    # 检查成交量是否需要除以100
                    if volume > 0:
                        approx_price = amount / volume
                        if approx_price > close_price * 10:
                            volume = volume * 100

                    # 检查数据合理性
                    if not (open_price > 0 and high_price > 0 and low_price > 0 and close_price > 0 and volume >= 0 and amount >= 0):
                        print(f"[数据准备] 数据值异常: 开:{open_price} 高:{high_price} 低:{low_price} 收:{close_price} 量:{volume} 额:{amount}")
                        continue

                    klines.append({
                        'time': timestamp,
                        'open': float(open_price),
                        'high': float(high_price),
                        'low': float(low_price),
                        'close': float(close_price),
                        'volume': float(volume),
                        'amount': float(amount)
                    })
                except struct.error as se:
                     print(f"[数据准备] 解析价格数据失败: {se}")
                     continue

    except FileNotFoundError:
        print(f"[数据准备] 文件不存在: {file_path}")
        return []
    except Exception as e:
        print(f"[ERROR] prepare_csv: 读取日线文件失败 {file_path}: {e}", file=sys.stderr)
        return []

    print(f"[数据准备] 成功读取日线数据: {file_path}, 共 {len(klines)} 条记录")
    return klines

def read_tdx_min5_data(file_path: str, mytz: pytz.BaseTzInfo, numeric_code: str) -> List[Dict]:
    """读取通达信5分钟线数据 (.lc5)，解析方式完全对齐 app.py 的 read_tdx_min_data"""
    print(f"[数据准备] 读取5分钟线文件: {file_path}")
    if not os.path.exists(file_path):
        print(f"[数据准备] 5分钟线文件不存在: {file_path}")
        return []

    klines = []
    recentDaySessionDay = 0
    try:
        with open(file_path, 'rb') as f:
            buffer = f.read()
            record_size = 32
            record_count = len(buffer) // record_size

            for i in range(record_count):
                pos = i * record_size
                if pos + record_size > len(buffer):
                    print(f"[数据准备] 文件末尾记录不完整: {file_path}, 索引 {i}")
                    break
                try:
                    # 读取日期时间 (前2字节日期，后2字节分钟)
                    date_time_bytes = buffer[pos:pos+4]
                    date_part = int.from_bytes(date_time_bytes[0:2], byteorder='little')
                    time_part = int.from_bytes(date_time_bytes[2:4], byteorder='little')

                    # 计算年月日
                    year = (date_part // 2048) + 2004
                    month = (date_part % 2048) // 100
                    day = (date_part % 2048) % 100
                    # 计算时分
                    hour = time_part // 60
                    minute = time_part % 60

                    if not (1990 <= year <= datetime.datetime.now().year + 1 and 1 <= month <= 12 and 1 <= day <= 31 and 0 <= hour <= 23 and 0 <= minute <= 59):
                        print(f"[数据准备] 无效日期时间: 年:{year} 月:{month} 日:{day} 时:{hour} 分:{minute}")
                        continue

                    dt_naive = datetime.datetime(year, month, day, hour, minute)
                    dt_aware = mytz.localize(dt_naive)
                    timestamp = int(dt_aware.timestamp())

                    # 处理夜盘
                    is_night_trading = hour >= 17 or hour < 4
                    adjusted_timestamp = timestamp
                    if is_night_trading:
                        current_or_next_workday = dt_aware.date()
                        while current_or_next_workday.weekday() >= 5:
                            current_or_next_workday += datetime.timedelta(days=1)
                        if dt_aware.weekday() < 5 and hour >= 17:
                            current_or_next_workday += datetime.timedelta(days=1)
                            while current_or_next_workday.weekday() >= 5:
                                current_or_next_workday += datetime.timedelta(days=1)
                        day_start_ts = int(mytz.localize(datetime.datetime(
                            current_or_next_workday.year,
                            current_or_next_workday.month,
                            current_or_next_workday.day
                        )).timestamp())
                        time_offset = hour * 3600 + minute * 60
                        adjusted_timestamp = day_start_ts + time_offset

                    # 价格全部用 float32 解析，不做任何缩放
                    open_price = struct.unpack('<f', buffer[pos+4:pos+8])[0]
                    high_price = struct.unpack('<f', buffer[pos+8:pos+12])[0]
                    low_price = struct.unpack('<f', buffer[pos+12:pos+16])[0]
                    close_price = struct.unpack('<f', buffer[pos+16:pos+20])[0]
                    amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
                    volume = int.from_bytes(buffer[pos+24:pos+28], byteorder='little')

                    # 检查成交量是否需要除以100
                    if volume > 0:
                        approx_price = amount / volume
                        if approx_price > close_price * 10:
                            volume = volume * 100

                    # 检查数据合理性
                    if not (open_price > 0 and high_price > 0 and low_price > 0 and close_price > 0 and volume >= 0 and amount >= 0):
                        print(f"[数据准备] 数据值异常: 开:{open_price} 高:{high_price} 低:{low_price} 收:{close_price} 量:{volume} 额:{amount}")
                        continue

                    klines.append({
                        'time': adjusted_timestamp,
                        'open': float(open_price),
                        'high': float(high_price),
                        'low': float(low_price),
                        'close': float(close_price),
                        'volume': float(volume),
                        'amount': float(amount)
                    })
                except (ValueError, OverflowError, struct.error, pytz.exceptions.InvalidTimeError) as e:
                    print(f"[数据准备] 处理记录错误: {e}")
                    continue
                except Exception as inner_e:
                    print(f"[ERROR] prepare_csv: 处理5分钟记录异常 {i} in {file_path}: {inner_e}", file=sys.stderr)
                    continue
    except FileNotFoundError:
        print(f"[数据准备] 文件不存在: {file_path}")
        return []
    except Exception as e:
        print(f"[ERROR] prepare_csv: 读取5分钟文件失败 {file_path}: {e}", file=sys.stderr)
        return []

    print(f"[数据准备] 成功读取5分钟数据: {file_path}, 共 {len(klines)} 条记录")
    return klines

# --- Filename Parsing/Generation ---
def parse_wt_code(wt_code, period):
    """
    解析形如 'SSE.600000' 的 WonderTrader 代码，并添加周期后缀。

    参数:
    wt_code (str): 原始的 WonderTrader 代码。
    period (str): 周期字符串。

    返回:
    str: 组合后的字符串，或者在解析失败时返回 None。
    """
    parts = wt_code.split('.')
    if len(parts) == 2:
        # 合法的交易所代码和市场代码
        exchange_code = parts[0].upper()
        market_code = parts[1]
        return f"{exchange_code}.{market_code}_{period}.csv"
    elif len(parts) > 2 and parts[0].upper() in ('CFFEX', 'SHFE', 'DCE', 'CZCE', 'INE', 'HKEX', 'SSE', 'SZSE', 'BSE', 'NASDAQ', 'NYSE', 'AMEX'):
        # 合法的交易所代码和市场代码
        exchange_code = parts[0].upper()
        market_code = '.'.join(parts[1:])
        return f"{exchange_code}.{market_code}_{period}.csv"
    else:
        return None


def get_tdx_market_and_code(wt_exchange: str, wt_code_part: str) -> Optional[tuple[str, str]]:
    """Gets the TDX market subdir and the filename code part."""
    tdx_market = TDX_MARKET_MAP.get(wt_exchange)
    if not tdx_market:
        # Fallback for 'ds' or unknown exchanges? Currently returns None.
        return None
    tdx_file_code = f"{tdx_market}{wt_code_part}"
    return tdx_market, tdx_file_code

def get_tdx_file_path(wt_code: str, period: str, tdx_root: str) -> Optional[str]:
    """
    构建通达信数据文件的完整路径。
    处理'交易所.代码'、'交易所.品类.代码'等格式。

    Args:
        wt_code: WonderTrader格式的代码
        period: 周期，例如'day'或'min5'
        tdx_root: 通达信数据根目录

    Returns:
        构建的通达信数据文件路径，失败时返回None
    """
    # 解析WonderTrader代码
    parts = wt_code.split('.')
    if len(parts) < 2: # 至少需要 交易所.代码
        print(f"[ERROR] prepare_csv: get_tdx_file_path - 无效WT代码格式 (至少两部分): {wt_code}", file=sys.stderr)
        return None

    exchange = parts[0].upper()
    # symbol_parts 包含交易所之后的所有部分
    symbol_parts = parts[1:]

    # 检查周期是否支持
    period_info = WT_PERIOD_MAP.get(period.lower())
    if not period_info:
        print(f"[ERROR] prepare_csv: get_tdx_file_path - 不支持的周期: {period}", file=sys.stderr)
        return None

    tdx_subdir = period_info['subdir']  # lday 或 fzline
    tdx_ext = period_info['ext']        # .day 或 .lc5

    # 获取通达信市场目录 ('sh', 'sz', 'bj', 'ds')
    tdx_market = TDX_MARKET_MAP.get(exchange)
    if not tdx_market:
        print(f"[ERROR] prepare_csv: get_tdx_file_path - 无法映射交易所: {exchange}", file=sys.stderr)
        return None

    # 构建通达信文件名 (不含扩展名)
    tdx_filename_stem = ""
    if tdx_market in ['sh', 'sz', 'bj']:
        # 股票/ETF/基金类: 取最后一部分作为代码，前面加上市场标识
        # SSE.ETF.510300 -> sh510300
        # SSE.600000 -> sh600000
        symbol_code = symbol_parts[-1] # 取最后一部分
        tdx_filename_stem = f"{tdx_market}{symbol_code}"
        tdx_file_path = os.path.join(tdx_root, tdx_market, tdx_subdir, f"{tdx_filename_stem}{tdx_ext}")

    elif tdx_market == 'ds':
        # 期货或其他 ('ds' 目录): 取交易所对应的市场数字代码，加上'#'，再加上交易所后的所有部分
        # SHFE.fu.HOT -> 30#fu.HOT
        market_code_num = TDX_MARKET_CODE_MAP.get(exchange)
        if not market_code_num:
            print(f"[ERROR] prepare_csv: get_tdx_file_path - 无法获取{exchange}的通达信市场数字代码", file=sys.stderr)
            return None

        tdx_symbol_string = '.'.join(symbol_parts) # 重新组合交易所后的部分
        tdx_filename_stem = f"{market_code_num}#{tdx_symbol_string}"
        tdx_file_path = os.path.join(tdx_root, tdx_market, tdx_subdir, f"{tdx_filename_stem}{tdx_ext}")
    else:
        # 其他未知的市场类型
        print(f"[ERROR] prepare_csv: get_tdx_file_path - 未知的TDX市场目录类型: {tdx_market} for exchange {exchange}", file=sys.stderr)
        return None

    print(f"[数据准备] WT代码: {wt_code} (周期: {period}) -> 查找TDX文件: {tdx_file_path}")
    return tdx_file_path

def get_wt_csv_filename(wt_code: str, period: str) -> Optional[str]:
    """
    根据 WonderTrader 代码和周期构建目标 CSV 文件名。
    直接使用 wt_code 格式，加上周期后缀。
    例如： SSE.ETF.510300, day -> SSE.ETF.510300_d.csv
    """
    period_info = WT_PERIOD_MAP.get(period.lower())
    if not period_info:
        print(f"[ERROR] prepare_csv: get_wt_csv_filename - 不支持的周期: {period}", file=sys.stderr)
        return None
    wt_suffix = period_info['wt_suffix'] # _d, _m5 etc.

    # 直接使用 wt_code 并附加后缀
    wt_filename = f"{wt_code}{wt_suffix}.csv"

    return wt_filename

# --- Main CSV Preparation Function ---
def prepare_csv_files(codes: List[str], period: str, output_dir: str) -> bool:
    """
    Prepares WonderTrader compliant CSV files from TDX data. Runs silently unless errors occur.

    Args:
        codes (List[str]): List of WonderTrader codes (e.g., ['SSE.600000']).
        period (str): Desired period ('day', 'min5').
        output_dir (str): Directory to save the output CSV files.

    Returns:
        bool: True if all files were processed (or skipped) without fatal errors,
              False if configuration failed or major errors occurred.
    """

    if period.lower() == 'day':
        period = '1D'
    elif period.lower() == 'week':
        period = '1W'
    elif period.lower() == 'month':
        period = '1M'

    print(f"[INFO] prepare_csv: Starting CSV Preparation for {len(codes)} codes, period: {period}, output: {output_dir}") # Basic info

    # 更改当前目录
    original_cwd = os.getcwd()
    script_dir = os.path.dirname(__file__)
    #os.chdir(script_dir)

    config = load_config()
    if not config:
        print("[ERROR] prepare_csv: Failed to load configuration. Aborting.", file=sys.stderr)
        return False

    # === 路径处理: 用 replace_root_prefix 统一处理所有 root/ 路径 ===
    tdx_data_root = replace_root_prefix(config.get('tdx_data', {}).get('path', ''))
    tdx_symbols_root = replace_root_prefix(config.get('tdx_symbols', {}).get('path', ''))
    crypto_data_root = replace_root_prefix(config.get('crypto_data', {}).get('path', ''))

    print(f"[INFO] prepare_csv: Using TDX Data Root: {tdx_data_root}")
    print(f"[INFO] prepare_csv: Using Config Dir: {script_dir}")

    if not os.path.isabs(tdx_data_root):
        tdx_data_root = os.path.abspath(os.path.join(script_dir, tdx_data_root))

    # print(f"[INFO] prepare_csv: Using TDX Data Root: {tdx_data_root}") # Removed info
    if not os.path.isdir(tdx_data_root):
        print(f"[ERROR] prepare_csv: TDX data path is not a valid directory: {tdx_data_root}. Aborting.", file=sys.stderr)
        return False

    timezone_str = config.get('default_timezone', DEFAULT_TIMEZONE)
    try:
        mytz = pytz.timezone(timezone_str)
    except pytz.UnknownTimeZoneError:
        print(f"[ERROR] prepare_csv: Unknown timezone '{timezone_str}' in config. Using default '{DEFAULT_TIMEZONE}'.", file=sys.stderr)
        mytz = pytz.timezone(DEFAULT_TIMEZONE)

    period_info = WT_PERIOD_MAP.get(period.lower())
    if not period_info:
        print(f"[ERROR] prepare_csv: Unsupported period '{period}'. Cannot select TDX reader function. Aborting.", file=sys.stderr)
        return False
    read_func: Optional[Callable] = None
    if period.lower() in ('1d',):
        read_func = read_tdx_day_data
    elif period.lower() in ('min5', '5m'):
        read_func = read_tdx_min5_data
    # elif period.lower() in ('min1', '1m'):
    #     read_func = read_tdx_min1_data

    if read_func is None:
         print(f"[ERROR] prepare_csv: No TDX reader function implemented for period '{period}'. Aborting.", file=sys.stderr)
         return False

    try:
        os.makedirs(output_dir, exist_ok=True)
    except OSError as e:
        print(f"[ERROR] prepare_csv: Failed to create output directory {output_dir}: {e}. Aborting.", file=sys.stderr)
        return False

    # --- Process each code ---
    files_updated = 0
    files_skipped = 0
    files_failed = 0

    for wt_code in codes:
        # print(f"[INFO] prepare_csv: Processing code: {wt_code} for period: {period}") # Reduced verbosity

        tdx_file_path = get_tdx_file_path(wt_code, period, tdx_data_root)
        if not tdx_file_path:
            # print(f"  [ERROR] Failed to determine TDX file path for {wt_code}. Skipping.") # Error printed inside func
            files_failed += 1
            continue

        if not os.path.exists(tdx_file_path):
            # print(f"  [WARN] TDX source file not found: {tdx_file_path}. Skipping.") # Reduced verbosity
            files_skipped += 1
            continue

        wt_csv_filename = get_wt_csv_filename(wt_code, period)
        if not wt_csv_filename:
            print(f"[ERROR] prepare_csv: Failed to generate WT CSV filename for {wt_code} and period {period}. Skipping.", file=sys.stderr)
            files_failed += 1
            continue
        wt_csv_path = os.path.join(output_dir, wt_csv_filename)
        # print(f"  [DEBUG] Target WT CSV path: {wt_csv_path}") # Removed debug

        # --- Extract numeric code part from wt_code ---
        parts = wt_code.split('.')
        numeric_code = parts[-1] if len(parts) >= 2 else "" # Get the last part as numeric code
        if not numeric_code:
             print(f"[WARN] prepare_csv: Could not extract numeric code from {wt_code}. ETF check might be inaccurate.", file=sys.stderr)
             # Fallback or skip? For now, continue with empty string, is_etf will return False.

        needs_update = False
        try:
            tdx_mtime = os.path.getmtime(tdx_file_path)
            if not os.path.exists(wt_csv_path):
                # print(f"  Target CSV does not exist. Update required.") # Reduced verbosity
                needs_update = True
            else:
                csv_mtime = os.path.getmtime(wt_csv_path)
                if tdx_mtime > csv_mtime + 1:
                    # print(f"  TDX file is newer (TDX: {tdx_mtime}, CSV: {csv_mtime}). Update required.") # Reduced verbosity
                    needs_update = True
                else:
                    # print(f"  Target CSV is up-to-date. Skipping.") # Reduced verbosity
                    files_skipped += 1
        except Exception as check_e:
            print(f"[ERROR] prepare_csv: Error comparing file times for {wt_code}: {check_e}. Assuming update needed.", file=sys.stderr)
            needs_update = True

        if needs_update:
            # print(f"  Converting {tdx_file_path} to {wt_csv_path}...") # Reduced verbosity
            # Pass numeric_code to the reader function
            klines = read_func(tdx_file_path, mytz, numeric_code)

            if not klines:
                print(f"[ERROR] prepare_csv: Failed to read data from TDX file: {tdx_file_path}. CSV not updated.", file=sys.stderr)
                files_failed += 1
                continue

            try:
                df = pd.DataFrame(klines)
                if df.empty:
                     print(f"[ERROR] prepare_csv: DataFrame created from TDX data is empty: {tdx_file_path}. CSV not updated.", file=sys.stderr)
                     files_failed += 1
                     continue

                # --- Start of modifications for WT format ---
                # Ensure required columns from reader exist
                if not all(col in df.columns for col in ['time', 'open', 'high', 'low', 'close', 'volume', 'amount']):
                    print(f"[ERROR] prepare_csv: DataFrame from reader missing expected columns. Actual: {df.columns}. CSV not updated.", file=sys.stderr)
                    files_failed += 1
                    continue

                # 1. Convert timestamp to datetime objects (localized)
                #    We need this for both date and time formatting.
                datetime_col = pd.to_datetime(df['time'], unit='s', utc=True).dt.tz_convert(mytz)

                # 2. Create date column based on period
                if period.lower() == '1d':
                    # 日线数据：YYYY/MM/DD格式，不包含time字段
                    df['date'] = datetime_col.dt.strftime('%Y/%m/%d')
                    # 重命名列以匹配WonderTrader日线格式
                    df.rename(columns={'amount': 'turnover'}, inplace=True)
                    output_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 'turnover']
                else:
                    # 分钟线数据：YYYY/M/D格式 + HH:MM:SS时间
                    df['Date'] = datetime_col.dt.strftime('%Y/%m/%d')
                    df['Time'] = datetime_col.dt.strftime('%H:%M:%S')
                    # 重命名列以匹配WonderTrader分钟线格式
                    df.rename(columns={'amount': 'Volume'}, inplace=True)
                    output_columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']

                # 5. Select final output columns
                # Ensure all expected output columns are present after transformation
                if not all(col in df.columns for col in output_columns):
                     print(f"[ERROR] prepare_csv: DataFrame missing required output columns after transformation {output_columns}. Actual: {df.columns}. CSV not updated.", file=sys.stderr)
                     files_failed += 1
                     continue
                df_output = df[output_columns]
                # --- End of modifications for WT format ---

                df_output.to_csv(wt_csv_path, index=False, encoding='utf-8')
                # print(f"  Successfully updated CSV: {wt_csv_path}") # Reduced verbosity
                files_updated += 1
            except Exception as write_e:
                print(f"[ERROR] prepare_csv: Failed to process DataFrame or write CSV for {wt_code}: {write_e}", file=sys.stderr)
                files_failed += 1

    print(f"[INFO] prepare_csv: CSV Preparation Finished for period: {period}. Updated: {files_updated}, Skipped: {files_skipped}, Failed: {files_failed}")

    # 如果所有文件都被跳过，且没有更新或失败，可能是因为没有更多数据了
    if files_skipped > 0 and files_updated == 0 and files_failed == 0:
        print(f"[INFO] prepare_csv: 所有文件都已是最新，没有需要更新的数据。这可能意味着通达信数据源没有更新或没有更多数据。")

    # 如果没有任何文件被处理（跳过、更新或失败），可能是因为没有找到任何数据文件
    if files_skipped == 0 and files_updated == 0 and files_failed == 0:
        print(f"[INFO] prepare_csv: 没有找到任何可处理的数据文件。请检查通达信数据路径和品种代码是否正确。")

    # 恢复当前目录
    #os.chdir(original_cwd)

    return files_failed == 0