/**
 * 交易通道处理器
 * 负责管理交易客户端的连接、心跳和状态
 * 并提供XTP接口实现
 */

const express = require('express');
const router = express.Router();
const axios = require('axios');
// --- 新增: 引入 User 模型 和 认证中间件 ---
const { User } = require('../../models'); // 假设 User 模型在 models 目录下定义
const { authenticateToken } = require('../../middleware/auth'); // 假设认证中间件路径
// --- 新增: 引入 jwt 和 config ---
const jwt = require('jsonwebtoken');
const config = require('../../config.json'); // 假设配置文件在根目录
const JWT_SECRET = config.jwt.secret; // 从配置获取JWT密钥
// --- 结束新增 ---

// 引入XTP工厂和Socket.IO初始化模块
const XtpFactory = require('./xtpFactory');
const socketIO = require('./initSocketIO');

// 创建简单的日志记录器
const logger = {
    info: (message) => console.log(`[TradeHandler INFO] ${message}`), // 添加前缀以区分日志来源
    warn: (message) => console.warn(`[TradeHandler WARN] ${message}`),
    error: (message) => console.error(`[TradeHandler ERROR] ${message}`),
    debug: (message) => console.log(`[TradeHandler DEBUG] ${message}`)
};

// --- 修改: 存储 Socket.IO 连接对象 ---
const tradingClients = new Map(); // 使用 Map 存储: clientKey -> { username, client_type, socket, registered_at, last_heartbeat }
// --- 结束修改 ---

// 存储XTP实例
const xtpInstances = new Map(); // 使用 Map 存储: clientType -> XTP实例

// 清理过期客户端的间隔（毫秒）
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5分钟

// 初始化标志
let initialized = false;
let ioNamespace = null; // 用于存储 Socket.IO 命名空间

/**
 * 初始化交易通道处理器 (HTTP 部分)
 */
function initHttp() {
    // 防止重复初始化
    if (initialized) {
        return router;
    }

    initialized = true;
    logger.info('初始化交易通道处理器');

    // 启动定时清理过期客户端的任务
    setInterval(cleanupExpiredClients, CLEANUP_INTERVAL);

    // --- 移除: 从模拟数据库加载客户端 ---
    // loadClientsFromDatabase();

    // 初始化XTP实例
    initXtpInstances();

    // 注册API路由
    registerHttpRoutes();

    return router;
}

/**
 * 注册API路由
 */
function registerHttpRoutes() {

    // 测试交易客户端连接 - *** 检查在线状态 ***
    router.post('/test_trading_client', authenticateToken, async (req, res) => {
        try {
             const targetUsername = req.body.username;
             const targetClientType = req.body.client_type;
             const clientKey = `${targetUsername}_${targetClientType}`;
             const clientInfo = tradingClients.get(clientKey);
             const requestingUsername = req.user?.username;
             logger.debug(`测试客户端连接请求 (来自用户: ${requestingUsername}): targetUser=${targetUsername}, type=${targetClientType}`);

             // TODO: 权限检查 - 是否允许 requestingUsername 测试 targetUsername 的客户端?

             // 检查客户端是否活跃
             if (clientInfo) {
                 const now = Date.now();
                 const heartbeatTimeout = 2 * 60 * 1000; // 2分钟

                 // 检查连接状态
                 let isActive = false;

                 if (clientInfo.socket) {
                     // Socket.IO 连接
                     isActive = clientInfo.socket.connected;
                 } else {
                     // HTTP API 注册
                     isActive = (now - (clientInfo.last_heartbeat || clientInfo.registered_at)) < heartbeatTimeout;
                 }

                 if (isActive) {
                     logger.info(`测试客户端连接成功 (目标: ${clientKey}): 客户端在线`);
                     return res.json({
                         success: true,
                         message: '客户端已连接',
                         data: {
                             connection_type: clientInfo.socket ? 'socket' : 'http',
                             last_heartbeat: clientInfo.last_heartbeat ? new Date(clientInfo.last_heartbeat).toISOString() : null
                         }
                     });
                 }
             }

             // 如果到这里，说明客户端不存在或不活跃
             logger.warn(`测试客户端连接失败 (目标: ${clientKey}): 客户端未找到或未连接`);
             return res.status(404).json({ success: false, message: '客户端未找到或未连接' });
        } catch (error) {
             logger.error(`测试交易客户端连接处理异常: ${error.message}`);
             return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    });

    // --- Wonder Trader XTP API接口 ---
    router.post('/wt_trade_api', async (req, res) => {
        try {
            const { acckey, username, action, params } = req.body;
            logger.debug(`收到Wonder Trader XTP请求: acckey=${acckey}, action=${action}`);

            if (!acckey || !action) {
                logger.warn('Wonder Trader XTP请求参数不完整');
                return res.status(400).json({ success: false, message: '参数不完整：缺少acckey或action' });
            }

            // acckey就是clientKey，例如: "user1_easytrader_ths"
            const clientInfo = tradingClients.get(acckey);

            if (!clientInfo) {
                logger.warn(`Wonder Trader XTP请求失败: 客户端未找到 ${acckey}`);
                return res.status(404).json({
                    success: false,
                    message: `交易客户端未找到: ${acckey}`
                });
            }

            // 检查客户端是否活跃
            const now = Date.now();
            const heartbeatTimeout = 2 * 60 * 1000; // 2分钟
            let isActive = false;

            if (clientInfo.socket) {
                isActive = clientInfo.socket.connected;
            } else {
                isActive = (now - (clientInfo.last_heartbeat || clientInfo.registered_at)) < heartbeatTimeout;
            }

            if (!isActive) {
                logger.warn(`Wonder Trader XTP请求失败: 客户端不活跃 ${acckey}`);
                return res.status(503).json({
                    success: false,
                    message: `交易客户端不活跃: ${acckey}`
                });
            }

            // 执行交易指令
            await executeTradeViaSocket(
                clientInfo.username,
                clientInfo.client_type,
                action,
                params
            );

            logger.info(`Wonder Trader XTP指令已发送: ${acckey} -> ${action}`);
            return res.json({
                success: true,
                message: '交易指令已发送',
                data: { acckey, action }
            });

        } catch (error) {
            logger.error(`Wonder Trader XTP请求处理异常: ${error.message}`);
            return res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // --- 测试连接接口 ---
    router.get('/test_connection', (req, res) => {
        res.json({
            success: true,
            message: 'tradeHandler连接正常',
            timestamp: Date.now()
        });
    });

    // --- 修改: 执行交易操作 - 通过 Socket.IO 发送命令 ---
    router.post('/execute_trade', authenticateToken, async (req, res) => {
        try {
            const username = req.user?.username; // 用户来自认证
            const { client_key, action, params } = req.body; // 改为接收client_key而不是client_type
            logger.debug(`收到交易执行请求 (来自Token用户: ${username}): client_key=${client_key}, action=${action}`);

            if (!username || !client_key || !action) {
                logger.warn('执行交易请求参数不完整或Token无效');
                return res.status(400).json({ success: false, message: '参数不完整或用户未认证' });
            }

            // 调用新的 executeTradeViaSocket 函数
            try {
                await executeTradeViaSocket(client_key, action, params); // 直接使用client_key
                 logger.info(`交易指令 '${action}' 已通过 Socket 发送给客户端 '${client_key}'`);
                 return res.json({ success: true, message: '交易指令已发送' });
            } catch (tradeError) {
                 logger.error(`发送交易指令 '${action}' 失败 for '${client_key}': ${tradeError.message}`);
                 if (tradeError.message.includes('未找到') || tradeError.message.includes('未连接')) {
                     return res.status(404).json({ success: false, message: tradeError.message });
                 }
                return res.status(500).json({ success: false, message: tradeError.message || '发送交易指令时发生未知错误' });
            }
        } catch (error) {
             logger.error(`处理 /execute_trade 请求异常: ${error.message}`);
            return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    });
    // --- 结束修改 ---

    // --- 修改: 移除交易客户端 - 断开 Socket 连接 ---
    router.post('/remove_trading_client', authenticateToken, async (req, res) => {
        try {
            const targetUsername = req.body.username;
            const targetClientType = req.body.client_type;
            const requestingUsername = req.user?.username;
             logger.debug(`收到移除客户端请求 (来自用户: ${requestingUsername}): targetUser=${targetUsername}, type=${targetClientType}`);

            if (!targetUsername || !targetClientType) {
                return res.status(400).json({ success: false, message: '参数不完整' });
            }

            // TODO: 权限检查
            if (requestingUsername !== targetUsername) {
                // logger.warn(`权限不足: 用户 ${requestingUsername} 尝试移除 ${targetUsername} 的客户端`);
                // return res.status(403).json({ success: false, message: '权限不足' });
            }

            const clientKey = `${targetUsername}_${targetClientType}`;
            const clientInfo = tradingClients.get(clientKey);

            if (!clientInfo) {
                 logger.warn(`移除失败: 客户端 Key '${clientKey}' 不存在`);
                return res.status(404).json({ success: false, message: '客户端不存在' });
            }

            if (clientInfo.socket) {
                 logger.info(`正在断开客户端连接: Key=${clientKey}, SocketID=${clientInfo.socket.id}`);
                 clientInfo.socket.disconnect(true);
            } else {
                 tradingClients.delete(clientKey);
            }

            logger.info(`交易客户端移除请求已处理: Key=${clientKey}`);
            return res.json({ success: true, message: '移除成功' });
        } catch (error) {
            logger.error(`移除交易客户端失败: ${error.message}`);
            return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    });
    // --- 结束修改 ---

    // 添加注册交易客户端路由
    router.post('/register_trading_client', async (req, res) => {
        try {
            const { username, client_ip, client_port, client_type, timestamp } = req.body;
            logger.info(`收到客户端注册请求: username=${username}, client_type=${client_type}, ip=${client_ip}, port=${client_port}`);

            if (!username || !client_ip || !client_port || !client_type || !timestamp) {
                logger.warn('客户端注册请求参数不完整');
                return res.status(400).json({ success: false, message: '参数不完整' });
            }

            // 检查用户是否存在 (可选，不强制要求)
            try {
                const user = await User.findOne({ where: { username } });
                if (!user) {
                    logger.warn(`客户端注册警告: 用户 '${username}' 不存在，但仍允许注册`);
                    // 不返回错误，允许注册继续
                }
            } catch (userError) {
                logger.warn(`查询用户时出错: ${userError.message}`);
                // 继续处理，不阻止注册
            }

            // 新增校验
            if (!username || typeof username !== 'string' || !client_type || typeof client_type !== 'string') {
                logger.warn(`HTTP注册失败: client_type 无效 (username=${username}, client_type=${client_type})`);
                return res.status(400).json({ success: false, message: 'client_type 无效' });
            }

            // 生成客户端键
            const clientKey = `${username}_${client_type}`;

            // 检查是否已存在相同的客户端
            const existingClient = tradingClients.get(clientKey);
            if (existingClient && existingClient.socket && existingClient.socket.connected) {
                logger.warn(`客户端注册失败: 已存在相同的客户端连接 (${clientKey})`);
                return res.status(409).json({ success: false, message: '已存在相同的客户端连接' });
            }

            // 注册客户端 (HTTP方式，没有socket)
            tradingClients.set(clientKey, {
                username,
                client_type,
                client_ip,
                client_port,
                registered_at: Date.now(),
                last_heartbeat: Date.now(),
                socket: null // HTTP注册没有socket
            });

            logger.info(`客户端注册成功: ${clientKey}, ip=${client_ip}, port=${client_port}`);
            return res.json({ success: true, message: '注册成功' });
        } catch (error) {
            logger.error(`处理客户端注册请求时发生错误: ${error.message}`);
            return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    });

    // 添加客户端心跳路由
    router.post('/trading_client_heartbeat', (req, res) => {
        try {
            const { username, client_type, client_ip, client_port, timestamp } = req.body;
            logger.debug(`收到客户端心跳: username=${username}, client_type=${client_type}`);

            // 新增校验
            if (!username || typeof username !== 'string' || !client_type || typeof client_type !== 'string') {
                logger.warn('客户端心跳请求参数不完整或 client_type 无效');
                return res.status(400).json({ success: false, message: '参数不完整或 client_type 无效' });
            }

            const clientKey = `${username}_${client_type}`;
            const clientInfo = tradingClients.get(clientKey);

            if (!clientInfo) {
                logger.warn(`客户端心跳失败: 客户端 '${clientKey}' 不存在`);
                return res.status(404).json({ success: false, message: '客户端不存在' });
            }

            // 更新心跳时间
            clientInfo.last_heartbeat = Date.now();

            // 如果提供了IP和端口，也更新这些信息
            if (client_ip) clientInfo.client_ip = client_ip;
            if (client_port) clientInfo.client_port = client_port;

            tradingClients.set(clientKey, clientInfo);

            logger.debug(`客户端心跳更新成功: ${clientKey}`);
            return res.json({ success: true, message: '心跳更新成功' });
        } catch (error) {
            logger.error(`处理客户端心跳请求时发生错误: ${error.message}`);
            return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    });
}

/**
 * 清理过期的客户端 (基于心跳)
 */
function cleanupExpiredClients() {
    const now = Date.now();
    const heartbeatTimeout = 2 * 60 * 1000; // 2 分钟没有心跳则认为过期

    logger.debug('开始检查过期客户端 (基于Socket心跳)...');
    tradingClients.forEach((client, key) => {
        // --- 修改: 使用 last_heartbeat 判断 ---
        if (now - (client.last_heartbeat || client.registered_at) > heartbeatTimeout) { // 如果没有心跳，用注册时间
            logger.warn(`客户端 '${key}' 检测为过期 (最后心跳: ${client.last_heartbeat ? new Date(client.last_heartbeat).toISOString() : 'N/A'}), 将断开连接.`);
            if (client.socket) {
                 client.socket.disconnect(true);
            } else {
                 tradingClients.delete(key);
            }
        }
        // --- 结束修改 ---
    });
    logger.debug('过期客户端检查完成');
}

// --- 移除旧函数 ---
/*
function isClientActive(client) { ... }
function getClient(username, client_type) { ... }
*/

// --- 新增: 执行交易操作 (通过 Socket.IO 或 HTTP API) ---
async function executeTradeViaSocket(client_key, action, params) {
     logger.debug(`准备发送交易指令: client_key=${client_key}, action=${action}`);
     const clientInfo = tradingClients.get(client_key);

     // 检查客户端是否存在
     if (!clientInfo) {
         logger.warn(`发送交易指令失败: 客户端 '${client_key}' 未找到`);
         throw new Error(`交易客户端未找到: ${client_key}`);
     }

     // 检查客户端是否活跃
     const now = Date.now();
     const heartbeatTimeout = 2 * 60 * 1000; // 2分钟
     let isActive = false;

     if (clientInfo.socket) {
         // Socket.IO 连接
         isActive = clientInfo.socket.connected;
     } else {
         // HTTP API 注册
         isActive = (now - (clientInfo.last_heartbeat || clientInfo.registered_at)) < heartbeatTimeout;
     }

     if (!isActive) {
         logger.warn(`发送交易指令失败: 客户端 '${client_key}' 不活跃`);
         throw new Error(`交易客户端不活跃: ${client_key}`);
     }

     // 根据连接类型发送指令
     if (clientInfo.socket && clientInfo.socket.connected) {
         // 通过 Socket.IO 发送
         logger.debug(`通过 Socket 发送指令: ${clientInfo.socket.id}, 指令:`, { action, params });
         clientInfo.socket.emit('execute_command', { action, params });
         return Promise.resolve(); // 指令已发送，不等待结果
     } else {
         // 通过 HTTP API 发送
         logger.debug(`通过 HTTP API 发送指令: ${clientInfo.client_ip}:${clientInfo.client_port}, 指令:`, { action, params });

         try {
             // 构建 API URL
             const apiUrl = `http://${clientInfo.client_ip}:${clientInfo.client_port}/api/${action}`;

             // 发送 HTTP 请求
             const response = await axios.post(apiUrl, params, { timeout: 5000 });

             if (response.status === 200 && response.data.success) {
                 logger.info(`通过 HTTP API 发送指令成功: ${action}`);
                 return response.data;
             } else {
                 logger.warn(`通过 HTTP API 发送指令失败: ${action}, 状态: ${response.status}, 消息: ${response.data.message || '未知错误'}`);
                 throw new Error(`发送指令失败: ${response.data.message || '未知错误'}`);
             }
         } catch (error) {
             logger.error(`通过 HTTP API 发送指令异常: ${error.message}`);
             throw new Error(`发送指令异常: ${error.message}`);
         }
     }
}
// --- 结束新增 ---

// --- 移除旧的 executeTrade ---
/*
async function executeTrade(username, client_type, action, params) { ... }
*/

/**
 * 检查客户端是否在线
 * @param {string} clientKey 客户端键值 (username_clientType)
 * @returns {boolean} 客户端是否在线
 */
function isClientOnline(clientKey) {
    const clientInfo = tradingClients.get(clientKey);
    return !!(clientInfo && clientInfo.socket && clientInfo.socket.connected);
}

/**
 * 初始化XTP实例
 */
function initXtpInstances() {
    logger.info('初始化XTP实例');

    // 获取支持的客户端类型
    const supportedClientTypes = XtpFactory.getSupportedClientTypes();

    // 为每种客户端类型创建XTP实例
    for (const clientType of supportedClientTypes) {
        try {
            const xtpInstance = XtpFactory.createXTP(clientType, {
                tradeHandler: {
                    executeTradeViaSocket,
                    isClientOnline
                },
                logger
            });

            xtpInstances.set(clientType, xtpInstance);
            logger.info(`已创建XTP实例: ${clientType}`);
        } catch (error) {
            logger.error(`创建XTP实例失败 (${clientType}): ${error.message}`);
        }
    }
}

/**
 * 获取XTP实例
 * @param {string} clientType 客户端类型
 * @returns {BaseXTP|null} XTP实例
 */
function getXtpInstance(clientType) {
    return xtpInstances.get(clientType) || null;
}

// --- 修改: 更新 module.exports ---
module.exports = {
    init: initHttp, // 导出 HTTP 初始化函数
    initSocketIO: (namespace) => socketIO.initSocketIO(namespace, tradingClients, initXtpInstances), // 导出 Socket.IO 初始化函数
    isClientOnline, // 导出检查客户端在线状态的函数
    executeTradeViaSocket, // 导出执行交易的函数
    getXtpInstance, // 导出获取XTP实例的函数
    getRouter: () => router,
    tradingClients // 导出 tradingClients Map，使其可以在其他模块中使用
};
// --- 结束修改 ---