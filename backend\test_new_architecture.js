/**
 * 测试新的多组合架构
 */

const UserGroupManager = require('./services/UserGroupManager');
const GroupConfigGenerator = require('./services/GroupConfigGenerator');
const liveStrategyAdapter = require('./services/LiveStrategyAdapter');

async function testNewArchitecture() {
    console.log('=== 测试新的多组合架构 ===\n');

    try {
        // 1. 测试用户组合管理器
        console.log('1. 测试用户组合管理器...');
        const userId = 1;
        const groupId = 1;  // 测试组合ID
        const groupManager = new UserGroupManager(userId, groupId);
        
        // 初始化组合
        const group = await groupManager.initialize();
        console.log(`✅ 组合初始化成功: ${group.id}`);
        
        // 获取组合状态
        const status = await groupManager.getGroupStatus();
        console.log(`✅ 组合状态: ${status.status}`);
        console.log(`   - 初始资金: ${status.currentCapital}`);
        console.log(`   - 总盈亏: ${status.totalPnl}`);

        // 2. 测试配置生成器
        console.log('\n2. 测试配置生成器...');
        const configGenerator = new GroupConfigGenerator();
        
        // 生成组合配置
        const configPath = await configGenerator.generateGroupConfig(group.id);
        console.log(`✅ 配置文件生成成功: ${configPath}`);

        // 3. 测试适配器
        console.log('\n3. 测试策略适配器...');
        
        // 获取策略列表
        const strategiesResult = await liveStrategyAdapter.getLiveStrategies(userId);
        console.log(`✅ 获取策略列表: ${strategiesResult.success ? '成功' : '失败'}`);
        console.log(`   - 策略数量: ${strategiesResult.data ? strategiesResult.data.length : 0}`);

        // 4. 测试策略部署
        console.log('\n4. 测试策略部署...');
        const deployData = {
            strategyId: 'test_strategy_001',
            accountId: 'test_account',
            initialCapital: 100000,
            commissionRate: 0.0003,
            riskSettings: {
                maxOrderSize: 100,
                maxDailyTrades: 10,
                stopLossPercent: 5
            }
        };

        const deployResult = await liveStrategyAdapter.deployStrategy(userId, 'testuser', deployData);
        console.log(`✅ 策略部署: ${deployResult.success ? '成功' : '失败'}`);
        if (deployResult.success) {
            console.log(`   - 策略ID: ${deployResult.data.id}`);
        } else {
            console.log(`   - 错误: ${deployResult.error}`);
        }

        console.log('\n=== 测试完成 ===');
        console.log('✅ 新架构基本功能正常');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    testNewArchitecture().then(() => {
        console.log('\n测试结束');
        process.exit(0);
    }).catch(error => {
        console.error('测试异常:', error);
        process.exit(1);
    });
}

module.exports = { testNewArchitecture };
