[supervisord]
nodaemon=true

[program:strategy-server]
command=D:/projects/quantquart2/python_venv/Scripts/python.exe ./_Providers/_Python/strategy/strategy_server.py
directory=D:/projects/quantquart2
autostart=false
autorestart=false
stdout_logfile=D:/projects/quantquart2/strategy-server.out.log
stderr_logfile=D:/projects/quantquart2/strategy-server.err.log

[program:tdx-server]
command=D:/projects/quantquart2/python_venv/Scripts/python.exe ./_Providers/_Python/tdxserver.py
directory=D:/projects/quantquart2
autostart=false
autorestart=false
stdout_logfile=D:/projects/quantquart2/tdx-server.out.log
stderr_logfile=D:/projects/quantquart2/tdx-server.err.log

[program:python-server]
command=D:/projects/quantquart2/python_venv/Scripts/python.exe ./_Providers/_Python/app.py
directory=D:/projects/quantquart2
autostart=false
autorestart=false
stdout_logfile=D:/projects/quantquart2/python-server.out.log
stderr_logfile=D:/projects/quantquart2/python-server.err.log

[program:unzip-monitor]
command=D:/projects/quantquart2/python_venv/Scripts/python.exe ./_Providers/_Python/unzip_monitor.py ../stockdata -i 60
directory=D:/projects/quantquart2
autostart=false
autorestart=false
stdout_logfile=D:/projects/quantquart2/unzip-monitor.out.log
stderr_logfile=D:/projects/quantquart2/unzip-monitor.err.log

[program:node-app]
command=node D:/projects/quantquart2/backend/index.js
directory=D:/projects/quantquart2/backend
autostart=false
autorestart=false
environment=NODE_ENV="development"
stdout_logfile=D:/projects/quantquart2/backend/node-app.out.log
stderr_logfile=D:/projects/quantquart2/backend/node-app.err.log