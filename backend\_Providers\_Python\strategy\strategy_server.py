import os
import sys
import json
import yaml
import uuid
import shutil
import duckdb
import datetime
import traceback
import subprocess
import threading
import time
import signal
from typing import Tuple, Optional, Dict, List, Union # Import necessary types
from flask import Flask, request, jsonify
import codecs
import logging
from multiprocessing import Process
import math

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# --- Configuration ---

def find_backend_dir(start_dir, max_depth=10):
    """
    向上查找_Python目录
    
    Args:
        start_dir: 开始查找的目录
        max_depth: 最大查找深度，默认10层
    
    Returns:
        str: _Python目录的绝对路径，如果找不到返回None
    """
    current = start_dir
    for _ in range(max_depth):
        if os.path.basename(current) == 'backend':
            return current
        parent = os.path.dirname(current)
        if parent == current:  # 已经到达根目录
            break
        current = parent
    return None

def setup_strategy_import_path():
    """
    设置strategy模块的导入路径
    将_Python目录添加到sys.path，使得可以直接import strategy
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = find_backend_dir(current_dir)
    if not backend_dir:
        raise FileNotFoundError("找不到backend目录，无法设置strategy导入路径")
    
    # 将_Python目录添加到sys.path，这样就可以 import strategy
    if backend_dir not in sys.path:
        # 将backend/_Providers/_Python/strategy目录添加到sys.path
        sys.path.insert(0, os.path.join(backend_dir, '_Providers', '_Python'))
        logging.info(f"[Strategy Server] 已将backend目录添加到sys.path: {backend_dir}")
    else:
        logging.info(f"[Strategy Server] backend目录已在sys.path中: {backend_dir}")

# 设置strategy模块导入路径
setup_strategy_import_path()

# 导入策略相关模块（使用标准的strategy包导入方式）
try:
    from strategy.portfolio.portfolio_center import run_backtest
    logging.info("[Strategy Server] Successfully imported run_backtest from strategy.portfolio.portfolio_center.")
    _run_backtest_import_failed = False
except ImportError as e:
    logging.error(f"[Strategy Server] Error importing run_backtest: {e}")
    logging.error(f"[Strategy Server] Current sys.path: {sys.path}")
    _run_backtest_import_failed = True
    _import_error_msg = str(e)

# 导入实盘进程管理器
try:
    from live_process_manager import live_process_manager
    logging.info("[Strategy Server] Successfully imported live_process_manager.")
except ImportError as e:
    logging.error(f"[Strategy Server] Error importing live_process_manager: {e}")
    # 创建一个占位符，避免运行时错误
    class DummyProcessManager:
        def start_process(self, *args): return False, "Process manager not available", None
        def stop_process(self, *args): return False, "Process manager not available"
        def get_process_status(self, *args): return None
        def list_all_processes(self): return {}
        def get_running_count(self): return 0
        def shutdown(self): pass  # 添加shutdown方法
    live_process_manager = DummyProcessManager()

# 使用新的路径查找方式
current_strategy_dir = os.path.dirname(os.path.abspath(__file__))  # 当前strategy目录
PORTFOLIO_DIR = os.path.join(current_strategy_dir, 'portfolio')
PORTFOLIO_LIST_FILE = os.path.join(PORTFOLIO_DIR, 'portfolio_list.json')
DUCKDB_FILE = os.path.join(current_strategy_dir, 'backtest_results.duckdb')
DB_TABLE_NAME = 'backtest_results'
CUSTOM_STRATEGY_DB_FILE = os.path.join(current_strategy_dir, 'custom_strategy_list.duckdb')
CUSTOM_DB_TABLE_NAME = 'custom_strategies'
BACKTEST_RESULTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backtest_results')
os.makedirs(BACKTEST_RESULTS_DIR, exist_ok=True)

# --- DuckDB Initialization ---
def init_db():
    """Initializes the DuckDB database and creates the table if it doesn't exist."""
    try:
        # 检查是否在退出过程中
        if hasattr(init_db, '_exiting'):
            return
            
        con = duckdb.connect(DUCKDB_FILE)
        con.execute(f"""
            CREATE TABLE IF NOT EXISTS {DB_TABLE_NAME} (
                strategy_id VARCHAR NOT NULL,
                run_timestamp TIMESTAMP NOT NULL,
                result_data JSON NOT NULL,      -- Store the result dictionary as JSON
                config_yaml TEXT,             -- Store the config YAML (especially for custom)
                PRIMARY KEY (strategy_id, run_timestamp)
            );
        """)
        con.close()
        logging.info(f"[Strategy Server] DuckDB database initialized at {DUCKDB_FILE}")
    except Exception as e:
        logging.error(f"[Strategy Server] Error initializing DuckDB: {e}")
        # 在退出时不要打印完整的traceback
        if not hasattr(init_db, '_exiting'):
            traceback.print_exc()

# 新增：初始化自定义策略数据库
def init_custom_strategy_db():
    """Initializes the DuckDB database for custom strategies and creates the table if it doesn't exist."""
    try:
        # 检查是否在退出过程中
        if hasattr(init_custom_strategy_db, '_exiting'):
            return
            
        con = duckdb.connect(CUSTOM_STRATEGY_DB_FILE)
        con.execute(f"""
            CREATE TABLE IF NOT EXISTS {CUSTOM_DB_TABLE_NAME} (
                strategy_id VARCHAR PRIMARY KEY, -- 策略名称 (UUID), 全局唯一
                content_yaml TEXT NOT NULL,       -- 策略配置 YAML 内容
                creation_time TIMESTAMP NOT NULL,   -- 创建时间
                creator_user_id VARCHAR,          -- 创建用户 ID
                strategy_type VARCHAR NOT NULL      -- 策略类型 (e.g., 'portfolio')
            );
        """)
        con.close()
        logging.info(f"[Strategy Server] Custom strategy DuckDB database initialized at {CUSTOM_STRATEGY_DB_FILE}")
    except Exception as e:
        logging.error(f"[Strategy Server] Error initializing custom strategy DuckDB: {e}")
        # 在退出时不要打印完整的traceback
        if not hasattr(init_custom_strategy_db, '_exiting'):
            traceback.print_exc()

# --- Flask App Initialization ---
app = Flask(__name__)

# --- Helper Functions ---

def load_system_strategies():
    """Loads the list of registered system strategies from portfolio_list.json."""
    try:
        with open(PORTFOLIO_LIST_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logging.error(f"[Strategy Server] Error: System strategy list file not found at {PORTFOLIO_LIST_FILE}")
        return None
    except json.JSONDecodeError:
        logging.error(f"[Strategy Server] Error: Failed to parse system strategy list file {PORTFOLIO_LIST_FILE}")
        return None

# get_custom_strategies 函数，使用传进来的策略名称列表，到 custom_stratey_list.duckdb 中获取所有列表中的策略及yaml内容
def get_custom_strategies(strategy_ids: List[str]) -> List[Dict[str, str]]:
    """
    Retrieves custom strategies from the custom_strategy_list.duckdb database.
    Args:
        strategy_ids (List[str]): List of strategy IDs to retrieve.
    Returns:
        List[Dict[str, str]]: List of dictionaries containing strategy id, name and YAML content.
    """
    # --- Linter 修复：检查 strategy_ids 是否为空 --- 
    if not strategy_ids: # 如果列表为空，直接返回空列表，避免空的 IN 子句
        return []
    # --- 修复结束 ---
    try:
        con = duckdb.connect(CUSTOM_STRATEGY_DB_FILE, read_only=True)
        # --- 修改：使用参数化查询避免 SQL 注入 ---
        placeholders = ', '.join('?' * len(strategy_ids)) # 生成 ?, ?, ?
        query = f"SELECT strategy_id, content_yaml FROM {CUSTOM_DB_TABLE_NAME} WHERE strategy_id IN ({placeholders})"
        results = con.execute(query, strategy_ids).fetchall() # 修复：将 fetchall() 应用于 execute() 的返回值
        # --- 修改结束 ---
        con.close()

        # 修改：将 'name' 改为 'id'，并添加 'name' 字段（从YAML中提取）
        strategies = []
        for strategy_id, yaml_content in results:
            strategy_info = {"id": strategy_id, "yaml": yaml_content}
            
            # 尝试从YAML中提取策略名称
            try:
                config = yaml.safe_load(yaml_content)
                if isinstance(config, dict) and 'strategy_name' in config:
                    strategy_info['name'] = config['strategy_name']
                else:
                    strategy_info['name'] = strategy_id  # 如果无法提取名称，使用ID作为名称
            except:
                strategy_info['name'] = strategy_id  # 解析失败时使用ID作为名称
                
            strategies.append(strategy_info)
            
        return strategies
    except Exception as e:
        logging.error(f"[Strategy Server] Error retrieving custom strategies: {e}")
        traceback.print_exc()
        return []

def validate_strategy_config(yaml_content: str) -> Tuple[bool, Optional[str]]: # Use typing.Tuple and Optional
    """
    Validates if the provided YAML string contains the necessary keys for a strategy config.
    Returns (isValid, strategy_name_from_yaml or None).
    """
    try:
        config = yaml.safe_load(yaml_content)
        if not isinstance(config, dict):
            logging.error("[Strategy Server] Validation Error: Config is not a dictionary.")
            return False, None

        # Required top-level keys
        required_keys = [
            'strategy_id', 'strategy_name', 'trading_type', 'universe', 'order_by', 'rebalance_interval', 'top_n', 'data_freq', 'bar_count',
            'backtest', 'data'
            # 'rebalance_interval', 'top_n', 'weighting_scheme' are also in example
        ]
        missing_keys = [key for key in required_keys if key not in config]
        if missing_keys:
            logging.error(f"[Strategy Server] Validation Error: Missing required keys: {missing_keys}")
            return False, None

        # Basic type validation
        if not isinstance(config.get('strategy_id'), str) or not config['strategy_id']:
             logging.error("[Strategy Server] Validation Error: 'strategy_id' must be a non-empty string.")
             return False, None
        strategy_id_from_yaml = config['strategy_id']

        if not isinstance(config.get('universe'), list) or not config['universe']:
             logging.error("[Strategy Server] Validation Error: 'universe' must be a non-empty list.")
             return False, strategy_id_from_yaml
        if not isinstance(config.get('backtest'), dict):
             logging.error("[Strategy Server] Validation Error: 'backtest' section missing or not a dictionary.")
             return False, strategy_id_from_yaml
        if not isinstance(config.get('data'), dict):
             logging.error("[Strategy Server] Validation Error: 'data' section missing or not a dictionary.")
             return False, strategy_id_from_yaml

        # Required sub-keys
        if 'stime' not in config['backtest'] or 'initial_capital' not in config['backtest']:
             logging.error("[Strategy Server] Validation Error: 'backtest' missing 'stime' or 'initial_capital'.")
             return False, strategy_id_from_yaml
        if 'path' not in config['data'] or 'mode' not in config['data']:
             logging.error("[Strategy Server] Validation Error: 'data' missing 'path' or 'mode'.")
             return False, strategy_id_from_yaml

        logging.info(f"[Strategy Server] Custom strategy YAML validation successful for '{strategy_id_from_yaml}'.")
        return True, strategy_id_from_yaml
    except yaml.YAMLError as e:
        logging.error(f"[Strategy Server] Validation Error: Invalid YAML format: {e}")
        return False, None
    except Exception as e:
        logging.error(f"[Strategy Server] Validation Error: Unexpected error during validation: {e}")
        return False, None

def save_backtest_result(strategy_id: str, result_data: dict, config_yaml: Optional[str] = None):
    """Saves the backtest result dictionary to DuckDB."""
    
    # 检查结果是否包含异常字符
    if not check_result_validity(result_data):
        logging.warning(f"[Strategy Server] Skipping save for '{strategy_id}' - result contains NaN/Infinity")
        return
    
    max_retries = 2
    for attempt in range(max_retries):
        try:
            con = duckdb.connect(DUCKDB_FILE)
            current_timestamp = datetime.datetime.now()
            result_json = json.dumps(result_data)

            con.execute(
                f"INSERT INTO {DB_TABLE_NAME} (strategy_id, run_timestamp, result_data, config_yaml) VALUES (?, ?, ?, ?)",
                (strategy_id, current_timestamp, result_json, config_yaml)
            )
            con.close()
            logging.info(f"[Strategy Server] Saved backtest result for '{strategy_id}' to DuckDB.")
            return
        except Exception as e:
            logging.error(f"[Strategy Server] Error saving result (attempt {attempt+1}/{max_retries}): {e}")
            if attempt == 0:
                logging.info("[Strategy Server] Attempting database initialization...")
                init_db()
            else:
                logging.error("[Strategy Server] Database initialization failed after retry")
                traceback.print_exc()
                raise
        
def check_result_validity(result_dict: dict) -> bool:
    """
    检查回测结果是否包含NaN或其他异常字符
    
    Args:
        result_dict: 回测结果字典
        
    Returns:
        bool: True表示结果有效，False表示包含异常字符
    """
    def check_value(value):
        """递归检查单个值"""
        if isinstance(value, dict):
            return all(check_value(v) for v in value.values())
        elif isinstance(value, list):
            return all(check_value(v) for v in value)
        elif isinstance(value, (int, float)):
            # 检查数值是否为NaN或Infinity
            if math.isnan(value) or math.isinf(value):
                return False
        elif isinstance(value, str):
            # 检查字符串是否包含NaN或Infinity
            if 'NaN' in value or 'nan' in value or 'Infinity' in value or 'infinity' in value:
                return False
        return True
    
    try:
        return check_value(result_dict)
    except Exception as e:
        logging.error(f"[Strategy Server] Error checking result validity: {e}")
        return False

def get_backtest_result_from_db(strategy_id: str) -> Optional[dict]: # Use typing.Optional
    """Retrieves the latest backtest result for a strategy from DuckDB."""
    try:
        con = duckdb.connect(DUCKDB_FILE, read_only=True)
        # Fetch the most recent entry for the given strategy name
        result = con.execute(
            f"SELECT result_data FROM {DB_TABLE_NAME} WHERE strategy_id = ? ORDER BY run_timestamp DESC LIMIT 1",
            (strategy_id,)
        ).fetchone()
        con.close()

        if result and result[0]:
            # Parse the JSON string back into a dictionary
            result_dict = json.loads(result[0])
            
            # 检查结果是否包含异常字符
            if not check_result_validity(result_dict):
                logging.warning(f"[Strategy Server] Found invalid result (contains NaN/Infinity) for '{strategy_id}', returning None.")
                return None
                
            logging.info(f"[Strategy Server] Found latest backtest result for '{strategy_id}' in DuckDB.")
            return result_dict
        else:
            logging.info(f"[Strategy Server] No backtest result found for '{strategy_id}' in DuckDB.")
            return None
    except Exception as e:
        logging.error(f"[Strategy Server] Error retrieving backtest result for '{strategy_id}' from DuckDB: {e}")
        traceback.print_exc()
        return None

def delete_backtest_results(strategy_id: str):
    """Deletes all existing backtest results for a specific strategy from DuckDB."""
    deleted_count = 0 # 用于记录删除的行数
    try:
        con = duckdb.connect(DUCKDB_FILE)
        # 先查询有多少条记录将被删除
        count_result = con.execute(f"SELECT COUNT(*) FROM {DB_TABLE_NAME} WHERE strategy_id = ?", (strategy_id,)).fetchone()
        if count_result:
            deleted_count = count_result[0]
            logging.info(f"[Strategy Server] Found {deleted_count} previous result(s) for '{strategy_id}' to delete.")
        
        # 执行删除
        con.execute(f"DELETE FROM {DB_TABLE_NAME} WHERE strategy_id = ?", (strategy_id,))
        con.commit() # 确保更改被提交
        con.close()
        logging.info(f"[Strategy Server] Successfully deleted {deleted_count} previous result(s) for '{strategy_id}' from DuckDB.") # 修改成功日志
        return True
    except Exception as e:
        logging.error(f"[Strategy Server] Error deleting backtest results for '{strategy_id}' from DuckDB: {e}")
        traceback.print_exc()
        return False

# --- 新增：从自定义策略数据库获取 YAML ---
def get_custom_strategy_yaml_from_db(strategy_id: str) -> Optional[str]:
    """Retrieves the YAML content for a custom strategy from custom_strategy_list.duckdb."""
    try:
        con = duckdb.connect(CUSTOM_STRATEGY_DB_FILE, read_only=True)
        result = con.execute(
            f"SELECT content_yaml FROM {CUSTOM_DB_TABLE_NAME} WHERE strategy_id = ?",
            (strategy_id,)
        ).fetchone()
        con.close()

        if result and result[0]:
            logging.info(f"[Strategy Server] Found custom strategy YAML for '{strategy_id}' in custom DB.")
            return result[0]
        else:
            logging.info(f"[Strategy Server] No custom strategy YAML found for '{strategy_id}' in custom DB.")
            return None
    except Exception as e:
        logging.error(f"[Strategy Server] Error retrieving custom strategy YAML for '{strategy_id}' from custom DB: {e}")
        traceback.print_exc()
        return None

# --- API Endpoints ---

@app.route('/strategy/list', methods=['GET'])
def get_strategy_list():
    """Endpoint to retrieve the list of system strategies along with their latest backtest results."""
    logging.info("[Strategy Server] Received request for /api/strategy/list (including backtest results)")
    system_strategies = load_system_strategies()
    
    if system_strategies is None:
        # 返回统一的错误格式
        return jsonify({"success": False, "error": "Could not load system strategy list."}), 500
    
    # 获取自定义策略，策略名称在调用者处以 const response = await apiClient.get(systemApiUrl, { params: { customStrategyNames } }); 方式传递进来
    raw_custom_strategy_ids = request.args.getlist('customStrategyIds', type=str)
    
    # 处理可能的逗号分隔字符串：将 ['id1,id2', 'id3'] 转换为 ['id1', 'id2', 'id3']
    custom_strategy_ids = []
    for item in raw_custom_strategy_ids:
        custom_strategy_ids.extend([id.strip() for id in item.split(',') if id.strip()])
    
    logging.info(f"[Strategy Server] Parsed custom strategy IDs: {custom_strategy_ids}") # 增加解析后的日志
    custom_strategies = get_custom_strategies(custom_strategy_ids)
    
    logging.info(f"[Strategy Server] Found {len(custom_strategies)} custom strategies.")

    # 合并系统策略和自定义策略
    strategies = system_strategies + custom_strategies
    
    logging.info(f"[Strategy Server] Found {len(strategies)} strategies in total.")

    # 为每个策略添加最新的回测数据
    strategies_with_results = []
    for strategy_info in strategies:
        strategy_id = strategy_info.get('id')  # 修改：从 'name' 改为 'id'
        if not strategy_id:
            logging.warning(f"[Strategy Server] Warning: Strategy entry missing 'id' in portfolio_list.json: {strategy_info}")
            continue # 跳过没有ID的策略条目

        logging.info(f"[Strategy Server] Getting latest result for strategy: {strategy_id}")
        latest_result = get_backtest_result_from_db(strategy_id)

        # 创建新的字典包含策略信息和回测结果
        strategy_entry = strategy_info.copy() # 复制基础信息

        if latest_result:
            # 清理回测结果，只保留缩略图数据 (和 get_saved_backtest_result 端点保持一致)
            if 'equity_curve_data' in latest_result:
                del latest_result['equity_curve_data']
            # 移除其他大数据字段，只保留核心指标
            if 'trade_history' in latest_result:
                del latest_result['trade_history']
            if 'equity_curve_thumbnail_data' in latest_result:
                # 只保留前100个点，减少数据量
                if len(latest_result['equity_curve_thumbnail_data']) > 100:
                    latest_result['equity_curve_thumbnail_data'] = latest_result['equity_curve_thumbnail_data'][:100]
            # 确保缩略图字段存在
            if 'equity_curve_thumbnail_data' not in latest_result:
                 latest_result['equity_curve_thumbnail_data'] = [] # 如果没有，置为空列表
                 logging.warning(f"[Strategy Server] Warning: equity_curve_thumbnail_data missing for {strategy_id} in list retrieval.")
            strategy_entry['backtest_result'] = latest_result
            logging.info(f"[Strategy Server] Found result for {strategy_id}.")
        else:
            strategy_entry['backtest_result'] = None # 没有找到回测数据则置为 None
            logging.info(f"[Strategy Server] No result found for {strategy_id}.")

        strategies_with_results.append(strategy_entry)

    # 返回包含回测数据的策略列表
    return jsonify({"success": True, "data": strategies_with_results})

@app.route('/strategy/backtest_result', methods=['GET'])
def get_saved_backtest_result():
    """Endpoint to retrieve the latest saved backtest result for a strategy."""
    logging.info("[Strategy Server] Received request for /api/strategy/backtest_result")
    strategy_id = request.args.get('id')  # 修改：从 'name' 改为 'id'
    if not strategy_id:
         # 返回统一的错误格式
         return jsonify({"success": False, "error": "Missing 'id' query parameter."}), 400  # 修改错误消息

    logging.info(f"[Strategy Server] Querying database for results of strategy: {strategy_id}")
    result = get_backtest_result_from_db(strategy_id)

    if result is None:
        # 返回统一的错误格式
        return jsonify({"success": False, "error": f"No saved backtest result found for strategy '{strategy_id}'"}), 404
    else:
        # --- 修改：从返回结果中移除完整权益曲线数据，只保留缩略版 --- 
        if 'equity_curve_data' in result:
            del result['equity_curve_data']
            logging.info(f"[Strategy Server] Removed full equity_curve_data for strategy '{strategy_id}' before sending.")
        # 确保缩略图数据存在 (虽然 analyzer 应该总是添加它)
        if 'equity_curve_thumbnail_data' not in result:
            logging.warning(f"[Strategy Server] Warning: equity_curve_thumbnail_data missing for strategy '{strategy_id}'. Client might expect it.")
            # 可以选择添加一个空列表或其他占位符
            # result['equity_curve_thumbnail_data'] = [] 
        # --- 修改结束 ---

        # 返回统一的成功格式 (已移除 equity_curve_data)
        return jsonify({"success": True, "data": result})

@app.route('/strategy/details/<strategy_id>', methods=['GET'])
def get_strategy_details(strategy_id: str):
    """Endpoint to retrieve the detailed backtest results for a strategy.
    Includes all metrics, full equity curve, trade history, and the raw strategy config YAML.
    """
    logging.info(f"[Strategy Server] Received request for /strategy/details/{strategy_id}")
    
    logging.info(f"[Strategy Server] Querying latest result for '{strategy_id}'.")
    
    # 1. 从数据库获取最新的回测结果 JSON
    latest_result_dict = get_backtest_result_from_db(strategy_id)

    if latest_result_dict is None:
        # 如果数据库没有结果，也尝试只返回配置？或者就返回404？当前返回404
        logging.info(f"[Strategy Server] No backtest result found for '{strategy_id}'.")
        return jsonify({"success": False, "error": f"No backtest result found for strategy '{strategy_id}'"}), 404

    # 2. 尝试读取策略的原始 YAML 配置字符串
    yaml_content = None
    try:
        # 优先从文件读取 (适用于系统策略)
        strategy_dir_path = os.path.join(PORTFOLIO_DIR, strategy_id)
        yaml_path = os.path.join(strategy_dir_path, 'index.yaml')
        
        if os.path.exists(yaml_path):
            with open(yaml_path, 'r', encoding='utf-8') as f_yaml:
                yaml_content = f_yaml.read() 
                logging.info(f"[Strategy Server] Read strategy config YAML from {yaml_path}")
        else:
            # 如果文件不存在，尝试从自定义策略数据库获取 (适用于自定义策略)
            # 并且也尝试从回测结果数据库获取（旧的自定义策略可能只存在于此）
            yaml_content = get_custom_strategy_yaml_from_db(strategy_id) # 检查自定义策略库
            if yaml_content:
                 logging.info(f"[Strategy Server] Read strategy config YAML from custom DB for '{strategy_id}'")
            
            else:
                """
                 # 最后尝试从回测结果数据库获取 config_yaml 字段
            con = duckdb.connect(DUCKDB_FILE, read_only=True)
            config_result = con.execute(
                f"SELECT config_yaml FROM {DB_TABLE_NAME} WHERE strategy_id = ? ORDER BY run_timestamp DESC LIMIT 1",
                (strategy_id,)
            ).fetchone()
            con.close()
            if config_result and config_result[0]:
                yaml_content = config_result[0]
                     logging.info(f"[Strategy Server] Read strategy config YAML from backtest result DB for '{strategy_id}'")
            else:
                      logging.warning(f"[Strategy Server] Warning: Could not find strategy config YAML in file, custom DB, or backtest DB for '{strategy_id}'.")
                """
                logging.warning(f"[Strategy Server] Warning: Could not find strategy config YAML in file, custom DB, or backtest DB for '{strategy_id}'.")

    except Exception as e:
        logging.error(f"[Strategy Server] Warning: Error reading strategy config YAML: {e}")
        traceback.print_exc()

    # 3. 将原始 YAML 字符串添加到结果字典中 (如果找到的话)
    if yaml_content:
        latest_result_dict['strategy_config_yaml'] = yaml_content 
    else:
        latest_result_dict['strategy_config_yaml'] = None # 明确设置为 None

    # 4. 直接返回包含回测结果和原始 YAML 的字典
    logging.info(f"[Strategy Server] Returning full details for '{strategy_id}' including raw config YAML.")
    return jsonify({"success": True, "data": latest_result_dict})

# --- 全局任务状态存储 ---
TASK_STATUS = {}  # {task_id: {'status': 'pending|running|success|failed', 'result': None or 回测结果, 'error': None or 错误信息}}

def run_backtest_async(task_id, strategy_to_run, yaml_to_run, config_to_save):
    try:
        # 确保os模块在函数开始时就被导入
        import os
        import sys
        
        # 在子进程中重新导入run_backtest函数
        try:
            from strategy.portfolio.portfolio_center import run_backtest
            logging.info("[Strategy Server] Successfully imported run_backtest in subprocess.")
        except ImportError as e:
            logging.error(f"[Strategy Server] Error importing run_backtest in subprocess: {e}")
            # 尝试备用导入方式
            script_dir = os.path.dirname(os.path.abspath(__file__))
            portfolio_path = os.path.join(script_dir, 'portfolio')
            if portfolio_path not in sys.path:
                sys.path.insert(0, portfolio_path)
            try:
                from portfolio_center import run_backtest
                logging.info("[Strategy Server] Successfully imported run_backtest using fallback method.")
            except ImportError as e2:
                logging.error(f"[Strategy Server] Fallback import also failed: {e2}")
                raise ImportError(f"无法导入run_backtest函数: {e2}")
        
        script_dir = os.path.dirname(os.path.abspath(__file__))
        portfolio_path = os.path.join(script_dir, 'portfolio')
        original_cwd = os.getcwd()
        os.chdir(portfolio_path)
        backtest_result = run_backtest(strategy_id=strategy_to_run, strategy_yaml=yaml_to_run)
        os.chdir(original_cwd)
        result_file = os.path.join(BACKTEST_RESULTS_DIR, f"{task_id}.json")
        if isinstance(backtest_result, dict) and "error" in backtest_result:
            with open(result_file, "w", encoding="utf-8") as f:
                json.dump({"status": "failed", "error": backtest_result["error"]}, f, ensure_ascii=False)
        elif not isinstance(backtest_result, dict):
            with open(result_file, "w", encoding="utf-8") as f:
                json.dump({"status": "failed", "error": "Backtest completed but returned unexpected data format."}, f, ensure_ascii=False)
        else:
            # 检查结果是否包含异常字符
            if not check_result_validity(backtest_result):
                logging.warning(f"[Strategy Server] Backtest result for '{strategy_to_run}' contains NaN/Infinity, skipping database save.")
                with open(result_file, "w", encoding="utf-8") as f:
                    json.dump({"status": "failed", "error": "Backtest result contains invalid values (NaN/Infinity)"}, f, ensure_ascii=False)
            else:
                # 保存完整回测结果到DuckDB
                save_backtest_result(strategy_to_run, backtest_result, config_to_save)
                # 写result_file时只保留缩略图
                cleaned_result = backtest_result.copy()
                if 'equity_curve_data' in cleaned_result:
                    del cleaned_result['equity_curve_data']
                if 'equity_curve_thumbnail_data' not in cleaned_result:
                    cleaned_result['equity_curve_thumbnail_data'] = []
                with open(result_file, "w", encoding="utf-8") as f:
                    json.dump({"status": "success", "result": cleaned_result}, f, ensure_ascii=False)
    except Exception as e:
        # 确保在异常处理中也能访问os模块
        import os
        result_file = os.path.join(BACKTEST_RESULTS_DIR, f"{task_id}.json")
        with open(result_file, "w", encoding="utf-8") as f:
            json.dump({"status": "failed", "error": str(e)}, f, ensure_ascii=False)

@app.route('/strategy/run_backtest', methods=['POST'])
def run_strategy_backtest_endpoint():
    logging.info("[Strategy Server] [异步回测] Received request for /api/strategy/run_backtest")
    if not request.is_json:
        return jsonify({"success": False, "error": "Request must be JSON"}), 400
    data = request.get_json()
    strategy_id_input = data.get('id')
    strategy_yaml_input = data.get('strategy_yaml', '')
    if not strategy_id_input:
        return jsonify({"success": False, "error": "Missing 'id' field in request JSON"}), 400
    # --- 策略查找逻辑与原来一致 ---
    strategy_to_run = ""
    yaml_to_run = ""
    is_system = False
    config_to_save = None
    system_strategies = load_system_strategies()
    is_known_system_strategy = system_strategies is not None and any(s['id'] == strategy_id_input for s in system_strategies)
    if is_known_system_strategy and not strategy_yaml_input:
        strategy_path = os.path.join(PORTFOLIO_DIR, strategy_id_input)
        yaml_path = os.path.join(strategy_path, 'index.yaml')
        if not os.path.isdir(strategy_path) or not os.path.exists(yaml_path):
            return jsonify({"success": False, "error": f"System strategy '{strategy_id_input}' directory or index.yaml not found."}), 404
        strategy_to_run = strategy_id_input
        is_system = True
        try:
            with open(yaml_path, 'r', encoding='utf-8') as f_yaml:
                config_to_save = f_yaml.read()
                yaml_to_run = config_to_save
        except Exception as e:
            pass
    elif not is_known_system_strategy:
        custom_yaml = get_custom_strategy_yaml_from_db(strategy_id_input)
        if custom_yaml:
            is_valid, name_from_yaml = validate_strategy_config(custom_yaml)
            if is_valid:
                strategy_to_run = strategy_id_input
                yaml_to_run = custom_yaml
                config_to_save = custom_yaml
                is_system = False
            else:
                return jsonify({"success": False, "error": f"Custom strategy '{strategy_id_input}' found but its configuration is invalid."}), 400
        else:
            return jsonify({"success": False, "error": f"Strategy '{strategy_id_input}' is not a registered system strategy and not found in custom strategies."}), 404
    else:
        return jsonify({"success": False, "error": f"Could not process strategy request for '{strategy_id_input}'."}), 500
    # --- 异步任务提交 ---
    task_id = str(uuid.uuid4())
    TASK_STATUS[task_id] = {'status': 'pending', 'result': None, 'error': None}
    Process(target=run_backtest_async, args=(task_id, strategy_to_run, yaml_to_run, config_to_save)).start()
    return jsonify({"success": True, "task_id": task_id})

@app.route('/strategy/backtest_status', methods=['GET'])
def get_backtest_status():
    task_id = request.args.get('task_id')
    if not task_id:
        return jsonify({"success": False, "error": "Missing 'task_id' query parameter."}), 400
    result_file = os.path.join(BACKTEST_RESULTS_DIR, f"{task_id}.json")
    if os.path.exists(result_file):
        with open(result_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        return jsonify({"success": True, **data})
    else:
        return jsonify({"success": True, "status": "pending"})

@app.route('/strategy/copy', methods=['POST'])
def copy_strategy_endpoint():
    """
    Endpoint to copy an existing system or custom strategy.
    Expects JSON payload: {"source_strategy_id": "<id>", "user_id": "<id>"}
    Creates a new custom strategy with a unique UUID name and returns the new name.
    """
    logging.info("[Strategy Server] Received request for /strategy/copy")
    if not request.is_json:
        logging.error("[Strategy Server] Error: Request is not JSON")
        return jsonify({"success": False, "error": "Request must be JSON"}), 400

    data = request.get_json()
    source_strategy_id = data.get('source_strategy_id')
    user_id = data.get('user_id')
    new_name = data.get('new_name') 

    if not source_strategy_id:
        return jsonify({"success": False, "error": "Missing 'source_strategy_id' field in request JSON"}), 400
    if not user_id: # 简单校验 user_id 是否存在
        return jsonify({"success": False, "error": "Missing 'user_id' field in request JSON"}), 400

    logging.info(f"[Strategy Server] Attempting to copy strategy: {source_strategy_id} for user: {user_id}")

    source_yaml_content = None
    source_type = None # 'system' or 'custom'

    # 1. 检查是否为系统策略
    system_strategies = load_system_strategies() # 获得系统策略的索引，有 id name 两个字段
    if system_strategies:
        for strategy_info in system_strategies:
            if strategy_info.get('id') == source_strategy_id:
                logging.info(f"[Strategy Server] Source '{source_strategy_id}' is a system strategy.")
                strategy_path = os.path.join(PORTFOLIO_DIR, source_strategy_id)
                yaml_path = os.path.join(strategy_path, 'index.yaml')
                if os.path.exists(yaml_path):
                    try:
                        with open(yaml_path, 'r', encoding='utf-8') as f_yaml:
                            source_yaml_content = f_yaml.read()
                        source_type = 'system'
                        logging.info(f"[Strategy Server] Successfully read YAML for system strategy '{source_strategy_id}'.")
                    except Exception as e:
                        logging.error(f"[Strategy Server] Error reading YAML for system strategy '{source_strategy_id}': {e}")
                        return jsonify({"success": False, "error": f"Could not read configuration file for system strategy '{source_strategy_id}'."}), 500
                else:
                    logging.error(f"[Strategy Server] Error: index.yaml not found for system strategy '{source_strategy_id}'.")
                    return jsonify({"success": False, "error": f"Configuration file (index.yaml) not found for system strategy '{source_strategy_id}'."}), 404
                break # 找到系统策略后退出循环

    # 2. 如果不是系统策略，检查自定义策略数据库
    if source_type is None:
        logging.info(f"[Strategy Server] Source '{source_strategy_id}' is not a system strategy. Checking custom strategy DB...")
        source_yaml_content = get_custom_strategy_yaml_from_db(source_strategy_id)
        if source_yaml_content:
            source_type = 'custom'
            logging.info(f"[Strategy Server] Found source '{source_strategy_id}' in custom strategy DB.")
        else:
            logging.error(f"[Strategy Server] Source strategy '{source_strategy_id}' not found in system list or custom DB.")
            return jsonify({"success": False, "error": f"Source strategy '{source_strategy_id}' not found."}), 404

    # 3. 生成新策略名称 (UUID) 并修改 YAML 内容
    new_strategy_uuid = str(uuid.uuid4())
    logging.info(f"[Strategy Server] Generated new strategy name (UUID): {new_strategy_uuid}")

    # --- Linter 修复：确保 source_yaml_content 不是 None ---
    if source_yaml_content is None:
         logging.error(f"[Strategy Server] Error: Source YAML content is unexpectedly None before processing.")
         # 此处应该不会发生，因为之前的逻辑会处理未找到策略的情况
         return jsonify({"success": False, "error": "Internal server error: Failed to retrieve strategy content."}), 500
    # --- 修复结束 ---

    try:
        # 现在可以安全地调用 safe_load
        parsed_yaml = yaml.safe_load(source_yaml_content)
        if not isinstance(parsed_yaml, dict):
            logging.error("[Strategy Server] Error: Source YAML content is not a valid dictionary.")
            return jsonify({"success": False, "error": "Invalid source strategy configuration format."}), 500

        # 更新 YAML 内部的 strategy_id
        original_internal_name = parsed_yaml.get('strategy_id')
        parsed_yaml['strategy_id'] = new_strategy_uuid
        parsed_yaml['strategy_name'] = new_name
        new_yaml_content = yaml.dump(parsed_yaml, allow_unicode=True, sort_keys=False) # 转回 YAML 字符串
        logging.info(f"[Strategy Server] Updated internal strategy_id from '{original_internal_name}' to '{new_strategy_uuid}'.")

    except yaml.YAMLError as e:
        logging.error(f"[Strategy Server] Error parsing or dumping YAML for source strategy '{source_strategy_id}': {e}")
        return jsonify({"success": False, "error": f"Error processing configuration of source strategy '{source_strategy_id}'."}), 500
    except Exception as e:
         logging.error(f"[Strategy Server] Unexpected error processing YAML: {e}")
         traceback.print_exc()
         return jsonify({"success": False, "error": "An unexpected error occurred while processing the strategy configuration."}), 500


    # 4. 将新策略写入自定义策略数据库
    try:
        con = duckdb.connect(CUSTOM_STRATEGY_DB_FILE)
        current_time = datetime.datetime.now()
        con.execute(
            f"""
            INSERT INTO {CUSTOM_DB_TABLE_NAME} (strategy_id, content_yaml, creation_time, creator_user_id, strategy_type)
            VALUES (?, ?, ?, ?, ?)
            """,
            (new_strategy_uuid, new_yaml_content, current_time, user_id, 'portfolio') # 假设类型总是 portfolio
        )
        con.commit()
        con.close()
        logging.info(f"[Strategy Server] Successfully saved new custom strategy '{new_strategy_uuid}' to DB for user '{user_id}'.")

        # 5. 返回成功和新的策略名称
        return jsonify({"success": True, "new_strategy_id": new_strategy_uuid}), 201 # 201 Created

    except duckdb.ConstraintException as e:
         #理论上 UUID 不会重复，但以防万一
         logging.error(f"[Strategy Server] Error saving new strategy: Constraint violation (likely duplicate name '{new_strategy_uuid}'): {e}")
         traceback.print_exc()
         return jsonify({"success": False, "error": "Failed to save copied strategy due to a naming conflict."}), 409 # 409 Conflict
    except Exception as e:
        logging.error(f"[Strategy Server] Error saving new custom strategy '{new_strategy_uuid}' to DB: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": "Failed to save the copied strategy to the database."}), 500

# --- 新增：修改策略 Endpoint ---
@app.route('/strategy/update', methods=['PUT'])
def update_strategy_endpoint():
    """
    Endpoint to update an existing custom strategy.
    Expects JSON payload: {"strategy_id": "<uuid_name>", "strategy_yaml": "<yaml_content>"}
    System strategies cannot be updated.
    Updates the YAML in the custom strategy DB and deletes existing backtest results.
    """
    logging.info("[Strategy Server] Received request for /strategy/update")
    if not request.is_json:
        logging.error("[Strategy Server] Error: Request is not JSON")
        return jsonify({"success": False, "error": "Request must be JSON"}), 400

    data = request.get_json()
    strategy_name_to_update = data.get('strategy_id')
    new_yaml_content = data.get('strategy_yaml')

    if not strategy_name_to_update:
        return jsonify({"success": False, "error": "Missing 'strategy_id' field in request JSON"}), 400
    if not new_yaml_content:
        return jsonify({"success": False, "error": "Missing 'strategy_yaml' field in request JSON"}), 400

    logging.info(f"[Strategy Server] Attempting to update strategy: {strategy_name_to_update}")

    # 1. 检查是否为系统策略
    system_strategies = load_system_strategies()
    if system_strategies and any(s.get('name') == strategy_name_to_update for s in system_strategies):
        logging.error(f"[Strategy Server] Error: Attempted to update system strategy '{strategy_name_to_update}'.")
        return jsonify({"success": False, "error": f"System strategy '{strategy_name_to_update}' cannot be modified."}), 403 # 403 Forbidden

    # 2. 检查是否存在于自定义策略数据库
    existing_yaml = get_custom_strategy_yaml_from_db(strategy_name_to_update)
    if existing_yaml is None:
        logging.error(f"[Strategy Server] Error: Custom strategy '{strategy_name_to_update}' not found for update.")
        return jsonify({"success": False, "error": f"Custom strategy '{strategy_name_to_update}' not found."}), 404

    # 3. 校验新的 YAML 内容
    logging.info(f"[Strategy Server] Validating new YAML for strategy '{strategy_name_to_update}'.")
    is_valid, name_from_yaml = validate_strategy_config(new_yaml_content)

    if not is_valid:
        logging.error(f"[Strategy Server] Error: New YAML validation failed for '{strategy_name_to_update}'.")
        # validate_strategy_config 内部会打印具体错误
        return jsonify({"success": False, "error": "Provided strategy configuration YAML is invalid."}), 400

    # 4. 检查 YAML 内部名称是否与目标策略名称匹配
    if name_from_yaml != strategy_name_to_update:
        logging.error(f"[Strategy Server] Error: YAML internal name '{name_from_yaml}' does not match target strategy name '{strategy_name_to_update}'.")
        return jsonify({
            "success": False,
            "error": f"Strategy name inside YAML ('{name_from_yaml}') must match the name of the strategy being updated ('{strategy_name_to_update}')."
        }), 400

    # 5. 更新自定义策略数据库
    try:
        con = duckdb.connect(CUSTOM_STRATEGY_DB_FILE)
        con.execute(
            f"UPDATE {CUSTOM_DB_TABLE_NAME} SET content_yaml = ? WHERE strategy_id = ?",
            (new_yaml_content, strategy_name_to_update)
        )
        con.commit()
        con.close()
        # 移除 rowcount 检查，因为我们已经确认策略存在
        logging.info(f"[Strategy Server] Successfully updated YAML for custom strategy '{strategy_name_to_update}' in DB.")
        # rows_updated = con.close_and_rowcount() # DuckDB extension to get affected rows

        # if rows_updated > 0:
        #     print(f"[Strategy Server] Successfully updated YAML for custom strategy '{strategy_name_to_update}' in DB.")
        # else:
        #      # 这理论上不应该发生，因为前面已经检查过存在性
        #      print(f"[Strategy Server] Warning: Update query affected 0 rows for strategy '{strategy_name_to_update}', though it was found earlier.")
        #      # 仍然继续尝试删除回测结果，但可能返回一个警告性的成功？或者当作错误？暂定继续

    except Exception as e:
        logging.error(f"[Strategy Server] Error updating custom strategy '{strategy_name_to_update}' in DB: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": "Failed to update the strategy in the database."}), 500

    # 6. 删除该策略的旧回测结果
    logging.info(f"[Strategy Server] Deleting existing backtest results for updated strategy '{strategy_name_to_update}'.")
    delete_success = delete_backtest_results(strategy_name_to_update)
    if not delete_success:
        logging.warning(f"[Strategy Server] Warning: Failed to delete old backtest results for '{strategy_name_to_update}'. Proceeding anyway.")
        # 可以选择在这里返回带有警告的成功消息

    # 7. 返回成功
    logging.info(f"[Strategy Server] Strategy '{strategy_name_to_update}' updated successfully.")
    return jsonify({"success": True, "message": f"Strategy '{strategy_name_to_update}' updated successfully."}), 200

# --- 新增：创建策略 Endpoint ---
@app.route('/strategy/create', methods=['POST'])
def create_strategy_endpoint():
    """
    Endpoint to create a new custom strategy.
    Expects JSON payload: {"strategy_yaml": "<yaml_content>", "user_id": "<id>"}
    Validates the YAML, generates a UUID name, updates the internal name in YAML,
    and saves it to the custom strategy DB.
    Returns the new strategy UUID name.
    """
    logging.info("[Strategy Server] Received request for /strategy/create")
    if not request.is_json:
        logging.error("[Strategy Server] Error: Request is not JSON")
        return jsonify({"success": False, "error": "Request must be JSON"}), 400

    data = request.get_json()
    input_yaml_content = data.get('strategy_yaml')
    user_id = data.get('user_id')

    if not input_yaml_content:
        return jsonify({"success": False, "error": "Missing 'strategy_yaml' field in request JSON"}), 400
    if not user_id:
        return jsonify({"success": False, "error": "Missing 'user_id' field in request JSON"}), 400

    logging.info(f"[Strategy Server] Attempting to create new strategy for user: {user_id}")

    # 1. 校验输入的 YAML 内容
    logging.info("[Strategy Server] Validating input YAML...")
    is_valid, name_from_yaml = validate_strategy_config(input_yaml_content)

    if not is_valid:
        logging.error("[Strategy Server] Error: Input YAML validation failed.")
        # validate_strategy_config 内部会打印具体错误
        return jsonify({"success": False, "error": "Provided strategy configuration YAML is invalid."}), 400

    # 2. 生成新策略名称 (UUID)
    new_strategy_uuid = str(uuid.uuid4())
    logging.info(f"[Strategy Server] Generated new strategy name (UUID): {new_strategy_uuid}")

    # 3. 修改 YAML 内容，使其内部名称与 UUID 一致
    try:
        parsed_yaml = yaml.safe_load(input_yaml_content)
        if not isinstance(parsed_yaml, dict):
            # 理论上 validate_strategy_config 已经检查过，但再确认一次
            logging.error("[Strategy Server] Error: Input YAML content is not a valid dictionary after validation.")
            return jsonify({"success": False, "error": "Invalid strategy configuration format."}), 500

        # 更新 YAML 内部的 strategy_id
        original_internal_name = parsed_yaml.get('strategy_id')
        parsed_yaml['strategy_id'] = new_strategy_uuid
        final_yaml_content = yaml.dump(parsed_yaml, allow_unicode=True, sort_keys=False)
        logging.info(f"[Strategy Server] Updated internal strategy_id from '{original_internal_name}' to '{new_strategy_uuid}'.")

    except yaml.YAMLError as e:
        logging.error(f"[Strategy Server] Error parsing or dumping YAML during creation: {e}")
        return jsonify({"success": False, "error": "Error processing strategy configuration."}), 500
    except Exception as e:
         logging.error(f"[Strategy Server] Unexpected error processing YAML: {e}")
         traceback.print_exc()
         return jsonify({"success": False, "error": "An unexpected error occurred while processing the strategy configuration."}), 500

    # 4. 将新策略写入自定义策略数据库
    try:
        con = duckdb.connect(CUSTOM_STRATEGY_DB_FILE)
        current_time = datetime.datetime.now()
        con.execute(
            f"""
            INSERT INTO {CUSTOM_DB_TABLE_NAME} (strategy_id, content_yaml, creation_time, creator_user_id, strategy_type)
            VALUES (?, ?, ?, ?, ?)
            """,
            (new_strategy_uuid, final_yaml_content, current_time, user_id, 'portfolio')
        )
        con.commit()
        con.close()
        logging.info(f"[Strategy Server] Successfully saved new custom strategy '{new_strategy_uuid}' to DB for user '{user_id}'.")

        # 5. 返回成功和新的策略名称
        return jsonify({"success": True, "new_strategy_id": new_strategy_uuid}), 201 # 201 Created

    except duckdb.ConstraintException as e:
         # 理论上 UUID 不会重复
         logging.error(f"[Strategy Server] Error saving new strategy: Constraint violation (likely duplicate name '{new_strategy_uuid}'): {e}")
         traceback.print_exc()
         return jsonify({"success": False, "error": "Failed to save strategy due to a naming conflict."}), 409 # 409 Conflict
    except Exception as e:
        logging.error(f"[Strategy Server] Error saving new custom strategy '{new_strategy_uuid}' to DB: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": "Failed to save the new strategy to the database."}), 500

# --- 新增：实盘相关 Endpoints ---

# 在 strategy_server.py 中添加新的端点

@app.route('/strategy/live/run', methods=['POST'])
def run_live_strategy_endpoint():
    """
    运行实盘策略 - 使用进程管理器
    """
    logging.info("[Strategy Server] Received request for /strategy/live/run")
    if not request.is_json:
        return jsonify({"success": False, "error": "Request must be JSON"}), 400

    data = request.get_json()
    group_path = data.get('group_path')
    project_id = data.get('project_id')  # 新增：项目ID用于进程管理

    if not group_path:
        return jsonify({"success": False, "error": "Missing 'group_path' field"}), 400
    
    if not project_id:
        return jsonify({"success": False, "error": "Missing 'project_id' field"}), 400

    try:
        # 检查 run.py 是否存在
        run_script = os.path.join(group_path, 'run.py')
        if not os.path.exists(run_script):
            return jsonify({"success": False, "error": f"run.py not found in {group_path}"}), 400

        # 使用进程管理器启动
        command = [sys.executable, 'run.py']
        success, message, pid = live_process_manager.start_process(project_id, command, group_path)
        
        if success:
            return jsonify({
                "success": True,
                "message": message,
                "pid": pid,
                "project_id": project_id,
                "group_path": group_path
            })
        else:
            return jsonify({"success": False, "error": message}), 400

    except Exception as e:
        logging.error(f"[Strategy Server] Error in run_live_strategy_endpoint: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/strategy/live/stop', methods=['POST'])
def stop_live_strategy_endpoint():
    """
    停止实盘策略进程
    """
    data = request.get_json()
    project_id = data.get('project_id')
    force = data.get('force', False)
    
    if not project_id:
        return jsonify({"success": False, "error": "Missing 'project_id' field"}), 400
    
    success, message = live_process_manager.stop_process(project_id, force)
    
    if success:
        return jsonify({"success": True, "message": message})
    else:
        return jsonify({"success": False, "error": message}), 400

@app.route('/strategy/live/status/<project_id>', methods=['GET'])
def get_live_strategy_status(project_id: str):
    """
    获取实盘策略状态
    """
    status = live_process_manager.get_process_status(project_id)
    
    if status:
        return jsonify({"success": True, "data": status})
    else:
        return jsonify({"success": False, "error": "Process not found"}), 404

@app.route('/strategy/live/list', methods=['GET'])
def list_live_strategies():
    """
    列出所有实盘策略
    """
    processes = live_process_manager.list_all_processes()
    running_count = live_process_manager.get_running_count()
    
    return jsonify({
        "success": True, 
        "data": {
            "processes": processes,
            "total_count": len(processes),
            "running_count": running_count
        }
    })

# --- 注意：实盘项目配置生成已移至Node.js端的LiveGroupManager
# Python端只负责运行，不再处理配置生成和数据库查询

# --- 数据引擎管理模块 ---

class DataEngineManager:
    """
    中心数据引擎管理器
    负责管理实盘项目的品种订阅，更新contracts.json，重启数据引擎
    """

    def __init__(self):
        self.live_dt_dir = os.path.join(current_strategy_dir, 'portfolio', 'live_DT')
        self.common_dir = os.path.join(current_strategy_dir, 'common')
        self.contracts_file = os.path.join(self.live_dt_dir, 'contracts.json')  # 改为live_DT目录下
        self.data_engine_process = None
        self.subscription_registry = {}  # {project_id: [contract_codes]}
        self.contract_ref_count = {}     # {contract_code: ref_count} 引用计数器

        # 确保目录存在
        os.makedirs(self.live_dt_dir, exist_ok=True)

        logging.info(f"[DataEngine Manager] Initialized with live_DT: {self.live_dt_dir}")
        logging.info(f"[DataEngine Manager] Contracts file: {self.contracts_file}")
        logging.info(f"[DataEngine Manager] Reference counting enabled for contract subscriptions")

    def register_project_subscription(self, project_id: str, contract_codes: List[str]) -> tuple[bool, bool]:
        """
        注册实盘项目的品种订阅（使用引用计数机制）
        Args:
            project_id: 实盘项目ID
            contract_codes: 合约代码列表，如 ['SSE.600036', 'SZSE.000001']
        Returns:
            tuple[bool, bool]: (是否成功注册, 是否需要重启数据引擎)
        """
        try:
            logging.info(f"[DataEngine Manager] Registering subscription for project {project_id}: {contract_codes}")

            # 记录操作前的活跃合约列表
            old_active_contracts = set(self.get_all_subscribed_contracts())

            # 如果项目已存在，先减少旧订阅的引用计数
            if project_id in self.subscription_registry:
                old_contracts = self.subscription_registry[project_id]
                logging.info(f"[DataEngine Manager] Project {project_id} already exists, updating subscription")
                logging.info(f"[DataEngine Manager] Old contracts: {old_contracts}")
                self._decrease_ref_count(old_contracts)

            # 注册新的订阅
            self.subscription_registry[project_id] = contract_codes

            # 增加新订阅的引用计数
            self._increase_ref_count(contract_codes)

            # 检查操作后的活跃合约列表
            new_active_contracts = set(self.get_all_subscribed_contracts())

            # 判断是否需要重启数据引擎
            contracts_changed = old_active_contracts != new_active_contracts

            logging.info(f"[DataEngine Manager] Updated reference counts: {self.contract_ref_count}")
            logging.info(f"[DataEngine Manager] Contracts changed: {contracts_changed}")
            if contracts_changed:
                logging.info(f"[DataEngine Manager] Old active contracts: {sorted(old_active_contracts)}")
                logging.info(f"[DataEngine Manager] New active contracts: {sorted(new_active_contracts)}")

            return True, contracts_changed

        except Exception as e:
            logging.error(f"[DataEngine Manager] Error registering subscription for {project_id}: {e}")
            traceback.print_exc()
            return False, False

    def unregister_project_subscription(self, project_id: str) -> tuple[bool, bool]:
        """
        取消注册实盘项目的品种订阅（使用引用计数机制）
        Args:
            project_id: 实盘项目ID
        Returns:
            tuple[bool, bool]: (是否成功取消注册, 是否需要重启数据引擎)
        """
        try:
            if project_id in self.subscription_registry:
                logging.info(f"[DataEngine Manager] Unregistering subscription for project {project_id}")

                # 记录操作前的活跃合约列表
                old_active_contracts = set(self.get_all_subscribed_contracts())

                # 获取项目的合约列表
                contract_codes = self.subscription_registry[project_id]
                logging.info(f"[DataEngine Manager] Removing contracts: {contract_codes}")

                # 减少引用计数
                self._decrease_ref_count(contract_codes)

                # 从注册表中移除项目
                del self.subscription_registry[project_id]

                # 检查操作后的活跃合约列表
                new_active_contracts = set(self.get_all_subscribed_contracts())

                # 判断是否需要重启数据引擎
                contracts_changed = old_active_contracts != new_active_contracts

                logging.info(f"[DataEngine Manager] Updated reference counts: {self.contract_ref_count}")
                logging.info(f"[DataEngine Manager] Contracts changed: {contracts_changed}")
                if contracts_changed:
                    logging.info(f"[DataEngine Manager] Old active contracts: {sorted(old_active_contracts)}")
                    logging.info(f"[DataEngine Manager] New active contracts: {sorted(new_active_contracts)}")

                return True, contracts_changed
            else:
                logging.error(f"[DataEngine Manager] Project {project_id} not found in subscription registry")
                return False, False
        except Exception as e:
            logging.error(f"[DataEngine Manager] Error unregistering subscription for {project_id}: {e}")
            traceback.print_exc()
            return False, False

    def get_all_subscribed_contracts(self) -> List[str]:
        """
        获取所有已订阅的合约代码（基于引用计数，只返回引用计数>0的合约）
        Returns:
            List[str]: 当前有效的合约代码列表
        """
        # 基于引用计数获取有效合约
        active_contracts = [contract for contract, count in self.contract_ref_count.items() if count > 0]

        logging.info(f"[DataEngine Manager] Active contracts (ref_count > 0): {len(active_contracts)}")
        logging.info(f"[DataEngine Manager] Reference counts: {self.contract_ref_count}")
        return active_contracts

    def _increase_ref_count(self, contract_codes: List[str]) -> None:
        """
        增加合约的引用计数
        Args:
            contract_codes: 合约代码列表
        """
        for contract in contract_codes:
            if contract in self.contract_ref_count:
                self.contract_ref_count[contract] += 1
            else:
                self.contract_ref_count[contract] = 1
            logging.info(f"[DataEngine Manager] {contract} ref_count: {self.contract_ref_count[contract]}")

    def _decrease_ref_count(self, contract_codes: List[str]) -> None:
        """
        减少合约的引用计数
        Args:
            contract_codes: 合约代码列表
        """
        for contract in contract_codes:
            if contract in self.contract_ref_count:
                self.contract_ref_count[contract] -= 1
                logging.info(f"[DataEngine Manager] {contract} ref_count: {self.contract_ref_count[contract]}")

                # 如果引用计数降到0或以下，移除该合约
                if self.contract_ref_count[contract] <= 0:
                    del self.contract_ref_count[contract]
                    logging.info(f"[DataEngine Manager] {contract} removed from reference count (no longer needed)")
            else:
                logging.warning(f"[DataEngine Manager] Warning: {contract} not found in reference count")

    def get_contract_ref_count(self, contract_code: str) -> int:
        """
        获取指定合约的引用计数
        Args:
            contract_code: 合约代码
        Returns:
            int: 引用计数，如果不存在则返回0
        """
        return self.contract_ref_count.get(contract_code, 0)

    def load_existing_contracts(self) -> Dict:
        """
        加载现有的contracts.json文件
        Returns:
            Dict: 现有的合约配置，如果文件不存在则返回空字典
        """
        try:
            if os.path.exists(self.contracts_file):
                with open(self.contracts_file, 'r', encoding='utf-8') as f:
                    contracts = json.load(f)
                logging.info(f"[DataEngine Manager] Loaded existing contracts.json with {len(contracts)} exchanges")
                return contracts
            else:
                logging.info(f"[DataEngine Manager] contracts.json not found, will create new one")
                return {}
        except Exception as e:
            logging.error(f"[DataEngine Manager] Error loading contracts.json: {e}")
            return {}

    def generate_contract_entry(self, contract_code: str) -> Dict:
        """
        根据合约代码生成合约配置条目
        Args:
            contract_code: 合约代码，如 'SSE.600036' 或 'SZSE.000001'
        Returns:
            Dict: 合约配置条目
        """
        try:
            if '.' in contract_code:
                exchange, code = contract_code.split('.', 1)
            else:
                # 如果没有交易所前缀，根据代码判断
                if contract_code.startswith('6'):
                    exchange = 'SSE'
                elif contract_code.startswith(('0', '3')):
                    exchange = 'SZSE'
                else:
                    exchange = 'SSE'  # 默认
                code = contract_code

            # 根据代码类型确定产品类型
            if code.startswith('51') or code.startswith('15'):
                product = 'ETF'
                max_limit_qty = 50
                max_market_qty = 10
            else:
                product = 'STK'
                max_limit_qty = 100
                max_market_qty = 20

            # 生成合约名称（这里简化处理，实际可以从数据库或API获取）
            name = f"合约{code}"

            return {
                "name": name,
                "code": code,
                "exchg": exchange,
                "product": product,
                "maxlimitqty": max_limit_qty,
                "maxmarketqty": max_market_qty
            }
        except Exception as e:
            logging.error(f"[DataEngine Manager] Error generating contract entry for {contract_code}: {e}")
            return {}

    def update_contracts_file(self) -> bool:
        """
        更新contracts.json文件，只包含引用计数>0的合约
        Returns:
            bool: 是否成功更新
        """
        try:
            # 获取所有有效的合约（引用计数>0）
            active_contracts = self.get_all_subscribed_contracts()
            if not active_contracts:
                logging.info("[DataEngine Manager] No active contracts, creating empty contracts.json")
                # 如果没有活跃合约，创建空的配置文件
                empty_config = {}
                with open(self.contracts_file, 'w', encoding='utf-8') as f:
                    json.dump(empty_config, f, ensure_ascii=False, indent=2)
                logging.info("[DataEngine Manager] Created empty contracts.json")
                return True

            # 加载现有配置（如果需要保留其他配置）
            contracts_config = {}  # 重新生成，只包含当前活跃的合约

            # 按交易所分组处理合约
            for contract_code in active_contracts:
                contract_entry = self.generate_contract_entry(contract_code)
                if not contract_entry:
                    continue

                exchange = contract_entry['exchg']
                code = contract_entry['code']

                # 确保交易所节点存在
                if exchange not in contracts_config:
                    contracts_config[exchange] = {}

                # 添加合约配置
                contracts_config[exchange][code] = contract_entry

            # 保存更新后的配置
            with open(self.contracts_file, 'w', encoding='utf-8') as f:
                json.dump(contracts_config, f, ensure_ascii=False, indent=2)

            logging.info(f"[DataEngine Manager] Updated contracts.json with {len(active_contracts)} active contracts")
            return True

        except Exception as e:
            logging.error(f"[DataEngine Manager] Error updating contracts.json: {e}")
            traceback.print_exc()
            return False

    def stop_data_engine(self) -> bool:
        """
        停止数据引擎进程
        Returns:
            bool: 是否成功停止
        """
        try:
            if self.data_engine_process and self.data_engine_process.poll() is None:
                logging.info("[DataEngine Manager] Stopping data engine process...")
                self.data_engine_process.terminate()

                # 等待进程结束，最多等待10秒
                try:
                    self.data_engine_process.wait(timeout=10)
                    logging.info("[DataEngine Manager] Data engine process stopped gracefully")
                except subprocess.TimeoutExpired:
                    logging.info("[DataEngine Manager] Force killing data engine process...")
                    self.data_engine_process.kill()
                    self.data_engine_process.wait()
                    logging.info("[DataEngine Manager] Data engine process killed")

                self.data_engine_process = None
                return True
            else:
                logging.info("[DataEngine Manager] Data engine process not running")
                return True

        except Exception as e:
            logging.error(f"[DataEngine Manager] Error stopping data engine: {e}")
            return False

    def start_data_engine(self) -> bool:
        """
        启动数据引擎进程
        Returns:
            bool: 是否成功启动
        """
        try:
            # 确保之前的进程已停止
            self.stop_data_engine()

            # 检查runDT.py是否存在
            rundt_path = os.path.join(self.live_dt_dir, 'runDT.py')
            if not os.path.exists(rundt_path):
                logging.error(f"[DataEngine Manager] runDT.py not found at {rundt_path}")
                return False

            # 启动数据引擎进程
            logging.info(f"[DataEngine Manager] Starting data engine from {self.live_dt_dir}")
            self.data_engine_process = subprocess.Popen(
                [sys.executable, 'runDT.py'],
                cwd=self.live_dt_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 等待一小段时间检查进程是否正常启动
            time.sleep(2)
            if self.data_engine_process.poll() is None:
                logging.info(f"[DataEngine Manager] Data engine started successfully, PID: {self.data_engine_process.pid}")
                return True
            else:
                logging.error("[DataEngine Manager] Data engine failed to start")
                return False

        except Exception as e:
            logging.error(f"[DataEngine Manager] Error starting data engine: {e}")
            traceback.print_exc()
            return False

    def restart_data_engine(self) -> bool:
        """
        重启数据引擎（先更新配置，再重启）
        Returns:
            bool: 是否成功重启
        """
        try:
            logging.info("[DataEngine Manager] Restarting data engine...")

            # 1. 更新contracts.json
            if not self.update_contracts_file():
                logging.error("[DataEngine Manager] Failed to update contracts.json")
                return False

            # 2. 停止数据引擎
            if not self.stop_data_engine():
                logging.error("[DataEngine Manager] Failed to stop data engine")
                return False

            # 3. 启动数据引擎
            if not self.start_data_engine():
                logging.error("[DataEngine Manager] Failed to start data engine")
                return False

            logging.info("[DataEngine Manager] Data engine restarted successfully")
            return True

        except Exception as e:
            logging.error(f"[DataEngine Manager] Error restarting data engine: {e}")
            traceback.print_exc()
            return False

    def smart_restart_if_needed(self, contracts_changed: bool) -> bool:
        """
        智能重启：只有当合约列表发生变化时才重启数据引擎
        Args:
            contracts_changed: 合约列表是否发生变化
        Returns:
            bool: 是否成功（如果不需要重启则直接返回True）
        """
        if contracts_changed:
            logging.info("[DataEngine Manager] Contracts changed, restarting data engine...")
            return self.restart_data_engine()
        else:
            logging.info("[DataEngine Manager] Contracts unchanged, no restart needed")
            return True

# 创建全局数据引擎管理器实例
data_engine_manager = DataEngineManager()

# 全局进程跟踪字典 {project_id: process}
live_project_processes = {}

# --- 数据引擎管理 API 端点 ---

@app.route('/data_engine/subscribe', methods=['POST'])
def subscribe_contracts_endpoint():
    """
    注册实盘项目的品种订阅
    Expects JSON payload: {
        "project_id": "live_project_123",
        "contract_codes": ["SSE.600036", "SZSE.000001", "SSE.510300"]
    }
    """
    logging.info("[Strategy Server] Received request for /data_engine/subscribe")
    if not request.is_json:
        return jsonify({"success": False, "error": "Request must be JSON"}), 400

    data = request.get_json()
    project_id = data.get('project_id')
    contract_codes = data.get('contract_codes', [])

    if not project_id:
        return jsonify({"success": False, "error": "Missing 'project_id' field"}), 400
    if not contract_codes or not isinstance(contract_codes, list):
        return jsonify({"success": False, "error": "Missing or invalid 'contract_codes' field"}), 400

    try:
        # 注册订阅
        success, contracts_changed = data_engine_manager.register_project_subscription(project_id, contract_codes)
        if success:
            return jsonify({
                "success": True,
                "message": f"Registered subscription for project {project_id}",
                "contract_count": len(contract_codes),
                "contracts_changed": contracts_changed,
                "restart_needed": contracts_changed
            })
        else:
            return jsonify({"success": False, "error": "Failed to register subscription"}), 500

    except Exception as e:
        logging.error(f"[Strategy Server] Error in subscribe endpoint: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Internal error: {e}"}), 500

@app.route('/data_engine/unsubscribe', methods=['POST'])
def unsubscribe_contracts_endpoint():
    """
    取消注册实盘项目的品种订阅
    Expects JSON payload: {
        "project_id": "live_project_123"
    }
    """
    logging.info("[Strategy Server] Received request for /data_engine/unsubscribe")
    if not request.is_json:
        return jsonify({"success": False, "error": "Request must be JSON"}), 400

    data = request.get_json()
    project_id = data.get('project_id')

    if not project_id:
        return jsonify({"success": False, "error": "Missing 'project_id' field"}), 400

    try:
        # 取消注册订阅
        success, contracts_changed = data_engine_manager.unregister_project_subscription(project_id)
        if success:
            return jsonify({
                "success": True,
                "message": f"Unregistered subscription for project {project_id}",
                "contracts_changed": contracts_changed,
                "restart_needed": contracts_changed
            })
        else:
            return jsonify({"success": False, "error": "Project not found in subscription registry"}), 404

    except Exception as e:
        logging.error(f"[Strategy Server] Error in unsubscribe endpoint: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Internal error: {e}"}), 500

@app.route('/data_engine/restart', methods=['POST'])
def restart_data_engine_endpoint():
    """
    重启数据引擎（更新contracts.json并重启runDT进程）
    """
    logging.info("[Strategy Server] Received request for /data_engine/restart")

    try:
        # 重启数据引擎
        success = data_engine_manager.restart_data_engine()
        if success:
            all_contracts = data_engine_manager.get_all_subscribed_contracts()
            return jsonify({
                "success": True,
                "message": "Data engine restarted successfully",
                "total_contracts": len(all_contracts),
                "contracts": all_contracts
            })
        else:
            return jsonify({"success": False, "error": "Failed to restart data engine"}), 500

    except Exception as e:
        logging.error(f"[Strategy Server] Error in restart endpoint: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Internal error: {e}"}), 500

@app.route('/data_engine/status', methods=['GET'])
def data_engine_status_endpoint():
    """
    获取数据引擎状态
    """
    logging.info("[Strategy Server] Received request for /data_engine/status")

    try:
        # 检查数据引擎进程状态
        is_running = (data_engine_manager.data_engine_process and
                     data_engine_manager.data_engine_process.poll() is None)

        all_contracts = data_engine_manager.get_all_subscribed_contracts()

        process_id = None
        if is_running and data_engine_manager.data_engine_process:
            process_id = data_engine_manager.data_engine_process.pid

        status_info = {
            "engine_running": is_running,
            "process_id": process_id,
            "total_projects": len(data_engine_manager.subscription_registry),
            "total_contracts": len(all_contracts),
            "subscribed_contracts": all_contracts,
            "project_subscriptions": data_engine_manager.subscription_registry,
            "contract_ref_counts": data_engine_manager.contract_ref_count
        }

        return jsonify({"success": True, "data": status_info})

    except Exception as e:
        logging.error(f"[Strategy Server] Error in status endpoint: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Internal error: {e}"}), 500

@app.route('/data_engine/update_and_restart', methods=['POST'])
def update_and_restart_endpoint():
    """
    一键更新订阅并重启数据引擎
    Expects JSON payload: {
        "project_id": "live_project_123",
        "contract_codes": ["SSE.600036", "SZSE.000001"]
    }
    """
    logging.info("[Strategy Server] Received request for /data_engine/update_and_restart")
    if not request.is_json:
        return jsonify({"success": False, "error": "Request must be JSON"}), 400

    data = request.get_json()
    project_id = data.get('project_id')
    contract_codes = data.get('contract_codes', [])

    if not project_id:
        return jsonify({"success": False, "error": "Missing 'project_id' field"}), 400
    if not isinstance(contract_codes, list):
        return jsonify({"success": False, "error": "Invalid 'contract_codes' field"}), 400

    try:
        # 1. 注册订阅
        success, contracts_changed = data_engine_manager.register_project_subscription(project_id, contract_codes)
        if not success:
            return jsonify({"success": False, "error": "Failed to register subscription"}), 500

        # 2. 智能重启数据引擎（只有在需要时才重启）
        restart_success = data_engine_manager.smart_restart_if_needed(contracts_changed)
        if not restart_success:
            return jsonify({"success": False, "error": "Failed to restart data engine"}), 500

        all_contracts = data_engine_manager.get_all_subscribed_contracts()

        message = f"Updated subscription for project {project_id}"
        if contracts_changed:
            message += " and restarted data engine"
        else:
            message += " (no restart needed - contracts unchanged)"

        return jsonify({
            "success": True,
            "message": message,
            "project_contracts": len(contract_codes),
            "total_contracts": len(all_contracts),
            "contracts_changed": contracts_changed,
            "engine_restarted": contracts_changed
        })

    except Exception as e:
        logging.error(f"[Strategy Server] Error in update_and_restart endpoint: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Internal error: {e}"}), 500

# 旧的项目启动端点已移除，现在由Node.js的LiveGroupManager负责配置生成
# Python只负责运行策略

@app.route('/api/strategy/live/stop/<int:project_id>', methods=['POST'])
def stop_live_project_endpoint(project_id):
    """
    停止实盘项目
    """
    logging.info(f"[Strategy Server] Received request to stop live project {project_id}")

    try:
        # 检查项目是否在运行
        if project_id not in live_project_processes:
            return jsonify({"success": False, "error": "Live project not found or not running"}), 404

        process = live_project_processes[project_id]

        # 1. 停止项目进程
        from strategy.portfolio.live_center import stop_live_project_process

        stop_success = stop_live_project_process(process)

        if stop_success:
            # 2. 取消数据订阅
            unsubscribe_success, contracts_changed = data_engine_manager.unregister_project_subscription(
                f"live_project_{project_id}"
            )

            if unsubscribe_success and contracts_changed:
                # 3. 智能重启数据引擎（如果需要）
                restart_success = data_engine_manager.smart_restart_if_needed(contracts_changed)
                if not restart_success:
                    logging.warning(f"[Strategy Server] Warning: Failed to restart data engine after stopping project {project_id}")

            # 4. 清理进程跟踪
            del live_project_processes[project_id]

            return jsonify({
                "success": True,
                "message": f"Live project {project_id} stopped successfully",
                "project_id": project_id,
                "process_stopped": stop_success,
                "data_unsubscribed": unsubscribe_success,
                "contracts_changed": contracts_changed
            })
        else:
            return jsonify({"success": False, "error": "Failed to stop project process"}), 500

    except Exception as e:
        logging.error(f"[Strategy Server] Error stopping live project {project_id}: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Internal error: {e}"}), 500

@app.route('/api/strategy/live/status/<int:project_id>', methods=['GET'])
def get_live_project_status_endpoint(project_id):
    """
    获取实盘项目状态
    """
    logging.info(f"[Strategy Server] Received request for live project {project_id} status")

    try:
        if project_id in live_project_processes:
            process = live_project_processes[project_id]
            is_running = process.poll() is None

            return jsonify({
                "success": True,
                "project_id": project_id,
                "is_running": is_running,
                "process_id": process.pid if is_running else None,
                "status": "running" if is_running else "stopped"
            })
        else:
            return jsonify({
                "success": True,
                "project_id": project_id,
                "is_running": False,
                "process_id": None,
                "status": "not_started"
            })

    except Exception as e:
        logging.error(f"[Strategy Server] Error getting live project {project_id} status: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Internal error: {e}"}), 500

@app.route('/api/strategy/live/list', methods=['GET'])
def list_live_projects_endpoint():
    """
    列出所有运行中的实盘项目
    """
    logging.info("[Strategy Server] Received request for live projects list")

    try:
        running_projects = []

        for project_id, process in live_project_processes.items():
            is_running = process.poll() is None
            running_projects.append({
                "project_id": project_id,
                "process_id": process.pid if is_running else None,
                "is_running": is_running,
                "status": "running" if is_running else "stopped"
            })

        return jsonify({
            "success": True,
            "projects": running_projects,
            "total_count": len(running_projects)
        })

    except Exception as e:
        logging.error(f"[Strategy Server] Error listing live projects: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Internal error: {e}"}), 500

# --- Main Execution ---
if __name__ == '__main__':
    # 全局变量用于存储需要清理的资源
    cleanup_resources = []
    
    def signal_handler(signum, frame):
        """处理退出信号"""
        logging.info("[Strategy Server] Received shutdown signal, cleaning up...")
        
        # 设置退出标志
        init_db._exiting = True
        init_custom_strategy_db._exiting = True
        
        # 清理LiveProcessManager
        try:
            logging.info("[Strategy Server] Shutting down LiveProcessManager...")
            if hasattr(live_process_manager, 'shutdown'):
                live_process_manager.shutdown()
            logging.info("LiveProcessManager shutdown complete")
        except Exception as e:
            logging.error(f"[Strategy Server] Error shutting down LiveProcessManager: {e}")
        
        # 清理其他资源
        for resource in cleanup_resources:
            try:
                if hasattr(resource, 'close'):
                    resource.close()
                elif hasattr(resource, 'shutdown'):
                    resource.shutdown()
            except Exception as e:
                logging.error(f"[Strategy Server] Error cleaning up resource: {e}")
        
        logging.info("[Strategy Server] Cleanup complete, exiting...")
        # 使用os._exit避免触发其他清理逻辑
        os._exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    init_db() # Initialize the backtest results database
    init_custom_strategy_db() # 新增：Initialize the custom strategies database
    logging.info("[Strategy Server] Starting Flask development server...")

    # 新增：读取 config.json 的 strategy_service.port 字段
    import json
    config_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../config.json'))
    port = 5002  # 默认端口
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            port = int(config.get('strategy_service', {}).get('port', 5002))
            logging.info(f"[Strategy Server] Using port from config.json: {port}")
    except Exception as e:
        logging.warning(f"[Strategy Server] Failed to read port from config.json, using default 5002. Error: {e}")

    try:
        app.run(host='0.0.0.0', port=port, debug=True, use_reloader=False)
    except KeyboardInterrupt:
        logging.info("[Strategy Server] Received KeyboardInterrupt, exiting...")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        logging.error(f"[Strategy Server] Unexpected error: {e}")
        # 不调用signal_handler，直接退出避免DuckDB错误
        logging.info("[Strategy Server] Exiting due to error...")
        os._exit(1)