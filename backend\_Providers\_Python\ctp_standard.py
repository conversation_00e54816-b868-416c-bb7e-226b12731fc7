# -*- coding: utf-8 -*-
"""
标准CTP行情获取程序
完全按照CTP官方示例实现
"""
from openctp_ctp import thostmduserapi as mdapi

class MdSpi(mdapi.CThostFtdcMdSpi):
    def __init__(self):
        super().__init__()
        self.api = None

    def OnFrontConnected(self):
        print("OnFrontConnected")
        # 行情通道不检查用户名密码
        req = mdapi.CThostFtdcReqUserLoginField()
        self.api.ReqUserLogin(req, 0)

    def OnFrontDisconnected(self, nReason):
        print(f"OnFrontDisconnected.[nReason={nReason}]")

    def OnRspUserLogin(self, pRspUserLogin, pRspInfo, nRequestID, bIsLast):
        if pRspInfo is not None and pRspInfo.ErrorID != 0:
            print(f"Login failed. {pRspInfo.ErrorMsg}")
            return
        print(f"Login succeed.{pRspUserLogin.TradingDay}")

        # 订阅合约 - 使用UTF-8编码
        print("开始订阅合约...")
        self.api.SubscribeMarketData(["600000".encode('utf-8'), "rb2412".encode('utf-8')], 2)

    def OnRspSubMarketData(self, pSpecificInstrument, pRspInfo, nRequestID, bIsLast):
        if pRspInfo is not None and pRspInfo.ErrorID != 0:
            print(f"订阅失败: {pRspInfo.ErrorMsg}")
        else:
            print(f"订阅成功: {pSpecificInstrument.InstrumentID}")
        if bIsLast:
            print("所有合约订阅完成")

    def OnRtnDepthMarketData(self, pDepthMarketData):
        print(f"收到行情: {pDepthMarketData.InstrumentID} - 价格:{pDepthMarketData.LastPrice} - 成交量:{pDepthMarketData.Volume}")

def main():
    # 创建API
    api = mdapi.CThostFtdcMdApi.CreateFtdcMdApi()
    spi = MdSpi()
    spi.api = api
    
    # 注册前置和回调
    api.RegisterFront("tcp://***************:10211")
    api.RegisterSpi(spi)
    api.Init()
    
    print("CTP行情客户端启动...")
    
    # 保持运行
    api.Join()

if __name__ == "__main__":
    main()
