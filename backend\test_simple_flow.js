#!/usr/bin/env node

/**
 * 简化的实盘策略流程测试
 */

const { v4: uuidv4 } = require('uuid');
const LiveStrategyAdapter = require('./services/LiveStrategyAdapter');
const { LiveStrategy } = require('./models');

async function testSimpleFlow() {
  console.log('🚀 开始简化流程测试...\n');

  try {
    // 测试用户ID
    const userId = 1;
    const username = 'test_user';

    // 获取适配器实例（单例）
    const adapter = LiveStrategyAdapter;
    console.log('✅ 适配器获取成功');

    // 模拟策略YAML
    const mockStrategyYaml = `
trading_type: etf
universe:
  - "SSE.ETF.510300"
  - "SSE.ETF.510500"
bar_count: 50
data_freq: "day"
top_n: 2
`;

    // 1. 创建实盘策略记录
    console.log('📦 创建实盘策略记录...');
    const liveStrategyId = uuidv4();
    
    const liveStrategy = await LiveStrategy.create({
      id: liveStrategyId,
      userId,
      username,
      strategyId: 'test_strategy',
      name: `实盘-测试策略`,
      status: 'stopped',
      accountId: 'test_account',
      initialCapital: 100000,
      commissionRate: 0.0003,
      riskSettings: { stopLossPercent: 5 },
      yaml: mockStrategyYaml,
      createdAt: new Date(),
      lastUpdateTime: new Date()
    });

    console.log(`✅ 策略创建成功，ID: ${liveStrategyId}`);

    // 2. 测试获取策略列表
    console.log('\n📋 获取策略列表...');
    const strategiesResult = await adapter.getLiveStrategies(userId);
    
    if (strategiesResult.success) {
      console.log(`✅ 获取到 ${strategiesResult.data.length} 个策略`);
    } else {
      console.error(`❌ 获取策略列表失败: ${strategiesResult.error}`);
      return;
    }

    // 3. 测试用户组合管理器初始化
    console.log('\n🔧 初始化用户组合管理器...');
    const groupManager = await adapter.getUserGroupManager(userId);
    console.log('✅ 组合管理器初始化成功');

    const groupStatus = await groupManager.getGroupStatus();
    console.log('组合状态:', {
      id: groupStatus.id,
      status: groupStatus.status,
      currentCapital: groupStatus.currentCapital
    });

    // 4. 测试配置生成（不启动引擎）
    console.log('\n⚙️ 测试配置生成...');
    
    // 手动添加策略到组合
    await groupManager._addStrategyToGroup(liveStrategyId);
    console.log('✅ 策略已添加到组合');

    // 生成配置文件
    await groupManager._generateGroupConfig();
    console.log('✅ 配置文件生成成功');

    // 5. 检查生成的文件
    console.log('\n📁 检查生成的配置文件...');
    const path = require('path');
    const fs = require('fs').promises;
    
    const groupPath = path.dirname(groupManager.group.configPath);
    console.log(`组合目录: ${groupPath}`);
    
    const expectedFiles = ['config.yaml', 'contracts.json', 'executers.yaml'];
    
    for (const fileName of expectedFiles) {
      const filePath = path.join(groupPath, fileName);
      try {
        await fs.access(filePath);
        console.log(`✅ ${fileName} - 存在`);
        
        // 显示文件大小
        const stats = await fs.stat(filePath);
        console.log(`   大小: ${stats.size} 字节`);
      } catch (error) {
        console.log(`❌ ${fileName} - 不存在`);
      }
    }

    // 6. 显示contracts.json内容
    try {
      const contractsPath = path.join(groupPath, 'contracts.json');
      const contractsContent = await fs.readFile(contractsPath, 'utf8');
      console.log('\n📄 contracts.json 内容:');
      console.log(contractsContent);
    } catch (error) {
      console.log('❌ 无法读取contracts.json:', error.message);
    }

    console.log('\n🎉 简化流程测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误堆栈:', error.stack);
  } finally {
    // 关闭数据库连接
    try {
      const { sequelize } = require('./database');
      await sequelize.close();
      console.log('✅ 数据库连接已关闭');
    } catch (error) {
      console.log('⚠️ 关闭数据库连接时出错:', error.message);
    }
  }
}

// 运行测试
if (require.main === module) {
  testSimpleFlow().then(() => {
    console.log('\n测试结束');
    process.exit(0);
  }).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = testSimpleFlow;
