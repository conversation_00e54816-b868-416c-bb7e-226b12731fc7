#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths测试服务器

该脚本提供了一个简单的测试服务器，用于测试easytrader_ths客户端的连接。
"""

from flask import Flask, request, jsonify
import logging
import json
import time
import requests

# 配置日志
# --- 修改：明确指定 FileHandler 的编码 --- 
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_file_handler = logging.FileHandler("test_server.log", encoding='utf-8') # 指定编码
log_file_handler.setFormatter(log_formatter)
log_stream_handler = logging.StreamHandler()
log_stream_handler.setFormatter(log_formatter)

logger = logging.getLogger("EasyTrader-THS-TestServer")
logger.setLevel(logging.INFO)
logger.addHandler(log_file_handler)
logger.addHandler(log_stream_handler)
# 移除旧的 basicConfig 调用，因为它会添加默认处理器
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler("test_server.log"),
#         logging.StreamHandler()
#     ]
# )
# --- 结束修改 ---

app = Flask(__name__)
clients = {}

@app.route('/api/register_trading_client', methods=['POST'])
def register_trading_client():
    """注册交易客户端"""
    try:
        data = request.get_json(silent=True)
        if data is None:
            return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400
        
        username = data.get('username')
        client_ip = data.get('client_ip')
        client_port = data.get('client_port')
        client_type = data.get('client_type')
        timestamp = data.get('timestamp')
        
        if not all([username, client_ip, client_port, client_type, timestamp]):
            return jsonify({"success": False, "message": "参数不完整"}), 400
        
        client_url = f"http://{client_ip}:{client_port}"
        
        # 检查客户端连接
        try:
            response = requests.get(f"{client_url}/api/status", timeout=5)
            if response.status_code != 200:
                logger.error(f"客户端连接失败: {client_url}")
                return jsonify({"success": False, "message": "客户端连接失败"}), 500
        except Exception as e:
            logger.error(f"客户端连接失败: {client_url}, 错误: {str(e)}")
            return jsonify({"success": False, "message": f"客户端连接失败: {str(e)}"}), 500
        
        # 注册客户端
        clients[username] = {
            'username': username,
            'client_ip': client_ip,
            'client_port': client_port,
            'client_url': client_url,
            'client_type': client_type,
            'last_heartbeat': timestamp,
            'registered_at': timestamp
        }
        
        logger.info(f"客户端注册成功: username={username}, client_url={client_url}")
        return jsonify({"success": True, "message": "注册成功"})
    except Exception as e:
        logger.error(f"注册交易客户端失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/trading_client_heartbeat', methods=['POST'])
def trading_client_heartbeat():
    """交易客户端心跳"""
    try:
        data = request.get_json(silent=True)
        if data is None:
            return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400
        
        username = data.get('username')
        timestamp = data.get('timestamp')
        
        if not all([username, timestamp]):
            return jsonify({"success": False, "message": "参数不完整"}), 400
        
        if username not in clients:
            logger.warning(f"心跳更新失败: 客户端不存在, username={username}")
            return jsonify({"success": False, "message": "客户端不存在"}), 404
        
        clients[username]['last_heartbeat'] = timestamp
        logger.debug(f"心跳更新成功: username={username}")
        return jsonify({"success": True, "message": "心跳更新成功"})
    except Exception as e:
        logger.error(f"更新交易客户端心跳失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/clients', methods=['GET'])
def get_clients():
    """获取所有客户端"""
    try:
        return jsonify({"success": True, "clients": list(clients.values())})
    except Exception as e:
        logger.error(f"获取客户端列表失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/test_client', methods=['GET'])
def test_client():
    """测试客户端连接"""
    try:
        username = request.args.get('username')
        if not username:
            return jsonify({"success": False, "message": "参数不完整"}), 400
        
        if username not in clients:
            return jsonify({"success": False, "message": "客户端不存在"}), 404
        
        client = clients[username]
        client_url = client['client_url']
        
        # 测试连接客户端
        try:
            response = requests.get(f"{client_url}/api/status", timeout=5)
            if response.status_code == 200:
                return jsonify({
                    "success": True, 
                    "message": "连接成功", 
                    "data": response.json()
                })
            else:
                return jsonify({
                    "success": False, 
                    "message": f"连接失败，状态码: {response.status_code}"
                }), 500
        except Exception as e:
            return jsonify({
                "success": False, 
                "message": f"连接失败: {str(e)}"
            }), 500
    except Exception as e:
        logger.error(f"测试客户端连接失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/test_trade', methods=['POST'])
def test_trade():
    """测试交易功能 (修改：即使客户端未注册也尝试转发)"""
    try:
        data = request.get_json(silent=True)
        if data is None:
             return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400
        
        username = data.get('username')
        action = data.get('action')
        params = data.get('params')
        
        if not all([username, action]):
            return jsonify({"success": False, "message": "参数不完整"}), 400
        
        client_url = None
        # --- 修改: 尝试获取客户端 URL，如果不存在则使用默认值 --- 
        if username in clients:
            client = clients[username]
            client_url = client['client_url']
            logger.info(f"为用户 '{username}' 找到已注册的客户端 URL: {client_url}")
        else:
            # 如果客户端未注册，则使用默认 URL (用于测试)
            default_client_port = 8888 # 从 client.py 默认配置获取或硬编码
            client_url = f"http://127.0.0.1:{default_client_port}"
            logger.warning(f"客户端 '{username}' 未注册。将尝试使用默认测试 URL: {client_url}")
            # Removed: return jsonify({"success": False, "message": "客户端不存在"}), 404
        # --- 结束修改 ---

        # 执行交易操作 (现在总会尝试执行，使用获取到的或默认的 client_url)
        try:
            if action == "balance":
                response = requests.get(f"{client_url}/api/balance", timeout=10)
            elif action == "position":
                response = requests.get(f"{client_url}/api/position", timeout=10)
            elif action == "today_entrusts":
                response = requests.get(f"{client_url}/api/today_entrusts", timeout=10)
            elif action == "today_trades":
                response = requests.get(f"{client_url}/api/today_trades", timeout=10)
            elif action == "buy":
                if not params:
                    return jsonify({"success": False, "message": "买入参数不能为空"}), 400
                response = requests.post(f"{client_url}/api/buy", json=params, timeout=10)
            elif action == "sell":
                if not params:
                    return jsonify({"success": False, "message": "卖出参数不能为空"}), 400
                response = requests.post(f"{client_url}/api/sell", json=params, timeout=10)
            elif action == "cancel":
                if not params:
                    return jsonify({"success": False, "message": "撤单参数不能为空"}), 400
                response = requests.post(f"{client_url}/api/cancel", json=params, timeout=10)
            elif action == "refresh":
                # 假设 refresh 不需要参数，如果需要，应从 params 获取
                response = requests.post(f"{client_url}/api/refresh", timeout=10)
            else:
                return jsonify({"success": False, "message": f"不支持的操作: {action}"}), 400
            
            if response.status_code == 200:
                return jsonify({
                    "success": True, 
                    "message": "操作成功", 
                    "data": response.json()
                })
            else:
                return jsonify({
                    "success": False, 
                    "message": f"操作失败，状态码: {response.status_code}"
                }), 500
        except Exception as e:
            return jsonify({
                "success": False, 
                "message": f"操作失败: {str(e)}"
            }), 500
    except Exception as e:
        logger.error(f"测试交易功能失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

if __name__ == '__main__':
    logger.info("启动测试服务器，监听端口: 8000")
    app.run(host='0.0.0.0', port=8000)
