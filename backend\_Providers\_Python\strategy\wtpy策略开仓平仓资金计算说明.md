# wtpy 策略买卖与资金管理说明

本文档旨在说明在 `wtpy` (WonderTrader Python API) 中编写策略时，如何进行资金管理、仓位计算和交易执行，特别适用于需要管理多个品种的选股或资产组合策略（如 `MultiFactorsSel`）。

## 核心概念

1.  **策略类型**: `wtpy` 提供不同的策略基类，如 `BaseCtaStrategy` (适用于单一品种CTA) 和 `BaseSelStrategy` (适用于多品种选择和组合管理)。虽然基类不同，但核心的交易执行和信息获取 API (通过 `context` 对象提供) 大部分是通用的。
2.  **资金管理**: 策略本身需要对资金进行管理和分配。通常在初始化时接收一个总的初始资金，在运行过程中根据当前的动态权益 (`context.stra_get_fund_data(0)`) 和策略逻辑（如权重）来计算每个品种的目标仓位。
3.  **状态管理**: 策略需要维护自身的状态，例如上次调仓时间、当前持仓情况（虽然可以通过 API 获取，但有时策略内部维护更方便）。
4.  **股票 vs 合约**: 两者在交易机制上有显著差异：
    *   **交易单位**: 股票通常以 100 股为一手进行买卖；合约通常以 1 手为单位。
    *   **保证金**: 合约交易涉及保证金，计算可开仓位时需要考虑；股票则没有保证金概念。
    *   **做空**: 股票通常不允许或限制做空；合约可以双向交易。
    *   **价格单位**: 合约有 `volscale` (每手乘数)，计算价值时需乘以价格和 `volscale`；股票则直接是价格乘以数量。

## 策略实现关键步骤

### 1. 初始化 (`__init__`)

*   接收策略参数，包括 `name`, `codes` (品种列表), `period` (数据周期) 等。
*   **接收初始资金**: 添加一个 `initial_capital` 参数来接收回测或实盘开始时的总资金。
*   **区分股票/合约**: 添加一个 `isForStk` (布尔值) 参数来标识策略是用于股票还是合约，以便后续处理差异。
*   设置其他策略参数，如调仓周期 (`rebalance_interval`)、选股数量 (`top_n`)、权重方案 (`weighting_scheme`) 等。

```python
#示例 (MultiFactorsSel.__init__)
import math # 需要导入 math 库

class MultiFactorsSel(BaseSelStrategy):
    def __init__(self, name, codes:list, barCnt:int, period:str, initial_capital:float, isForStk:bool = False):
        BaseSelStrategy.__init__(self, name)
        self.__codes__ = codes
        self.__period__ = period
        self.__bar_cnt__ = barCnt
        # 存储初始资金 - 注意：wtpy的回测资金由此参数设定，策略内部通常不直接使用它来限制交易
        # 策略内应使用 context.stra_get_fund_data(0) 获取当前动态权益
        self.__initial_capital__ = initial_capital
        self.__is_stk__ = isForStk # 标记是否为股票
        # ... 其他参数初始化 ...
        self.__last_rebalance_date__ = 0
        # --- 从配置文件加载 ---
        self.__rebalance_interval__ = 'daily' # 默认值
        self.__top_n__ = 1 # 默认值
        self.__weighting_scheme__ = 'equal' # 默认值
        self.__factors__ = []
        self.__factor_modules__ = {}
        # --------------------

    def on_init(self, context:SelContext):
        # 在这里加载配置文件，覆盖默认值
        # ... (加载 index.yaml 的代码) ...
        # 加载因子模块
        self.__load_factor_modules__()

    # ... 其他方法 ...

```

### 2. 调仓逻辑 (例如在 `on_calculate` 或 `__rebalance_portfolio__` 中)

#### a. 获取当前资金状况

*   使用 `context.stra_get_fund_data(0)` 获取当前的**动态权益**。这是进行仓位计算的基础。

```python
current_fund = context.stra_get_fund_data(0)
print(f"[策略计算] 当前动态权益: {current_fund}")
if current_fund <= 0:
    print("[策略计算] 资金不足，跳过本次调仓")
    return
```

#### b. 确定目标品种和权重

*   根据策略逻辑（如因子打分、排序）选出目标持仓品种 (`selected_codes`)。
*   根据权重方案 (`equal`, `signal_weighted` 等) 计算每个选中品种的目标权重 (`weights[code]`)。

#### c. 计算目标仓位数量 (`qty`)

*   对每一个选中的 `code`：
    *   **分配资金**: `allocated_capital = current_fund * weights[code]`
    *   **获取价格**: `estimated_price = context.stra_get_price(code)` 或通过 `context.stra_get_bars` 获取最新收盘价。需要处理价格获取失败或价格为零的情况。
    *   **确定交易单位**: `trade_unit = 100 if self.__is_stk__ else 1`
    *   **获取品种信息 (合约需要)**: 如果是合约，需要获取 `volscale` 和 `margin_rate`。`pInfo = context.stra_get_comminfo(code)`。保证金率 (`margin_rate`) 可能需要从其他地方获取（如配置文件或固定值），`wtpy` 本身不直接提供每个品种的实时保证金率。假设我们有一个 `self.__margin_rate__`。
    *   **计算手数**:\
        *   **股票**: `target_units = math.floor(allocated_capital / estimated_price / trade_unit) * trade_unit` (向下取整到 `trade_unit` 的倍数)\
        *   **合约**: 需要考虑保证金。`trdUnit_value_per_unit = pInfo.volscale * estimated_price` (一手合约的名义价值)。`margin_per_unit = trdUnit_value_per_unit * self.__margin_rate__` (一手的保证金)。 `target_units = math.floor(allocated_capital / margin_per_unit)` (合约通常单位是1，直接取整)。 *注意：保证金计算可能更复杂，具体取决于经纪商规则和资金使用率控制。*\
    *   **检查最小单位**: 确保计算出的 `target_units` 大于等于 `trade_unit` (如果资金足够的话)。

```python
# 示例 (计算股票/合约目标数量)
import math # 确保导入 math

target_units = 0
trade_unit = 100 if self.__is_stk__ else 1
estimated_price = context.stra_get_price(code) # 优先使用最新价

if estimated_price <= 0: # 处理无效价格
    print(f"[策略计算] {code} 价格无效 ({estimated_price})，无法计算仓位")
else:
    if self.__is_stk__: # 股票逻辑
        target_units = math.floor(allocated_capital / estimated_price / trade_unit) * trade_unit
        # 简单处理：如果计算出小于1手但资金足够买1手，则至少买1手
        if target_units < trade_unit and allocated_capital >= estimated_price * trade_unit:
             target_units = trade_unit
    else: # 合约逻辑 (假设 self.__margin_rate__ 已定义)
        pInfo = context.stra_get_comminfo(code)
        if pInfo is None:
             print(f"[策略计算] {code} 无法获取品种信息，跳过")
        else:
            trdUnit_value_per_unit = pInfo.volscale * estimated_price
            # 假设保证金率，实际中应更精确管理
            margin_rate = getattr(self, '__margin_rate__', 0.15) # 尝试获取，默认0.15
            margin_per_unit = trdUnit_value_per_unit * margin_rate
            if margin_per_unit > 0:
                target_units = math.floor(allocated_capital / margin_per_unit)
            else:
                print(f"[策略计算] {code} 计算保证金为0或负数，无法计算仓位")

print(f"[策略计算] {code} 分配资金: {allocated_capital:.2f}, 价格: {estimated_price:.2f}, 计算目标单位: {target_units}")

```

#### d. 执行交易

*   **获取当前持仓**: `current_positions = context.stra_get_all_position()` (返回字典，键为品种代码，值为当前持仓数量)
*   **推荐方法: 使用 `stra_set_position`**: 这是处理轮动和目标权重策略最简洁的方式。
    *   **整合处理**: 将当前持仓的品种和新选中的品种合并，遍历这个集合。
    *   **计算目标**: 对每个品种，计算其目标仓位 `target_units`（如果不在新选中列表，目标就是 0）。
    *   **比较并执行**: 获取该品种当前实际持仓 `current_pos = current_positions.get(code, 0)`。只有当 `target_units != current_pos` 时，才调用 `context.stra_set_position(code, target_units)`。

```python
# 示例 (使用 stra_set_position)
import math # 确保导入

current_positions = context.stra_get_all_position() # 获取现有持仓 {code: position}
print(f"[交易执行] 当前持仓: {current_positions}")

# 合并所有需要考虑的品种
all_codes_in_trade = set(current_positions.keys()) | set(selected_codes)

for code in all_codes_in_trade:
    target_units = 0 # 默认目标是清仓
    if code in selected_codes:
        # 重新计算该品种的目标单位数 (代码同上一步 c)
        allocated_capital = current_fund * weights[code]
        estimated_price = context.stra_get_price(code)

        if estimated_price > 0:
            trade_unit = 100 if self.__is_stk__ else 1
            if self.__is_stk__:
                target_units = math.floor(allocated_capital / estimated_price / trade_unit) * trade_unit
                if target_units < trade_unit and allocated_capital >= estimated_price * trade_unit:
                    target_units = trade_unit
            else:
                pInfo = context.stra_get_comminfo(code)
                if pInfo:
                    margin_rate = getattr(self, '__margin_rate__', 0.15)
                    margin_per_unit = pInfo.volscale * estimated_price * margin_rate
                    if margin_per_unit > 0:
                         target_units = math.floor(allocated_capital / margin_per_unit)
                    else:
                         target_units = 0 # 防止除零
                else:
                    target_units = 0 # 无品种信息无法计算

    # 获取当前实际持仓
    current_pos = current_positions.get(code, 0)

    # 仅在目标与当前不同时执行
    if current_pos != target_units:
        print(f"[交易执行] {code}: 设置目标仓位从 {current_pos} 到 {target_units}")
        context.stra_set_position(code, target_units) # 自动处理多空和开平
    else:
        # 可选：打印无需操作的信息
        # print(f"[交易执行] {code}: 目标仓位 {target_units} 与当前相同，无需操作")
        pass

```

*   **备选方法: 使用 `enter/exit` 系列接口**:
    *   需要更复杂的逻辑：先遍历当前持仓，判断是否需要平仓 (`exit_long`/`exit_short`)。再遍历目标仓位，判断是开新仓 (`enter_long`/`enter_short`) 还是对已有仓位进行调整。
    *   优点：可以更精细地控制交易指令和标记 (`usertag`)。
    *   缺点：代码更复杂，更容易出错，对于目标权重调整的场景显得冗余。

### 3. 更新状态

*   在调仓逻辑执行完毕后，更新 `self.__last_rebalance_date__ = context.stra_get_date()`。

## API 参考

以下是编写策略时常用的 API 及其说明：

### 获取持仓信息

*   **`stra_get_position(self, stdCode:str = "", usertag:str = "") -> float`**
    *   **说明**: 获取指定品种的当前持仓部位。
    *   **参数**:\
        *   `stdCode`: 品种代码，不传则使用主 K 线合约。\
        *   `usertag`: 入场标记，用于区分同一品种的不同策略或批次持仓。
    *   **返回值**: 持仓数量。正数表示多头，负数表示空头，0 表示无仓位。

*   **`stra_get_position_avgpx(self, stdCode:str) -> float`**
    *   **说明**: 获取指定品种的持仓均价。
    *   **参数**: `stdCode`: 品种代码。
    *   **返回值**: 持仓均价。

*   **`stra_get_position_profit(self, stdCode:str) -> float`**
    *   **说明**: 获取指定品种的持仓浮动盈亏。
    *   **参数**: `stdCode`: 品种代码。
    *   **返回值**: 浮动盈亏金额。

*   **`stra_get_all_position(self) -> dict`**
    *   **说明**: 获取当前所有持仓的详细信息。
    *   **返回值**: 一个字典，键为品种代码，值为**当前持仓数量** (`float`)。

### 获取资金数据

*   **`stra_get_fund_data(self, flag:int = 0) -> float`**
    *   **说明**: 获取账户层面的资金数据。
    *   **参数 `flag`**:\
        *   `0`: **动态权益** (可用资金 + 持仓市值，最常用于计算仓位)。\
        *   `1`: 总平仓盈亏。\
        *   `2`: 总浮动盈亏。\
        *   `3`: 总手续费。
    *   **返回值**: 对应的资金数据。

### 获取价格数据

*   **`stra_get_price(self, stdCode:str) -> float`**
    *   **说明**: 获取指定品种的**最新市场价格**。\
    *   **参数**: `stdCode`: 品种代码。\
    *   **返回值**: 最新价格。

*   **`stra_get_bars(self, stdCode:str, period:str, count:int, isMain:bool = False)`**
    *   **说明**: 获取指定品种、周期和数量的历史 K 线数据。\
    *   **参数**:\
        *   `stdCode`: 品种代码。\
        *   `period`: 周期，如 "m5", "d1"。\
        *   `count`: 数量。\
        *   `isMain`: 是否是主 K 线周期。\
    *   **返回值**: `WtBars` 对象，包含 `opens`, `highs`, `lows`, `closes`, `volumes` 等 `numpy` 数组。可以通过 `df_bars.closes[-1]` 获取最新收盘价。

### 获取品种信息

*   **`stra_get_comminfo(self, stdCode:str)`**
    *   **说明**: 获取品种的基本信息，如交易单位(`volscale`)、价格精度等。\
    *   **参数**: `stdCode`: 品种代码。\
    *   **返回值**: 品种信息对象 (`CommodityInfo`)。如果找不到品种信息，可能返回 `None`。

### 交易执行接口

*   **`stra_set_position(self, stdCode:str, qty:float, usertag:str = "", limitprice:float = 0.0, stopprice:float = 0.0)`**
    *   **说明**: **(推荐)** 直接设置目标仓位数量。`wtpy` 会自动计算需要执行的操作（开/平/增/减/反手）。\
    *   **参数**:\
        *   `stdCode`: 品种代码。\
        *   `qty`: 目标持仓数量 (正数表示多头，负数表示空头，0 表示清仓)。\
        *   `usertag`: 用户标记。\
        *   `limitprice`, `stopprice`: 限价和止损价（通常在基本轮动中设为 0，表示市价指令）。\
    *   **注意事项**: 这是实现目标权重调整最简洁的方式。

*   **`stra_enter_long(self, stdCode:str, qty:float, usertag:str = "", limitprice:float = 0.0, stopprice:float = 0.0)`**
    *   **说明**: 多头进场。如果当前有空头持仓，会先平掉空头，再开多头到 `qty`。\
    *   **参数**: 同 `stra_set_position`，但 `qty` 必须为正数。

*   **`stra_enter_short(self, stdCode:str, qty:float, usertag:str = "", limitprice:float = 0.0, stopprice:float = 0.0)`**
    *   **说明**: 空头进场。如果当前有多头持仓，会先平掉多头，再开空头到 `qty`。\
    *   **参数**: 同 `stra_set_position`，但 `qty` 必须为正数 (表示开仓数量，方向由函数名决定)。

*   **`stra_exit_long(self, stdCode:str, qty:float, usertag:str = "", limitprice:float = 0.0, stopprice:float = 0.0)`**
    *   **说明**: 多头出场。只会减少多头持仓，如果持仓不足 `qty` 则全部平仓。不会反向开空。\
    *   **参数**: 同 `stra_set_position`，`qty` 为要平掉的数量。

*   **`stra_exit_short(self, stdCode:str, qty:float, usertag:str = "", limitprice:float = 0.0, stopprice:float = 0.0)`**
    *   **说明**: 空头出场。只会减少空头持仓，如果持仓不足 `qty` 则全部平仓。不会反向开多。\
    *   **参数**: 同 `stra_set_position`，`qty` 为要平掉的数量。

## 总结

对于 `MultiFactorsSel` 这类定期调仓至目标权重的策略：
1.  在 `__init__` 中接收 `initial_capital` 和 `isForStk`，并设置好其他参数（因子、调仓周期等）。
2.  在调仓逻辑中（如 `__rebalance_portfolio__`）：
    *   使用 `context.stra_get_fund_data(0)` 获取当前动态权益。
    *   确定 `selected_codes` 和 `weights`。
    *   使用 `context.stra_get_all_position()` 获取当前持仓。
    *   遍历所有涉及的品种 (`current_positions.keys()` 和 `selected_codes` 的并集)。
    *   对每个品种，计算其**目标持仓数量 `target_units`** (不在选中列表则为 0)，注意区分股票和合约的计算方式、价格有效性及交易单位。
    *   获取当前实际持仓 `current_pos`。
    *   **当 `target_units != current_pos` 时**，调用 `context.stra_set_position(code, target_units)` 来执行交易。
3.  调仓后更新 `self.__last_rebalance_date__`。

遵循这些步骤可以帮助你构建一个能够有效管理资金和执行交易的 `wtpy` 策略。