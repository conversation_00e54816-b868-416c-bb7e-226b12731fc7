# KLineChart/scripts 目录说明

## 交易通道执行架构与低频策略支持

### 1. 指令-执行分离架构
- 本系统采用“指令-执行分离”模式：
  - **接口模块**：仅负责接收交易指令（如买卖、调仓），将指令内容保存到指令文件（如 orders.csv），立即返回，不做实际下单。
  - **执行模块**：独立进程/线程，定期或实时扫描指令文件，读取新指令，并根据指令内容调用券商API、模拟撮合、人工通知等方式完成实际下单、状态更新等操作。
- 指令文件是两者的桥梁，需保证写入、读取的原子性和一致性。

### 2. 低频策略与多样执行方式
- 支持低频策略（如日内、日级别），执行模块可采用多种方式：
  - 自动API下单
  - 微信/邮箱等人工通知
  - 分批开仓、人工确认等
- 执行结果可能出现“实际成交数量 < 指令数量”的情况。

### 3. 卖出/平仓时的特殊处理
- 卖出指令不能简单假设“买了多少就能卖多少”，必须以**当前实际持仓为准**。
- 执行模块在处理卖出指令时，需先查询/同步当前实际持仓，再决定卖出数量，避免超卖或报错。
- 若指令数量大于实际持仓，只卖出实际持仓部分，其余部分自动忽略或记录为“未成交”，不阻碍策略主流程。

### 4. 业务应对原则
- 一切以“实际持仓”为准，灵活应对执行不定性，保证策略主流程不被阻碍。
- 指令状态可标记为“部分成交”或“部分失败”，但不阻塞后续策略执行。
- 所有状态流转、日志、通知都应以“实际成交”为准，保持策略流程顺畅。

## 5. 执行模块详细规划与重试机制

### 5.1 核心职责
- 定期扫描指令文件，发现新指令（买入、卖出、调仓等）。
- 自动优先执行：能自动下单的，优先自动完成（如API直连券商）。
- 失败指令管理：对执行失败的指令，记录失败原因，进入“待重试”队列。
- 弹性重试机制：失败指令不立即死循环重试，而是根据策略周期、市场时段等智能安排重试时机。
- 状态同步与日志：所有指令的执行、重试、最终状态都要有详细日志和状态标记，便于追踪和人工干预。

### 5.2 指令队列与状态管理
- 指令文件（如 orders.csv）需包含如下字段：
  - id、symbol、action、qty、status（pending/done/failed/retry）、last_attempt_time、fail_reason、retry_count 等
- 执行模块每次扫描时，优先处理 status=pending 或 status=retry 且到达重试时间的指令。

### 5.3 自动执行优先
- 能自动下单的券商API，直接调用API下单，成功则标记为 done，失败则记录原因并进入 retry 队列。
- 需要人工确认的（如微信/邮箱通知），可发送通知并等待人工反馈，反馈后再更新状态。

### 5.4 失败重试机制
- 重试策略可配置（如每隔N分钟、下一个交易日开盘后、或自定义时间窗口）。
- 日线级别策略建议：收盘后生成指令，第二天开盘后自动重试未成交指令。
- 每次重试都要更新 last_attempt_time、retry_count，避免频繁无效重试。
- 超过最大重试次数后，标记为“人工干预”，并推送通知。

### 5.5 时间窗口与市场时段感知
- 执行模块需感知市场开盘/收盘时间，避免在非交易时段盲目重试。
- 可结合交易所日历、节假日等信息，智能安排重试。

### 5.6 日志与人工干预
- 所有指令的执行、重试、失败、最终状态都要有详细日志。
- 多次失败的指令可自动推送通知（如微信/邮箱）提醒人工介入。

### 5.7 典型执行流程（伪代码）

```python
# 仅为说明用，具体实现需结合实际系统

def execute_orders():
    orders = load_orders()
    now = datetime.now()
    for order in orders:
        if order.status in ['pending', 'retry'] and should_attempt(order, now):
            result = try_execute(order)
            if result.success:
                mark_done(order)
            else:
                order.retry_count += 1
                order.last_attempt_time = now
                order.fail_reason = result.reason
                if order.retry_count < MAX_RETRY:
                    order.status = 'retry'
                else:
                    order.status = 'failed'
                    notify_human(order)
    save_orders(orders)

def should_attempt(order, now):
    # 仅在交易时段内，且距离上次尝试已过最小间隔
    return is_market_open(now) and (order.last_attempt_time is None or (now - order.last_attempt_time).total_seconds() > MIN_RETRY_INTERVAL)
```

---

本目录下脚本如 `utils.js` 主要用于路径、版本等工具函数，与上述架构配合使用。 