# BaseCtaStrategy.py
# 兼容WT的策略基础类，所有策略需从该类派生。
# 用于实盘与回测环境的统一接口，便于策略代码零侵入切换。

class BaseCtaStrategy:
    '''
    兼容WT的策略基础类，所有策略需从该类派生。
    接口与WT官方一致，便于策略代码无缝切换。
    '''
    def __init__(self, name):
        self.__name__ = name
        self.timeframe = None  # 兼容引擎调度

    def name(self):
        return self.__name__

    def on_init(self, context):
        '''策略初始化，启动时调用'''
        pass

    def on_calculate(self, context):
        '''K线闭合时调用，核心计算模块'''
        pass

    def on_tick(self, context, stdCode, newTick):
        '''逐笔数据进来时调用'''
        pass

    def on_bar(self, context, stdCode, period, newBar):
        '''K线闭合时回调'''
        pass 