const { MarketType, ExchangeType, KLineInterval } = require('../shared_types/market')
const axios = require('axios');
const { readFileSync } = require('fs');
const Redis = require('ioredis');
const config = require('../config.json');
const moment = require('moment-timezone');  // 添加时区支持

// Redis配置
const redis = new Redis({
  host: config.redis?.host || 'localhost',
  port: config.redis?.port || 6379,
  password: config.redis?.password,
  db: config.redis?.db || 0,
  // 启用键事件通知
  enableNotifyKeyspaceEvents: true
});

// 创建一个单独的Redis客户端用于订阅
const redisSub = new Redis({
  host: config.redis?.host || 'localhost',
  port: config.redis?.port || 6379,
  password: config.redis?.password,
  db: config.redis?.db || 0
});

// 缓存过期时间配置(秒)
const CACHE_EXPIRE_TIME = {
  SYMBOLS: 24 * 60 * 60,  // 品种列表缓存1天
  KLINE: {
    [KLineInterval.MIN1]: 60,           // 1分钟K线缓存1分钟
    [KLineInterval.MIN5]: 5 * 60,       // 5分钟K线缓存5分钟
    [KLineInterval.MIN15]: 15 * 60,     // 15分钟K线缓存15分钟
    [KLineInterval.MIN30]: 30 * 60,     // 30分钟K线缓存30分钟
    [KLineInterval.HOUR1]: 60 * 60,     // 1小时K线缓存1小时
    [KLineInterval.HOUR4]: 4 * 60 * 60, // 4小时K线缓存4小时
    [KLineInterval.DAY1]: 24 * 60 * 60, // 日K线缓存1天
    [KLineInterval.WEEK1]: 7 * 24 * 60 * 60,    // 周K线缓存7天
    [KLineInterval.MONTH1]: 30 * 24 * 60 * 60   // 月K线缓存30天
  }
};

// 读取配置文件

const PYTHON_SERVICE_URL = `http://127.0.0.1:${config.python_service.port}`;

// 新浪期货API基础URL
const SINA_FUTURES_BASE_URL = `${config.sina_futures.url}`;

// 外部行情API基础URL
const EXTERNAL_MARKET_BASE_URL = `${config.external_service.url}`;
const EXTERNAL_MARKET_INSTRUMENTS_URL = `${EXTERNAL_MARKET_BASE_URL}/instruments`;
const EXTERNAL_MARKET_KLINE_URL = `${EXTERNAL_MARKET_BASE_URL}/kline`;

// 加密货币中文名称映射
const CRYPTO_NAME_MAP = {
  'BTC-USDT': '比特币',
  'ETH-USDT': '以太坊',
  'BNB-USDT': '币安币',
  'XRP-USDT': '瑞波币',
  'ADA-USDT': '艾达币',
  'DOGE-USDT': '狗狗币',
  'DOT-USDT': '波卡币',
  'UNI-USDT': '优尼币',
  'LTC-USDT': '莱特币',
  'LINK-USDT': '链链币'
};

// Helper to get the python server base URL from config
function getPythonTdxServerUrl() {
  const port = config.tdx_service?.port || 5003; // Use a specific config key or default
  const host = config.tdx_service?.host || '127.0.0.1';
  return `http://${host}:${port}`;
}

/**
 * 计算K线数据的过期时间（秒级时间戳）
 * @param {string} market - 市场类型
 * @param {string} interval - K线周期
 * @returns {number} 过期时间戳（秒）
 */
function calculateKLineExpireTime(market, interval) {
  const now = moment();
  let expireTime;

  // 根据市场类型选择时区
  const timezone = market === MarketType.CRYPTO ? 'UTC' : 'Asia/Shanghai';
  now.tz(timezone);

  // 对于加密货币使用UTC时间，其他使用北京时间
  switch(interval) {
    case KLineInterval.MIN1:
      // 下一分钟的开始
      expireTime = now.clone().add(1, 'minute').startOf('minute');
      break;
    case KLineInterval.MIN5:
      // 下一个5分钟的开始
      expireTime = now.clone().add(5 - (now.minute() % 5), 'minutes').startOf('minute');
      break;
    case KLineInterval.MIN15:
      // 下一个15分钟的开始
      expireTime = now.clone().add(15 - (now.minute() % 15), 'minutes').startOf('minute');
      break;
    case KLineInterval.MIN30:
      // 下一个30分钟的开始
      expireTime = now.clone().add(30 - (now.minute() % 30), 'minutes').startOf('minute');
      break;
    case KLineInterval.HOUR1:
      // 下一个小时的开始
      expireTime = now.clone().add(1, 'hour').startOf('hour');
      break;
    case KLineInterval.HOUR4:
      // 下一个4小时的开始
      expireTime = now.clone().add(4 - (now.hour() % 4), 'hours').startOf('hour');
      break;
    case KLineInterval.DAY1:
      if (market === MarketType.CRYPTO) {
        // 加密货币：UTC 下一天 00:00
        expireTime = now.clone().add(1, 'day').startOf('day');
      } else {
        // A股和期货：北京时间下一天 00:00
        expireTime = now.clone().add(1, 'day').startOf('day');
      }
      break;
    case KLineInterval.WEEK1:
      // 下周一 00:00
      expireTime = now.clone().add(1, 'week').startOf('isoWeek');
      break;
    case KLineInterval.MONTH1:
      // 下月1日 00:00
      expireTime = now.clone().add(1, 'month').startOf('month');
      break;
    default:
      // 默认使用日线的过期时间
      expireTime = now.clone().add(1, 'day').startOf('day');
  }

  // 转换为秒级时间戳
  return Math.floor(expireTime.valueOf() / 1000);
}

class MarketProvider {
  constructor() {
    this.klineSubscriptions = new Map()
    this.klineIntervals = new Map()
    this.preloadIntervals = new Map()  // 用于存储预加载定时器
    
    // 启动自动预取机制
    this.startAutoPreload();

    // 启动Redis键空间事件监听
    this.startRedisKeyspaceNotifications();
  }

  /**
   * 启动自动预取机制
   * @private
   */
  startAutoPreload() {
    //console.log('[K线预取] 启动自动预取机制');
    
    // 每分钟检查一次所有缓存的K线数据
    setInterval(async () => {
      try {
        // 获取所有缓存的key
        const keys = await redis.keys('market:kline:*');
        console.log(`[K线预取] 开始检查 ${keys.length} 个K线缓存`);

        for (const key of keys) {
          // 从key中解析出symbol、market和interval
          const [, , symbol, market, interval] = key.split(':');
          
          // 对每个key进行预取检查
          await this._preloadExpiringKLine(symbol, market, interval);
        }
      } catch (error) {
        console.error('[K线预取] 自动预取检查过程发生错误:', error);
      }
    }, 60000); // 每分钟执行一次
  }

  /**
   * 启动Redis键空间事件监听
   * @private
   */
  async startRedisKeyspaceNotifications() {
    try {
      // 配置Redis开启键空间通知
      await redis.config('SET', 'notify-keyspace-events', 'Ex');
      console.log('[K线预取] Redis键空间通知已启用');

      // 订阅过期事件通道
      const expirationChannel = '__keyevent@0__:expire';
      redisSub.subscribe(expirationChannel, (err) => {
        if (err) {
          console.error('[K线预取] 订阅Redis过期事件失败:', err);
          return;
        }
        console.log('[K线预取] 已订阅Redis过期事件通道');
      });

      // 监听过期事件
      redisSub.on('message', async (channel, key) => {
        // 只处理K线相关的键
        if (key.startsWith('market:kline:')) {
          try {
            // 从key中解析出symbol、market和interval
            const [, , symbol, market, interval] = key.split(':');
            console.log(`[K线预取] 收到键过期通知: ${key}`);

            // 获取TTL
            const ttl = await this._getTTL(key);
            if (ttl !== null && ttl < 300) { // 如果TTL小于5分钟
              await this._preloadExpiringKLine(symbol, market, interval);
            }
          } catch (error) {
            console.error('[K线预取] 处理过期通知时发生错误:', error);
          }
        }
      });

      // 定期检查所有K线缓存的TTL
      setInterval(async () => {
        try {
          // 使用SCAN命令替代KEYS
          let cursor = '0';
          do {
            // 使用SCAN命令获取键
            const [nextCursor, keys] = await redis.scan(
              cursor,
              'MATCH',
              'market:kline:*',
              'COUNT',
              1000
            );
            cursor = nextCursor;

            // 对每个键检查TTL
            for (const key of keys) {
              const ttl = await this._getTTL(key);
              if (ttl !== null && ttl < 300) { // 如果TTL小于5分钟
                const [, , symbol, market, interval] = key.split(':');
                await this._preloadExpiringKLine(symbol, market, interval);
              }
            }
          } while (cursor !== '0'); // 当cursor为0时表示遍历完成
        } catch (error) {
          console.error('[K线预取] 定期检查过程发生错误:', error);
        }
      }, 5 * 60 * 1000); // 每5分钟执行一次完整扫描
    } catch (error) {
      console.error('[K线预取] 启动Redis键空间通知失败:', error);
    }
  }

  /**
   * 获取缓存的TTL（剩余生存时间）
   * @param {string} key 缓存键
   * @returns {Promise<number|null>} TTL in seconds, null if key doesn't exist
   * @private
   */
  async _getTTL(key) {
    try {
      return await redis.ttl(key);
    } catch (error) {
      console.error('Error getting TTL:', error);
      return null;
    }
  }

  /**
   * 生成缓存key
   * @private
   */
  _generateCacheKey(type, ...args) {
    switch(type) {
      case 'symbols':
        return `market:symbols:${args[0]}`; // args[0] = market
      case 'kline':
        return `market:kline:${args[0]}:${args[1]}:${args[2]}`; // args = [symbol, market, interval]
      default:
        throw new Error(`Unknown cache key type: ${type}`);
    }
  }

  /**
   * 从缓存获取数据
   * @private
   */
  async _getFromCache(key) {
    try {
      const data = await redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`Error getting data from cache: ${error.message}`);
      return null;
    }
  }

  /**
   * 将数据存入缓存
   * @private
   */
  async _setToCache(key, data, expireTime) {
    try {
      await redis.set(key, JSON.stringify(data), 'EX', expireTime);
    } catch (error) {
      console.error(`Error setting data to cache: ${error.message}`);
    }
  }

  /**
   * 获取期货K线数据的URL
   * @param {string} symbol - 期货代码
   * @param {string} interval - K线周期
   * @returns {string} API URL
   */
  getFuturesKLineUrl(symbol, interval) {
    // 根据不同的时间间隔返回对应的URL
    switch (interval) {
      case KLineInterval.MIN5:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesMiniKLine5m?symbol=${symbol}`;
      case KLineInterval.MIN15:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesMiniKLine15m?symbol=${symbol}`;
      case KLineInterval.MIN30:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesMiniKLine30m?symbol=${symbol}`;
      case KLineInterval.HOUR1:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesMiniKLine60m?symbol=${symbol}`;
      case KLineInterval.DAY1:
        return `${SINA_FUTURES_BASE_URL}/IndexService.getInnerFuturesDailyKLine?symbol=${symbol}`;
      default:
        throw new Error(`Unsupported interval for futures: ${interval}`);
    }
  }

  /**
   * 转换新浪期货K线数据格式
   * @param {Array} data - 新浪返回的K线数据
   * @returns {Array} 标准化的K线数据
   */
  transformFuturesKLineData(data) {
    if (!Array.isArray(data)) {
      throw new Error('Invalid futures kline data format');
    }

    return data.map(item => {
      // 新浪期货K线数据格式：
      // [0]日期, [1]开盘价, [2]最高价, [3]最低价, [4]收盘价, [5]成交量
      return {
        time: new Date(item[0]).getTime() / 1000, // 转换为时间戳
        open: parseFloat(item[1]),
        high: parseFloat(item[2]),
        low: parseFloat(item[3]),
        close: parseFloat(item[4]),
        volume: parseFloat(item[5])
      };
    });
  }

  /**
   * 获取交易品种列表
   */
  async getSymbols(market) {
    try {
      // 生成缓存key
      const cacheKey = this._generateCacheKey('symbols', market);
      
      // 尝试从缓存获取
      const cachedData = await this._getFromCache(cacheKey);
      if (cachedData && cachedData.length > 0) {
        console.log(`[Cache Hit] Symbols for market: ${market}`);
        return cachedData;
      }

      console.log('Failed to get symbols for market: ', market, ' from cache: cachedData is ', cachedData);

      // 缓存未命中，从数据源获取
      const symbols = await this._fetchSymbolsFromSource(market);
      
      // 存入缓存
      await this._setToCache(cacheKey, symbols, CACHE_EXPIRE_TIME.SYMBOLS);
      
      console.log('Symbols from source: symbols length is ', symbols.length);

      return symbols;
    } catch (error) {
      console.error('Error in getSymbols:', error);
      return [];
    }
  }

  /**
   * 从数据源获取交易品种列表
   * @private
   */
  async _fetchSymbolsFromSource(market) {
    switch (market) {
      case MarketType.STOCK:
        // 调用 Python 服务获取股票列表
        const response = await axios.get(`${PYTHON_SERVICE_URL}/stock/list`);
        if (response.data.success) {
          return response.data.data;
        }
        throw new Error(response.data.error || 'Failed to fetch stock list');

      case MarketType.INDEX:
        // 返回指数列表（暂时使用静态数据）
        return [
          { code: '000001.SH', name: '上证指数', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          { code: '399001.SZ', name: '深证成指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '399006.SZ', name: '创业板指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '000016.SH', name: '上证50', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          { code: '000300.SH', name: '沪深300', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          { code: '000905.SH', name: '中证500', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          { code: '399005.SZ', name: '中小100', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '399673.SZ', name: '创业板50', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          // 新增上海市场指数
          { code: '000002.SH', name: 'A股指数', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          { code: '000003.SH', name: 'B股指数', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          { code: '000008.SH', name: '综合指数', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          { code: '000010.SH', name: '基金指数', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          { code: '000012.SH', name: '国债指数', market: MarketType.INDEX, exchange: ExchangeType.SSE },
          // 新增深圳市场指数
          { code: '399106.SZ', name: '深证综指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '399002.SZ', name: '深成指R', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '399003.SZ', name: '成份B指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '399004.SZ', name: '深证100R', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '399101.SZ', name: '中小综指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '399107.SZ', name: '深证A指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
          { code: '399108.SZ', name: '深证B指', market: MarketType.INDEX, exchange: ExchangeType.SZSE }
        ];

      case MarketType.FUTURE:
        // 调用 Python 服务获取期货列表
        const futureResponse = await axios.get(`${PYTHON_SERVICE_URL}/future/list`);
        if (futureResponse.data.success) {
          return futureResponse.data.data;
        }
        throw new Error(futureResponse.data.error || 'Failed to fetch future list');

      case MarketType.CRYPTO:
        try {
          // 调用加密货币API获取品种列表
          const response = await axios.get(EXTERNAL_MARKET_INSTRUMENTS_URL);
          if (response.data && Array.isArray(response.data.data)) {
            return response.data.data.map(symbol => ({
              code: symbol,
              name: CRYPTO_NAME_MAP[symbol] || `(${symbol})`, // 有中文名用中文名,没有的用括号包围代码
              market: MarketType.CRYPTO,
              exchange: ExchangeType.CRYPTOCURRENCY
            }));
          }
          throw new Error('Invalid crypto symbols data format');
        } catch (error) {
          console.error('Error fetching crypto symbols:', error);
          return [];
        }

      default:
        return [];
    }
  }

  /**
   * 获取美股列表
   * Fetches the US stock list from the Python backend API.
   * @returns {Promise<Array<import('../shared_types/market').SymbolInfo>>} A promise that resolves to an array of US stock symbols.
   */
  async getUsSymbols() {
    // IMPORTANT: Adjust this URL if needed
    const url = `${PYTHON_SERVICE_URL}/us/list`;
    console.log(`[MarketProvider] Fetching US symbols from: ${url}`);
    try {
      // Using axios as it's already imported
      const response = await axios.get(url, { timeout: 15000 });

      // Check response structure
      if (response && response.data && response.data.success && Array.isArray(response.data.data)) {
        console.log(`[MarketProvider] Successfully fetched ${response.data.data.length} US symbols.`);
        // Map data to ensure consistency
        return response.data.data.map(s => ({
          code: s.code,
          name: s.name,
          // Use market identifier returned by Python, or default
          market: s.market || 'US_STOCK', // Ensure consistency with marketHandler.js
          exchange: s.exchange,
        }));
      } else {
        const errorMsg = response?.data?.error || 'Invalid data structure received';
        console.error(`[MarketProvider] Failed to get valid US symbols data from ${url}:`, errorMsg);
        return [];
      }
    } catch (error) {
      const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
      console.error(`[MarketProvider] Error fetching US symbols from ${url}:`, errorMsg);
      return [];
    }
  }

  /**
   * 检查并预加载即将过期的K线数据
   * @param {string} symbol 交易品种代码
   * @param {string} market 市场类型
   * @param {string} interval K线周期
   * @private
   */
  async _preloadExpiringKLine(symbol, market, interval) {
    try {
      const cacheKey = this._generateCacheKey('kline', symbol, market, interval);
      
      //console.log(`[K线预取] 开始检查K线缓存状态: ${symbol} ${market} ${interval}`);
      
      // 获取缓存的TTL
      const ttl = await this._getTTL(cacheKey);
      //console.log(`[K线预取] 缓存TTL: ${ttl}秒`);
      
      // 如果TTL小于5分钟，预加载新数据
      if (ttl !== null && ttl < 300) {
        const expireTime = new Date(Date.now() + ttl * 1000);
        /*console.log(`[K线预取] 检测到K线即将过期:
          品种: ${symbol}
          市场: ${market}
          周期: ${interval}
          剩余时间: ${ttl}秒
          过期时间: ${expireTime.toLocaleString()}
          开始预加载...`);*/
        
        // 异步预加载，不等待结果
        this._fetchKLineFromSource(symbol, market, interval).then(async klineData => {
          /*console.log(`[K线预取] 数据获取成功:
            品种: ${symbol}
            数据长度: ${klineData.length}
            最新时间: ${new Date(klineData[klineData.length - 1].time * 1000).toLocaleString()}`);*/

          const expireTime = calculateKLineExpireTime(market, interval);
          const now = Math.floor(Date.now() / 1000);
          const newTTL = expireTime - now;

          if (newTTL > 0) {
            const newExpireTime = new Date(expireTime * 1000);
            /*console.log(`[K线预取] 更新缓存:
              品种: ${symbol}
              新TTL: ${newTTL}秒
              新过期时间: ${newExpireTime.toLocaleString()}`);*/

            await this._setToCache(cacheKey, klineData, newTTL);

            console.log(`[K线预取] 检测到K线即将过期: 品种: ${symbol} 市场: ${market} 周期: ${interval} 剩余时间: ${ttl}秒 过期时间: ${expireTime.toLocaleString()} 获取及缓存成功`);
          } else {
            console.log(`[K线预取] 跳过缓存更新，因为新TTL <= 0:
              品种: ${symbol}
              计算的TTL: ${newTTL}秒`);
          }
        }).catch(error => {
          console.error(`[K线预取] 预加载失败:
            品种: ${symbol}
            市场: ${market}
            周期: ${interval}
            错误: ${error.message}`);
        });
      } else {
        //console.log(`[K线预取] 无需预加载:
        //  品种: ${symbol}
        //  市场: ${market}
        //  周期: ${interval}
        //  当前TTL: ${ttl}秒`);
      }
    } catch (error) {
      console.error(`[K线预取] 检查过程发生错误:
        品种: ${symbol}
        市场: ${market}
        周期: ${interval}
        错误: ${error.message}
        堆栈: ${error.stack}`);
    }
  }

  /**
   * 获取K线历史数据
   */
  async getKLineHistory(symbol, market, exchange, interval, options = {}) {
    try {
      // 生成缓存key
      const cacheKey = this._generateCacheKey('kline', symbol, market, interval);
      
      // 尝试从缓存获取
      const cachedData = await this._getFromCache(cacheKey);
      if (cachedData) {
        console.log(`[Cache Hit] KLine for ${symbol} ${interval}`);
        return cachedData;
      }

      // 缓存未命中，从数据源获取
      const klineData = await this._fetchKLineFromSource(symbol, market, exchange, interval, options);

      if (klineData.length === 0) {
        throw new Error(`[从数据源获取K线数据失败] 品种: ${symbol} 市场: ${market} 周期: ${interval}`);
      }
      
      // 计算过期时间
      const expireTime = calculateKLineExpireTime(market, interval);
      const now = Math.floor(Date.now() / 1000);
      const ttl = expireTime - now;  // 计算剩余秒数

      // 存入缓存
      if (ttl > 0) {  // 只在有效期大于0时缓存，还需要显示最新的k线时间
        await this._setToCache(cacheKey, klineData, ttl);
        // 显示最新的K线时间
        const latestKline = klineData.length > 0 ? klineData[klineData.length - 1] : null;
        const latestTime = latestKline ? new Date(latestKline.time * 1000).toISOString() : 'N/A';
        console.log(`[Cached] Kline data from source: length=${klineData.length}, latest_time=${latestTime}, expires_at=${new Date(expireTime * 1000).toISOString()}`);
      } else {
        console.log(`[Skip Cache] Expire time ${new Date(expireTime * 1000).toISOString()} already passed`);
      }
      
      return klineData;
    } catch (error) {
      console.error('Error in getKLineHistory:', error);
      throw error;
    }
  }

  /**
   * 从数据源获取K线数据
   * @private
   */
  async _fetchKLineFromSource(symbol, market, exchange, interval, options) {
    switch (market) {
      case MarketType.STOCK:
      case MarketType.ETF:
      case MarketType.INDEX:
      case MarketType.FUTURE:
        // 将K线周期转换为标准格式
        let period;
        switch (interval) {
          case KLineInterval.MIN1:
            period = '1m';
            break;
          case KLineInterval.MIN5:
            period = '5m';
            break;
          case KLineInterval.MIN15:
            period = '15m';
            break;
          case KLineInterval.MIN30:
            period = '30m';
            break;
          case KLineInterval.HOUR1:
            period = '60m';
            break;
          case KLineInterval.HOUR4:
            period = '240m';
            break;
          case KLineInterval.DAY1:
            period = '1D';
            break;
          case KLineInterval.WEEK1:
            period = '1W';
            break;
          case KLineInterval.MONTH1:
            period = '1M';
            break;
          default:
            period = '1D'; // 默认使用日线
        }

        // 调用 Python 服务获取K线数据
        console.log(`Fetching kline data: symbol=${symbol}, period=${period}, market=${market}`);
        const endpoint = market === MarketType.INDEX ? '/index/kline' : market === MarketType.FUTURE ? '/future/kline' : '/stock/kline';
        const response = await axios.get(`${PYTHON_SERVICE_URL}${endpoint}`, {
          params: {
            symbol: symbol,
            period: period,
            exchange: exchange,
            market: market,
            adjust: market === MarketType.STOCK ? 'qfq' : undefined  // 指数数据不需要复权
          }
        });
        
        if (response.data.success) {
          return response.data.data;
        }
        throw new Error(response.data.error || 'Failed to fetch stock kline data');

      /*case MarketType.FUTURE:
        try {
          // 获取期货K线数据
          const url = this.getFuturesKLineUrl(symbol, interval);
          console.log(`Fetching futures kline data: ${url}`);
          
          const response = await axios.get(url);
          if (!response.data) {
            throw new Error('Empty response from Sina futures API');
          }

          // 转换数据格式
          return this.transformFuturesKLineData(response.data);
        } catch (error) {
          console.error('Error fetching futures data:', error);
          throw new Error(`Failed to fetch futures data: ${error.message}`);
        }*/

      case MarketType.CRYPTO:
        try {
          // 构建K线请求参数
          const params = {
            instrument: symbol,
            interval: interval
          };

          // 调用外部行情API获取K线数据
          const response = await axios.get(EXTERNAL_MARKET_KLINE_URL, { params });
          
          if (response.data && Array.isArray(response.data.data)) {
            console.log('[数据提供者] 获取到K线数据：', response.data.data.length);
            return response.data.data.map(item => ({
              time: parseInt(item[0]) / 1000,  // 转换为秒级时间戳
              open: parseFloat(item[1]),
              high: parseFloat(item[2]),
              low: parseFloat(item[3]),
              close: parseFloat(item[4]),
              volume: parseFloat(item[5]),
              volCcy: parseFloat(item[6]),     // 成交额
              volCcyQuote: parseFloat(item[7]) // 报价币种成交额
            }));
          }
          throw new Error('Invalid crypto kline data format');
        } catch (error) {
          console.error('Error fetching crypto kline data:', error);
          throw error;  // 向上传递错误
        }

      default:
        return [];
    }
  }

  /**
   * 订阅K线数据更新
   */
  subscribeKLine(symbol, interval, callback) {
    const key = `${symbol}_${interval}`
    
    // 保存订阅回调
    if (!this.klineSubscriptions.has(key)) {
      this.klineSubscriptions.set(key, new Set())
    }
    this.klineSubscriptions.get(key).add(callback)
    
    // 如果这个周期还没有在轮询，开始轮询
    if (!this.klineIntervals.has(key)) {
      const intervalMs = this.getIntervalMilliseconds(interval)
      const timerId = setInterval(async () => {
        try {
          const kline = await this.getLatestKLine(symbol, interval)
          const callbacks = this.klineSubscriptions.get(key)
          if (callbacks) {
            callbacks.forEach(cb => cb(kline))
          }
        } catch (error) {
          console.error(`Error fetching kline for ${symbol}:`, error)
        }
      }, intervalMs)
      
      this.klineIntervals.set(key, timerId)
    }
    
    // 返回取消订阅的函数
    return () => {
      const callbacks = this.klineSubscriptions.get(key)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          // 如果没有订阅者了，停止轮询
          const timerId = this.klineIntervals.get(key)
          if (timerId) {
            clearInterval(timerId)
            this.klineIntervals.delete(key)
          }
          this.klineSubscriptions.delete(key)
        }
      }
    }
  }

  /**
   * 获取最新K线数据
   */
  async getLatestKLine(symbol, interval) {
    // TODO: 实现实际的数据获取逻辑
    return {
      time: Date.now() / 1000,
      open: 0,
      high: 0,
      low: 0,
      close: 0,
      volume: 0
    }
  }

  /**
   * 将K线周期转换为毫秒数
   */
  getIntervalMilliseconds(interval) {
    const unit = interval.slice(-1)
    const value = parseInt(interval.slice(0, -1))
    
    switch (unit) {
      case 'm':
        return value * 60 * 1000
      case 'h':
        return value * 60 * 60 * 1000
      case 'd':
        return value * 24 * 60 * 60 * 1000
      case 'w':
        return value * 7 * 24 * 60 * 60 * 1000
      case 'M':
        return value * 30 * 24 * 60 * 60 * 1000
      default:
        throw new Error(`Unknown interval unit: ${unit}`)
    }
  }

  /**
   * 获取实时K线数据 (Modified to use local Python TDX server with string market)
   * @param {string} code - 股票代码 (e.g., '600000.SH')
   * @param {string} period - K线周期 (e.g., '1m', '5m', '1D')
   * @param {string} exchange - 交易所 (e.g., 'SSE', 'SZSE')
   * @param {number} count - 获取的K线数量
   * @returns {Promise<Array>} - K线数据数组 in format [{time, open, high, low, close, volume}]
   */
  async getRealTimeKLines(code, period, exchange, count = 500) {
    try {
      console.log(`[MarketProvider] 获取实时K线数据 (via Python): ${code} (${exchange}) ${period}`);

      const pythonServerBaseUrl = getPythonTdxServerUrl();
      if (!pythonServerBaseUrl) {
        throw new Error("Python TDX server URL not configured correctly.");
      }

      // --- Map exchange to Python market string ('sz' or 'sh') ---
      let pythonMarketStr = null;
      const upperExchange = exchange?.toUpperCase();
      if (upperExchange === 'SZSE') {
        pythonMarketStr = 'sz';
      } else if (upperExchange === 'SSE') {
        pythonMarketStr = 'sh';
      }

      if (pythonMarketStr === null) {
        throw new Error(`Unsupported exchange for Python TDX request: ${exchange}`);
      }
      // ---------------------------------------------------------

      // Prepare code for Python service (strip suffix)
      const codeWithoutSuffix = code.split('.')[0];

      // Prepare period (pass the string 'day', '5m' directly)
      const pythonPeriod = period;

      const url = `${pythonServerBaseUrl}/kline`;
      const params = {
        market: pythonMarketStr, // Use the mapped string 'sz' or 'sh'
        code: codeWithoutSuffix,
        period: pythonPeriod,
        count: count // Fetch a reasonable number of bars
      };

      console.log(`[MarketProvider] Calling Python TDX server: ${url} with params:`, params);

      const response = await axios.get(url, {
        params: params,
        timeout: 15000,
        headers: {
          'User-Agent': 'QuantQuart-MarketProvider/1.0'
        }
      });

      // Check for Python server application error
      if (response.data && response.data.error) {
        throw new Error(`Python TDX server error: ${response.data.error}`);
      }

      // Data should already be in the desired format [{time, open, high, low, close, volume}]
      if (response.status === 200 && Array.isArray(response.data)) {
        console.log(`[MarketProvider] Python TDX server success for ${code} ${period}, bars: ${response.data.length}`);
        // No need for formatKLineData if Python returns the correct format
        return response.data;
      } else {
        console.error(`[MarketProvider] 获取 Python TDX 实时K线数据失败: ${code} ${period}. Status: ${response.status}, Data type: ${typeof response.data}`);
        return [];
      }
    } catch (error) {
      const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
      console.error(`[MarketProvider] 获取 Python TDX 实时K线数据出错: ${code} ${period}`, errorMsg);
      // Add more context for debugging network errors
      if (error.request) {
        console.error(`[MarketProvider] Error details: No response received from ${getPythonTdxServerUrl()}`);
      } else if (!error.response) {
        console.error(`[MarketProvider] Error details: Request setup error: ${error.message}`);
      }
      return []; // Return empty array on error
    }
  }

  /**
   * 格式化K线数据 (Kept for potential future use, but likely not needed for Python TDX data)
   * @param {Array} rawData - 原始API返回的数据
   * @param {string} code - 股票代码
   * @param {string} period - K线周期
   * @returns {Array} - 格式化后的K线数据
   */
  formatKLineData(rawData, code, period) {
    // ... (implementation remains the same) ...
  }
}

// 创建单例实例
const marketProvider = new MarketProvider()

module.exports = {
  marketProvider
} 