import type { Symbol } from '@/shared_types/market';
import { MarketType, ExchangeType, KLineInterval } from '@/shared_types/market';

// 获取自选股列表
export const getWatchList = async (): Promise<Symbol[]> => {
  // TODO: 替换为实际的API调用
  return [
    { code: '000001.SH', name: '上证指数', market: MarketType.INDEX, exchange: ExchangeType.SSE },
    { code: '399001.SZ', name: '深证成指', market: MarketType.INDEX, exchange: ExchangeType.SZSE },
    { code: 'BTC/USDT', name: '比特币', market: MarketType.CRYPTO, exchange: ExchangeType.BINANCE },
    { code: 'ETH/USDT', name: '以太坊', market: MarketType.CRYPTO, exchange: ExchangeType.BINANCE },
  ];
};

// 获取周期列表
export const getPeriodList = () => {
  return [
    { label: '1分钟', value: KLineInterval.MIN1 },
    { label: '5分钟', value: KLineInterval.MIN5 },
    { label: '15分钟', value: KLineInterval.MIN15 },
    { label: '30分钟', value: KLineInterval.MIN30 },
    { label: '1小时', value: KLineInterval.HOUR1 },
    { label: '4小时', value: KLineInterval.HOUR4 },
    { label: '日线', value: KLineInterval.DAY1 },
    { label: '周线', value: KLineInterval.WEEK1 },
  ];
};