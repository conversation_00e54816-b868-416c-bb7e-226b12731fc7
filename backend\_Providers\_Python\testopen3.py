from openctp_ctp import MdApi

class MyMdSpi(MdApi):
    def __init__(self):
        super().__init__()
        
    def on_front_connected(self):
        print("行情服务器连接成功")
        # 登录请求（注意：行情服务器通常不验证密码）
        self.login(broker_id="9999", user_id="your_id", password="")
        
    def on_rsp_user_login(self, session_id, error_info):
        if error_info['ErrorID'] == 0:
            print("行情登录成功")
            # 订阅合约（示例：沪深300股指期货IF2106）
            self.subscribe_market_data(["IF2106"])
        else:
            print(f"登录失败: {error_info['ErrorMsg']}")

    def on_market_data(self, data):
        print(f"行情更新: {data['InstrumentID']} 最新价: {data['LastPrice']}")

# 创建实例并连接
md = MyMdSpi()
md.connect("tcp://114.80.104.195:61213")  # SimNow行情地址