const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { sequelize } = require('../database');
const { User } = require('../models');
const multer = require('multer');
const router = express.Router();
const path = require('path');
const nodemailer = require('nodemailer');
const config = require('../config.json');

// JWT密钥从配置文件获取
const JWT_SECRET = config.jwt.secret;

// 邮件发送配置从配置文件获取
const transporter = nodemailer.createTransport(config.email);

// 验证邮件配置
transporter.verify(function (error, success) {
  if (error) {
    console.error('[邮件服务] 配置验证失败:', error);
  } else {
    console.log('[邮件服务] 配置验证成功, 服务器已就绪');
  }
});

// JWT验证中间件
const authenticateToken = (req, res, next) => {

  console.log('[UserHandler 验证中间件] authenticateToken');

  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'No token provided'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        error: 'Invalid token'
      });
    }
    req.user = user;
    next();
  });
};

// 头像上传配置
const avatarUpload = multer({
  storage: multer.diskStorage({
    destination: './uploads/avatars/',
    filename: (req, file, cb) => {
      // 生成唯一文件名
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const filename = uniqueSuffix + path.extname(file.originalname);
      console.log('[UserHandler] 生成头像文件名:', filename);
      cb(null, filename);
    }
  }),
  limits: {
    fileSize: 2 * 1024 * 1024  // 2MB
  },
  fileFilter: (req, file, cb) => {
    console.log('[UserHandler] 检查头像文件类型:', file.mimetype);
    if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/png') {
      cb(null, true);
    } else {
      console.log('[UserHandler] 头像文件类型不支持:', file.mimetype);
      cb(new Error('只允许上传 JPG/PNG 格式的图片'));
    }
  }
});

// 在文件顶部定义 handleDatabaseError 函数
function handleDatabaseError(error) {
  if (error.name === 'SequelizeUniqueConstraintError') {
    const messages = error.errors.map(err => err.message);
    return {
      success: false,
      error: messages.join(', ') || '数据库错误'
    };
  } else if (error.name === 'SequelizeValidationError') {
    const messages = error.errors.map(err => err.message);
    return {
      success: false,
      error: messages.join(', ') || '验证错误'
    };
  }

  // 处理其他类型的错误
  return {
    success: false,
    error: '数据库操作失败'
  };
}

class UserHandler {
  constructor() {
    // 初始化路由
    this.router = router;
    this.setupRoutes();
    
    // 添加路由日志中间件
    this.router.use((req, res, next) => {
      console.log(`[UserHandler] ${req.method} ${req.path}`, {
        body: req.body,
        query: req.query,
        params: req.params,
        headers: {
          authorization: req.headers.authorization ? 'Bearer ...' : undefined,
          'content-type': req.headers['content-type']
        }
      });
      next();
    });
  }

  setupRoutes() {
    // 登录路由
    this.router.post('/login', this.handleLogin.bind(this));
    // 注册路由
    this.router.post('/register', this.handleRegister.bind(this));
    // 登出路由
    this.router.post('/logout', this.handleLogout.bind(this));
    // 获取用户信息
    this.router.get('/profile', authenticateToken, this.handleGetProfile.bind(this));
    // 更新用户信息
    this.router.post('/update', authenticateToken, this.handleUpdate.bind(this));
    // 上传头像
    this.router.post('/avatar', authenticateToken, avatarUpload.single('avatar'), this.handleUploadAvatar.bind(this));
    // 发送邮箱验证码
    this.router.post('/send-email', this.handleSendEmail.bind(this));
    // 新增：批准用户路由
    this.router.post('/approve', authenticateToken, this.handleApproveUser.bind(this));
    // 添加重置密码路由
    this.router.post('/reset-password', this.handleResetPassword.bind(this));
  }
  // 处理用户注册
  async handleRegister(req, res) {
    console.log('[UserHandler] Processing registration request:', {
      username: req.body.username,
      email: req.body.email
    });
    
    const { username, password, email } = req.body;

    try {
      // 检查用户是否已存在
      const existingUser = await User.findOne({ where: { username } });
      if (existingUser) {
        console.log('[UserHandler] 用户已经存在:', existingUser);
        return res.status(400).json({
          success: false,
          error: '用户已经存在'
        });
      }

      // 密码加密
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // 创建新用户
      const user = await User.create({
        username,
        password: hashedPassword,
        email,
        status: 'pending',
        createdat: new Date()
      });

      // 生成JWT token
      const token = jwt.sign(
        { id: user.id, username: user.username },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      // 返回成功响应
      res.status(201).json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          token
        }
      });

      console.log('[UserHandler] Registration successful:', {
        userId: user.id,
        username: user.username
      });

    } catch (error) {
      console.error('[UserHandler] Registration failed:', error);

      // 使用统一的错误处理函数
      const result = handleDatabaseError(error);
      return res.status(400).json(result);
    }
  }

  // 处理用户登录
  async handleLogin(req, res) {
    console.log('[UserHandler] Processing login request:', {
      credential: req.body.username
    });
    
    const { username, password } = req.body;
    const isEmail = /\S+@\S+\.\S+/.test(username); // 简单的邮箱格式验证

    try {
      // 查找用户（支持用户名或邮箱登录）
      let user;
      if (isEmail) {
        // 如果输入的是邮箱
        user = await User.findOne({ where: { email: username } });
      } else {
        // 如果输入的是用户名
        user = await User.findOne({ where: { username } });
      }

      if (!user) {
        return res.status(401).json({
          success: false,
          error: '用户名/邮箱或密码错误'
        });
      }

      // 验证密码
      const isValid = await user.comparePassword(password);
      if (!isValid) {
        return res.status(401).json({
          success: false,
          error: '用户名/邮箱或密码错误'
        });
      }

      // 生成 token
      const token = jwt.sign(
        { id: user.id, username: user.username },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      // 更新最后登录时间
      await user.update({
        lastLogin: new Date()
      });

      // 返回用户信息和token
      res.json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          status: user.status,
          role: user.role,
          token
        }
      });

      console.log('[UserHandler] Login successful:', {
        userId: user.id,
        username: user.username
      });

    } catch (error) {
      console.error('[UserHandler] Login failed:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // 处理用户登出
  async handleLogout(req, res) {
    console.log('[UserHandler] Processing logout request');
    // 由于使用JWT，服务器端不需要维护session
    // 客户端需要清除token
    res.json({
      success: true,
      message: 'Logged out successfully'
    });
    console.log('[UserHandler] Logout successful');
  }

  // 新增：获取用户信息的处理方法
  async handleGetProfile(req, res) {
    console.log('[UserHandler] Processing get profile request:', {
      userId: req.user.id
    });
    
    try {
      // 从JWT中获取用户ID
      const userId = req.user.id;

      // 查找用户信息（排除密码字段）
      const user = await User.findOne({
        where: { id: userId },
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // 返回用户信息
      res.json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          status: user.status,
          createdat: user.createdat,
          lastLogin: user.lastLogin
        }
      });

      console.log('[UserHandler] Profile retrieval successful:', {
        userId: user.id,
        username: user.username
      });

    } catch (error) {
      console.error('[UserHandler] Profile retrieval failed:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // 添加用户信息更新方法
  async handleUpdate(req, res) {
    try {
      const { avatar, password } = req.body;
      const userId = req.user.id;  // 从JWT token中获取用户ID
      const updateData = {};

      if (avatar) {
        updateData.avatar = avatar;
      }

      if (password) {
        const salt = await bcrypt.genSalt(10);
        updateData.password = await bcrypt.hash(password, salt);
      }

      await User.update(updateData, {
        where: { id: userId }
      });

      const updatedUser = await User.findByPk(userId);
      res.json({
        success: true,
        data: {
          id: updatedUser.id,
          username: updatedUser.username,
          email: updatedUser.email,
          avatar: updatedUser.avatar,
          token: req.token
        }
      });
    } catch (error) {
      console.error('Failed to update user:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update user info'
      });
    }
  }

  // 头像上传处理
  async handleUploadAvatar(req, res) {

    console.log('[UserHandler] Processing avatar upload request:');

    try {
      const file = req.file;
      if (!file) {
        console.log('[UserHandler] 头像上传失败: 没有收到文件');
        return res.status(400).json({
          success: false,
          error: '没有收到文件'
        });
      }

      console.log('[UserHandler] 收到头像文件:', {
        filename: file.filename,
        size: file.size,
        mimetype: file.mimetype
      });

      const userId = req.user.id;
      const avatarUrl = `/uploads/avatars/${file.filename}`;
      
      await User.update({ avatar: avatarUrl }, { where: { id: userId } });
      
      console.log('[UserHandler] 头像上传成功:', {
        userId,
        avatarUrl,
        filename: file.filename
      });

      res.json({
        success: true,
        url: avatarUrl
      });
    } catch (error) {
      console.error('[UserHandler] 头像上传失败:', {
        error: error.message,
        stack: error.stack
      });
      res.status(500).json({
        success: false,
        error: '头像上传失败: ' + error.message
      });
    }
  }

  // 处理发送邮箱验证码
  async handleSendEmail(req, res) {
    try {
      console.log('[UserHandler] 处理发送邮箱验证码请求:', {
        email: req.body.email,
        code: '****' // 隐藏验证码
      });

      const { email, code } = req.body;
      
      if (!email || !code) {
        return res.status(400).json({
          success: false,
          error: '邮箱和验证码不能为空'
        });
      }

      if (!config.email || !config.email.auth.user || !config.email.auth.pass) {
        console.error('[UserHandler] 邮箱配置未设置');
        return res.status(500).json({
          success: false,
          error: 'Email service not properly configured'
        });
      }

      // 邮件内容
      const mailOptions = {
        from: config.email.auth.user,
        to: email,
        subject: 'QuantQuart 验证码',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">QuantQuart 验证码</h2>
            <p>您好，</p>
            <p>感谢您使用 QuantQuart 服务。您的验证码是：</p>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; margin: 20px 0;">
              ${code}
            </div>
            <p>此验证码将在 15 分钟后失效。</p>
            <p>如果这不是您本人的操作，请忽略此邮件。</p>
            <p style="margin-top: 30px; color: #666;">
              此致<br>
              QuantQuart 团队
            </p>
          </div>
        `
      };

      // 发送邮件
      console.log('[UserHandler] 开始发送邮件');
      await transporter.sendMail(mailOptions);
      console.log('[UserHandler] 邮件发送成功');

      res.json({
        success: true,
        data: {
          message: '验证码已发送'
        }
      });

    } catch (error) {
      console.error('[UserHandler] 发送邮件失败:', {
        error: error.message,
        stack: error.stack
      });
      
      res.status(500).json({
        success: false,
        error: error.message || '发送验证码失败'
      });
    }
  }

  // 新增：处理用户批准的逻辑
  async handleApproveUser(req, res) {
    const { username } = req.body;

    console.log('[UserHandler] Processing approve user request:', {
      username
    });

    try {
      // 查找用户
      const user = await User.findOne({ where: { username } });
      if (!user) {
        return res.status(404).json({
          success: false,
          error: '用户未找到'
        });
      }

      // 更新用户状态为 'activate'
      user.status = 'active';
      await user.save();

      console.log('[UserHandler] 用户已批准:', {
        userId: user.id,
        username: user.username
      });

      res.json({
        success: true,
        message: '用户已批准'
      });
    } catch (error) {
      console.error('[UserHandler] 批准用户失败:', error);
      res.status(500).json({
        success: false,
        error: '批准用户失败'
      });
    }
  }

  /**
   * 处理重置密码请求
   * @param {Request} req - 请求对象
   * @param {Response} res - 响应对象
   */
  async handleResetPassword(req, res) {
    try {
      const { email, code, newPassword } = req.body;
      
      console.log('[UserHandler] 重置密码请求:', { email });

      // 验证参数
      if (!email || !code || !newPassword) {
        return res.status(400).json({
          success: false,
          error: '邮箱、验证码和新密码不能为空'
        });
      }

      // 从数据库查询用户
      const user = await User.findOne({
        where: { email }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          error: '用户不存在'
        });
      }

      // 验证验证码 (这里假设验证码是通过邮件发送的)
      // 注意：在生产环境中，应该从数据库或缓存中获取保存的验证码并进行比对
      // 此处使用的是前端传来的验证码，实际项目中可能需要进一步改进

      // 更新用户密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);
      
      await user.update({ password: hashedPassword });

      // 返回成功响应
      return res.json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email
        }
      });
    } catch (error) {
      console.error('[UserHandler] 重置密码失败:', error);
      
      // 使用统一的错误处理函数
      if (error.name && error.name.startsWith('Sequelize')) {
        const result = handleDatabaseError(error);
        return res.status(400).json(result);
      }
      
      return res.status(500).json({
        success: false,
        error: '重置密码失败：' + (error.message || '未知错误')
      });
    }
  }
}

// 创建用户处理器实例
const userHandler = new UserHandler();

// 导出路由和处理器实例
module.exports = {
  userRouter: userHandler.router,
  userHandler
};
