const jwt = require('jsonwebtoken');
const config = require('../config.json');

// JWT密钥从配置文件获取
const JWT_SECRET = config.jwt.secret;
const JWT_EXPIRES_IN = config.jwt.expiresIn;

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    console.log('[Auth] No token provided');
    return res.status(401).json({ error: 'No token provided' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      // 打印详细的错误信息
      console.log('[Auth] JWT Verification Failed:');
      console.log('Error Message:', err.message); // 错误的具体描述
      console.log('Error Name:', err.name); // 错误类型（如 TokenExpiredError、JsonWebTokenError 等）
      console.log('Token:', token); // 打印传入的 token
      console.log('JWT Secret:', JWT_SECRET); // 打印使用的 JWT 密钥
      console.log('Timestamp:', new Date().toISOString()); // 打印当前时间，用于检查 token 是否过期
  
      // 如果是 TokenExpiredError，打印过期时间
      if (err.name === 'TokenExpiredError') {
        console.log('Token Expired At:', err.expiredAt);
      }
  
      return res.status(403).json({ error: 'Invalid token' });
    }
  
    // 打印验证成功的信息
    console.log('[Auth] JWT Verification Successful:');
    console.log('Decoded User:', user); // 打印解码后的用户信息
    console.log('Token:', token); // 打印传入的 token
    console.log('JWT Secret:', JWT_SECRET); // 打印使用的 JWT 密钥
  
    req.user = user;
    next();
  });
};

module.exports = { authenticateToken }; 