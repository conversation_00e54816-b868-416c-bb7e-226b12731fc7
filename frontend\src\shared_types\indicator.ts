import { KLineData } from "./market";
import { IndicatorType } from "./market";

// 添加指标线类型定义
export enum IndicatorLineType {
    Line = 'line',
    Histogram = 'histogram'
  }
  
  // 添加指标线配置接口
export interface IndicatorLineConfig {
    name: string;
    type: IndicatorLineType;
    color?: string;
    lineWidth?: number;
  }
  
  
/**
 * 指标参数
 */
export interface IndicatorParam {
  name: string         // 参数名称
  type: 'number' | 'string' | 'boolean'  // 参数类型
  description: string  // 参数描述
  default?: any       // 默认值
}

/**
 * 指标定义
 */
export interface Indicator {
  id: string           // 指标ID
  name: string         // 指标名称
  type: IndicatorType       // 指标类型
  description: string  // 指标描述
  parameters: IndicatorParam[]  // 参数列表
  lines: IndicatorLineConfig[]  // 线型配置
  isMain: boolean  // 是否是主图指标
  createCalculator: (params: Record<string, number>) => any;  // 创建计算器
  calculate: (data: KLineData[], params: Record<string, any>, isLastBar: boolean) => number[]  // 计算函数
}
