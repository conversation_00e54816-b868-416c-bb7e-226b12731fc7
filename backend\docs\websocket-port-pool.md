# WebSocket端口池管理系统设计文档

## 1. 系统概述

WebSocket端口池管理系统是一个用于管理WebSocket连接端口的中央服务，它解决了多客户端需要使用不同WebSocket端口进行连接的问题。系统主要由以下部分组成：

- **端口池管理器**：负责分配和回收WebSocket端口
- **Socket.IO服务器**：在分配的端口上启动Socket.IO服务器
- **命名空间处理**：为不同命名空间设置相应的业务处理逻辑

## 2. 工作流程

### 2.1 端口分配流程

1. 客户端通过HTTP API请求获取可用的WebSocket端口
2. 端口池管理器从可用端口池中分配一个端口
3. 在该端口上启动Socket.IO服务器，并创建所有已注册的命名空间
4. 返回分配的端口号给客户端
5. 客户端使用该端口连接到Socket.IO服务器的特定命名空间

### 2.2 连接处理流程

1. 客户端连接到特定端口和命名空间（如`http://host:8001/realtime`）
2. Socket.IO服务器接受连接，并根据命名空间将连接路由到相应的事件处理器
3. 事件处理器处理客户端发送的事件（如订阅、取消订阅等）
4. 处理器可以向客户端发送数据或事件

### 2.3 端口回收流程

1. 客户端断开连接或长时间不活动
2. 端口池管理器检测到端口不再活跃
3. 关闭该端口上的Socket.IO服务器
4. 将端口标记为可用，可以被重新分配

## 3. 组件设计

### 3.1 端口池管理器 (socketHandler.js)

**职责**：
- 管理可用端口池
- 分配和回收端口
- 在分配的端口上启动Socket.IO服务器
- 为不同命名空间设置处理逻辑

**主要函数**：
- `getAvailablePort(username)`: 分配可用端口
- `releasePort(port, username)`: 释放端口
- `startWebSocketServer(port)`: 在端口上启动Socket.IO服务器
- `stopWebSocketServer(port)`: 停止端口上的Socket.IO服务器
- `cleanupInactivePorts()`: 清理不活跃的端口

### 3.2 业务处理器

#### 3.2.1 市场数据处理器 (marketHandler.js)

**职责**：
- 处理实时K线数据的订阅和发送
- 管理订阅关系
- 获取和处理市场数据

**主要函数**：
- `handleRealtimeSubscription(socket, data)`: 处理订阅请求
- `handleRealtimeUnsubscription(socket, data)`: 处理取消订阅请求
- `fetchAndSendRealtimeData(socket, symbol, period)`: 获取并发送实时数据

#### 3.2.2 交易处理器 (tradeHandler/index.js)

**职责**：
- 处理交易客户端的连接和通信
- 管理交易指令的发送和接收
- 处理交易状态更新

**主要函数**：
- `handleConnection(socket)`: 处理新连接
- `handleTradeCommand(socket, data)`: 处理交易指令
- `sendTradeStatus(socket, status)`: 发送交易状态更新

## 4. 命名空间处理

系统使用Socket.IO的命名空间功能来处理不同类型的连接：

| 命名空间 | 处理器 | 功能描述 |
|---------|-------|---------|
| `/realtime` | marketHandler | 处理实时K线数据 |
| `/trade` | tradeHandler | 处理交易指令和状态 |

当客户端连接到特定命名空间时，端口池管理器会直接调用相应处理器的方法处理连接事件。这种硬编码方式简单直接，易于理解和维护。

### 4.1 实时数据命名空间

实时数据命名空间(`/realtime`)处理实时K线数据的订阅和发送。当客户端连接到此命名空间时，可以：

- 发送`subscribe`事件订阅特定品种和周期的实时K线数据
- 发送`unsubscribe`事件取消订阅
- 接收`realtimeKline`事件获取实时K线数据

### 4.2 交易命名空间

交易命名空间(`/trade`)处理交易客户端的连接和通信。当客户端连接到此命名空间时，可以：

- 发送`register`事件注册交易客户端
- 发送`heartbeat`事件保持连接活跃
- 接收`execute_command`事件执行交易指令
- 发送`command_response`事件返回指令执行结果

## 5. API接口

### 5.1 获取可用端口

**请求**：
```
POST /api/socket/get_port
Headers: Authorization: Bearer <token>
```

**响应**：
```json
{
  "success": true,
  "port": 8001,
  "message": "成功分配WebSocket端口"
}
```

### 5.2 释放端口

**请求**：
```
POST /api/socket/release_port
Headers: Authorization: Bearer <token>
Body: { "port": 8001 }
```

**响应**：
```json
{
  "success": true,
  "message": "成功释放WebSocket端口"
}
```

## 6. 客户端使用示例

### 6.1 前端实时K线数据

```javascript
// 1. 获取可用端口
const response = await axios.post('/api/socket/get_port', {}, {
  headers: { 'Authorization': `Bearer ${token}` }
});

if (response.data.success) {
  const port = response.data.port;

  // 2. 连接到Socket.IO服务器的realtime命名空间
  const socket = io(`http://${host}:${port}/realtime`);

  // 3. 订阅实时数据
  socket.emit('subscribe', {
    symbol: { code: 'AAPL', market: 'STOCK' },
    period: '1m'
  });

  // 4. 接收实时数据
  socket.on('realtimeKline', (data) => {
    // 处理实时K线数据
    updateChart(data.klines);
  });

  // 5. 断开连接时释放端口
  window.addEventListener('beforeunload', async () => {
    socket.disconnect();
    await axios.post('/api/socket/release_port', { port }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
  });
}
```

### 6.2 交易客户端

```python
# 1. 获取可用端口
response = requests.post(
    f"{backend_url}/api/socket/get_port",
    headers={"Authorization": f"Bearer {token}"}
)

if response.json()["success"]:
    port = response.json()["port"]

    # 2. 连接到Socket.IO服务器的trade命名空间
    sio = socketio.Client()
    sio.connect(f"http://{host}:{port}/trade")

    # 3. 注册客户端
    sio.emit('register', {
        "username": username,
        "client_type": "easytrader_ths",
        "timestamp": int(time.time())
    })

    # 4. 处理交易指令
    @sio.on('execute_command')
    def on_execute_command(data):
        # 执行交易指令
        result = execute_command(data)
        sio.emit('command_response', result)

    # 5. 断开连接时释放端口
    def cleanup():
        sio.disconnect()
        requests.post(
            f"{backend_url}/api/socket/release_port",
            json={"port": port},
            headers={"Authorization": f"Bearer {token}"}
        )
```

## 7. 注意事项和最佳实践

1. **端口范围配置**：
   - 在`config.json`中配置适当的端口范围，避免与其他服务冲突
   - 建议使用较高的端口范围，如8000-8100

2. **安全性**：
   - 所有API请求都需要认证
   - 端口分配与用户关联，确保只有分配端口的用户才能释放它

3. **资源管理**：
   - 系统会自动清理长时间不活跃的端口（默认30分钟）
   - 客户端应在不再需要连接时主动释放端口

4. **错误处理**：
   - 客户端应处理端口分配失败的情况
   - 如果无法获取端口，可以尝试使用默认连接方式
