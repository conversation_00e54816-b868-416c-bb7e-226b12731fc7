import React, { lazy, Suspense } from 'react';
import { Row, Col, Spin } from 'antd';
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import {
  LineChartOutlined,
  FundOutlined,
  RocketOutlined,
  BarChartOutlined
} from '@ant-design/icons';

const { Statistic } = StatisticCard;

// 懒加载组件
const MainPage = lazy(() => import('../main'));
const LiveStrategyPanel = lazy(() => import('./components/LiveStrategyPanel'));

// Loading组件
const TabLoading = () => (
  <div style={{ padding: 24, textAlign: 'center' }}>
    <Spin size="large" />
  </div>
);

const DashboardContent: React.FC = () => {
  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '运行中的策略',
              value: 5,
              icon: <RocketOutlined />,
            }}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '今日收益',
              value: '+2.5%',
              icon: <LineChartOutlined />,
            }}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '总资产',
              value: '1,234,567',
              prefix: '¥',
              icon: <FundOutlined />,
            }}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '策略胜率',
              value: '78%',
              icon: <BarChartOutlined />,
            }}
          />
        </Col>
      </Row>

      <ProCard
        title="策略概览"
        style={{ marginTop: 16 }}
        tabs={{
          items: [
            {
              label: '策略研究',
              key: 'research',
              children: (
                <Suspense fallback={<TabLoading />}>
                  <MainPage />
                </Suspense>
              ),
            },
            {
              label: '实盘策略',
              key: 'live',
              children: (
                <Suspense fallback={<TabLoading />}>
                  <LiveStrategyPanel />
                </Suspense>
              ),
            },
            {
              label: '回测策略',
              key: 'backtest',
              children: '回测策略列表',
            },
          ],
        }}
      />

      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={12}>
          <ProCard title="收益走势">
            {/* 后续添加收益走势图表 */}
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard title="策略分析">
            {/* 后续添加策略分析内容 */}
          </ProCard>
        </Col>
      </Row>
    </>
  );
};

export default DashboardContent;