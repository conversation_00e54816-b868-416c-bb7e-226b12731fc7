.scroll-to-latest-btn {
  font-size: 18px; /* 增大图标尺寸，可按需调整 */
  // 添加 flex 布局确保图标在 Button 内完美居中
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.add-indicator-btn {
  // --- Default Appearance ---
  opacity: 0.6; // 默认 60% 不透明度
  // Inherit standard Ant Design button border, background, color

  // --- Icon Sizing & Centering ---
  font-size: 18px; // 图标大小
  display: flex !important; 
  align-items: center !important;
  justify-content: center !important;

  // --- Transitions ---
  transition: opacity 0.3s, background-color 0.3s, border-color 0.3s, color 0.3s;

  // --- Hover State ---
  &:hover {
    opacity: 0.95 !important; // 悬停时 95% 不透明度
    // No need to change color/border/background explicitly for this effect
  }
}

// 选股进度浮动窗口样式 - 顶部中间窄条
.stock-selection-progress-top {
  position: fixed;
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 6px;
  padding: 8px 12px;
  z-index: 1000;
  width: 400px;
  color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.3s ease-in-out;

  .progress-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
  }

  .progress-info-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .progress-summary {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
  }

  .progress-percentage {
    font-weight: bold;
    color: #87d068;
    font-size: 14px;
  }

  .progress-selected {
    color: #fff;
    opacity: 0.9;
  }

  .progress-bar-container {
    .ant-progress {
      margin: 0;
      
      .ant-progress-outer {
        height: 6px;
      }
      
      .ant-progress-inner {
        height: 6px;
        background-color: rgba(255, 255, 255, 0.2);
      }
      
      .ant-progress-bg {
        height: 6px;
        background: linear-gradient(90deg, #1890ff 0%, #87d068 100%);
      }
    }
  }

  .stop-button {
    flex-shrink: 0;
    
    .ant-btn {
      height: 28px;
      padding: 0 12px;
      font-size: 12px;
      border-radius: 4px;
    }
  }
}

// 保留原有的样式作为备用
.stock-selection-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 16px;
  z-index: 1000;
  width: 300px;
  color: #fff;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.3s ease-in-out;

  h3 {
    margin: 0 0 12px 0;
    color: #fff;
  }

  .progress-info {
    margin-bottom: 12px;
  }

  .progress-item {
    margin-bottom: 8px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// 指标面板按钮容器
.indicator-pane-buttons {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 2px;
  z-index: 10;
}

// 指标按钮基础样式
.indicator-button {
  width: 16px;
  height: 16px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color, #fff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 2px;
  cursor: pointer;
  opacity: 0.7;
  color: var(--text-color, #000);
  transition: opacity 0.3s, background-color 0.3s;

  &:hover {
    opacity: 1;
    background: var(--hover-color, #f0f0f0);
  }
}

// 主图按钮容器
.main-pane-button {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  z-index: 10;
}
