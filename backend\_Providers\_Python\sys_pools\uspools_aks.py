import akshare as ak
import pandas as pd
import json
import logging
import os
from typing import Optional, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Configuration ---
OUTPUT_DIR = "." # Save in the current directory (sys_pools)
# Use a more robust way to get the script's directory if needed:
# OUTPUT_DIR = os.path.dirname(os.path.abspath(__file__))


# --- Helper Functions ---

def format_us_symbol(code: str, name: str) -> Optional[Dict[str, str]]:
    """
    Formats a US stock code and name into the desired dictionary structure.
    Tries to infer the exchange from common suffixes used by some data sources
    (like .O for NASDAQ, .N for NYSE) or makes an educated guess.
    """
    code_upper = code.upper().strip()
    name_clean = name.strip()
    exchange = None

    # Check for common suffixes (adjust if akshare provides different ones)
    if '.' in code_upper:
        parts = code_upper.split('.')
        stock_code = parts[0]
        suffix = parts[-1]
        if suffix == 'O': # Often NASDAQ
            exchange = 'NASDAQ'
        elif suffix == 'N': # Often NYSE
            exchange = 'NYSE'
        # Add other suffix checks if needed (e.g., .P for NYSE Arca, .A for AMEX)
        else:
             logging.warning(f"Unrecognized suffix '{suffix}' for code: {code_upper}. Cannot determine exchange reliably.")
             stock_code = code_upper # Use full code if suffix unknown
    else:
        stock_code = code_upper
        # If no suffix, we have to guess or query more data.
        # For simplicity here, we'll rely on the calling function's default guess.
        # A more robust solution might involve another API call per stock.

    if not exchange:
        # Basic heuristic: shorter tickers often NYSE, longer often NASDAQ, but very unreliable
        # It's better to rely on the index source for a default guess.
        # We will let the calling function provide a default/guess.
         logging.debug(f"No exchange suffix found for {stock_code}. Default will be used.")
         # Let's assume the default exchange will be passed or handled later
         # For now, return None if we can't determine it here. A better approach
         # might be needed depending on how akshare returns data consistently.
         # --- Returning None if exchange cannot be guessed ---
         # return None # Strict approach
         # --- OR --- Allow default guess (handled in scrape_index) ---
         pass # Let the caller handle the default exchange assignment

    # If exchange was determined by suffix:
    if exchange:
        return {
            "symbol": f"{exchange}.STOCK.{stock_code}",
            "name": name_clean
        }

    # If exchange is still None, the caller (scrape_index) will assign a default
    return {
        "symbol": f"UNKNOWN.STOCK.{stock_code}", # Placeholder, will be replaced
        "name": name_clean,
        "code_only": stock_code # Store code separately for default assignment
    }


def save_to_json(data: List[Dict[str, str]], filename: str):
    """Save the list of stock data to a JSON file in the specified directory."""
    filepath = os.path.join(OUTPUT_DIR, filename)
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logging.info(f"Successfully saved {len(data)} stocks to {filepath}")
    except IOError as e:
        logging.error(f"Error saving data to {filepath}: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred while saving to {filepath}: {e}")

# --- Scraper Functions ---

def scrape_index(index_func, output_filename: str, default_exchange: str = 'NYSE'):
    """
    Generic function to scrape index constituents using an akshare function.

    Args:
        index_func: The akshare function to call (e.g., ak.index_dow jones_cons).
        output_filename: The name of the JSON file to save results.
        default_exchange: The exchange to assume if it cannot be inferred ('NYSE' or 'NASDAQ').
    """
    logging.info(f"Attempting to fetch constituents for {output_filename}...")
    try:
        # Execute the provided akshare function
        cons_df = index_func()

        if cons_df is None or cons_df.empty:
            logging.warning(f"No constituent data returned for {output_filename}.")
            return

        logging.info(f"Fetched {len(cons_df)} raw entries for {output_filename}.")
        # logging.debug(f"Columns available: {cons_df.columns.tolist()}")
        # logging.debug(cons_df.head())

        formatted_list = []
        # Adapt column names based on what akshare actually returns
        # Common names are '代码', '名称' or 'Symbol', 'Name', 'symbol', 'name'
        code_col = None
        name_col = None
        possible_code_cols = ['代码', 'symbol', 'Symbol', '代码 '] # Added space just in case
        possible_name_cols = ['名称', 'name', 'Name', '公司', '公司名称', '名称 ']

        for col in possible_code_cols:
            if col in cons_df.columns:
                code_col = col
                break
        for col in possible_name_cols:
            if col in cons_df.columns:
                name_col = col
                break

        if not code_col or not name_col:
            logging.error(f"Could not find required columns (code/name) in DataFrame for {output_filename}. Available columns: {cons_df.columns.tolist()}")
            return

        for _, row in cons_df.iterrows():
            code = str(row[code_col])
            name = str(row[name_col])

            formatted_data = format_us_symbol(code, name)

            if formatted_data:
                # If format_us_symbol couldn't determine exchange, assign default
                if "UNKNOWN.STOCK" in formatted_data["symbol"]:
                     logging.debug(f"Assigning default exchange '{default_exchange}' to {formatted_data['code_only']}")
                     formatted_data["symbol"] = f"{default_exchange}.STOCK.{formatted_data['code_only']}"
                     del formatted_data["code_only"] # Clean up temp field

                # Final check if symbol is validly formatted
                if "UNKNOWN.STOCK" not in formatted_data["symbol"]:
                     formatted_list.append(formatted_data)
                else:
                    logging.warning(f"Could not properly format symbol for code {code}, name {name}. Skipping.")


        if formatted_list:
            save_to_json(formatted_list, output_filename)
        else:
            logging.warning(f"No valid stocks could be formatted for {output_filename}. JSON file not saved.")

    except Exception as e:
        logging.error(f"Failed to fetch or process constituents for {output_filename}: {e}", exc_info=True)

# --- Main Execution ---

if __name__ == "__main__":
    logging.info("Starting US index constituents scrape using akshare...")

    # Ensure output directory exists
    if not os.path.exists(OUTPUT_DIR):
        try:
            os.makedirs(OUTPUT_DIR)
            logging.info(f"Created output directory: {OUTPUT_DIR}")
        except OSError as e:
            logging.error(f"Failed to create output directory {OUTPUT_DIR}: {e}")
            # Optionally exit if directory cannot be created
            # exit(1)


    # 1. Dow Jones Industrial Average (^DJI) - Default to NYSE
    scrape_index(lambda: ak.index_stock_cons(symbol="道琼斯"), "道琼斯工业平均指数.json", default_exchange='NYSE')

    # 2. S&P 500 Index (^SPX) - Default to NYSE (mixed, but NYSE dominant)
    scrape_index(lambda: ak.index_stock_cons(symbol="标普500"), "标普500指数.json", default_exchange='NYSE')

    # 3. NASDAQ 100 Index (^NDX) - Default to NASDAQ
    scrape_index(lambda: ak.index_stock_cons(symbol="纳斯达克100"), "纳斯达克100指数.json", default_exchange='NASDAQ')

    logging.info("US index constituents scrape finished.")