#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通达信数据到Wonder Trader DSB文件同步器
独立模块，负责检查和更新WT数据文件
"""

import os
import sys
import json
import pandas as pd
import datetime
import struct
import logging
from typing import Optional, Dict, Any, List

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入现有模块
from prepare_csv import load_config
# 配置日志
logging.basicConfig(level=logging.INFO, format='[%(asctime)s] [TdxWtSync] %(message)s')
logger = logging.getLogger(__name__)

# 导入pytz用于时区处理
import pytz

class TdxToWtSynchronizer:
    """通达信数据到Wonder Trader DSB文件同步器"""
    
    def __init__(self):
        """初始化同步器"""
        self.config = None
        self.tdx_data_root = None
        self.wt_storage_path = None
        self.wt_csv_path = None
        self.dt_helper = None
        
        # 加载配置
        self._load_config()
        
        # 初始化WtDataHelper
        self._init_wt_helper()
        
        # WT周期到内部格式的映射（参考unified_ext_data_loader.py）
        self.wt_period_map = {
            'd1': 'day',
            'day': 'day',
            'm5': 'min5',
            'min5': 'min5',
            'm1': 'min1',
            'min1': 'min1',
        }

        # 数据类型映射（参考tdx_to_dsb.py）
        self.data_type_map = {
            'day': {'tdx_subdir': 'lday', 'tdx_ext': '.day', 'wt_subdir': 'day', 'wt_period': 'd'},
            'min5': {'tdx_subdir': 'fzline', 'tdx_ext': '.lc5', 'wt_subdir': 'min5', 'wt_period': 'm5'},
            'min1': {'tdx_subdir': 'minline', 'tdx_ext': '.lc1', 'wt_subdir': 'min1', 'wt_period': 'm1'},
        }
        
        # 市场映射
        self.market_map = {
            'SSE': 'sh',      # 上海证券交易所
            'SZSE': 'sz',     # 深圳证券交易所
            'BSE': 'bj',      # 北京证券交易所
        }
    
    def _load_config(self):
        """加载配置文件"""
        try:
            self.config = load_config()
            if not self.config:
                raise ValueError("无法加载配置文件")
            
            # 获取TDX数据路径
            self.tdx_data_root = self.config.get('tdx_data', {}).get('path')
            if not self.tdx_data_root:
                raise ValueError("配置文件中缺少TDX数据路径 (tdx_data.path)")
            
            # 解析相对路径
            if not os.path.isabs(self.tdx_data_root):
                config_dir = os.path.dirname(os.path.abspath(__file__))
                self.tdx_data_root = os.path.abspath(os.path.join(config_dir, self.tdx_data_root))
            
            # 设置WT存储路径
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.wt_storage_path = os.path.join(script_dir, 'strategy', 'storage', 'his')
            self.wt_csv_path = os.path.join(script_dir, 'strategy', 'storage', 'csv')
            
            # 确保目录存在
            os.makedirs(self.wt_storage_path, exist_ok=True)
            os.makedirs(self.wt_csv_path, exist_ok=True)
            
            logger.info(f"配置加载成功")
            logger.info(f"  TDX数据路径: {self.tdx_data_root}")
            logger.info(f"  WT存储路径: {self.wt_storage_path}")
            logger.info(f"  CSV临时路径: {self.wt_csv_path}")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    def _init_wt_helper(self):
        """初始化WtDataHelper"""
        try:
            from wtpy.wrapper.WtDtHelper import WtDataHelper
            self.dt_helper = WtDataHelper()
            logger.info("WtDataHelper初始化成功")
        except ImportError as e:
            logger.error(f"无法导入WtDataHelper: {e}")
            self.dt_helper = None
        except Exception as e:
            logger.error(f"WtDataHelper初始化失败: {e}")
            self.dt_helper = None
    
    def parse_wt_code(self, std_code: str) -> Dict[str, str]:
        """
        解析Wonder Trader标准代码
        
        Args:
            std_code: 标准代码，如 SSE.ETF.513330
            
        Returns:
            包含exchange和symbol的字典
        """
        try:
            parts = std_code.split('.')
            if len(parts) >= 2:
                exchange = parts[0].upper()
                
                # 提取最后一部分作为symbol（数字代码）
                if len(parts) >= 3:
                    symbol = parts[-1]  # 如 SSE.ETF.513330 -> 513330
                else:
                    symbol = parts[1]   # 如 SSE.600000 -> 600000
                
                return {
                    'exchange': exchange,
                    'symbol': symbol,
                    'tdx_market': self.market_map.get(exchange, exchange.lower()),
                    'full_code': std_code
                }
            else:
                raise ValueError(f"无效的标准代码格式: {std_code}")
                
        except Exception as e:
            logger.error(f"解析代码失败 {std_code}: {e}")
            return {}
    
    def get_file_paths(self, std_code: str, period: str) -> Dict[str, str]:
        """
        获取相关文件路径

        Args:
            std_code: Wonder Trader标准代码
            period: 数据周期

        Returns:
            包含各种文件路径的字典
        """
        try:
            # 解析代码
            code_info = self.parse_wt_code(std_code)
            if not code_info:
                return {}

            exchange = code_info['exchange']
            symbol = code_info['symbol']
            tdx_market = code_info['tdx_market']

            # 转换WT周期到内部格式
            internal_period = self.wt_period_map.get(period.lower())
            if not internal_period:
                logger.warning(f"不支持的WT周期: {period}")
                return {}

            # 获取数据类型信息
            type_info = self.data_type_map.get(internal_period)
            if not type_info:
                logger.warning(f"不支持的内部周期: {internal_period}")
                return {}

            # 检查必要的路径是否已设置
            if not self.tdx_data_root or not self.wt_csv_path or not self.wt_storage_path:
                logger.error("必要的路径未设置")
                return {}

            # 构建通达信文件路径（参考tdx_to_dsb.py的方式）
            tdx_file = os.path.join(
                self.tdx_data_root,
                tdx_market,
                type_info['tdx_subdir'],
                f"{tdx_market}{symbol}{type_info['tdx_ext']}"
            )

            # 构建CSV和DSB文件路径
            csv_file = os.path.join(
                self.wt_csv_path,
                tdx_market,
                type_info['wt_subdir'],
                f"{symbol}.csv"
            )

            dsb_file = os.path.join(
                self.wt_storage_path,
                type_info['wt_subdir'],
                exchange,
                f"{symbol}.dsb"
            )
            
            return {
                'tdx_file': tdx_file,
                'csv_file': csv_file,
                'dsb_file': dsb_file,
                'csv_dir': os.path.dirname(csv_file),
                'dsb_dir': os.path.dirname(dsb_file),
                'period': period,
                'wt_period': type_info['wt_period'],
                'symbol': symbol,
                'exchange': exchange
            }
            
        except Exception as e:
            logger.error(f"获取文件路径失败 {std_code}/{period}: {e}")
            return {}
    
    def check_sync_needed(self, std_code: str, period: str) -> bool:
        """
        检查是否需要进行数据同步
        
        Args:
            std_code: Wonder Trader标准代码
            period: 数据周期
            
        Returns:
            True表示需要同步，False表示不需要
        """
        try:
            paths = self.get_file_paths(std_code, period)
            if not paths:
                return False
            
            tdx_file = paths['tdx_file']
            dsb_file = paths['dsb_file']
            
            # 检查TDX文件是否存在
            if not tdx_file or not os.path.exists(tdx_file):
                logger.debug(f"TDX文件不存在: {tdx_file}")
                return False
            
            # 检查DSB文件是否存在
            if not os.path.exists(dsb_file):
                logger.info(f"DSB文件不存在，需要同步: {dsb_file}")
                return True
            
            # 比较文件时间
            tdx_mtime = os.path.getmtime(tdx_file)
            dsb_mtime = os.path.getmtime(dsb_file)
            
            if tdx_mtime > dsb_mtime + 1:  # 1秒缓冲
                logger.info(f"TDX文件更新，需要同步: {std_code}/{period}")
                logger.debug(f"  TDX时间: {datetime.datetime.fromtimestamp(tdx_mtime)}")
                logger.debug(f"  DSB时间: {datetime.datetime.fromtimestamp(dsb_mtime)}")
                return True
            
            logger.debug(f"DSB文件已是最新: {std_code}/{period}")
            return False
            
        except Exception as e:
            logger.error(f"检查同步需求失败 {std_code}/{period}: {e}")
            return False
    
    def sync_data(self, std_code: str, period: str) -> bool:
        """
        执行通达信数据到DSB的同步
        
        Args:
            std_code: Wonder Trader标准代码
            period: 数据周期
            
        Returns:
            同步成功返回True，失败返回False
        """
        if not self.dt_helper:
            logger.error("WtDataHelper不可用，无法同步")
            return False
        
        try:
            paths = self.get_file_paths(std_code, period)
            if not paths:
                return False
            
            # 确保目录存在
            os.makedirs(paths['csv_dir'], exist_ok=True)
            os.makedirs(paths['dsb_dir'], exist_ok=True)
            
            # 第一步：TDX → CSV
            if not self._convert_tdx_to_csv(paths):
                return False
            
            # 第二步：CSV → DSB
            if not self._convert_csv_to_dsb(paths):
                return False
            
            logger.info(f"同步成功: {std_code}/{period}")
            return True
            
        except Exception as e:
            logger.error(f"同步失败 {std_code}/{period}: {e}")
            return False
    
    def _convert_tdx_to_csv(self, paths: Dict[str, str]) -> bool:
        """TDX文件转CSV"""
        try:
            logger.info(f"开始TDX→CSV转换: {paths['tdx_file']}")

            period = paths['period']

            # 使用内置的TDX读取函数
            if period in ['d1', 'day']:
                klines = self._read_tdx_day_data(paths['tdx_file'])
            elif period in ['m5', 'min5']:
                klines = self._read_tdx_min5_data(paths['tdx_file'])
            else:
                logger.error(f"不支持的周期: {period}")
                return False
            
            if not klines:
                logger.warning(f"没有读取到数据: {paths['tdx_file']}")
                return False
            
            # 转换为DataFrame并保存CSV
            df = pd.DataFrame(klines)
            required_cols = ['time', 'open', 'high', 'low', 'close', 'volume', 'amount']
            
            if not all(col in df.columns for col in required_cols):
                logger.error(f"数据缺少必要列: {df.columns}")
                return False
            
            # 确保数据类型正确
            df = df[required_cols]
            df['time'] = df['time'].astype(int)
            
            # 保存CSV文件
            df.to_csv(paths['csv_file'], index=False, encoding='utf-8')
            logger.info(f"CSV保存成功: {paths['csv_file']}, 共{len(df)}条记录")
            
            return True
            
        except Exception as e:
            logger.error(f"TDX→CSV转换失败: {e}")
            return False
    
    def _convert_csv_to_dsb(self, paths: Dict[str, str]) -> bool:
        """CSV文件转DSB"""
        try:
            logger.info(f"开始CSV→DSB转换: {paths['csv_dir']}")

            # 检查WtDataHelper是否可用
            if not self.dt_helper:
                logger.error("WtDataHelper不可用")
                return False

            # 使用WtDataHelper进行转换
            self.dt_helper.trans_csv_bars(
                csvFolder=paths['csv_dir'],
                binFolder=paths['dsb_dir'],
                period=paths['wt_period']
            )

            logger.info(f"CSV→DSB转换完成")
            return True

        except Exception as e:
            logger.error(f"CSV→DSB转换失败: {e}")
            return False

    def _read_tdx_day_data(self, file_path: str) -> List[Dict]:
        """读取通达信日线数据 (.day)"""
        if not os.path.exists(file_path):
            return []

        klines = []
        try:
            with open(file_path, 'rb') as f:
                buffer = f.read()
                record_size = 32
                record_count = len(buffer) // record_size

                for i in range(record_count):
                    pos = i * record_size
                    if pos + record_size > len(buffer):
                        break

                    date_value = int.from_bytes(buffer[pos:pos+4], byteorder='little')
                    year = date_value // 10000
                    month = (date_value % 10000) // 100
                    day = date_value % 100

                    # 基本日期验证
                    if not (1 <= month <= 12 and 1 <= day <= 31):
                        continue

                    date_str = f"{year:04d}-{month:02d}-{day:02d}"
                    try:
                        dt_aware = pd.Timestamp(date_str).tz_localize('Asia/Shanghai')
                        timestamp = int(dt_aware.timestamp())
                    except Exception:
                        continue

                    # 价格格式检测
                    try:
                        open_raw = int.from_bytes(buffer[pos+4:pos+8], byteorder='little')
                        is_int_format = abs(open_raw) < 100000000
                    except struct.error:
                        is_int_format = False

                    try:
                        if is_int_format:  # 股票数据
                            open_price = open_raw / 100.0
                            high_price = int.from_bytes(buffer[pos+8:pos+12], byteorder='little') / 100.0
                            low_price = int.from_bytes(buffer[pos+12:pos+16], byteorder='little') / 100.0
                            close_price = int.from_bytes(buffer[pos+16:pos+20], byteorder='little') / 100.0
                            amount = float(int.from_bytes(buffer[pos+20:pos+24], byteorder='little'))
                            volume = float(int.from_bytes(buffer[pos+24:pos+28], byteorder='little'))
                        else:  # 期货数据
                            open_price = struct.unpack('<f', buffer[pos+4:pos+8])[0]
                            high_price = struct.unpack('<f', buffer[pos+8:pos+12])[0]
                            low_price = struct.unpack('<f', buffer[pos+12:pos+16])[0]
                            close_price = struct.unpack('<f', buffer[pos+16:pos+20])[0]
                            amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
                            volume = struct.unpack('<f', buffer[pos+24:pos+28])[0]
                    except struct.error:
                        continue

                    klines.append({
                        'time': timestamp,
                        'open': float(open_price),
                        'high': float(high_price),
                        'low': float(low_price),
                        'close': float(close_price),
                        'volume': float(volume),
                        'amount': float(amount)
                    })
        except Exception as e:
            logger.error(f"读取日线数据失败 {file_path}: {e}")
            return []

        return klines

    def _read_tdx_min5_data(self, file_path: str) -> List[Dict]:
        """读取通达信5分钟线数据 (.lc5)"""
        if not os.path.exists(file_path):
            return []

        klines = []
        try:
            with open(file_path, 'rb') as f:
                buffer = f.read()
                record_size = 32
                record_count = len(buffer) // record_size

                for i in range(record_count):
                    pos = i * record_size
                    if pos + record_size > len(buffer):
                        break

                    date_time_bytes = buffer[pos:pos+4]
                    date_part = int.from_bytes(date_time_bytes[0:2], byteorder='little')
                    time_part = int.from_bytes(date_time_bytes[2:4], byteorder='little')

                    year = (date_part // 2048) + 2004
                    month = (date_part % 2048) // 100
                    day = (date_part % 2048) % 100
                    hour = time_part // 60
                    minute = time_part % 60

                    if not (1 <= month <= 12 and 1 <= day <= 31 and 0 <= hour <= 23 and 0 <= minute <= 59):
                        continue

                    try:
                        dt_naive = datetime.datetime(year, month, day, hour, minute)
                        dt_aware = pytz.timezone('Asia/Shanghai').localize(dt_naive)
                        timestamp = int(dt_aware.timestamp())

                        # 价格数据（分钟数据为浮点格式）
                        open_price = struct.unpack('<f', buffer[pos+4:pos+8])[0]
                        high_price = struct.unpack('<f', buffer[pos+8:pos+12])[0]
                        low_price = struct.unpack('<f', buffer[pos+12:pos+16])[0]
                        close_price = struct.unpack('<f', buffer[pos+16:pos+20])[0]
                        amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
                        volume = struct.unpack('<f', buffer[pos+24:pos+28])[0]

                        klines.append({
                            'time': timestamp,
                            'open': float(open_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'close': float(close_price),
                            'volume': float(volume),
                            'amount': float(amount)
                        })

                    except Exception:
                        continue

        except Exception as e:
            logger.error(f"读取5分钟数据失败 {file_path}: {e}")
            return []

        return klines


# 全局实例
_synchronizer = None

def get_synchronizer():
    """获取同步器实例"""
    global _synchronizer
    if _synchronizer is None:
        _synchronizer = TdxToWtSynchronizer()
    return _synchronizer

def ensure_wt_data_updated(std_code: str, period: str) -> bool:
    """
    确保Wonder Trader数据是最新的（便捷函数）
    
    Args:
        std_code: Wonder Trader标准代码
        period: 数据周期
        
    Returns:
        数据准备成功返回True
    """
    try:
        sync = get_synchronizer()
        
        if sync.check_sync_needed(std_code, period):
            return sync.sync_data(std_code, period)
        else:
            return True  # 数据已是最新
            
    except Exception as e:
        logger.error(f"处理失败 {std_code}/{period}: {e}")
        return False

if __name__ == "__main__":
    # 测试代码
    test_cases = [
        ("SSE.ETF.513330", "d1"),
        ("SSE.STK.600000", "d1"),
        ("SZSE.STK.000001", "m5"),
    ]
    
    print("=" * 60)
    print("测试通达信到WT数据同步")
    print("=" * 60)
    
    for std_code, period in test_cases:
        print(f"\n测试: {std_code} ({period})")
        try:
            result = ensure_wt_data_updated(std_code, period)
            if result:
                print(f"  ✅ 同步成功")
            else:
                print(f"  ❌ 同步失败")
        except Exception as e:
            print(f"  💥 异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
