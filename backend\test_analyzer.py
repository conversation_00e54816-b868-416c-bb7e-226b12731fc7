# backend/test_analyzer.py

import os

import sys

import json

import traceback

import pandas as pd # 导入 pandas 用于设置显示选项


# --- 将 _Providers/_Python 添加到 sys.path ---

# 假设此脚本是从 'backend' 目录运行的

provider_dir = os.path.abspath("_Providers/_Python")

if provider_dir not in sys.path:

    sys.path.insert(0, provider_dir)

print(f"[*] 添加到 sys.path: {provider_dir}")


try:

    # 使用调整后的路径导入函数

    # 需要确保 strategy/portfolio 结构在 sys.path 下可访问

    from strategy.portfolio.portfolio_analyzer import analyze_backtest_results

    print("[*] 成功导入 analyze_backtest_results.")

except ImportError as e:

    print(f"[!] 导入 analyze_backtest_results 出错: {e}")

    print(f"[!] 当前 sys.path: {sys.path}")

    print("[!] 请确保你正从 'backend' 目录运行此脚本。")

    sys.exit(1)

except Exception as e:

    print(f"[!] 导入过程中发生未知错误: {e}")

    sys.exit(1)


def test_analyzer(strategy_id='etf_trend_rotation'):
    """

    测试指定策略的回测结果分析器。


    Args:

        strategy_id (str): 要测试的策略名称。
    """

    print(f"\n--- 开始测试策略 '{strategy_id}' 的分析器 ---")


    # --- 定义目标策略的输出目录 ---

    # 假设从 'backend' 目录运行

    output_dir = os.path.abspath(os.path.join("_Providers", "_Python", "strategy", "portfolio", "outputs_bt", strategy_id))

    print(f"[*] 目标输出目录: {output_dir}")


    if not os.path.isdir(output_dir):

        print(f"[!] 错误: 输出目录未找到: {output_dir}")

        print(f"[!] 请确保策略 '{strategy_id}' 的回测已经运行并生成了输出文件。")
        return


    try:

        # --- 调用分析器函数 ---

        print(f"\n[*] 正在调用 analyze_backtest_results...")

        # 设置 pandas 显示选项，以便在 analyzer 内部打印完整 DataFrame (如果需要)

        pd.set_option('display.max_rows', None)

        pd.set_option('display.max_columns', None)

        pd.set_option('display.width', 1000)

        results = analyze_backtest_results(output_dir)


        # --- 打印结果 ---

        print("\n--- 分析结果 ---")

        if isinstance(results, dict) and "error" in results:

            print(f"[!] 分析函数返回错误: {results['error']}")

        elif isinstance(results, dict):

            # 格式化打印整个结果字典

            print(json.dumps(results, indent=2, ensure_ascii=False))


            # 单独提取并打印开始日期和缩略图数据开头

            start_date = results.get('backtest_period_start', 'N/A')

            print(f"\n[*] === 从结果提取的回测开始日期: {start_date} ===")


            print(f"[*] === 缩略图数据 (前 5 条): ===")

            thumbnail_data = results.get('equity_curve_thumbnail_data', [])

            if thumbnail_data:

                print(thumbnail_data[:5])

            else:

                print("    (未找到或为空)")


        else:

            print(f"[!] 分析器返回了预料之外的类型: {type(results)}")
            print(results)


    except Exception as e:

        print(f"[!] 分析过程中发生未知错误: {e}")

        print(traceback.format_exc())


if __name__ == '__main__':

    # 你可以在这里修改要测试的策略名称

    test_analyzer('etf_trend_rotation')

    print("\n--- 测试结束 ---")

