def calculate_unix_timestamp(year, month, day, hour, minute, second, utc_offset_hours):
    SECONDS_PER_DAY = 86400
    SECONDS_PER_HOUR = 3600
    SECONDS_PER_MINUTE = 60
    DAYS_PER_YEAR = 365
    DAYS_PER_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]

    epoch_year = 1970

    def is_leap_year(y):
        return y % 4 == 0 and (y % 100 != 0 or y % 400 == 0)

    leap_years = sum(1 for y in range(epoch_year, year) if is_leap_year(y))
    total_days = (year - epoch_year) * DAYS_PER_YEAR + leap_years

    months = DAYS_PER_MONTH.copy()
    if is_leap_year(year):
        months[1] = 29

    for m in range(month - 1):
        total_days += months[m]

    total_days += day  # 到当天的总天数

    total_seconds = total_days * SECONDS_PER_DAY  # 不减 1，直接计算到当天的 00:00:00 UTC

    local_seconds = (hour * SECONDS_PER_HOUR) + (minute * SECONDS_PER_MINUTE) + second
    utc_seconds = local_seconds - (utc_offset_hours * SECONDS_PER_HOUR)

    timestamp = total_seconds + utc_seconds
    return timestamp

# 测试
timestamp = calculate_unix_timestamp(2024, 12, 9, 9, 55, 0, 0)
print(f"北京时间 2025-02-28 15:00:00 的 Unix 时间戳: {timestamp}")