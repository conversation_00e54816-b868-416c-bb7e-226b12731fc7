#!/bin/bash
set -e # 如果任何命令失败，立即退出脚本

# --- 配置 ---
# 脚本所在的目录 (backend/_Providers/_Python/)
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
# 项目根目录 (上一级目录)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
# 虚拟环境目录名
VENV_NAME="venv"
# 虚拟环境完整路径
VENV_DIR="$SCRIPT_DIR/$VENV_NAME"
# requirements 文件路径
REQUIREMENTS_FILE="$SCRIPT_DIR/requirements.txt"
# 尝试查找 Python 3 可执行文件
PYTHON_EXEC="python3"
if ! command -v $PYTHON_EXEC &> /dev/null; then
    echo "未找到 python3 命令，尝试使用 python..."
    PYTHON_EXEC="python"
    if ! command -v $PYTHON_EXEC &> /dev/null; then
        echo "错误：系统中未找到 python3 或 python 命令。"
        echo "请先安装 Python 3 (推荐 3.8 或更高版本)。"
        exit 1
    fi
fi
echo "找到 Python 可执行文件: $($PYTHON_EXEC --version)"

# --- 检查或创建虚拟环境 ---
echo "检查虚拟环境目录: $VENV_DIR"
if [ ! -d "$VENV_DIR" ]; then
    echo "虚拟环境目录不存在，正在创建..."
    "$PYTHON_EXEC" -m venv "$VENV_DIR"
    if [ $? -ne 0 ]; then
        echo "错误：创建虚拟环境失败。"
        exit 1
    fi
    echo "虚拟环境创建成功。"
else
    echo "虚拟环境已存在。"
fi

# --- 安装依赖 ---
# 直接使用虚拟环境中的 pip
VENV_PIP="$VENV_DIR/bin/pip"
if [ ! -f "$VENV_PIP" ]; then
    echo "错误：在虚拟环境中未找到 pip 可执行文件 ($VENV_PIP)。"
    echo "虚拟环境可能已损坏，请尝试删除 '$VENV_DIR' 目录后重新运行此脚本。"
    exit 1
fi

echo "开始安装依赖项从 $REQUIREMENTS_FILE ..."
"$VENV_PIP" install -r "$REQUIREMENTS_FILE"
if [ $? -ne 0 ]; then
    echo "错误：安装依赖项失败。"
    exit 1
fi
echo "依赖项安装成功！"

# --- 完成 ---
echo ""
echo "Python 环境设置完成。"
echo "现在你可以使用 './run_pyserver.sh {start|stop|restart|status}' 来管理 Python 服务了。"
echo "注意: run_pyserver.sh 会使用 gunicorn，它会自动使用当前环境(或你指定的解释器)。"
echo "这个 setup 脚本只需要运行一次，或者在 requirements.txt 文件更新后再次运行。"

exit 0