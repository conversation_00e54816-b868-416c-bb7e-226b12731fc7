import { atom, useAtom } from 'jotai';

export type ThemeType = 
  | 'light-default' 
  | 'light-blue' 
  | 'light-tech' 
  | 'dark-default' 
  | 'dark-blue' 
  | 'dark-tech';

// 创建主题原子
export const themeAtom = atom<ThemeType>('light-default');

// 创建主题 hook
export const useTheme = () => {
  const [theme, setTheme] = useAtom(themeAtom);

  const changeTheme = (newTheme: ThemeType) => {
    setTheme(newTheme);
    localStorage.setItem('app-theme', newTheme);
  };

  const isDarkMode = theme.startsWith('dark');

  return {
    theme,
    changeTheme,
    isDarkMode,
  };
};

// 初始化主题
const savedTheme = localStorage.getItem('app-theme') as ThemeType;
if (savedTheme && [
  'light-default',
  'light-blue',
  'light-tech',
  'dark-default',
  'dark-blue',
  'dark-tech'
].includes(savedTheme)) {
  themeAtom.init = savedTheme;
} 