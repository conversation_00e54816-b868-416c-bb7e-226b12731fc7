module.exports = (sequelize, DataTypes) => {
  const GroupPosition = sequelize.define('GroupPosition', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '持仓ID'
  },
  groupid: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'groups',
      key: 'groupid'
    },
    comment: '组合ID'
  },
  symbol: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '品种代码'
  },
  position: {
    type: DataTypes.DECIMAL(15, 4),
    allowNull: false,
    defaultValue: 0,
    comment: '持仓数量'
  },
  avgPrice: {
    type: DataTypes.DECIMAL(15, 4),
    allowNull: true,
    comment: '平均成本价'
  },
  marketValue: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: '市值'
  },
  unrealizedPnl: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
    comment: '浮动盈亏'
  },
  realizedPnl: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
    comment: '已实现盈亏'
  },
  lastUpdateTime: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '最后更新时间'
  }
}, {
  tableName: 'group_positions',
  timestamps: false,
  indexes: [
    {
      fields: ['groupid']
    },
    {
      fields: ['symbol']
    },
    {
      unique: true,
      fields: ['groupid', 'symbol']
    }
  ]
});

  // 定义关联关系
  GroupPosition.associate = function(models) {
    // 关联到组合
    GroupPosition.belongsTo(models.Group, {
      foreignKey: 'groupid',
      as: 'group'
    });
  };

  return GroupPosition;
};
