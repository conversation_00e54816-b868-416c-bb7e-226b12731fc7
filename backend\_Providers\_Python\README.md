# 通达信数据处理工具

这个项目提供了一组Python工具，用于读取和处理通达信(TDX)格式的股票数据。

## 功能特点

- 读取通达信股票代码表文件(shs.tnf, szs.tnf)，获取股票代码和名称
- 读取通达信日线数据文件(.day)，解析股票历史价格数据
- 支持批量处理多只股票数据
- 将通达信格式数据导出为CSV格式
- 提供命令行界面，易于使用

## 文件说明

- `tdxsymbols.py`: 读取通达信股票代码表
- `tdxreader1.py`: 读取通达信日线数据文件
- `tdx_main.py`: 主程序，整合了所有功能，提供命令行界面

## 安装依赖

本工具只依赖Python标准库，不需要安装额外的依赖包。

## 使用方法

### 通过主程序使用

#### 1. 列出股票代码

```bash
# 列出沪深两市股票代码
python tdx_main.py list

# 只列出沪市股票代码
python tdx_main.py list --market sh

# 只列出深市股票代码
python tdx_main.py list --market sz

# 显示更多股票代码
python tdx_main.py list --limit 50

# 指定通达信安装路径
python tdx_main.py list --tdx-path "D:\mypath\T0002\hq_cache"
```

#### 2. 读取单个股票数据

```bash
# 读取指定股票的数据
python tdx_main.py read --code 600000

# 指定市场类型
python tdx_main.py read --code 600000 --market sh

# 指定数据目录
python tdx_main.py read --code 600000 --data-dir "D:\mydata\stockdata"

# 导出为CSV文件
python tdx_main.py read --code 600000 --output "600000.csv"

# 显示更多数据条目
python tdx_main.py read --code 600000 --limit 50
```

#### 3. 批量处理股票数据

```bash
# 批量处理默认数量的股票数据
python tdx_main.py batch

# 只处理沪市股票
python tdx_main.py batch --market sh

# 只处理深市股票
python tdx_main.py batch --market sz

# 处理更多股票
python tdx_main.py batch --limit 50

# 指定数据目录和输出目录
python tdx_main.py batch --data-dir "D:\mydata\stockdata" --output-dir "D:\mydata\output"

# 指定通达信安装路径
python tdx_main.py batch --tdx-path "D:\mypath\T0002\hq_cache"
```

### 单独使用各模块

#### 使用tdxsymbols.py读取股票代码表

```bash
python tdxsymbols.py
```

#### 使用tdxreader1.py读取日线数据

```bash
# 使用默认路径
python tdxreader1.py

# 指定数据目录和输出目录
python tdxreader1.py "D:\mydata\stockdata" "D:\mydata\output"

# 读取单个股票
python tdxreader1.py "D:\mydata\stockdata" "D:\mydata\output" --single 600000

# 批量处理指定数量的股票
python tdxreader1.py "D:\mydata\stockdata" "D:\mydata\output" 20
```

## 数据格式说明

### 通达信股票代码表格式

通达信股票代码表(shs.tnf, szs.tnf)是二进制文件，主要包含以下信息：
- 股票代码: 如 600000
- 股票名称: 如 浦发银行

### 通达信日线数据格式

通达信日线数据(.day)是二进制文件，每条记录32字节，包含以下信息：
- 日期: 整数格式，如 20230501 表示 2023年5月1日
- 开盘价: 整数，实际值除以100
- 最高价: 整数，实际值除以100
- 最低价: 整数，实际值除以100
- 收盘价: 整数，实际值除以100
- 成交额: 浮点数
- 成交量: 手数，整数

### CSV导出格式

导出的CSV文件包含以下字段：
- date: 日期，格式为 YYYY-MM-DD
- code: 股票代码
- name: 股票名称
- open: 开盘价
- high: 最高价
- low: 最低价
- close: 收盘价
- amount: 成交额
- vol: 成交量

## 注意事项

1. 确保通达信软件安装路径正确，默认路径为 `D:\new_tdx\T0002\hq_cache`
2. 确保数据文件存在于指定路径
3. 导出目录会自动创建，无需提前手动创建

## 常见问题

1. **找不到股票代码表文件**
   - 检查通达信安装路径是否正确
   - 确认文件名是否为 shs.tnf 和 szs.tnf

2. **找不到股票数据文件**
   - 检查股票数据目录路径是否正确
   - 确认文件命名格式是否为 sh[股票代码].day 或 sz[股票代码].day

3. **导出CSV文件失败**
   - 确保有足够的磁盘空间
   - 确保对输出目录有写权限

# Python 后端服务启动说明

本文档说明如何设置和运行本项目中的 Python 后端服务 (`app.py` 和 `tdxserver.py`)。

## 服务简介

*   **`app.py`**: 主要的后端 Flask 应用，负责处理核心业务逻辑和提供 API 接口，包括从通达信本地数据文件读取历史 K 线、获取股票/期货列表等。
*   **`tdxserver.py`**: 一个独立的 Flask 应用，使用 `pytdx` 库连接本地运行的通达信客户端，提供实时的行情数据接口（如实时报价、实时 K 线等，具体功能需参考代码实现）。

## 先决条件

1.  **Python 环境**: 需要安装 Python 3 (推荐 3.8 或更高版本)。
2.  **通达信客户端 (tdxw.exe)**: `tdxserver.py` 需要本地运行的通达信客户端来获取实时行情数据。请确保在运行 `tdxserver.py` 服务前已启动通达信客户端并登录。
3.  **通达信数据文件**: `app.py` 需要读取通达信的本地数据文件 (日线、分钟线、代码列表等)。请确保 `config.json` 中 `tdx_symbols` 和 `tdx_data` 的路径配置正确，并且这些数据文件存在且可访问。

## 设置步骤

1.  **进入目录**: 打开终端，进入 `backend/_Providers/_Python/` 目录。

2.  **设置 Python 环境**: 运行环境设置脚本。
    ```bash
    chmod +x setup_python_env.sh
    ./setup_python_env.sh
    ```
    此脚本会自动：
    *   检查 Python 是否可用。
    *   在当前目录下创建名为 `venv` 的 Python 虚拟环境 (如果尚不存在)。
    *   激活虚拟环境并使用 `pip` 安装 `requirements.txt` 文件中列出的所有 Python 依赖库。

    **注意**: 此脚本通常只需要运行一次。如果在 `requirements.txt` 中添加或修改了依赖项，则需要重新运行此脚本。

## 运行服务

使用 `run_pyserver.sh` 脚本来管理这两个 Python 服务。

1.  **赋予执行权限 (首次)**:
    ```bash
    chmod +x run_pyserver.sh
    ```

2.  **启动服务**:
    ```bash
    ./run_pyserver.sh start
    ```
    此命令会使用 Gunicorn 在后台启动 `app.py` 和 `tdxserver.py`。

3.  **停止服务**:
    ```bash
    ./run_pyserver.sh stop
    ```

4.  **重启服务**:
    ```bash
    ./run_pyserver.sh restart
    ```

5.  **查看服务状态**:
    ```bash
    ./run_pyserver.sh status
    ```

## 配置文件

*   `config.json`: 配置 `app.py` 的服务地址、端口以及通达信数据文件的路径。
*   `tdx_servers.json`: 配置 `tdxserver.py` 连接通达信客户端的 IP 和端口。`tdxserver.py` 启动时会尝试自动检测并更新此文件，但如果自动检测失败，可能需要手动配置。
*   `requirements.txt`: 列出了运行这两个服务所需的 Python 库及其版本。

## 日志和 PID 文件

*   服务运行时，日志文件会保存在项目根目录下的 `run/logs/` 目录中。
*   服务的进程 ID (PID) 文件会保存在项目根目录下的 `run/pids/` 目录中。

## 开发模式

`run_pyserver.sh` 脚本默认配置了 Gunicorn 的 `--reload` 参数。这意味着在开发过程中，如果修改了 `app.py`、`tdxserver.py` 或它们导入的其他 Python 文件，Gunicorn 会自动重启对应的服务。这在开发时非常方便，但在生产环境中通常不推荐使用。 

# Python 后端服务启动说明

本文档说明如何设置和运行本项目中的 Python 后端服务 (`app.py` 和 `tdxserver.py`)。

## 服务简介

*   **`app.py`**: 主要的后端 Flask 应用，负责处理核心业务逻辑和提供 API 接口，包括从通达信本地数据文件读取历史 K 线、获取股票/期货列表等。
*   **`tdxserver.py`**: 一个独立的 Flask 应用，使用 `pytdx` 库连接本地运行的通达信客户端，提供实时的行情数据接口（如实时报价、实时 K 线等，具体功能需参考代码实现）。

## 先决条件

1.  **Python 环境**: 需要安装 Python 3 (推荐 3.8 或更高版本)。
2.  **通达信客户端 (tdxw.exe)**: `tdxserver.py` 需要本地运行的通达信客户端来获取实时行情数据。请确保在运行 `tdxserver.py` 服务前已启动通达信客户端并登录。
3.  **通达信数据文件**: `app.py` 需要读取通达信的本地数据文件 (日线、分钟线、代码列表等)。请确保 `config.json` 中 `tdx_symbols` 和 `tdx_data` 的路径配置正确，并且这些数据文件存在且可访问。

## 设置步骤

1.  **进入目录**: 打开终端，进入 `backend/_Providers/_Python/` 目录。

2.  **设置 Python 环境**: 运行环境设置脚本。
    ```bash
    chmod +x setup_python_env.sh
    ./setup_python_env.sh
    ```
    此脚本会自动：
    *   检查 Python 是否可用。
    *   在当前目录下创建名为 `venv` 的 Python 虚拟环境 (如果尚不存在)。
    *   激活虚拟环境并使用 `pip` 安装 `requirements.txt` 文件中列出的所有 Python 依赖库。

    **注意**: 此脚本通常只需要运行一次。如果在 `requirements.txt` 中添加或修改了依赖项，则需要重新运行此脚本。

## 运行服务

使用 `run_pyserver.sh` 脚本来管理这两个 Python 服务。

1.  **赋予执行权限 (首次)**:
    ```bash
    chmod +x run_pyserver.sh
    ```

2.  **启动服务**:
    ```bash
    ./run_pyserver.sh start
    ```
    此命令会使用 Gunicorn 在后台启动 `app.py` 和 `tdxserver.py`。

3.  **停止服务**:
    ```bash
    ./run_pyserver.sh stop
    ```

4.  **重启服务**:
    ```bash
    ./run_pyserver.sh restart
    ```

5.  **查看服务状态**:
    ```bash
    ./run_pyserver.sh status
    ```

## 配置文件

*   `config.json`: 配置 `app.py` 的服务地址、端口以及通达信数据文件的路径。
*   `tdx_servers.json`: 配置 `tdxserver.py` 连接通达信客户端的 IP 和端口。`tdxserver.py` 启动时会尝试自动检测并更新此文件，但如果自动检测失败，可能需要手动配置。
*   `requirements.txt`: 列出了运行这两个服务所需的 Python 库及其版本。

## 日志和 PID 文件

*   服务运行时，日志文件会保存在项目根目录下的 `run/logs/` 目录中。
*   服务的进程 ID (PID) 文件会保存在项目根目录下的 `run/pids/` 目录中。

## 开发模式

`run_pyserver.sh` 脚本默认配置了 Gunicorn 的 `--reload` 参数。这意味着在开发过程中，如果修改了 `app.py`、`tdxserver.py` 或它们导入的其他 Python 文件，Gunicorn 会自动重启对应的服务。这在开发时非常方便，但在生产环境中通常不推荐使用。