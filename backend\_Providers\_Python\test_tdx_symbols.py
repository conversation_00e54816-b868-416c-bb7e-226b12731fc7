#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json

def read_tdx_stock_list(tdx_symbols_path):
    """直接从通达信数据文件读取股票列表
    
    Args:
        tdx_symbols_path: 通达信符号文件的路径
        
    Returns:
        list: 股票列表
    """
    stocks = []
    sh_count = 0
    sz_count = 0
    
    # 处理上海股票列表
    try:
        sh_file_path = os.path.join(tdx_symbols_path, 'shs.tnf')
        print(f"读取上海股票列表: {sh_file_path}")
        
        if os.path.exists(sh_file_path):
            with open(sh_file_path, 'rb') as f:
                f.seek(50)  # 跳过文件头
                while True:
                    data = f.read(360)  # 360字节记录
                    if not data or len(data) < 360:
                        break
                        
                    try:
                        code = data[0:6].decode('utf-8').strip()  # 股票代码
                        name = data[30:48].decode('gbk', errors='ignore').strip()  # 从第31个字节开始读取股票名称
                        if code and name:
                            stocks.append({
                                'code': code,
                                'name': name,
                                'market': 'STOCK',
                                'exchange': 'SSE'
                            })
                            sh_count += 1
                    except UnicodeDecodeError:
                        continue  # 如果解码出错，跳过该记录
            
            print(f"成功读取上海股票列表，共 {sh_count} 只股票")
        else:
            print(f"上海股票列表文件不存在: {sh_file_path}")
    except Exception as e:
        print(f"读取上海股票列表出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 处理深圳股票列表
    try:
        sz_file_path = os.path.join(tdx_symbols_path, 'szs.tnf')
        print(f"读取深圳股票列表: {sz_file_path}")
        
        if os.path.exists(sz_file_path):
            with open(sz_file_path, 'rb') as f:
                f.seek(50)  # 跳过文件头
                while True:
                    data = f.read(360)  # 360字节记录
                    if not data or len(data) < 360:
                        break
                        
                    try:
                        code = data[0:6].decode('utf-8').strip()  # 股票代码
                        name = data[30:48].decode('gbk', errors='ignore').strip()  # 从第31个字节开始读取股票名称
                        if code and name:
                            stocks.append({
                                'code': code,
                                'name': name,
                                'market': 'STOCK',
                                'exchange': 'SZSE'
                            })
                            sz_count += 1
                    except UnicodeDecodeError:
                        continue  # 如果解码出错，跳过该记录
            
            print(f"成功读取深圳股票列表，共 {sz_count} 只股票")
        else:
            print(f"深圳股票列表文件不存在: {sz_file_path}")
    except Exception as e:
        print(f"读取深圳股票列表出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print(f"读取完成，共获取 {len(stocks)} 只股票")
    return stocks

def main():
    """主函数"""
    # 从命令行参数或配置文件获取路径
    if len(sys.argv) > 1:
        tdx_symbols_path = sys.argv[1]
    else:
        # 尝试从配置文件读取
        try:
            # 直接从当前目录或上级目录读取config.json
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, 'config.json')
            
            if not os.path.exists(config_path):
                # 尝试从父目录查找
                config_path = os.path.join(os.path.dirname(current_dir), 'config.json')
            
            if not os.path.exists(config_path):
                # 最后尝试从根目录查找
                config_path = 'D:/projects/quantquart/backend/_Providers/_Python/config.json'
            
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                tdx_symbols_path = config['tdx_symbols']['path']
                print(f"从配置文件读取路径: {tdx_symbols_path}")
            else:
                print("配置文件不存在，请提供通达信符号文件路径作为命令行参数")
                sys.exit(1)
        except Exception as e:
            print(f"读取配置文件出错: {str(e)}")
            import traceback
            traceback.print_exc()
            print("请提供通达信符号文件路径作为命令行参数")
            sys.exit(1)
    
    # 读取股票列表
    stocks = read_tdx_stock_list(tdx_symbols_path)
    
    # 打印前10只股票
    if stocks:
        print("\n前10只股票:")
        print("-" * 60)
        print(f"{'序号':<5}{'代码':<10}{'名称':<20}{'市场':<10}{'交易所':<10}")
        print("-" * 60)
        
        for i, stock in enumerate(stocks[:10]):
            print(f"{i+1:<5}{stock['code']:<10}{stock['name']:<20}{stock['market']:<10}{stock['exchange']:<10}")
    else:
        print("没有获取到任何股票数据")

if __name__ == "__main__":
    main()