import { EventBus } from '@/events/eventBus';
import { ChartEvents, IndicatorEvents, MarketEvents } from '@/events/events';
import { IndicatorType, KLineInterval, Symbol, ChartConfig } from '@/shared_types/market';
import { IndicatorLineConfig } from '@/shared_types/indicator';
import { IndicatorWrapper, INDICATORS } from './IndicatorWrapper';
import { getIndicatorInstances } from '@/store/global';
import httpDispatcher from '@/_Dispatchers/HttpDispatcher';

/**
 * 指标列表管理器
 * 负责指标列表的加载、保存和同步
 */
class IndicatorListManager {
  private initialized = false;

  /**
   * 初始化指标列表管理器
   */
  initialize(): void {
    if (this.initialized) {
      console.log('[IndicatorListManager] 已经初始化，跳过');
      return;
    }

    // 订阅指标相关事件
    this.subscribeToIndicatorEvents();

    this.initialized = true;
    console.log('[IndicatorListManager] 初始化完成');
  }

  /**
   * 订阅指标相关事件
   */
  private subscribeToIndicatorEvents(): void {
    // 监听指标添加事件
    EventBus.on(IndicatorEvents.Types.ADD_INDICATOR, (payload: any) => {
      console.log('[IndicatorListManager] 收到指标添加事件:', payload);
      this.saveIndicatorList();
    });

    // 监听指标删除事件
    EventBus.on(IndicatorEvents.Types.REMOVE_INDICATOR, (payload: any) => {
      console.log('[IndicatorListManager] 收到指标删除事件:', payload);
      this.saveIndicatorList();
    });

    // 监听指标参数编辑事件
    EventBus.on(IndicatorEvents.Types.INDICATOR_PARAMS_EDITED, (payload: any) => {
      console.log('[IndicatorListManager] 收到指标参数编辑事件:', payload);
      this.saveIndicatorList();
    });

    // 不再需要监听K线数据就绪事件来更新品种和周期
  }

  /**
   * 保存指标列表
   */
  saveIndicatorList(): void {

    try {
      // 获取当前指标实例
      const indicatorInstances = getIndicatorInstances();
      console.log('[IndicatorListManager] 获取到的指标实例:', indicatorInstances);

      if (!indicatorInstances || indicatorInstances.length === 0) {
        console.warn('[IndicatorListManager] 没有找到指标实例，尝试从图表中获取');
        // 这里可以添加从图表中获取指标的逻辑
      }

      // 分离主图和副图指标
      const mainIndicators = indicatorInstances
        .filter(indicator => {
          const isMain = indicator.getIsMain();
          console.log(`[IndicatorListManager] 指标 ${indicator.type} 是主图指标: ${isMain}`);
          return isMain;
        })
        .map(indicator => {
          const result = {
            type: indicator.type,
            params: indicator.getParams(),
            lines: indicator.getLineConfigs()
          };
          console.log(`[IndicatorListManager] 主图指标 ${indicator.type} 的配置:`, result);
          return result;
        });

      const subIndicators = indicatorInstances
        .filter(indicator => {
          const isMain = indicator.getIsMain();
          console.log(`[IndicatorListManager] 指标 ${indicator.type} 是副图指标: ${!isMain}`);
          return !isMain;
        })
        .map(indicator => {
          const result = {
            type: indicator.type,
            params: indicator.getParams(),
            lines: indicator.getLineConfigs()
          };
          console.log(`[IndicatorListManager] 副图指标 ${indicator.type} 的配置:`, result);
          return result;
        });

      // 创建指标数据
      const indicators = {
        main: mainIndicators,
        sub: subIndicators
      };

      // 创建图表配置（仅用于本地存储）
      const config: ChartConfig = {
        symbol: null, // 不再需要品种信息
        period: null, // 不再需要周期信息
        indicators
      };

      // 发送保存图表配置事件（本地存储）
      EventBus.emit(ChartEvents.Types.SAVE_CHART_CONFIG, {
        config
      });

      // 保存到后端
      this.saveIndicatorListToBackend(indicators);

      console.log('[IndicatorListManager] 指标列表保存成功:', indicators);
    } catch (error) {
      console.error('[IndicatorListManager] 保存指标列表失败:', error);
    }
  }

  /**
   * 保存指标列表到后端
   * @param indicators 指标数据
   */
  private async saveIndicatorListToBackend(
    indicators: any
  ): Promise<void> {
    try {
      // 发送请求到后端
      const response = await httpDispatcher.post('/chart/indicatorlist', {
        indicators
      });

      if (response.data.success) {
        console.log('[IndicatorListManager] 指标列表保存到后端成功');
      } else {
        console.error('[IndicatorListManager] 指标列表保存到后端失败:', response.data.error);
      }
    } catch (error) {
      console.error('[IndicatorListManager] 保存指标列表到后端出错:', error);
    }
  }

  /**
   * 加载指标列表
   */
  async loadIndicatorList(): Promise<void> {
    console.log('[IndicatorListManager] 加载指标列表');

    try {
      // 从后端加载指标列表
      await this.loadIndicatorListFromBackend();
    } catch (error) {
      console.error('[IndicatorListManager] 加载指标列表失败:', error);
    }
  }

  /**
   * 从后端加载指标列表
   */
  private async loadIndicatorListFromBackend(): Promise<void> {
    try {
      // 发送请求到后端
      const response = await httpDispatcher.get('/chart/indicatorlist');

      if (response.data.success && response.data.data && response.data.data.indicators) {
        // 创建图表配置
        const config: ChartConfig = {
          symbol: null, // 不再需要品种信息
          period: null, // 不再需要周期信息
          indicators: response.data.indicators
        };

        // 发送图表配置就绪事件
        EventBus.emit(ChartEvents.Types.CHART_CONFIG_READY, {
          config
        });

        console.log('[IndicatorListManager] 从后端加载指标列表成功:', response.data.indicators);
      } else {
        console.log('[IndicatorListManager] 后端没有找到指标列表数据');
      }
    } catch (error) {
      console.error('[IndicatorListManager] 从后端加载指标列表出错:', error);
    }
  }

  /**
   * 创建指标实例
   * @param type 指标类型
   * @param params 指标参数
   * @param lines 指标线配置
   * @returns 指标实例
   */
  createIndicator(
    type: IndicatorType,
    params: Record<string, any>,
    lines?: IndicatorLineConfig[]
  ): IndicatorWrapper | null {
    try {
      const indicatorDef = INDICATORS[type];
      if (!indicatorDef) {
        console.error(`[IndicatorListManager] 未找到指标类型: ${type}`);
        return null;
      }

      // 创建指标实例
      const indicator = new IndicatorWrapper(
        indicatorDef,
        null,
        params || indicatorDef.defaultParams
      );

      // 设置线配置
      if (lines) {
        indicator.setLineConfigs(lines);
      }

      return indicator;
    } catch (error) {
      console.error('[IndicatorListManager] 创建指标实例失败:', error);
      return null;
    }
  }
}

// 创建单例
const indicatorListManager = new IndicatorListManager();

export default indicatorListManager;
