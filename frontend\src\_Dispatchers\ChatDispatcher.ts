//import api from '../api';
import { EventBus } from '../events/eventBus';
import { ChatEvents } from '../events/events';
import { isLoggedIn } from '../utils/auth';
import { wsManager } from '../utils/WebSocketManager';
import httpDispatcher from './HttpDispatcher';

class ChatDispatcher {
  private static instance: ChatDispatcher | null = null; // 显式声明静态属性
  private currentSessionId: number | null = null;

  constructor() {
    if (ChatDispatcher.instance) {
      throw new Error('ChatDispatcher 已经是单例，请使用 getInstance() 方法获取实例。');
    }
    ChatDispatcher.instance = this;
    this.setupListeners();
    console.log('[ChatDispatcher] ChatDispatcher initialized');
  }

  private setupListeners() {
    // 获取默认会话
    EventBus.on(ChatEvents.Types.GET_DEFAULT_SESSION, async () => {
      // 如果已经有 sessionId 且用户已登录，就不需要再次获取
      if (this.currentSessionId !== null && isLoggedIn()) {
        console.log('[ChatDispatcher] Session already exists:', this.currentSessionId);
        EventBus.emit(ChatEvents.Types.SESSION_UPDATED, this.currentSessionId);
        return;
      }

      if (isLoggedIn()) {
        try {
          const { data } = await httpDispatcher.get<{ sessionId: number }>('/chat/session/default');
          this.currentSessionId = data.sessionId;
          EventBus.emit(ChatEvents.Types.SESSION_UPDATED, data.sessionId);
        } catch (error) {
          console.error('[ChatDispatcher] Failed to get default session:', error);
        }
      }
    });

    // 发送消息
    EventBus.on(ChatEvents.Types.SEND_MESSAGE, (message: ChatEvents.Message) => {
      if (!this.currentSessionId) {
        console.error('[ChatDispatcher] No active session');
        return;
      }

      wsManager.sendMessage({
        type: 'message',
        data: {
          ...message,
          sessionId: this.currentSessionId
        }
      });
    });

    // 文件上传
    EventBus.on(ChatEvents.Types.UPLOAD_FILE, async (file: File) => {
      if (!this.currentSessionId) {
        console.error('[ChatDispatcher] No active session');
        return;
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('sessionId', String(this.currentSessionId));

      try {
        // 开始上传
        const uploadStartEvent: ChatEvents.FileUpload = {
          fileName: file.name,
          sessionId: this.currentSessionId
        };
        EventBus.emit(ChatEvents.Types.UPLOAD_STARTED, uploadStartEvent);

        const { data } = await httpDispatcher.post<{ 
            url: string;
            fileSize: number;
        }>('/chat/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (uploadProgress: any) => {
            const progress = Math.round((uploadProgress.loaded * 100) / uploadProgress.total!);
            const progressData: ChatEvents.FileProgress = {
              fileName: file.name,
              sessionId: this.currentSessionId!,
              progress
            };
            EventBus.emit(ChatEvents.Types.UPLOAD_PROGRESS, progressData);
          }
        });

        // 上传完成
        const completedEvent: ChatEvents.FileCompleted = {
          fileName: file.name,
          sessionId: this.currentSessionId,
          url: data.url
        };
        EventBus.emit(ChatEvents.Types.UPLOAD_COMPLETED, completedEvent);

        // 发送文件消息
        const fileMessage: ChatEvents.FileUploaded = {
          url: data.url,
          name: file.name,
          fileSize: data.fileSize
        };
        EventBus.emit(ChatEvents.Types.FILE_UPLOADED, fileMessage);
      } catch (error) {
        console.error('[ChatDispatcher] File upload failed:', error);
        const failedEvent: ChatEvents.FileUpload = {
          fileName: file.name,
          sessionId: this.currentSessionId
        };
        EventBus.emit(ChatEvents.Types.UPLOAD_FAILED, failedEvent);
      }
    });

    // 在 SESSION_UPDATED 事件处理中建立连接
    EventBus.on(ChatEvents.Types.SESSION_UPDATED, (sessionId: number) => {
      console.log('[ChatDispatcher] Session updated:', sessionId, ' fetching chat history');
     
      wsManager.connect(sessionId);
      this.requestChatHistory(sessionId);
    });
  }

  private requestChatHistory(sessionId: number) {
    console.log('[ChatDispatcher] Requesting chat history for session:', sessionId);
    try {
      const message = {
        type: 'get_history',
        data: { sessionId }
      };
      wsManager.sendMessage(message);
      console.log('[ChatDispatcher] Chat history request sent:', message);
    } catch (error) {
      console.error('[ChatDispatcher] Failed to request chat history:', error);
      //EventBus.emit(ChatEventTypes.UPDATE_CHAT_HISTORY, []);
    }
  }
}

export const chatDispatcher = new ChatDispatcher(); 