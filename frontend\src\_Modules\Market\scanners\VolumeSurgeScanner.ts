import { MarketScanner, ScanResult } from '../index';
import { KLineData } from '@/shared_types/market';

/**
 * 成交量放大扫描器
 * 用于发现成交量突然放大的品种
 */
export class VolumeSurgeScanner implements MarketScanner {
  name = 'VolumeSurge';
  description = '扫描成交量突然放大的品种';
  
  private period: number;  // 计算周期
  private threshold: number;  // 放大倍数阈值

  /**
   * 构造函数
   * @param period - 计算周期，默认20天
   * @param threshold - 放大倍数阈值，默认3倍
   */
  constructor(period: number = 20, threshold: number = 3) {
    this.period = period;
    this.threshold = threshold;
  }

  /**
   * 计算平均成交量
   * @param data - K线数据
   * @param start - 起始位置
   * @param end - 结束位置
   */
  private calculateAverageVolume(data: KLineData[], start: number, end: number): number {
    let sum = 0;
    for (let i = start; i < end; i++) {
      sum += data[i].volume;
    }
    return sum / (end - start);
  }

  /**
   * 实现扫描方法
   * @param data - K线数据
   */
  scan(data: KLineData[]): ScanResult {
    if (data.length < this.period + 1) {
      return {
        symbol: '',
        market: '',
        score: 0,
        time: 0
      };
    }

    // 计算最近一根K线的成交量
    const currentVolume = data[data.length - 1].volume;
    
    // 计算前N天的平均成交量
    const avgVolume = this.calculateAverageVolume(
      data,
      data.length - this.period - 1,
      data.length - 1
    );

    // 计算放大倍数
    const ratio = currentVolume / avgVolume;
    
    // 计算分数（放大倍数相对于阈值的比例）
    const score = ratio / this.threshold;

    return {
      symbol: '',  // 由调用者填充
      market: '',  // 由调用者填充
      score: score,
      time: data[data.length - 1].time,
      data: {
        currentVolume,
        averageVolume: avgVolume,
        ratio
      }
    };
  }
} 