import os
import sys
import struct

def read_stock_codes(file_path):
    with open(file_path, 'rb') as f:
        # 读取文件头
        header = f.read(50)
        ip = header[:40].rstrip(b'\x00').decode('gbk', errors='ignore')  # 忽略无法解码的字节
        port = struct.unpack('>H', header[40:42])[0]
        date = struct.unpack('>I', header[42:46])[0]
        time = struct.unpack('>I', header[46:50])[0]

        # 读取数据体
        stocks = []
        while True:
            record = f.read(314)
            if not record:
                break
            # 解析股票代码和名称
            code = record[:6].rstrip(b'\x00').decode('gbk', errors='ignore')
            name = record[23:41].rstrip(b'\x00').decode('gbk', errors='ignore')
            # 解析昨收盘价
            last_close = struct.unpack('<f', record[276:280])[0]
            # 解析名称缩写
            abbr = record[285:293].rstrip(b'\x00').decode('gbk', errors='ignore')
            stocks.append((code, name, last_close, abbr))
    return stock

    
def print_stock_list(stock_map, market_name, limit=100):
    """
    打印股票列表
    
    参数:
        stock_map: 股票字典
        market_name: 市场名称
        limit: 打印数量限制
    """
    if not stock_map:
        print(f"未读取到{market_name}股票数据")
        return
        
    print(f"\n{market_name}股票代码和名称示例（前{limit}个）:")
    print("=" * 40)
    print("代码\t\t名称")
    print("-" * 40)

    count = 0
    for code, name in stock_map.items():
        print(f"{code}\t\t{name}")
        count += 1
        if count >= limit:
            break

    print(f"\n总共读取到 {len(stock_map)} 个{market_name}股票")

def main():
    # 默认路径
    tdx_base_path = r"D:\new_tdx\T0002\hq_cache"
    
    # 允许从命令行指定通达信安装路径
    if len(sys.argv) > 1:
        tdx_base_path = sys.argv[1]
    
    # 沪市和深市代码表文件路径
    sh_file_path = os.path.join(tdx_base_path, "shs.tnf")
    sz_file_path = os.path.join(tdx_base_path, "szs.tnf")
    
    try:
        # 读取沪市股票数据
        sh_stock_map = read_stock_codes(sh_file_path)
        print_stock_list(sh_stock_map, "沪市")
        
        # 读取深市股票数据
        sz_stock_map = read_stock_codes(sz_file_path)
        print_stock_list(sz_stock_map, "深市")
        
        # 统计总数
        total_stocks = len(sh_stock_map) + len(sz_stock_map)
        print(f"\n沪深两市共有 {total_stocks} 只股票")
        
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()