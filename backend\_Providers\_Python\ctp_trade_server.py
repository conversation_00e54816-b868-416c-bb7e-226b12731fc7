import socket
import logging
import threading
from datetime import datetime

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler("ctp_server.log"),
        logging.StreamHandler()
    ]
)

class CTPServer:
    def __init__(self, host='0.0.0.0', port=10000):
        self.host = host
        self.port = port
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.running = False
        
    def start(self):
        """启动CTP服务器"""
        try:
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.running = True
            logging.info(f"CTP Server started on {self.host}:{self.port}")
            
            while self.running:
                client_socket, addr = self.server_socket.accept()
                logging.info(f"New connection from {addr[0]}:{addr[1]}")
                # 修正了这里的括号问题
                client_thread = threading.Thread(
                    target=self.handle_client, 
                    args=(client_socket, addr)
                )
                client_thread.daemon = True
                client_thread.start()
                
        except Exception as e:
            logging.error(f"Server error: {str(e)}")
        finally:
            self.stop()
    
    def handle_client(self, client_socket, addr):
        """处理客户端连接"""
        try:
            while self.running:
                # 接收CTP指令（假设前4字节为消息长度）
                header = client_socket.recv(4)
                if not header:
                    break
                    
                # 解析消息长度 (big-endian)
                msg_length = int.from_bytes(header, byteorder='big')
                
                # 接收完整消息体
                data = b''
                while len(data) < msg_length:
                    chunk = client_socket.recv(msg_length - len(data))
                    if not chunk:
                        break
                    data += chunk
                
                if len(data) == msg_length:
                    # 记录接收到的CTP指令
                    timestamp = datetime.now().strftime('%Y%m%d-%H%M%S.%f')[:-3]
                    logging.info(f"RECV [{addr[0]}:{addr[1]}] "
                                 f"Size={len(data)+4} | "
                                 f"Header={header.hex().upper()} | "
                                 f"Body={data.hex().upper()}")
                    
                    # 模拟简单响应 (实际CTP协议需要解析后响应)
                    # 注释掉响应部分，只记录日志
                    # response = header + data
                    # client_socket.sendall(response)
                else:
                    logging.warning(f"Incomplete message from {addr}")
                    break
                    
        except ConnectionResetError:
            logging.info(f"Client {addr} disconnected abruptly")
        except Exception as e:
            logging.error(f"Client error: {str(e)}")
        finally:
            client_socket.close()
            logging.info(f"Connection closed: {addr[0]}:{addr[1]}")
    
    def stop(self):
        """停止服务器"""
        self.running = False
        try:
            self.server_socket.close()
        except:
            pass
        logging.info("CTP Server stopped")

if __name__ == "__main__":
    server = CTPServer()
    try:
        server.start()
    except KeyboardInterrupt:
        server.stop()
        logging.info("Server shutdown by user")