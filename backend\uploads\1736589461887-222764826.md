# 股票策略回测交易平台

## 项目概述
本项目是一个模块化、事件驱动的股票策略回测交易平台，采用前后端分离架构，旨在为用户提供高效的行情数据查询、策略回测和交易执行功能。

## 核心设计理念
1. **模块化设计**
   - 前后端均采用模块化设计，模块间通过事件总线和API通信
   - 模块独立开发、测试和部署
   - 支持热插拔，可根据需求动态加载模块
   - 每个模块必须保持独立性，不允许直接依赖其他模块

2. **事件驱动架构**
   - 使用mitt实现前端事件总线
   - 通过事件发布/订阅机制实现模块间通信
   - 数据流清晰，易于追踪和调试
   - 严格禁止模块间直接调用，必须通过事件总线通信
   - 事件处理器需要考虑异常情况和错误处理

3. **前后端分离**
   - 前端：React + Ant Design Pro，在 Ant Design Pro 中，必须使用它的布局系统来编排界面，切记！
   - 后端：Express + SQLite
   - 通过RESTful API进行通信
   - 前端组件必须遵循 Ant Design Pro 的布局规范

4. **开发流程规范**
   - 全局视角优先：任何修改前都要分析整体架构影响
   - 严格的开发流程：先分析、后实现、边开发边测试
   - 使用框架的最佳实践，避免过度定制
   - 代码修改必须基于逻辑推导，禁止盲目试错

5. **调试与错误处理**
   - 问题追溯：遇到问题时必须从源头开始追溯
   - 日志记录：关键操作必须记录日志
   - 错误处理：所有异常情况都要有相应处理机制
   - 测试驱动：重要功能必须编写测试用例

## 技术栈
### 前端
- 框架：React
- UI库：Ant Design Pro
- 事件总线：mitt
- 图表：Lightweight Charts
- 路由：React Router

### 后端
- 框架：Express.js
- 数据库：SQLite
- ORM：Sequelize
- 异步处理：RxJS
- API文档：Swagger UI

## 项目结构
```
project-root/
├── docker-compose.yml
├── .dockerignore
├── .env
├── README.md
├── frontend/
│   ├── Dockerfile
│   ├── package.json
│   ├── public/
│   ├── src/
│   │   ├── assets/
│   │   ├── components/
│   │   ├── layouts/
│   │   ├── pages/
│   │   ├── services/
│   │   ├── stores/
│   │   └── utils/
│   └── config/
├── backend/
│   ├── Dockerfile
│   ├── package.json
│   ├── config/
│   ├── controllers/
│   ├── models/
│   ├── routes/
│   ├── services/
│   ├── utils/
│   └── database/
└── shared/
```

## 主要功能模块
### 1. 用户管理模块
- 用户注册、登录、权限管理
- 用户配置（偏好设置、通知设置）

### 2. 内部交流模块
- 实时聊天功能（类似微信）
- 支持文本、图片、文件传输
- 消息历史记录查询

### 3. 实时行情模块
- 实时股票行情展示
- 自定义指标显示
- 历史数据查询

### 4. 策略因子研究模块
- 形态因子：通过选取形态，对形态进行量化为数值
- 指标因子：通过代码编写指标，或AI生成指标代码
- 基本面因子：通过文本描述获得基本面匹配程度
- 因子组合回测功能

### 5. 策略组合平台
- 信号策略：基于因子研究结果生成
- 操作策略：网格、马丁等自定义开平仓逻辑
- 策略组合回测
- 策略组合优化

### 6. 实盘交易模块
- 策略组合实盘执行
- 多策略并行运行
- 策略监控与人工干预
- 交易结果分析

## 开发指南
### 环境要求
- Node.js >= 16.x
- Yarn
- Docker（可选）

### 安装步骤
1. 克隆仓库
2. 安装依赖
   ```bash
   cd frontend && yarn install
   cd ../backend && yarn install
   ```
3. 配置环境变量
   - 复制`.env.example`为`.env`
   - 根据实际情况修改配置

4. 启动开发服务器
   - 前端：`cd frontend && yarn start`
   - 后端：`cd backend && yarn dev`

## 注意事项
1. 数据库使用SQLite作为临时方案，未来可无缝迁移到PostgreSQL
2. 前端不使用状态管理库，通过组件状态和事件总线管理数据流
3. 所有模块接口需遵循RESTful规范
4. 代码提交前需通过ESLint检查
5. 重要功能需编写单元测试
6. 使用框架时必须遵循其最佳实践
7. 模块间通信必须通过事件总线，禁止直接调用
8. 代码修改必须基于逻辑推导，禁止盲目试错
9. 数据库表结构变更必须通过指定流程执行

## 数据库表结构更新机制
1. **数据库表结构更新机制**
   - 使用`dbupdate.js`和`dbstructure.js`进行数据库表结构管理
   - `dbupdate.js`：负责执行表结构更新，包含以下逻辑：
     - 如果需要的表不存在，则创建新表
     - 如果表存在但字段不存在，则添加新字段
     - 如果存在不需要的字段，则删除该字段
   - `dbstructure.js`：定义最新的数据库表结构，随着项目迭代不断更新
   - 更新流程：
     1. 开发过程中需要修改表结构时，更新`backend/database/dbstructure.js`
     2. 通知项目负责人手动执行`node backend/database/dbupdate.js`进行数据结构更新
     3. 确保在开发环境和生产环境都执行更新
   - 重要规则：
     - `dbupdate.js`一旦确定就不再更改
     - 所有表结构变更必须通过`dbstructure.js`定义
     - 禁止直接通过SQL语句修改数据库结构

## 部署说明
1. 使用Docker部署
   ```bash
   docker-compose up -d --build
   ```
2. 访问地址
   - 前端：http://localhost:80
   - 后端API文档：http://localhost:3000/api-docs

## 贡献指南
1. 遵循项目代码风格
2. 提交Pull Request前确保通过所有测试
3. 新功能需提供相应文档和测试用例
4. 重大修改需先创建Issue讨论

## 开发规范
### 命名规范
- 数据库表名使用小写，不使用下划线连接
- 变量名使用驼峰命名法
- 组件名使用大驼峰命名法

### 代码提交规范
- 每次提交都要说明改动原因和影响范围
- 提交前必须通过 linter 检查
- 重要改动需要添加或更新测试用例

### 文档维护
- README.md 需要持续更新，保持与项目最新状态一致
- 关键算法和复杂逻辑需要添加详细注释
- API 接口需要及时更新文档

### 调试规则
- 发现问题时，需要按照固定流程排查：
  1. 确定问题发生的具体环节
  2. 从显示层往后端一步步追溯
  3. 排查过程中及时记录日志
  4. 找到根本原因后再进行修复
