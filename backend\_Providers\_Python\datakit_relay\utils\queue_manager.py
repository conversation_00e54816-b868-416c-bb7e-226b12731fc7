#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
请求队列管理器
管理数据请求队列，实现请求去重和优先级管理
"""

import time
import logging
import threading
from queue import PriorityQueue
from typing import Dict, List, Any, Optional, Tuple

class QueueManager:
    """请求队列管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化队列管理器"""
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.max_queue_size = config.get('max_queue_size', 100)
        self.request_interval = config.get('request_interval', 200) / 1000.0  # 转换为秒
        self.max_concurrent_requests = config.get('max_concurrent_requests', 5)
        
        # 请求队列
        self.queue = PriorityQueue(maxsize=self.max_queue_size)
        
        # 活跃请求映射表
        self.active_requests = {}
        
        # 请求计数器
        self.request_counter = {}
        
        # 最后请求时间
        self.last_request_time = {}
        
        # 当前活跃请求数
        self.active_request_count = 0
        
        # 锁
        self.lock = threading.Lock()
        
        self.logger.info(f"请求队列管理器初始化完成，最大队列大小: {self.max_queue_size}, 请求间隔: {self.request_interval}秒, 最大并发请求数: {self.max_concurrent_requests}")
    
    def add_request(self, request: Dict[str, Any]) -> bool:
        """
        添加请求到队列
        返回是否成功添加
        """
        try:
            # 生成请求键
            request_key = self._generate_request_key(request)
            
            with self.lock:
                # 检查是否已有相同请求
                if request_key in self.active_requests:
                    # 增加请求计数
                    self.request_counter[request_key] = self.request_counter.get(request_key, 0) + 1
                    self.logger.debug(f"请求已存在，增加计数: {request_key}, 计数: {self.request_counter[request_key]}")
                    return True
                
                # 检查队列是否已满
                if self.queue.full():
                    self.logger.warning(f"请求队列已满，丢弃请求: {request_key}")
                    return False
                
                # 生成优先级
                priority = self._calculate_priority(request)
                
                # 添加到队列
                self.queue.put((priority, time.time(), request))
                
                # 记录请求
                self.active_requests[request_key] = request
                self.request_counter[request_key] = 1
                
                self.logger.debug(f"请求已添加到队列: {request_key}, 优先级: {priority}")
                return True
        
        except Exception as e:
            self.logger.error(f"添加请求到队列时出错: {e}")
            return False
    
    def get_next_request(self) -> Optional[Dict[str, Any]]:
        """
        获取下一个请求
        返回请求字典，或者 None（如果没有可用请求）
        """
        try:
            with self.lock:
                # 检查是否达到最大并发请求数
                if self.active_request_count >= self.max_concurrent_requests:
                    return None
                
                # 检查队列是否为空
                if self.queue.empty():
                    return None
                
                # 获取下一个请求
                priority, timestamp, request = self.queue.get(block=False)
                
                # 生成请求键
                request_key = self._generate_request_key(request)
                
                # 检查请求间隔
                now = time.time()
                last_time = self.last_request_time.get(request_key, 0)
                if now - last_time < self.request_interval:
                    # 请求间隔太短，重新放回队列
                    self.queue.put((priority, timestamp, request))
                    return None
                
                # 更新最后请求时间
                self.last_request_time[request_key] = now
                
                # 增加活跃请求计数
                self.active_request_count += 1
                
                self.logger.debug(f"获取到下一个请求: {request_key}, 优先级: {priority}")
                return request
        
        except Exception as e:
            self.logger.error(f"获取下一个请求时出错: {e}")
            return None
    
    def complete_request(self, request: Dict[str, Any], success: bool = True) -> None:
        """
        完成请求
        success: 请求是否成功
        """
        try:
            # 生成请求键
            request_key = self._generate_request_key(request)
            
            with self.lock:
                # 减少活跃请求计数
                self.active_request_count = max(0, self.active_request_count - 1)
                
                # 减少请求计数
                if request_key in self.request_counter:
                    self.request_counter[request_key] = max(0, self.request_counter[request_key] - 1)
                    
                    # 如果计数为0，从活跃请求中移除
                    if self.request_counter[request_key] == 0:
                        self.active_requests.pop(request_key, None)
                        self.request_counter.pop(request_key, None)
                
                # 如果请求失败，重新添加到队列
                if not success and request_key not in self.active_requests:
                    # 增加优先级（数字越小优先级越高）
                    priority = self._calculate_priority(request) - 1
                    
                    # 添加到队列
                    if not self.queue.full():
                        self.queue.put((priority, time.time(), request))
                        
                        # 记录请求
                        self.active_requests[request_key] = request
                        self.request_counter[request_key] = 1
                        
                        self.logger.debug(f"请求失败，重新添加到队列: {request_key}, 新优先级: {priority}")
        
        except Exception as e:
            self.logger.error(f"完成请求时出错: {e}")
    
    def _generate_request_key(self, request: Dict[str, Any]) -> str:
        """生成请求键"""
        symbol = request.get('symbol', '')
        exchange = request.get('exchange', '')
        market = request.get('market', '')
        return f"{exchange}.{market}.{symbol}"
    
    def _calculate_priority(self, request: Dict[str, Any]) -> int:
        """
        计算请求优先级
        返回优先级值（数字越小优先级越高）
        """
        # 默认优先级
        priority = 10
        
        # 根据市场类型调整优先级
        market = request.get('market', '').lower()
        if market == 'crypto':
            priority = 5  # 加密货币优先级较高
        elif market in ['stock', 'etf']:
            priority = 10  # 股票和ETF优先级中等
        elif market == 'future':
            priority = 8  # 期货优先级较高
        
        return priority
