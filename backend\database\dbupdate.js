const { Sequelize, DataTypes } = require('sequelize');
const dbStructure = require('./dbstructure');

const path = require('path');

// 使用绝对路径，本 js 文件所在路径
const dbPath = path.resolve(__dirname, './database.sqlite');

// 初始化Sequelize
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: dbPath,
  logging: false
});

async function updateDatabase() {
  console.log('开始更新数据库结构...');
  
  try {
    // 验证数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 开启事务
    const transaction = await sequelize.transaction();

    // 1. 检查并更新表结构
    for (const [tableName, tableDefinition] of Object.entries(dbStructure)) {
      console.log(`\n正在处理表: ${tableName}`);
      
      // 检查表是否存在
      const tableExists = await sequelize.getQueryInterface().tableExists(tableName, { transaction });
      
      if (!tableExists) {
        // 创建新表
        console.log(`表 ${tableName} 不存在，正在创建...`);
        await sequelize.getQueryInterface().createTable(tableName, tableDefinition, { transaction });
        console.log(`表 ${tableName} 创建成功`);
      } else {
        // 更新现有表
        console.log(`表 ${tableName} 已存在，正在检查字段...`);
        const existingColumns = await sequelize.getQueryInterface().describeTable(tableName, { transaction });
        
        // 添加新字段
        for (const [columnName, columnDefinition] of Object.entries(tableDefinition)) {
          if (!existingColumns[columnName]) {
            console.log(`[新增字段] ${columnName}: ${JSON.stringify(columnDefinition)}`);
            await sequelize.getQueryInterface().addColumn(tableName, columnName, columnDefinition, { transaction });
            console.log(`字段 ${columnName} 添加成功`);
          } else {
            // 检查字段定义是否一致
            const existingColumn = existingColumns[columnName];
            const isChanged = Object.keys(columnDefinition).some(key => {
              return columnDefinition[key] !== existingColumn[key];
            });

            /*if (isChanged) {
              console.log(`[修改字段] ${columnName}:`);
              console.log(`  原定义: ${JSON.stringify(existingColumn)}`);
              console.log(`  新定义: ${JSON.stringify(columnDefinition)}`);
              await sequelize.getQueryInterface().changeColumn(tableName, columnName, columnDefinition, { transaction });
              console.log(`字段 ${columnName} 修改成功`);
            }*/
          }
        }
        
        // 删除不需要的字段
        for (const existingColumn of Object.keys(existingColumns)) {
          if (!tableDefinition[existingColumn]) {
            console.log(`[删除字段] ${existingColumn}`);
            await sequelize.getQueryInterface().removeColumn(tableName, existingColumn, { transaction });
            console.log(`字段 ${existingColumn} 删除成功`);
          }
        }
      }
    }

    // 提交事务
    await transaction.commit();
    console.log('\n数据库结构更新完成 ✅');

    // 手动触发 SQLite 同步
    await sequelize.query('PRAGMA wal_checkpoint;');
    console.log('SQLite WAL 已同步');
  } catch (error) {
    console.error('\n数据库结构更新失败 ❌');
    console.error('错误详情:');
    console.error(error);
    process.exit(1); // 非正常退出
  } finally {
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

// 执行更新
updateDatabase();