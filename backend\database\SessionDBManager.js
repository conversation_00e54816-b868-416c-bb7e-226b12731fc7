const { Sequelize, DataTypes } = require('sequelize');
const path = require('path');
const fs = require('fs');

class SessionDBManager {
  constructor() {
    this.connections = new Map();
    this.sessionsDir = path.join(__dirname, 'sessions');
    this.ensureSessionsDirectory();
  }

  ensureSessionsDirectory() {
    if (!fs.existsSync(this.sessionsDir)) {
      fs.mkdirSync(this.sessionsDir, { recursive: true });
    }
  }

  async getSessionDB(sessionId) {
    if (!this.connections.has(sessionId)) {
      console.log(`[SessionDBManager] Creating new database for session ${sessionId}`);
      const dbPath = path.join(this.sessionsDir, `session_${sessionId}.sqlite`);
      
      // 检查数据库文件是否存在
      const isNewDB = !fs.existsSync(dbPath);
      
      try {
        const sequelize = new Sequelize({
          dialect: 'sqlite',
          storage: dbPath,
          logging: false,
          define: {
            timestamps: false,
            underscored: true
          }
        });

        // 定义消息表结构
        const ChatMessage = sequelize.define('ChatMessage', {
          id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
          },
          senderid: {
            type: DataTypes.INTEGER,
            allowNull: false
          },
          type: {
            type: DataTypes.STRING(20),
            allowNull: false
          },
          content: {
            type: DataTypes.TEXT,
            allowNull: true
          },
          fileurl: {
            type: DataTypes.TEXT,
            allowNull: true
          },
          filename: {
            type: DataTypes.STRING(255),
            allowNull: true
          },
          filesize: {
            type: DataTypes.INTEGER,
            allowNull: true
          },
          filetype: {
            type: DataTypes.STRING(50),
            allowNull: true
          },
          isdeleted: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
          },
          createdat: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW
          }
        }, {
          tableName: 'messages',
          timestamps: false
        });

        // 如果是新数据库，同步表结构
        if (isNewDB) {
          console.log(`[SessionDBManager] Initializing database structure for session ${sessionId}`);
          await sequelize.sync();
        }

        // 测试连接
        await sequelize.authenticate();
        console.log(`[SessionDBManager] Database connection established for session ${sessionId}`);

        this.connections.set(sessionId, {
          sequelize,
          ChatMessage
        });
      } catch (error) {
        console.error(`[SessionDBManager] Failed to initialize database for session ${sessionId}:`, error);
        throw error;
      }
    }

    return this.connections.get(sessionId);
  }

  async closeConnection(sessionId) {
    const connection = this.connections.get(sessionId);
    if (connection) {
      try {
        await connection.sequelize.close();
        this.connections.delete(sessionId);
        console.log(`[SessionDBManager] Closed connection for session ${sessionId}`);
      } catch (error) {
        console.error(`[SessionDBManager] Error closing connection for session ${sessionId}:`, error);
      }
    }
  }

  async closeAllConnections() {
    const promises = [];
    for (const [sessionId] of this.connections) {
      promises.push(this.closeConnection(sessionId));
    }
    await Promise.all(promises);
    console.log('[SessionDBManager] All connections closed');
  }
}

// 确保进程退出时关闭所有连接
process.on('SIGINT', async () => {
  const manager = new SessionDBManager();
  await manager.closeAllConnections();
  process.exit(0);
});

module.exports = new SessionDBManager(); 