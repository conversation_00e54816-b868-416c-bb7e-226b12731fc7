export default new Vuex.Store({
  state: {
    klineData: [], // K线数据
    rsiData: [],   // RSI数据
    selectionProgress: { // 选股进度
      current: 0,
      total: 0,
      progress: 0
    }
  },
  mutations: {
    SET_KLINE_DATA(state, data) {
      state.klineData = data;
    },
    SET_RSI_DATA(state, data) {
      state.rsiData = data;
    },
    UPDATE_SELECTION_PROGRESS(state, progress) {
      state.selectionProgress = progress;
    }
  },
  actions: {
    fetchKlineData({ commit }, params) {
    },
    fetchRsiData({ commit }, params) {
    },
    updateSelectionProgress({ commit }, progress) {
      commit('UPDATE_SELECTION_PROGRESS', progress);
    }
  }
});