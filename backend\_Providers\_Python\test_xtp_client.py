#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XTP交易服务器测试客户端
用于测试自定义XTP服务器的功能
"""

import json
import socket
import struct
import time
import logging
import argparse
import uuid

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

class XTPTestClient:
    """XTP测试客户端"""
    
    def __init__(self, host='127.0.0.1', port=6001):
        self.host = host
        self.port = port
        self.client_socket = None
        self.session_id = None
        
    def connect(self):
        """连接到XTP服务器"""
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.connect((self.host, self.port))
            logger.info(f"已连接到XTP服务器: {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"连接XTP服务器失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.client_socket:
            self.client_socket.close()
            self.client_socket = None
            logger.info("已断开与XTP服务器的连接")
    
    def send_message(self, message):
        """发送消息到服务器"""
        if not self.client_socket:
            logger.error("socket未连接")
            return False
            
        try:
            msg_data = json.dumps(message).encode('utf-8')
            header = struct.pack('!I', len(msg_data))
            self.client_socket.sendall(header + msg_data)
            
            logger.debug(f"发送消息: {message}")
            return True
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False
    
    def receive_message(self):
        """接收服务器响应"""
        if not self.client_socket:
            logger.error("socket未连接")
            return None
            
        try:
            # 接收消息头（长度）
            header_data = self.client_socket.recv(4)
            if not header_data:
                return None
                
            msg_length = struct.unpack('!I', header_data)[0]
            
            # 接收消息体
            msg_data = b''
            while len(msg_data) < msg_length:
                chunk = self.client_socket.recv(msg_length - len(msg_data))
                if not chunk:
                    break
                msg_data += chunk
            
            if len(msg_data) == msg_length:
                message = json.loads(msg_data.decode('utf-8'))
                logger.debug(f"接收消息: {message}")
                return message
            else:
                logger.error("接收消息不完整")
                return None
                
        except Exception as e:
            logger.error(f"接收消息失败: {e}")
            return None
    
    def login(self, user='test_user', password='password123', acckey='your_jwt_token_here'):
        """登录XTP服务器"""
        login_msg = {
            "type": "login",
            "user": user,
            "password": password,
            "acckey": acckey
        }
        
        self.send_message(login_msg)
        response = self.receive_message()
        if response and response.get('result') == 0:
            self.session_id = response.get('session_id')
            logger.info(f"登录成功: user={user}, session_id={self.session_id}")
            return True
        else:
            logger.error("登录失败")
        
        return False
    
    def insert_order(self, ticker="600036", price=15.0, quantity=100, side=1):
        """下单"""
        if not self.session_id:
            logger.error("未登录，无法下单")
            return False
        
        order_msg = {
            "type": "order_insert",
            "session_id": self.session_id,
            "order_info": {
                "ticker": ticker,
                "price": price,
                "quantity": quantity,
                "side": side,  # 1=买入, 2=卖出
                "order_type": 2 # 1=市价单, 2=限价单
            }
        }
        
        logger.info(f"发送下单请求: {ticker}, 价格={price}, 数量={quantity}, 方向={'买入' if side==1 else '卖出'}")
        
        self.send_message(order_msg)
        
        # 接收订单回报
        order_response = self.receive_message()
        if order_response and order_response.get('type') == 'order_response':
            logger.info(f"订单回报: {order_response}")
            
            # 接收成交回报
            trade_response = self.receive_message()
            if trade_response and trade_response.get('type') == 'trade_response':
                logger.info(f"成交回报: {trade_response}")
            
            return True
        
        return False
    
    def cancel_order(self, order_xtp_id):
        """撤单"""
        if not self.session_id:
            logger.error("未登录，无法撤单")
            return False
        
        message = {
            'type': 'order_cancel',
            'session_id': self.session_id,
            'order_xtp_id': order_xtp_id
        }
        
        logger.info(f"发送撤单请求: order_id={order_xtp_id}")
        
        if self.send_message(message):
            response = self.receive_message()
            if response and response.get('type') == 'cancel_response':
                logger.info(f"撤单回报: {response}")
                return True
        
        return False
    
    def query_asset(self):
        """查询资金"""
        if not self.session_id:
            logger.error("未登录，无法查询资金")
            return False
        
        message = {
            'type': 'query_asset',
            'session_id': self.session_id
        }
        
        logger.info("发送资金查询请求")
        
        if self.send_message(message):
            response = self.receive_message()
            if response and response.get('type') == 'asset_response':
                logger.info(f"资金查询结果: {response}")
                return True
        
        return False
    
    def query_position(self, ticker=''):
        """查询持仓"""
        if not self.session_id:
            logger.error("未登录，无法查询持仓")
            return False
        
        message = {
            'type': 'query_position',
            'session_id': self.session_id,
            'ticker': ticker
        }
        
        logger.info(f"发送持仓查询请求: ticker={ticker}")
        
        if self.send_message(message):
            response = self.receive_message()
            if response and response.get('type') == 'position_response':
                logger.info(f"持仓查询结果: {response}")
                return True
        
        return False

def test_xtp_server(user, acckey, host='127.0.0.1', port=6001):
    """测试XTP服务器功能"""
    client = XTPTestClient(host, port)
    
    try:
        # 1. 连接服务器
        if not client.connect():
            return
        
        # 2. 登录（使用用户输入的参数）
        if not client.login(user, acckey=acckey):
            return
        
        # 3. 查询资金
        client.query_asset()
        
        # 4. 查询持仓
        client.query_position('000001')
        
        # 5. 下单测试
        client.insert_order('000001', 10.50, 100, side=1)  # 买入
        
        time.sleep(1)
        
        client.insert_order('000002', 15.80, 200, side=2)  # 卖出
        
        # 6. 撤单测试
        client.cancel_order(1001)
        
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    finally:
        client.disconnect()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='XTP交易服务器测试客户端')
    parser.add_argument('--host', default='127.0.0.1', help='XTP服务器地址 (默认: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=6001, help='XTP服务器端口 (默认: 6001)')
    parser.add_argument('--user', required=True, help='用户名/通道ID (必需)')
    parser.add_argument('--acckey', required=True, help='认证密钥 (必需)')
    
    args = parser.parse_args()
    
    logger.info("=" * 50)
    logger.info("XTP交易服务器测试客户端")
    logger.info(f"目标服务器: {args.host}:{args.port}")
    logger.info(f"用户: {args.user}")
    logger.info(f"认证密钥: {'*' * len(args.acckey)}")  # 隐藏真实密钥
    logger.info("=" * 50)
    
    test_xtp_server(args.user, args.acckey, args.host, args.port)

if __name__ == "__main__":
    main()
