import sys
import os
import json
from datetime import datetime, timedelta

# 配置文件路径
CONFIG_FILE = "qmt_config.json"

def load_config():
    """
    加载配置文件
    """
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    return {}

def save_config(config):
    """
    保存配置文件
    """
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"配置已保存到: {CONFIG_FILE}")
    except Exception as e:
        print(f"保存配置文件失败: {e}")

def init_qmt():
    """
    初始化QMT连接
    """
    try:
        # 加载配置
        config = load_config()
        
        # 添加QMT Python路径
        qmt_path = config.get('qmt_path', "C:/QMT")  # 从配置文件读取，默认C:/QMT
        if os.path.exists(qmt_path):
            sys.path.append(qmt_path)
        else:
            print(f"QMT路径不存在: {qmt_path}")
            print("请检查QMT安装路径，或手动设置正确的路径")
            return None, None
        
        # 尝试导入QMT模块
        import xtquant
        from xtquant import xtdata
        from xtquant.xttype import StockAccount
        from xtquant import xttrader
        
        print("QMT模块导入成功")
        
        # 保存连接信息到配置
        connection_info = {
            'qmt_path': qmt_path,
            'last_connection': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'connected'
        }
        
        # 更新配置
        config.update(connection_info)
        save_config(config)
        
        return xtdata, xttrader
    except ImportError as e:
        print(f"QMT模块导入失败: {e}")
        print("请确保QMT已正确安装，并检查Python路径")
        return None, None
    except Exception as e:
        print(f"QMT初始化失败: {e}")
        return None, None

def test_qmt_connection():
    """
    测试QMT连接
    """
    print("=== 测试QMT连接 ===")
    
    xtdata, xttrader = init_qmt()
    if xtdata is None:
        print("QMT初始化失败，无法进行测试")
        return False
    
    try:
        # 测试连接
        print("正在连接QMT...")
        xtdata.download_sector_data()
        print("QMT连接成功")
        
        # 获取连接信息
        config = load_config()
        if config:
            print(f"QMT路径: {config.get('qmt_path', 'N/A')}")
            print(f"最后连接时间: {config.get('last_connection', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"QMT连接失败: {e}")
        return False

def get_stock_dividend_qmt(stock_code):
    """
    使用QMT获取股票除权除息数据
    """
    print(f"=== 获取股票 {stock_code} 的除权除息数据 ===")
    
    xtdata, xttrader = init_qmt()
    if xtdata is None:
        print("QMT初始化失败")
        return None
    
    try:
        # 获取当前日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365*2)  # 获取2年数据
        
        print(f"查询时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 尝试不同的除权除息数据获取方法
        dividend_data = None
        
        # 方法1：使用get_financial_data获取分红数据
        try:
            dividend_data = xtdata.get_financial_data(
                [stock_code],  # stock_list
                ['dividend'],  # field_list
                start_date.strftime('%Y%m%d'),  # start_time
                end_date.strftime('%Y%m%d'),  # end_time
                'report_time'  # sort_field
            )
            print("使用get_financial_data方法")
        except Exception as e:
            print(f"get_financial_data方法失败: {e}")
        
        # 方法2：使用get_market_data获取包含除权数据的K线
        if not dividend_data:
            try:
                dividend_data = xtdata.get_market_data(
                    field_list=["time", "open", "high", "low", "close", "volume"],
                    stock_list=[stock_code],
                    period="1d",
                    count=500
                )
                print("使用get_market_data方法")
            except Exception as e:
                print(f"get_market_data方法失败: {e}")
        
        # 方法3：使用get_local_data获取本地数据
        if not dividend_data:
            try:
                dividend_data = xtdata.get_local_data(stock_code, period='1d', count=500)
                print("使用get_local_data方法")
            except Exception as e:
                print(f"get_local_data方法失败: {e}")
        
        if dividend_data:
            print(f"获取到 {len(dividend_data)} 条数据")
            print("数据示例:")
            for i, record in enumerate(dividend_data[:5]):
                print(f"  {i+1}. {record}")
            return dividend_data
        else:
            print("未获取到除权除息数据")
            return None
            
    except Exception as e:
        print(f"获取除权除息数据时发生错误: {e}")
        return None

def get_stock_info_qmt(stock_code):
    """
    使用QMT获取股票基本信息
    """
    print(f"=== 获取股票 {stock_code} 的基本信息 ===")
    
    xtdata, xttrader = init_qmt()
    if xtdata is None:
        print("QMT初始化失败")
        return None
    
    try:
        # 检查股票是否在股票列表中
        stock_list = xtdata.get_stock_list_in_sector('沪深A股')
        if stock_list and stock_code in stock_list:
            print(f"股票 {stock_code} 在股票列表中")
            
            # 尝试获取该股票的K线数据来验证
            try:
                kline_data = xtdata.get_local_data(stock_code, period='1d', count=1)
                if kline_data:
                    print(f"股票 {stock_code} 数据获取成功")
                    print(f"最新数据: {kline_data[-1] if kline_data else 'N/A'}")
                    return {
                        'code': stock_code,
                        'status': 'available',
                        'data': kline_data[-1] if kline_data else None
                    }
                else:
                    print(f"股票 {stock_code} 数据获取失败")
                    return None
            except Exception as e:
                print(f"获取股票数据时出错: {e}")
                return None
        else:
            print(f"股票 {stock_code} 不在股票列表中")
            return None
            
    except Exception as e:
        print(f"获取股票基本信息时发生错误: {e}")
        return None

def get_complete_daily_klines(stock_code):
    """
    获取指定品种的完整日K线数据
    """
    print(f"=== 获取 {stock_code} 的完整日K线数据 ===")
    
    xtdata, xttrader = init_qmt()
    if xtdata is None:
        print("QMT初始化失败")
        return None
    
    try:
        print(f"正在获取 {stock_code} 的日K线数据...")
        
        # 获取完整的日K线数据
        print(f"正在调用 get_market_data...")
        kline_data = xtdata.get_market_data(
            field_list=["time", "open", "high", "low", "close", "volume"],
            stock_list=[stock_code],
            period="1d",
            count=-1  # -1表示获取所有可用数据
        )
        
        print(f"API返回数据类型: {type(kline_data)}")
        print(f"API返回数据长度: {len(kline_data) if kline_data else 0}")
        
        if kline_data and len(kline_data) > 0:
            print(f"✅ 成功获取到 {len(kline_data)} 条日K线数据")
            
            # 显示数据统计信息
            if 'time' in kline_data and len(kline_data['time']) > 0:
                try:
                    if hasattr(kline_data['time'], 'iloc'):
                        first_date = kline_data['time'].iloc[0] if len(kline_data['time']) > 0 else 'N/A'
                        last_date = kline_data['time'].iloc[-1] if len(kline_data['time']) > 0 else 'N/A'
                    else:
                        first_date = kline_data['time'][0] if len(kline_data['time']) > 0 else 'N/A'
                        last_date = kline_data['time'][-1] if len(kline_data['time']) > 0 else 'N/A'
                    print(f"📅 数据时间范围: {first_date} 到 {last_date}")
                except Exception as e:
                    print(f"📅 时间范围获取错误: {e}")
            else:
                print("📅 数据时间范围: 无数据")
            
            # 显示最新几条数据
            print("\n📊 最新5条数据:")
            
            # 检查数据是否为空
            if len(kline_data.get('time', [])) == 0:
                print("  ⚠️ 数据为空，可能是股票代码不正确或数据不可用")
                return kline_data
            
            # 安全地显示数据
            for i in range(min(5, len(kline_data.get('time', [])))):
                try:
                    time = kline_data['time'][i] if i < len(kline_data['time']) else 'N/A'
                    open_price = kline_data['open'][i] if i < len(kline_data['open']) else 'N/A'
                    high_price = kline_data['high'][i] if i < len(kline_data['high']) else 'N/A'
                    low_price = kline_data['low'][i] if i < len(kline_data['low']) else 'N/A'
                    close_price = kline_data['close'][i] if i < len(kline_data['close']) else 'N/A'
                    volume = kline_data['volume'][i] if i < len(kline_data['volume']) else 'N/A'
                    
                    print(f"  {i+1}. 时间:{time} 开:{open_price} 高:{high_price} 低:{low_price} 收:{close_price} 量:{volume}")
                except Exception as e:
                    print(f"  {i+1}. 数据访问错误: {e}")
            
            return kline_data
        else:
            print("❌ 未获取到K线数据")
            return None
            
    except Exception as e:
        print(f"❌ 获取K线数据时发生错误: {e}")
        return None

def test_qmt_apis():
    """
    测试QMT的各种API
    """
    print("=== 测试QMT API ===")
    
    xtdata, xttrader = init_qmt()
    if xtdata is None:
        print("QMT初始化失败")
        return
    
    try:
        # 测试获取股票列表
        print("1. 测试获取股票列表...")
        stock_list = xtdata.get_stock_list_in_sector('沪深A股')
        if stock_list:
            print(f"   获取到 {len(stock_list)} 只股票")
            print(f"   示例: {stock_list[:5]}")
        else:
            print("   获取股票列表失败")
        
        # 测试获取K线数据 - 根据官方API文档修复
        print("\n2. 测试获取K线数据...")
        try:
            # 根据getcc2.py的正确调用方式
            kline_data = xtdata.get_market_data(
                field_list=["time", "open", "high", "low", "close", "volume"],
                stock_list=["000001.SZ"],
                period="1d",
                count=10
            )
            
            if kline_data is not None and len(kline_data) > 0:
                print(f"   获取到 {len(kline_data)} 条K线数据")
                print(f"   数据示例: {kline_data}")
            else:
                print("   获取K线数据失败")
        except Exception as e:
            print(f"   获取K线数据出错: {e}")
        
        # 测试获取财务数据
        print("\n3. 测试获取财务数据...")
        try:
            # 测试获取板块列表
            print("   测试获取板块列表...")
            sector_list = xtdata.get_sector_list()
            if sector_list:
                print(f"   获取到 {len(sector_list)} 个板块")
                print(f"   示例板块: {sector_list[:5]}")
            else:
                print("   获取板块数据失败")
        except Exception as e:
            print(f"   获取板块数据出错: {e}")
            # 尝试其他财务数据API
            try:
                print("   尝试其他财务数据API...")
                # 尝试使用get_local_data获取本地数据
                local_data = xtdata.get_local_data('000001.SZ', period='1d', count=10)
                if local_data:
                    print(f"   获取到本地数据: {len(local_data)} 条记录")
                else:
                    print("   获取本地数据失败")
            except Exception as e2:
                print(f"   获取本地数据也出错: {e2}")
        
        # 测试获取股票基本信息
        print("\n4. 测试获取股票基本信息...")
        try:
            # 尝试使用其他方法获取股票信息
            stock_info = xtdata.get_stock_list_in_sector('沪深A股')
            if stock_info and '000001.SZ' in stock_info:
                print(f"   股票000001.SZ在股票列表中")
            else:
                print("   未找到股票000001.SZ")
        except Exception as e:
            print(f"   获取股票信息出错: {e}")
        
        # 测试获取板块数据
        print("\n5. 测试获取板块数据...")
        try:
            sector_list = xtdata.get_sector_list()
            if sector_list:
                print(f"   获取到 {len(sector_list)} 个板块")
                print(f"   示例板块: {sector_list[:5]}")
            else:
                print("   获取板块数据失败")
        except Exception as e:
            print(f"   获取板块数据出错: {e}")
        
    except Exception as e:
        print(f"测试QMT API时发生错误: {e}")

def show_config():
    """
    显示当前配置
    """
    print("=== 当前配置 ===")
    config = load_config()
    if config:
        for key, value in config.items():
            print(f"{key}: {value}")
    else:
        print("暂无配置信息")

def update_config():
    """
    更新配置
    """
    print("=== 更新配置 ===")
    config = load_config()
    
    print("当前配置:")
    for key, value in config.items():
        print(f"{key}: {value}")
    
    print("\n请输入新的配置信息:")
    qmt_path = input("QMT安装路径 (留空保持当前): ").strip()
    if qmt_path:
        config['qmt_path'] = qmt_path
    
    save_config(config)
    print("配置已更新")

def main():
    """
    主函数
    """
    print("=== QMT除权数据查询测试 ===")
    print("1. 测试QMT连接")
    print("2. 获取股票除权除息数据")
    print("3. 获取股票基本信息")
    print("4. 测试QMT API")
    print("5. 获取指定品种完整日K线")
    print("6. 显示配置")
    print("7. 更新配置")
    print("8. 退出")
    
    while True:
        choice = input("\n请选择操作 (1/2/3/4/5/6/7/8): ").strip()
        
        if choice == "1":
            test_qmt_connection()
            
        elif choice == "2":
            stock_code = input("请输入股票代码 (如: 000001.SZ): ").strip()
            if stock_code:
                get_stock_dividend_qmt(stock_code)
            else:
                print("股票代码不能为空")
                
        elif choice == "3":
            stock_code = input("请输入股票代码 (如: 000001.SZ): ").strip()
            if stock_code:
                get_stock_info_qmt(stock_code)
            else:
                print("股票代码不能为空")
                
        elif choice == "4":
            test_qmt_apis()
            
        elif choice == "5":
            stock_code = input("请输入股票代码 (如: 000001.SZ): ").strip()
            if stock_code:
                get_complete_daily_klines(stock_code)
            else:
                print("股票代码不能为空")
                
        elif choice == "6":
            show_config()
            
        elif choice == "7":
            update_config()
            
        elif choice == "8":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
