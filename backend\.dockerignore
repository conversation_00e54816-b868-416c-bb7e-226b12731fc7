# Git files
.git
.gitignore

# Docker files
Dockerfile*
docker-compose.yml
.dockerignore

# Python cache and virtual environments
__pycache__/
*.py[cod]
*$py.class
.env
venv/
.venv/
env/
*.egg-info/
.pytest_cache/
nautilus_env/ # Added based on your file listing

# Node modules
node_modules/
npm-debug.log
*.log

# OS generated files
.DS_Store
Thumbs.db

# IDE files
.idea/
.vscode/

# Other cache/build files
dist/
build/
*.sqlite # Exclude databases unless needed in image
*.duckdb # Exclude databases unless needed in image
*.duckdb.wal # Exclude databases unless needed in image
logs/
pids/

# Configs that might contain secrets or are environment-specific
# (Mount them as volumes or use env vars instead)
# config.json
# tdx_servers.json

# Specific large files or outputs
*.xlsx