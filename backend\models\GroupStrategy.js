module.exports = (sequelize, DataTypes) => {
  const GroupStrategy = sequelize.define('GroupStrategy', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '关联ID'
  },
  groupid: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'groups',
      key: 'groupid'
    },
    comment: '组合ID'
  },
  strategyid: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'live_strategies',
      key: 'id'
    },
    comment: '策略ID'
  },
  deployedat: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '部署时间'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    allowNull: false,
    defaultValue: 'active',
    comment: '在组合中的状态'
  },
  weight: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: true,
    defaultValue: 1.0,
    comment: '策略权重'
  }
}, {
  tableName: 'group_strategies',
  timestamps: false,
  indexes: [
    {
      fields: ['groupid']
    },
    {
      fields: ['strategyid']
    },
    {
      unique: true,
      fields: ['groupid', 'strategyid']
    }
  ]
});

  // 定义关联关系
  GroupStrategy.associate = function(models) {
    // 关联到组合
    GroupStrategy.belongsTo(models.Group, {
      foreignKey: 'groupid',
      as: 'group'
    });

    // 关联到策略
    GroupStrategy.belongsTo(models.LiveStrategy, {
      foreignKey: 'strategyid',
      as: 'strategy'
    });
  };

  return GroupStrategy;
};
