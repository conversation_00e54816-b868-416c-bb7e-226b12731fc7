#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
框架验证测试脚本
用于验证整个实盘框架流程是否正常工作
"""

import os
import sys
import json
import shutil
import requests
import time
from datetime import datetime

# 策略服务器地址
STRATEGY_SERVER_URL = "http://localhost:5002"

def create_test_project():
    """
    创建测试项目目录和配置
    """
    print("🔧 创建框架测试项目...")
    
    try:
        # 创建测试目录
        test_dir = "backend/_Providers/_Python/strategy/portfolio/live_groups/test_user/framework_test"
        os.makedirs(test_dir, exist_ok=True)
        
        # 复制必要的文件
        portfolio_dir = "backend/_Providers/_Python/strategy/portfolio"
        
        # 复制run.py
        shutil.copy2(f"{portfolio_dir}/run.py", f"{test_dir}/run.py")
        
        # 复制logcfg.yaml
        shutil.copy2(f"{portfolio_dir}/logcfg.yaml", f"{test_dir}/logcfg.yaml")
        
        # 复制测试策略配置
        shutil.copy2(f"{portfolio_dir}/test_framework_strategies.yaml", f"{test_dir}/strategies.yaml")
        
        # 创建简化的config.yaml
        config_content = """
basefiles:
    commodity: ../../common/stk_comms.json
    contract: contracts.json
    holiday: ../../common/holidays.json
    session: ../../common/stk_sessions.json
    utf-8: true

env:
    name: cta
    mode: simulate  # 模拟模式，不实际下单
    product:
        session: TRADING

data:
    store:
        module: WtDataStorage
        path: ../../../storage/
"""
        
        with open(f"{test_dir}/config.yaml", 'w', encoding='utf-8') as f:
            f.write(config_content.strip())
        
        # 创建简单的contracts.json
        contracts_content = {
            "SSE": {
                "600036": {
                    "name": "招商银行",
                    "code": "600036",
                    "exchg": "SSE",
                    "product": "STK",
                    "maxlimitqty": 100,
                    "maxmarketqty": 20
                }
            },
            "SZSE": {
                "000001": {
                    "name": "平安银行",
                    "code": "000001",
                    "exchg": "SZSE",
                    "product": "STK",
                    "maxlimitqty": 100,
                    "maxmarketqty": 20
                }
            }
        }
        
        with open(f"{test_dir}/contracts.json", 'w', encoding='utf-8') as f:
            json.dump(contracts_content, f, ensure_ascii=False, indent=2)
        
        # 创建日志目录
        os.makedirs(f"{test_dir}/logs", exist_ok=True)
        
        print(f"✅ 测试项目创建完成: {test_dir}")
        return test_dir
        
    except Exception as e:
        print(f"❌ 创建测试项目失败: {e}")
        return None

def run_manual_test(test_dir):
    """
    手动运行测试（不通过API）
    """
    print("🚀 手动启动框架测试...")
    
    try:
        import subprocess
        
        # 启动测试进程
        process = subprocess.Popen(
            [sys.executable, 'run.py'],
            cwd=test_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ 测试进程已启动, PID: {process.pid}")
        print("📋 请观察以下内容:")
        print("   1. 控制台输出是否显示 'on_calculate 被调用'")
        print("   2. 日志文件是否生成")
        print("   3. 是否有错误信息")
        print()
        print("⏰ 等待30秒观察输出...")
        
        # 等待30秒
        time.sleep(30)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ 进程仍在运行，框架基本正常")
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ 进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ 进程已强制终止")
        else:
            stdout, stderr = process.communicate()
            print("❌ 进程已退出")
            print(f"输出: {stdout}")
            print(f"错误: {stderr}")
        
        # 检查日志文件
        log_files = []
        for file in os.listdir(test_dir):
            if file.startswith('framework_test_') and file.endswith('.log'):
                log_files.append(file)
        
        if log_files:
            print(f"📄 找到日志文件: {log_files}")
            
            # 显示最新日志的前几行
            latest_log = os.path.join(test_dir, log_files[0])
            try:
                with open(latest_log, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print("📋 日志内容预览:")
                    for i, line in enumerate(lines[:10]):
                        print(f"   {line.strip()}")
                    if len(lines) > 10:
                        print(f"   ... (共 {len(lines)} 行)")
            except Exception as e:
                print(f"⚠️ 读取日志文件失败: {e}")
        else:
            print("⚠️ 未找到日志文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_integration():
    """
    测试API集成
    """
    print("🔗 测试API集成...")
    
    try:
        # 测试数据引擎状态
        response = requests.get(f"{STRATEGY_SERVER_URL}/data_engine/status", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"✅ 数据引擎状态:")
                print(f"   运行状态: {data.get('engine_running')}")
                print(f"   项目数量: {data.get('total_projects')}")
                print(f"   活跃合约: {data.get('total_contracts')}")
            else:
                print(f"❌ 数据引擎状态查询失败: {result.get('error')}")
        else:
            print(f"❌ 数据引擎API请求失败: HTTP {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到策略服务器，请确保 strategy_server.py 正在运行")
        return False
    except Exception as e:
        print(f"❌ API集成测试失败: {e}")
        return False

def main():
    """
    主测试流程
    """
    print("=" * 60)
    print("🧪 Wonder Trader 框架验证测试")
    print("=" * 60)
    print()
    
    # 1. 创建测试项目
    test_dir = create_test_project()
    if not test_dir:
        return False
    
    print()
    
    # 2. 测试API集成
    api_ok = test_api_integration()
    
    print()
    
    # 3. 手动运行测试
    test_ok = run_manual_test(test_dir)
    
    print()
    print("=" * 60)
    print("📊 测试结果总结:")
    print(f"   项目创建: {'✅' if test_dir else '❌'}")
    print(f"   API集成: {'✅' if api_ok else '❌'}")
    print(f"   框架测试: {'✅' if test_ok else '❌'}")
    print()
    
    if test_dir and test_ok:
        print("🎉 框架验证测试完成！")
        print("📋 关键验证点:")
        print("   1. ✅ 项目目录和配置文件生成正常")
        print("   2. ✅ run.py 能够正常启动")
        print("   3. ✅ 策略类能够正确加载")
        print("   4. ✅ Wonder Trader 引擎能够初始化")
        print()
        print("🔍 如果看到 'on_calculate 被调用' 的日志，说明整个框架流程正常！")
        print(f"📁 测试目录: {test_dir}")
    else:
        print("❌ 框架验证测试失败，请检查错误信息")
    
    return test_dir and test_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
