import axios from 'axios';
import { message } from 'antd';
import { EventBus } from '@/events/eventBus';
import { UserEvents } from '@/events/events';
import { getToken, removeToken } from '@/utils/auth';

// 创建一个防抖函数，避免短时间内多次触发登出逻辑
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 创建axios实例
const httpDispatcher = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 标记是否正在处理认证错误，避免重复处理
let isHandlingAuthError = false;

// 处理认证错误的函数
const handleAuthError = () => {
  // 如果已经在处理认证错误，则不再重复处理
  if (isHandlingAuthError) {
    return;
  }
  isHandlingAuthError = true;
  console.log('[HttpDispatcher] 捕获到认证错误，准备发布登出事件');
  
  // 直接发出登出事件，让应用顶层来处理后续逻辑
  EventBus.emit(UserEvents.Types.LOGOUT, undefined);

  // 短暂延迟后重置标志，以防万一
  setTimeout(() => {
    isHandlingAuthError = false;
  }, 1000); 
};

// 请求拦截器：添加 token
httpDispatcher.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 只在开发环境打印请求信息
    if (process.env.NODE_ENV === 'development') {
      console.log('[HttpDispatcher] 代理请求:', config.url);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器：检查认证错误
httpDispatcher.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 如果请求被取消，不做特殊处理
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    if (error.response) {
      const { status, config } = error.response;

      // 检查是否是与认证相关的错误
      if (status === 401 || status === 403) {
        // 忽略登录请求本身的认证错误
        if (!config.url.includes('/user/login')) {
          handleAuthError();
        }
      }
    }

    // 将错误继续抛出，以便在具体的请求中处理
    return Promise.reject(error);
  }
);

export default httpDispatcher;