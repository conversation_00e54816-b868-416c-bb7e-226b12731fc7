#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通达信数据适配器
连接到 tdxserver.py 获取通达信数据
"""

import requests
import logging
import time
from typing import Dict, List, Any, Optional, Union

class TDXAdapter:
    """通达信数据适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化适配器"""
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.url = config.get('url', 'http://127.0.0.1:5001')
        
        # 请求超时时间（秒）
        self.timeout = config.get('timeout', 10)
        
        # 重试次数
        self.max_retries = config.get('max_retries', 3)
        
        # 重试间隔（秒）
        self.retry_interval = config.get('retry_interval', 1)
        
        # 周期映射
        self.period_map = {
            'm1': '1m',
            'm5': '5m',
            'm15': '15m',
            'm30': '30m',
            'm60': '1h',
            'day': '1D',
            'week': '1W',
            'month': '1M'
        }
        
        self.logger.info(f"通达信数据适配器初始化完成，URL: {self.url}")
    
    def fetch_data(self, symbol: str, exchange: str, market: str, period: str = 'm1') -> Optional[Dict[str, Any]]:
        """
        获取数据
        返回数据字典，或者 None（如果获取失败）
        """
        try:
            # 转换周期
            tdx_period = self.period_map.get(period, '1m')
            
            # 构建请求URL
            url = f"{self.url}/kline"
            
            # 构建请求参数
            params = {
                'market': exchange,
                'code': symbol,
                'period': tdx_period,
                'count': 1  # 只获取最新的一条数据
            }
            
            self.logger.info(f"获取通达信数据: {exchange}.{market}.{symbol}, 周期: {period}")
            
            # 发送请求
            for retry in range(self.max_retries):
                try:
                    response = requests.get(url, params=params, timeout=self.timeout)
                    
                    # 检查响应状态码
                    if response.status_code != 200:
                        self.logger.warning(f"请求失败，状态码: {response.status_code}, 重试 {retry+1}/{self.max_retries}")
                        time.sleep(self.retry_interval)
                        continue
                    
                    # 解析响应数据
                    data = response.json()
                    
                    # 检查数据
                    if not data or not isinstance(data, list) or len(data) == 0:
                        self.logger.warning(f"返回数据为空或格式错误，重试 {retry+1}/{self.max_retries}")
                        time.sleep(self.retry_interval)
                        continue
                    
                    # 获取最新的一条数据
                    latest_data = data[0]
                    
                    # 转换数据格式
                    result = self._convert_data(latest_data, period)
                    
                    self.logger.info(f"获取通达信数据成功: {exchange}.{market}.{symbol}, 周期: {period}")
                    return result
                
                except requests.exceptions.RequestException as e:
                    self.logger.warning(f"请求异常: {e}, 重试 {retry+1}/{self.max_retries}")
                    time.sleep(self.retry_interval)
                    continue
                
                except Exception as e:
                    self.logger.error(f"处理响应数据时出错: {e}")
                    time.sleep(self.retry_interval)
                    continue
            
            self.logger.error(f"获取通达信数据失败，已重试 {self.max_retries} 次")
            return None
        
        except Exception as e:
            self.logger.error(f"获取通达信数据时出错: {e}")
            return None
    
    def fetch_quote(self, symbol: str, exchange: str, market: str) -> Optional[Dict[str, Any]]:
        """
        获取盘口数据
        返回数据字典，或者 None（如果获取失败）
        """
        try:
            # 构建请求URL
            url = f"{self.url}/quote"
            
            # 构建请求参数
            params = {
                'market': exchange,
                'code': symbol
            }
            
            self.logger.info(f"获取通达信盘口数据: {exchange}.{market}.{symbol}")
            
            # 发送请求
            for retry in range(self.max_retries):
                try:
                    response = requests.get(url, params=params, timeout=self.timeout)
                    
                    # 检查响应状态码
                    if response.status_code != 200:
                        self.logger.warning(f"请求失败，状态码: {response.status_code}, 重试 {retry+1}/{self.max_retries}")
                        time.sleep(self.retry_interval)
                        continue
                    
                    # 解析响应数据
                    data = response.json()
                    
                    # 检查数据
                    if not data or not isinstance(data, list) or len(data) == 0:
                        self.logger.warning(f"返回数据为空或格式错误，重试 {retry+1}/{self.max_retries}")
                        time.sleep(self.retry_interval)
                        continue
                    
                    # 获取第一条数据
                    quote_data = data[0]
                    
                    # 转换数据格式
                    result = self._convert_quote_data(quote_data)
                    
                    self.logger.info(f"获取通达信盘口数据成功: {exchange}.{market}.{symbol}")
                    return result
                
                except requests.exceptions.RequestException as e:
                    self.logger.warning(f"请求异常: {e}, 重试 {retry+1}/{self.max_retries}")
                    time.sleep(self.retry_interval)
                    continue
                
                except Exception as e:
                    self.logger.error(f"处理响应数据时出错: {e}")
                    time.sleep(self.retry_interval)
                    continue
            
            self.logger.error(f"获取通达信盘口数据失败，已重试 {self.max_retries} 次")
            return None
        
        except Exception as e:
            self.logger.error(f"获取通达信盘口数据时出错: {e}")
            return None
    
    def _convert_data(self, data: Dict[str, Any], period: str) -> Dict[str, Any]:
        """转换数据格式"""
        # 提取时间戳
        timestamp = data.get('time', 0)
        
        # 转换为日期和时间字符串
        date_str = time.strftime('%Y%m%d', time.localtime(timestamp))
        time_str = time.strftime('%H:%M:%S', time.localtime(timestamp))
        
        # 构建结果
        result = {
            'date': date_str,
            'time': time_str,
            'open': data.get('open', 0.0),
            'high': data.get('high', 0.0),
            'low': data.get('low', 0.0),
            'close': data.get('close', 0.0),
            'volume': data.get('volume', 0),
            'turnover': data.get('amount', 0.0),
            'open_interest': 0.0,  # 通达信数据中没有持仓量
            'period': period
        }
        
        return result
    
    def _convert_quote_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """转换盘口数据格式"""
        # 提取时间戳
        timestamp = int(time.time())  # 使用当前时间
        
        # 转换为日期和时间字符串
        date_str = time.strftime('%Y%m%d', time.localtime(timestamp))
        time_str = time.strftime('%H:%M:%S', time.localtime(timestamp))
        
        # 提取买卖盘数据
        bid_prices = []
        bid_volumes = []
        ask_prices = []
        ask_volumes = []
        
        # 通达信盘口数据格式可能不同，需要根据实际情况调整
        for i in range(1, 11):
            bid_price_key = f'bid{i}'
            bid_volume_key = f'bid_vol{i}'
            ask_price_key = f'ask{i}'
            ask_volume_key = f'ask_vol{i}'
            
            if bid_price_key in data:
                bid_prices.append(data.get(bid_price_key, 0.0))
                bid_volumes.append(data.get(bid_volume_key, 0))
            
            if ask_price_key in data:
                ask_prices.append(data.get(ask_price_key, 0.0))
                ask_volumes.append(data.get(ask_volume_key, 0))
        
        # 构建结果
        result = {
            'date': date_str,
            'time': time_str,
            'price': data.get('price', 0.0),
            'volume': data.get('vol', 0),
            'turnover': data.get('amount', 0.0),
            'open_interest': 0.0,  # 通达信数据中没有持仓量
            'bid_prices': bid_prices,
            'bid_volumes': bid_volumes,
            'ask_prices': ask_prices,
            'ask_volumes': ask_volumes
        }
        
        return result
