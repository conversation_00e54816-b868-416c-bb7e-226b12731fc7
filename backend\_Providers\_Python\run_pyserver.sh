#!/bin/bash

# --- 配置 ---
# 脚本所在的目录 (backend/_Providers/_Python/)
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
# 项目根目录 (假设是脚本目录的上一级)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
# Python 服务目录 (就是脚本所在的目录)
PYTHON_PROVIDER_DIR="$SCRIPT_DIR"
# 虚拟环境目录
VENV_DIR="$SCRIPT_DIR/venv"
# Gunicorn 可执行文件路径 (指向虚拟环境中的 gunicorn)
GUNICORN_EXEC="$VENV_DIR/bin/gunicorn"
# 日志和 PID 文件目录 (放在项目根目录的 run 目录下)
RUN_DIR="$PROJECT_ROOT/run"
LOG_DIR="$RUN_DIR/logs"
PID_DIR="$RUN_DIR/pids"

# app.py 配置
APP_MODULE="app:app"
APP_HOST="0.0.0.0" # 监听所有接口，以便从外部访问 (如果需要)
APP_PORT="5000"    # 或从 config.json 读取？暂时硬编码
APP_WORKERS=2      # 根据服务器 CPU 核心数调整 (例如: 2 * cores + 1)
APP_PID_FILE="$PID_DIR/app.pid"
APP_ACCESS_LOG="$LOG_DIR/app_access.log"
APP_ERROR_LOG="$LOG_DIR/app_error.log"

# tdxserver.py 配置
TDX_MODULE="tdxserver:app"
TDX_HOST="127.0.0.1" # TDX 服务通常只供本地访问
TDX_PORT="5001"
TDX_WORKERS=1      # TDX 服务通常不需要太多 worker
TDX_PID_FILE="$PID_DIR/tdxserver.pid"
TDX_ACCESS_LOG="$LOG_DIR/tdxserver_access.log"
TDX_ERROR_LOG="$LOG_DIR/tdxserver_error.log"

# --- 函数定义 ---

# 检查进程是否在运行
is_running() {
    local pid_file=$1
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null; then
            return 0 # 正在运行
        else
            # PID 文件存在但进程不存在，清理 PID 文件
            echo "警告: PID 文件 $pid_file 存在，但进程 $pid 未运行。正在清理..."
            rm -f "$pid_file"
            return 1 # 不在运行
        fi
    else
        return 1 # 不在运行
    fi
}

# 启动服务
start_server() {
    local module=$1
    local host=$2
    local port=$3
    local workers=$4
    local pid_file=$5
    local access_log=$6
    local error_log=$7
    local name=$8 # 服务名称用于日志

    if is_running "$pid_file"; then
        echo "$name 服务已在运行 (PID: $(cat "$pid_file"))。"
        return 0
    fi

    # --- 检查 Gunicorn 是否存在于虚拟环境 ---
    if [ ! -x "$GUNICORN_EXEC" ]; then
        echo "错误: 在虚拟环境 $VENV_DIR 中未找到可执行的 gunicorn。"
        echo "请先运行 ./setup_python_env.sh 来创建虚拟环境并安装依赖。"
        return 1
    fi
    # --- 结束检查 ---

    echo "正在启动 $name 服务..."
    # 使用 --chdir 切换到 Python 代码目录
    # 使用 --daemon 在后台运行
    "$GUNICORN_EXEC" --chdir "$PYTHON_PROVIDER_DIR" \
        "$module" \
        --workers "$workers" \
        --bind "$host:$port" \
        --pid "$pid_file" \
        --access-logfile "$access_log" \
        --error-logfile "$error_log" \
        --log-level info \
        --reload \
#        --daemon # <-- 注释掉这一行以便在前台调试

    # 等待一小段时间让服务启动并检查 PID 文件
    sleep 1
    if is_running "$pid_file"; then
        echo "$name 服务已启动 (PID: $(cat "$pid_file"))。"
    else
        echo "错误: $name 服务启动失败。请检查日志文件: $error_log"
        return 1
    fi
}

# 停止服务
stop_server() {
    local pid_file=$1
    local name=$2

    if ! is_running "$pid_file"; then
        echo "$name 服务未在运行。"
        return 0
    fi

    local pid=$(cat "$pid_file")
    echo "正在停止 $name 服务 (PID: $pid)..."
    kill $pid

    # 等待进程结束并检查
    local count=0
    while ps -p $pid > /dev/null; do
        if [ $count -ge 10 ]; then # 最多等待10秒
            echo "错误: 停止 $name 服务 (PID: $pid) 超时。可能需要手动处理。"
            return 1
        fi
        sleep 1
        count=$((count+1))
    done

    rm -f "$pid_file"
    echo "$name 服务已停止。"
}

# --- 主逻辑 ---

# 创建运行时目录
mkdir -p "$LOG_DIR"
mkdir -p "$PID_DIR"

# 解析命令
case "$1" in
    start)
        echo "正在启动所有服务..."
        start_server "$APP_MODULE" "$APP_HOST" "$APP_PORT" "$APP_WORKERS" "$APP_PID_FILE" "$APP_ACCESS_LOG" "$APP_ERROR_LOG" "主应用(app.py)"
        app_status=$?
        start_server "$TDX_MODULE" "$TDX_HOST" "$TDX_PORT" "$TDX_WORKERS" "$TDX_PID_FILE" "$TDX_ACCESS_LOG" "$TDX_ERROR_LOG" "TDX服务(tdxserver.py)"
        tdx_status=$?
        if [ $app_status -ne 0 ] || [ $tdx_status -ne 0 ]; then exit 1; fi
        ;;
    stop)
        echo "正在停止所有服务..."
        # 通常先停止依赖性较小的服务
        stop_server "$APP_PID_FILE" "主应用(app.py)"
        stop_server "$TDX_PID_FILE" "TDX服务(tdxserver.py)"
        ;;
    restart)
        echo "正在重启所有服务..."
        "$0" stop
        sleep 1 # 等待停止完成
        "$0" start
        ;;
    status)
        echo "检查服务状态..."
        if is_running "$APP_PID_FILE"; then
            echo "主应用(app.py) 正在运行 (PID: $(cat "$APP_PID_FILE"))。"
        else
            echo "主应用(app.py) 未运行。"
        fi
        if is_running "$TDX_PID_FILE"; then
            echo "TDX服务(tdxserver.py) 正在运行 (PID: $(cat "$TDX_PID_FILE"))。"
        else
            echo "TDX服务(tdxserver.py) 未运行。"
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        echo "示例: ./run_pyserver.sh start"
        exit 1
        ;;
esac

exit 0