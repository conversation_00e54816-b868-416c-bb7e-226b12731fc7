# 实时计算模式下的因子计算框架

本文档为在实时计算模式下（即在策略运行的 `on_calculate` 阶段按需计算因子）编写因子计算文件（位于本目录下）提供规范，并侧重于在此模式下优化计算效能。

## 核心思想

与批量预计算框架不同，本框架适应在策略运行时逐步计算因子的场景：

1.  **按需计算:** 因子计算发生在 `on_calculate` 事件触发时，通常在需要进行交易决策（如调仓）的时间点。
2.  **数据局部性:** 每次计算只使用当前时间点之前、由策略配置的 `bar_count` 定义的有限数量的历史 K 线数据。
3.  **单品种 focus:** 计算逻辑的核心是处理单个品种传入的近期数据，并返回该品种在当前时间点的因子值。
4.  **效率优化:** 由于计算会在每个决策点重复执行，优化的重点在于**每一次计算本身的速度**：
    *   **使用高效库:** 优先使用 `TA-Lib` C 库（通过 Python 包装器 `talib-lib`）计算标准指标。
    *   **使用 NumPy:** 对自定义因子使用优化的 NumPy 操作。
    *   **避免 Pandas 开销:** 在因子函数内部，直接操作传入的 NumPy 数组，避免创建临时 DataFrame。

## 计算流程 (策略层面，如 `MultiFactorsCTA.__calculate_current_scores__`)

1.  **获取数据:** 在需要计算的时间点，循环遍历所有相关品种。对每个品种，调用 `context.stra_get_bars(code, period, bar_count)` 获取最近的 K 线数据（返回如 `WtNpKline` 对象）。
2.  **准备输入:** 从返回的 `bars` 对象中提取因子计算所需的 NumPy 数组（例如 `closes = bars.closes`, `highs = bars.highs`）。
3.  **调用因子函数:** 调用相应因子文件（如 `roc.py`）中的核心计算函数（如 `calculate_value`），将提取的 NumPy 数组和参数（如 `period`）传递给它。
4.  **获取结果:** 因子函数返回该品种在当前时间点的因子值（一个 float 或 int）。
5.  **聚合与评估:** 收集所有品种的当前因子值，用于后续的公式评估、排序和交易决策。

## 因子代码文件规范 (`*.py` in `factors` directory)

所有位于本目录下的因子计算文件（`.py`）在实时计算模式下应遵循以下规范：

1.  **文件名:** 保持清晰、小写、下划线分隔（例如：`moving_average.py`, `rate_of_change.py`）。
2.  **核心函数:**
    *   **必须**包含一个用于计算**最新因子值**的核心函数。
    *   **推荐名称:** `calculate_value`。
    *   **函数签名:**
        ```python
        import numpy as np
        import talib # 或其他必要库
        from typing import Optional

        def calculate_value(opens: Optional[np.ndarray] = None,
                            highs: Optional[np.ndarray] = None,
                            lows: Optional[np.ndarray] = None,
                            closes: Optional[np.ndarray] = None,
                            volumes: Optional[np.ndarray] = None,
                            # 可以根据需要添加其他如 dates, times 等 np.ndarray
                            **kwargs) -> Optional[float]:
            """
            计算[因子名称]在最后一个时间点的数值。

            Args:
                opens (Optional[np.ndarray]): 开盘价序列 (长度为 bar_count)。
                highs (Optional[np.ndarray]): 最高价序列 (长度为 bar_count)。
                lows (Optional[np.ndarray]): 最低价序列 (长度为 bar_count)。
                closes (Optional[np.ndarray]): 收盘价序列 (长度为 bar_count)。
                volumes (Optional[np.ndarray]): 成交量序列 (长度为 bar_count)。
                # 其他可能的输入数组...
                **kwargs: 因子特定的参数 (e.g., period: int, column: str - 主要用于选择输入数组)。

            Returns:
                Optional[float]: 计算出的最后一个时间点的因子值。
                                 如果无法计算（例如输入数据不足或错误），返回 None 或 np.nan。
            """
            # --- 函数实现 ---
            # 1. 从 kwargs 获取参数 (如 period)
            # 2. 根据 kwargs 中的 'column' 或因子逻辑确定主要输入数组 (如 closes)
            # 3. 检查输入数组是否有效 (非 None, 长度足够)
            # 4. 调用 talib 函数或执行 numpy 计算
            # 5. 返回计算结果数组的 *最后一个* 元素
            pass
        ```
    *   **输入:** 函数直接接收计算所需的 NumPy 数组作为参数。策略代码负责从 `stra_get_bars` 返回的对象中提取这些数组。

3.  **实现要求:**
    *   **优先 TA-Lib:** 对于 TA-Lib 支持的标准指标（MA, MACD, RSI, ROC, ATR 等），**必须**使用 `talib` 库函数进行计算。
    *   **NumPy 计算:** 对于自定义指标，使用高效的 NumPy 操作。
    *   **返回最新值:** 函数的核心目标是返回输入序列计算得到的因子序列的**最后一个有效值**。
    *   **参数处理:** 使用 `kwargs.get('param_name', default_value)` 获取参数。
    *   **边界与 NaN 处理:** 必须能处理输入数据不足（例如长度小于计算所需 `period`）或 TA-Lib/NumPy 计算结果为 NaN 的情况，并返回 `None` 或 `np.nan`。
    *   **文档字符串 (Docstring):** 必须包含清晰的文档字符串。
    *   **日志:** 避免在函数内部打印日志，除非是关键的错误或警告。
4.  **禁止内容:**
    *   **禁止包含 `calculate_series_vectorized` 函数:** 这是为预计算框架设计的。
    *   **禁止直接依赖 Pandas:** 核心计算应在 NumPy 层面完成。
    *   **禁止访问外部上下文:** 同前。
5.  **依赖:**
    *   核心依赖应为 `numpy` 和 `talib-lib`。

## 示例: `rate_of_change.py` (实时计算版)

```python
import numpy as np
import talib
from typing import Optional

def calculate_value(opens: Optional[np.ndarray] = None,
                    highs: Optional[np.ndarray] = None,
                    lows: Optional[np.ndarray] = None,
                    closes: Optional[np.ndarray] = None,
                    volumes: Optional[np.ndarray] = None,
                    **kwargs) -> Optional[float]:
    """
    计算价格变化率 (Rate of Change - ROC) 在最后一个时间点的数值。

    Args:
        closes (Optional[np.ndarray]): 收盘价序列 (长度为 bar_count)。
        **kwargs:
            period (int): ROC 的计算周期 (默认 10)。
            column (str): 指定使用哪个价格序列，应为 'close' (默认).

    Returns:
        Optional[float]: 计算出的最后一个时间点的 ROC 值。
    """
    period = kwargs.get('period', 10)
    column = kwargs.get('column', 'close') # 确认使用 closes

    # 验证输入数据
    if column != 'close' or closes is None:
        # print(f"[因子计算:ROC] 输入错误: 需要 closes 数据。")
        return np.nan # 返回 NaN 更符合数值计算习惯
    if not isinstance(period, int) or period <= 0:
        # print(f"[因子计算:ROC] 参数错误: period ({period}) 必须是正整数。")
        return np.nan
    # TA-Lib 的 ROC 需要 period + 1 个数据点才能产生第一个有效值
    if len(closes) < period + 1:
        # print(f"[因子计算:ROC] 数据不足: 需要至少 {period + 1} 条数据，实际只有 {len(closes)} 条。")
        return np.nan

    try:
        # 使用 TA-Lib 计算 ROC 序列
        roc_series = talib.ROC(closes, timeperiod=period)
        # 返回最后一个值 (TA-Lib 输出长度与输入相同，最后的值即最新值)
        # 如果最后一个值是 NaN（可能因为输入数据末尾有问题），则返回 NaN
        last_roc = roc_series[-1]
        return float(last_roc) if not np.isnan(last_roc) else np.nan
    except Exception as e:
        # print(f"[因子计算:ROC] 计算 ROC({column}, {period}) 时出错: {e}")
        return np.nan # 计算出错返回 NaN
``` 