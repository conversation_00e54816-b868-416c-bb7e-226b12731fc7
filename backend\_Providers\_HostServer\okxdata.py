from flask import Flask, jsonify, request
from okx.api import Public, Market  # 导入 OKX API 的 Public 和 Market 模块
from okx.app.utils import eprint
import datetime

app = Flask(__name__)

# 初始化 Public 客户端（用于获取交易产品基础信息）
public_client = Public(
    key="", secret="", passphrase="", flag='0', proxies={}, proxy_host=None
)

# 初始化 Market 客户端（用于获取 K 线数据）
market_client = Market(
    key="", secret="", passphrase="", flag='0', proxies={}, proxy_host=None
)

@app.route('/api/instruments', methods=['GET'])
def get_instruments():
    """
    查询支持的品种列表
    参数:
        instType: 市场类型 (可选，默认为 SPOT)
            - SPOT: 现货
            - FUTURES: 期货
            - SWAP: 永续合约
            - OPTION: 期权
    """
    # 获取请求参数
    inst_type = request.args.get('instType', 'SPOT').upper()  # 默认为现货市场

    # 验证市场类型是否有效
    valid_inst_types = ["SPOT", "FUTURES", "SWAP", "OPTION"]
    if inst_type not in valid_inst_types:
        return jsonify({
            "status": "error",
            "message": f"Invalid instType: {inst_type}. Valid options are {valid_inst_types}."
        }), 400

    try:
        # 调用 OKX API 获取支持的品种列表
        result = public_client.get_instruments(instType=inst_type)

        # 提取品种名称
        instrument_list = [item["instId"] for item in result["data"]]

        return jsonify({
            "status": "success",
            "data": instrument_list
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to fetch instruments: {str(e)}"
        }), 500

# 定义合法的 interval 格式
valid_intervals = ['1m', '5m', '15m', '1H', '4H', '1D', '1W', '1M']

def standardize_interval(interval):
    """
    将输入的 interval 转换为标准格式，如果不符合则返回 None
    """
    if not interval:
        return None
    interval = interval.strip().lower()
    for valid_interval in valid_intervals:
        if interval == valid_interval.lower():
            return valid_interval
    return None

@app.route('/api/kline', methods=['GET'])
def get_kline():
    """
    查询指定品种和指定周期的走势数据
    参数:
        instrument: 品种名称 (例如 BTC-USDT)
        interval: 时间周期 (例如 1m, 5m, 15m, 1H, 4H, 1D, 1W)
    """
    full_url = request.url
    print(f"接收到的 URL: {full_url}")

    # 获取请求参数
    instrument = request.args.get('instrument')
    interval = standardize_interval(request.args.get('interval'))

    # 验证参数是否有效
    if not instrument or not interval:
        return jsonify({
            "status": "error",
            "message": "Missing required parameters: 'instrument' and 'interval'"
        }), 400

    try:
        all_data = []
        max_calls = 4  # 最大调用次数
        current_before = ''  # 初始为空表示获取最新数据
        current_after = ''

        for _ in range(max_calls):

            print(f"尝试获取时间：after:{current_after} before:{current_before} 之前的数据")

            # 调用 OKX API 获取 K线数据
            kline_result = market_client.get_candles(
                instId=instrument,
                bar=interval,
                before=current_before,
                after=current_after,  # 始终获取更旧的数据
                limit="300"
            )

            # 检查返回数据有效性
            if not isinstance(kline_result, dict) or kline_result.get('code') != '0':
                print(f"调用失败！错误响应：{kline_result}")
                break

            current_data = kline_result.get('data', [])
            if not current_data:
                print("获取到空数据，终止循环")
                break

            print(f"获取到时间 ", current_after, f" 之前的数据：{len(current_data)} 条")
            # 将数据添加到总列表（OKX返回的数据是时间倒序，最新->最旧）
            all_data.extend(current_data)

            # OKX API 设置after则返回之前的数据
            current_after = current_data[-1][0]

            # 如果返回数据不足300条说明已经没有更多数据
            if len(current_data) < 250:
                print("返回数据不足250条，终止循环")
                break

        # 最终数据按时间正序排列（最旧->最新）
        all_data.reverse()

        return jsonify({
            "status": "success",
            "data": all_data
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to fetch data: {str(e)}"
        }), 500

if __name__ == '__main__':
    # 启动 Flask 服务
    app.run(host='0.0.0.0', port=5001)
