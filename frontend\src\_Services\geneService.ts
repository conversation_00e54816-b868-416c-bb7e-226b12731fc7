import { GeneIndicators, GeneParam } from '../_Pages/main/models/geneState';

/**
 * 基因服务
 * 提供基因指标和配置相关的服务函数
 */

/**
 * 获取基因指标列表
 * @returns 包含过去和未来指标的列表
 */
export const getGeneIndicators = async (): Promise<GeneIndicators> => {
    // 模拟数据
    return {
        past: [
            { id: 1, name: 'MA交叉', type: 'past', description: '移动平均线交叉' },
            { id: 2, name: 'RSI', type: 'past', description: '相对强弱指标' },
            { id: 3, name: 'MACD', type: 'past', description: '指数平滑异同移动平均线' }
        ],
        future: [
            { id: 4, name: '未来涨幅', type: 'future', description: '未来N个周期的涨幅' },
            { id: 5, name: '最大回撤', type: 'future', description: '未来N个周期的最大回撤' }
        ]
    };
};

/**
 * 获取指标参数配置
 * @param indicatorId 指标ID
 * @returns 参数配置列表
 */
export const getIndicatorParams = async (indicatorId: number): Promise<GeneParam[]> => {
    // 模拟数据
    const paramConfigs: { [key: number]: GeneParam[] } = {
        1: [ // MA交叉
            {
                id: 1,
                name: 'fastPeriod',
                label: '快线周期',
                type: 'number',
                required: true
            },
            {
                id: 2,
                name: 'slowPeriod',
                label: '慢线周期',
                type: 'number',
                required: true
            },
            {
                id: 3,
                name: 'condition',
                label: '交叉条件',
                type: 'select',
                required: true,
                options: [
                    { value: 'above', label: '快线上穿慢线' },
                    { value: 'below', label: '快线下穿慢线' }
                ]
            }
        ],
        4: [ // 未来涨幅
            {
                id: 1,
                name: 'period',
                label: '周期数',
                type: 'number',
                required: true
            },
            {
                id: 2,
                name: 'threshold',
                label: '涨幅阈值(%)',
                type: 'number',
                required: true
            }
        ]
    };

    return paramConfigs[indicatorId] || [];
}; 