import { KLine, KLineData } from '@/shared_types/market';
import { BaseSignal, SignalParameter, SignalType } from '../BaseSignal';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';

/**
 * 唐奇安通道突破信号
 * 使用价格突破最高价和最低价通道来判断趋势
 * 当价格突破上轨（最高价通道）时产生买入信号
 * 当价格突破下轨（最低价通道）时产生卖出信号
 */
export class DonchianChannelSignal extends BaseSignal {
  private period: number;
  private breakoutStrength: number;

  constructor(config: SignalConfig) {
    super(config);
    
    // 设置参数
    this.period = this.parameters.period;
    this.breakoutStrength = this.parameters.breakoutStrength;

    // 验证参数
    if (this.breakoutStrength <= 0 || this.breakoutStrength >= 1) {
      throw new Error('Breakout strength must be between 0 and 1');
    }
  }

  /**
   * 获取信号参数列表
   */
  static async getParameters(): Promise<SignalParameter[]> {
    return Promise.resolve([
      {
        name: 'period',
        paramType: 'number',
        default: 20,
        description: '通道周期',
        minValue: 5,
        maxValue: 100,
        step: 1
      },
      {
        name: 'breakoutStrength',
        paramType: 'number',
        default: 0.001,
        description: '突破确认比例',
        minValue: 0.0001,
        maxValue: 0.01,
        step: 0.0001
      }
    ]);
  }

  /**
   * 计算最高价通道
   */
  private calculateUpperChannel(data: KLineData[], period: number): number[] {
    const upper: number[] = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        upper.push(NaN);
        continue;
      }

      let highest = -Infinity;
      for (let j = 0; j < period; j++) {
        highest = Math.max(highest, data[i - j].high);
      }
      upper.push(highest);
    }
    return upper;
  }

  /**
   * 计算最低价通道
   */
  private calculateLowerChannel(data: KLineData[], period: number): number[] {
    const lower: number[] = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        lower.push(NaN);
        continue;
      }

      let lowest = Infinity;
      for (let j = 0; j < period; j++) {
        lowest = Math.min(lowest, data[i - j].low);
      }
      lower.push(lowest);
    }
    return lower;
  }

  calculateSignals(kline: KLine, params: Record<string, any>): MarketEvents.SignalItem[] {
    const signals: MarketEvents.SignalItem[] = [];
    const period = params.period as number;
    const breakoutStrength = params.breakoutStrength as number;

    const data = kline.data;

    // 计算上下轨道
    const upperChannel = this.calculateUpperChannel(data, period);
    const lowerChannel = this.calculateLowerChannel(data, period);

    // 从period开始遍历，寻找突破点
    for (let i = period; i < data.length; i++) {
      const price = data[i].close;
      const prevPrice = data[i - 1].close;
      const upper = upperChannel[i];
      const lower = lowerChannel[i];

      // 计算突破幅度
      const upperBreakout = (price - upper) / upper;
      const lowerBreakout = (lower - price) / lower;

      // 向上突破
      if (prevPrice <= upperChannel[i - 1] && price > upper && upperBreakout > breakoutStrength) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.BUY,
          price: price,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      }
      // 向下突破
      else if (prevPrice >= lowerChannel[i - 1] && price < lower && lowerBreakout > breakoutStrength) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.SELL,
          price: price,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      }
    }

    return signals;
  }
} 