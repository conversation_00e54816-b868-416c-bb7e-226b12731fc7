from flask import Flask, jsonify, request
import akshare as ak
import pandas as pd
from datetime import datetime
import json
import os
import backtrader as bt
from strategy.gene.base import FactorType, FactorMode, SignalType
from strategy.gene.factors.ma_cross import MACrossFactor
import baostock as bs

# 读取配置文件
# 获取当前文件的目录
current_dir = os.path.dirname(__file__)

# 构建 config.json 的路径
config_path = os.path.join(current_dir, 'config.json')

# 加载 config.json
with open(config_path, 'r') as f:
    config = json.load(f)

# 获取Python服务配置
python_config = config['python_service']
HOST = python_config['host']
PORT = python_config['port']

# K线周期映射
PERIOD_MAP = {
    "1m": "1",      # 1分钟
    "5m": "5",      # 5分钟
    "15m": "15",    # 15分钟
    "30m": "30",    # 30分钟
    "60m": "60",    # 1小时
    "1D": "daily",  # 日线
    "1W": "weekly", # 周线
    "1M": "monthly", # 月线
    "day": "daily",  # 日线
    "week": "weekly", # 周线
    "month": "monthly" # 月线
}

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/stock/list', methods=['GET'])
def get_stock_list():
    """获取A股股票列表"""
    try:
        # 登录 baostock
        lg = bs.login()
        if lg.error_code != '0':
            print(f"baostock 登录失败: {lg.error_msg}")
            return jsonify({
                'success': False,
                'error': f'Failed to login baostock: {lg.error_msg}'
            }), 500

        try:
            # 获取证券基本资料
            rs = bs.query_stock_basic()
            if rs.error_code != '0':
                return jsonify({
                    'success': False,
                    'error': f'Failed to query stock list: {rs.error_msg}'
                }), 500

            # 获取字段名
            fields = rs.fields

            # 获取数据
            data_list = []
            while (rs.error_code == '0') & rs.next():
                # 将行数据转换为字典
                row_dict = dict(zip(fields, rs.get_row_data()))
                
                # 只返回状态为1（正常上市）的股票
                if row_dict['status'] == '1':
                    # 处理股票代码
                    code = row_dict['code']
                    if code.startswith('sh.'):
                        exchange = 'SSE'
                        pure_code = code.replace('sh.', '')
                    else:
                        exchange = 'SZSE'
                        pure_code = code.replace('sz.', '')

                    data_list.append({
                        'code': pure_code,
                        'name': row_dict['code_name'],
                        'market': 'STOCK',
                        'exchange': exchange
                    })

            print(f"获取到 {len(data_list)} 只股票")
            return jsonify({
                'success': True,
                'data': data_list
            })

        finally:
            # 确保登出
            bs.logout()

    except Exception as e:
        print(f"Error in get_stock_list: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def get_minute_kline(symbol, period, market='STOCK'):
    """获取分钟级别K线数据"""
    try:
        print(f"开始获取分钟K线数据: symbol={symbol}, period={period}, market={market}")
        
        if market == 'INDEX':
            # 对于指数分钟数据，使用index_zh_a_hist_min_em函数
            # 去掉后缀 .SH 或 .SZ
            symbol = symbol.split('.')[0]
            print(f"获取指数分钟数据: {symbol}")
            df = ak.index_zh_a_hist_min_em(symbol=symbol, period=period)
        else:
            # 对于股票分钟数据，使用stock_zh_a_hist_min_em函数
            print(f"获取股票分钟数据: {symbol}")
            df = ak.stock_zh_a_minute_em(symbol=symbol, period=period)

        if df is None or df.empty:
            raise Exception(f"No data returned for symbol {symbol}")

        print(f"获取到数据，开始处理...")
        klines = []
        for _, row in df.iterrows():
            # 转换日期时间字符串为时间戳
            time_str = str(row['时间'])
            timestamp = int(datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S').timestamp())
            klines.append({
                'time': timestamp,
                'open': float(row['开盘']),
                'high': float(row['最高']),
                'low': float(row['最低']),
                'close': float(row['收盘']),
                'volume': float(row['成交量'])
            })
        
        print(f"处理完成，返回 {len(klines)} 条数据")
        return klines
    except Exception as e:
        print(f"Error in get_minute_kline: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        raise e

@app.route('/stock/kline', methods=['GET'])
def get_stock_kline():
    """获取股票K线数据"""
    try:
        # 获取并打印所有请求参数
        print("Request parameters:", dict(request.args))
        
        symbol = request.args.get('symbol')
        period = request.args.get('period', '1D')  # 默认日K
        market = request.args.get('market', 'STOCK').upper()  # 默认STOCK
        adjust = request.args.get('adjust', 'qfq')  # 默认前复权
        
        if not symbol:
            return jsonify({
                'success': False,
                'error': 'Symbol is required'
            }), 400

        # 自动识别指数代码（以.SH或.SZ结尾）
        if symbol.endswith(('.SH', '.SZ')):
            return get_index_kline()

        # 判断是否是分钟级别的K线
        is_minute_level = period in ['1m', '5m', '15m', '30m', '60m']
        
        try:
            # 添加交易所前缀
            # 深交所股票代码以0、2、3开头
            if symbol.startswith(('0', '2', '3')):
                symbol_with_prefix = f"sz.{symbol}"
            # 上交所股票代码以6、9开头
            elif symbol.startswith(('6', '9')):
                symbol_with_prefix = f"sh.{symbol}"
            else:
                raise ValueError(f"Invalid stock code format: {symbol}")

            print(f"使用带前缀的股票代码: {symbol_with_prefix}")

            if is_minute_level:
                # 获取分钟级别数据
                period_num = PERIOD_MAP[period]
                klines = get_minute_kline(symbol_with_prefix, period_num, market=market)
            else:
                # 获取日线及以上周期数据
                akshare_period = PERIOD_MAP.get(period)
                if akshare_period is None:
                    return jsonify({
                        'success': False,
                        'error': f'Invalid period: {period}'
                    }), 400
                
                df = ak.stock_zh_a_hist(symbol=symbol, period=akshare_period, adjust=adjust)
                klines = []
                for _, row in df.iterrows():
                    klines.append({
                        'time': int(datetime.strptime(str(row['日期']), '%Y-%m-%d').timestamp()),
                        'open': float(row['开盘']),
                        'high': float(row['最高']),
                        'low': float(row['最低']),
                        'close': float(row['收盘']),
                        'volume': float(row['成交量'])
                    })
            
            return jsonify({
                'success': True,
                'data': klines
            })
            
        except ValueError as ve:
            print(f"Invalid stock code: {str(ve)}")
            return jsonify({
                'success': False,
                'error': str(ve)
            }), 400
        except Exception as e:
            print(f"Error fetching data: {str(e)}")
            return jsonify({
                'success': False,
                'error': f"Failed to fetch data: {str(e)}"
            }), 500
            
    except Exception as e:
        print(f"Unexpected error in get_stock_kline: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/index/kline', methods=['GET'])
def get_index_kline():
    """获取指数K线数据"""
    try:
        print("Request parameters for index:", dict(request.args))
        
        symbol = request.args.get('symbol')
        period = request.args.get('period', '1D')  # 默认日K
        
        if not symbol:
            return jsonify({
                'success': False,
                'error': 'Symbol is required'
            }), 400

        # 判断是否是分钟级别的K线
        is_minute_level = period in ['1m', '5m', '15m', '30m', '60m']
        
        try:
            if is_minute_level:
                # 获取分钟级别数据
                period_num = PERIOD_MAP[period]
                klines = get_minute_kline(symbol, period_num, market='INDEX')
            else:
                # 获取日线及以上周期数据
                akshare_period = PERIOD_MAP.get(period)
                if akshare_period is None:
                    return jsonify({
                        'success': False,
                        'error': f'Invalid period: {period}'
                    }), 400
                
                # 使用指数专用的接口，去掉后缀 .SH 或 .SZ
                symbol = symbol.split('.')[0]
                df = ak.index_zh_a_hist(symbol=symbol, period=akshare_period)
                klines = []
                for _, row in df.iterrows():
                    klines.append({
                        'time': int(datetime.strptime(str(row['日期']), '%Y-%m-%d').timestamp()),
                        'open': float(row['开盘']),
                        'high': float(row['最高']),
                        'low': float(row['最低']),
                        'close': float(row['收盘']),
                        'volume': float(row['成交量'])
                    })
            
            return jsonify({
                'success': True,
                'data': klines
            })
            
        except Exception as e:
            print(f"Error fetching index data: {str(e)}")
            return jsonify({
                'success': False,
                'error': f"Failed to fetch index data: {str(e)}"
            }), 500
            
    except Exception as e:
        print(f"Unexpected error in get_index_kline: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 因子类型到类的映射
FACTOR_MAP = {
    'MA_CROSS': MACrossFactor,
    # 后续添加更多因子
}

@app.route('/gene/factors', methods=['GET'])
def get_factor_list():
    """获取可用的因子列表"""
    try:
        factors = []
        for factor_id, factor_class in FACTOR_MAP.items():
            # 获取因子的参数信息
            params = {}
            for name, default in factor_class.params._getitems():
                if name not in ['factor_type', 'mode']:  # 排除基类参数
                    params[name] = {
                        'default': default,
                        'type': type(default).__name__
                    }
            
            factors.append({
                'id': factor_id,
                'name': factor_class.__name__,
                'description': factor_class.__doc__,
                'parameters': params
            })
        
        return jsonify({
            'success': True,
            'data': factors
        })
    except Exception as e:
        print(f"Error in get_factor_list: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def load_data_with_info(cerebro, dataname, symbol_info, **kwargs):
    """
    加载数据并记录基本信息
    Args:
        cerebro: Backtrader Cerebro 实例
        dataname: DataFrame 或 CSV 文件路径
        symbol_info: 交易品种信息 {code, name, market, exchange}
        **kwargs: 数据加载参数
    """
    # 加载数据
    if isinstance(dataname, pd.DataFrame):
        data = bt.feeds.PandasData(dataname=dataname, **kwargs)
    else:
        data = bt.feeds.GenericCSVData(dataname=dataname, **kwargs)

    # 记录基本信息
    data.symbol_info = symbol_info
    data.params_info = {
        'dataname': str(dataname),
        'timeframe': kwargs.get('timeframe', bt.TimeFrame.Days),
        'compression': kwargs.get('compression', 1),
        'fromdate': kwargs.get('fromdate'),
        'todate': kwargs.get('todate'),
        'fields': ['open', 'high', 'low', 'close', 'volume', 'openinterest']
    }
    
    print(f"加载数据信息:")
    print(f"- 品种信息: {data.symbol_info}")
    print(f"- 参数信息: {data.params_info}")

    # 将数据添加到 Cerebro
    cerebro.adddata(data)
    return data

@app.route('/gene/locate', methods=['POST'])
def locate_gene():
    """基因定位服务"""
    try:
        data = request.get_json()
        
        # 验证必要参数
        required_fields = ['symbol', 'market', 'period', 'factor_id', 'parameters', 'klines']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # 获取因子类
        factor_class = FACTOR_MAP.get(data['factor_id'])
        if not factor_class:
            return jsonify({
                'success': False,
                'error': f'Invalid factor_id: {data["factor_id"]}'
            }), 400
        
        # 创建 Cerebro 引擎
        cerebro = bt.Cerebro()
        
        # 准备数据
        df = pd.DataFrame(data['klines'])
        df['datetime'] = pd.to_datetime(df['time'].apply(lambda x: datetime.fromtimestamp(x)))
        df.set_index('datetime', inplace=True)
        
        # 构建品种信息
        symbol_info = {
            'code': data['symbol'],
            'market': data['market'],
            'period': data['period']
        }
        
        # 加载数据
        load_data_with_info(
            cerebro=cerebro,
            dataname=df,
            symbol_info=symbol_info,
            timeframe=bt.TimeFrame.Minutes if data['period'].endswith('m') else bt.TimeFrame.Days,
            compression=int(data['period'].rstrip('mDWM')) if data['period'] != '1D' else 1
        )
        
        # 添加因子
        params = data.get('parameters', {})
        params.update({
            'factor_type': FactorType.TECHNICAL,
            'mode': FactorMode.RESEARCH
        })
        factor = cerebro.addindicator(factor_class, **params)
        
        # 运行回测
        cerebro.run()
        
        # 获取信号
        signals = []
        for signal in factor.get_signals():
            signals.append({
                'time': int(signal.time.timestamp()),
                'type': signal.type.value,
                'price': signal.price,
                'index': signal.index
            })
        
        return jsonify({
            'success': True,
            'data': {
                'signals': signals,
                'factor_values': factor.lines.factor.array.tolist()
            }
        })
        
    except Exception as e:
        print(f"Error in locate_gene: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(host=HOST, port=PORT) 