# 策略配置文件模板
# 此文件将在实盘项目创建时动态生成，包含该项目的所有策略配置

strategies:
  # 示例策略配置（实际使用时会被动态替换）
  - id: "example_strategy_001"
    yaml: |
      name: "示例多因子策略"
      universe:
        - "SSE.600036"
        - "SZSE.000001"
        - "SSE.510300"
      
      parameters:
        # 因子配置
        factors:
          - name: "ma_factor"
            type: "ma"
            period: 20
            weight: 0.3
          
          - name: "roc_factor"
            type: "roc"
            period: 10
            weight: 0.4
          
          - name: "vol_factor"
            type: "std"
            period: 15
            weight: 0.3
        
        # 交易配置
        trading:
          rebalance_frequency: "daily"
          max_position_size: 0.1
          min_position_size: 0.02
          transaction_cost: 0.0003
        
        # 风控配置
        risk:
          max_drawdown: 0.15
          max_single_position: 0.2
          stop_loss: 0.05
        
        # 其他参数
        lookback_period: 252
        min_trade_amount: 1000
