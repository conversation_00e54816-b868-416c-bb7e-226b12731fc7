// 定义前端用于存储分类和池子结构的 JSON 类型

/**
 * 代表一个股票池（列表）的标识符
 */
export interface PoolIdentifier {
  poolid: number; // 后端数据库中的 symbol_list 表的 ID
  poolname: string;
}

/**
 * 代表一个分类的结构，包含其下的股票池列表
 */
export interface CategoryStructure {
  categoryname: string;
  lists: PoolIdentifier[];
  // 注意：这里没有包含 categoryId，因为当前需求是基于 JSON 操作。
  // 如果未来需要与后端同步分类本身，可能需要添加。
}

/**
 * 存储在 IndexedDB 中的完整 JSON 数据结构类型
 * 它是一个分类结构的数组
 */
export type PoolIndexJson = CategoryStructure[]; 