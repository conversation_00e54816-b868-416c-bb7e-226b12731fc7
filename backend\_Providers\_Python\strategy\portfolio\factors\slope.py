import numpy as np
from typing import Optional

def calculate_value(opens: Optional[np.ndarray] = None,
                              highs: Optional[np.ndarray] = None,
                              lows: Optional[np.ndarray] = None,
                              closes: Optional[np.ndarray] = None,
                              volumes: Optional[np.ndarray] = None,
                              **kwargs) -> Optional[float]:
    # 参数解析与校验
    try:
        window = int(kwargs.get('window', 25))
    except (TypeError, ValueError):
        return np.nan
    if window < 2:
        return np.nan
    
    column = kwargs.get('column', 'close').lower()
    input_array = {
        'open': opens, 'high': highs, 'low': lows, 
        'close': closes, 'volume': volumes
    }.get(column, None)
    
    if input_array is None or len(input_array) < window:
        return np.nan
    
    # 数据提取与校验
    y = input_array[-window:].astype(np.float64)
    if not np.all(np.isfinite(y)):
        return np.nan
    
    # 手动计算斜率
    try:
        x = np.arange(window, dtype=np.float64)
        sum_x = x.sum()
        sum_y = y.sum()
        sum_xy = np.dot(x, y)
        sum_x2 = (x**2).sum()
        
        denominator = window * sum_x2 - sum_x**2
        if denominator == 0:
            return np.nan  # 理论上不可能出现
        
        slope = (window * sum_xy - sum_x * sum_y) / denominator
        return float(slope)
    except Exception as e:
        return np.nan

# --------- 验证测试 ----------
if __name__ == "__main__":
    # 测试数据（上升趋势）
    test_data = np.array([10.0, 11.1, 12.3, 13.6, 14.9, 16.4, 17.8, 19.3, 20.7, 22.2])
    
    print("优化版计算结果:", calculate_value(closes=test_data, window=5))
    # 预期输出近似 2.47 (根据最后5个数据点)