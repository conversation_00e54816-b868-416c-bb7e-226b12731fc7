# 策略定制说明

本文档说明如何进行策略的定制化操作，包括复制现有策略。

## 数据库

自定义策略的配置信息存储在 `strategy/custom_strategy_list.duckdb` 文件中。

数据表结构 (`custom_strategies`):

*   `strategy_id` (VARCHAR, PRIMARY KEY): 策略的唯一标识符，通常是一个 UUID。
*   `content_yaml` (TEXT): 策略的完整 YAML 配置内容。
*   `creation_time` (TIMESTAMP): 策略创建的时间戳。
*   `creator_user_id` (VARCHAR): 创建该策略的用户 ID。
*   `strategy_type` (VARCHAR): 策略类型，目前固定为 'portfolio'。

## API 端点

### 复制策略

通过此端点可以复制一个现有的系统策略或自定义策略，生成一个新的自定义策略。

*   **URL:** `/strategy/copy`
*   **Method:** `POST`
*   **Request Body (JSON):**
    ```json
    {
      "source_strategy_name": "<要复制的策略名称>",
      "user_id": "<当前用户ID>"
    }
    ```
    *   `source_strategy_name`: 需要复制的策略的名称。这可以是系统策略的名称（如 `etf_trend_rotation`）或一个已存在的自定义策略的名称（通常是 UUID）。
    *   `user_id`: 执行复制操作的用户标识符。

*   **Success Response (201 Created):**
    ```json
    {
      "success": true,
      "new_strategy_id": "<新生成的策略UUID>"
    }
    ```
    *   `new_strategy_id`: 新创建的自定义策略的唯一名称（UUID）。

*   **Error Responses:**
    *   `400 Bad Request`: 请求体不是 JSON 格式，或缺少必要的字段 (`source_strategy_name`, `user_id`)。
    *   `404 Not Found`: 指定的 `source_strategy_name` 既不是有效的系统策略名称，也未在自定义策略数据库中找到。或者系统策略的 `index.yaml` 文件丢失。
    *   `409 Conflict`: （理论上不太可能发生）生成的 UUID 与数据库中现有策略名称冲突。
    *   `500 Internal Server Error`: 读取系统策略配置文件失败、处理源策略 YAML 内容时出错、或保存新策略到数据库时发生意外错误。

*   **逻辑:**
    1.  接收 `source_strategy_name` 和 `user_id`。
    2.  校验 `source_strategy_name`：
        *   首先检查是否存在于系统策略列表 (`portfolio_list.json`)。
        *   如果不是系统策略，则查询 `custom_strategies` 表中是否存在该 `strategy_id`。
    3.  如果策略未找到，返回 404 错误。
    4.  获取源策略的 YAML 内容：
        *   如果是系统策略，读取对应的 `portfolio/<name>/index.yaml` 文件。
        *   如果是自定义策略，从数据库读取 `content_yaml` 字段。
    5.  生成一个新的 UUID 作为新策略的 `strategy_id`。
    6.  解析源 YAML 内容，将其中的 `strategy_id` 键的值修改为新的 UUID。
    7.  将新的策略信息（新 UUID 名称、修改后的 YAML 内容、当前时间、用户 ID、策略类型 'portfolio'）插入到 `custom_strategies` 表中。
    8.  如果插入成功，返回 201 状态码和新的策略 UUID 名称。

### 修改策略 (仅限自定义策略)

通过此端点可以修改一个已存在的自定义策略的 YAML 配置。系统策略不允许修改。

*   **URL:** `/strategy/update`
*   **Method:** `PUT`
*   **Request Body (JSON):**
    ```json
    {
      "strategy_id": "<要修改的策略名称>",
      "strategy_yaml": "<新的策略配置YAML内容>"
    }
    ```
    *   `strategy_id`: 需要修改的自定义策略的名称（通常是 UUID）。
    *   `strategy_yaml`: 新的、完整的策略配置 YAML 字符串。

*   **Success Response (200 OK):**
    ```json
    {
      "success": true,
      "message": "Strategy '<strategy_id>' updated successfully."
    }
    ```

*   **Error Responses:**
    *   `400 Bad Request`: 请求体不是 JSON 格式，或缺少必要的字段 (`strategy_id`, `strategy_yaml`)。或者提供的 `strategy_yaml` 无效，或者 YAML 内部的 `strategy_id` 与请求的 `strategy_id` 不匹配。
    *   `403 Forbidden`: 尝试修改一个系统策略。
    *   `404 Not Found`: 指定的 `strategy_id` 在自定义策略数据库中未找到。
    *   `500 Internal Server Error`: 更新数据库或删除回测结果时发生意外错误。

*   **逻辑:**
    1.  接收 `strategy_id` 和 `strategy_yaml`。
    2.  检查 `strategy_id` 是否为系统策略。如果是，返回 403 错误。
    3.  查询 `custom_strategies` 表，检查是否存在 `strategy_id`。如果不存在，返回 404 错误。
    4.  校验 `strategy_yaml` 的格式是否合规。如果不合规，返回 400 错误。
    5.  解析 `strategy_yaml`，获取其内部的 `strategy_id` 字段值。
    6.  比较 YAML 内部的 `strategy_id` 和请求中的 `strategy_id`。如果不一致，返回 400 错误。
    7.  更新 `custom_strategies` 表中对应记录的 `content_yaml` 字段为新的 `strategy_yaml`。
    8.  调用内部函数删除 `backtest_results.duckdb` 中与该 `strategy_id` 相关的所有回测结果。
    9.  如果更新和删除（或尝试删除）完成，返回 200 成功消息。

### 创建新策略

通过此端点可以根据提供的 YAML 配置创建一个新的自定义策略。

*   **URL:** `/strategy/create`
*   **Method:** `POST`
*   **Request Body (JSON):**
    ```json
    {
      "strategy_yaml": "<新策略的配置YAML内容>",
      "user_id": "<当前用户ID>"
    }
    ```
    *   `strategy_yaml`: 完整的策略配置 YAML 字符串。其内部的 `strategy_id` 字段会被忽略，并由后端生成的 UUID 覆盖。
    *   `user_id`: 创建该策略的用户标识符。

*   **Success Response (201 Created):**
    ```json
    {
      "success": true,
      "new_strategy_id": "<新生成的策略UUID>"
    }
    ```
    *   `new_strategy_id`: 新创建的自定义策略的唯一名称（UUID）。

*   **Error Responses:**
    *   `400 Bad Request`: 请求体不是 JSON 格式，或缺少必要的字段 (`strategy_yaml`, `user_id`)。或者提供的 `strategy_yaml` 无效。
    *   `409 Conflict`: （理论上不太可能发生）生成的 UUID 与数据库中现有策略名称冲突。
    *   `500 Internal Server Error`: 处理 YAML 内容或保存到数据库时发生意外错误。

*   **逻辑:**
    1.  接收 `strategy_yaml` 和 `user_id`。
    2.  校验 `strategy_yaml` 的格式是否合规。如果不合规，返回 400 错误。
    3.  生成一个新的 UUID 作为新策略的 `strategy_id`。
    4.  解析 `strategy_yaml`，将其内部的 `strategy_id` 键的值强制修改为新生成的 UUID。
    5.  将新的策略信息（新 UUID 名称、修改后的 YAML 内容、当前时间、用户 ID、策略类型 'portfolio'）插入到 `custom_strategies` 表中。
    6.  如果插入成功，返回 201 状态码和新的策略 UUID 名称。 