import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
// 引入 Less 配置
import lessConfig from './less.mjs';

export default defineConfig({
    plugins: [react()],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
            '@shared_types': path.resolve(__dirname, './src/shared_types'),
            '@store': path.resolve(__dirname, './src/store'),
            '@events': path.resolve(__dirname, './src/events'),
            '~antd': path.resolve(__dirname, 'node_modules/antd'),

            // 自定义Ant Design主题文件别名（需要放在图标前面）
            
            'antd/es/style/themes/default.less': path.resolve(__dirname, './src/theme/antd5-theme.less'),
            'antd/lib/style/themes/default.less': path.resolve(__dirname, './src/theme/antd5-theme.less'),
            // 添加图标别名
            '@ant-design/icons/lib/dist$': path.resolve(__dirname, './src/icons.tsx'),
        },
    },
    build: {
        outDir: 'build',
        // 优化构建配置
        rollupOptions: {
            output: {
                manualChunks: {
                    'react-vendor': ['react', 'react-dom'],
                    'antd-vendor': ['antd', '@ant-design/icons', '@ant-design/pro-components'],
                    'chart-vendor': ['@antv/g2', '@antv/g2plot', 'klinecharts'],
                    'utils-vendor': ['axios', 'dayjs', 'crypto-js', 'uuid'],
                }
            }
        },
        // 启用源码映射（开发时）
        sourcemap: true,
        // 优化chunk大小警告阈值
        chunkSizeWarningLimit: 1000,
    },
    server: {
        port: 3005,
        strictPort: false, // 如果端口被占用，尝试下一个可用的端口
        watch: {
            usePolling: true,
        },
        hmr: {
            // 设置额外的 HMR 选项，帮助防止 ECONNRESET 错误
            timeout: 60000, // 增加超时时间到 60 秒
            overlay: true,  // 在页面上显示错误
            clientPort: 3005, // 确保客户端连接到正确的端口
        },
        proxy: {
            '/api': {
                target: 'http://localhost:3000',
                changeOrigin: true,
                ws: false,
                secure: false, // 允许不安全的连接
                timeout: 60000, // 设置超时时间
            },
            '/chat': {
                target: 'ws://localhost:3000',
                changeOrigin: true,
                ws: true,
                secure: false,
                timeout: 60000,
            },
            '/uploads': {
                target: 'http://localhost:3000',
                changeOrigin: true,
                ws: false,
                secure: false,
                timeout: 60000,
            },
            '/socket.io': {
                target: 'http://localhost:3000',
                changeOrigin: true,
                ws: true,
                secure: false,
                timeout: 60000,
            },
        },
    },
    css: {
        preprocessorOptions: {
            less: {
                ...lessConfig,
                // 确保 javascript 功能启用
                javascriptEnabled: true,
                // 添加全局路径
                paths: [path.resolve(__dirname, 'node_modules')],
            },
        },
    },
    optimizeDeps: {
        // 预构建这些依赖项以提高性能
        include: [
            'react', 
            'react-dom', 
            'react-router-dom', 
            'antd', 
            '@ant-design/icons', 
            '@ant-design/pro-components',
            '@ant-design/pro-layout',
            '@ant-design/pro-provider',
            'jotai',
            'react-split',
            'mitt',
            'axios',
            'dayjs',
            'crypto-js',
            'uuid',
            'socket.io-client',
            'rxjs',
            'klinecharts',
            '@antv/g2',
            '@antv/g2plot',
            'trading-signals',
            '@debut/indicators',
            'react-icons',
            'react-json-view',
            'js-yaml',
            'date-fns',
        ],
        // 排除一些不需要预构建的依赖
        exclude: [
            'react-scripts',
            'webpack-bundle-analyzer',
        ],
        // 强制预构建
        force: false,
    },
    // 增加资源限制，防止大型组件处理时出现连接问题
    esbuild: {
        treeShaking: true,
        logOverride: { 'this-is-undefined-in-esm': 'silent' },
        // 优化esbuild配置
        target: 'es2020',
        supported: {
            'bigint': true
        },
    },
    // 添加缓存配置
    cacheDir: 'node_modules/.vite',
});