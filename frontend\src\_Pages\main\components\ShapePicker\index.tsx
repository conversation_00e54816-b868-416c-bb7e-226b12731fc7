import React, { useEffect, useState } from 'react';
import { EventBus } from '@/events/eventBus';
import { ChartEvents } from '@/events/events';
import { ChartTimeRange } from '@/shared_types/chart';
import AnalysisModal from '@/_Pages/main/components/ShapePicker/AnalysisModal';

/**
 * 形态选择器组件
 * 监听区域选择事件，打开分析对话框
 */
const ShapePicker: React.FC = () => {
  // 分析对话框状态
  const [isAnalyzeModalVisible, setIsAnalyzeModalVisible] = useState(false);
  const [kLineCount, setKLineCount] = useState(0);
  const [startIndex, setStartIndex] = useState(0);
  const [endIndex, setEndIndex] = useState(0);

  // 处理分析对话框确认
  const handleAnalyzeOk = (shapes: any[], name: string) => {
    console.log('分析结果:', shapes, '名称:', name);
    setIsAnalyzeModalVisible(false);
  };

  // 监听区域选择事件
  useEffect(() => {
    const subscription = EventBus.on(
      ChartEvents.Types.RANGE_SELECTED,
      (payload: ChartEvents.RangeSelected) => {
        console.log('[ShapePicker] 收到区域选择事件:', payload);
        
        const { range } = payload;
        if (range.fromIndex !== undefined && range.toIndex !== undefined) {
          // 设置选择范围
          setStartIndex(range.fromIndex);
          setEndIndex(range.toIndex);
          // 计算K线数量
          setKLineCount(range.toIndex - range.fromIndex + 1);
          // 打开分析对话框
          setIsAnalyzeModalVisible(true);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return (
    <>
      <AnalysisModal
        visible={isAnalyzeModalVisible}
        onOk={handleAnalyzeOk}
        onCancel={() => setIsAnalyzeModalVisible(false)}
        kLineCount={kLineCount}
        startIndex={startIndex}
        endIndex={endIndex}
      />
    </>
  );
};

export default ShapePicker; 