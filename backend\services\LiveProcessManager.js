const { spawn } = require('child_process');
const path = require('path');
const fsp = require('fs').promises;
const fs = require('fs');
const redis = require('redis');
const config = require('../config.json'); // For Redis config

const getBeijingTimeString = () => {
    const now = new Date();
    // UTC+8
    const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    return beijingTime.toISOString().replace('T', ' ').replace('Z', '');
};

const logger = {
    info: (message) => console.log(`[LiveProcessManager INFO ${getBeijingTimeString()}] ${message}`),
    warn: (message) => console.warn(`[LiveProcessManager WARN ${getBeijingTimeString()}] ${message}`),
    error: (message) => console.error(`[LiveProcessManager ERROR ${getBeijingTimeString()}] ${message}`),
};

const PYTHON_PATH = 'python'; // Or specific path to python executable
const userProcesses = new Map();

// 新增：读取engine_snapshot_{userId}.json快照文件
async function loadSnapshot(userId) {
    try {
        const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
        const snapshotPath = path.join(userDir, `engine_snapshot_${userId}.json`);
        if (!fs.existsSync(snapshotPath)) {
            logger.warn(`Snapshot file not found for user ${userId}: ${snapshotPath}`);
            return null;
        }
        const content = await fsp.readFile(snapshotPath, 'utf8');
        return JSON.parse(content);
    } catch (err) {
        logger.error(`Failed to load snapshot for user ${userId}: ${err.message}`);
        return null;
    }
}

// 新增：恢复所有status为'running'的策略
async function recoverRunningStrategies(lpm, userId, snapshot, jwtToken) {
    if (!snapshot || !Array.isArray(snapshot.strategies)) {
        logger.warn(`No strategies to recover for user ${userId}`);
        return;
    }
    for (const s of snapshot.strategies) {
        if (s.status === 'running') {
            // 需要恢复的策略，尝试读取YAML内容
            try {
                const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
                const yamlPath = path.join(userDir, `${s.strategy_id}.yaml`);
                if (!fs.existsSync(yamlPath)) {
                    logger.warn(`YAML file not found for strategy ${s.strategy_id} (user ${userId}), skip recovery.`);
                    continue;
                }
                const strategyYaml = await fsp.readFile(yamlPath, 'utf8');
                await lpm.addStrategy(
                    userId,
                    s.strategy_id,
                    strategyYaml,
                    s.commodity_info ? s.commodity_info.commodity : undefined,
                    s.timeframe,
                    s.initial_capital,
                    'portfolio', // 目前只支持portfolio类型
                    jwtToken, // 这里传null，需要前端重新启动策略来获取新token
                    s.channel_id
                );
                logger.info(`Recovered running strategy ${s.strategy_id} for user ${userId} (without JWT token - needs frontend restart)`);
            } catch (err) {
                logger.error(`Failed to recover strategy ${s.strategy_id} for user ${userId}: ${err.message}`);
            }
        }
    }
}

// 新增：读取pid.txt
async function readPid(userId) {
    try {
        const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
        const pidPath = path.join(userDir, 'pid.txt');
        if (!fs.existsSync(pidPath)) return null;
        const content = await fsp.readFile(pidPath, 'utf8');
        return parseInt(content.trim(), 10);
    } catch (err) {
        logger.error(`Failed to read pid.txt for user ${userId}: ${err.message}`);
        return null;
    }
}

// 新增：判断进程是否存活
function checkProcessAlive(pid) {
    if (!pid) return false;
    try {
        process.kill(pid, 0); // 不发送信号，只检测
        return true;
    } catch (e) {
        return false;
    }
}



class LiveProcessManager {
    constructor() {
        this.redisClient = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        this.redisClient.on('error', (err) => {
            logger.error('Redis Client Error', err);
            logger.error('Redis Client Error (full):', JSON.stringify(err, Object.getOwnPropertyNames(err)));
            if (err && err.stack) {
                logger.error('Redis Client Error stack:', err.stack);
            }
        });
        this.readyPromises = new Map(); // 用于存储等待ready/pong信号的Promise

        // 用于发布的客户端
        this.redisClient = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        this.redisClient.on('error', (err) => logger.error(`Redis Publisher Error: ${err.message}`));

        this.redisSubscriber = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        this.redisSubscriber.on('error', (err) => logger.error(`Redis Subscriber Error: ${err.message}`));

        (async () => {
            try {
                await this.redisClient.connect();
                logger.info('Redis Publisher connected.');
                await this.redisSubscriber.connect();
                logger.info('Redis Subscriber connected.');
                
                // 订阅所有用户的状态频道
                await this.redisSubscriber.pSubscribe('user_engine_status:*', (message, channel) => {
                    this.#handleStatusSignal(message, channel);
                });
                logger.info('Successfully subscribed to user_engine_status:*');

            } catch (err) {
                logger.error(`Redis connection failed: ${err.message}`);
            }
        })();

        // 新增：启动定时监控
        this.startMonitoring();
    }

    // 新增：启动定时监控方法
    startMonitoring() {
        // 每分钟执行一次监控
        const MONITOR_INTERVAL = 60000; // 60秒
        
        // 启动定时监控
        setInterval(async () => {
            try {
                logger.info('[LiveProcessManager] Starting periodic process monitoring...');
                await this.#monitorActiveUserProcesses();
                logger.info('[LiveProcessManager] Periodic process monitoring completed.');
            } catch (err) {
                logger.error(`[LiveProcessManager] Error in periodic monitoring: ${err.message}`);
            }
        }, MONITOR_INTERVAL);

        logger.info(`[LiveProcessManager] Started periodic monitoring with ${MONITOR_INTERVAL/1000}s interval.`);
    }

    // 新增：监控活跃用户进程
    async #monitorActiveUserProcesses() {
        // 获取所有有快照文件的用户（而不仅仅是内存中记录的用户）
        const allUserIds = new Set();
        
        // 1. 添加内存中记录的用户
        const memoryUserIds = Array.from(userProcesses.keys());
        memoryUserIds.forEach(id => allUserIds.add(id));
        
        // 2. 扫描所有有快照文件的用户
        const liveTradingDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading');
        try {
            const entries = await fsp.readdir(liveTradingDir, { withFileTypes: true });
            for (const entry of entries) {
                if (entry.isDirectory() && entry.name.startsWith('user_')) {
                    const userId = entry.name.replace('user_', '');
                    allUserIds.add(userId);
                }
            }
        } catch (err) {
            logger.error(`Error scanning live_trading directory: ${err.message}`);
        }
        
        logger.info(`[LiveProcessManager] Monitoring ${allUserIds.size} users: ${Array.from(allUserIds).join(', ')}`);
        
        for (const userId of allUserIds) {
            try {
                const snapshot = await loadSnapshot(userId);
                const pid = await readPid(userId);
                const isAlive = checkProcessAlive(pid);
                let pong = false;
                
                // 检查进程状态，不管有没有快照文件
                if (isAlive) {
                    // Redis ping-pong 检查
                    try {
                        const controlChannel = `user_engine_control:${userId}`;
                        // 尝试从数据库或其他地方获取最新的JWT token
                        // 暂时使用空token，让引擎知道需要更新
                        await this.redisClient.publish(controlChannel, JSON.stringify({ 
                            action: 'ping',
                            jwt_token: null // 暂时传null，后续可以通过其他方式获取
                        }));
                        await this.#waitForSignal(String(userId), 'pong', 2000);
                        pong = true;
                    } catch (err) {
                        pong = false;
                    }
                }
                
                if (isAlive && pong) {
                    logger.info(`[LiveProcessManager INFO] User ${userId} process healthy, no action needed (PID: ${pid})`);
                }
                
                if (!isAlive || !pong) {
                    if (isAlive) {
                        try {
                            process.kill(pid, 'SIGTERM');
                            logger.info(`[LiveProcessManager INFO] Stopped process for user ${userId} (PID: ${pid})`);
                        } catch (e) {
                            logger.error(`[LiveProcessManager ERROR] Failed to kill process for user ${userId} (PID: ${pid}): ${e.message}`);
                        }
                    }
                    
                    logger.warn(`[LiveProcessManager WARN] Process for user ${userId} not alive or unresponsive, restarting...`);
                    await this.#startAndAwaitReady(userId);
                    logger.info(`[LiveProcessManager INFO] Process for user ${userId} started with new PID, waiting for ready signal...`);
                    
                    // 只有在有快照文件时才恢复策略
                    if (snapshot) {
                        await recoverRunningStrategies(this, userId, snapshot, null);
                        logger.warn(`[LiveProcessManager] Process restarted for user ${userId}, strategies recovered but JWT token needs to be refreshed`);
                    } else {
                        logger.info(`[LiveProcessManager] Process restarted for user ${userId}, no strategies to recover (new user or no snapshot)`);
                    }
                }
            } catch (err) {
                logger.error(`Monitor error for user ${userId}: ${err.message}`);
            }
        }
    }

    #handleStatusSignal(message, channel) {
        try {
            const userId = channel.split(':')[1];
            const data = JSON.parse(message);

            // 检查是否有等待这个用户信号的Promise
            if (this.readyPromises.has(userId)) {
                const { resolve, timeout, signalType } = this.readyPromises.get(userId);
                
                // 如果是在等待 'pong' 或 'ready' 信号，并且收到了匹配的信号
                if (data.status === signalType) {
                    logger.info(`Received expected signal '${data.status}' from user ${userId} (PID: ${data.pid})`);
                    clearTimeout(timeout);
                    resolve(true); // Promise成功解决
                    this.readyPromises.delete(userId);
                }
            }
        } catch (err) {
            logger.error(`Error handling status signal: ${err.message}`);
        }
    }

    /**
     * 等待特定的信号 ('ready' 或 'pong')
     */
    #waitForSignal(userId, signalType, timeoutMs) {
        return new Promise((resolve, reject) => {
            // 如果之前有同用户的Promise，先清理
            if (this.readyPromises.has(userId)) {
                const { timeout } = this.readyPromises.get(userId);
                clearTimeout(timeout);
            }

            const timeout = setTimeout(() => {
                this.readyPromises.delete(userId);
                reject(new Error(`Timeout: Did not receive '${signalType}' signal from user ${userId} within ${timeoutMs}ms.`));
            }, timeoutMs);

            this.readyPromises.set(String(userId), { resolve, reject, timeout, signalType });
        });
    }

    /**
     * 确保指定用户的引擎进程正在运行并已就绪。
     * @param {number|string} userId - 用户ID
     * @returns {Promise<boolean>}
     */
    async #ensureUserProcessIsRunning(userId) {
        try {
            logger.info(`Pinging engine for user ${userId}...`);
            const controlChannel = `user_engine_control:${userId}`;
            await this.redisClient.publish(controlChannel, JSON.stringify({ action: 'ping' }));
            
            // 等待 pong 回复，超时时间较短
            await this.#waitForSignal(String(userId), 'pong', 2000); 
            logger.info(`Pong received from user ${userId}. Process is running.`);
            return true;

        } catch (error) {
            // 如果ping超时或失败，则认为进程未运行，启动它
            logger.warn(`Ping failed for user ${userId}: ${error.message}. Starting new process...`);
            return this.#startAndAwaitReady(userId);
        }
    }

    /**
     * 启动一个新进程并等待其 'ready' 信号
     */
    async #startAndAwaitReady(userId) {
        // 首先确保没有旧的进程映射
        if (userProcesses.has(userId)) {
             logger.warn(`Found stale process map for user ${userId}, cleaning up before start.`);
             userProcesses.delete(userId);
        }
        
        const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
        const engineSourcePath = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'base', 'live_engine.py');
        const engineDestPath = path.join(userDir, 'live_engine.py');

        await fsp.mkdir(userDir, { recursive: true });
        await fsp.copyFile(engineSourcePath, engineDestPath);

        const pyProcess = spawn(PYTHON_PATH, [
            'live_engine.py',
            '--user_id', userId,
            '--redis_host', config.redis.host,
            '--redis_port', config.redis.port,
            '--redis_password', config.redis.password,
            '--redis_db', config.redis.db,
        ], {
            cwd: userDir,
            detached: true,
            stdio: ['ignore', 'pipe', 'pipe']
        });

        userProcesses.set(userId, pyProcess);
        logger.info(`Process for user ${userId} started with PID: ${pyProcess.pid}. userProcesses内存进程= ${JSON.stringify(Array.from(userProcesses.entries()))} Waiting for ready signal...`);

        // 新增：写入pid.txt
        try {
            const pidPath = path.join(userDir, 'pid.txt');
            await fsp.writeFile(pidPath, String(pyProcess.pid), 'utf8');
        } catch (err) {
            logger.error(`Failed to write pid.txt for user ${userId}: ${err.message}`);
        }

        pyProcess.on('exit', (code, signal) => {
            logger.warn(`Process for user ${userId} exited with code ${code} and signal ${signal}`);
            userProcesses.delete(userId);
            if (this.readyPromises.has(String(userId))) {
                const { reject, timeout } = this.readyPromises.get(String(userId));
                clearTimeout(timeout);
                reject(new Error(`Process for user ${userId} exited before sending ready signal.`));
                this.readyPromises.delete(String(userId));
            }
        });
        
        pyProcess.on('error', (err) => {
             logger.error(`Failed to start process for user ${userId}: ${err.message}`);
             userProcesses.delete(userId);
             if (this.readyPromises.has(String(userId))) {
                const { reject, timeout } = this.readyPromises.get(String(userId));
                clearTimeout(timeout);
                reject(new Error(`Process for user ${userId} failed to start: ${err.message}`));
                this.readyPromises.delete(String(userId));
            }
        });

        // 等待 ready 信号，超时时间较长
        return this.#waitForSignal(String(userId), 'ready', 15000);
    }

    /**
     * 为指定用户添加一个策略到其运行的引擎中
     * @param {number|string} userId - 用户ID
     * @param {string} liveStrategyId - 实盘策略ID
     * @param {string} strategyYaml - 策略的YAML配置内容
     * @param {string} commodity - 品种
     * @param {string} timeframe - 数据级别
     * @param {number} initialCapital - 初始资金
     * @param {string} strategyType - 策略类型，默认为 'portfolio'
     * @param {string} jwtToken - 当前用户的JWT token，可选
     */
    async addStrategy(userId, liveStrategyId, strategyYaml, commodity, timeframe, initialCapital, strategyType = 'portfolio', jwtToken, channelId) {

        logger.info('LiveProcessManager.addStrategy 被调用');

        await this.#ensureUserProcessIsRunning(userId);

        logger.info(`添加策略被调用，用户ID：${userId}，实盘策略ID：${liveStrategyId}，策略类型：${strategyType}`);

        const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
        //const yamlFileName = `${liveStrategyId}.yaml`;
        //const yamlPath = path.join(userDir, yamlFileName);
        
        // 将策略的YAML内容写入用户目录下的文件
        //await fsp.writeFile(yamlPath, strategyYaml, 'utf8');
        //logger.info(`Wrote strategy YAML to ${yamlPath}`);

        // 通过Redis发布"添加策略"的指令
        const controlChannel = `user_engine_control:${userId}`;
        const command = {
            action: 'add',
            //yaml_path: yamlFileName,
            yaml: strategyYaml,
            live_strategy_id: liveStrategyId,
            strategy_type: strategyType,
            jwt_token: jwtToken,
            channel_id: channelId,
            commodity: commodity,
            timeframe: timeframe,
            initial_capital: initialCapital
        };
        logger.info(`[DEBUG] addStrategy 发布前 channelId: ${channelId}`);
        await this.redisClient.publish(controlChannel, JSON.stringify(command));
        logger.info(`Published 'add' command for strategy ${liveStrategyId} with type ${strategyType} to channel ${controlChannel}`);
    }
    
    /**
     * 从指定用户的引擎中移除一个策略
     * @param {number|string} userId - 用户ID
     * @param {string} strategyId - 策略ID
     * @param {string} jwtToken - 当前用户的JWT token，可选
     */
    async removeStrategy(userId, strategyId, jwtToken) {
        // 检查进程是否在运行（包括内存映射和实际进程状态）
        const pid = await readPid(userId);
        const isAlive = checkProcessAlive(pid);
        const inMemory = userProcesses.has(userId);
        
        if (!inMemory && !isAlive) {
            logger.warn(`Cannot remove strategy: Process for user ${userId} is not running.`);
            return;
        }
        
        // 如果进程在运行但不在内存映射中，添加到映射中
        if (isAlive && !inMemory) {
            logger.info(`Process for user ${userId} is running (PID: ${pid}) but not in memory map, adding to map.`);
            // 这里我们不能直接添加到userProcesses，因为没有实际的process对象
            // 但我们可以继续执行移除策略的操作
        }

        // 通过Redis发布"移除策略"的指令
        const controlChannel = `user_engine_control:${userId}`;
        const command = {
            action: 'remove',
            strategy_id: strategyId,
            jwt_token: jwtToken
        };
        await this.redisClient.publish(controlChannel, JSON.stringify(command));
        logger.info(`Published 'remove' command for strategy ${strategyId} to channel ${controlChannel}`);
        
        // 删除对应的YAML文件
        const userDir = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading', `user_${userId}`);
        const yamlPath = path.join(userDir, `${strategyId}.yaml`);
        try {
            await fsp.unlink(yamlPath);
            logger.info(`Deleted strategy YAML file: ${yamlPath}`);
        } catch(err) {
            logger.warn(`Could not delete YAML file ${yamlPath}: ${err.message}`);
        }
    }

    /**
     * 停止指定用户的所有策略引擎进程
     * @param {number|string} userId - 用户ID
     */
    async stopUserProcess(userId) {
        // 优先读取pid.txt
        const pid = await readPid(userId);
        if (pid && checkProcessAlive(pid)) {
            try {
                process.kill(pid, 'SIGTERM');
                logger.info(`[LiveProcessManager INFO] Stopped process for user ${userId} (PID: ${pid})`);
            } catch (err) {
                logger.error(`[LiveProcessManager ERROR] Failed to kill process for user ${userId} (PID: ${pid}): ${err.message}`);
            }
        } else {
            logger.warn(`No running process found for user ${userId} (PID: ${pid})`);
        }
        userProcesses.delete(userId);
    }

    /**
     * 测试指定用户的实盘进程活动状态
     * @param {number|string} userId - 用户ID
     * @returns {Promise<Object>} 测试结果
     */
    async testProcessActivity(userId) {

        userId = String(userId); // ✅ 统一类型，避免覆盖参数

        try {
            logger.info(`[LiveProcessManager] Testing process activity for user ${userId}`);
            
            // 1. 检查进程是否存在
            const pid = await readPid(userId);
            const isAlive = checkProcessAlive(pid);
            
            let pong = false;
            let pingResponseTime = null;
            
            if (isAlive) {
                // 2. 进行Redis ping-pong测试
                try {
                    const startTime = Date.now();
                    const controlChannel = `user_engine_control:${userId}`;
                    await this.redisClient.publish(controlChannel, JSON.stringify({ action: 'ping' }));
                    await this.#waitForSignal(String(userId), 'pong', 2000);
                    pingResponseTime = Date.now() - startTime;
                    pong = true;
                } catch (err) {
                    pong = false;
                }
            }

            logger.info(`[LiveProcessManager] 前端获取状态，内存进程= ${JSON.stringify(Array.from(userProcesses.entries()))}`);

            // 3. 获取进程详细信息
            const processInfo = userProcesses.get(userId);
            const snapshot = await loadSnapshot(userId);
            
            const result = {
                userId: userId,
                pid: pid,
                isAlive: isAlive,
                pongResponse: pong,
                pingResponseTime: pingResponseTime,
                processInMemory: !!processInfo,
                hasSnapshot: !!snapshot,
                runningStrategies: snapshot?.strategies?.filter(s => s.status === 'running')?.length || 0,
                status: isAlive && pong ? 'healthy' : isAlive ? 'unresponsive' : 'dead',
                timestamp: new Date().toISOString()
            };
            
            logger.info(`[LiveProcessManager] Process test result for user ${userId}:`, result);
            return result;
            
        } catch (err) {
            logger.error(`[LiveProcessManager] Error testing process activity for user ${userId}: ${err.message}`);
            return {
                userId: userId,
                error: err.message,
                status: 'error',
                timestamp: new Date().toISOString()
            };
        }
    }
}

// 创建并导出LiveProcessManager实例
module.exports = new LiveProcessManager();