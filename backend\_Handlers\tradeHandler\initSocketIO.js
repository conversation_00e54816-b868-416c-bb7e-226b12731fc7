/**
 * Socket.IO初始化模块
 * 负责初始化Socket.IO服务，处理客户端连接、注册、心跳等事件
 */

// 创建简单的日志记录器
const logger = {
    info: (message) => console.log(`[TradeHandler:SocketIO INFO] ${message}`),
    warn: (message) => console.warn(`[TradeHandler:SocketIO WARN] ${message}`),
    error: (message) => console.error(`[TradeHandler:SocketIO ERROR] ${message}`),
    debug: (message) => console.log(`[TradeHandler:SocketIO DEBUG] ${message}`)
};

// 存储Socket.IO命名空间
let ioNamespace = null;

// 存储交易客户端连接
let tradingClients = null;

/**
 * 添加工具函数：只用client_id作为唯一key
 * @param {string} client_id 客户端ID
 * @returns {string} 构建后的唯一键
 */
function buildClientKey(client_id) {
    return client_id;
}

/**
 * 处理客户端注册
 * @param {SocketIO.Socket} socket Socket.IO连接
 * @param {Object} data 注册数据
 */
function handleClientRegistration(socket, data) {
    try {
        const { username, client_type, client_id, timestamp } = data;
        logger.info(`处理客户端注册: username=${username}, client_type=${client_type}, client_id=${client_id}, socketId=${socket.id}`);

        if (!client_id || typeof client_id !== 'string' || !client_id.trim()) {
            logger.warn(`客户端注册失败: client_id 无效 (client_id=${client_id})`);
            socket.emit('register_response', { success: false, message: 'client_id 无效' });
            return;
        }

        const clientKey = buildClientKey(client_id);

        const existingClient = tradingClients.get(clientKey);
        if (existingClient && existingClient.socket && existingClient.socket.connected) {
            logger.warn(`客户端注册失败: 已存在相同的客户端连接 (${clientKey})`);
            socket.emit('register_response', { success: false, message: '已存在相同的客户端连接' });
            return;
        }

        tradingClients.set(clientKey, {
            username,
            client_type,
            client_id,
            socket,
            registered_at: Date.now(),
            last_heartbeat: Date.now(),
            verified: true
        });

        logger.info(`客户端注册成功: ${clientKey}, socketId=${socket.id}`);
        socket.emit('register_response', { success: true, message: '注册成功' });
    } catch (error) {
        logger.error(`处理客户端注册时发生错误: ${error.message}`);
        socket.emit('register_response', { success: false, message: '服务器内部错误' });
    }
}

/**
 * 处理客户端心跳
 * @param {SocketIO.Socket} socket Socket.IO连接
 * @param {Object} data 心跳数据
 */
function handleClientHeartbeat(socket, data) {
    try {
        logger.debug(`[心跳原始数据] socketId=${socket.id}, data=${JSON.stringify(data)}`);
        const { client_id, timestamp } = data;
        if (!client_id || typeof client_id !== 'string' || !client_id.trim()) {
            logger.warn(`心跳处理失败: client_id 无效 (client_id=${client_id}), socketId=${socket.id}, data=${JSON.stringify(data)}`);
            return;
        }
        const clientKey = buildClientKey(client_id);
        let clientInfo = tradingClients.get(clientKey);
        if (!clientInfo) {
            // 自动恢复注册
            logger.warn(`心跳检测到通道丢失，自动恢复注册: ${clientKey}, socketId=${socket.id}, data=${JSON.stringify(data)}`);
            clientInfo = {
                client_id,
                socket,
                registered_at: Date.now(),
                last_heartbeat: Date.now(),
                verified: true
            };
            tradingClients.set(clientKey, clientInfo);
            logger.info(`通道已通过心跳自动恢复: ${clientKey}, socketId=${socket.id}`);
        } else {
        clientInfo.last_heartbeat = Date.now();
        tradingClients.set(clientKey, clientInfo);
            logger.debug(`客户端心跳更新: ${clientKey}, socketId=${socket.id}`);
        }
        socket.emit('heartbeat_response', { success: true, timestamp: Date.now() });
    } catch (error) {
        logger.error(`处理客户端心跳时发生错误: ${error.message}`);
    }
}

/**
 * 处理命令响应
 * @param {SocketIO.Socket} socket Socket.IO连接
 * @param {Object} data 命令响应数据
 */
function handleCommandResponse(socket, data) {
    try {
        const { command_id, success, result, error } = data;
        logger.debug(`收到命令响应: command_id=${command_id}, success=${success}`);

        // TODO: 处理命令响应，可能需要通知等待结果的客户端
    } catch (error) {
        logger.error(`处理命令响应时发生错误: ${error.message}`);
    }
}

/**
 * 处理客户端断开连接
 * @param {SocketIO.Socket} socket Socket.IO连接
 * @param {string} reason 断开原因
 */
function handleClientDisconnect(socket, reason) {
    logger.info(`Socket.IO连接断开: ${socket.id}, 原因: ${reason}`);

    // 查找并移除断开连接的客户端
    for (const [key, client] of tradingClients.entries()) {
        if (client.socket && client.socket.id === socket.id) {
            logger.info(`移除断开连接的客户端: ${key}`);
            tradingClients.delete(key);
            break;
        }
    }
}

/**
 * 初始化Socket.IO服务
 * @param {SocketIO.Namespace} namespace Socket.IO命名空间
 * @param {Map} clients 交易客户端连接Map
 */
function initSocketIO(namespace, clients) {
    logger.info(`初始化交易通道Socket.IO服务 (命名空间: ${namespace.name})`);
    ioNamespace = namespace;
    tradingClients = clients;

    // 处理连接事件
    ioNamespace.on('connection', (socket) => {
        logger.info(`新的Socket.IO连接: ${socket.id}`);

        // 处理注册事件
        socket.on('register', (data) => {
            handleClientRegistration(socket, data);
        });

        // 处理心跳事件
        socket.on('heartbeat', (data) => {
            handleClientHeartbeat(socket, data);
        });

        // 处理命令响应事件
        socket.on('command_response', (data) => {
            handleCommandResponse(socket, data);
        });

        // 处理断开连接事件
        socket.on('disconnect', (reason) => {
            handleClientDisconnect(socket, reason);
        });
    });
}

module.exports = initSocketIO;
