#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
K线数据转换器
将不同来源的K线数据转换为 Wonder Trader 格式
"""

import logging
from typing import Dict, Any, Optional, Union
import struct

class BarConverter:
    """K线数据转换器"""
    
    def __init__(self):
        """初始化转换器"""
        self.logger = logging.getLogger(__name__)
        
        # 周期映射
        self.period_map = {
            'm1': 'm1',
            'm5': 'm5',
            'm15': 'm15',
            'm30': 'm30',
            'm60': 'm60',
            'd1': 'd1',
            'w1': 'w1',
            'mon1': 'mon1'
        }
    
    def convert(self, exchange: str, code: str, data: Dict[str, Any]) -> Optional[bytes]:
        """
        转换K线数据
        返回二进制数据，或者 None（如果转换失败）
        """
        try:
            # 提取周期
            period = data.get('period', 'm1')
            wt_period = self.period_map.get(period, 'm1')
            
            # 创建缓冲区
            buffer = bytearray(512)  # 预分配足够大的缓冲区
            
            # 写入头部
            buffer[0:4] = b'WTPY'
            
            # 写入数据类型 (2002 for Bar)
            struct.pack_into('<I', buffer, 4, 2002)
            
            # 跳过正文长度，稍后填充
            body_start = 12
            
            # 写入交易所
            exchange_bytes = exchange.encode('utf-8').ljust(16, b'\0')
            buffer[body_start:body_start+16] = exchange_bytes
            
            # 写入代码
            code_bytes = code.encode('utf-8').ljust(32, b'\0')
            buffer[body_start+16:body_start+48] = code_bytes
            
            # 写入周期
            period_bytes = wt_period.encode('utf-8').ljust(8, b'\0')
            buffer[body_start+48:body_start+56] = period_bytes
            
            # 写入日期
            date_str = data.get('date', '').ljust(8, '\0')
            buffer[body_start+56:body_start+64] = date_str.encode('utf-8')
            
            # 写入时间
            time_str = data.get('time', '').ljust(9, '\0')
            buffer[body_start+64:body_start+73] = time_str.encode('utf-8')
            
            # 写入开盘价
            struct.pack_into('<d', buffer, body_start+73, float(data.get('open', 0.0)))
            
            # 写入最高价
            struct.pack_into('<d', buffer, body_start+81, float(data.get('high', 0.0)))
            
            # 写入最低价
            struct.pack_into('<d', buffer, body_start+89, float(data.get('low', 0.0)))
            
            # 写入收盘价
            struct.pack_into('<d', buffer, body_start+97, float(data.get('close', 0.0)))
            
            # 写入成交量
            struct.pack_into('<d', buffer, body_start+105, float(data.get('volume', 0.0)))
            
            # 写入成交额
            struct.pack_into('<d', buffer, body_start+113, float(data.get('turnover', 0.0)))
            
            # 写入持仓量
            struct.pack_into('<d', buffer, body_start+121, float(data.get('open_interest', 0.0)))
            
            # 计算正文长度
            body_length = 16 + 32 + 8 + 8 + 9 + 8*7
            
            # 写入正文长度
            struct.pack_into('<I', buffer, 8, body_length)
            
            # 返回数据
            return bytes(buffer[:body_start+body_length])
        
        except Exception as e:
            self.logger.error(f"转换K线数据时出错: {e}")
            return None
