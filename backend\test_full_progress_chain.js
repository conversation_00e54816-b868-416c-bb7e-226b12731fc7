/**
 * 完整进度链路测试脚本
 * 测试从Redis到前端的完整进度传递链路
 */

const redis = require('redis');
const config = require('./config.json');

async function testFullProgressChain() {
    console.log('[进度链路测试] 开始测试完整进度传递链路...');
    
    try {
        // 创建Redis发布客户端
        const publisher = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        
        await publisher.connect();
        console.log('[进度链路测试] Redis连接成功');
        
        // 模拟选股任务ID
        const testTaskId = 'test-progress-chain-' + Date.now();
        console.log(`[进度链路测试] 使用测试任务ID: ${testTaskId}`);
        
        // 模拟选股进度序列
        const progressSequence = [
            {
                taskId: testTaskId,
                stage: '开始选股',
                current: 0,
                total: 0,
                selectedCount: 0,
                symbolInfo: '',
                progress: 0,
                timestamp: Date.now()
            },
            {
                taskId: testTaskId,
                stage: '获取候选品种列表',
                current: 0,
                total: 0,
                selectedCount: 0,
                symbolInfo: '',
                progress: 0,
                timestamp: Date.now()
            },
            {
                taskId: testTaskId,
                stage: '候选品种获取完成',
                current: 0,
                total: 100,
                selectedCount: 0,
                symbolInfo: '',
                progress: 0,
                timestamp: Date.now()
            },
            {
                taskId: testTaskId,
                stage: '处理品种',
                current: 10,
                total: 100,
                selectedCount: 2,
                symbolInfo: '000001 - 平安银行',
                progress: 10,
                timestamp: Date.now()
            },
            {
                taskId: testTaskId,
                stage: '处理品种',
                current: 25,
                total: 100,
                selectedCount: 5,
                symbolInfo: '000002 - 万科A',
                progress: 25,
                timestamp: Date.now()
            },
            {
                taskId: testTaskId,
                stage: '处理品种',
                current: 50,
                total: 100,
                selectedCount: 12,
                symbolInfo: '000858 - 五粮液',
                progress: 50,
                timestamp: Date.now()
            },
            {
                taskId: testTaskId,
                stage: '处理品种',
                current: 75,
                total: 100,
                selectedCount: 18,
                symbolInfo: '600036 - 招商银行',
                progress: 75,
                timestamp: Date.now()
            },
            {
                taskId: testTaskId,
                stage: '处理品种',
                current: 100,
                total: 100,
                selectedCount: 25,
                symbolInfo: '600519 - 贵州茅台',
                progress: 100,
                timestamp: Date.now()
            },
            {
                taskId: testTaskId,
                stage: '选股完成',
                current: 100,
                total: 100,
                selectedCount: 25,
                symbolInfo: '',
                progress: 100,
                timestamp: Date.now()
            }
        ];
        
        console.log('[进度链路测试] 开始发送进度序列...');
        console.log('[进度链路测试] 请在前端观察进度浮窗是否正常显示');
        console.log('[进度链路测试] 如果前端有选股任务正在运行，请先停止');
        
        // 逐个发送进度消息
        for (let i = 0; i < progressSequence.length; i++) {
            const progressData = progressSequence[i];
            const channel = `stock_selection_progress:${testTaskId}`;
            
            console.log(`[进度链路测试] 发送进度 ${i + 1}/${progressSequence.length}: ${progressData.stage} - ${progressData.progress}%`);
            
            await publisher.publish(channel, JSON.stringify(progressData));
            
            // 等待一段时间再发送下一个进度
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log('[进度链路测试] 所有进度消息已发送完成');
        console.log('[进度链路测试] 请检查前端是否正确显示了进度更新');
        
        // 关闭连接
        await publisher.disconnect();
        console.log('[进度链路测试] 测试完成');
        
    } catch (error) {
        console.error('[进度链路测试] 测试失败:', error);
        process.exit(1);
    }
}

// 运行测试
testFullProgressChain();
