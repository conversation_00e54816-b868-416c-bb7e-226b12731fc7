#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的 TDX Socket.IO 测试客户端
用于快速验证服务器功能
"""

import socketio
import time
import sys

def main():
    print("TDX Socket.IO 简单测试客户端")
    print("=" * 40)
    
    # 创建客户端
    sio = socketio.Client()
    
    # 连接状态
    connected = False
    
    @sio.event
    def connect():
        global connected
        connected = True
        print("✓ 已连接到服务器")
    
    @sio.event
    def disconnect():
        global connected
        connected = False
        print("✗ 与服务器断开连接")
    
    @sio.event
    def connected(data):
        print(f"✓ 收到连接确认: {data.get('message', '')}")
    
    @sio.event
    def subscribed(data):
        print(f"✓ 订阅成功: {data.get('full_symbol', '')}")
    
    @sio.event
    def tick_data(data):
        symbol = data.get('symbol', 'Unknown')
        tick_info = data.get('data', {})
        price = tick_info.get('price', 0)
        volume = tick_info.get('volume', 0)
        tick_time = tick_info.get('time', '')
        
        print(f"📊 {symbol}: 价格={price}, 成交量={volume}, 时间={tick_time}")
    
    @sio.event
    def error(data):
        print(f"❌ 错误: {data.get('message', '')}")
    
    try:
        # 连接到服务器
        print("正在连接到 Socket.IO 服务器...")
        sio.connect('http://127.0.0.1:5004', wait_timeout=10)
        
        # 等待连接建立
        for i in range(10):
            if connected:
                break
            time.sleep(0.5)
        
        if not connected:
            print("❌ 连接超时")
            return
        
        print("\n开始测试订阅...")
        
        # 测试订阅
        test_symbols = [
            ('sh', '000001', '上证指数'),
            ('sz', '000001', '平安银行'),
        ]
        
        for market, symbol, name in test_symbols:
            print(f"订阅 {name} ({market}:{symbol})...")
            sio.emit('subscribe_tick', {
                'market': market,
                'symbol': symbol
            })
            time.sleep(1)
        
        print(f"\n等待 15 秒接收数据...")
        print("(按 Ctrl+C 提前结束)")
        
        # 等待数据
        for i in range(15):
            time.sleep(1)
            if i % 5 == 4:
                print(f"已等待 {i+1} 秒...")
        
        print("\n测试完成，断开连接...")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    finally:
        if connected:
            sio.disconnect()
        print("测试结束")

if __name__ == '__main__':
    main()
