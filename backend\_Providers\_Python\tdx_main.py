#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通达信数据处理工具主程序

整合了tdxsymbols.py和tdxreader1.py的功能，提供简单的命令行界面
"""

import os
import sys
import argparse
from pathlib import Path

# 确保能够导入同目录下的模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入功能模块
try:
    from tdxsymbols import read_stock_mapping, print_stock_list
    from tdxreader1 import read_day_data, export_to_csv, batch_process_stocks, get_stock_filepath
except ImportError as e:
    print(f"导入模块错误: {e}")
    print("请确保tdxsymbols.py和tdxreader1.py在同一目录下")
    sys.exit(1)

def setup_args():
    """设置命令行参数"""
    parser = argparse.ArgumentParser(description='通达信数据处理工具')
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='命令')
    
    # 列出股票代码的命令
    list_parser = subparsers.add_parser('list', help='列出股票代码')
    list_parser.add_argument('--market', choices=['sh', 'sz', 'all'], default='all', help='市场类型：沪市(sh)、深市(sz)或全部(all)')
    list_parser.add_argument('--limit', type=int, default=20, help='显示的股票数量限制')
    list_parser.add_argument('--tdx-path', default=r'D:\new_tdx\T0002\hq_cache', help='通达信安装路径')
    
    # 读取单个股票数据的命令
    read_parser = subparsers.add_parser('read', help='读取单个股票数据')
    read_parser.add_argument('--code', required=True, help='股票代码，如：600000')
    read_parser.add_argument('--market', choices=['sh', 'sz'], help='市场类型：沪市(sh)或深市(sz)，如不指定则自动判断')
    read_parser.add_argument('--data-dir', default='D:/projects/quantquart/stockdata', help='数据目录路径')
    read_parser.add_argument('--output', help='输出CSV文件路径')
    read_parser.add_argument('--limit', type=int, default=20, help='显示的数据条数限制')
    
    # 批量处理股票数据的命令
    batch_parser = subparsers.add_parser('batch', help='批量处理股票数据')
    batch_parser.add_argument('--market', choices=['sh', 'sz', 'all'], default='all', help='市场类型：沪市(sh)、深市(sz)或全部(all)')
    batch_parser.add_argument('--data-dir', default='D:/projects/quantquart/stockdata', help='数据目录路径')
    batch_parser.add_argument('--output-dir', default='D:/projects/quantquart/output', help='输出目录路径')
    batch_parser.add_argument('--limit', type=int, default=10, help='处理的股票数量限制')
    batch_parser.add_argument('--tdx-path', default=r'D:\new_tdx\T0002\hq_cache', help='通达信安装路径')
    
    return parser.parse_args()

def cmd_list_stocks(args):
    """列出股票代码命令处理"""
    tdx_base_path = args.tdx_path
    sh_file_path = os.path.join(tdx_base_path, "shs.tnf")
    sz_file_path = os.path.join(tdx_base_path, "szs.tnf")
    
    # 根据市场参数读取股票数据
    if args.market in ['sh', 'all']:
        sh_stocks = read_stock_mapping(sh_file_path)
        if sh_stocks:
            print_stock_list(sh_stocks, "沪市", args.limit if args.market == 'sh' else min(args.limit // 2, 10))
    
    if args.market in ['sz', 'all']:
        sz_stocks = read_stock_mapping(sz_file_path)
        if sz_stocks:
            print_stock_list(sz_stocks, "深市", args.limit if args.market == 'sz' else min(args.limit // 2, 10))
    
    # 显示总数
    if args.market == 'all':
        sh_count = len(read_stock_mapping(sh_file_path))
        sz_count = len(read_stock_mapping(sz_file_path))
        print(f"\n沪深两市共有 {sh_count + sz_count} 只股票")

def cmd_read_stock(args):
    """读取单个股票数据命令处理"""
    stock_code = args.code
    
    # 如果未指定市场，根据股票代码判断
    if not args.market:
        market = 'sh' if stock_code.startswith('6') else 'sz'
    else:
        market = args.market
    
    # 获取股票数据文件路径
    file_path = get_stock_filepath(args.data_dir, market, stock_code)
    
    if not os.path.exists(file_path):
        print(f"错误: 股票数据文件 '{file_path}' 不存在!")
        return
    
    print(f"开始读取 {market}{stock_code} 的数据文件...")
    data = read_day_data(file_path)
    
    if not data:
        print("未能读取到数据，请检查文件路径是否正确")
        return
    
    # 打印数据信息
    print(f"\n数据起始日期: {data[0]['date']}")
    print(f"数据结束日期: {data[-1]['date']}")
    print(f"数据总条数: {len(data)}")
    
    # 显示部分数据
    limit = min(args.limit, len(data))
    print(f"\n最近 {limit} 条数据:")
    print("日期\t\t开盘\t最高\t最低\t收盘\t成交量")
    print("-" * 70)
    for item in data[-limit:]:
        print(f"{item['date']}\t{item['open']:.2f}\t{item['high']:.2f}\t{item['low']:.2f}\t{item['close']:.2f}\t{item['vol']}")
    
    # 导出CSV文件
    if args.output:
        output_file = args.output
        export_to_csv(data, output_file, stock_code)

def cmd_batch_process(args):
    """批量处理股票数据命令处理"""
    tdx_base_path = args.tdx_path
    sh_file_path = os.path.join(tdx_base_path, "shs.tnf")
    sz_file_path = os.path.join(tdx_base_path, "szs.tnf")
    
    # 根据市场参数读取股票数据
    stocks = {}
    if args.market in ['sh', 'all']:
        sh_stocks = read_stock_mapping(sh_file_path)
        stocks.update(sh_stocks)
    
    if args.market in ['sz', 'all']:
        sz_stocks = read_stock_mapping(sz_file_path)
        stocks.update(sz_stocks)
    
    if not stocks:
        print("未能读取到股票列表，请检查通达信安装路径是否正确")
        return
    
    print(f"共读取到 {len(stocks)} 只股票")
    
    # 创建输出目录
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # 批量处理股票数据
    batch_process_stocks(stocks, args.data_dir, args.output_dir, args.limit)

def main():
    """主函数"""
    args = setup_args()
    
    if args.command == 'list':
        cmd_list_stocks(args)
    elif args.command == 'read':
        cmd_read_stock(args)
    elif args.command == 'batch':
        cmd_batch_process(args)
    else:
        print("请指定要执行的命令: list, read 或 batch")
        print("使用 --help 查看帮助信息")

if __name__ == "__main__":
    main() 