import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Drawer, Tag, Tooltip, List, FloatButton, Table } from 'antd';
import { LineChartOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { EventBus } from '@/events/eventBus';
import { ChartEvents, MarketEvents } from '@/events/events';
import './index.less';

// 使用已定义的类型而不是重新定义
type SignalItem = MarketEvents.SignalItem;
let signalsRef: SignalItem[] = [];
let titleRef: string = '';

const SignalList: React.FC<{
  onTimeSelect?: (timestamp: number, index: number) => void;
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}> = ({ onTimeSelect, visible = true, onVisibleChange }) => {

  const [signals, setSignals] = useState<SignalItem[]>([]);
  const [title, setTitle] = useState<string>('');
  

  // 处理信号数据 - 用于显示列表的数据保持倒序
  const handleSignalsResult = useCallback((payload: MarketEvents.SignalsCalculatedResultPayload) => {
    console.log('[信号列表] 处理信号数据:', payload);
    
    if (!payload.signals || payload.signals.length === 0) {
      signalsRef = [];
      titleRef = '';
      setSignals([]);
      setTitle('');
      return;
    }

    // 对信号数据进行倒序排列（仅用于显示）
    const sortedSignals = [...payload.signals].sort((a, b) => b.time - a.time);
    signalsRef = sortedSignals;
    titleRef = payload.title;
    setSignals(sortedSignals);
    setTitle(payload.title);
  }, []);

  // 安装监听
  useEffect(() => {
    const subscription = EventBus.on(
      MarketEvents.Types.SIGNALCALCULATED_RESULT,
      (payload: MarketEvents.SignalsCalculatedResultPayload) => {
        console.log('[SignalList] 收到信号结果:', payload);
        handleSignalsResult(payload);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // 监听 visible 变化
  useEffect(() => {

    if (visible && signalsRef.length > 0) {
      // 更新状态
      setSignals(signalsRef);
      setTitle(titleRef);
    }
  

  }, [visible]);

  // 在 visible 状态改变时发送事件
  useEffect(() => {
    if (signals.length > 0) {
      // 发送带有显示状态的事件
      if (visible) {
        // 给图表的数据需要按时间正序排列
        const orderedSignals = [...signals].sort((a, b) => a.time - b.time);
        EventBus.emit(MarketEvents.Types.SIGNAL_MARKERS_VISIBLE, {
          signals: orderedSignals,
          visible
        });
      }
    }
  }, [visible, signals]);

  const handleItemClick = useCallback((timestamp: number, index: number) => {
    // timestamp 是秒级时间戳，需要转换为毫秒级
    const milliseconds = timestamp * 1000;
    console.log('[SignalList] 点击信号项:', {
      originalSeconds: timestamp,
      milliseconds,
      date: new Date(milliseconds).toISOString(),
      index
    });
    /*if (onTimeSelect) {
      onTimeSelect(milliseconds, index);
    }*/

    // 发送走势图滚动事件，参数为 index
    EventBus.emit(ChartEvents.Types.CHART_SCROLL_TO_INDEX, { index });
  }, []);

  // 渲染信号类型标签
  const renderSignalTag = (type: string) => {
    const color = type === 'BUY' ? '#52c41a' : '#f5222d';
    return (
      <Tag color={color}>
        {type === 'BUY' ? '买入' : '卖出'}
      </Tag>
    );
  };

  // 定义表格列
  const columns: ColumnsType<SignalItem> = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type) => renderSignalTag(type),
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price) => (
        <span className="signal-price">
          {price.toFixed(2)}
        </span>
      ),
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      render: (time) => (
        <span className="signal-time">
          {new Date(time * 1000).toLocaleString()}
        </span>
      ),
    },
  ];

  return (
    <div className="signal-list-container">
      <h3 style={{ padding: '15px', margin: 0, borderBottom: '1px solid var(--border-color)' }}>{title == '' ? '信号列表' : title} ({signals.length})</h3>
      <div className="list-content">
        <Table
          columns={columns}
          dataSource={signals}
          rowKey="time"
          pagination={false}
          size="small"
          onRow={(record) => ({
            onClick: () => handleItemClick(record.time, record.index),
            className: 'signal-table-row'
          })}
          className="signal-table"
        />
      </div>
    </div>
  );
};

export default SignalList; 