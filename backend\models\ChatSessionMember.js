const { Sequelize, DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const ChatSessionMember = sequelize.define('ChatSessionMember', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    sessionid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'chatsessions',
        key: 'id'
      }
    },
    userid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    role: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'member'
    },
    lastreadat: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    tableName: 'chatsessionmembers',
    timestamps: false
  });

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  ChatSessionMember.associate = function(models) {
    // 一个会话成员属于一个会话
    ChatSessionMember.belongsTo(models.ChatSession, {
      foreignKey: 'sessionid',
      as: 'session'
    });
     // 一个会话成员属于一个用户
     ChatSessionMember.belongsTo(models.User, {
        foreignKey: 'userid',
        as: 'user'
      });
  };

  return ChatSessionMember;
}; 