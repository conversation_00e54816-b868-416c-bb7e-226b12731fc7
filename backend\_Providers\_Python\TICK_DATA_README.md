# TDX Tick数据扩展功能

## 概述

本扩展为TDX数据服务器添加了专业的tick数据获取功能，提供实时和历史tick数据的完整解决方案。

## 🏗️ 架构设计

### 逻辑隔离原则

为了保证代码的可维护性和模块化，tick数据功能采用了严格的逻辑隔离设计：

1. **独立模块**: `tick_data_handler.py` - 专门处理tick数据逻辑
2. **接口隔离**: 新增专用的REST API接口，不影响现有功能
3. **数据隔离**: 独立的缓存机制和数据处理流程
4. **连接复用**: 复用现有的TDX连接函数，避免重复连接

### 模块结构

```
backend/_Providers/_Python/
├── tdxserver.py              # 主服务器（已扩展）
├── tick_data_handler.py      # Tick数据处理模块（新增）
├── test_tick_data.py         # 测试脚本（新增）
└── TICK_DATA_README.md       # 说明文档（新增）
```

## 🔌 新增API接口

### 1. 实时Tick数据接口

**接口**: `GET /tick`

**参数**:
- `market` (必需): 市场代码 (如: sh, sz, hk, us, shfe)
- `code` (必需): 证券/合约代码

**响应示例**:
```json
{
  "code": "000001",
  "market": "sz",
  "name": "平安银行",
  "price": 12.34,
  "last_close": 12.30,
  "open": 12.32,
  "high": 12.45,
  "low": 12.28,
  "volume": 1234567,
  "amount": 15234567.89,
  "bid1": 12.33,
  "bid_vol1": 1000,
  "ask1": 12.34,
  "ask_vol1": 800,
  "bid2": 12.32,
  "bid_vol2": 1500,
  "ask2": 12.35,
  "ask_vol2": 1200,
  "timestamp": 1703123456789,
  "time": "14:30:15",
  "date": "2023-12-21",
  "datetime": "2023-12-21 14:30:15"
}
```

### 2. 历史Tick数据接口

**接口**: `GET /tick/history`

**参数**:
- `market` (必需): 市场代码
- `code` (必需): 证券/合约代码  
- `date` (可选): 日期 (YYYY-MM-DD格式)，默认今天

**响应示例**:
```json
[
  {
    "code": "000001",
    "market": "sz",
    "price": 12.34,
    "volume": 100,
    "amount": 1234.0,
    "type": "B",
    "timestamp": 1703123456789,
    "time": "09:30:03",
    "date": "2023-12-21",
    "datetime": "2023-12-21 09:30:03"
  },
  // ... 更多tick数据
]
```

## 🔧 核心功能特性

### 1. 智能缓存机制
- 1秒缓存过期时间，平衡实时性和性能
- 自动清理过期缓存
- 按品种独立缓存

### 2. 多市场支持
- **A股市场**: 上海(sh)、深圳(sz)、北京(bj)
- **港股市场**: 香港(hk)
- **美股市场**: 美国(us)
- **期货市场**: 上期所(shfe)、大商所(dce)、郑商所(czce)、中金所(cffex)等

### 3. 数据格式标准化
- 统一的tick数据格式
- 完整的五档买卖盘信息
- 标准化的时间戳处理
- 上海时区时间转换

### 4. 错误处理和容错
- 完善的异常处理机制
- 连接自动断开和清理
- 详细的日志记录
- 优雅的错误响应

## 🚀 使用方法

### 1. 启动服务器

```bash
cd backend/_Providers/_Python/
python tdxserver.py
```

服务器将在以下端口启动：
- HTTP API: `http://127.0.0.1:5003`
- Socket.IO: `http://127.0.0.1:5004`

### 2. 测试功能

```bash
# 运行测试脚本
python test_tick_data.py
```

### 3. API调用示例

```python
import requests

# 获取平安银行实时tick数据
response = requests.get('http://127.0.0.1:5003/tick', params={
    'market': 'sz',
    'code': '000001'
})
tick_data = response.json()

# 获取历史tick数据
response = requests.get('http://127.0.0.1:5003/tick/history', params={
    'market': 'sz',
    'code': '000001',
    'date': '2023-12-21'
})
history_ticks = response.json()
```

### 4. Socket.IO实时推送

现有的Socket.IO实时推送功能已自动使用新的tick数据处理器，无需额外配置。

## 📊 数据字段说明

### 实时Tick数据字段

| 字段 | 类型 | 说明 |
|------|------|------|
| code | string | 证券代码 |
| market | string | 市场代码 |
| name | string | 证券名称 |
| price | float | 最新价 |
| last_close | float | 昨收价 |
| open | float | 开盘价 |
| high | float | 最高价 |
| low | float | 最低价 |
| volume | int | 成交量 |
| amount | float | 成交额 |
| bid1-bid5 | float | 买一到买五价格 |
| bid_vol1-bid_vol5 | int | 买一到买五数量 |
| ask1-ask5 | float | 卖一到卖五价格 |
| ask_vol1-ask_vol5 | int | 卖一到卖五数量 |
| timestamp | int | 毫秒时间戳 |
| time | string | 时间 (HH:MM:SS) |
| date | string | 日期 (YYYY-MM-DD) |
| datetime | string | 日期时间 |

### 历史Tick数据字段

| 字段 | 类型 | 说明 |
|------|------|------|
| code | string | 证券代码 |
| market | string | 市场代码 |
| price | float | 成交价格 |
| volume | int | 成交数量 |
| amount | float | 成交金额 |
| type | string | 买卖类型 (B/S) |
| timestamp | int | 毫秒时间戳 |
| time | string | 时间 |
| date | string | 日期 |
| datetime | string | 日期时间 |

## ⚠️ 注意事项

1. **依赖通达信客户端**: 需要通达信客户端(tdxw.exe)正在运行
2. **市场时间限制**: 历史tick数据获取受交易时间限制
3. **数据可用性**: 不同市场的数据可用性可能不同
4. **请求频率**: 建议控制请求频率，避免过于频繁的调用
5. **缓存机制**: 实时数据有1秒缓存，高频调用会返回缓存数据

## 🔍 故障排除

### 常见问题

1. **连接失败**: 确保通达信客户端正在运行且网络连接正常
2. **数据为空**: 检查市场代码和证券代码是否正确
3. **历史数据获取失败**: 某些市场可能不支持历史tick数据
4. **服务器启动失败**: 检查端口5003和5004是否被占用

### 日志查看

服务器运行时会输出详细的日志信息，包括：
- 连接状态
- 数据获取过程
- 错误信息
- 性能统计

## 🔄 扩展性

该tick数据模块设计具有良好的扩展性：

1. **新增数据源**: 可以轻松添加其他数据源支持
2. **数据格式扩展**: 可以扩展更多的tick数据字段
3. **缓存策略**: 可以调整缓存策略和过期时间
4. **API接口**: 可以添加更多专业的tick数据接口

通过这种模块化和隔离的设计，tick数据功能可以独立维护和升级，不会影响现有的系统功能。
