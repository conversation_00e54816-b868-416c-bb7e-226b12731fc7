import React, { useState, useEffect, useReducer } from 'react';
import { Modal, Form, Input, Upload, Avatar, message, Space, Radio } from 'antd';
import {
  UserOutlined, LoadingOutlined, PlusOutlined,
  SmileOutlined, RocketOutlined, StarOutlined,
  HeartOutlined, TrophyOutlined, CrownOutlined
} from '@ant-design/icons';
import type { UploadChangeParam } from 'antd/es/upload';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import { getUserInfo, getToken } from '@/utils/auth';
import { EventBus } from '@/events/eventBus';
import { UserEvents } from '@/events/events';
import { useUser } from '@/models/useUser';

interface UserInfoModalProps {
  open: boolean;
  onCancel: () => void;
  userInfo: {
    username?: string;
    avatar?: string;
  };
}

const UserInfoModal: React.FC<UserInfoModalProps> = ({ open, onCancel, userInfo }) => {
  const [, forceUpdate] = useReducer(x => x + 1, 0);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(
    userInfo.avatar ? userInfo.avatar.split('/').pop() || '' : ''
  );
  const { setUserInfo } = useUser();

  // 当 Modal 打开时，设置初始头像
  useEffect(() => {
    console.log("UserInfoModal 的 userInfo：", userInfo);
    if (open && userInfo.avatar) {
      setImageUrl(userInfo.avatar.split('/').pop() || '');
    }
  }, [open, userInfo.avatar]);

  useEffect(() => {
    console.log('UserInfoModal装载完成');
  }, [open, userInfo.avatar]);

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片！');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片必须小于 2MB！');
      return false;
    }

    console.log('上传之前：getUserInfo()：', getUserInfo());
    return true;
  };

  const handleChange: UploadProps['onChange'] = (info: UploadChangeParam<UploadFile>) => {
    console.log('handleChange 的 info：', info);

    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      setLoading(false);
      setImageUrl(info.file.response.url.split('/').pop() || '');
      message.success('头像上传成功！');
    }
  };

  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>上传头像</div>
    </div>
  );

  const handleFinish = async (values: any) => {
    try {
      if (imageUrl) {
        values.avatar = imageUrl;
        console.log("设置了头像，赋值给values.avatar，文件名：", imageUrl);
      }

      // 发布更新事件
      EventBus.emit(UserEvents.Types.UPDATE, values);
      setUserInfo(values);
      onCancel();
    } catch (error) {
      message.error('保存失败，请重试');
    }
  };

  return (
    <Modal
      title="用户信息"
      open={open}
      onCancel={onCancel}
      okText="保存"
      cancelText="取消"
      onOk={() => form.submit()}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={userInfo}
        onFinish={handleFinish}
        autoComplete="off"
      >
        <Form.Item label="头像">
          <Upload
            name="avatar"
            listType="picture-circle"
            showUploadList={false}
            action="/api/user/avatar"
            beforeUpload={beforeUpload}
            onChange={handleChange}
            headers={{
              Authorization: `Bearer ${getToken()}`,
              'Accept': 'application/json'
            }}
          >
            {imageUrl ? (
              <Avatar
                size={100}
                src={imageUrl ? `/uploads/avatars/${imageUrl}` : undefined}
              />
            ) : (
              uploadButton
            )}
          </Upload>
        </Form.Item>

        <Form.Item
          label="用户名"
          name="username"
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          label="修改密码"
          name="password"
          rules={[
            { min: 6, message: '密码至少6位' },
            { max: 20, message: '密码最多20位' }
          ]}
        >
          <Input.Password
            placeholder="输入新密码，不修改请留空"
            autoComplete="new-password"
          />
        </Form.Item>

        <Form.Item
          label="确认密码"
          name="confirmPassword"
          dependencies={['password']}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password
            placeholder="再次输入新密码"
            autoComplete="new-password"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UserInfoModal; 