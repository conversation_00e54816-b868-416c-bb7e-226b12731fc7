#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试除权数据处理功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from tdx_kline_lib import get_dividend_data, apply_forward_adjustment, get_stock_kline

def test_dividend_data():
    """测试获取除权数据"""
    print("=== 测试获取除权数据 ===")

    # 测试获取浦发银行的除权数据
    symbol = "600000"
    exchange = "SSE"

    try:
        dividend_data = get_dividend_data(symbol, exchange, start_date="2015-01-01", end_date="2024-12-31")

        if dividend_data:
            print(f"成功获取到 {len(dividend_data)} 条除权记录:")
            for i, data in enumerate(dividend_data):  # 显示所有记录
                print(f"  {i+1}. 日期: {data['date']}, 前复权因子: {data['fore_adjust_factor']:.6f}, "
                      f"后复权因子: {data['back_adjust_factor']:.6f}")
        else:
            print("未获取到除权数据")

        return dividend_data

    except Exception as e:
        print(f"获取除权数据出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def test_manual_adjustment():
    """测试手动复权计算"""
    print("\n=== 测试手动复权计算 ===")

    # 模拟一些K线数据
    mock_klines = [
        {'time': 1495555200, 'open': 15.30, 'high': 15.50, 'low': 15.20, 'close': 15.47, 'volume': 1000000, 'amount': 15470000},  # 2017-05-24
        {'time': 1495641600, 'open': 11.75, 'high': 13.00, 'low': 11.70, 'close': 12.93, 'volume': 2000000, 'amount': 25860000},  # 2017-05-25 (除权日)
        {'time': 1495728000, 'open': 12.81, 'high': 12.90, 'low': 12.80, 'close': 12.84, 'volume': 1500000, 'amount': 19260000},  # 2017-05-26
    ]

    # 模拟除权数据（基于baostock文档的浦发银行例子）
    mock_dividend_data = [
        {'date': '2016-06-23', 'timestamp': 1466611200, 'fore_adjust_factor': 0.759535, 'back_adjust_factor': 7.128788, 'adjust_factor': 0.759535},
        {'date': '2017-05-25', 'timestamp': 1495641600, 'fore_adjust_factor': 1.0, 'back_adjust_factor': 9.385732, 'adjust_factor': 1.0},
    ]

    print("原始K线数据:")
    for i, kline in enumerate(mock_klines):
        print(f"  {i+1}. 时间: {kline['time']}, 收盘价: {kline['close']:.2f}")

    print("\n除权信息:")
    for i, div in enumerate(mock_dividend_data):
        print(f"  {i+1}. 日期: {div['date']}, 前复权因子: {div['fore_adjust_factor']:.6f}")

    # 应用前复权
    adjusted_klines = apply_forward_adjustment(mock_klines, mock_dividend_data)

    print("\n前复权后K线数据:")
    for i, kline in enumerate(adjusted_klines):
        print(f"  {i+1}. 时间: {kline['time']}, 收盘价: {kline['close']:.6f}")

def test_kline_with_adjustment():
    """测试带除权处理的K线数据获取"""
    print("\n=== 测试带除权处理的K线数据获取 ===")

    symbol = "600000"
    exchange = "SSE"

    try:
        # 获取最近10条日线数据（不进行除权处理）
        print("获取原始K线数据（最近10条）...")
        original_klines = get_stock_kline(symbol, period='1D', market='STOCK', exchange=exchange, k_count=10)

        if original_klines:
            print(f"原始数据最后一条: 时间={original_klines[-1]['time']}, 收盘价={original_klines[-1]['close']:.2f}")
            print(f"原始数据第一条: 时间={original_klines[0]['time']}, 收盘价={original_klines[0]['close']:.2f}")

        # 获取完整数据（会进行除权处理）
        print("获取完整K线数据（包含除权处理）...")
        adjusted_klines = get_stock_kline(symbol, period='1D', market='STOCK', exchange=exchange, k_count=0)

        if adjusted_klines:
            print(f"复权数据最后一条: 时间={adjusted_klines[-1]['time']}, 收盘价={adjusted_klines[-1]['close']:.2f}")
            print(f"复权数据总条数: {len(adjusted_klines)}")
            if len(adjusted_klines) >= 10:
                print(f"复权数据倒数第10条: 时间={adjusted_klines[-10]['time']}, 收盘价={adjusted_klines[-10]['close']:.2f}")

    except Exception as e:
        print(f"获取K线数据出错: {str(e)}")
        import traceback
        traceback.print_exc()

def test_minute_kline_adjustment():
    """测试分钟线除权处理"""
    print("\n=== 测试分钟线除权处理 ===")

    symbol = "600000"
    exchange = "SSE"

    try:
        # 获取最近100条5分钟线数据（不进行除权处理）
        print("获取原始5分钟线数据（最近100条）...")
        original_klines = get_stock_kline(symbol, period='5m', market='STOCK', exchange=exchange, k_count=100)

        if original_klines:
            print(f"原始5分钟线数据最后一条: 时间={original_klines[-1]['time']}, 收盘价={original_klines[-1]['close']:.2f}")

        # 获取完整5分钟线数据（会进行除权处理）
        print("获取完整5分钟线数据（包含除权处理）...")
        adjusted_klines = get_stock_kline(symbol, period='5m', market='STOCK', exchange=exchange, k_count=0)

        if adjusted_klines:
            print(f"复权5分钟线数据最后一条: 时间={adjusted_klines[-1]['time']}, 收盘价={adjusted_klines[-1]['close']:.2f}")
            print(f"复权5分钟线数据总条数: {len(adjusted_klines)}")

    except Exception as e:
        print(f"获取5分钟线数据出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 设置调试模式
    os.environ['TDX_DEBUG'] = 'true'

    # 测试除权数据获取
    dividend_data = test_dividend_data()

    # 测试手动复权计算
    test_manual_adjustment()

    # 测试实际K线数据复权
    test_kline_with_adjustment()

    # 测试分钟线复权
    test_minute_kline_adjustment()
