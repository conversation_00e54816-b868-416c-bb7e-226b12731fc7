#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试除权数据处理功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from tdx_kline_lib import get_dividend_data, apply_forward_adjustment, get_stock_kline

def test_dividend_data():
    """测试获取除权数据"""
    print("=== 测试获取除权数据 ===")
    
    # 测试获取浦发银行的除权数据
    symbol = "600000"
    exchange = "SSE"
    
    try:
        dividend_data = get_dividend_data(symbol, exchange, start_date="2020-01-01", end_date="2024-12-31")
        
        if dividend_data:
            print(f"成功获取到 {len(dividend_data)} 条除权记录:")
            for i, data in enumerate(dividend_data[:5]):  # 只显示前5条
                print(f"  {i+1}. 日期: {data['date']}, 前复权因子: {data['fore_adjust_factor']:.6f}")
        else:
            print("未获取到除权数据")
            
    except Exception as e:
        print(f"获取除权数据出错: {str(e)}")
        import traceback
        traceback.print_exc()

def test_kline_with_adjustment():
    """测试带除权处理的K线数据获取"""
    print("\n=== 测试带除权处理的K线数据获取 ===")
    
    symbol = "600000"
    exchange = "SSE"
    
    try:
        # 获取最近100条日线数据（不进行除权处理）
        print("获取原始K线数据（最近100条）...")
        original_klines = get_stock_kline(symbol, period='1D', market='STOCK', exchange=exchange, k_count=100)
        
        if original_klines:
            print(f"原始数据最后一条: 时间={original_klines[-1]['time']}, 收盘价={original_klines[-1]['close']:.2f}")
        
        # 获取完整数据（会进行除权处理）
        print("获取完整K线数据（包含除权处理）...")
        adjusted_klines = get_stock_kline(symbol, period='1D', market='STOCK', exchange=exchange, k_count=0)
        
        if adjusted_klines:
            print(f"复权数据最后一条: 时间={adjusted_klines[-1]['time']}, 收盘价={adjusted_klines[-1]['close']:.2f}")
            print(f"复权数据总条数: {len(adjusted_klines)}")
        
    except Exception as e:
        print(f"获取K线数据出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 设置调试模式
    os.environ['TDX_DEBUG'] = 'true'
    
    test_dividend_data()
    test_kline_with_adjustment()
