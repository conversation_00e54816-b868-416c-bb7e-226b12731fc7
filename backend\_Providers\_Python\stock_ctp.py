# -*- coding: utf-8 -*-
"""
专门用于股票行情的CTP程序
模仿minitrader的实现方式
"""
from openctp_ctp import thostmduserapi as mdapi
import time

class StockMdSpi(mdapi.CThostFtdcMdSpi):
    def __init__(self):
        super().__init__()
        self.api = None

    def OnFrontConnected(self):
        print("股票行情服务器连接成功")
        # 股票行情登录
        req = mdapi.CThostFtdcReqUserLoginField()
        req.BrokerID = ""
        req.UserID = ""
        req.Password = ""
        self.api.ReqUserLogin(req, 1)

    def OnRspUserLogin(self, pRspUserLogin, pRspInfo, nRequestID, bIsLast):
        if pRspInfo and pRspInfo.ErrorID != 0:
            print(f"登录失败: {pRspInfo.ErrorMsg}")
            return
            
        print("股票行情登录成功")
        print(f"交易日: {pRspUserLogin.TradingDay}")
        
        # 只订阅600000浦发银行
        print("订阅600000浦发银行...")
        instruments = ["600000"]
        self.api.SubscribeMarketData([code.encode('utf-8') for code in instruments], len(instruments))

    def OnRspSubMarketData(self, pSpecificInstrument, pRspInfo, nRequestID, bIsLast):
        if pRspInfo and pRspInfo.ErrorID != 0:
            print(f"订阅失败: {pSpecificInstrument.InstrumentID} - {pRspInfo.ErrorMsg}")
        else:
            print(f"订阅成功: {pSpecificInstrument.InstrumentID}")
            
        if bIsLast:
            print("股票订阅完成，等待行情数据...")

    def OnRtnDepthMarketData(self, pDepthMarketData):
        """收到行情数据"""
        symbol = pDepthMarketData.InstrumentID
        last_price = pDepthMarketData.LastPrice
        volume = pDepthMarketData.Volume
        turnover = pDepthMarketData.Turnover
        
        # 显示关键行情信息
        print(f"[{symbol}] 最新价:{last_price:.2f} 成交量:{volume} 成交额:{turnover:.0f}")
        
        # 显示买卖盘
        if hasattr(pDepthMarketData, 'BidPrice1') and pDepthMarketData.BidPrice1 > 0:
            print(f"  买一:{pDepthMarketData.BidPrice1:.2f}({pDepthMarketData.BidVolume1}) "
                  f"卖一:{pDepthMarketData.AskPrice1:.2f}({pDepthMarketData.AskVolume1})")

def main():
    print("启动股票行情客户端...")
    
    # 创建API
    api = mdapi.CThostFtdcMdApi.CreateFtdcMdApi()
    spi = StockMdSpi()
    spi.api = api
    
    # 连接服务器
    api.RegisterSpi(spi)
    api.RegisterFront("tcp://***************:10211")  # SimNow行情前置
    api.Init()
    
    print("等待连接和行情数据...")
    
    # 保持运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("程序退出")
        api.Release()

if __name__ == "__main__":
    main()
