import pandas as pd
import numpy as np
from typing import List, Dict, Any

def select_stock(klines: List[Dict[str, Any]], params: Dict[str, Any]) -> bool:
    """
    均线交叉策略
    
    策略逻辑：
    1. 计算短期和长期移动平均线
    2. 当短期均线上穿长期均线时（金叉），选中该股票
    3. 当短期均线下穿长期均线时（死叉），不选中该股票
    
    Args:
        klines: K线数据列表
        params: 策略参数，包含：
            - ma_short: 短期均线周期，默认5
            - ma_long: 长期均线周期，默认20
            
    Returns:
        bool: True表示选中，False表示不选中
    """
    if len(klines) < 20:
        return False
    
    try:
        # 转换为DataFrame
        df = pd.DataFrame(klines)
        
        # 获取策略参数
        ma_short = params.get('ma_short', 5)
        ma_long = params.get('ma_long', 20)
        
        # 确保有足够的数据
        if len(df) < ma_long:
            return False
        
        # 计算均线
        df['ma_short'] = df['close'].rolling(window=ma_short).mean()
        df['ma_long'] = df['close'].rolling(window=ma_long).mean()
        
        # 检查是否有足够的数据计算均线
        if df['ma_short'].isna().any() or df['ma_long'].isna().any():
            return False
        
        # 获取最新和前一期的数据
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        
        # 金叉条件：短期均线上穿长期均线
        if (latest['ma_short'] > latest['ma_long'] and 
            prev['ma_short'] <= prev['ma_long']):
            return True
        
        return False
        
    except Exception as e:
        print(f"[均线交叉策略] 策略执行出错: {str(e)}")
        return False

def get_required_klines() -> int:
    """
    获取策略需要的K线数量
    
    Returns:
        int: 需要的K线数量（至少20条）
    """
    return 20 