// frontend/src/_Dispatchers/LiveStrategyDispatcher.ts
import { EventBus } from '../events/eventBus';
import { StrategyEvents } from '../events/events';
import axios from 'axios';
import { getToken, getUserInfo } from '@/utils/auth';
import {
  mockDeployToLive,
  mockGetLiveStrategies,
  mockStartLiveStrategy,
  mockStopLiveStrategy
} from './MockLiveStrategyData';
import { LiveStrategyInfo } from '@/shared_types/trade';

// 是否使用模拟数据（当后端API未实现时）
const USE_MOCK_DATA = false;

// 模拟的用户配置存储
const mockUserConfigs = new Map<string, StrategyEvents.TradingChannelConfig[]>();

// 定义后端 API 的基础路径
const BACKEND_API_BASE = '/api';

// --- 新增: 获取可用交易通道API响应类型 ---
interface GetTradingChannelsApiResponse {
  success: boolean;
  data?: StrategyEvents.AvailableTradingChannel[];
  error?: string;
}

// 部署到实盘API响应类型
interface DeployToLiveApiResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// 获取实盘策略列表API响应类型
interface GetLiveStrategiesApiResponse {
  success: boolean;
  data?: LiveStrategyInfo[];
  error?: string;
}

// 控制实盘策略API响应类型
interface ControlLiveStrategyApiResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// 删除实盘策略API响应类型
interface DeleteLiveStrategyApiResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// 通用API响应类型
interface ApiResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// --- 新增: 进程健康检查API响应类型 ---
interface ProcessActivityApiResponse {
  success: boolean;
  data?: {
    processInMemory: boolean;
    processAlive: boolean;
    redisResponse: boolean;
    snapshotExists: boolean;
    timestamp: string;
  };
  error?: string;
}

/**
 * 处理部署策略到实盘请求
 */
async function handleDeployToLive(payload: StrategyEvents.DeployToLivePayload) {
  console.log('[实盘分发器] 收到部署策略到实盘事件:', payload.config.strategyId);
  const { config, callback } = payload;

  if (!config.strategyId) {
    console.error('[实盘分发器] 部署到实盘事件缺少策略ID');
    if (callback) callback(false, '缺少策略ID');
    return;
  }

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法部署策略');
    if (callback) callback(false, '用户未认证');
    return;
  }

  try {
    // 使用模拟数据或真实API
    if (USE_MOCK_DATA) {
      console.log('[实盘分发器] 使用模拟数据部署策略:', config.strategyId);

      const result = await mockDeployToLive(config);

      if (result.success) {
        console.log('[实盘分发器] 策略部署成功(模拟):', result.message);
        // 通知策略列表更新
        EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
        if (callback) callback(true, result.message);
      } else {
        console.error('[实盘分发器] 策略部署失败(模拟):', result.message);
        if (callback) callback(false, result.message || '部署失败');
      }
    } else {
      // 真实API调用
      const apiUrl = `${BACKEND_API_BASE}/strategy/deploy`;
      console.log(`[实盘分发器] 调用API: POST ${apiUrl}`, config);

      const response = await axios.post<DeployToLiveApiResponse>(apiUrl, config, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success) {
        console.log('[实盘分发器] 策略部署成功:', response.data.message);
        // 通知策略列表更新
        EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
        if (callback) callback(true, response.data.message);
      } else {
        console.error('[实盘分发器] 策略部署失败:', response.data?.error || '未知错误');
        if (callback) callback(false, response.data?.error || '部署失败');
      }
    }
  } catch (error: any) {
    console.error('[实盘分发器] 请求部署策略时出错:', error);
    // 安全地提取错误信息
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      // 处理Axios错误
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }

    console.error('[实盘分发器] 错误详情:', errorMsg);
    if (callback) callback(false, errorMsg);
  }
}

/**
 * 处理获取实盘策略列表请求
 */
async function handleGetLiveStrategies(payload: StrategyEvents.GetLiveStrategiesPayload) {
  console.log('[实盘分发器] 收到获取实盘策略列表事件');
  const { callback } = payload;

  if (!callback || typeof callback !== 'function') {
    console.error('[实盘分发器] 获取实盘策略列表事件缺少有效的回调函数');
    return;
  }

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法获取实盘策略列表');
    callback([]);
    return;
  }

  try {
    // 使用模拟数据或真实API
    if (USE_MOCK_DATA) {
      console.log('[实盘分发器] 使用模拟数据获取实盘策略列表');

      const result = await mockGetLiveStrategies();

      if (result.success) {
        console.log('[实盘分发器] 成功获取实盘策略列表(模拟):', result.data);
        callback(result.data);
      } else {
        console.error('[实盘分发器] 获取实盘策略列表失败(模拟)');
        callback([]);
      }
    } else {
      // 真实API调用
      const apiUrl = `${BACKEND_API_BASE}/strategy/live`;
      console.log(`[实盘分发器] 调用API: GET ${apiUrl}`);

      const response = await axios.get<GetLiveStrategiesApiResponse>(apiUrl, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        console.log('[实盘分发器] 成功获取实盘策略列表:', response.data.data);
        callback(response.data.data);
      } else {
        console.error('[实盘分发器] 获取实盘策略列表失败:', response.data?.error || '响应格式不符合预期');
        callback([]);
      }
    }
  } catch (error: any) {
    console.error('[实盘分发器] 请求实盘策略列表时出错:', error);
    // 安全地提取错误信息
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      // 处理Axios错误
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }

    console.error('[实盘分发器] 错误详情:', errorMsg);
    // 返回空数组，但在控制台显示错误信息
    callback([]);
  }
}

/**
 * 处理启动实盘策略请求
 */
async function handleStartLiveStrategy(payload: StrategyEvents.ControlLiveStrategyPayload) {
  console.log('[实盘分发器] 收到启动实盘策略事件:', payload.liveStrategyId);
  const { liveStrategyId, callback } = payload;

  if (!liveStrategyId) {
    console.error('[实盘分发器] 启动实盘策略事件缺少策略ID');
    if (callback) callback(false, '缺少策略ID');
    return;
  }

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法启动策略');
    if (callback) callback(false, '用户未认证');
    return;
  }

  try {
    // 使用模拟数据或真实API
    /*if (USE_MOCK_DATA) {
      console.log('[实盘分发器] 使用模拟数据启动策略:', liveStrategyId);

      const result = await mockStartLiveStrategy(liveStrategyId);

      if (result.success) {
        console.log('[实盘分发器] 策略启动成功(模拟):', result.message);
        // 通知策略列表更新
        EventBus.emit(StrategyEvents.Types.LIVE_GROUP_UPDATED, undefined);
        if (callback) callback(true, result.message);
      } else {
        console.error('[实盘分发器] 策略启动失败(模拟):', result.message);
        if (callback) callback(false, result.message || '启动失败');
      }
    } else {*/
      // 真实API调用
      const apiUrl = `${BACKEND_API_BASE}/strategy/live/start/${liveStrategyId}`;
      console.log(`[实盘分发器] 调用API: POST ${apiUrl}`);

      const response = await axios.post<ControlLiveStrategyApiResponse>(apiUrl, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success) {
        console.log('[实盘分发器] 策略启动成功:', response.data.message);
        // 通知策略列表更新
        EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
        if (callback) callback(true, response.data.message);
      } else {
        console.error('[实盘分发器] 策略启动失败:', response.data?.error || '未知错误');
        if (callback) callback(false, response.data?.error || '启动失败');
      }
    //}
  } catch (error: any) {
    console.error('[实盘分发器] 请求启动策略时出错:', error);
    // 安全地提取错误信息
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      // 处理Axios错误
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }

    console.error('[实盘分发器] 错误详情:', errorMsg);
    if (callback) callback(false, errorMsg);
  }
}

/**
 * 处理停止实盘策略请求
 */
async function handleStopLiveStrategy(payload: StrategyEvents.ControlLiveStrategyPayload) {
  console.log('[实盘分发器] 收到停止实盘策略事件:', payload.liveStrategyId);
  const { liveStrategyId, callback } = payload;

  if (!liveStrategyId) {
    console.error('[实盘分发器] 停止实盘策略事件缺少策略ID');
    if (callback) callback(false, '缺少策略ID');
    return;
  }

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法停止策略');
    if (callback) callback(false, '用户未认证');
    return;
  }

  try {
    // 使用模拟数据或真实API
    if (USE_MOCK_DATA) {
      console.log('[实盘分发器] 使用模拟数据停止策略:', liveStrategyId);

      const result = await mockStopLiveStrategy(liveStrategyId);

      if (result.success) {
        console.log('[实盘分发器] 策略停止成功(模拟):', result.message);
        // 通知策略列表更新
        EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
        if (callback) callback(true, result.message);
      } else {
        console.error('[实盘分发器] 策略停止失败(模拟):', result.message);
        if (callback) callback(false, result.message || '停止失败');
      }
    } else {
      // 真实API调用
      const apiUrl = `${BACKEND_API_BASE}/strategy/live/stop/${liveStrategyId}`;
      console.log(`[实盘分发器] 调用API: POST ${apiUrl}`);

      const response = await axios.post<ControlLiveStrategyApiResponse>(apiUrl, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success) {
        console.log('[实盘分发器] 策略停止成功:', response.data.message);
        // 通知策略列表更新
        EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
        if (callback) callback(true, response.data.message);
      } else {
        console.error('[实盘分发器] 策略停止失败:', response.data?.error || '未知错误');
        if (callback) callback(false, response.data?.error || '停止失败');
      }
    }
  } catch (error: any) {
    console.error('[实盘分发器] 请求停止策略时出错:', error);
    // 安全地提取错误信息
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      // 处理Axios错误
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }

    console.error('[实盘分发器] 错误详情:', errorMsg);
    if (callback) callback(false, errorMsg);
  }
}

// --- 新增: 处理获取可用交易通道请求 ---
async function handleGetAvailableTradingChannels(payload: StrategyEvents.GetAvailableTradingChannelsPayload) {
  console.log('[实盘分发器] 收到获取可用交易通道事件');
  const { callback } = payload;

  if (!callback || typeof callback !== 'function') {
    console.error('[实盘分发器] 获取可用交易通道事件缺少有效的回调函数');
    return;
  }

  const token = getToken();
  const userInfo = getUserInfo();

  if (!token || !userInfo || !userInfo.username) {
    console.error('[实盘分发器] 未找到认证Token或用户信息，无法获取可用交易通道');
    callback(false, []);
    return;
  }

  try {
    // 使用模拟数据或真实API
    if (USE_MOCK_DATA) {
      // 模拟数据
      console.log('[实盘分发器] 使用模拟数据获取可用交易通道');

      // 获取用户的配置
      const userConfigs = mockUserConfigs.get(userInfo.username) || [];

      // 检查各个通道是否已配置
      const openctpConfigured = userConfigs.some(config => config.channelType === 'openctp');
      const miniQMTConfigured = userConfigs.some(config => config.channelType === 'miniQMT');

      // 简单的模拟数据
      const mockChannels: StrategyEvents.AvailableTradingChannel[] = [
        {
          id: `${userInfo.username}_easytrader_ths`,
          name: `同花顺 (${userInfo.username})`,
          type: 'easytrader_ths',
          status: 'online',
          needsConfig: false
        },
        {
          id: 'openctp',
          name: 'OpenCTP期货交易',
          type: 'openctp',
          status: 'online',
          needsConfig: true,
          configured: openctpConfigured
        },
        {
          id: 'miniQMT',
          name: 'miniQMT量化交易',
          type: 'miniQMT',
          status: 'online',
          needsConfig: true,
          configured: miniQMTConfigured
        }
      ];

      console.log('[实盘分发器] 成功获取可用交易通道(模拟):', mockChannels);
      callback(true, mockChannels);
    } else {
      // 真实API调用 - 使用正确的API端点
      const apiUrl = `${BACKEND_API_BASE}/strategy/trading_channels`;
      console.log(`[实盘分发器] 调用API: GET ${apiUrl}`);

      const response = await axios.get<GetTradingChannelsApiResponse>(apiUrl, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        console.log('[实盘分发器] 成功获取可用交易通道:', response.data.data);
        callback(true, response.data.data);
      } else {
        console.error('[实盘分发器] 获取可用交易通道失败:', response.data?.error || '响应格式不符合预期');
        callback(false, []);
      }
    }
  } catch (error: any) {
    console.error('[实盘分发器] 请求可用交易通道时出错:', error);
    // (错误处理逻辑同其他请求)
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }
    console.error('[实盘分发器] 错误详情:', errorMsg);
    callback(false, []);
  }
}

/**
 * 处理删除实盘策略请求
 */
async function handleDeleteLiveStrategy(payload: StrategyEvents.ControlLiveStrategyPayload) {
  console.log('[实盘分发器] 收到删除实盘策略事件:', payload.liveStrategyId);
  const { liveStrategyId, callback } = payload;

  if (!liveStrategyId) {
    console.error('[实盘分发器] 删除实盘策略事件缺少策略ID');
    if (callback) callback(false, '缺少策略ID');
    return;
  }

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法删除策略');
    if (callback) callback(false, '用户未认证');
    return;
  }

  try {
    // 真实API调用
    const apiUrl = `${BACKEND_API_BASE}/strategy/live/delete/${liveStrategyId}`;
    console.log(`[实盘分发器] 调用API: DELETE ${apiUrl}`);

    const response = await axios.delete<DeleteLiveStrategyApiResponse>(apiUrl, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data && response.data.success) {
      console.log('[实盘分发器] 策略删除成功:', response.data.message);
      // 通知策略列表更新
      EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
      if (callback) callback(true, response.data.message);
    } else {
      console.error('[实盘分发器] 策略删除失败:', response.data?.error || '未知错误');
      if (callback) callback(false, response.data?.error || '删除失败');
    }
  } catch (error: any) {
    console.error('[实盘分发器] 请求删除策略时出错:', error);
    // 安全地提取错误信息
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      // 处理Axios错误
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }

    console.error('[实盘分发器] 错误详情:', errorMsg);
    if (callback) callback(false, errorMsg);
  }
}

/**
 * 处理更新实盘策略配置请求
 */
async function handleUpdateLiveStrategy(payload: StrategyEvents.UpdateLiveStrategyPayload) {
  console.log('[实盘分发器] 收到更新实盘策略配置事件:', payload.liveStrategyId);
  const { liveStrategyId, config, callback } = payload;

  if (!liveStrategyId) {
    console.error('[实盘分发器] 更新实盘策略配置事件缺少实盘策略ID');
    if (callback) callback(false, '缺少实盘策略ID');
    return;
  }

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法更新策略配置');
    if (callback) callback(false, '用户未认证');
    return;
  }

  try {
    // 使用模拟数据或真实API
    if (USE_MOCK_DATA) {
      console.log('[实盘分发器] 使用模拟数据更新策略配置:', liveStrategyId);

      // 模拟成功响应
      setTimeout(() => {
        console.log('[实盘分发器] 策略配置更新成功(模拟)');
        // 通知策略列表更新
        EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
        if (callback) callback(true, '策略配置已更新');
      }, 500);
    } else {
      // 真实API调用
      const apiUrl = `${BACKEND_API_BASE}/strategy/live/${liveStrategyId}/update`;
      console.log(`[实盘分发器] 调用API: PUT ${apiUrl}`, config);

      const response = await axios.put<ApiResponse>(apiUrl, config, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success) {
        console.log('[实盘分发器] 策略配置更新成功:', response.data.message);
        // 通知策略列表更新
        EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
        if (callback) callback(true, response.data.message);
      } else {
        console.error('[实盘分发器] 策略配置更新失败:', response.data?.error || '未知错误');
        if (callback) callback(false, response.data?.error || '更新失败');
      }
    }
  } catch (error: any) {
    console.error('[实盘分发器] 更新策略配置时发生错误:', error);
    // 安全地提取错误信息
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      // 处理Axios错误
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }

    console.error('[实盘分发器] 错误详情:', errorMsg);
    if (callback) callback(false, errorMsg);
  }
}

/**
 * 处理测试进程活动状态请求
 */
async function handleTestProcessActivity(payload: StrategyEvents.TestProcessActivityPayload) {
  console.log('[实盘分发器] 收到测试进程活动状态事件:', payload.liveStrategyId);
  const { liveStrategyId, callback } = payload;

  // 如果liveStrategyId是'current'，表示检查当前用户的进程健康状态
  const isCurrentUserCheck = liveStrategyId === 'current';

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法测试进程活动状态');
    if (callback) callback(false, null, '用户未认证');
    return;
  }

  try {
    // 对于当前用户检查，不需要传递liveStrategyId
    const requestBody = isCurrentUserCheck ? {} : { liveStrategyId };
    
    const response = await fetch('/api/strategy/live/test_process_activity', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestBody)
    });

    const result: ProcessActivityApiResponse = await response.json();
    
    if (result.success && result.data) {
      console.log('[实盘分发器] 进程活动状态测试成功:', result.data);
      if (callback) callback(true, result.data);
    } else {
      console.error('[实盘分发器] 进程活动状态测试失败:', result.error);
      if (callback) callback(false, null, result.error || '测试失败');
    }
  } catch (error) {
    console.error('[实盘分发器] 进程活动状态测试请求异常:', error);
    if (callback) callback(false, null, '网络请求失败');
  }
}

/**
 * 处理测试实盘数据连接状态请求
 */
async function handleTestRTConnection(payload: StrategyEvents.TestLiveRTConnectionPayload) {
  console.log('[实盘分发器] 收到测试数据连接状态事件:', payload.liveStrategyId);
  const { liveStrategyId, callback } = payload;

  const token = getToken();
      if (!token) {
      console.error('[实盘分发器] 未找到认证Token，无法测试数据连接状态');
      if (callback) callback(false, null);
      return;
    }

  try {
    const response = await fetch('/api/strategy/live/test_rt_connection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ liveStrategyId })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('[实盘分发器] 数据连接状态测试成功:', result.message);
      if (callback) callback(true, result);
    } else {
      console.error('[实盘分发器] 数据连接状态测试失败:', result.error || result.message);
      if (callback) callback(false, null);
    }
  } catch (error) {
    console.error('[实盘分发器] 数据连接状态测试请求异常:', error);
    if (callback) callback(false, null);
  }
}

async function handleGetRuntimeData(payload: StrategyEvents.GetRuntimeDataPayload) {
  console.log('[实盘分发器] 收到获取运行时数据事件');
  const { liveStrategyId, callback } = payload;

  if (!callback || typeof callback !== 'function') {
    console.error('[实盘分发器] 获取运行时数据事件缺少有效的回调函数');
    return;
  }

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法获取运行时数据');
    callback(false, null, '未找到认证Token');
    return;
  }

  try {
    const apiUrl = `${BACKEND_API_BASE}/strategy/live/${liveStrategyId}/runtime_data`;
    console.log(`[实盘分发器] 调用API: GET ${apiUrl}`);

    const response = await axios.get(apiUrl, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data?.success) {
      const data = response.data.data?.data || response.data.data;
      console.log('[实盘分发器] 成功获取运行时数据:', data);
      callback(true, data, undefined);
    } else {
      console.error('[实盘分发器] 获取运行时数据失败:', response.data?.error || '响应格式不符合预期');
      callback(false, null, response.data?.error || '获取运行时数据失败');
    }
  } catch (error: any) {
    console.error('[实盘分发器] 请求运行时数据时出错:', error);
    
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }

    console.error('[实盘分发器] 错误详情:', errorMsg);
    callback(false, null, errorMsg);
  }
}
// --- 新增: 处理保存交易通道配置请求 ---
async function handleSaveTradingChannelConfig(payload: StrategyEvents.SaveTradingChannelConfigPayload) {
  console.log('[实盘分发器] 收到保存交易通道配置事件:', payload.config);
  const { config, callback } = payload;

  if (!config.channelType || !config.configName) {
    console.error('[实盘分发器] 保存交易通道配置事件缺少必要参数');
    if (callback) callback(false, '缺少必要参数');
    return;
  }

  const token = getToken();
  const userInfo = getUserInfo();

  if (!token || !userInfo || !userInfo.username) {
    console.error('[实盘分发器] 未找到认证Token或用户信息，无法保存配置');
    if (callback) callback(false, '用户未认证');
    return;
  }

  try {
    // 使用模拟数据或真实API
    if (USE_MOCK_DATA) {
      console.log('[实盘分发器] 使用模拟数据保存交易通道配置:', config);

      // 模拟保存成功
      setTimeout(() => {
        // 保存到模拟存储
        const userConfigs = mockUserConfigs.get(userInfo.username) || [];

        // 检查是否已存在相同类型的配置，如果存在则更新，否则添加
        const existingIndex = userConfigs.findIndex(c => c.channelType === config.channelType);
        if (existingIndex >= 0) {
          userConfigs[existingIndex] = config;
          console.log('[实盘分发器] 更新现有配置:', config.channelType);
        } else {
          userConfigs.push(config);
          console.log('[实盘分发器] 添加新配置:', config.channelType);
        }

        mockUserConfigs.set(userInfo.username, userConfigs);
        console.log('[实盘分发器] 交易通道配置保存成功(模拟)');
        if (callback) callback(true, '配置已保存');
      }, 500);
    } else {
      // 真实API调用
      const apiUrl = `${BACKEND_API_BASE}/strategy/trading_channel_config`;
      console.log(`[实盘分发器] 调用API: POST ${apiUrl}`, config);

      const response = await axios.post<ApiResponse>(apiUrl, config, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success) {
        console.log('[实盘分发器] 交易通道配置保存成功:', response.data.message);
        if (callback) callback(true, response.data.message);
      } else {
        console.error('[实盘分发器] 交易通道配置保存失败:', response.data?.error || '未知错误');
        if (callback) callback(false, response.data?.error || '保存失败');
      }
    }
  } catch (error: any) {
    console.error('[实盘分发器] 保存交易通道配置时出错:', error);
    let errorMsg = '请求出错';
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        errorMsg = '后端API未实现 (404 Not Found)';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else {
        errorMsg = error.message;
      }
    } else if (error instanceof Error) {
      errorMsg = error.message;
    }
    console.error('[实盘分发器] 错误详情:', errorMsg);
    if (callback) callback(false, errorMsg);
  }
}

// --- 新增: 处理获取交易通道配置请求 ---
async function handleGetTradingChannelConfig(payload: StrategyEvents.GetTradingChannelConfigPayload) {
  console.log('[实盘分发器] 收到获取交易通道配置事件:', payload.channelType);
  const { channelType, callback } = payload;

  if (!channelType) {
    console.error('[实盘分发器] 获取交易通道配置事件缺少通道类型');
    if (callback) callback(false);
    return;
  }

  const userInfo = getUserInfo();
  if (!userInfo || !userInfo.username) {
    console.error('[实盘分发器] 未找到用户信息，无法获取配置');
    if (callback) callback(false);
    return;
  }

  try {
    // 使用模拟数据或真实API
    if (USE_MOCK_DATA) {
      console.log('[实盘分发器] 使用模拟数据获取交易通道配置:', channelType);

      // 从模拟存储中获取配置
      const userConfigs = mockUserConfigs.get(userInfo.username) || [];
      const config = userConfigs.find(c => c.channelType === channelType);

      if (config) {
        console.log('[实盘分发器] 找到配置:', config);
        if (callback) callback(true, config);
      } else {
        console.log('[实盘分发器] 未找到配置');
        if (callback) callback(false);
      }
    } else {
      // 真实API调用
      const token = getToken();
      if (!token) {
        console.error('[实盘分发器] 未找到认证Token');
        if (callback) callback(false);
        return;
      }

      const apiUrl = `${BACKEND_API_BASE}/strategy/trading_channel_config/${channelType}`;
      console.log(`[实盘分发器] 调用API: GET ${apiUrl}`);

      const response = await axios.get<{success: boolean, data?: StrategyEvents.TradingChannelConfig, error?: string}>(apiUrl, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data && response.data.success && response.data.data) {
        console.log('[实盘分发器] 获取配置成功:', response.data.data);
        if (callback) callback(true, response.data.data);
      } else {
        console.log('[实盘分发器] 未找到配置或获取失败');
        if (callback) callback(false);
      }
    }
  } catch (error: any) {
    console.error('[实盘分发器] 获取交易通道配置时出错:', error);
    if (callback) callback(false);
  }
}

// --- 新增: 处理交易通道健康测试请求 ---
async function handleTestTradingChannel(payload: StrategyEvents.TestTradingChannelPayload) {
  console.log('[实盘分发器] 收到交易通道健康测试事件:', payload.channelId);
  const { channelId, callback } = payload;

  if (!channelId) {
    console.error('[实盘分发器] 交易通道健康测试事件缺少通道ID');
    if (callback) callback(false);
    return;
  }

  const token = getToken();
  if (!token) {
    console.error('[实盘分发器] 未找到认证Token，无法测试交易通道');
    if (callback) callback(false);
    return;
  }

  try {
    const response = await axios.post(`${BACKEND_API_BASE}/strategy/trading_channel/health_test`, {
      channelId: channelId
    }, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data.success) {
      console.log('[实盘分发器] 交易通道健康测试成功:', response.data);
      if (callback) callback(true, response.data);
    } else {
      console.error('[实盘分发器] 交易通道健康测试失败:', response.data.error);
      if (callback) callback(false, response.data.error);
    }
  } catch (error) {
    console.error('[实盘分发器] 交易通道健康测试请求失败:', error);
    if (callback) callback(false, '网络请求失败');
  }
}

/**
 * 初始化实盘策略分发器，监听相关事件
 */
export function initializeLiveStrategyDispatcher() {
  console.log('[实盘分发器] 初始化，开始监听事件...');
  EventBus.on(StrategyEvents.Types.DEPLOY_TO_LIVE, handleDeployToLive);
  EventBus.on(StrategyEvents.Types.GET_LIVE_STRATEGIES, handleGetLiveStrategies);
  EventBus.on(StrategyEvents.Types.START_LIVE_STRATEGY, (payload) => {
    handleStartLiveStrategy({
      liveStrategyId: payload.liveStrategyId,
      callback: payload.callback || (() => {})
    });
  });
  
  // --- 新增: 监听获取通道事件 ---
  EventBus.on(StrategyEvents.Types.GET_AVAILABLE_TRADING_CHANNELS, handleGetAvailableTradingChannels);

  EventBus.on(StrategyEvents.Types.STOP_LIVE_STRATEGY, (payload) => {
    handleStopLiveStrategy({
      liveStrategyId: payload.liveStrategyId,
      callback: payload.callback || (() => {})
    });
  });
  EventBus.on(StrategyEvents.Types.DELETE_LIVE_STRATEGY, (payload) => {
    handleDeleteLiveStrategy({
      liveStrategyId: payload.liveStrategyId,
      callback: payload.callback || (() => {})
    });
  });
  
  // 监听更新实盘策略配置事件
  EventBus.on(StrategyEvents.Types.UPDATE_LIVE_STRATEGY, handleUpdateLiveStrategy);
  
  // --- 新增: 监听测试进程活动状态事件 ---
  EventBus.on(StrategyEvents.Types.TEST_PROCESS_ACTIVITY, handleTestProcessActivity);
  
  // --- 新增: 监听测试数据连接状态事件 ---
  EventBus.on(StrategyEvents.Types.TEST_LIVE_RT_CONNECTION, handleTestRTConnection);
  
  // --- 新增: 监听获取运行时数据事件 ---
  EventBus.on(StrategyEvents.Types.GET_RUNTIME_DATA, handleGetRuntimeData);
  
  // --- 新增: 监听交易通道健康测试事件 ---
  EventBus.on(StrategyEvents.Types.TEST_TRADING_CHANNEL, handleTestTradingChannel);
}
