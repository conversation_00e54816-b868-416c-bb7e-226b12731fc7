import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Row, Col, Spin, Empty } from 'antd';
import StrategyShower, { StrategyData } from '../../../../_Widgets/StrategyShower';
import { EventBus } from '../../../../events/eventBus';
import { StrategyEvents } from '../../../../events/events';
import StrategyDetailView from '../../../../_Widgets/StrategyDetailView';
import StrategyEditor from '../../../../_Widgets/StrategyEditor';
import { StrategyListItem } from '@/shared_types/strategy';

const StrategyPanel: React.FC = () => {
  const [strategies, setStrategies] = useState<StrategyData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // 把发送策略列表请求单独拎出来作为函数调用
  const fetchStrategies = () => {
    setLoading(true);
    setError(null);
    console.log('[策略面板] 开始获取策略列表');
    
    EventBus.emit(StrategyEvents.Types.GET_STRATEGIES_LIST, {
      callback: (success: boolean, data: any[]) => {
        console.log('[策略面板] 获取策略列表回调', success, data);
        if (success && data) {
          setStrategies(data);
        } else {
          setError('获取策略列表失败，请检查网络连接或后端服务状态');
        }
        setLoading(false);
      }
    });
  };

  const handleStrategyListUpdate = (payload: StrategyEvents.StrategyListUpdatedPayload) => {
    console.log('[策略面板] 监听到策略列表更新事件', payload);
    fetchStrategies();
  };

  useEffect(() => {
    // 组件挂载时获取策略列表

    fetchStrategies();

    const updateSubscription = EventBus.on(StrategyEvents.Types.STRATEGY_LIST_UPDATED, handleStrategyListUpdate);

    // 组件卸载时取消监听
    return () => {
      updateSubscription.unsubscribe();
      console.log('[策略面板] 取消监听策略列表更新事件');
    };
  }, []);

  useEffect(() => {
    const handleShowDetails = (payload: StrategyEvents.ShowStrategyDetailsPayload) => {
      console.log(`[策略面板] 监听到显示详情事件 (仅记录): ${payload.strategyId}`);
    };

    const subscription = EventBus.on(StrategyEvents.Types.SHOW_STRATEGY_DETAILS, handleShowDetails);

    // 组件卸载时取消监听
    return () => {
      subscription.unsubscribe();
      console.log('[策略面板] 取消监听显示详情事件');
    };
  }, []);

  return (
    <PageContainer
      title="策略精选"
      header={{
        title: <div style={{ textAlign: 'center', fontSize: '24px', fontWeight: 'bold' }}>策略精选</div>,
        ghost: true,
        breadcrumb: undefined,
      }}
      content={null}
      footer={[]}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" tip="加载策略数据..." />
        </div>
      ) : error ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Empty description={error} />
        </div>
      ) : strategies.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Empty description="暂无策略数据" />
        </div>
      ) : (
        <Row gutter={[32, 32]} style={{ marginTop: '24px' }}>
          {strategies.map(strategy => (
            <Col key={strategy.id}>
              <StrategyShower strategy={strategy} />
            </Col>
          ))}
        </Row>
      )}

      <StrategyDetailView />
      <StrategyEditor />

    </PageContainer>
  );
};

export default StrategyPanel; 