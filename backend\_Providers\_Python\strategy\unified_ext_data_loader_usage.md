# UnifiedExtDataLoader 使用说明

## 概述

`UnifiedExtDataLoader` 现在支持两种数据源模式：
1. **本地数据模式** (`local`): 从本地通达信数据文件获取历史数据（原有模式）
2. **Provider接口模式** (`provider`): 通过 TDX服务接口获取实时数据（新增功能）

## 全局配置

### 设置数据源模式

```python
from unified_ext_data_loader import (
    set_data_source_to_local, 
    set_data_source_to_provider,
    set_data_source_mode,
    get_data_source_mode
)

# 方式1: 使用便利函数
set_data_source_to_provider()  # 设置为Provider接口模式
set_data_source_to_local()     # 设置为本地数据模式

# 方式2: 直接设置
set_data_source_mode('provider')  # 或 'local'

# 查看当前模式
current_mode = get_data_source_mode()
print(f"当前数据源模式: {current_mode}")
```

### 配置 TDX 服务地址

```python
from unified_ext_data_loader import configure_tdx_service

# 配置服务地址（默认为 127.0.0.1:5003，从backend/config.json自动读取）
configure_tdx_service(host='127.0.0.1', port=5003)
```

**注意**: 系统会自动从 `backend/config.json` 文件中读取 `tdx_service` 配置，通常不需要手动配置。

## 使用方式

### 1. 在策略中使用

```python
from unified_ext_data_loader import get_unified_ext_data_loader, set_data_source_to_provider

# 设置为Provider模式
set_data_source_to_provider()

# 获取加载器实例
loader = get_unified_ext_data_loader()

# 在Wonder Trader中注册
engine.set_extended_data_loader(loader)
```

### 2. 数据源切换

```python
# 运行时切换数据源
if use_realtime_data:
    set_data_source_to_provider()
else:
    set_data_source_to_local()
```

### 3. 查看当前配置

```python
from unified_ext_data_loader import get_current_config

config = get_current_config()
print(f"数据源模式: {config['data_source_mode']}")
print(f"MarketProvider地址: {config['market_provider_config']}")
```

## 工作原理

### Provider模式数据获取流程

1. **接收请求**: Wonder Trader调用 `load_final_his_bars()`
2. **检查模式**: 根据 `DATA_SOURCE_MODE` 决定数据源
3. **TDX请求**: 调用 `_get_klines_from_provider()` 向 TDX服务发送HTTP请求
4. **数据转换**: 将TDX服务返回的数据转换为Wonder Trader格式
5. **回退机制**: 如果TDX服务获取失败，自动回退到本地数据源

### 请求参数映射

| Wonder Trader | TDX服务接口 | 说明 |
|---------------|-------------|------|
| SZSE.ETF.159985 | market=sz, code=159985 | 深交所ETF |
| SSE.600000 | market=sh, code=600000 | 上交所股票 |
| d1 | period=1D | 日线数据 |
| m5 | period=5m | 5分钟数据 |

### 数据格式转换

TDX服务返回格式:
```json
[
  {
    "time": **********,
    "open": 10.5,
    "high": 10.8,
    "low": 10.3,
    "close": 10.7,
    "volume": 1000000,
    "datetime": "2022-01-01 09:30:00"
  }
]
```

转换为内部格式:
```python
{
  "date": "2022/1/1",
  "time": "09:30:00", 
  "open": 10.5,
  "high": 10.8,
  "low": 10.3,
  "close": 10.7,
  "vol": 1000000.0,
  "money": 10700000.0
}
```

## 错误处理

1. **网络错误**: 自动回退到本地数据源
2. **数据格式错误**: 记录错误日志，跳过异常数据行
3. **Provider服务不可用**: 回退到本地数据源

## 配置建议

### 生产环境
```python
# 使用TDX服务接口获取实时数据
set_data_source_to_provider()
# 配置会自动从backend/config.json读取，通常不需要手动设置
```

### 开发/测试环境
```python
# 使用本地数据，避免网络依赖
set_data_source_to_local()
```

### 混合模式
```python
# 根据品种类型选择数据源
if symbol.startswith('CRYPTO'):
    set_data_source_to_provider()  # 虚拟货币使用实时数据
else:
    set_data_source_to_local()     # 股票期货使用本地数据
```

## 注意事项

1. **默认模式**: 系统默认使用 `provider` 模式
2. **回退机制**: Provider模式失败时会自动回退到本地数据
3. **性能考虑**: Provider模式需要网络请求，可能比本地数据稍慢
4. **数据一致性**: 两种模式的数据格式完全一致，可以无缝切换
