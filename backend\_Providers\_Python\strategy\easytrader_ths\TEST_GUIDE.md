# easytrader_ths 测试指南

本文档提供了测试 easytrader_ths 模块的详细步骤。

## 准备工作

### 1. 安装依赖

```bash
pip install easytrader flask requests
```

### 2. 配置同花顺客户端

确保同花顺交易客户端已安装并能正常登录。修改 `easytrader_ths_config.json` 文件中的 `ths_path` 为您电脑上同花顺交易客户端的实际路径。

```json
{
  "username": "quantquart_user",
  "backend_url": "http://localhost:8000",
  "local_port": 8888,
  "heartbeat_interval": 60,
  "ths_path": "C:/同花顺软件/xiadan.exe"  // 修改为实际路径
}
```

## 测试步骤

### 1. 启动测试服务器

在一个命令行窗口中运行：

```bash
python test_server.py
```

服务器将在 8000 端口监听请求。

### 2. 启动客户端

在另一个命令行窗口中运行：

```bash
python client.py
```

如果一切正常，您应该会看到客户端成功连接到同花顺交易客户端，并注册到测试服务器。

### 3. 测试基本功能

#### 查询账户余额

```bash
python test_trade.py --action balance
```

#### 查询持仓信息

```bash
python test_trade.py --action position
```

#### 查询今日委托

```bash
python test_trade.py --action today_entrusts
```

#### 查询今日成交

```bash
python test_trade.py --action today_trades
```

#### 刷新接口

```bash
python test_trade.py --action refresh
```

### 4. 测试交易功能（谨慎测试，可能会实际下单）

#### 买入股票

```bash
python test_trade.py --action buy --code sh600000 --price 10.00 --amount 100
```

#### 卖出股票

```bash
python test_trade.py --action sell --code sh600000 --price 10.50 --amount 100
```

#### 撤销委托

```bash
python test_trade.py --action cancel --entrust_no 委托编号
```

委托编号可以从 `today_entrusts` 操作的结果中获取。

## 常见问题排查

### 1. 客户端无法连接到同花顺交易客户端

- 检查同花顺交易客户端是否已启动并登录
- 检查 `ths_path` 是否正确
- 查看客户端日志，了解具体错误原因

### 2. 客户端无法注册到服务器

- 检查服务器是否已启动
- 检查 `backend_url` 是否正确
- 检查网络连接是否正常

### 3. 交易操作失败

- 检查同花顺交易客户端是否正常登录
- 检查账户余额是否足够
- 检查股票代码是否正确
- 查看客户端日志，了解具体错误原因

## 集成到 Wonder Trader

完成基本测试后，您可以将 easytrader_ths 适配器集成到 Wonder Trader 中：

### 1. 修改 Wonder Trader 配置文件

在 Wonder Trader 的配置文件中添加交易通道配置：

```json
{
  "traders": {
    "easytrader_ths": {
      "active": true,
      "module": "TraderEasyTraderTHS",
      "username": "quantquart_user",
      "client_url": "http://客户端IP:8888",
      "timeout": 10
    }
  }
}
```

### 2. 在品种与通道映射中指定使用该通道

```json
{
  "product_mappings": {
    "SSE.600000": "easytrader_ths",
    "SZSE.000001": "easytrader_ths"
  }
}
```

## 注意事项

1. **安全性**：测试环境没有实现任何安全措施，仅用于测试。在生产环境中，应添加适当的身份验证和加密。

2. **网络连接**：确保客户端和服务器之间的网络连接正常，没有防火墙阻止。

3. **同花顺客户端**：确保同花顺客户端保持登录状态，否则交易操作会失败。

4. **实际交易**：测试买入和卖出操作时要特别小心，因为这些操作会实际下单。建议在测试环境中使用小额委托或者模拟盘。
