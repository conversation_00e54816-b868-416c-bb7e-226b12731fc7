import { KLineData, IndicatorType, KLine, KLineInterval } from '@/shared_types/market';
import { v4 as uuidv4 } from 'uuid';  // 需要安装 uuid 包
import { EventBus } from '@/events/eventBus';
import { IndicatorEvents, MarketEvents } from '@/events/events';
import { useAtomValue } from 'jotai';
import { jotaiStore, chartDataAtom } from '@/store/state';
import { Indicator, IndicatorLineConfig } from '@/shared_types/indicator';
import { MACD } from './index_debutIndicatorsd_based';
import { BOLL, SWMA, WMA, RSI, KDJ, VOL, MA, EMA } from './index_debutIndicatorsd_based';

// 指标映射表
export const INDICATORS: Record<IndicatorType, any> = {
  [IndicatorType.MACD]: MACD,
  [IndicatorType.RSI]: RSI,
  [IndicatorType.KDJ]: KDJ,
  [IndicatorType.VOL]: VOL,
  [IndicatorType.MA]: MA,
  [IndicatorType.EMA]: EMA,
  [IndicatorType.WMA]: WMA,
  [IndicatorType.BOLL]: BOLL,
  [IndicatorType.SWMA]: SWMA,
  [IndicatorType.SHAPE]: {
    name: 'SHAPE',
    type: IndicatorType.SHAPE,
    lines: [
      { name: 'shape', color: '#FF6B6B' }
    ],
    createCalculator: () => ({}),
    calculate: () => ({}),
    // 特征提取方法：将K线数据转换为1-5的离散序列
    extractFeatures: (klineData: KLineData[]): number[] => {
      if (!klineData || klineData.length === 0) return [];
      
      const closePrices = klineData.map(k => k.close);
      const minPrice = Math.min(...closePrices);
      const maxPrice = Math.max(...closePrices);
      const priceRange = maxPrice - minPrice;
      
      if (priceRange === 0) {
        // 如果价格范围为零，返回中间值3
        return new Array(klineData.length).fill(3);
      }
      
      // 将价格范围分为5个格子，映射到1-5
      return closePrices.map(price => {
        const normalizedPrice = (price - minPrice) / priceRange;
        const gridIndex = Math.floor(normalizedPrice * 5);
        // 确保结果在1-5范围内
        return Math.max(1, Math.min(5, gridIndex + 1));
      });
    }
  }
};  

export class IndicatorWrapper {
  private klineId: string;
  private kline: KLine | null;
  public readonly indicator: Indicator;
  private params: Record<string, number>;
  private calculator: any;
  public values: Record<string, number[]>;
  private lastCalculatedIndex: number;
  private lineConfigs: IndicatorLineConfig[] = [];
  

  // 添加公共属性
  public readonly type: IndicatorType;
  public readonly id: string;  // 添加唯一ID
  public readonly lines: IndicatorLineConfig[]; //把indicator.lines输出

  // 新增：响应 KLINES_READY 事件的处理函数
  private klinesReadyHandler = (payload: MarketEvents.KLinesReady) => {
    this.updateHistoricalData(payload.kline);    
  }

  // 使用合并后的数据调用指标的重新计算，指标内部会处理
  private klinesUpdatedHandler = (payload: MarketEvents.KLinesUpdated) => {
    // 这个事件传过来的是增量后的全量数据
    this.updateHistoricalData(payload.kline);
  }

  private showParamsHandler = (payload: IndicatorEvents.ShowIndicatorParamsPayload) => {
    if (payload.id === this.id) {
      EventBus.emit(IndicatorEvents.Types.SHOW_PARAMS_MODAL, {
        id: this.id,
        type: this.type,
        params: this.getParams(),
        lines: this.getLineConfigs()
      });
    }
  };

  private paramsEditedHandler = (payload: IndicatorEvents.UpdateIndicatorPayload) => {
    if (payload.id === this.id && payload.params) {
      this.updateParams(payload.params);
    }
  };

  constructor(
    indicator: Indicator,
    kline: KLine | null,
    params: Record<string, number>
  ) {

    this.indicator = indicator;
    this.type = indicator.type;  // 初始化 type
    this.lines = indicator.lines;
    this.id = uuidv4();  // 使用 uuidv4 生成唯一ID

    console.log("[IndicatorWrapper] 创建指标", this.type, "，生成ID: ", this.id);

    this.klineId = kline?.id || '';
    this.kline = kline;
    this.params = params;
    this.values = {};
    this.lastCalculatedIndex = -1;
    
    // 初始化线条配置
    this.lineConfigs = indicator.lines;
    
    this.initCalculator();
    this.calculate(true);

    // 使用类的方法作为事件处理器
    EventBus.on(IndicatorEvents.Types.SHOW_INDICATOR_PARAMS, this.showParamsHandler);
    EventBus.on(IndicatorEvents.Types.INDICATOR_PARAMS_EDITED, this.paramsEditedHandler);

    // 当前的版本，指标配置是全局的，而对于使用习惯也是如此，所以，应该订阅这个事件
    // 这个事件发生的时候，所有的指标实例都是已经存在的
    EventBus.on(MarketEvents.Types.KLINES_READY, this.klinesReadyHandler);
    EventBus.on(MarketEvents.Types.KLINES_UPDATED, this.klinesUpdatedHandler);
  }

  // 初始化计算器
  private initCalculator(): void {
    this.calculator = this.indicator.createCalculator(this.params);
  }

  // 计算所有K线的指标值
  private calculate(newDataset: boolean = false): Record<string, number[]> {
    // 如果是新数据集，重置结果数组，否则保持现有结果
    if (newDataset) {
      this.values = {};
      this.lastCalculatedIndex = -1;
      // 重新初始化计算器以确保没有残留状态
      this.initCalculator();
    }
    
    // 确定开始计算的位置
    const startIndex = newDataset ? 0 : this.lastCalculatedIndex; // 不能加一，这样定位在上一次最后一条数据，则最后一条数据就会再计算一次，然后结束
    let calculatedCount = 0;

    // 从指定位置开始遍历K线
    if (!this.kline) {
      this.lastCalculatedIndex = -1;
      return {};
    }
    
    for (let index = startIndex; index < this.kline.data.length || 0; index++) {
      const candle = this.kline.data[index];
      const result = this.indicator.calculate(
        this.calculator, 
        candle, 
        index === this.kline.data.length - 1
      );
      calculatedCount++;

      // 将结果添加到对应的数组中
      Object.entries(result).forEach(([key, value]) => {
        if (!this.values[key]) this.values[key] = [];
        this.values[key][index] = value;
      });
    }

    // 更新最后计算的位置
    this.lastCalculatedIndex = this.kline.data.length - 1;
    
    EventBus.emit(newDataset ? IndicatorEvents.Types.INDICATOR_CALCULATED : 
        IndicatorEvents.Types.INDICATOR_NEWDATACALCULATED, {
      id: this.id,
      values: this.values,
      times: this.kline.data.map(d => d.time)
    });
    
    console.log(
      "指标：", this.type,
      newDataset ? '全量计算' : '增量计算',
      '参数: ', this.params,
      "计算完毕，计算了", calculatedCount, "项数据"
    );

    return this.values;
  }

  public getName(): string {
    return this.indicator.name;
  }

  // 获取计算结果
  public getValues(): Record<string, number[]> {
    console.log('[IndicatorWrapper] getValues:', this.values);
    return this.values;
  }

  public clearData(): void {
    this.values = {};
    this.lastCalculatedIndex = -1;
    this.klineId = '';
  }

  // 添加获取参数的公共方法
  public getParams(): Record<string, number> {
    return { ...this.params };  // 返回参数的副本
  }

  // 更新参数的方法也需要修改为公共方法
  public updateParams(newParams: Record<string, number> | undefined): void {
    if (typeof newParams === 'undefined') return;
    
    this.params = { ...this.params, ...newParams };
    this.initCalculator();
    this.calculate(true);

    EventBus.emit(IndicatorEvents.Types.INDICATOR_CALCULATED, {
      id: this.id,
      values: this.values,
      times: this.kline?.data.map(d => d.time) || []
    });
  }

  // 添加一个公共方法来获取时间
  public getTimes(): number[] {
    return this.kline?.data.map(d => d.time) || [];
  }

  // 设置指标线条配置
  setLineConfigs(configs: IndicatorLineConfig[]) {
    this.lineConfigs = configs;
  }

  // 获取指标线条配置
  getLineConfigs(): IndicatorLineConfig[] {
    return this.lineConfigs;
  }

  public getIsMain(): boolean {
    return this.indicator.isMain;
  }

  // 清理事件监听器
  public destroy(): void {
    // 清理参数设置事件监听器
    EventBus.off(IndicatorEvents.Types.SHOW_INDICATOR_PARAMS, this.showParamsHandler);
    // 清理参数更新事件监听器  
    EventBus.off(IndicatorEvents.Types.INDICATOR_PARAMS_EDITED, this.paramsEditedHandler);
    // 清理K线数据更新事件监听器
    EventBus.off(MarketEvents.Types.KLINES_READY, this.klinesReadyHandler);
    EventBus.off(MarketEvents.Types.KLINES_UPDATED, this.klinesUpdatedHandler);
  }

  // 更新历史数据
  public updateHistoricalData(kline: KLine): void {
   
    console.log('[指标计算。。。] updateHistoricalData', kline);

    if (kline && kline.data.length === 0) {
      return;
    }

    // 检查是否需要全量更新
    //if (!this.kline) return;
    let needFullRefresh = true;

    if (this.kline && kline) {
      needFullRefresh = this.kline?.id !== kline.id && this.kline?.data.length - kline.data.length > kline.data.length / 2;
    }
    needFullRefresh = true; // 先全部用全量更新

    if (needFullRefresh) {
      // 品种变更，更新klineId
      this.klineId = kline.id;
      console.log(`[IndicatorWrapper] ${this.type} 指标执行全量更新 - 品种切换从 ${this.kline?.id} 到 ${kline.id}`);
      console.log('[IndicatorWrapper] 数据量：', this.kline?.data.length, " -> ", kline.data.length);
    } else {
      console.log(`[IndicatorWrapper] ${this.type} 指标执行增量更新, kline.length=${kline.data.length}`);
    }
    
    this.kline = kline;
    this.calculate(needFullRefresh);
  }
}
