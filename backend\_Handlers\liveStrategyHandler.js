/**
 * 实盘策略处理器
 * 负责管理实盘策略的部署、启动、停止和状态更新
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { User, LiveStrategy, GroupStrategy } = require('../models'); // GroupStrategy可能仍需用于关联查询
const path = require('path');
const fs = require('fs');
const fsp = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
// 新增：使用适配器
const liveStrategyAdapter = require('../services/LiveStrategyAdapter');
// 新增：使用新的进程管理器
const liveProcessManager = require('../services/LiveProcessManager');

// 创建简单的日志记录器
const logger = {
    info: (message) => console.log(`[LiveStrategyHandler INFO] ${message}`),
    warn: (message) => console.warn(`[LiveStrategyHandler WARN] ${message}`),
    error: (message) => console.error(`[LiveStrategyHandler ERROR] ${message}`),
    debug: (message) => console.log(`[LiveStrategyHandler DEBUG] ${message}`)
};

// 存储实盘策略实例
const liveStrategies = new Map(); // 使用 Map 存储: strategyId -> 策略实例信息

// 存储策略进程
const strategyProcesses = new Map(); // 使用 Map 存储: strategyId -> 进程信息

// 配置
const config = {
    // Wonder Trader 配置目录
    wtConfigDir: path.join(__dirname, '../_Providers/_Python/strategy/config'),
    // 策略文件目录
    strategyDir: path.join(__dirname, '../_Providers/_Python/strategy/strategies'),
    // 实盘配置目录
    liveConfigDir: path.join(__dirname, '../_Providers/_Python/strategy/live_configs'),
    // 实盘日志目录
    liveLogDir: path.join(__dirname, '../_Providers/_Python/strategy/live_logs'),
};

// 确保目录存在
async function ensureDirectoriesExist() {
    // 确保基本目录存在
    for (const dir of [config.wtConfigDir, config.strategyDir, config.liveConfigDir, config.liveLogDir]) {
        try {
            await fsp.mkdir(dir, { recursive: true });
            logger.info(`创建目录: ${dir}`);
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
        }
    }

    // 确保实盘配置实例目录存在
    const instancesDir = path.join(config.liveConfigDir, 'instances');
    try {
        await fsp.mkdir(instancesDir, { recursive: true });
        logger.info(`创建实盘配置实例目录: ${instancesDir}`);
    } catch (error) {
        if (error.code !== 'EEXIST') {
            throw error;
        }
    }

    // 确保实盘日志目录存在
    for (const [id, strategy] of liveStrategies.entries()) {
        const strategyLogDir = path.join(config.liveLogDir, id);
        try {
            await fsp.mkdir(strategyLogDir, { recursive: true });
            logger.info(`创建策略日志目录: ${strategyLogDir}`);
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
        }
    }
}

/**
 * 同步版本的目录创建函数
 */
function ensureDirectoriesExistSync() {
    const fs = require('fs');
    
    // 确保基本目录存在
    for (const dir of [config.wtConfigDir, config.strategyDir, config.liveConfigDir, config.liveLogDir]) {
        try {
            fs.mkdirSync(dir, { recursive: true });
            logger.info(`创建目录: ${dir}`);
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
        }
    }

    // 确保实盘配置实例目录存在
    const instancesDir = path.join(config.liveConfigDir, 'instances');
    try {
        fs.mkdirSync(instancesDir, { recursive: true });
        logger.info(`创建实盘配置实例目录: ${instancesDir}`);
    } catch (error) {
        if (error.code !== 'EEXIST') {
            throw error;
        }
    }

    // 注意：这里不创建策略日志目录，因为启动时 liveStrategies 是空的
    // 策略日志目录会在实际启动策略时创建
}

/**
 * 从数据库加载实盘策略到内存
 */
async function loadLiveStrategiesFromDB() {
    try {
        logger.info('从数据库加载实盘策略到内存...');
        const dbStrategies = await LiveStrategy.findAll();

        logger.info(`从数据库加载了 ${dbStrategies.length} 个实盘策略`);

        // 清空内存中的策略
        liveStrategies.clear();

        // 将数据库中的策略加载到内存中
        dbStrategies.forEach(dbStrategy => {
            const strategyData = dbStrategy.toJSON();
            liveStrategies.set(strategyData.id, strategyData);
            logger.info(`加载策略: ${strategyData.id}, 名称: ${strategyData.name}`);
        });

        return true;
    } catch (error) {
        logger.error(`从数据库加载实盘策略失败: ${error.message}`);
        return false;
    }
}

/**
 * 初始化路由
 */
function initRoutes() {
    // 获取实盘策略列表
    router.get('/live', authenticateToken, handleGetLiveStrategies);

    // 部署策略到实盘
    router.post('/deploy', authenticateToken, handleDeployToLive);

    // 启动实盘策略
    router.post('/live/start/:id', authenticateToken, handleStartLiveStrategy);

    // 停止实盘策略
    router.post('/live/stop/:id', authenticateToken, handleStopLiveStrategy);

    // 停止整个实盘项目
    router.post('/live/group/stop/:id', authenticateToken, handleStopLiveGroup);

    // 获取实盘策略详情
    router.get('/live/:id', authenticateToken, handleGetLiveStrategyDetails);

    // 获取可用交易通道
    router.get('/trading_channels', authenticateToken, handleGetTradingChannels);

    // 删除实盘策略
    router.delete('/live/delete/:id', authenticateToken, handleDeleteLiveStrategy);

    // 更新实盘策略配置
    router.put('/live/:id/update', authenticateToken, handleUpdateLiveStrategy);

    // 获取策略日志
    router.get('/live/:id/logs', authenticateToken, handleGetStrategyLogs);

    // 获取策略持仓
    router.get('/live/:id/positions', authenticateToken, handleGetStrategyPositions);

    // 获取策略交易记录
    router.get('/live/:id/trades', authenticateToken, handleGetStrategyTrades);

    // 保存交易通道配置
    router.post('/trading_channel_config', authenticateToken, handleSaveTradingChannelConfig);

    // 获取交易通道配置
    router.get('/trading_channel_config/:channelType', authenticateToken, handleGetTradingChannelConfig);

    // 获取策略调试信息
    router.get('/live/:id/debug', authenticateToken, handleGetStrategyDebugInfo);

    // 测试实盘进程活动状态
    router.post('/live/test_process_activity', authenticateToken, handleTestProcessActivity);

    // 获取策略运行数据
    router.get('/live/:id/runtime_data', authenticateToken, handleGetStrategyRuntimeData);

    // 测试实盘数据连接状态
    router.post('/live/test_rt_connection', authenticateToken, handleTestRTConnection);

    // 测试交易通道健康状态
    router.post('/trading_channel/health_test', authenticateToken, handleTradingChannelHealthTest);

    return router;
}

/**
 * 处理获取实盘策略列表请求
 */
async function handleGetLiveStrategies(req, res) {
    try {
        const userId = req.user.id;
        const username = req.user.username;

        logger.debug(`获取用户 ${username} 的实盘策略列表`);

        // 使用适配器获取策略列表
        const result = await liveStrategyAdapter.getLiveStrategies(userId);

        if (result.success) {
            logger.info(`返回用户 ${username} 的 ${result.data.length} 个策略`);
            // 移除详细数据输出，只保留数量信息
            res.json(result);
        } else {
            logger.error(`获取实盘策略列表失败: ${result.error}`);
            res.status(500).json(result);
        }
    } catch (error) {
        logger.error(`获取实盘策略列表失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理部署策略到实盘请求
 */
async function handleDeployToLive(req, res) {
    try {
        const { strategyId, strategyType, accountId, initialCapital, commissionRate, riskSettings } = req.body;
        const userId = req.user.id;
        const username = req.user.username;

        logger.info(`用户 ${username} 请求部署策略 ${strategyId} 到实盘`);

        if (!strategyId || !strategyType || !accountId || !initialCapital) {
            return res.status(400).json({ success: false, error: '缺少必要参数' });
        }

        // 使用适配器部署策略
        const result = await liveStrategyAdapter.deployStrategy(
            userId,
            username, 
            {
                strategyId,
                strategyType,
                accountId,
                initialCapital,
                commissionRate,
                riskSettings
            }
        );

        if (result.success) {
            // 部署成功后，同步新策略到内存
            if (result.data && result.data.liveStrategyId) {
                await syncStrategyToMemory(result.data.liveStrategyId);
            }
            res.json(result);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        logger.error(`部署策略到实盘失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理启动实盘策略请求
 */
async function handleStartLiveStrategy(req, res) {
    try {
        const { id: liveStrategyId } = req.params;
        const userId = req.user.id;
        logger.info(`用户 ${userId} 请求启动策略: ${liveStrategyId}`);

        // 1. 从数据库获取策略，以校验所有权并获取YAML内容
        const strategy = await LiveStrategy.findByPk(liveStrategyId);
        if (!strategy) {
            logger.error(`启动失败：未找到策略 ${liveStrategyId}`);
            return res.status(404).json({ success: false, error: '未找到指定策略' });
        }
        if (strategy.userId !== userId) {
            logger.error(`启动失败：用户 ${userId} 无权操作策略 ${liveStrategyId} 策略用户: ${strategy.userId}`);
            return res.status(403).json({ success: false, error: '无权操作此策略' });
        }

        logger.info('准备调用liveProcessManager.addStrategy 。。。');

        // 2. 调用新的进程管理器来添加（并确保启动）策略
        // 这将自动处理用户进程的创建/复用，并发送'add'指令
        // 传递策略类型参数
        const jwtToken = req.headers.authorization || (req.token ? `Bearer ${req.token}` : undefined);
        await liveProcessManager.addStrategy(userId, liveStrategyId, strategy.yaml, strategy.commodity, strategy.timeframe, strategy.initialCapital, strategy.strategyType, jwtToken, strategy.accountId);

        // 3. 更新数据库中的策略状态
        await strategy.update({ 
            status: 'running',
            startTime: new Date()
        });

        // 4. 同步内存Map状态
        await syncStrategyToMemory(liveStrategyId);

        logger.info(`策略 ${liveStrategyId} (用户 ${userId}) 已成功启动`);
        res.json({ success: true, message: '策略已启动' });

    } catch (error) {
        logger.error(`启动实盘策略 ${req.params.id} 失败: ${error.message}`, error.stack);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理停止实盘策略请求
 */
async function handleStopLiveStrategy(req, res) {
    try {
        const { id: strategyId } = req.params;
        const userId = req.user.id;
        logger.info(`用户 ${userId} 请求停止策略: ${strategyId}`);

        // 1. 再次校验策略所有权
        const strategy = await LiveStrategy.findByPk(strategyId);
        if (!strategy) {
            logger.error(`停止失败：未找到策略 ${strategyId}`);
            return res.status(404).json({ success: false, error: '未找到指定策略' });
        }
        if (strategy.userId !== userId) {
            logger.error(` 策略详情：${JSON.stringify(strategy)}`);
            logger.error(`停止失败：用户 ${userId} 无权操作策略 ${strategyId}, 策略用户: ${strategy.userid}`);
            return res.status(403).json({ success: false, error: '无权操作此策略' });
        }

        // 2. 调用进程管理器移除策略
        const jwtToken = req.headers.authorization || (req.token ? `Bearer ${req.token}` : undefined);
        await liveProcessManager.removeStrategy(userId, strategyId, jwtToken);

        // 3. 更新数据库中的策略状态
        await strategy.update({ 
            status: 'stopped',
            stopTime: new Date()
        });

        // 4. 同步内存Map状态
        await syncStrategyToMemory(strategyId);
        
        logger.info(`策略 ${strategyId} (用户 ${userId}) 已成功停止`);
        res.json({ success: true, message: '策略已停止' });

    } catch (error) {
        logger.error(`停止实盘策略 ${req.params.id} 失败: ${error.message}`, error.stack);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理停止整个实盘项目请求
 */
async function handleStopLiveGroup(req, res) {
    try {
        const { id } = req.params; // id是组合ID
        const userId = req.user.id;
        const username = req.user.username;

        logger.info(`用户${username}请求停止整个实盘项目组合: ${id}`);

        // 使用适配器停止整个组合
        const result = await liveStrategyAdapter.stopGroup(userId, id);

        if (result.status === 'success') {
            res.json({ success: true, message: result.message });
        } else {
            res.status(500).json({ success: false, error: result.error });
        }
    } catch (error) {
        logger.error(`停止实盘项目组合失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理获取实盘策略详情请求
 */
async function handleGetLiveStrategyDetails(req, res) {
    try {
        const { id } = req.params;
        const userId = req.user.id;

        logger.debug(`获取实盘策略详情: ${id}`);

        // 检查策略是否存在
        const strategy = liveStrategies.get(id);
        if (!strategy) {
            return res.status(404).json({ success: false, error: '未找到指定策略' });
        }

        // 检查是否是该用户的策略
        if (strategy.userId !== userId) {
            return res.status(403).json({ success: false, error: '无权查看此策略' });
        }

        // 获取策略详情
        const details = await getLiveStrategyDetails(id);

        res.json({ success: true, data: details });
    } catch (error) {
        logger.error(`获取实盘策略详情失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理获取可用交易通道请求
 */
async function handleGetTradingChannels(req, res) {
    try {
        const username = req.user.username;

        logger.debug(`获取用户 ${username} 的可用交易通道`);

        // 从 tradeHandler 获取可用交易通道
        const tradingChannels = await getTradingChannels(username);

        res.json({ success: true, data: tradingChannels });
    } catch (error) {
        logger.error(`获取可用交易通道失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理更新实盘策略配置请求
 */
async function handleUpdateLiveStrategy(req, res) {
    try {
        const { id } = req.params;
        const { strategyType, accountId, initialCapital, commissionRate, riskSettings } = req.body;
        const userId = req.user.id;
        const username = req.user.username;

        logger.info(`用户 ${username} 请求更新实盘策略配置: ${id}`);

        // 检查必要参数
        if (!strategyType || !accountId || !initialCapital || !commissionRate) {
            logger.error(`更新失败：缺少必要参数 strategyType = ${strategyType}, accountId = ${accountId}, initialCapital = ${initialCapital}, commissionRate = ${commissionRate}`);
            return res.status(400).json({ success: false, error: '缺少必要参数' });
        }

        // 1. 优先从数据库查询策略（确保数据准确性）
        const dbStrategy = await LiveStrategy.findByPk(id);

        logger.info(`查找实盘项目：dbStrategy = ${JSON.stringify(dbStrategy)}`);

        if (!dbStrategy) {
            logger.error(`更新失败：数据库中未找到策略 ${id}`);
            return res.status(404).json({ success: false, error: '未找到指定策略' });
        }

        // 2. 检查权限
        if (dbStrategy.userId !== userId) {
            logger.error(`更新失败：用户 ${userId} 无权操作策略 ${id} 策略用户: ${dbStrategy.userid}`);
            return res.status(403).json({ success: false, error: '无权操作此策略' });
        }

        // 3. 检查策略是否正在运行（从内存Map查询状态更快）
        const memoryStrategy = liveStrategies.get(id);
        const currentStatus = memoryStrategy ? memoryStrategy.status : dbStrategy.status;
        if (currentStatus === 'running') {
            return res.status(400).json({ success: false, error: '策略正在运行中，请先停止策略再更新配置' });
        }

        // 4. 更新数据库
        const updateTime = new Date();
        await dbStrategy.update({
            strategyType,
            accountId,
            initialCapital,
            commissionRate,
            riskSettings: JSON.stringify(riskSettings || {}),
            lastUpdateTime: updateTime
        });

        logger.info(`策略 ${id} 数据库配置已更新`);

        // 5. 同步更新内存Map（保持内存和数据库一致）
        const updatedStrategyData = {
            ...dbStrategy.toJSON(), // 获取最新的数据库数据
            strategyType,
            accountId,
            initialCapital,
            commissionRate,
            riskSettings: riskSettings || {},
            lastUpdateTime: updateTime
        };
        
        liveStrategies.set(id, updatedStrategyData);
        logger.info(`策略 ${id} 内存配置已同步更新`);

        // 注意：配置文件更新现在由 live_center.py 处理
        // 实盘配置文件将在策略启动时重新生成

        res.json({ success: true, message: '策略配置已成功更新' });
    } catch (error) {
        logger.error(`更新实盘策略配置失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理删除实盘策略请求
 */
async function handleDeleteLiveStrategy(req, res) {
    try {
        const { id } = req.params;
        const userId = req.user.id;
        const username = req.user.username;

        logger.info(`用户 ${username} 请求删除实盘策略: ${id}`);

        // 使用适配器删除策略
        const result = await liveStrategyAdapter.deleteStrategy(userId, id);

        if (result.success) {
            // 删除成功后，从内存Map中移除
            removeStrategyFromMemory(id);
            res.json(result);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        logger.error(`删除实盘策略失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

// 注意：createLiveConfigFile 函数已移除
// 配置文件创建现在由 live_center.py 处理

// 注意：generateConfigFiles 函数已移除
// 配置文件生成现在由 live_center.py 处理

// 注意：getStrategyYaml 函数已移除
// 策略YAML获取现在由 strategy_server.py 处理

/**
 * 检查交易通道健康状态
 * @param {string} accountId 账户ID
 * @returns {Promise<boolean>} 是否健康
 */
async function checkTradingChannelHealth(accountId) {
    try {
        // 解析账户ID，格式为 username_client_type
        const [username, clientType] = accountId.split('_');

        // 获取交易客户端信息
        const tradeHandler = require('./tradeHandler/index');
        const clientInfo = tradeHandler.tradingClients.get(accountId);

        if (!clientInfo) {
            logger.warn(`交易通道 ${accountId} 不存在，无法检查健康状态`);
            return false;
        }

        // 检查客户端是否在线
        if (!clientInfo.socket || !clientInfo.socket.connected) {
            logger.warn(`交易通道 ${accountId} Socket连接不可用，客户端可能离线`);
            return false;
        }

        logger.info(`通过Socket连接检查交易通道 ${accountId} 健康状态`);

        // 创建一个Promise，通过Socket发送健康检查请求并等待响应
        return new Promise((resolve) => {
            // 设置超时
            const timeout = setTimeout(() => {
                logger.error(`交易通道 ${accountId} 健康检查超时`);
                resolve(false);
            }, 5000);

            // 监听一次性响应
            clientInfo.socket.once('health_response', (data) => {
                clearTimeout(timeout);

                if (data && data.success) {
                    const healthData = data.data;
                    logger.info(`交易通道 ${accountId} 健康状态: ${healthData.status}`);

                    // 检查交易接口状态
                    if (healthData.trader && healthData.trader.status === 'connected') {
                        logger.info(`交易通道 ${accountId} 交易接口状态正常`);
                        resolve(true);
                    } else {
                        logger.warn(`交易通道 ${accountId} 交易接口状态异常: ${healthData.trader ? healthData.trader.status : '未知'}`);
                        resolve(false);
                    }
                } else {
                    logger.warn(`交易通道 ${accountId} 健康检查失败: ${data ? JSON.stringify(data) : '无响应数据'}`);
                    resolve(false);
                }
            });

            // 发送健康检查请求
            clientInfo.socket.emit('check_health', { timestamp: Date.now() });
            logger.debug(`已向交易通道 ${accountId} 发送健康检查请求`);
        });
    } catch (error) {
        logger.error(`检查交易通道健康状态失败: ${error.message}`);
        return false;
    }
}

/**
 * 获取用户交易配置
 * @param {string} username 用户名
 * @returns {Promise<Array>} 用户交易配置列表
 */
async function getUserTradingConfig(username) {
    try {
        const { UserTradingConfig, User } = require('../models');

        // 根据用户名查找用户ID
        const user = await User.findOne({ where: { username } });
        if (!user) {
            logger.warn(`用户 ${username} 不存在`);
            return [];
        }

        // 查询用户的交易配置
        const configs = await UserTradingConfig.findAll({
            where: {
                userId: user.id,
                isActive: true
            },
            order: [['createdAt', 'DESC']]
        });

        return configs.map(config => ({
            channelType: config.channelType,
            configName: config.configName,
            broker: config.broker || '',
            username: config.username,
            password: config.password, // 注意：实际使用时应该解密
            appid: config.appid || '',
            authcode: config.authcode || '',
            frontAddress: config.frontAddress
        }));
    } catch (error) {
        logger.error(`获取用户交易配置失败: ${error.message}`);
        return [];
    }
}

/**
 * 获取交易通道信息
 * @param {string} accountId 账户ID
 * @returns {Promise<Object|null>} 交易通道信息
 */
async function getTradingChannelInfo(accountId) {
    try {
        // 检查是否是公共IP交易通道
        if (accountId === 'openctp' || accountId === 'miniQMT') {
            // 从用户配置中获取公共IP通道的配置信息
            // TODO: 实现从数据库获取用户配置的逻辑
            // 这里暂时返回默认配置，实际应该从用户保存的配置中获取

            const defaultConfig = {
                id: accountId,
                module: accountId === 'openctp' ? 'TraderCTP' : 'TraderMiniQMT',
                front: accountId === 'openctp' ? 'tcp://************:4400' : 'tcp://127.0.0.1:8080',
                broker: '', // 空字符串，将从用户配置获取
                user: '', // 空字符串，将从用户配置获取
                pass: '', // 空字符串，将从用户配置获取
                appid: '', // 空字符串，将从用户配置获取
                authcode: '', // 空字符串，将从用户配置获取
                is_mock: false
            };

            // TODO: 这里应该查询数据库获取用户的实际配置
            // const userConfig = await getUserTradingChannelConfig(username, accountId);
            // if (userConfig) {
            //     defaultConfig.broker = userConfig.broker || '';
            //     defaultConfig.user = userConfig.username || '';
            //     defaultConfig.pass = userConfig.password || '';
            //     defaultConfig.appid = userConfig.appid || '';
            //     defaultConfig.authcode = userConfig.authcode || '';
            //     defaultConfig.front = userConfig.frontAddress || defaultConfig.front;
            // }

            return defaultConfig;
        }

        // 解析账户ID，格式为 username_client_type
        const [username, clientType] = accountId.split('_');

        // 获取交易客户端信息
        const tradeHandler = require('./tradeHandler/index');
        const clientInfo = tradeHandler.tradingClients.get(accountId);

        if (!clientInfo) {
            logger.error(`交易通道 ${accountId} 不存在，请确保交易客户端已连接`);
            return null;
        }

        // 构建交易通道信息
        return {
            id: accountId,
            module: clientType === 'easytrader_ths' ? 'EasyTraderXTP' : clientType,
            host: clientInfo.client_ip || '127.0.0.1',
            port: clientInfo.client_port || 3005,
            username: username,
            password: '******', // 实际应该从安全存储获取
            is_mock: false // 标记为真实通道
        };
    } catch (error) {
        logger.error(`获取交易通道信息失败: ${error.message}`);
        return null;
    }
}

/**
 * 获取可用交易通道
 * @param {string} username 用户名
 * @returns {Promise<Array>} 可用交易通道列表
 */
async function getTradingChannels(username) {
    try {
        const channels = [];

        // 1. 获取已注册的easytraderXTP通道（动态存在）
        const tradeHandler = require('./tradeHandler/index');
        const userClients = Array.from(tradeHandler.tradingClients.values())
            .filter(client => {
                // 用户名匹配
                if (client.username !== username) return false;

                // 检查是否是活跃的客户端
                const now = Date.now();
                const heartbeatTimeout = 2 * 60 * 1000; // 2分钟

                if (client.socket) {
                    return client.socket.connected;
                } else {
                    return (now - (client.last_heartbeat || client.registered_at)) < heartbeatTimeout;
                }
            })
            .map(client => ({
                id: `${client.client_id}`,
                name: `${client.client_id}`,
                type: client.client_type,
                status: 'online',
                needsConfig: false // easytraderXTP不需要额外配置
            }));

        channels.push(...userClients);

        // 2. 添加公共IP交易通道（始终存在，但需要配置）
        // 检查用户是否已配置OpenCTP
        const userTradingConfig = await getUserTradingConfig(username) || [];

        // OpenCTP通道
        const openctpConfig = userTradingConfig.find(config => config.channelType === 'openctp');
        channels.push({
            id: 'openctp',
            name: 'OpenCTP期货交易',
            type: 'openctp',
            status: 'online', // 始终在线
            needsConfig: true, // 始终需要配置
            configured: !!openctpConfig
        });

        // miniQMT通道（示例，可以添加更多公共IP通道）
        const miniQMTConfig = userTradingConfig.find(config => config.channelType === 'miniQMT');
        channels.push({
            id: 'miniQMT',
            name: 'miniQMT量化交易',
            type: 'miniQMT',
            status: 'online', // 始终在线
            needsConfig: true, // 始终需要配置
            configured: !!miniQMTConfig
        });

        logger.info(`用户 ${username} 可用交易通道: ${channels.length} 个`);
        return channels;
    } catch (error) {
        logger.error(`获取可用交易通道失败: ${error.message}`);
        return [];
    }
}

/**
 * 获取实盘策略详情
 * @param {string} liveStrategyId 实盘策略ID
 * @returns {Promise<Object>} 策略详情
 */
async function getLiveStrategyDetails(liveStrategyId) {
    try {
        const strategy = liveStrategies.get(liveStrategyId);
        if (!strategy) {
            throw new Error(`策略 ${liveStrategyId} 不存在`);
        }

        // 获取进程信息
        const processInfo = strategyProcesses.get(liveStrategyId);

        // 获取日志信息
        let logContent = '';
        if (processInfo && processInfo.logFilePath) {
            try {
                // 检查日志文件是否存在
                await fsp.access(processInfo.logFilePath);
                
                // 读取最后100行日志
                const logFileContent = await fsp.readFile(processInfo.logFilePath, 'utf8');
                const logLines = logFileContent.split('\n');
                const lastLines = logLines.slice(-100);
                logContent = lastLines.join('\n');
            } catch (error) {
                logger.error(`读取策略日志失败: ${error.message}`);
            }
        }

        // 检查进程状态
        let processStatus = 'unknown';
        if (processInfo && processInfo.process) {
            if (processInfo.process.exitCode === null) {
                processStatus = 'running';
            } else {
                processStatus = processInfo.process.exitCode === 0 ? 'completed' : 'error';
            }
        }

        // 获取性能数据
        let performanceData = {
            totalReturn: 0,
            dailyReturn: 0,
            logicalCapital: strategy.initialCapital,
            positions: []
        };

        // 如果策略正在运行，尝试获取实时性能数据
        if (strategy.status === 'running' && processStatus === 'running') {
            try {
                // 这里可以实现从策略进程获取实时性能数据的逻辑
                // 例如，通过读取策略生成的性能数据文件，或者通过API调用获取

                // 目前使用模拟数据，后续可以替换为真实数据
                performanceData = {
                    totalReturn: Math.random() * 10 - 5, // -5% 到 5% 之间的随机值
                    dailyReturn: Math.random() * 2 - 1, // -1% 到 1% 之间的随机值
                    logicalCapital: strategy.initialCapital * (1 + Math.random() * 0.1 - 0.05), // 初始资金上下浮动5%
                    positions: [
                        {
                            code: 'SSE.600000',
                            name: '浦发银行',
                            volume: Math.floor(Math.random() * 1000) * 100,
                            price: 10 + Math.random() * 2,
                            profit: Math.random() * 1000 - 500
                        }
                    ]
                };
            } catch (error) {
                logger.error(`获取策略性能数据失败: ${error.message}`);
            }
        }

        // 构建详情对象
        return {
            ...strategy,
            process: processInfo ? {
                pid: processInfo.pid,
                startTime: processInfo.startTime,
                status: processStatus,
                runningTime: processInfo.startTime ?
                    Math.floor((new Date() - new Date(processInfo.startTime)) / 1000) : 0
            } : null,
            performance: performanceData,
            log: logContent
        };
    } catch (error) {
        logger.error(`获取实盘策略详情失败: ${error.message}`);
        throw error;
    }
}

/**
 * 处理获取策略日志请求
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function handleGetStrategyLogs(req, res) {
    try {
        const strategyId = req.params.id;
        let userId, groupId;
        if (strategyId.includes('_group_')) {
            // 解析 projectId 格式: user_{userId}_group_{groupId}
            const match = strategyId.match(/user_(\d+)_group_(\d+)/);
            if (match) {
                userId = match[1];
                groupId = match[2];
            } else {
                return res.status(400).json({ error: 'Invalid project ID format' });
            }
        } else {
            // 旧格式，直接使用 strategyId 作为 groupId，假设 userId 从其他地方获取
            groupId = strategyId;
            userId = req.query.userId || '5'; // 假设默认用户ID为5，或从请求中获取
        }

        const logsDir = path.join(__dirname, '../_Providers/_Python/strategy/portfolio/live_groups', `user_${userId}`, `group_${groupId}`, 'logs');
        console.log(`[日志获取] 尝试读取日志目录: ${logsDir}`);

        if (!fs.existsSync(logsDir)) {
            console.log(`[日志获取] 日志目录不存在: ${logsDir}`);
            return res.json({ logs: [] });
        }

        // 查找所有 Engine*.log 文件，按修改时间排序，取最新
        const logFiles = fs.readdirSync(logsDir)
            .filter(file => /^Engine.*\.log$/.test(file))
            .map(file => ({
                file,
                mtime: fs.statSync(path.join(logsDir, file)).mtime
            }))
            .sort((a, b) => b.mtime - a.mtime);

        if (logFiles.length === 0) {
            console.log(`[日志获取] 未找到 Engine*.log 文件`);
            return res.json({ logs: [] });
        }

        const latestLogFile = path.join(logsDir, logFiles[0].file);
        console.log(`[日志获取] 读取最新日志文件: ${latestLogFile}`);

        const logContent = fs.readFileSync(latestLogFile, 'utf8');
        const logLines = logContent.split('\n');
        const recentLogs = logLines.slice(-100); // 返回最近的100条日志
        res.json({ logs: recentLogs });
    } catch (error) {
        console.error(`[日志获取] 错误: ${error.message}`);
        res.status(500).json({ error: 'Failed to fetch strategy logs' });
    }
}

/**
 * 处理获取策略持仓请求
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function handleGetStrategyPositions(req, res) {
    try {
        const { id } = req.params;
        const userId = req.user.id;
        logger.info(`获取策略持仓: ${id}`);

        // 检查策略是否存在
        const strategy = liveStrategies.get(id);
        if (!strategy) {
            return res.status(404).json({
                success: false,
                error: `策略 ${id} 不存在`
            });
        }

        // 从Python引擎获取持仓数据
        try {
            const axios = require('axios');
            const pythonEngineUrl = `http://localhost:5001/positions/${id}`;
            
            logger.info(`[持仓查询] 调用Python引擎接口: ${pythonEngineUrl}`);
            
            const response = await axios.get(pythonEngineUrl, {
                timeout: 5000
            });
            
            if (response.data && response.data.success) {
                const positions = response.data.data;
                logger.info(`[持仓查询] 从Python引擎获取到 ${positions.length} 个持仓`);
                
                // 转换持仓数据格式，适配前端显示
                const formattedPositions = positions.map(pos => {
                    return {
                        symbol: pos.symbol,
                        name: pos.name,
                        logicalPosition: pos.logical_position,  // 逻辑持仓
                        expectedPosition: pos.expected_position,  // 预期持仓
                        ratio: pos.ratio,  // 资金比例
                        initialCapital: pos.initial_capital,  // 初始资金
                        currentPrice: pos.current_price,  // 当前价格
                        marketValue: pos.market_value,  // 市值
                        // 兼容旧格式的字段
                        quantity: pos.expected_position,
                        price: pos.current_price,
                        cost: pos.current_price,  // 简化处理
                        profit: 0,  // 暂不计算盈亏
                        profitPercent: 0  // 暂不计算盈亏比例
                    };
                });
                
                return res.json({
                    success: true,
                    data: formattedPositions
                });
            } else {
                logger.warn(`[持仓查询] Python引擎返回失败: ${response.data?.error || '未知错误'}`);
                return res.json({
                    success: true,
                    data: []
                });
            }
        } catch (error) {
            logger.error(`[持仓查询] 调用Python引擎接口失败: ${error.message}`);
            
            // 如果Python引擎不可用，返回空持仓
            return res.json({
                success: true,
                data: []
            });
        }
    } catch (error) {
        logger.error(`获取策略持仓失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            error: `获取策略持仓失败: ${error.message}`
        });
    }
}

/**
 * 处理获取策略交易记录请求
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function handleGetStrategyTrades(req, res) {
    try {
        const { id } = req.params;
        logger.info(`获取策略交易记录: ${id}`);

        // 检查策略是否存在
        const strategy = liveStrategies.get(id);
        if (!strategy) {
            return res.status(404).json({
                success: false,
                error: `策略 ${id} 不存在`
            });
        }

        // 获取交易记录文件路径
        const tradesFilePath = path.join(config.liveLogDir, `${id}_trades.json`);

        try {
            // 检查交易记录文件是否存在
            await fsp.access(tradesFilePath);
            
            // 读取交易记录文件
            const tradesContent = await fsp.readFile(tradesFilePath, 'utf8');
            let trades = [];

            try {
                trades = JSON.parse(tradesContent);
            } catch (error) {
                logger.error(`解析交易记录文件失败: ${error.message}`);
                return res.json({
                    success: true,
                    data: []
                });
            }

            // 转换交易记录格式
            const formattedTrades = trades.map(trade => {
                // 从代码中提取股票名称（如果有）
                let name = trade.code || trade.symbol;
                const parts = (trade.code || trade.symbol).split('.');
                if (parts.length > 1) {
                    name = parts[1];
                }

                return {
                    time: trade.time,
                    symbol: trade.code || trade.symbol,
                    name: name,
                    direction: trade.direction.toLowerCase() === 'buy' ? 'buy' : 'sell',
                    quantity: trade.volume || trade.quantity,
                    price: trade.price,
                    amount: (trade.price * (trade.volume || trade.quantity))
                };
            });

            return res.json({
                success: true,
                data: formattedTrades
            });
        } catch (error) {
            // 如果交易记录文件不存在或读取失败
            logger.warn(`交易记录文件不存在或无法读取: ${error.message}`);
            return res.json({
                success: true,
                data: [] // 返回空交易记录
            });
        }
    } catch (error) {
        logger.error(`获取策略交易记录失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            error: `获取策略交易记录失败: ${error.message}`
        });
    }
}

/**
 * 处理保存交易通道配置请求
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function handleSaveTradingChannelConfig(req, res) {
    try {
        const { username } = req.user; // 从JWT token中获取用户名
        const config = req.body;

        logger.info(`保存交易通道配置: 用户=${username}, 类型=${config.channelType}`);

        // 验证必要字段
        if (!config.channelType || !config.configName || !config.username || !config.password || !config.frontAddress) {
            return res.status(400).json({
                success: false,
                error: '缺少必要的配置字段'
            });
        }

        // 保存配置
        const success = await saveTradingChannelConfig(username, config);

        if (success) {
            return res.json({
                success: true,
                message: '配置保存成功'
            });
        } else {
            return res.status(500).json({
                success: false,
                error: '配置保存失败'
            });
        }
    } catch (error) {
        logger.error(`处理保存交易通道配置请求失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            error: `保存配置失败: ${error.message}`
        });
    }
}

/**
 * 处理获取交易通道配置请求
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function handleGetTradingChannelConfig(req, res) {
    try {
        const { username } = req.user; // 从JWT token中获取用户名
        const { channelType } = req.params;

        logger.info(`获取交易通道配置: 用户=${username}, 类型=${channelType}`);

        // 获取配置
        const config = await getTradingChannelConfig(username, channelType);

        if (config) {
            return res.json({
                success: true,
                data: config
            });
        } else {
            return res.status(404).json({
                success: false,
                error: '未找到配置'
            });
        }
    } catch (error) {
        logger.error(`处理获取交易通道配置请求失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            error: `获取配置失败: ${error.message}`
        });
    }
}

/**
 * 获取策略调试信息
 */
async function handleGetStrategyDebugInfo(req, res) {
    try {
        const { id } = req.params;
        const userId = req.user.id;

        logger.debug(`获取策略调试信息: ${id}, 用户ID: ${userId}`);

        // 检查策略是否存在
        const strategy = liveStrategies.get(id);
        logger.debug(`内存中策略查找结果: ${strategy ? '找到' : '未找到'}`);
        
        if (!strategy) {
            logger.warn(`策略 ${id} 在内存中未找到`);
            return res.status(404).json({ success: false, error: '未找到指定策略' });
        }

        // 检查是否是该用户的策略
        logger.debug(`策略用户ID: ${strategy.userId}, 当前用户ID: ${userId}`);
        if (strategy.userId !== userId) {
            logger.warn(`用户 ${userId} 无权查看策略 ${id} (策略用户: ${strategy.userId})`);
            return res.status(403).json({ success: false, error: '无权查看此策略' });
        }

        logger.debug(`开始调用适配器获取调试信息...`);
        // 使用适配器获取调试信息
        const debugInfo = await liveStrategyAdapter.getStrategyDebugInfo(userId, id);
        logger.debug(`适配器返回结果: ${debugInfo.success ? '成功' : '失败'}`);

        if (debugInfo.success) {
            logger.info(`成功获取策略 ${id} 的调试信息`);
            res.json({ success: true, data: debugInfo.data });
        } else {
            logger.error(`获取策略 ${id} 调试信息失败: ${debugInfo.error}`);
            res.status(500).json({ success: false, error: debugInfo.error });
        }
    } catch (error) {
        logger.error(`获取策略调试信息失败: ${error.message}`);
        logger.error(`错误堆栈: ${error.stack}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理测试实盘进程活动状态请求
 */
async function handleTestProcessActivity(req, res) {
    try {
        const userId = req.user.id;
        const username = req.user.username;

        logger.info(`用户 ${username} 请求测试实盘进程活动状态`);

        // 调用LiveProcessManager测试进程活动状态
        const testResult = await liveProcessManager.testProcessActivity(userId);

        res.json({ 
            success: true, 
            message: '进程活动状态测试完成',
            data: testResult 
        });
    } catch (error) {
        logger.error(`测试实盘进程活动状态失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 处理测试实盘数据连接状态请求
 */
async function handleTestRTConnection(req, res) {
    try {
        const { liveStrategyId } = req.body;
        const username = req.user.username;

        logger.info(`用户 ${username} 请求测试实盘策略 ${liveStrategyId} 的数据连接状态`);

        if (!liveStrategyId) {
            return res.status(400).json({ success: false, error: '缺少实盘策略ID' });
        }

        // 获取策略详情
        const strategyDetails = await getLiveStrategyDetails(liveStrategyId);
        if (!strategyDetails) {
            return res.status(404).json({ success: false, error: '实盘策略不存在' });
        }

        // 检查策略是否属于当前用户
        if (strategyDetails.userId !== req.user.id) {
            return res.status(403).json({ success: false, error: '无权访问此策略' });
        }

        // 从配置文件读取rt_server.url
        const config = require('../config.json');
        const rtServerUrl = config.rt_server?.url;
        
        if (!rtServerUrl) {
            logger.error('配置文件中缺少rt_server.url配置');
            return res.status(500).json({ success: false, error: '服务器配置错误' });
        }

        // 调用健康检查接口
        const axios = require('axios');
        const healthCheckUrl = `${rtServerUrl}/subscribe_health`;
        
        try {
            const response = await axios.post(healthCheckUrl, {
                key: liveStrategyId
            }, {
                timeout: 5000 // 5秒超时
            });

            // 检查后端返回的result字段
            const isConnected = response.data.result === true;
            const result = {
                success: true, // API调用成功
                result: isConnected, // 实际的连接状态
                message: response.data.message || '连接状态检查完成'
            };

            logger.info(`实盘策略 ${liveStrategyId} 数据连接状态测试完成: 连接状态=${isConnected}, 消息=${result.message}`);
            res.json(result);

        } catch (axiosError) {
            logger.error(`调用健康检查接口失败: ${axiosError.message}`);
            res.json({ success: false, message: '数据服务连接失败' });
        }

    } catch (error) {
        logger.error(`测试数据连接状态失败: ${error.message}`);
        res.status(500).json({ success: false, error: '服务器内部错误' });
    }
}

/**
 * 获取策略运行时数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function handleGetStrategyRuntimeData(req, res) {
    try {
        const { id } = req.params;
        const userId = req.user.id;
        
        console.log(`[LiveStrategyHandler] 获取策略运行时数据: strategyId=${id}, userId=${userId}`);
        
        // 1. 从内存中获取策略信息（已在面板初始化时加载）
        const strategy = liveStrategies.get(id);
        if (!strategy) {
            console.log(`[LiveStrategyHandler] 策略不存在或未加载到内存: ${id}`);
            return res.status(404).json({
                success: false,
                error: '策略不存在或未加载到内存'
            });
        }

        // 检查策略是否属于当前用户
        if (strategy.userId !== userId) {
            console.log(`[LiveStrategyHandler] 策略不属于当前用户: ${id}, userId: ${userId}`);
            return res.status(403).json({
                success: false,
                error: '无权访问此策略'
            });
        }
        
        // 2. 获取策略类型和初始资金
        let strategyType = 'portfolio'; // 默认类型为portfolio
        
        // 从内存中的策略数据获取策略类型（策略数据已在面板初始化时加载）
        if (strategy.strategyType) {
            strategyType = strategy.strategyType;
        }
        
        // 获取初始资金
        const initialCapital = parseFloat(strategy.initialCapital) || 100000;
        
        console.log(`[LiveStrategyHandler] 策略类型: ${strategyType}, 初始资金: ${initialCapital}`);
        
        // 3. 调用策略日志解析器
        const StrategyLogParser = require('../services/StrategyLogParser');

        // *** 解析出通用格式的结果，格式与策略无关，前端可以读取和展示，一次调用，仅获取一个实盘策略的运行数据 ***
        const parseResult = await StrategyLogParser.parseSingleStrategyRuntimeData(
            id,
            userId.toString(),
            strategyType,
            initialCapital
        );
        
        console.log(`[LiveStrategyHandler] 策略日志解析结果:`, parseResult);
        
        // 检查解析结果是否有错误
        if (parseResult.error) {
            console.log(`[LiveStrategyHandler] 策略日志解析失败: ${parseResult.error}`);
            return res.status(500).json({
                success: false,
                error: parseResult.error || '策略日志解析失败'
            });
        }
        
        console.log(`[LiveStrategyHandler] 策略运行时数据获取成功`);
        
        // 4. 返回统一的数据结构
        return res.json({
            success: true,
            data: parseResult
        });
        
    } catch (error) {
        console.error(`[LiveStrategyHandler] 获取策略运行时数据时出错:`, error);
        return res.status(500).json({
            success: false,
            error: '获取策略运行时数据失败'
        });
    }
}

/**
 * 保存用户交易通道配置
 * @param {string} username 用户名
 * @param {Object} config 配置对象
 * @returns {Promise<boolean>} 是否成功
 */
async function saveTradingChannelConfig(username, config) {
    try {
        const { UserTradingConfig, User } = require('../models');

        // 根据用户名查找用户ID
        const user = await User.findOne({ where: { username } });
        if (!user) {
            logger.error(`用户 ${username} 不存在`);
            return false;
        }

        // 检查是否已存在相同类型的配置
        const existingConfig = await UserTradingConfig.findOne({
            where: {
                userId: user.id,
                channelType: config.channelType
            }
        });

        const configData = {
            userId: user.id,
            channelType: config.channelType,
            configName: config.configName,
            broker: config.broker || '',
            username: config.username,
            password: config.password, // 注意：实际应该加密存储
            appid: config.appid || '',
            authcode: config.authcode || '',
            frontAddress: config.frontAddress,
            isActive: true
        };

        if (existingConfig) {
            // 更新现有配置
            await existingConfig.update(configData);
            logger.info(`更新用户 ${username} 的 ${config.channelType} 配置`);
        } else {
            // 创建新配置
            await UserTradingConfig.create(configData);
            logger.info(`创建用户 ${username} 的 ${config.channelType} 配置`);
        }

        return true;
    } catch (error) {
        logger.error(`保存用户交易配置失败: ${error.message}`);
        return false;
    }
}

/**
 * 同步策略数据到内存Map
 * @param {string} strategyId 策略ID
 * @returns {Promise<boolean>} 是否成功
 */
async function syncStrategyToMemory(strategyId) {
    try {
        const dbStrategy = await LiveStrategy.findByPk(strategyId);
        if (dbStrategy) {
            const strategyData = dbStrategy.toJSON();
            liveStrategies.set(strategyId, strategyData);
            logger.info(`策略 ${strategyId} 已同步到内存`);
            return true;
        } else {
            logger.warn(`策略 ${strategyId} 在数据库中不存在，无法同步到内存`);
            // 如果数据库中不存在，从内存中移除
            liveStrategies.delete(strategyId);
            return false;
        }
    } catch (error) {
        logger.error(`同步策略 ${strategyId} 到内存失败: ${error.message}`);
        return false;
    }
}

/**
 * 从内存Map中移除策略
 * @param {string} strategyId 策略ID
 */
function removeStrategyFromMemory(strategyId) {
    const existed = liveStrategies.has(strategyId);
    liveStrategies.delete(strategyId);
    if (existed) {
        logger.info(`策略 ${strategyId} 已从内存中移除`);
    }
}

/**
 * 获取用户特定类型的交易通道配置
 * @param {string} username 用户名
 * @param {string} channelType 通道类型
 * @returns {Promise<Object|null>} 配置对象或null
 */
async function getTradingChannelConfig(username, channelType) {
    try {
        const { UserTradingConfig, User } = require('../models');

        // 根据用户名查找用户ID
        const user = await User.findOne({ where: { username } });
        if (!user) {
            logger.warn(`用户 ${username} 不存在`);
            return null;
        }

        // 查询特定类型的配置
        const config = await UserTradingConfig.findOne({
            where: {
                userId: user.id,
                channelType: channelType,
                isActive: true
            }
        });

        if (!config) {
            return null;
        }

        return {
            channelType: config.channelType,
            configName: config.configName,
            broker: config.broker || '',
            username: config.username,
            password: config.password, // 注意：实际使用时应该解密
            appid: config.appid || '',
            authcode: config.authcode || '',
            frontAddress: config.frontAddress
        };
    } catch (error) {
        logger.error(`获取用户交易配置失败: ${error.message}`);
        return null;
    }
}

/**
 * 处理交易通道健康测试请求
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function handleTradingChannelHealthTest(req, res) {
    try {
        const { channelId } = req.body;
        const userId = req.user.id;
        const username = req.user.username;

        logger.info(`用户 ${username} 请求测试交易通道健康状态: ${channelId}`);

        if (!channelId) {
            return res.status(400).json({
                success: false,
                error: '缺少交易通道ID'
            });
        }

        // 1. 获取交易通道信息
        const channelInfo = await getTradingChannelInfo(channelId);
        if (!channelInfo) {
            logger.error(`交易通道 ${channelId} 不存在或无法获取信息`);
            return res.status(404).json({
                success: false,
                error: '交易通道不存在'
            });
        }

        logger.info(`交易通道信息: ${JSON.stringify(channelInfo)}`);

        // 2. 根据通道类型进行健康测试
        if (channelInfo.module === 'EasyTraderXTP' || channelId.includes('easytrader_ths')) {
            // 处理 easytrader_ths 类型的交易通道
            return await handleEasyTraderHealthTest(channelId, username, res);
        } else {
            logger.warn(`暂不支持交易通道类型: ${channelInfo.module}`);
            return res.status(400).json({
                success: false,
                error: `暂不支持交易通道类型: ${channelInfo.module}`
            });
        }

    } catch (error) {
        logger.error(`交易通道健康测试失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            error: `交易通道健康测试失败: ${error.message}`
        });
    }
}

/**
 * 处理 easytrader_ths 类型的交易通道健康测试
 * @param {string} channelId - 通道ID
 * @param {string} username - 用户名
 * @param {Object} res - 响应对象
 */
async function handleEasyTraderHealthTest(channelId, username, res) {
    try {
        // 获取交易客户端信息
        const tradeHandler = require('./tradeHandler/index');
        const clientInfo = tradeHandler.tradingClients.get(channelId);

        console.log('[LiveStrategyHandler] 频道ID=', channelId, '，交易客户端信息:', clientInfo);

        if (!clientInfo) {
            logger.error(`交易客户端 ${channelId} 不存在`);
            return res.status(404).json({
                success: false,
                error: '交易客户端不存在'
            });
        }

        // 检查客户端是否在线
        if (!clientInfo.socket || !clientInfo.socket.connected) {
            logger.warn(`交易客户端 ${channelId} Socket连接不可用`);
            return res.json({
                success: false,
                error: '交易客户端离线',
                status: 'offline'
            });
        }

        logger.info(`通过Socket连接测试交易客户端 ${channelId} 健康状态`);

        // 创建一个Promise，通过Socket发送健康测试请求并等待响应
        return new Promise((resolve) => {
            // 设置超时
            const timeout = setTimeout(() => {
                logger.error(`交易客户端 ${channelId} 健康测试超时`);
                resolve(res.json({
                    success: false,
                    error: '健康测试超时',
                    status: 'timeout'
                }));
            }, 5000);

            // 监听一次性响应
            clientInfo.socket.once('health_test_response', (data) => {
                clearTimeout(timeout);

                if (data && data.success) {
                    logger.info(`交易客户端 ${channelId} 健康测试成功: ${data.message}`);
                    resolve(res.json({
                        success: true,
                        status: 'healthy',
                        data: {
                            client_id: data.client_id,
                            message: data.message,
                            timestamp: data.timestamp
                        }
                    }));
                } else {
                    logger.warn(`交易客户端 ${channelId} 健康测试失败: ${data ? JSON.stringify(data) : '无响应数据'}`);
                    resolve(res.json({
                        success: false,
                        error: data ? data.message : '健康测试失败',
                        status: 'unhealthy'
                    }));
                }
            });

            // 发送健康测试请求
            clientInfo.socket.emit('health_test', {
                test: true,
                timestamp: Date.now()
            });
            logger.debug(`已向交易客户端 ${channelId} 发送健康测试请求`);
        });

    } catch (error) {
        logger.error(`处理 easytrader_ths 健康测试失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            error: `健康测试失败: ${error.message}`
        });
    }
}

// 导出模块
module.exports = {
    init: async () => {
        // 确保目录存在
        await ensureDirectoriesExist();

        // 从数据库加载实盘策略到内存
        try {
            await loadLiveStrategiesFromDB();
            logger.info('实盘策略模块初始化成功');
        } catch (error) {
            logger.error(`实盘策略模块初始化失败: ${error.message}`);
        }

        // 返回路由
        return initRoutes();
    },
    getRouter: () => {
        // 同步创建必要目录
        try {
            ensureDirectoriesExistSync();
            logger.info('实盘策略模块目录初始化成功');
        } catch (error) {
            logger.error(`实盘策略模块目录初始化失败: ${error.message}`);
        }
        
        // 初始化路由并返回
        return initRoutes();
    },

    // 导出函数供其他模块使用
    getTradingChannels,

    // 新增：同步函数
    syncStrategyToMemory: syncStrategyToMemory,
    removeStrategyFromMemory: removeStrategyFromMemory,

    // 导出内部函数，用于测试
    _internal: {
        loadLiveStrategiesFromDB,
        liveStrategies // 导出Map供调试用
    }
};
