import { registerFigure, registerOverlay } from 'klinecharts';

// 定义三角形属性接口
interface TriangleAttrs {
  x: number;
  y: number;
  width: number;
  height: number;
}

// 定义样式接口
interface TriangleStyles {
  color: string;
}

// 注册向上三角形图形
export const registerTriangleDown = () => {
  registerFigure({
    name: 'triangledown',
    draw: (ctx, attrs: TriangleAttrs, styles: TriangleStyles) => {
      const { x, y, width, height } = attrs;
      const { color } = styles;
      
      ctx.beginPath();
      // 从底部开始绘制三角形
      ctx.moveTo(x, y + height / 2); // 底部中点
      ctx.lineTo(x - width / 2, y - height / 2); // 左上角
      ctx.lineTo(x + width / 2, y - height / 2); // 右上角
      ctx.closePath();
      
      // 填充颜色
      ctx.fillStyle = color;
      ctx.fill();
    },
    // 检查点击是否在三角形内
    checkEventOn: (coordinate: { x: number; y: number }, attrs: TriangleAttrs) => {
      const { x, y } = coordinate;
      const { width, height } = attrs;
      // 使用三角形面积法判断点是否在三角形内
      const area = width * height;
      const area1 = Math.abs(x * height) + Math.abs(y * width);
      return area1 <= area / 2;
    }
  });

  // 注册买入信号覆盖物
  registerOverlay({
    name: 'triangledown',
    totalStep: 1,
    createPointFigures: ({ coordinates }) => {
      return {
        type: 'triangledown',
        attrs: {
          x: coordinates[0].x,
          y: coordinates[0].y,
          width: 12,
          height: 12
        },
        styles: { color: '#f44336' } // 红色
      }
    }
  });
}

// 注册向下三角形图形
export const registerTriangleUp = () => {
  registerFigure({
    name: 'triangleup',
    draw: (ctx, attrs: TriangleAttrs, styles: TriangleStyles) => {
      const { x, y, width, height } = attrs;
      const { color } = styles;
      
      ctx.beginPath();
      // 从顶部开始绘制三角形
      ctx.moveTo(x, y - height / 2); // 顶部中点
      ctx.lineTo(x - width / 2, y + height / 2); // 左下角
      ctx.lineTo(x + width / 2, y + height / 2); // 右下角
      ctx.closePath();
      
      // 填充颜色
      ctx.fillStyle = color;
      ctx.fill();
    },
    // 检查点击是否在三角形内
    checkEventOn: (coordinate: { x: number; y: number }, attrs: TriangleAttrs) => {
      const { x, y } = coordinate;
      const { width, height } = attrs;
      // 使用三角形面积法判断点是否在三角形内
      const area = width * height;
      const area1 = Math.abs(x * height) + Math.abs(y * width);
      return area1 <= area / 2;
    }
  });

  // 注册卖出信号覆盖物
  registerOverlay({
    name: 'triangleup',
    totalStep: 1,
    createPointFigures: ({ coordinates }) => {
      return {
        type: 'triangleup',
        attrs: {
          x: coordinates[0].x,
          y: coordinates[0].y,
          width: 12,
          height: 12
        },
        // 使用柔和的蓝色
        styles: { color: '#4166f5' } 
      }
    }
  });
}