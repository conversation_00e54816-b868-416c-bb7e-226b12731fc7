#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
加密货币数据适配器
连接到后端的加密货币数据服务获取数据
"""

import requests
import logging
import time
from typing import Dict, List, Any, Optional, Union

class OKXAdapter:
    """加密货币数据适配器"""

    def __init__(self, config: Dict[str, Any]):
        """初始化适配器"""
        self.logger = logging.getLogger(__name__)

        # 配置参数
        self.backend_url = config.get('backend_url', 'http://127.0.0.1:3000')

        # 请求超时时间（秒）
        self.timeout = config.get('timeout', 10)

        # 重试次数
        self.max_retries = config.get('max_retries', 3)

        # 重试间隔（秒）
        self.retry_interval = config.get('retry_interval', 1)

        # 周期映射 (Wonder Trader -> 后端API)
        self.period_map = {
            'm1': '1m',
            'm5': '5m',
            'm15': '15m',
            'm30': '30m',
            'm60': '1h',
            'd1': '1D',
            'w1': '1W',
            'mon1': '1M'
        }

        self.logger.info(f"加密货币数据适配器初始化完成，后端URL: {self.backend_url}")

    def fetch_data(self, symbol: str, exchange: str, period: str = 'm1') -> Optional[Dict[str, Any]]:
        """
        获取数据
        返回数据字典，或者 None（如果获取失败）
        """
        try:
            # 转换周期
            api_period = self.period_map.get(period, '1m')

            # 构建请求URL
            url = f"{self.backend_url}/api/market/klines"

            # 构建请求参数
            params = {
                'symbol': symbol,
                'interval': api_period,
                'market': 'CRYPTO',
                'exchange': exchange
            }

            self.logger.info(f"获取加密货币数据: {exchange}.{symbol}, 周期: {period}")

            # 发送请求
            for retry in range(self.max_retries):
                try:
                    response = requests.get(url, params=params, timeout=self.timeout)

                    # 检查响应状态码
                    if response.status_code != 200:
                        self.logger.warning(f"请求失败，状态码: {response.status_code}, 重试 {retry+1}/{self.max_retries}")
                        time.sleep(self.retry_interval)
                        continue

                    # 解析响应数据
                    data = response.json()

                    # 检查数据
                    if not data or not data.get('success') or not data.get('data') or len(data['data']) == 0:
                        self.logger.warning(f"返回数据为空或格式错误，重试 {retry+1}/{self.max_retries}")
                        time.sleep(self.retry_interval)
                        continue

                    # 获取最新的一条数据
                    latest_data = data['data'][0]

                    # 转换数据格式
                    result = self._convert_data(latest_data, period)

                    self.logger.info(f"获取加密货币数据成功: {exchange}.{symbol}, 周期: {period}")
                    return result

                except requests.exceptions.RequestException as e:
                    self.logger.warning(f"请求异常: {e}, 重试 {retry+1}/{self.max_retries}")
                    time.sleep(self.retry_interval)
                    continue

                except Exception as e:
                    self.logger.error(f"处理响应数据时出错: {e}")
                    time.sleep(self.retry_interval)
                    continue

            self.logger.error(f"获取加密货币数据失败，已重试 {self.max_retries} 次")
            return None

        except Exception as e:
            self.logger.error(f"获取加密货币数据时出错: {e}")
            return None

    def fetch_symbols(self) -> Optional[List[Dict[str, Any]]]:
        """
        获取加密货币品种列表
        返回品种列表，或者 None（如果获取失败）
        """
        try:
            # 构建请求URL
            url = f"{self.backend_url}/api/market/symbols"

            # 构建请求参数
            params = {
                'market': 'CRYPTO'
            }

            self.logger.info("获取加密货币品种列表")

            # 发送请求
            for retry in range(self.max_retries):
                try:
                    response = requests.get(url, params=params, timeout=self.timeout)

                    # 检查响应状态码
                    if response.status_code != 200:
                        self.logger.warning(f"请求失败，状态码: {response.status_code}, 重试 {retry+1}/{self.max_retries}")
                        time.sleep(self.retry_interval)
                        continue

                    # 解析响应数据
                    data = response.json()

                    # 检查数据
                    if not data or not data.get('success') or not data.get('data') or not isinstance(data['data'], list):
                        self.logger.warning(f"返回数据为空或格式错误，重试 {retry+1}/{self.max_retries}")
                        time.sleep(self.retry_interval)
                        continue

                    # 获取品种列表
                    symbols = data['data']

                    self.logger.info(f"获取加密货币品种列表成功，共 {len(symbols)} 个品种")
                    return symbols

                except requests.exceptions.RequestException as e:
                    self.logger.warning(f"请求异常: {e}, 重试 {retry+1}/{self.max_retries}")
                    time.sleep(self.retry_interval)
                    continue

                except Exception as e:
                    self.logger.error(f"处理响应数据时出错: {e}")
                    time.sleep(self.retry_interval)
                    continue

            self.logger.error(f"获取加密货币品种列表失败，已重试 {self.max_retries} 次")
            return None

        except Exception as e:
            self.logger.error(f"获取加密货币品种列表时出错: {e}")
            return None

    def _convert_data(self, data: Dict[str, Any], period: str) -> Dict[str, Any]:
        """转换数据格式"""
        # 后端API返回的K线数据格式：
        # {time: timestamp, open: number, high: number, low: number, close: number, volume: number, ...}

        # 提取时间戳
        timestamp = int(data['time'])  # 确保是秒级时间戳

        # 转换为日期和时间字符串
        date_str = time.strftime('%Y%m%d', time.localtime(timestamp))
        time_str = time.strftime('%H:%M:%S', time.localtime(timestamp))

        # 构建结果
        result = {
            'date': date_str,
            'time': time_str,
            'open': float(data['open']),
            'high': float(data['high']),
            'low': float(data['low']),
            'close': float(data['close']),
            'volume': float(data['volume']),
            'turnover': float(data.get('volCcy', 0.0)),  # 可能的成交额字段
            'open_interest': 0.0,  # 加密货币数据中通常没有持仓量
            'period': period
        }

        return result
