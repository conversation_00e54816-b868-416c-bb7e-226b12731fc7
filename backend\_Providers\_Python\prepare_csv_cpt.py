# backend/_Providers/_Python/prepare_csv_cpt.py

import os
import json
import pandas as pd
import datetime
import time
import pytz
import sys
from typing import List, Dict, Optional, Callable

# === 新增: 查找项目根目录和 root 路径替换工具 ===
def find_project_root():
    """
    向上查找 backend 目录，返回其上一级目录（即项目根目录）的绝对路径。
    """
    import os
    current = os.path.abspath(os.path.dirname(__file__))
    while True:
        if os.path.isdir(os.path.join(current, 'backend')):
            return current
        parent = os.path.dirname(current)
        if parent == current:
            raise RuntimeError("未找到 backend 目录，无法定位项目根目录")
        current = parent


def replace_root_prefix(path):
    """
    如果 path 以 root/ 开头，则将 root 替换为项目根目录绝对路径。
    否则原样返回。
    """
    import os
    if path.startswith('root/'):
        project_root = find_project_root()
        rel_path = path[5:]
        return os.path.normpath(os.path.join(project_root, rel_path))
    return path

# --- Default Configuration ---
DEFAULT_TIMEZONE = 'Asia/Shanghai' # Or 'UTC' if your WT operates in UTC

# --- Configuration Loading (Copied from prepare_csv.py for consistency) ---
def load_config() -> Optional[Dict]:
    """Loads configuration from config.json"""
    config = None
    try:
        script_dir = os.path.dirname(__file__)
        config_path = os.path.join(script_dir, 'config.json')

        if not os.path.exists(config_path):
            print(f"[ERROR] prepare_csv_cpt: config.json not found at: {config_path}", file=sys.stderr)
            return None

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        # print(f"[INFO] prepare_csv_cpt: Successfully loaded config from: {config_path}")
        return config

    except FileNotFoundError:
        print(f"[ERROR] prepare_csv_cpt: config.json not found at expected location.", file=sys.stderr)
        return None
    except json.JSONDecodeError as jde:
         print(f"[ERROR] prepare_csv_cpt: Error decoding config.json: {jde}", file=sys.stderr)
         return None
    except Exception as e:
        print(f"[ERROR] prepare_csv_cpt: Failed to load configuration: {e}", file=sys.stderr)
        return None

# --- Crypto Specific Path/Filename Functions ---

def get_crypto_period_subdir(period: str) -> Optional[str]:
    """Maps WT period string to crypto subdirectory name."""
    period_lower = period.lower()
    if period_lower == '1d':
        return '1d'
    elif period_lower == '4h': # Example if you add 4h later
        return '4h'
    elif period_lower == '1h': # Example if you add 1h later
        return '1h'
    # Add other mappings as needed (e.g., 'min5' -> '5m')
    else:
        print(f"[ERROR] prepare_csv_cpt: Unsupported crypto period '{period}' for directory mapping.", file=sys.stderr)
        return None

def get_crypto_source_path(wt_code: str, period: str, data_root: str) -> Optional[str]:
    """
    Constructs the full path to the source cryptocurrency CSV file.
    Assumes source data root structure like: data_root/crypto/1d/BTCUSDT.csv

    Args:
        wt_code: WonderTrader code (e.g., CRYPTO.CPT.BTC-USDT)
        period: Period string ('day', '4h', '1h')
        data_root: The root directory containing the 'crypto' subdirectory.

    Returns:
        The full path to the source CSV file, or None if invalid.
    """
    parts = wt_code.split('.')
    if len(parts) != 3 or parts[0].upper() != 'CRYPTO' or parts[1].upper() != 'CPT':
        # print(f"[DEBUG] prepare_csv_cpt: Skipping non-crypto code format: {wt_code}") # Can be noisy
        return None # Not the expected crypto format

    symbol = parts[2] # e.g., BTC-USDT
    # Convert symbol to filename format (remove '-')
    symbol_filename = symbol.replace('-', '') # BTCUSDT

    period_subdir = get_crypto_period_subdir(period)
    if not period_subdir:
        return None # Error already printed

    # Construct the path: data_root / crypto / [period_subdir] / [symbol_filename].csv
    source_path = os.path.join(data_root, 'crypto', period_subdir, f"{symbol_filename}.csv")
    # print(f"[DEBUG] prepare_csv_cpt: Constructed source path for {wt_code} ({period}): {source_path}")
    return source_path

def get_wt_crypto_csv_filename(wt_code: str, period: str) -> Optional[str]:
    """
    Constructs the target WonderTrader CSV filename for crypto.
    e.g., CRYPTO.CPT.BTC-USDT, day -> CRYPTO.CPT.BTC-USDT_d.csv

    Args:
        wt_code: WonderTrader code (e.g., CRYPTO.CPT.BTC-USDT)
        period: Period string ('day', '4h', '1h')

    Returns:
        The target WT CSV filename, or None if invalid period.
    """
    period_lower = period.lower()
    wt_suffix = ""
    if period_lower == '1d':
        wt_suffix = '_d'
    elif period_lower == '4h':
        wt_suffix = '_4h' # Define your WT suffixes
    elif period_lower == '1h':
        wt_suffix = '_1h' # Define your WT suffixes
    # Add other mappings as needed
    else:
        print(f"[ERROR] prepare_csv_cpt: Unsupported crypto period '{period}' for WT filename suffix.", file=sys.stderr)
        return None

    # Filename uses the original wt_code directly + suffix
    wt_filename = f"{wt_code}{wt_suffix}.csv"
    return wt_filename

# --- Main CSV Preparation Function ---
def prepare_csv_files_cpt(codes: List[str], period: str, output_dir: str) -> bool:
    """
    Prepares WonderTrader compliant CSV files for CRYPTO codes from source CSVs.

    Args:
        codes (List[str]): List of WonderTrader codes (e.g., ['CRYPTO.CPT.BTC-USDT']).
        period (str): Desired period (currently only 'day' is fully implemented).
        output_dir (str): Directory to save the output WT CSV files.

    Returns:
        bool: True if all relevant files were processed without fatal errors, False otherwise.
    """
    if period.lower() == 'day':
        period = '1D'
    elif period.lower() == 'week':
        period = '1W'
    elif period.lower() == 'month':
        period = '1M'

    print(f"[INFO] prepare_csv_cpt: Starting Crypto CSV Preparation for period: {period}, output: {output_dir}")

    # --- Only process 'day' for now as requested ---
    if period.lower() != '1d':
        print(f"[WARN] prepare_csv_cpt: Currently only 'day' period is implemented for crypto. Skipping period '{period}'.")
        return True # Return True as no *error* occurred, just skipped.

    config = load_config()
    if not config:
        print("[ERROR] prepare_csv_cpt: Failed to load configuration. Aborting.", file=sys.stderr)
        return False

    # Use the same data path as defined for TDX (or add a specific crypto path if needed)
    data_root = replace_root_prefix(config.get('tdx_data', {}).get('path', '')) # Reuse TDX path for now
    crypto_data_root = replace_root_prefix(config.get('crypto_data', {}).get('path', ''))

    if not data_root:
        print("[ERROR] prepare_csv_cpt: Data root path ('tdx_data.path') not found in config.json. Aborting.", file=sys.stderr)
        return False
    config_dir = os.path.dirname(os.path.abspath(__file__))

    if not os.path.isabs(data_root):
        data_root = os.path.abspath(os.path.join(config_dir, data_root))

    print(f"[INFO] prepare_csv_cpt: Using Data Root: {data_root}")
    if not os.path.isdir(data_root):
        print(f"[ERROR] prepare_csv_cpt: Data root path is not a valid directory: {data_root}. Aborting.", file=sys.stderr)
        return False
    # Check if crypto subdir exists
    crypto_base_dir = os.path.join(data_root, 'crypto')
    if not os.path.isdir(crypto_base_dir):
         print(f"[ERROR] prepare_csv_cpt: Crypto base directory not found: {crypto_base_dir}. Aborting.", file=sys.stderr)
         return False


    timezone_str = config.get('default_timezone', DEFAULT_TIMEZONE)
    try:
        mytz = pytz.timezone(timezone_str)
    except pytz.UnknownTimeZoneError:
        print(f"[ERROR] prepare_csv_cpt: Unknown timezone '{timezone_str}'. Using default '{DEFAULT_TIMEZONE}'.", file=sys.stderr)
        mytz = pytz.timezone(DEFAULT_TIMEZONE)

    try:
        os.makedirs(output_dir, exist_ok=True)
    except OSError as e:
        print(f"[ERROR] prepare_csv_cpt: Failed to create output directory {output_dir}: {e}. Aborting.", file=sys.stderr)
        return False

    # --- Process each code ---
    files_updated = 0
    files_skipped = 0
    files_failed = 0

    for wt_code in codes:
        # Try to get the source path, this implicitly checks the code format
        source_csv_path = get_crypto_source_path(wt_code, period, data_root)
        if not source_csv_path:
            # Not a crypto code or invalid period, skip silently or log debug if needed
            continue # Skip this code

        # print(f"[INFO] prepare_csv_cpt: Processing Crypto Code: {wt_code} for period: {period}")

        if not os.path.exists(source_csv_path):
            print(f"[WARN] prepare_csv_cpt: Source crypto CSV file not found: {source_csv_path}. Skipping.")
            files_skipped += 1
            continue

        wt_csv_filename = get_wt_crypto_csv_filename(wt_code, period)
        if not wt_csv_filename:
            # Error already printed
            files_failed += 1
            continue
        wt_csv_path = os.path.join(output_dir, wt_csv_filename)
        # print(f"[DEBUG] prepare_csv_cpt: Target WT CSV path: {wt_csv_path}")

        needs_update = False
        try:
            source_mtime = os.path.getmtime(source_csv_path)
            if not os.path.exists(wt_csv_path):
                needs_update = True
            else:
                csv_mtime = os.path.getmtime(wt_csv_path)
                if source_mtime > csv_mtime + 1: # Add tolerance if needed
                    needs_update = True
                else:
                    files_skipped += 1
        except Exception as check_e:
            print(f"[ERROR] prepare_csv_cpt: Error comparing file times for {wt_code}: {check_e}. Assuming update needed.", file=sys.stderr)
            needs_update = True

        if needs_update:
            # print(f"  Converting {source_csv_path} to {wt_csv_path}...")
            try:
                # Read the source CSV
                df_source = pd.read_csv(source_csv_path)

                if df_source.empty:
                    print(f"[WARN] prepare_csv_cpt: Source CSV is empty: {source_csv_path}. Skipping update.")
                    files_skipped += 1 # Treat as skipped, not failed
                    continue

                # Define required source columns
                required_cols = ['OpenTime', 'Open', 'High', 'Low', 'Close', 'Volume', 'QuoteAssetVolume']
                if not all(col in df_source.columns for col in required_cols):
                     print(f"[ERROR] prepare_csv_cpt: Source CSV missing required columns {required_cols}. Actual: {df_source.columns}. File: {source_csv_path}. Skipping update.", file=sys.stderr)
                     files_failed += 1
                     continue

                # --- Transform data to WT format ---
                df_wt = pd.DataFrame()

                # 1. Convert OpenTime (milliseconds) to seconds timestamp
                #    Handle potential non-numeric data gracefully
                try:
                    # Ensure OpenTime is numeric before conversion
                    df_source['OpenTimeNumeric'] = pd.to_numeric(df_source['OpenTime'], errors='coerce')
                    # Drop rows where OpenTime could not be converted
                    df_source.dropna(subset=['OpenTimeNumeric'], inplace=True)
                    if df_source.empty:
                        print(f"[WARN] prepare_csv_cpt: No valid numeric OpenTime found after coercion in {source_csv_path}. Skipping.")
                        files_skipped += 1
                        continue
                    timestamps_sec = df_source['OpenTimeNumeric'] / 1000
                except KeyError:
                    print(f"[ERROR] prepare_csv_cpt: 'OpenTime' column not found after attempting conversion in {source_csv_path}. Skipping update.", file=sys.stderr)
                    files_failed += 1
                    continue
                except Exception as conv_e:
                     print(f"[ERROR] prepare_csv_cpt: Error converting 'OpenTime' to numeric/seconds in {source_csv_path}: {conv_e}. Skipping update.", file=sys.stderr)
                     files_failed += 1
                     continue


                # 2. Convert timestamp to datetime objects (localized to target timezone)
                #    Assume source timestamps (like Binance) are typically UTC based
                try:
                    datetime_col = pd.to_datetime(timestamps_sec, unit='s', utc=True).dt.tz_convert(mytz)
                except Exception as dt_conv_e:
                    print(f"[ERROR] prepare_csv_cpt: Error converting timestamp to datetime object for {source_csv_path}: {dt_conv_e}. Skipping update.", file=sys.stderr)
                    files_failed += 1
                    continue

                # 3. Create 'date' column in YYYY/MM/DD format
                df_wt['date'] = datetime_col.dt.strftime('%Y/%m/%d')

                # 4. Map source columns to WT columns
                df_wt['open'] = pd.to_numeric(df_source['Open'], errors='coerce')
                df_wt['high'] = pd.to_numeric(df_source['High'], errors='coerce')
                df_wt['low'] = pd.to_numeric(df_source['Low'], errors='coerce')
                df_wt['close'] = pd.to_numeric(df_source['Close'], errors='coerce')
                df_wt['volume'] = pd.to_numeric(df_source['Volume'], errors='coerce')
                df_wt['turnover'] = pd.to_numeric(df_source['QuoteAssetVolume'], errors='coerce') # Map QuoteAssetVolume to turnover

                # 5. Drop rows with NaN values introduced by coercion
                df_wt.dropna(inplace=True)
                if df_wt.empty:
                    print(f"[WARN] prepare_csv_cpt: DataFrame became empty after processing/coercion for {source_csv_path}. Skipping update.")
                    files_skipped += 1
                    continue


                # 6. Define output columns (only 'day' implemented here)
                if period.lower() == '1d':
                    output_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 'turnover']
                # Add 'else' for other periods later, potentially adding a 'time' column
                # else:
                #     df_wt['time'] = datetime_col.dt.strftime('%H:%M:%S')
                #     output_columns = ['date', 'time', 'open', 'high', 'low', 'close', 'volume', 'turnover']

                # 7. Select final output columns
                df_output = df_wt[output_columns]

                # 8. Write to target WT CSV
                df_output.to_csv(wt_csv_path, index=False, encoding='utf-8', float_format='%.8f') # Use sufficient float precision
                # print(f"  Successfully updated WT CSV: {wt_csv_path}")
                files_updated += 1

            except pd.errors.EmptyDataError:
                 print(f"[WARN] prepare_csv_cpt: Source CSV is empty or invalid: {source_csv_path}. Skipping update.")
                 files_skipped += 1
            except FileNotFoundError:
                 print(f"[ERROR] prepare_csv_cpt: Source file disappeared before reading? {source_csv_path}. Skipping.", file=sys.stderr)
                 files_failed += 1
            except Exception as process_e:
                print(f"[ERROR] prepare_csv_cpt: Failed to process or write CSV for {wt_code}: {process_e}", file=sys.stderr)
                import traceback
                # print(traceback.format_exc()) # Uncomment for detailed debug
                files_failed += 1

    print(f"[INFO] prepare_csv_cpt: Crypto CSV Preparation Finished for period: {period}. Updated: {files_updated}, Skipped: {files_skipped}, Failed: {files_failed}")
    if files_skipped > 0 and files_updated == 0 and files_failed == 0 and any(c.startswith("CRYPTO.CPT.") for c in codes):
        print(f"[INFO] prepare_csv_cpt: All relevant crypto files were already up-to-date.")
    if files_updated == 0 and files_failed == 0 and files_skipped == 0 and any(c.startswith("CRYPTO.CPT.") for c in codes):
        print(f"[INFO] prepare_csv_cpt: No crypto files found or processed. Check source paths and code formats.")

    return files_failed == 0

# Example Usage (if run directly)
if __name__ == "__main__":
    print("Running prepare_csv_cpt.py example...")
    # Assume config.json defines tdx_data.path correctly
    # and data exists at tdx_data.path/crypto/1d/BTCUSDT.csv etc.
    test_codes = ['CRYPTO.CPT.BTC-USDT', 'CRYPTO.CPT.ETH-USDT', 'SSE.600000'] # Include non-crypto to test skipping
    test_period = '1d'
    test_output_dir = './temp_wt_crypto_output/csv' # Example output dir

    print(f"Codes: {test_codes}")
    print(f"Period: {test_period}")
    print(f"Output Dir: {test_output_dir}")

    success = prepare_csv_files_cpt(test_codes, test_period, test_output_dir)

    if success:
        print("Crypto CSV preparation completed successfully (for relevant files).")
        # Check the contents of test_output_dir
        if os.path.exists(test_output_dir):
             print("Generated files:")
             for filename in os.listdir(test_output_dir):
                 if filename.startswith("CRYPTO.CPT."):
                      print(f"- {filename}")
    else:
        print("Crypto CSV preparation encountered errors.")
