declare module 'jsonwebtoken' {
  export interface JwtPayload {
    id: string;
    username: string;
    role: string;
    iat: number;
    exp: number;
  }

  export function sign(payload: object, secret: string, options?: object): string;
  export function verify(token: string, secret: string): JwtPayload;
}

declare module 'sequelize' {
  export interface Model {
    associate?: (models: any) => void;
  }
}
