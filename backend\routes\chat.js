const express = require('express');
const router = express.Router();
const chatHandler = require('../_Handlers/chatHandler');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置 multer
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 限制 5MB
    }
});

// 调试中间件
/*router.use((req, res, next) => {
  console.log('[ChatRouter]', req.method, req.url, 'user:', req.user?.id);
  next();
});*/

// 获取默认会话
router.get('/session/default', authenticateToken, async (req, res) => {
  console.log('[ChatRouter] Getting default session for user:', req.user.id);

  try {
    console.log('[ChatAPI] Getting default session for user:', req.user.id);
    const session = await chatHandler.getDefaultSession(req.user.id);
    res.json({ sessionId: session.id });
  } catch (error) {
    console.error('[ChatAPI] Failed to get default session:', error);
    res.status(500).json({ error: 'Failed to get default session' });
  }
});

// 发送消息
router.post('/messages', authenticateToken, async (req, res) => {
  console.log('[ChatRouter] 发送消息:', req.body);
  
  try {
    const message = {
      ...req.body,
      senderId: req.user.id
    };
    const latestMessages = await chatHandler.handleMessage(message);
    res.json(latestMessages);
  } catch (error) {
    console.error('[ChatAPI] Failed to send message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
});

// 获取会话消息历史
router.get('/messages/:sessionId', authenticateToken, async (req, res) => {
  try {
    const messages = await chatHandler.getMessages(req.params.sessionId);
    res.json(messages);
  } catch (error) {
    console.error('[ChatAPI] Failed to get messages:', error);
    res.status(500).json({ error: 'Failed to get messages' });
  }
});

// 上传文件
router.post('/upload', upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ 
                success: false, 
                error: 'No file uploaded' 
            });
        }

        // 返回文件URL和大小
        const fileUrl = `/uploads/${req.file.filename}`;
        res.json({ 
            success: true,
            url: fileUrl,
            fileName: req.file.originalname,
            fileSize: req.file.size
        });

    } catch (error) {
        console.error('[File Upload] Error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'File upload failed' 
        });
    }
});

// 撤回消息
router.post('/messages/:messageId/recall', authenticateToken, async (req, res) => {
  try {
    await chatHandler.recallMessage(req.params.messageId, req.user.id);
    res.json({ success: true });
  } catch (error) {
    console.error('[ChatAPI] Failed to recall message:', error);
    res.status(500).json({ error: 'Failed to recall message' });
  }
});

// 标记消息已读
router.post('/messages/read', authenticateToken, async (req, res) => {
  try {
    await chatHandler.markAsRead(req.body.sessionId, req.user.id);
    res.json({ success: true });
  } catch (error) {
    console.error('[ChatAPI] Failed to mark messages as read:', error);
    res.status(500).json({ error: 'Failed to mark messages as read' });
  }
});

// 搜索消息
router.get('/messages/search', authenticateToken, async (req, res) => {
  try {
    const messages = await chatHandler.searchMessages(req.query.text, req.user.id);
    res.json(messages);
  } catch (error) {
    console.error('[ChatAPI] Failed to search messages:', error);
    res.status(500).json({ error: 'Failed to search messages' });
  }
});


module.exports = router; 