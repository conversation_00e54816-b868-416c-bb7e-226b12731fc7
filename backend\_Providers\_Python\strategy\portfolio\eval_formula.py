# eval_formula.py
# Contains logic for evaluating formula strings using asteval

import re
import pandas as pd
import numpy as np
from asteval import Interpreter # Need asteval here
from typing import Dict, List, Optional, Callable

def evaluate_formula_string(formula: str,
                            eval_namespace: Dict[str, pd.Series],
                            aeval_interpreter: Interpreter,
                            target_codes: List[str],
                            logger: Callable[[str], None],
                            raw_data_columns: Optional[Dict[str, pd.Series]] = None) -> Optional[pd.Series]:
    """
    Evaluates a single formula string using the provided namespace and interpreter.
    Handles formula transformation (factor call -> internal key).

    Args:
        formula: The original formula string to evaluate.
        eval_namespace: Dict containing pre-calculated base factor Series.
                        Keys are internal factor identifiers (e.g., 'roc_close_period10').
                        Values are Pandas Series indexed by asset codes.
        aeval_interpreter: An instance of asteval.Interpreter.
        target_codes: A list of asset codes for indexing the result Series.
        logger: A logging function (e.g., print_log).
        raw_data_columns: Optional dictionary containing raw price/volume data.
                          Keys should be column names ('open', 'high', 'low', 'close', 'volume').
                          Values are Pandas Series indexed by asset codes.
                          Used for direct column references in formulas.

    Returns:
        A Pandas Series with the evaluation result for each code,
        indexed by target_codes. Returns a Series of NaNs if evaluation fails.
    """
    transformed_formula = formula # Start with the original
    result_series = pd.Series(np.nan, index=target_codes) # Default to NaN Series

    if not formula:
        logger("[公式评估] 警告: 传入空公式字符串。")
        return result_series

    # Allow evaluation even if namespace is empty if formula is a constant
    # (asteval handles constants like '1', '0', 'True')

    try:
        # --- Transform the formula string ---
        # logger(f"[公式评估] 开始转换公式: {formula}") # Optional detailed log
        matches_for_replace = []
        pattern = re.compile(r"([a-zA-Z_][a-zA-Z0-9_]*)\s*\(([^)]*)\)")
        for match in pattern.finditer(formula):
            original_call_string = match.group(0)
            factor_func_name = match.group(1)
            args_str = match.group(2)
            args = [arg.strip() for arg in args_str.split(',') if arg.strip()]
            if not args: continue

            column = args[0]
            params = {}
            if len(args) > 1:
                try:
                    params['period'] = int(args[1])
                    # Extend for more potential named parameters here if needed
                except (ValueError, IndexError): continue # Skip if params not parsable

            params_tuple = tuple(sorted(params.items()))
            # Reconstruct the key exactly as done during factor calculation
            factor_key_in_formula = f"{factor_func_name}_{column}_{'_'.join(f'{k}{v}' for k, v in params_tuple)}"

            # Crucially check if the required base factor exists in the provided namespace
            if factor_key_in_formula in eval_namespace:
                matches_for_replace.append({
                    'start': match.start(),
                    'end': match.end(),
                    'replacement': factor_key_in_formula
                })
            else:
                # If a required base factor is missing, the formula cannot be evaluated correctly
                logger(f"[公式评估] 错误: 公式 '{formula}' 依赖的因子 '{factor_key_in_formula}' 未在计算结果中找到。无法评估此公式。")
                return result_series # Return NaNs

        # Apply replacements from right to left (or reverse sorted by start index)
        matches_for_replace.sort(key=lambda x: x['start'], reverse=True)
        temp_formula_list = list(transformed_formula)
        for item in matches_for_replace:
            start, end, replacement_key = item['start'], item['end'], item['replacement']
            temp_formula_list[start:end] = list(replacement_key)
        transformed_formula = "".join(temp_formula_list)
        # logger(f"[公式评估] 转换后公式: {transformed_formula}") # Optional detailed log

        # --- Add raw data columns to namespace if provided ---
        # Create a copy of the namespace to avoid modifying the original
        formula_namespace = dict(eval_namespace)

        # Add raw data columns to the namespace if provided
        if raw_data_columns is not None:
            for col_name, series in raw_data_columns.items():
                if col_name in ['open', 'high', 'low', 'close', 'volume']:
                    # Only add if not already in namespace (don't override existing factors)
                    if col_name not in formula_namespace:
                        formula_namespace[col_name] = series
                        # logger(f"[公式评估] 添加原始数据列 '{col_name}' 到评估命名空间。")  # 屏蔽重复日志

        # --- Evaluate the transformed formula ---
        # Assign the calculated factors and raw data columns to the interpreter's symbol table
        aeval_interpreter.symtable = formula_namespace
        # Evaluate the transformed formula string
        result = aeval_interpreter(transformed_formula)

        # --- Process asteval result ---
        if isinstance(result, pd.Series):
            # Ensure the Series index matches the target codes and handle type
            temp_result_series = result.reindex(target_codes).astype(object) # Start as object
            # Attempt conversion - prioritize boolean then numeric
            try:
                # Try boolean first if it looks like a boolean formula result
                # Check for explicit bool type or if values are only 0/1/NaN
                is_bool_type = temp_result_series.map(lambda x: isinstance(x, (bool, np.bool_))).any()
                # Check if remaining non-NaN values are only 0 or 1
                non_na_values = temp_result_series.dropna()
                # Ensure comparison works even if non_na_values is empty
                is_zero_one = False
                if not non_na_values.empty:
                    is_zero_one = non_na_values.isin([0, 1]).all()

                is_potentially_bool = is_bool_type or is_zero_one

                if is_potentially_bool:
                     bool_mask = temp_result_series.notna()
                     # Convert non-NaN to bool, keep NaN
                     temp_result_series.loc[bool_mask] = temp_result_series.loc[bool_mask].astype(bool)
                     result_series = temp_result_series # Assign boolean result
                else: # Try numeric
                    numeric_series = pd.to_numeric(temp_result_series, errors='coerce')
                    # Use numeric if conversion didn't make everything NaN (unless original was all NaN)
                    if not numeric_series.isna().all() or temp_result_series.isna().all():
                        result_series = numeric_series # Assign numeric result
                    else: # If conversion failed badly, keep as object (or revert to NaN?)
                        logger(f"[公式评估] 警告: 尝试数值转换失败 for '{formula}', 结果保留为 object 类型或包含无法转换的值。")
                        result_series = temp_result_series # Keep as object

            except Exception as conv_err:
                 logger(f"[公式评估] 警告: 结果序列类型转换失败 for '{formula}': {conv_err}")
                 result_series = temp_result_series # Keep as object type if conversion fails

        elif isinstance(result, (int, float, bool, np.number, np.bool_)):
             # If result is a single scalar, broadcast it to a Series
             # Linter Fix: Create as object first, then convert type
             temp_series = pd.Series(result, index=target_codes, dtype=object)
             try:
                 if isinstance(result, (bool, np.bool_)):
                     result_series = temp_series.astype('boolean') # Use nullable boolean
                 elif isinstance(result, (int, np.integer)):
                     result_series = temp_series.astype('Int64') # Use nullable integer
                 elif isinstance(result, (float, np.floating)):
                      result_series = temp_series.astype('Float64') # Use nullable float
                 else: # Fallback for other np.number types
                      result_series = temp_series.astype(float) # Try float
             except Exception as scalar_conv_err:
                  logger(f"[公式评估] 警告: 标量结果 {result} 广播后类型转换失败: {scalar_conv_err}")
                  result_series = temp_series # Keep as object if conversion fails

        elif result is None:
             logger(f"[公式评估] 警告: asteval 评估公式 '{formula}' (转换后: '{transformed_formula}') 返回 None。")
             # result_series remains the initial NaN Series
        else:
             # Handle unexpected types from asteval
             logger(f"[公式评估] 错误: 公式 '{formula}' 评估返回未知类型: {type(result)}。")
             # result_series remains the initial NaN Series

    except Exception as e:
        logger(f"[公式评估] 严重错误: 评估公式 '{formula}' (转换后: '{transformed_formula}') 时发生异常: {e}")
        # import traceback; logger(traceback.format_exc()) # Uncomment for debugging
        # result_series remains the initial NaN Series

    # Final check, ensure returned object is a Series with the correct index
    if not isinstance(result_series, pd.Series):
         logger(f"[公式评估] 内部错误: 评估结果处理后不是 Series (类型: {type(result_series)})，强制返回 NaNs。")
         result_series = pd.Series(np.nan, index=target_codes)
    elif not result_series.index.equals(pd.Index(target_codes)):
         logger(f"[公式评估] 内部错误: 评估结果 Series 索引与目标代码不匹配，强制 reindex。")
         result_series = result_series.reindex(target_codes, fill_value=np.nan)


    return result_series