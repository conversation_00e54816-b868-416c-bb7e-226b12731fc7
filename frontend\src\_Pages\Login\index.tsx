import React, { useEffect, useState, useRef } from 'react';
import { message, Space, Modal, Form, Input, Button } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  LoginForm,
  ProFormText,
  ProFormInstance,
  ProFormCaptcha,
} from '@ant-design/pro-components';
import {
  UserOutlined,
  LockOutlined,
  GithubOutlined,
  MailOutlined,
} from '@ant-design/icons';
import { EventBus } from '@/events/eventBus';
import { UserEvents } from '@/events/events';
import { ChatEvents } from '@/events/events';
import { FrontendConfig } from '@/shared_types/enviorment';
import { useTheme } from '@/models/useTheme';
import { useAtom } from 'jotai';
import { globalLoadingAtom } from '@/components/GlobalLoading';

const Login: React.FC = () => {
  const formRef = React.useRef<ProFormInstance>();
  const navigate = useNavigate();
  const location = useLocation();
  const { theme, isDarkMode } = useTheme();
  const [, setGlobalLoading] = useAtom(globalLoadingAtom);
  const [resetModalVisible, setResetModalVisible] = useState(false);
  const [resetForm] = Form.useForm();
  const [verificationCode, setVerificationCode] = useState<string>('');

  // 获取登录后要跳转的路径，如果有的话
  const from = location.state?.from?.pathname || '/dashboard';

  const [config, setConfig] = useState<FrontendConfig | null>(null);

  useEffect(() => {
    // 从public目录获取配置文件
    fetch('/config.json')
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        setConfig(data); // 设置配置数据
      })
      .catch(error => {
        console.error('Error fetching config:', error);
      });
  }, []);

  useEffect(() => {
    const handleLoginResult = (result: UserEvents.ResultPayload) => {
      setGlobalLoading(false);
      if (result.success) {
        message.success('登录成功！');
        console.log('[Login] 登录成功，准备跳转到:', from);
        window.location.href = from;
      } else {
        message.error(result.error || '登录失败，请重试');
      }
    };

    const handleResetPasswordResult = (result: UserEvents.ResultPayload) => {
      setGlobalLoading(false);
      if (result.success) {
        message.success('密码重置成功！');
        setResetModalVisible(false);
        resetForm.resetFields();
      } else {
        message.error(result.error || '密码重置失败，请重试');
      }
    };

    const handleEmailCodeResult = (result: UserEvents.SMSCodeResultPayload) => {
      if (result.success) {
        message.success('验证码已发送');
      } else {
        message.error(result.error || '发送验证码失败');
      }
    };

    EventBus.off(UserEvents.Types.LOGIN_RESULT, handleLoginResult);
    EventBus.off(UserEvents.Types.RESET_PASSWORD_RESULT, handleResetPasswordResult);
    EventBus.off(UserEvents.Types.SMS_CODE_RESULT, handleEmailCodeResult);

    const loginSubscription = EventBus.on(UserEvents.Types.LOGIN_RESULT, handleLoginResult);
    const resetSubscription = EventBus.on(UserEvents.Types.RESET_PASSWORD_RESULT, handleResetPasswordResult);
    const codeSubscription = EventBus.on(UserEvents.Types.SMS_CODE_RESULT, handleEmailCodeResult);

    return () => {
      loginSubscription.unsubscribe();
      resetSubscription.unsubscribe();
      codeSubscription.unsubscribe();
    };
  }, [navigate, setGlobalLoading, resetForm, from]);

  const handleSubmit = (values: any) => {
    setGlobalLoading(true);
    EventBus.emit(UserEvents.Types.LOGIN, values);
  };

  const handleRegister = () => {
    setGlobalLoading(true);
    navigate('/register');
  };

  const handleForgotPassword = () => {
    setResetModalVisible(true);
  };

  const generateVerificationCode = () => {
    return Math.floor(1000 + Math.random() * 9000).toString();
  };

  const handleSendCode = async () => {
    try {
      await resetForm.validateFields(['email']);
      const email = resetForm.getFieldValue('email');
      const code = generateVerificationCode();
      setVerificationCode(code);
      EventBus.emit(UserEvents.Types.SEND_EMAIL_CODE, { email, code });
      message.success('验证码已发送');
    } catch (error) {
      message.error('请输入正确的邮箱');
    }
  };

  const handleResetPassword = async () => {
    try {
      const values = await resetForm.validateFields();
      if (values.code !== verificationCode) {
        message.error('验证码错误');
        return;
      }

      if (values.newPassword !== values.confirmPassword) {
        message.error('两次输入的密码不一致');
        return;
      }

      setGlobalLoading(true);
      EventBus.emit(UserEvents.Types.RESET_PASSWORD, {
        email: values.email,
        code: values.code,
        newPassword: values.newPassword
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <div style={{
      height: '100vh',
      background: isDarkMode ? '#141414' : '#f0f2f5',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <LoginForm
        formRef={formRef}
        logo={<GithubOutlined style={{ fontSize: 48, color: isDarkMode ? '#fff' : '#000' }} />}
        title={<div style={{ color: isDarkMode ? '#fff' : '#000' }}>{config?.title || "QuantQuart"}</div>}
        subTitle={<div style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.45)' }}>量化交易策略平台</div>}
        style={{
          backgroundColor: isDarkMode ? '#1f1f1f' : '#fff',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: isDarkMode
            ? '0 2px 8px rgba(0, 0, 0, 0.45)'
            : '0 2px 8px rgba(0, 0, 0, 0.15)'
        }}
        submitter={{
          searchConfig: {
            submitText: '登录',
          },
        }}
        onFinish={handleSubmit}
        actions={
          <Space direction="vertical" align="center" style={{ width: '100%', color: isDarkMode ? '#fff' : '#000' }}>
            <div>
              <a onClick={handleForgotPassword} style={{ color: isDarkMode ? '#1890ff' : '#1677ff' }}>
                忘记密码
              </a>
            </div>
            <div>
              还没有账号？
              <a onClick={handleRegister} style={{ fontWeight: 'bold', color: isDarkMode ? '#1890ff' : '#1677ff', marginLeft: '4px' }}>
                立即注册
              </a>
            </div>
          </Space>
        }
      >
        <ProFormText
          name="username"
          fieldProps={{
            size: 'large',
            prefix: <UserOutlined />,
          }}
          placeholder="用户名/邮箱"
          label="用户名或邮箱"
          rules={[
            {
              required: true,
              message: '请输入用户名或邮箱',
            },
          ]}
        />
        <ProFormText.Password
          name="password"
          fieldProps={{
            size: 'large',
            prefix: <LockOutlined />,
          }}
          placeholder="密码"
          rules={[
            {
              required: true,
              message: '请输入密码',
            },
          ]}
        />
      </LoginForm>

      <Modal
        title="重置密码"
        open={resetModalVisible}
        onCancel={() => setResetModalVisible(false)}
        footer={null}
      >
        <Form
          form={resetForm}
          layout="vertical"
          onFinish={handleResetPassword}
        >
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入正确的邮箱格式' }
            ]}
          >
            <Input prefix={<MailOutlined />} placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            name="code"
            label="验证码"
            rules={[{ required: true, message: '请输入验证码' }]}
          >
            <div style={{ display: 'flex' }}>
              <Input
                style={{ flex: 1, marginRight: 8 }}
                placeholder="请输入验证码"
              />
              <Button onClick={handleSendCode}>
                获取验证码
              </Button>
            </div>
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' }
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请确认新密码" />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              重置密码
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Login;