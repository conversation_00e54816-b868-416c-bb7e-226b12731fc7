/**
 * 多品种重叠指标
 * 用于在副图中显示多个品种的价格变化百分比对比
 * 参数为逗号分隔的品种代码串，每个代码必须符合 xxx.xxx.xxx 格式
 */

import { registerIndicator } from 'klinecharts';
import { KLineInterval } from '@/shared_types/market';
import marketDispatcher from '@/_Dispatchers/marketDispatcher';
import { EventBus } from '@/events/eventBus';

// 预定义颜色数组 - 与styles.lines中的颜色保持一致
const COLORS = [
    '#1E88E5', // 蓝色
    '#FF5252', // 红色
    '#4CAF50', // 绿色
    '#FB8C00', // 橙色
    '#9C27B0'  // 紫色
];

// 数据缓存，避免重复请求
const dataCache = new Map();

// 品种名称缓存
const nameCache = new Map();

// 获取品种名称的函数（同步版本，使用缓存）
function getSymbolName(fullCode) {
    try {
        // 检查缓存
        if (nameCache.has(fullCode)) {
            return nameCache.get(fullCode);
        }

        // 解析品种代码
        const parts = fullCode.split('.');
        if (parts.length !== 3) {
            console.warn(`[MultiSymbolLines] 无效的品种代码格式: ${fullCode}`);
            const codePart = parts[parts.length - 1] || fullCode;
            nameCache.set(fullCode, codePart);
            return codePart;
        }

        // 如果没有缓存，先返回代码部分，然后异步获取名称并更新缓存
        const code = parts[2];
        nameCache.set(fullCode, code);

        // 异步获取名称并更新缓存（不等待结果）
        fetchSymbolName(fullCode).catch(e => console.error(`[MultiSymbolLines] 异步获取品种名称失败: ${e}`));

        return code;
    } catch (error) {
        console.error(`[MultiSymbolLines] 获取品种名称失败: ${error}`);
        // 解析代码部分作为备用
        const parts = fullCode.split('.');
        const codePart = parts[parts.length - 1] || fullCode;
        nameCache.set(fullCode, codePart);
        return codePart;
    }
}

// 异步获取品种名称并更新缓存
async function fetchSymbolName(fullCode) {
    try {
        // 解析品种代码
        const parts = fullCode.split('.');
        if (parts.length !== 3) return;

        const exchange = parts[0];
        const market = parts[1];
        const code = parts[2];

        // 使用代码部分搜索品种
        console.log(`[MultiSymbolLines] 异步搜索品种: ${code}`);
        const searchResults = await marketDispatcher.searchSymbols(code);

        if (searchResults && searchResults.length > 0) {
            // 找到匹配的品种（交易所和市场类型都匹配）
            const matchedSymbol = searchResults.find(
                s => s.exchange === exchange && s.market === market && s.code === code
            );

            if (matchedSymbol) {
                console.log(`[MultiSymbolLines] 找到匹配的品种: ${matchedSymbol.name}`);
                nameCache.set(fullCode, matchedSymbol.name);
                // 触发图表更新
                EventBus.emit('chart:force_update', {});
                return matchedSymbol.name;
            }
        }
    } catch (error) {
        console.error(`[MultiSymbolLines] 异步获取品种名称失败: ${error}`);
    }
}

// 解析并验证品种代码格式
function parseAndValidateSymbols(symbolsStr) {
    console.log(`[MultiSymbolLines] 解析品种代码字符串: "${symbolsStr}"`);

    if (!symbolsStr || symbolsStr.trim() === '') {
        console.warn(`[MultiSymbolLines] 品种代码字符串为空`);
        return [];
    }

    const symbols = symbolsStr.split(',')
        .map(s => s.trim())
        .filter(s => s); // 过滤空字符串

    console.log(`[MultiSymbolLines] 分割后的品种代码数组:`, symbols);

    const validSymbols = symbols.map(s => {
        // 处理特殊格式：指数代码如 "SSE.INDEX.000001.SH"，需要去掉末尾的 .SH 或 .SZ 部分
        if (s.includes('.INDEX.') && (s.endsWith('.SH') || s.endsWith('.SZ') || s.endsWith('.CSI'))) {
            // 提取前三部分，即 SSE.INDEX.000001
            const parts = s.split('.');
            if (parts.length >= 3) {
                const normalizedCode = parts.slice(0, 3).join('.');
                console.log(`[MultiSymbolLines] 指数代码 "${s}" 规范化为 "${normalizedCode}"`);
                return normalizedCode;
            }
        }
        
        // 验证普通格式是否为 xxx.xxx.xxx，支持连字符、下划线等特殊字符
        const isValidFormat = /^[A-Za-z0-9]+\.[A-Za-z0-9]+\.[A-Za-z0-9\-_]+$/.test(s);
        if (!isValidFormat) {
            console.warn(`[MultiSymbolLines] 品种代码 "${s}" 不符合 xxx.xxx.xxx 格式，将被忽略`);
            return null;
        }
        
        return s;
    }).filter(s => s !== null); // 过滤掉无效的代码

    console.log(`[MultiSymbolLines] 验证后的有效品种代码:`, validSymbols);
    return validSymbols;
}

// 获取品种数据
async function fetchSymbolData(symbol) {
    // 检查缓存
    const cacheKey = symbol;
    if (dataCache.has(cacheKey)) {
        console.log(`[MultiSymbolLines] 使用缓存数据: ${symbol}`);
        return dataCache.get(cacheKey);
    }

    console.log(`[MultiSymbolLines] 请求品种数据: ${symbol}`);

    try {
        // 使用marketDispatcher.getKLines函数获取数据
        const response = await marketDispatcher.getKLines(symbol, KLineInterval.DAY1);

        if (response.success && response.data) {
            // 转换数据格式
            const formattedData = response.data.map(item => ({
                timestamp: item.time * 1000, // 转换为毫秒
                open: item.open,
                high: item.high,
                low: item.low,
                close: item.close,
                volume: item.volume || 0
            }));

            console.log(`[MultiSymbolLines] 获取到品种数据: ${symbol}, 数据点数: ${formattedData.length}`);

            // 缓存数据
            if (formattedData.length > 0) {
                dataCache.set(cacheKey, formattedData);
            }

            return formattedData;
        } else {
            throw new Error(`获取品种 ${symbol} 数据失败: ${response.error || '未知错误'}`);
        }
    } catch (error) {
        console.error(`[MultiSymbolLines] 获取品种 ${symbol} 数据失败:`, error);
        return [];
    }
}

// 时间匹配函数
function findMatchingData(timestamp, symbolData) {
    // 找到最接近的时间点数据
    if (!symbolData || symbolData.length === 0) return null;

    // 精确匹配
    const exactMatch = symbolData.find(data => data.timestamp === timestamp);
    if (exactMatch) return exactMatch;

    // 找最接近的时间点（使用二分查找提高效率）
    let left = 0;
    let right = symbolData.length - 1;
    let closest = null;
    let minDiff = Infinity;

    while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        const diff = Math.abs(symbolData[mid].timestamp - timestamp);

        if (diff < minDiff) {
            minDiff = diff;
            closest = symbolData[mid];
        }

        if (symbolData[mid].timestamp < timestamp) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    // 如果时间差太大（例如超过一个周期），可以返回null
    const maxAllowedDiff = 24 * 60 * 60 * 1000; // 例如1天
    return minDiff <= maxAllowedDiff ? closest : null;
}

// 清除缓存
export function clearMultiSymbolLinesCache() {
    console.log('[MultiSymbolLines] 清除数据缓存');
    dataCache.clear();
}

// 注册多品种重叠指标
export function registerMultiSymbolLines() {
    registerIndicator({
        name: 'MultiSymbolLines',
        shortName: '多品种重叠',
        series: 'price',
        calcParams: [''], // 默认参数为空，完全依赖传入的参数
        precision: 2,
        // 全局样式设置
        styles: {
            lines: [
                {
                    style: 'solid',
                    smooth: false,
                    size: 1,
                    dashedValue: [2, 2],
                    color: '#1E88E5' // 蓝色
                },
                {
                    style: 'solid',
                    smooth: false,
                    size: 1,
                    dashedValue: [2, 2],
                    color: '#FF5252' // 红色
                },
                {
                    style: 'solid',
                    smooth: false,
                    size: 1,
                    dashedValue: [2, 2],
                    color: '#4CAF50' // 绿色
                },
                {
                    style: 'solid',
                    smooth: false,
                    size: 1,
                    dashedValue: [2, 2],
                    color: '#FB8C00' // 橙色
                },
                {
                    style: 'solid',
                    smooth: false,
                    size: 1,
                    dashedValue: [2, 2],
                    color: '#9C27B0' // 紫色
                }
            ]
        },
        // 默认figures配置，会被regenerateFigures覆盖
        figures: [],

        // 动态生成figures配置
        regenerateFigures: (calcParams) => {
            // 解析品种代码并验证格式
            const symbols = parseAndValidateSymbols(calcParams[0] || '');
            console.log(`[MultiSymbolLines] regenerateFigures calcParams: `, JSON.stringify(calcParams));

            // 为每个品种创建一个figure
            return symbols.map((symbol, index) => {
                // 提取代码部分（最后一部分）
                const parts = symbol.split('.');
                const code = parts.length > 0 ? parts[parts.length - 1] : symbol;

                // 获取品种名称（同步版本）
                const name = getSymbolName(symbol);

                return {
                    key: `symbol_${index}`,
                    title: `${name}(${code}): `,
                    type: 'line'
                };
            });
        },

        // 创建自定义提示信息
        createTooltipDataSource: ({ indicator, crosshair }) => {
            const symbols = parseAndValidateSymbols(indicator.calcParams[0] || '');
            console.log(`[MultiSymbolLines] createTooltipDataSource calcParams:`, JSON.stringify(indicator.calcParams));
            const result = indicator.result;
            const data = result[crosshair.dataIndex];

            if (data) {
                // 创建每个品种的图例
                const legends = symbols.map((symbol, index) => {
                    // 提取代码部分（最后一部分）
                    const parts = symbol.split('.');
                    const code = parts.length > 0 ? parts[parts.length - 1] : symbol;

                    // 获取品种名称（同步版本）
                    const name = getSymbolName(symbol);

                    const value = data[`symbol_${index}`];
                    const colorIndex = index % COLORS.length;
                    const color = COLORS[colorIndex];

                    return {
                        title: `${name}(${code}): `,
                        value: value !== undefined ? {
                            text: `${value.toFixed(2)}%`,
                            color: color // 确保值的颜色与线条一致
                        } : '--'
                    };
                });

                return {
                    name: '多品种重叠',
                    legends
                };
            }
            return {};
        },

        // 计算函数
        calc: async function (dataList, indicator) {
            // 解析品种代码并验证格式
            let symbolsStr = '';

            // 直接从calcParams获取参数
            if (indicator.calcParams && indicator.calcParams.length > 0) {
                symbolsStr = indicator.calcParams[0] || '';
                console.log(`[MultiSymbolLines] 计算函数接收到的参数: "${symbolsStr}"`);
            }

            // 打印完整的indicator对象，查看其结构
            console.log(`[MultiSymbolLines] 完整的indicator对象:`, JSON.stringify(indicator, null, 2));

            const symbols = parseAndValidateSymbols(symbolsStr);
            console.log(`[MultiSymbolLines] C: `, JSON.stringify(symbols));
            console.log(`[MultiSymbolLines] 解析后的品种列表:`, symbols);

            if (symbols.length === 0) {
                console.log(`[MultiSymbolLines] 没有有效的品种代码，返回空数据`);
                return dataList.map(() => ({}));
            }

            try {
                // 获取所有品种的数据
                const symbolsData = await Promise.all(
                    symbols.map(symbol => fetchSymbolData(symbol))
                );

                // 定义基准K线的位置（倒数第100根）
                const baseIndex = 100;

                // 计算基准值（倒数第100根K线的收盘价，或最早的K线）
                const baseValues = symbolsData.map(data => {
                    // 如果数据不足baseIndex根，使用最早的K线
                    if (data.length < baseIndex) {
                        return data.length > 0 ? data[0].close : null;
                    }

                    // 否则使用倒数第baseIndex根K线
                    return data[data.length - baseIndex].close;
                });

                console.log(`[MultiSymbolLines] 基准值:`, baseValues);

                // 计算每个时间点的百分比变化
                return dataList.map((kLineData) => {
                    const result = {};

                    symbols.forEach((_, symbolIndex) => {
                        const symbolData = symbolsData[symbolIndex];
                        const baseValue = baseValues[symbolIndex];

                        // 找到对应时间点的数据
                        const matchedData = findMatchingData(kLineData.timestamp, symbolData);

                        if (matchedData && baseValue && baseValue !== 0) {
                            // 计算百分比变化
                            const percentChange = ((matchedData.close - baseValue) / baseValue) * 100;
                            result[`symbol_${symbolIndex}`] = percentChange;
                        }
                    });

                    return result;
                });
            } catch (error) {
                console.error('[MultiSymbolLines] 计算错误:', error);
                return dataList.map(() => ({}));
            }
        }
    });

    console.log('[MultiSymbolLines] 指标注册成功');
}
