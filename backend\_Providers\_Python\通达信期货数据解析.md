编号：
LCL8 碳酸锂主连
SIL8 工业硅主连
PSL8 多晶硅主连

推论：
L7 表示次连
L8 表示主连
L9 表示指数

LC2503
LC2504
...
LC2512
LC2601 LC2602


文件内容及格式
code2name.ini、code2name_hk.ini 和 code2name_qq.ini：
内容：主要包含期货品种（如中证指数期货、沪深指数期货、上证指数期货等）的代码、名称、交易所简称（如CZ代表中金所）、合约到期规则、保证金比例、最小交割单位、合约价值计算方式、盈利单位等信息。
格式：通常是用逗号或等号分隔的键值对或字段，例如：
AF,澳美,CZ,2,,,100,0.0100,3.00,3.00,1.0000,元,美元,107,3,0,合约到期月份的第三个周三
其中各字段分别表示期货代码、期货名称、交易所简称、交割规则等。
code2qhidx.ini：
内容：包含期货指数的配置信息，如价格指数和计算指数的代码、名称、基准值、小数位、成分股等。
格式：以INI文件格式为主，包含多个 [Section] 和键值对，例如：
复制
[PriceIndex]
47_ICL9=中证加权|5695.4
47_IFL9=沪深加权|3774.0
code2targ.ini：
内容：包含期货品种的映射关系，如期货代码与目标代码、交易所简称等的对应关系。
格式：同样是以逗号或等号分隔的键值对形式，例如：
IF,殺侮300豚歯,CZ,IF300,CZ
常见字段解释
code：期货品种的代码。
name：期货品种的名称。
exchange：期货交易所的简称（如CZ代表中金所，PH代表香港交易所）。
last_trade_date：合约的最后交易日。
multiplier：合约的乘数或最小交割单位。
margin_ratio：保证金比例。
contract_value：合约价值的计算方式。
文件用途
这些文件主要为通达信软件提供期货相关的数据支持，包括：
期货品种的基本信息：如代码、名称、交易所等。
合约规则：如最后交易日、保证金比例、最小交割单位等。
指数计算：如价格指数和计算指数的配置信息。
期货品种的映射关系：便于软件在不同系统或模块间进行数据转换和处理。

文件 1：code2name.ini
该文件主要包含中国大陆期货市场的品种信息。每个条目包含以下字段：
代码 (Code)：期货品种的代码，例如 AF、EF、IC 等。
名称 (Name)：期货品种的中文名称，例如 澳美、欧美、中证 等。
交易所 (Exchange)：期货交易所的简称，例如 CZ（中国金融期货交易所）、QD（大连商品交易所）、QS（上海期货交易所）等。
字段4 (Field 4)：通常为数字，可能表示合约的类型或分类。
字段5 (Field 5)：通常为空或数字，可能表示合约的某些特定属性。
字段6 (Field 6)：通常为空或日期，可能表示合约的某个特定日期。
字段7 (Field 7)：通常为数字，可能表示合约的乘数或单位。
字段8 (Field 8)：通常为数字，可能表示最小变动价位。
字段9 (Field 9)：通常为数字，可能表示价格波动限制。
字段10 (Field 10)：通常为数字，可能表示合约的保证金比例。
字段11 (Field 11)：通常为数字，可能表示手续费率。
字段12 (Field 12)：通常为数字，可能表示合约的最小交易单位。
字段13 (Field 13)：通常为单位，例如 元、美元、万分之 等，表示价格单位。
字段14 (Field 14)：通常为单位，例如 吨、手、张 等，表示交易单位。
字段15 (Field 15)：通常为数字，可能表示合约的代码或分类。
字段16 (Field 16)：通常为数字，可能表示合约的某些特定属性。
字段17 (Field 17)：通常为数字，可能表示合约的某些特定属性。
字段18 (Field 18)：通常为数字，可能表示合约的某些特定属性。
字段19 (Field 19)：通常为文字描述，表示合约的交割规则或最后交易日规则。
文件 2：code2name_hk.ini#
该文件主要包含香港期货市场的品种信息。每个条目包含以下字段：
代码 (Code)：期货品种的代码，例如 A50、AAC、ABC 等。
名称 (Name)：期货品种的中文名称，例如 安硕A50期货、瑞声科技、农业银行 等。
交易所 (Exchange)：期货交易所的简称，例如 PH（香港交易所）。
字段4 (Field 4)：通常为数字，可能表示合约的类型或分类。
字段5 (Field 5)：通常为空或日期，可能表示合约的某个特定日期。
字段6 (Field 6)：通常为数字，可能表示合约的乘数或单位。
字段7 (Field 7)：通常为数字，可能表示最小变动价位。
字段8 (Field 8)：通常为空或数字，可能表示价格波动限制。
字段9 (Field 9)：通常为空或数字，可能表示合约的保证金比例。
字段10 (Field 10)：通常为空或数字，可能表示手续费率。
字段11 (Field 11)：通常为空或单位，例如 港币/张，表示价格单位。
字段12 (Field 12)：通常为空或单位，例如 股、吨 等，表示交易单位。
字段13 (Field 13)：通常为数字，可能表示合约的代码或分类。
字段14 (Field 14)：通常为数字，可能表示合约的某些特定属性。
字段15 (Field 15)：通常为文字描述，表示合约的交割规则或最后交易日规则。
文件 3：code2name_qq.ini
该文件主要包含其他期货市场的品种信息。每个条目包含以下字段：
代码 (Code)：期货品种的代码，例如 A、B、C 等。
名称 (Name)：期货品种的中文名称，例如 豆一、豆二、玉米 等。
交易所 (Exchange)：期货交易所的简称，例如 OD（大连商品交易所）、OG（上海国际能源交易中心）等。
字段4 (Field 4)：通常为数字，可能表示合约的类型或分类。
字段5 (Field 5)：通常为空或日期，可能表示合约的某个特定日期。
字段6 (Field 6)：通常为数字，可能表示合约的乘数或单位。
字段7 (Field 7)：通常为数字，可能表示最小变动价位。
字段8 (Field 8)：通常为空或数字，可能表示价格波动限制。
字段9 (Field 9)：通常为空或数字，可能表示合约的保证金比例。
字段10 (Field 10)：通常为空或数字，可能表示手续费率。
字段11 (Field 11)：通常为空或单位，例如 元/手，表示价格单位。
字段12 (Field 12)：通常为空或单位，例如 吨、手 等，表示交易单位。
字段13 (Field 13)：通常为数字，可能表示合约的代码或分类。
字段14 (Field 14)：通常为数字，可能表示合约的某些特定属性。
字段15 (Field 15)：通常为数字，可能表示合约的某些特定属性。
字段16 (Field 16)：通常为文字描述，表示合约的交割规则或最后交易日规则。

#44 新三板
#74 美股
#44 新三板
#47 中金所 CZ CFFEX
#28 郑州商品 QZ CZCE
#29 大连商品 QD DCE
#30 上海商品 QS SHFE
#66 广州期货 GZ

#市场：郑州QZ：28/大连QD：29/上海QS：30/中金CZ：47/广州：66

code2name.ini: csv结构 所有期货代码
字段1：代码
字段2：名称
字段3：交易所