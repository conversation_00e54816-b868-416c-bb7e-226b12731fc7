#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tick数据功能测试脚本
测试新增的tick数据获取功能
"""

import requests
import json
import time
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 测试配置
TDX_SERVER_BASE_URL = "http://127.0.0.1:5003"
TEST_SYMBOLS = [
    {"market": "sh", "code": "000001", "name": "上证指数"},
    {"market": "sz", "code": "000001", "name": "平安银行"},
    {"market": "sz", "code": "000002", "name": "万科A"},
    {"market": "sh", "code": "600000", "name": "浦发银行"},
]

def test_tick_api():
    """测试实时tick数据API"""
    print("=" * 60)
    print("测试实时tick数据API (/tick)")
    print("=" * 60)
    
    for symbol in TEST_SYMBOLS:
        market = symbol["market"]
        code = symbol["code"]
        name = symbol["name"]
        
        print(f"\n测试品种: {name} ({market}:{code})")
        print("-" * 40)
        
        try:
            # 发送请求
            url = f"{TDX_SERVER_BASE_URL}/tick"
            params = {
                "market": market,
                "code": code
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print("✓ 请求成功")
                print(f"  价格: {data.get('price', 'N/A')}")
                print(f"  成交量: {data.get('volume', 'N/A')}")
                print(f"  买一价: {data.get('bid1', 'N/A')}")
                print(f"  卖一价: {data.get('ask1', 'N/A')}")
                print(f"  时间: {data.get('time', 'N/A')}")
            else:
                print(f"✗ 请求失败: {response.status_code}")
                print(f"  错误信息: {response.text}")
                
        except Exception as e:
            print(f"✗ 请求异常: {e}")
        
        # 避免请求过于频繁
        time.sleep(0.5)

def test_tick_history_api():
    """测试历史tick数据API"""
    print("\n" + "=" * 60)
    print("测试历史tick数据API (/tick/history)")
    print("=" * 60)
    
    # 只测试A股，因为历史tick数据主要支持A股
    a_stock_symbols = [s for s in TEST_SYMBOLS if s["market"] in ["sh", "sz"]]
    
    for symbol in a_stock_symbols[:2]:  # 只测试前两个
        market = symbol["market"]
        code = symbol["code"]
        name = symbol["name"]
        
        print(f"\n测试品种: {name} ({market}:{code})")
        print("-" * 40)
        
        try:
            # 发送请求
            url = f"{TDX_SERVER_BASE_URL}/tick/history"
            params = {
                "market": market,
                "code": code
                # 不指定date，使用默认今天
            }
            
            response = requests.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                print("✓ 请求成功")
                print(f"  历史tick数量: {len(data)}")
                
                if data:
                    # 显示前几条数据
                    print("  前3条数据:")
                    for i, tick in enumerate(data[:3]):
                        print(f"    [{i+1}] 价格: {tick.get('price', 'N/A')}, "
                              f"成交量: {tick.get('volume', 'N/A')}, "
                              f"时间: {tick.get('time', 'N/A')}")
                else:
                    print("  无历史tick数据")
            else:
                print(f"✗ 请求失败: {response.status_code}")
                print(f"  错误信息: {response.text}")
                
        except Exception as e:
            print(f"✗ 请求异常: {e}")
        
        # 避免请求过于频繁
        time.sleep(1)

def test_server_status():
    """测试服务器状态"""
    print("=" * 60)
    print("测试服务器状态")
    print("=" * 60)
    
    try:
        # 测试基本连接
        response = requests.get(f"{TDX_SERVER_BASE_URL}/quote?market=sh&code=000001", timeout=5)
        if response.status_code == 200:
            print("✓ TDX服务器运行正常")
            return True
        else:
            print(f"✗ TDX服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到TDX服务器: {e}")
        print("请确保tdxserver.py正在运行")
        return False

def main():
    """主测试函数"""
    print("TDX Tick数据功能测试")
    print("测试服务器地址:", TDX_SERVER_BASE_URL)
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 检查服务器状态
    if not test_server_status():
        print("\n测试终止：服务器不可用")
        return
    
    # 测试实时tick数据
    test_tick_api()
    
    # 测试历史tick数据
    test_tick_history_api()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    print("\n使用说明:")
    print("1. 实时tick数据API: GET /tick?market=<市场>&code=<代码>")
    print("2. 历史tick数据API: GET /tick/history?market=<市场>&code=<代码>&date=<日期>")
    print("3. 支持的市场: sh(上海), sz(深圳), hk(香港), us(美国), shfe(上期所)等")
    print("4. 历史tick数据主要支持A股市场")

if __name__ == "__main__":
    main()
