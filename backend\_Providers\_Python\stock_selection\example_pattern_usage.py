#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
形态选股策略使用示例
"""

import os
import sys
from typing import Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from engine import StockSelectionEngine

def progress_callback(progress_data: Dict[str, Any]):
    """进度回调函数"""
    stage = progress_data['stage']
    current = progress_data.get('current')
    total = progress_data.get('total')
    selected_count = progress_data.get('selected_count')
    symbol_info = progress_data.get('symbol_info')
    
    if stage == '开始选股':
        print(f"[进度] {stage}")
    elif stage == '获取候选品种列表':
        print(f"[进度] {stage}")
    elif stage == '候选品种获取完成':
        print(f"[进度] {stage}，共 {total} 个品种")
    elif stage == '处理品种':
        if current is not None and total is not None and total > 0:
            progress = (current / total * 100)
        else:
            progress = 0
        print(f"[进度] {stage}: {current}/{total} ({progress:.1f}%) - 已选中: {selected_count} - {symbol_info}")
    elif stage == '选股完成':
        print(f"[进度] {stage}，共选中 {selected_count} 个品种")

def main():
    """主函数"""
    # 配置选股引擎 - 使用形态选股策略
    config = {
        'candidate_type': 0,  # 0=A股, 1=期货, 2=美股
        'default_klines': 100,
        'strategy_module': 'strategies/pattern_strategy.py',
        'strategy_params': {
            'pattern': [5, 4, 3, 2, 2, 1, 1, 5],  # 形态定义：8个元素的序列
            'match_threshold': 0.4,  # 匹配度阈值
            'grid_count': 5  # 网格数量
        },
        'progress_callback': progress_callback
    }
    
    try:
        # 创建选股引擎
        engine = StockSelectionEngine(config)
        
        # 执行选股
        selected_symbols = engine.run_selection()
        
        # 输出结果
        print(f"\n=== 形态选股结果 ===")
        print(f"共选中 {len(selected_symbols)} 个品种:")
        
        for i, symbol in enumerate(selected_symbols, 1):
            print(f"{i}. {symbol['code']} - {symbol.get('name', '')}")
            
        # 获取详细结果
        result = engine.get_selection_result()
        print(f"\n=== 详细统计 ===")
        print(f"总候选品种: {result['total_symbols']}")
        print(f"已处理品种: {result['processed_symbols']}")
        print(f"选中品种数: {result['selected_count']}")
        
    except Exception as e:
        print(f"选股执行失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_pattern_calculation():
    """测试形态计算功能"""
    print("\n=== 测试形态计算 ===")
    
    # 模拟K线数据
    test_klines = [
        {'time': 1000, 'open': 10.0, 'high': 12.0, 'low': 9.0, 'close': 11.0, 'volume': 1000, 'amount': 11000, 'change': 1.0, 'change_pct': 10.0},
        {'time': 1001, 'open': 11.0, 'high': 13.0, 'low': 10.0, 'close': 12.0, 'volume': 1100, 'amount': 13200, 'change': 1.0, 'change_pct': 9.1},
        {'time': 1002, 'open': 12.0, 'high': 14.0, 'low': 11.0, 'close': 13.0, 'volume': 1200, 'amount': 15600, 'change': 1.0, 'change_pct': 8.3},
        {'time': 1003, 'open': 13.0, 'high': 15.0, 'low': 12.0, 'close': 14.0, 'volume': 1300, 'amount': 18200, 'change': 1.0, 'change_pct': 7.7},
        {'time': 1004, 'open': 14.0, 'high': 16.0, 'low': 13.0, 'close': 15.0, 'volume': 1400, 'amount': 21000, 'change': 1.0, 'change_pct': 7.1},
        {'time': 1005, 'open': 15.0, 'high': 17.0, 'low': 14.0, 'close': 16.0, 'volume': 1500, 'amount': 24000, 'change': 1.0, 'change_pct': 6.7},
        {'time': 1006, 'open': 16.0, 'high': 18.0, 'low': 15.0, 'close': 17.0, 'volume': 1600, 'amount': 27200, 'change': 1.0, 'change_pct': 6.3},
        {'time': 1007, 'open': 17.0, 'high': 19.0, 'low': 16.0, 'close': 18.0, 'volume': 1700, 'amount': 30600, 'change': 1.0, 'change_pct': 5.9},
    ]
    
    # 导入策略函数
    from strategies.pattern_strategy import calculate_pattern_sequence, calculate_match_score
    
    # 测试形态计算
    pattern = calculate_pattern_sequence(test_klines, 5)
    print(f"计算出的形态序列: {pattern}")
    
    # 测试匹配度计算
    target_pattern = [5, 4, 3, 2, 2, 1, 1, 5]
    match_score = calculate_match_score(pattern, target_pattern)
    print(f"目标形态序列: {target_pattern}")
    print(f"匹配度: {match_score:.3f}")
    
    # 测试选股策略
    params = {
        'pattern': target_pattern,
        'match_threshold': 0.4,
        'grid_count': 5
    }
    
    from strategies.pattern_strategy import select_stock
    result = select_stock(test_klines, params)
    print(f"选股结果: {'选中' if result else '未选中'}")

if __name__ == "__main__":
    # 先测试形态计算
    test_pattern_calculation()
    
    # 然后运行完整的选股示例
    print("\n" + "="*50)
    main() 