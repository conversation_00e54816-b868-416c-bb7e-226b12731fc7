import React from 'react';
import { List, Card, Typography, Empty } from 'antd';
import { useAtom } from 'jotai';
import { currentGeneConfigAtom } from '../../../../store/state';
import './index.less';

const { Text } = Typography;

const GeneList: React.FC = () => {
  const [currentGeneConfig] = useAtom(currentGeneConfigAtom);

  if (!currentGeneConfig) {
    return null;
  }

  return (
    <Card title="定位列表" className="gene-list">
      <List
        size="small"
        bordered
        dataSource={[]}
        locale={{
          emptyText: <Empty description="暂无定位结果" />
        }}
        renderItem={item => (
          <List.Item>
            <Text>{item}</Text>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default GeneList; 