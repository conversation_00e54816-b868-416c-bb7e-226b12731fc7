.toolbar {
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-color);

  .search-section {
    position: relative;

    :global {
      .ant-select-dropdown {
        @media screen and (max-width: 768px) {
          min-width: 280px !important;
          width: 90vw !important;
        }
      }
    }

    .search-results {
      position: absolute;
      top: 100%;
      left: 0;
      width: 300px;
      max-height: 400px;
      overflow-y: auto;
      background-color: #fff;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: 1000;

      .search-result-item {
        padding: 8px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;

        &:hover {
          background-color: #f5f5f5;
        }

        .symbol-code {
          font-weight: 500;
          color: #1890ff;
        }

        .symbol-name {
          color: rgba(0, 0, 0, 0.85);
        }

        .symbol-market {
          color: rgba(0, 0, 0, 0.45);
          margin-left: auto;
        }
      }
    }
  }

  :global {
    .ant-space {
      .ant-space-item {
        height: 100%;
      }
    }

    // 工具按钮样式
    .ant-btn-circle {
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: var(--primary-color);
        background-color: var(--hover-color);
      }

      &.ant-btn-primary {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);

        &:hover {
          background-color: var(--primary-color-hover);
          border-color: var(--primary-color-hover);
        }
      }
    }
  }
}

.period-selector {
  height: 100%;
  display: flex;
  align-items: center;
  
  :global {
    .ant-radio-button-wrapper {
      height: 100%;
      padding: 0 12px;
      background: transparent;
      border: none;
      border-right: 1px solid var(--border-color);
      color: var(--text-color);
      margin: 0;
      display: flex;
      align-items: center;
      transition: all 0.3s;
      
      &:hover {
        color: var(--primary-color);
        background: var(--hover-color);
      }

      &::before {
        display: none;
      }

      &.ant-radio-button-wrapper-checked {
        background: var(--primary-color);
        color: var(--text-color-inverse);

        &:hover {
          background: var(--primary-color);
          color: var(--text-color-inverse);
        }
      }
    }
  }
}

// 品种按钮的样式
.symbol-button {
  // 使用Ant Design主题变量
  background-color: rgba(0, 0, 0, 0.02) !important; // 亮色主题下轻微暗化
  
  &:hover {
    background-color: var(--hover-color, rgba(0, 0, 0, 0.05)) !important;
  }
  
  // 暗色主题下样式
  :global(.ant-pro-layout-content .dark &),
  :global([data-theme='dark'] &) {
    background-color: rgba(255, 255, 255, 0.08) !important; // 暗色主题下轻微亮化
    border-color: rgba(255, 255, 255, 0.15) !important;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.12) !important;
    }
  }
}

// 手机屏幕下的响应式样式
@media screen and (max-width: 768px) {
  .toolbar {
    .search-section {
      :global {
        .ant-select-dropdown {
          min-width: 280px !important;
          width: 90vw !important;
        }
      }
    }
    
    // 手机屏幕下减少按钮间距
    .tool-buttons {
      .ant-btn {
        padding: 4px 6px !important;
        min-width: 32px !important;
        height: 32px !important;
      }
    }
    
    // 手机屏幕下调整品种按钮样式
    .symbol-button {
      padding: 4px 8px !important;
      font-size: 12px !important;
      max-width: 120px !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }
    
    // 手机屏幕下减少分隔线间距
    .ant-divider-vertical {
      margin: 0 4px !important;
      height: 20px !important;
    }
    
    // 手机屏幕下调整周期选择器
    .period-selector {
      :global {
        .ant-radio-button-wrapper {
          padding: 0 6px !important;
          font-size: 12px !important;
        }
      }
    }
    
    // 手机屏幕下调整下拉按钮
    .ant-dropdown-trigger {
      padding: 4px 6px !important;
      font-size: 12px !important;
    }
  }
} 