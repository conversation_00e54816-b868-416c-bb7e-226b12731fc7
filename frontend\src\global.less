 // 导入 Ant Design 样式
@import 'antd/dist/reset.css';

// 全局样式
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

// 防止拖动选择内容
* {
  user-select: none;
}

// 输入元素应该可选
input, textarea {
  user-select: text;
}

// 修复一些 Ant Design 在深色模式下的样式问题
:root {
  --primary-color: #1890ff;
  --link-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --font-size-base: 14px;
  --heading-color: rgba(0, 0, 0, 0.85);
  --text-color: rgba(0, 0, 0, 0.65);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --disabled-color: rgba(0, 0, 0, 0.25);
  --border-radius-base: 2px;
  --border-color-base: #d9d9d9;
  --box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

// 自定义滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}