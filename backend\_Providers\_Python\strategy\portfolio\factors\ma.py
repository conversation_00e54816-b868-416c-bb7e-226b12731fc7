# ma.py - Simple Moving Average Factor

import numpy as np
import pandas as pd # Using pandas for rolling calculations

def calculate_value(opens: np.ndarray, highs: np.ndarray, lows: np.ndarray,
                    closes: np.ndarray, volumes: np.ndarray,
                    **params) -> float:
    """
    计算简单移动平均值 (Simple Moving Average, MA)。

    Args:
        opens (np.ndarray): 开盘价数组。
        highs (np.ndarray): 最高价数组。
        lows (np.ndarray): 最低价数组。
        closes (np.ndarray): 收盘价数组。
        volumes (np.ndarray): 成交量数组。
        **params: 关键字参数，必须包含:
            column (str): 指定要计算 MA 的数据列 ('open', 'high', 'low', 'close', 'volume')。
            period (int): MA 的计算周期 (必须 > 0)。

    Returns:
        float: 计算得到的最新 MA 值。如果数据不足或参数无效，则返回 np.nan。
    """
    column = params.get('column')
    period = params.get('period')

    # --- 参数验证 ---
    if column is None or period is None:
        # print("[MA Factor] 错误: 缺少 'column' 或 'period' 参数。") # 可选日志
        return np.nan
        
    if not isinstance(period, int) or period <= 0:
        # print(f"[MA Factor] 错误: 'period' 必须是正整数，但得到 {period}。") # 可选日志
        return np.nan
        
    valid_columns = {'open': opens, 'high': highs, 'low': lows, 'close': closes, 'volume': volumes}
    if column not in valid_columns:
        # print(f"[MA Factor] 错误: 无效的 'column' 参数: {column}。可选值: {list(valid_columns.keys())}") # 可选日志
        return np.nan
        
    # 获取指定列的数据
    data_array = valid_columns[column]

    # --- 数据长度检查 ---
    if len(data_array) < period:
        # print(f"[MA Factor] 警告: 数据长度 ({len(data_array)}) 小于指定的周期 ({period})，无法计算 MA。") # 可选日志
        return np.nan

    # --- 计算 MA ---
    try:
        # 使用 Pandas Series 进行滚动计算
        s = pd.Series(data_array)
        ma_series = s.rolling(window=period, min_periods=period).mean()
        
        # 返回最新的 MA 值
        latest_ma = ma_series.iloc[-1]
        
        # 检查结果是否为 NaN (可能发生在滚动窗口刚满足条件时，某些实现可能返回 NaN)
        if pd.isna(latest_ma):
            return np.nan
        else:
            return float(latest_ma)
            
    except Exception as e:
        # print(f"[MA Factor] 计算 MA 时发生错误: {e}") # 可选日志
        return np.nan

# --- Example Usage (for testing) ---
if __name__ == '__main__':
    # Mock data
    closes_data = np.array([10, 11, 12, 13, 14, 15, 16, 17, 18, 19])
    volumes_data = np.array([100, 110, 120, 130, 140, 150, 160, 170, 180, 190])
    dummy_data = np.zeros_like(closes_data) # For unused arrays

    # Test cases
    print("--- 测试 MA Factor ---")
    
    # Test 1: MA(close, 5)
    ma_close_5 = calculate_value(dummy_data, dummy_data, dummy_data, closes_data, dummy_data, column='close', period=5)
    expected_close_5 = (15 + 16 + 17 + 18 + 19) / 5
    print(f"MA(close, 5): Expected={expected_close_5}, Calculated={ma_close_5}")
    
    # Test 2: MA(volume, 3)
    ma_vol_3 = calculate_value(dummy_data, dummy_data, dummy_data, dummy_data, volumes_data, column='volume', period=3)
    expected_vol_3 = (170 + 180 + 190) / 3
    print(f"MA(volume, 3): Expected={expected_vol_3}, Calculated={ma_vol_3}")

    # Test 3: Insufficient data
    ma_close_15 = calculate_value(dummy_data, dummy_data, dummy_data, closes_data, dummy_data, column='close', period=15)
    print(f"MA(close, 15) (Insufficient Data): Expected=NaN, Calculated={ma_close_15}")

    # Test 4: Invalid column
    ma_invalid_col = calculate_value(dummy_data, dummy_data, dummy_data, closes_data, dummy_data, column='invalid', period=5)
    print(f"MA(invalid, 5): Expected=NaN, Calculated={ma_invalid_col}")

    # Test 5: Invalid period (zero)
    ma_invalid_period_0 = calculate_value(dummy_data, dummy_data, dummy_data, closes_data, dummy_data, column='close', period=0)
    print(f"MA(close, 0): Expected=NaN, Calculated={ma_invalid_period_0}")

    # Test 6: Invalid period (negative)
    ma_invalid_period_neg = calculate_value(dummy_data, dummy_data, dummy_data, closes_data, dummy_data, column='close', period=-5)
    print(f"MA(close, -5): Expected=NaN, Calculated={ma_invalid_period_neg}")

    # Test 7: Missing period
    ma_missing_period = calculate_value(dummy_data, dummy_data, dummy_data, closes_data, dummy_data, column='close')
    print(f"MA(close, ?) (Missing Period): Expected=NaN, Calculated={ma_missing_period}")
    
    # Test 8: Missing column
    ma_missing_column = calculate_value(dummy_data, dummy_data, dummy_data, closes_data, dummy_data, period=5)
    print(f"MA(?, 5) (Missing Column): Expected=NaN, Calculated={ma_missing_column}") 