#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths客户端主程序

该程序在Windows端运行，负责启动同花顺交易接口和反向连接服务。
通过内网穿透机制，使远程后端能够与本地同花顺客户端通信。
"""

# pyright: reportGeneralTypeIssues=false
# 忽略类型检查错误，因为 easytrader 库没有提供类型注解

import os
import sys
import time
import json
import logging
import argparse
import requests
import threading
import getpass
import easytrader
try:
    import socketio
except AttributeError:
    # 尝试从 python-socketio 导入
    from socketio import Client as SocketIOClient
else:
    SocketIOClient = socketio.Client
from flask import Flask, request, jsonify

# 配置日志
# --- 修改：明确指定 FileHandler 的编码 ---
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_file_handler = logging.FileHandler("easytrader_ths_client.log", encoding='utf-8') # 指定编码
log_file_handler.setFormatter(log_formatter)
log_stream_handler = logging.StreamHandler()
log_stream_handler.setFormatter(log_formatter)

logger = logging.getLogger("EasyTrader-THS-Client")
logger.setLevel(logging.INFO)
logger.addHandler(log_file_handler)
logger.addHandler(log_stream_handler)
# 移除旧的 basicConfig 调用
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler("easytrader_ths_client.log"),
#         logging.StreamHandler()
#     ]
# )
# --- 结束修改 ---

# 全局变量
trader = None
app = Flask(__name__)
sio = socketio.Client(logger=True, engineio_logger=True)  # type: ignore # 忽略 Client 类型检查
socket_connected = False

config = {
    "username": "",
    "backend_url": "",
    "local_port": 8888,
    "heartbeat_interval": 60,
    "ths_path": "",
    "auth_token": "",      # 用于存储认证token
    "socket_port": None,   # Socket.IO端口
}

def init_trader():
    """初始化同花顺交易接口"""
    global trader
    try:
        logger.info(f"正在初始化同花顺交易接口，路径: {config['ths_path']}")
        trader = easytrader.use('ths')
        trader.connect(config['ths_path'])
        logger.info("同花顺交易接口初始化成功")
        # 检查登录状态
        account_info = trader.balance
        logger.info(f"账户信息: {account_info}")
        return True
    except Exception as e:
        logger.error(f"初始化同花顺交易接口失败: {str(e)}")
        return False

# Socket.IO事件处理
@sio.event
def connect():
    """Socket.IO默认命名空间连接成功事件"""
    global socket_connected
    logger.info("Socket.IO默认命名空间连接成功")
    # 不设置 socket_connected = True，因为我们需要等待 /trade 命名空间连接成功

@sio.on('connect', namespace='/trade')
def connect_trade():
    """Socket.IO /trade 命名空间连接成功事件"""
    global socket_connected
    logger.info("Socket.IO /trade 命名空间连接成功")
    socket_connected = True

    # 连接成功后立即注册
    register_via_socketio()

@sio.event
def connect_error(error):
    """Socket.IO默认命名空间连接错误事件"""
    logger.error(f"Socket.IO默认命名空间连接错误: {error}")

@sio.on('connect_error', namespace='/trade')
def connect_error_trade(error):
    """Socket.IO /trade 命名空间连接错误事件"""
    global socket_connected
    logger.error(f"Socket.IO /trade 命名空间连接错误: {error}")
    socket_connected = False

@sio.event
def disconnect():
    """Socket.IO默认命名空间断开连接事件"""
    logger.info("Socket.IO默认命名空间断开连接")

@sio.on('disconnect', namespace='/trade')
def disconnect_trade():
    """Socket.IO /trade 命名空间断开连接事件"""
    global socket_connected
    logger.info("Socket.IO /trade 命名空间断开连接")
    socket_connected = False

    # 尝试重新连接
    threading.Timer(5.0, connect_to_socketio).start()

@sio.on('execute_command', namespace='/trade')
def on_execute_command(data):
    """处理执行命令事件"""
    logger.info(f"收到执行命令: {data}")

    try:
        action = data.get('action')
        params = data.get('params', {})

        if not action:
            logger.error("执行命令失败: 缺少action参数")
            sio.emit('command_response', {
                'success': False,
                'message': '缺少action参数'
            }, namespace='/trade')
            return

        # 执行相应的操作
        result = execute_command(action, params)

        # 发送响应
        sio.emit('command_response', {
            'success': True,
            'action': action,
            'result': result
        }, namespace='/trade')
    except Exception as e:
        logger.error(f"执行命令失败: {str(e)}")
        sio.emit('command_response', {
            'success': False,
            'message': str(e)
        }, namespace='/trade')

def execute_command(action, params):
    """执行命令"""
    if trader is None:
        raise Exception("交易接口未初始化")

    if action == 'buy':
        code = params.get('code')
        price = params.get('price')
        amount = params.get('amount')

        if not all([code, price is not None, amount is not None]):
            raise Exception("参数不完整 (code, price, amount)")

        # 注意：easytrader 的 buy 方法接受 float 类型的 price 参数
        # 这里的类型错误是 IDE 的误报，实际运行没有问题
        return trader.buy(str(code), price=float(price), amount=int(amount))  # type: ignore

    elif action == 'sell':
        code = params.get('code')
        price = params.get('price')
        amount = params.get('amount')

        if not all([code, price is not None, amount is not None]):
            raise Exception("参数不完整 (code, price, amount)")

        return trader.sell(str(code), price=float(price), amount=int(amount))

    elif action == 'cancel':
        entrust_no = params.get('entrust_no')

        if not entrust_no:
            raise Exception("参数不完整 (entrust_no)")

        return trader.cancel_entrust(int(entrust_no))

    elif action == 'balance':
        return trader.balance

    elif action == 'position':
        return trader.position

    elif action == 'today_entrusts':
        return trader.today_entrusts

    elif action == 'today_trades':
        return trader.today_trades

    elif action == 'refresh':
        trader.refresh()
        return {"message": "刷新成功"}

    else:
        raise Exception(f"不支持的操作: {action}")

def register_via_socketio():
    """通过Socket.IO注册客户端"""
    try:
        data = {
            "username": config["username"],
            "client_type": "easytrader_ths",
            "timestamp": int(time.time())
        }

        logger.info("通过Socket.IO注册客户端")
        # 在 /trade 命名空间中发送注册消息
        sio.emit('register', data, namespace='/trade')
        return True
    except Exception as e:
        logger.error(f"通过Socket.IO注册客户端失败: {str(e)}")
        return False

def get_socket_port():
    """从服务器获取可用的Socket.IO端口"""
    global config

    # 确保已登录并获取token
    if not config.get("auth_token") and not login_to_backend():
        logger.error("未能获取认证token，无法获取Socket.IO端口")
        return False

    try:
        # 构建获取端口API URL
        # 检查 backend_url 是否已经包含 /api/trade 路径
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            port_url = f"{base_url}/api/socket/get_port"
        else:
            # 否则直接添加 /api/socket/get_port
            port_url = f"{backend_url}/api/socket/get_port"

        if not port_url.startswith(("http://", "https://")):
            port_url = f"http://{port_url}"

        logger.info(f"尝试获取Socket.IO端口: {port_url}")

        # 添加认证头
        headers = {
            "Authorization": f"Bearer {config['auth_token']}",
            "Content-Type": "application/json"
        }

        # 发送请求
        response = requests.post(
            port_url,
            headers=headers,
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            # 打印响应内容以便调试
            logger.info(f"获取Socket.IO端口响应: {result}")

            # 检查响应格式，支持两种可能的格式：
            # 1. { success: true, port: 8000 }
            # 2. { success: true, data: { port: 8000 } }
            if result.get("success"):
                # 检查 port 是在根级别还是在 data 对象中
                if result.get("port"):
                    config["socket_port"] = result["port"]
                    logger.info(f"成功获取Socket.IO端口: {config['socket_port']}")
                    save_config()
                    return True
                elif result.get("data") and result["data"].get("port"):
                    config["socket_port"] = result["data"]["port"]
                    logger.info(f"成功获取Socket.IO端口: {config['socket_port']}")
                    save_config()
                    return True
                else:
                    logger.error("获取Socket.IO端口响应中未找到port字段")
                    print("获取Socket.IO端口响应中未找到port字段")
            else:
                error_msg = result.get('error') or result.get('message') or '未知错误'
                logger.error(f"获取Socket.IO端口失败: {error_msg}")
                print(f"获取Socket.IO端口失败: {error_msg}")
        elif response.status_code == 401:
            logger.error("认证失败，token可能已过期")
            # 清除token并尝试重新登录
            config["auth_token"] = ""
            save_config()
            if login_to_backend():
                # 重新尝试获取端口
                return get_socket_port()
        else:
            logger.error(f"获取Socket.IO端口失败，状态码: {response.status_code}")
            print(f"获取Socket.IO端口失败，状态码: {response.status_code}")

        return False
    except Exception as e:
        logger.error(f"获取Socket.IO端口时发生错误: {str(e)}")
        print(f"获取Socket.IO端口时发生错误: {str(e)}")
        return False

def release_socket_port():
    """释放Socket.IO端口"""
    global config

    if not config.get("socket_port"):
        logger.debug("没有Socket.IO端口需要释放")
        return True

    # 确保已登录并获取token
    if not config.get("auth_token") and not login_to_backend():
        logger.error("未能获取认证token，无法释放Socket.IO端口")
        return False

    try:
        # 构建释放端口API URL
        # 检查 backend_url 是否已经包含 /api/trade 路径
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            release_url = f"{base_url}/api/socket/release_port"
        else:
            # 否则直接添加 /api/socket/release_port
            release_url = f"{backend_url}/api/socket/release_port"

        if not release_url.startswith(("http://", "https://")):
            release_url = f"http://{release_url}"

        logger.info(f"尝试释放Socket.IO端口 {config['socket_port']}: {release_url}")

        # 添加认证头
        headers = {
            "Authorization": f"Bearer {config['auth_token']}",
            "Content-Type": "application/json"
        }

        # 发送请求
        response = requests.post(
            release_url,
            json={"port": config["socket_port"]},
            headers=headers,
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                logger.info(f"成功释放Socket.IO端口: {config['socket_port']}")
                config["socket_port"] = None
                save_config()
                return True
            else:
                logger.error(f"释放Socket.IO端口失败: {result.get('message', '未知错误')}")
        elif response.status_code == 401:
            logger.error("认证失败，token可能已过期")
        else:
            logger.error(f"释放Socket.IO端口失败，状态码: {response.status_code}")

        return False
    except Exception as e:
        logger.error(f"释放Socket.IO端口时发生错误: {str(e)}")
        return False

def connect_to_socketio():
    """连接到Socket.IO服务器"""
    global socket_connected

    if socket_connected:
        logger.debug("Socket.IO已连接，无需重新连接")
        return True

    # 确保已登录并获取token
    if not config.get("auth_token") and not login_to_backend():
        logger.error("未能获取认证token，无法连接Socket.IO")
        return False

    # 确保已获取Socket.IO端口
    if not config.get("socket_port") and not get_socket_port():
        logger.error("未能获取Socket.IO端口，无法连接Socket.IO")
        return False

    try:
        # 从backend_url中提取主机名
        backend_url = config["backend_url"]

        # 移除协议前缀
        if backend_url.startswith(("http://", "https://")):
            backend_url = backend_url.replace("http://", "").replace("https://", "")

        # 移除路径部分，只保留主机名
        if "/" in backend_url:
            backend_url = backend_url.split("/")[0]

        # 提取主机名（不包含端口）
        host = backend_url.split(':')[0]

        # 构建Socket.IO连接URL
        socketio_url = f"http://{host}:{config['socket_port']}"
        logger.info(f"正在连接Socket.IO服务器: {socketio_url}")

        # 连接到Socket.IO服务器
        logger.info("开始连接Socket.IO服务器...")
        try:
            sio.connect(
                socketio_url,
                socketio_path="/socket.io",
                namespaces=["/trade"],  # 使用 trade 命名空间
                wait=True,  # 改为阻塞等待连接，确保连接建立
                wait_timeout=5,  # 设置连接超时时间为5秒
                transports=["websocket", "polling"]  # 优先使用WebSocket
            )
            # 如果连接成功，sio.connect不会抛出异常
            logger.info("Socket.IO连接初始化成功")
        except Exception as connect_error:
            logger.error(f"Socket.IO连接初始化失败: {connect_error}")
            # 尝试重新连接
            try:
                logger.info("尝试使用polling传输方式重新连接...")
                sio.connect(
                    socketio_url,
                    socketio_path="/socket.io",
                    namespaces=["/trade"],
                    wait=True,
                    wait_timeout=5,
                    transports=["polling", "websocket"]  # 优先使用polling
                )
                logger.info("使用polling传输方式连接成功")
            except Exception as retry_error:
                logger.error(f"重新连接失败: {retry_error}")
                return False

        # 等待连接建立
        logger.info("等待Socket.IO连接完全建立...")
        for i in range(20):  # 最多等待20秒
            if socket_connected:
                logger.info(f"Socket.IO连接成功，用时 {i+1} 秒")
                # 连接成功后，主动发送一个心跳包
                try:
                    heartbeat_data = {
                        "username": config["username"],
                        "client_type": "easytrader_ths",
                        "timestamp": int(time.time())
                    }
                    logger.info("发送初始心跳包...")
                    sio.emit('heartbeat', heartbeat_data)
                    logger.info("初始心跳包发送成功")
                except Exception as e:
                    logger.error(f"发送初始心跳包失败: {e}")
                return True
            logger.debug(f"等待Socket.IO连接... {i+1}/20 秒")
            time.sleep(1)

        if not socket_connected:
            logger.warning("Socket.IO连接超时，20秒内未收到连接成功事件")
            return False

        return True
    except Exception as e:
        logger.error(f"连接Socket.IO服务器失败: {str(e)}")
        return False

def register_to_backend():
    """向后端注册本客户端"""
    # 确保已登录并获取token
    if not config.get("auth_token") and not login_to_backend():
        logger.error("未能获取认证token，无法注册客户端")
        return False

    # 通过Socket.IO连接和注册
    if connect_to_socketio():
        logger.info("已通过Socket.IO连接到后端")
        return True

    logger.error("无法通过Socket.IO连接到后端")
    return False

def heartbeat_thread():
    """定时向后端发送心跳"""
    while True:
        try:
            # 确保有认证token
            if not config.get("auth_token"):
                logger.warning("没有认证token，尝试登录")
                if not login_to_backend():
                    logger.error("登录失败，无法发送心跳")
                    time.sleep(config["heartbeat_interval"])
                    continue

            # 确保Socket.IO已连接
            if not socket_connected:
                logger.warning("Socket.IO未连接，尝试重新连接")
                if not connect_to_socketio():
                    logger.error("Socket.IO连接失败，无法发送心跳")
                    time.sleep(config["heartbeat_interval"])
                    continue

            # 发送心跳
            try:
                heartbeat_data = {
                    "username": config["username"],
                    "client_type": "easytrader_ths",
                    "timestamp": int(time.time())
                }

                logger.debug("通过Socket.IO发送心跳")
                # 在 /trade 命名空间中发送心跳消息
                sio.emit('heartbeat', heartbeat_data, namespace='/trade')
            except Exception as e:
                logger.error(f"发送心跳失败: {str(e)}")
                # 如果发送失败，尝试重新连接
                if not socket_connected:
                    connect_to_socketio()

        except Exception as e:
            logger.error(f"心跳线程发生错误: {str(e)}")

        time.sleep(config["heartbeat_interval"])

def get_local_ip():
    """获取本机IP地址"""
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

# API路由
@app.route('/api/balance', methods=['GET'])
def get_balance():
    """获取账户余额"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        balance = trader.balance
        return jsonify({"success": True, "data": balance})
    except Exception as e:
        logger.error(f"获取账户余额失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/position', methods=['GET'])
def get_position():
    """获取持仓信息"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        position = trader.position
        return jsonify({"success": True, "data": position})
    except Exception as e:
        logger.error(f"获取持仓信息失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/buy', methods=['POST'])
def buy():
    """买入股票"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        data = request.get_json(silent=True)
        if data is None:
             return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400

        code = data.get('code')
        price = data.get('price')
        amount = data.get('amount')

        if not all([code, price is not None, amount is not None]):
            return jsonify({"success": False, "message": "参数不完整 (code, price, amount)"}), 400

        try:
            result = trader.buy(str(code), price=float(price), amount=int(amount))
            return jsonify({"success": True, "data": result})
        except ValueError:
             logger.error(f"买入股票失败: price或amount参数无法转换为正确的数字类型 (price='{price}', amount='{amount}')")
             return jsonify({"success": False, "message": "price和amount参数必须是有效的数字"}), 400
    except Exception as e:
        logger.error(f"买入股票失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/sell', methods=['POST'])
def sell():
    """卖出股票"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        data = request.get_json(silent=True)
        if data is None:
             return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400

        code = data.get('code')
        price = data.get('price')
        amount = data.get('amount')

        if not all([code, price is not None, amount is not None]):
            return jsonify({"success": False, "message": "参数不完整 (code, price, amount)"}), 400

        try:
            result = trader.sell(str(code), price=float(price), amount=int(amount))
            return jsonify({"success": True, "data": result})
        except ValueError:
             logger.error(f"卖出股票失败: price或amount参数无法转换为正确的数字类型 (price='{price}', amount='{amount}')")
             return jsonify({"success": False, "message": "price和amount参数必须是有效的数字"}), 400
    except Exception as e:
        logger.error(f"卖出股票失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/cancel', methods=['POST'])
def cancel_order():
    """撤销订单"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        data = request.get_json(silent=True)
        if data is None:
             return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400

        entrust_no_str = data.get('entrust_no') # Get as string first

        if not entrust_no_str:
            return jsonify({"success": False, "message": "参数不完整 (entrust_no)"}), 400

        # --- 修改: 将 entrust_no 转换为 int ---
        try:
            entrust_no_int = int(entrust_no_str)
            result = trader.cancel_entrust(entrust_no_int) # Pass integer
            return jsonify({"success": True, "data": result})
        except ValueError:
            logger.error(f"撤销订单失败: entrust_no 参数无法转换为整数 (entrust_no='{entrust_no_str}')")
            return jsonify({"success": False, "message": "entrust_no 参数必须是有效的数字"}), 400
        # --- 结束修改 ---
    except Exception as e:
        logger.error(f"撤销订单失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/today_entrusts', methods=['GET'])
def get_today_entrusts():
    """获取今日委托"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        entrusts = trader.today_entrusts
        return jsonify({"success": True, "data": entrusts})
    except Exception as e:
        logger.error(f"获取今日委托失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/today_trades', methods=['GET'])
def get_today_trades():
    """获取今日成交"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        trades = trader.today_trades
        return jsonify({"success": True, "data": trades})
    except Exception as e:
        logger.error(f"获取今日成交失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/refresh', methods=['POST'])
def refresh():
    """刷新接口"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        trader.refresh()
        return jsonify({"success": True, "message": "刷新成功"})
    except Exception as e:
        logger.error(f"刷新接口失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取客户端状态"""
    try:
        status = {
            "trader_initialized": trader is not None,
            "username": config["username"],
            "local_ip": get_local_ip(),
            "local_port": config["local_port"],
            "backend_url": config["backend_url"],
            "timestamp": int(time.time())
        }

        if trader is not None:
            try:
                # 尝试获取账户信息，检查交易接口是否正常
                balance = trader.balance
                status["trader_status"] = "connected"
            except Exception:
                status["trader_status"] = "error"
        else:
            status["trader_status"] = "not_initialized"

        return jsonify({"success": True, "data": status})
    except Exception as e:
        logger.error(f"获取状态失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

def login_to_backend():
    """登录到后端获取token"""
    global config

    # 如果已有token且不为空，直接返回
    if config.get("auth_token"):
        logger.info("已有认证token，无需重新登录")
        return True

    # 提示用户输入登录信息
    print("\n===== 登录到量化平台 =====")

    # 如果配置中没有后端URL，提示输入
    if not config.get("backend_url"):
        backend_url = input("请输入后端服务器地址 (例如: http://example.com:3000): ")
        config["backend_url"] = backend_url.strip()
    else:
        print(f"后端服务器地址: {config['backend_url']}")
        change = input("是否修改后端地址? (y/n): ").lower()
        if change == 'y':
            backend_url = input("请输入新的后端服务器地址: ")
            config["backend_url"] = backend_url.strip()

    # 如果配置中没有用户名，提示输入
    if not config.get("username"):
        username = input("请输入用户名: ")
        config["username"] = username.strip()
    else:
        print(f"用户名: {config['username']}")
        change = input("是否修改用户名? (y/n): ").lower()
        if change == 'y':
            username = input("请输入新的用户名: ")
            config["username"] = username.strip()

    # 提示输入密码
    password = getpass.getpass("请输入密码: ")

    # 尝试登录
    try:
        # 构建登录API URL
        # 检查 backend_url 是否已经包含 /api/trade 路径
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            login_url = f"{base_url}/api/user/login"
        else:
            # 否则直接添加 /api/user/login
            login_url = f"{backend_url}/api/user/login"

        if not login_url.startswith(("http://", "https://")):
            login_url = f"http://{login_url}"

        logger.info(f"尝试登录到: {login_url}")

        # 发送登录请求
        response = requests.post(
            login_url,
            json={
                "username": config["username"],
                "password": password
            },
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            # 打印响应内容（不包含敏感信息）以便调试
            logger.info(f"登录响应: {result}")

            # 检查响应格式，支持两种可能的格式：
            # 1. { success: true, token: "..." }
            # 2. { success: true, data: { token: "..." } }
            if result.get("success"):
                # 检查 token 是在根级别还是在 data 对象中
                if result.get("token"):
                    config["auth_token"] = result["token"]
                    logger.info("登录成功，已获取认证token")
                    save_config()
                    return True
                elif result.get("data") and result["data"].get("token"):
                    config["auth_token"] = result["data"]["token"]
                    logger.info("登录成功，已获取认证token")
                    save_config()
                    return True
                else:
                    logger.error("登录响应中未找到token字段")
                    print("登录响应中未找到token字段")
            else:
                error_msg = result.get('error') or result.get('message') or '未知错误'
                logger.error(f"登录失败: {error_msg}")
                print(f"登录失败: {error_msg}")
        else:
            logger.error(f"登录请求失败，状态码: {response.status_code}")
            print(f"登录请求失败，状态码: {response.status_code}")

        return False
    except Exception as e:
        logger.error(f"登录过程中发生错误: {str(e)}")
        print(f"登录过程中发生错误: {str(e)}")
        return False

def get_websocket_port():
    """从服务器获取可用的WebSocket端口"""
    global config

    # 确保已登录并获取token
    if not config.get("auth_token") and not login_to_backend():
        logger.error("未能获取认证token，无法获取WebSocket端口")
        return False

    try:
        # 构建获取端口API URL
        # 检查 backend_url 是否已经包含 /api/trade 路径
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            port_url = f"{base_url}/api/socket/get_port"
        else:
            # 否则直接添加 /api/socket/get_port
            port_url = f"{backend_url}/api/socket/get_port"

        if not port_url.startswith(("http://", "https://")):
            port_url = f"http://{port_url}"

        logger.info(f"尝试获取WebSocket端口: {port_url}")

        # 添加认证头
        headers = {
            "Authorization": f"Bearer {config['auth_token']}",
            "Content-Type": "application/json"
        }

        # 发送请求
        response = requests.post(
            port_url,
            headers=headers,
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            # 打印响应内容以便调试
            logger.info(f"获取WebSocket端口响应: {result}")

            # 检查响应格式，支持两种可能的格式：
            # 1. { success: true, port: 8000 }
            # 2. { success: true, data: { port: 8000 } }
            if result.get("success"):
                # 检查 port 是在根级别还是在 data 对象中
                if result.get("port"):
                    config["ws_port"] = result["port"]
                    logger.info(f"成功获取WebSocket端口: {config['ws_port']}")
                    save_config()
                    return True
                elif result.get("data") and result["data"].get("port"):
                    config["ws_port"] = result["data"]["port"]
                    logger.info(f"成功获取WebSocket端口: {config['ws_port']}")
                    save_config()
                    return True
                else:
                    logger.error("获取WebSocket端口响应中未找到port字段")
                    print("获取WebSocket端口响应中未找到port字段")
            else:
                error_msg = result.get('error') or result.get('message') or '未知错误'
                logger.error(f"获取WebSocket端口失败: {error_msg}")
                print(f"获取WebSocket端口失败: {error_msg}")
        elif response.status_code == 401:
            logger.error("认证失败，token可能已过期")
            # 清除token并尝试重新登录
            config["auth_token"] = ""
            save_config()
            if login_to_backend():
                # 重新尝试获取端口
                return get_websocket_port()
        else:
            logger.error(f"获取WebSocket端口失败，状态码: {response.status_code}")
            print(f"获取WebSocket端口失败，状态码: {response.status_code}")

        return False
    except Exception as e:
        logger.error(f"获取WebSocket端口时发生错误: {str(e)}")
        print(f"获取WebSocket端口时发生错误: {str(e)}")
        return False

def release_websocket_port():
    """释放WebSocket端口"""
    global config

    if not config.get("ws_port"):
        logger.debug("没有WebSocket端口需要释放")
        return True

    # 确保已登录并获取token
    if not config.get("auth_token") and not login_to_backend():
        logger.error("未能获取认证token，无法释放WebSocket端口")
        return False

    try:
        # 构建释放端口API URL
        # 检查 backend_url 是否已经包含 /api/trade 路径
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            release_url = f"{base_url}/api/socket/release_port"
        else:
            # 否则直接添加 /api/socket/release_port
            release_url = f"{backend_url}/api/socket/release_port"

        if not release_url.startswith(("http://", "https://")):
            release_url = f"http://{release_url}"

        logger.info(f"尝试释放WebSocket端口 {config['ws_port']}: {release_url}")

        # 添加认证头
        headers = {
            "Authorization": f"Bearer {config['auth_token']}",
            "Content-Type": "application/json"
        }

        # 发送请求
        response = requests.post(
            release_url,
            json={"port": config["ws_port"]},
            headers=headers,
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                logger.info(f"成功释放WebSocket端口: {config['ws_port']}")
                config["ws_port"] = None
                save_config()
                return True
            else:
                logger.error(f"释放WebSocket端口失败: {result.get('message', '未知错误')}")
        elif response.status_code == 401:
            logger.error("认证失败，token可能已过期")
        else:
            logger.error(f"释放WebSocket端口失败，状态码: {response.status_code}")

        return False
    except Exception as e:
        logger.error(f"释放WebSocket端口时发生错误: {str(e)}")
        return False

def save_config():
    """保存配置到文件"""
    config_path = "easytrader_ths_config.json"
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4)
            logger.info(f"已保存配置到文件: {config_path}")
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {str(e)}")
        return False

def load_config():
    """加载配置文件"""
    global config
    config_path = "easytrader_ths_config.json"

    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
                config.update(loaded_config)
                logger.info(f"已加载配置文件: {config_path}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
    else:
        logger.warning(f"配置文件不存在: {config_path}，将使用默认配置")
        # 保存默认配置
        save_config()

def parse_arguments():
    """解析命令行参数"""
    global config
    parser = argparse.ArgumentParser(description='EasyTrader同花顺客户端')
    parser.add_argument('--username', help='用户名')
    parser.add_argument('--backend-url', help='后端URL')
    parser.add_argument('--local-port', type=int, help='本地端口')
    parser.add_argument('--ths-path', help='同花顺客户端路径')

    args = parser.parse_args()

    # 更新配置
    if args.username:
        config["username"] = args.username
    if args.backend_url:
        config["backend_url"] = args.backend_url
    if args.local_port:
        config["local_port"] = args.local_port
    if args.ths_path:
        config["ths_path"] = args.ths_path

def prompt_for_ths_path():
    """提示用户输入同花顺客户端路径"""
    print("\n===== 同花顺客户端配置 =====")

    if not config.get("ths_path"):
        ths_path = input("请输入同花顺客户端路径: ")
        config["ths_path"] = ths_path.strip()
    else:
        print(f"当前同花顺客户端路径: {config['ths_path']}")
        change = input("是否修改同花顺客户端路径? (y/n): ").lower()
        if change == 'y':
            ths_path = input("请输入新的同花顺客户端路径: ")
            config["ths_path"] = ths_path.strip()

    # 保存配置
    save_config()
    return bool(config["ths_path"])

def prompt_for_local_port():
    """提示用户输入本地端口"""
    print("\n===== 本地服务配置 =====")

    if not config.get("local_port"):
        try:
            port_str = input(f"请输入本地服务端口 (默认: 8888): ")
            if port_str.strip():
                config["local_port"] = int(port_str.strip())
            else:
                config["local_port"] = 8888
        except ValueError:
            print("端口必须是数字，使用默认端口8888")
            config["local_port"] = 8888
    else:
        print(f"当前本地服务端口: {config['local_port']}")
        change = input("是否修改本地服务端口? (y/n): ").lower()
        if change == 'y':
            try:
                port_str = input("请输入新的本地服务端口: ")
                if port_str.strip():
                    config["local_port"] = int(port_str.strip())
            except ValueError:
                print("端口必须是数字，保持原端口不变")

    # 保存配置
    save_config()
    return True

def main():
    """主函数"""
    # 加载配置
    load_config()
    parse_arguments()

    print("\n===== 同花顺交易客户端 =====")
    print("该程序将连接同花顺客户端并与量化平台后端通信")

    # 登录到后端获取token
    if not config.get("auth_token") and not login_to_backend():
        logger.error("登录失败，程序退出")
        return

    # 提示输入同花顺客户端路径
    if not config.get("ths_path") and not prompt_for_ths_path():
        logger.error("未指定同花顺客户端路径，程序退出")
        return

    # 提示输入本地端口
    prompt_for_local_port()

    print("\n===== 初始化交易接口 =====")
    # 初始化交易接口
    if not init_trader():
        logger.error("初始化交易接口失败，程序退出")
        return

    print("\n===== 连接到后端服务器 =====")
    # 获取Socket.IO端口并连接
    if not get_socket_port():
        logger.error("获取Socket.IO端口失败，程序退出")
        print("错误: 获取Socket.IO端口失败，程序退出")
        return

    # 注册到后端
    if not register_to_backend():
        logger.warning("注册到后端失败，将继续运行但可能无法被后端发现")
        print("警告: 注册到后端失败，将继续运行但可能无法被后端发现")
        return

    # 启动心跳线程
    hb_thread = threading.Thread(target=heartbeat_thread, daemon=True)
    hb_thread.start()

    # 启动API服务
    print(f"\n===== 启动本地API服务 =====")
    print(f"本地API服务已启动，监听端口: {config['local_port']}")
    print("现在可以通过量化平台使用同花顺交易功能")
    print("请保持此窗口开启，关闭窗口将断开与后端的连接")

    logger.info(f"启动API服务，监听端口: {config['local_port']}")
    try:
        app.run(host='0.0.0.0', port=config['local_port'])
    except Exception as flask_e:
        logger.error(f"启动 Flask 服务失败: {flask_e}")
        print(f"错误: 启动本地服务失败: {flask_e}")

    # 如果Flask服务器停止，确保Socket.IO也断开连接
    if socket_connected:
        logger.info("断开Socket.IO连接")
        sio.disconnect()

    # 释放Socket.IO端口
    if config.get("socket_port"):
        logger.info(f"释放Socket.IO端口: {config['socket_port']}")
        release_socket_port()

if __name__ == "__main__":
    main()
