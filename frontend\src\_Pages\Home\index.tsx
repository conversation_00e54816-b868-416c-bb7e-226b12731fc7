import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button, Typography, Space, Row, Col } from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import { useAtom } from 'jotai';
import { globalLoadingAtom } from '@/components/GlobalLoading';
import { 
  LineChartOutlined, 
  GlobalOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons';
import { useTheme } from '@/models/useTheme';

const { Title, Paragraph } = Typography;

const Home: React.FC = () => {
  const { theme, isDarkMode } = useTheme();
  const navigate = useNavigate();
  const [, setGlobalLoading] = useAtom(globalLoadingAtom);

  const handleNavigation = (path: string) => {
    setGlobalLoading(true);
    navigate(path);
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
      style={{
        background: isDarkMode ? '#141414' : '#f0f2f5',
        minHeight: '100vh'
      }}
    >
      {/* 顶部导航 */}
      <div style={{ textAlign: 'right', marginBottom: 24 }}>
        <Space>
          <Button type="primary" onClick={() => handleNavigation('/login')}>登录</Button>
          <Button onClick={() => handleNavigation('/register')}>注册</Button>
        </Space>
      </div>

      {/* 主标题区域 */}
      <div style={{ 
        textAlign: 'center', 
        marginBottom: 64,
        color: isDarkMode ? '#fff' : '#000'
      }}>
        <Title style={{ color: isDarkMode ? '#fff' : '#000' }}>
          量化交易策略研究平台
        </Title>
        <Paragraph style={{ 
          fontSize: 16, 
          marginBottom: 32,
          color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)'
        }}>
          提供全方位的策略研究、回测和实盘交易解决方案
        </Paragraph>
        <Button type="primary" size="large">
          <Link to="/login">开始使用</Link>
        </Button>
      </div>

      {/* 三大特性 */}
      <Row gutter={[32, 32]} justify="center" style={{ marginBottom: 64 }}>
        <Col span={8} style={{ textAlign: 'center' }}>
          <LineChartOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Title level={3} style={{ color: isDarkMode ? '#fff' : '#000' }}>智能策略</Title>
          <Paragraph style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)' }}>
            支持多种策略因子研究，包括技术指标、形态识别和基本面分析
          </Paragraph>
        </Col>
        <Col span={8} style={{ textAlign: 'center' }}>
          <GlobalOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Title level={3} style={{ color: isDarkMode ? '#fff' : '#000' }}>专业回测</Title>
          <Paragraph style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)' }}>
            强大的回测引擎，支持多策略组合回测，提供详细的绩效分析
          </Paragraph>
        </Col>
        <Col span={8} style={{ textAlign: 'center' }}>
          <SafetyCertificateOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Title level={3} style={{ color: isDarkMode ? '#fff' : '#000' }}>实盘交易</Title>
          <Paragraph style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)' }}>
            支持多策略并行运行，提供实时监控和风险管理功能
          </Paragraph>
        </Col>
      </Row>

      {/* 为什么选择我们 */}
      <div style={{ 
        background: isDarkMode ? '#1f1f1f' : '#f5f5f5', 
        padding: '48px 0',
        boxShadow: isDarkMode 
          ? '0 2px 8px rgba(0, 0, 0, 0.45)' 
          : '0 2px 8px rgba(0, 0, 0, 0.15)'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px' }}>
          <Title level={2} style={{ 
            textAlign: 'center', 
            marginBottom: 48,
            color: isDarkMode ? '#fff' : '#000'
          }}>
            在这里能玩些什么？
          </Title>
          <Row gutter={[48, 48]} justify="center">
            <Col span={12}>
              <Title level={4} style={{ color: isDarkMode ? '#fff' : '#000' }}>高性能回测引擎</Title>
              <Paragraph style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)' }}>
                支持大规模历史数据回测，快速验证策略有效性
              </Paragraph>
            </Col>
            <Col span={12}>
              <Title level={4} style={{ color: isDarkMode ? '#fff' : '#000' }}>多样化策略研究</Title>
              <Paragraph style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)' }}>
                提供丰富的策略研究工具，支持自定义形态、指标和因子
              </Paragraph>
            </Col>
            <Col span={12}>
              <Title level={4} style={{ color: isDarkMode ? '#fff' : '#000' }}>实时行情数据</Title>
              <Paragraph style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)' }}>
                同时提供A股、港股、期货、数字货币等市场数据
              </Paragraph>
            </Col>
            <Col span={12}>
              <Title level={4} style={{ color: isDarkMode ? '#fff' : '#000' }}>界面优美</Title>
              <Paragraph style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)' }}>
                流畅丝滑的界面，颠覆A股看盘软件呆板古老千年不变的风格
              </Paragraph>
            </Col>
          </Row>
        </div>
      </div>
    </PageContainer>
  );
};

export default Home; 