const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');
const { Group, GroupStrategy, LiveStrategy } = require('../models');

/**
 * 组合配置生成器 - 生成Wonder Trader标准配置
 */
class GroupConfigGenerator {
  constructor() {
    this.commonConfigPath = path.join(process.cwd(), 'live_trading', 'common');
  }

  /**
   * 生成组合的完整配置
   */
  async generateGroupConfig(groupId) {
    try {
      console.log(`[配置生成] 开始生成组合${groupId}的配置...`);

      // 获取组合信息
      const group = await Group.findByPk(groupId, {
        include: [{
          model: GroupStrategy,
          as: 'groupStrategies',
          where: { status: 'active' },
          required: false,
          include: [{
            model: LiveStrategy,
            as: 'strategy'
          }]
        }]
      });

      if (!group) {
        throw new Error(`组合${groupId}不存在`);
      }

      // 生成组合特定的contracts.json（根据策略汇总）
      await this._generateGroupContracts(group);

      // 生成主配置文件
      await this._generateMainConfig(group);

      // 生成其他Wonder Trader配置文件
      await this._generateExecutersConfig(group);
      await this._generateTdParsersConfig(group);
      await this._generateTdTradersConfig(group);
      await this._generateActPolicyConfig(group);
      await this._generateFiltersConfig(group);
      await this._generateLogConfig(group);

      // 生成数据引擎相关配置
      await this._generateDataKitConfig(group);
      await this._generateDataKitLogConfig(group);

      // 生成公共配置文件（如果不存在）
      await this._generateCommonConfigs();

      console.log(`[配置生成] 组合${groupId}配置生成完成`);
      return group.configPath;

    } catch (error) {
      console.error(`[配置生成] 生成组合${groupId}配置失败:`, error);
      throw error;
    }
  }

  /**
   * 生成主配置文件 (config.yaml) - 按照Wonder Trader标准格式
   */
  async _generateMainConfig(group) {
    // 根据策略类型确定使用的配置文件
    const strategyTypes = await this._getGroupStrategyTypes(group);
    const isStockGroup = strategyTypes.includes('STOCK');

    const config = {
      basefiles: {
        commodity: isStockGroup ? '../../../common/stk_comms.json' : '../../../common/commodities.json',
        contract: 'contracts.json',  // 组合特定的品种列表
        holiday: '../../../common/holidays.json',
        session: '../../../common/sessions.json'
      },

      data: {
        store: {
          module: 'WtDataStorageAD',
          path: '../ext_data_engine/storage/'
        }
      },

      env: {
        name: 'cta',
        fees: isStockGroup ? '../../../common/fees_stk.json' : '../../../common/fees.json',
        filters: 'filters.yaml',
        product: {
          session: 'TRADING'
        },
        riskmon: {
          active: true,
          module: 'WtRiskMonFact',
          name: 'SimpleRiskMon',
          base_amount: parseFloat(group.initialCapital || 100000),
          basic_ratio: 101,
          calc_span: 5,
          inner_day_active: true,
          inner_day_fd: group.riskSettings?.stopLossPercent || 20.0,
          multi_day_active: false,
          multi_day_fd: 60.0,
          risk_scale: 0.3,
          risk_span: 30
        }
      },

      executers: 'executers.yaml',
      parsers: 'tdparsers.yaml',
      traders: 'tdtraders.yaml',
      bspolicy: 'actpolicy.yaml'
    };

    // 写入配置文件
    const yamlContent = yaml.dump(config, {
      indent: 2,
      lineWidth: 120,
      noRefs: true
    });

    const configPath = group.configPath;
    await fs.mkdir(path.dirname(configPath), { recursive: true });
    await fs.writeFile(configPath, yamlContent, 'utf8');

    console.log(`[配置生成] 主配置文件已生成: ${configPath}`);
  }

  /**
   * 生成组合特定的contracts.json（根据策略汇总品种）
   */
  async _generateGroupContracts(group) {
    try {
      const allCodes = new Set();

      // 从组合中的所有活跃策略汇总品种列表
      if (group.groupStrategies && group.groupStrategies.length > 0) {
        for (const groupStrategy of group.groupStrategies) {
          const strategy = groupStrategy.strategy;
          if (strategy && strategy.yaml) {
            try {
              const config = yaml.load(strategy.yaml);
              const universe = config.universe || [];
              universe.forEach(code => allCodes.add(code));
              console.log(`[配置生成] 策略${strategy.id}的品种: ${universe.join(', ')}`);
            } catch (error) {
              console.warn(`[配置生成] 解析策略${strategy.id}的YAML失败:`, error);
            }
          }
        }
      }

      // 生成contracts.json
      const contractsList = Array.from(allCodes);
      const contractsPath = path.join(path.dirname(group.configPath), 'contracts.json');

      // 确保目录存在
      await fs.mkdir(path.dirname(contractsPath), { recursive: true });
      await fs.writeFile(contractsPath, JSON.stringify(contractsList, null, 2), 'utf8');

      console.log(`[配置生成] 组合品种列表已生成: ${contractsPath}`);
      console.log(`[配置生成] 包含品种: ${contractsList.join(', ')}`);

    } catch (error) {
      console.error(`[配置生成] 生成组合品种列表失败:`, error);
      throw error;
    }
  }

  /**
   * 生成公共配置文件 - Wonder Trader标准配置
   */
  async _generateCommonConfigs() {
    // 确保公共配置目录存在
    await fs.mkdir(this.commonConfigPath, { recursive: true });

    // 检查公共配置文件是否存在，不存在则生成
    const commonFiles = [
      'commodities.json',
      'stk_comms.json',
      'holidays.json',
      'sessions.json',
      'fees.json',
      'fees_stk.json'
    ];

    for (const fileName of commonFiles) {
      const filePath = path.join(this.commonConfigPath, fileName);
      try {
        await fs.access(filePath);
        console.log(`[配置生成] 公共配置文件已存在: ${fileName}`);
      } catch (error) {
        console.log(`[配置生成] 生成公共配置文件: ${fileName}`);
        await this._generateCommonConfigFile(fileName);
      }
    }
  }

  /**
   * 生成指定的公共配置文件
   */
  async _generateCommonConfigFile(fileName) {
    switch (fileName) {
      case 'commodities.json':
        await this._generateCommoditiesConfig();
        break;
      case 'stk_comms.json':
        await this._generateStockCommoditiesConfig();
        break;
      case 'holidays.json':
        await this._generateHolidaysConfig();
        break;
      case 'sessions.json':
        await this._generateSessionsConfig();
        break;
      case 'fees.json':
        await this._generateFeesConfig();
        break;
      case 'fees_stk.json':
        await this._generateStockFeesConfig();
        break;
      default:
        console.warn(`[配置生成] 未知的公共配置文件: ${fileName}`);
    }
  }

  /**
   * 生成执行器配置 (executers.yaml)
   */
  async _generateExecutersConfig(group) {
    const executersConfig = {
      executers: [
        {
          active: true,
          id: 'exec',
          trader: 'openctp',
          scale: 1,
          policy: {
            default: {
              name: 'WtExeFact.WtMinImpactExeUnit',
              offset: 0,
              expire: 0,
              pricemode: 0,
              pricetick: 1
            }
          }
        }
      ]
    };

    const executersPath = path.join(path.dirname(group.configPath), 'executers.yaml');
    const yamlContent = yaml.dump(executersConfig, { indent: 2 });

    // 确保目录存在
    await fs.mkdir(path.dirname(executersPath), { recursive: true });
    await fs.writeFile(executersPath, yamlContent, 'utf8');

    console.log(`[配置生成] 执行器配置已生成: ${executersPath}`);
  }

  /**
   * 生成行情配置 (tdparsers.yaml)
   */
  async _generateTdParsersConfig(group) {
    const parsersConfig = {
      parsers: [
        {
          active: true,
          broker: '9999',
          code: '',
          front: 'tcp://************:4402',
          id: 'parser',
          module: 'ParserCTP',
          pass: 'test',  // 模拟密码，后续会修改为真实
          user: 'test',  // 模拟用户，后续会修改为真实
          ctpmodule: 'tts_thostmduserapi_se'
        }
      ]
    };

    const parsersPath = path.join(path.dirname(group.configPath), 'tdparsers.yaml');
    const yamlContent = yaml.dump(parsersConfig, { indent: 2 });

    // 确保目录存在
    await fs.mkdir(path.dirname(parsersPath), { recursive: true });
    await fs.writeFile(parsersPath, yamlContent, 'utf8');

    console.log(`[配置生成] 行情配置已生成: ${parsersPath}`);
  }

  /**
   * 生成交易配置 (tdtraders.yaml)
   */
  async _generateTdTradersConfig(group) {
    const tradersConfig = {
      traders: [
        {
          active: true,
          id: 'openctp',
          module: 'TraderCTP',
          savedata: true,
          front: 'tcp://************:4400',
          broker: '9000',
          appid: 'empty',
          authcode: 'empty',
          pass: 'test',  // 模拟密码，后续会修改为真实
          user: 'test',  // 模拟用户，后续会修改为真实
          quick: true,
          ctpmodule: 'tts_thosttraderapi_se'
        }
      ]
    };

    const tradersPath = path.join(path.dirname(group.configPath), 'tdtraders.yaml');
    const yamlContent = yaml.dump(tradersConfig, { indent: 2 });

    // 确保目录存在
    await fs.mkdir(path.dirname(tradersPath), { recursive: true });
    await fs.writeFile(tradersPath, yamlContent, 'utf8');

    console.log(`[配置生成] 交易配置已生成: ${tradersPath}`);
  }

  /**
   * 生成开平策略配置 (actpolicy.yaml)
   */
  async _generateActPolicyConfig(group) {
    const actPolicyConfig = {
      strategies: [
        {
          name: 'default',
          filters: []
        }
      ]
    };

    const actPolicyPath = path.join(path.dirname(group.configPath), 'actpolicy.yaml');
    const yamlContent = yaml.dump(actPolicyConfig, { indent: 2 });

    // 确保目录存在
    await fs.mkdir(path.dirname(actPolicyPath), { recursive: true });
    await fs.writeFile(actPolicyPath, yamlContent, 'utf8');

    console.log(`[配置生成] 开平策略配置已生成: ${actPolicyPath}`);
  }

  /**
   * 生成过滤器配置 (filters.yaml)
   */
  async _generateFiltersConfig(group) {
    const filtersConfig = {
      filters: []
    };

    const filtersPath = path.join(path.dirname(group.configPath), 'filters.yaml');
    const yamlContent = yaml.dump(filtersConfig, { indent: 2 });

    // 确保目录存在
    await fs.mkdir(path.dirname(filtersPath), { recursive: true });
    await fs.writeFile(filtersPath, yamlContent, 'utf8');

    console.log(`[配置生成] 过滤器配置已生成: ${filtersPath}`);
  }

  /**
   * 生成日志配置 (logcfg.yaml)
   */
  async _generateLogConfig(group) {
    const logConfig = {
      dyn_pattern: '%d{%Y.%m.%d %H:%M:%S} [%p] <%t> %m%n',
      level: 'debug',
      sinks: [
        {
          filename: 'Logs/Runner.log',
          pattern: '%d{%Y.%m.%d %H:%M:%S} [%p] <%t> %m%n',
          truncate: false,
          type: 'daily_file_sink'
        },
        {
          pattern: '%^%d{%H:%M:%S} [%p] <%t> %m%n%$',
          type: 'ostream_sink'
        }
      ]
    };

    const logConfigPath = path.join(path.dirname(group.configPath), 'logcfg.yaml');
    const yamlContent = yaml.dump(logConfig, { indent: 2 });

    // 确保目录存在
    await fs.mkdir(path.dirname(logConfigPath), { recursive: true });
    await fs.writeFile(logConfigPath, yamlContent, 'utf8');

    console.log(`[配置生成] 日志配置已生成: ${logConfigPath}`);
  }

  /**
   * 生成合约配置 (contracts.json)
   */
  async _generateContractsConfig() {
    // 基础的期货合约配置
    const contractsConfig = {
      "CFFEX": {
        "IC": {
          "name": "中证500指数期货",
          "exchg": "CFFEX",
          "product": "IC",
          "pricetick": 0.2,
          "volscale": 200,
          "minlots": 1,
          "category": 1
        },
        "IF": {
          "name": "沪深300指数期货", 
          "exchg": "CFFEX",
          "product": "IF",
          "pricetick": 0.2,
          "volscale": 300,
          "minlots": 1,
          "category": 1
        },
        "IH": {
          "name": "上证50指数期货",
          "exchg": "CFFEX", 
          "product": "IH",
          "pricetick": 0.2,
          "volscale": 300,
          "minlots": 1,
          "category": 1
        }
      },
      "SHFE": {
        "ag": {
          "name": "白银期货",
          "exchg": "SHFE",
          "product": "ag",
          "pricetick": 1,
          "volscale": 15,
          "minlots": 1,
          "category": 1
        },
        "au": {
          "name": "黄金期货",
          "exchg": "SHFE", 
          "product": "au",
          "pricetick": 0.02,
          "volscale": 1000,
          "minlots": 1,
          "category": 1
        },
        "cu": {
          "name": "沪铜期货",
          "exchg": "SHFE",
          "product": "cu", 
          "pricetick": 10,
          "volscale": 5,
          "minlots": 1,
          "category": 1
        }
      },
      "DCE": {
        "j": {
          "name": "焦炭期货",
          "exchg": "DCE",
          "product": "j",
          "pricetick": 0.5,
          "volscale": 100,
          "minlots": 1,
          "category": 1
        },
        "eg": {
          "name": "乙二醇期货", 
          "exchg": "DCE",
          "product": "eg",
          "pricetick": 1,
          "volscale": 10,
          "minlots": 1,
          "category": 1
        }
      },
      "CZCE": {
        "CJ": {
          "name": "红枣期货",
          "exchg": "CZCE",
          "product": "CJ",
          "pricetick": 5,
          "volscale": 5,
          "minlots": 1,
          "category": 1
        },
        "SF": {
          "name": "硅铁期货",
          "exchg": "CZCE", 
          "product": "SF",
          "pricetick": 2,
          "volscale": 5,
          "minlots": 1,
          "category": 1
        }
      }
    };

    const contractsPath = path.join(this.commonConfigPath, 'contracts.json');
    await fs.writeFile(contractsPath, JSON.stringify(contractsConfig, null, 2), 'utf8');
    
    console.log(`[配置生成] 合约配置已生成: ${contractsPath}`);
  }

  /**
   * 生成期货品种配置 (commodities.json)
   */
  async _generateCommoditiesConfig() {
    const commoditiesConfig = {
      "CFFEX": {
        "IC": {
          "covermode": 0,
          "pricemode": 0,
          "category": 1,
          "trademode": 0,
          "precision": 1,
          "pricetick": 0.2,
          "volscale": 200,
          "name": "中证500指数期货",
          "exchg": "CFFEX",
          "session": "TRADING",
          "holiday": "CHINA"
        },
        "IF": {
          "covermode": 0,
          "pricemode": 0,
          "category": 1,
          "trademode": 0,
          "precision": 1,
          "pricetick": 0.2,
          "volscale": 300,
          "name": "沪深300指数期货",
          "exchg": "CFFEX",
          "session": "TRADING",
          "holiday": "CHINA"
        },
        "IH": {
          "covermode": 0,
          "pricemode": 0,
          "category": 1,
          "trademode": 0,
          "precision": 1,
          "pricetick": 0.2,
          "volscale": 300,
          "name": "上证50指数期货",
          "exchg": "CFFEX",
          "session": "TRADING",
          "holiday": "CHINA"
        }
      }
    };

    const commoditiesPath = path.join(this.commonConfigPath, 'commodities.json');
    await fs.writeFile(commoditiesPath, JSON.stringify(commoditiesConfig, null, 2), 'utf8');

    console.log(`[配置生成] 期货品种配置已生成: ${commoditiesPath}`);
  }

  /**
   * 生成股票品种配置 (stk_comms.json)
   */
  async _generateStockCommoditiesConfig() {
    const stkCommsConfig = {
      "SSE": {
        "STK": {
          "category": 0,
          "covermode": 0,
          "exchg": "SSE",
          "holiday": "CHINA",
          "name": "上证股票",
          "precision": 2,
          "pricemode": 1,
          "pricetick": 0.01,
          "session": "TRADING",
          "volscale": 1,
          "trademode": 2
        }
      },
      "SZSE": {
        "STK": {
          "category": 0,
          "covermode": 0,
          "exchg": "SZSE",
          "holiday": "CHINA",
          "name": "深证股票",
          "precision": 2,
          "pricemode": 1,
          "pricetick": 0.01,
          "session": "TRADING",
          "volscale": 1,
          "trademode": 2
        }
      }
    };

    const stkCommsPath = path.join(this.commonConfigPath, 'stk_comms.json');
    await fs.writeFile(stkCommsPath, JSON.stringify(stkCommsConfig, null, 2), 'utf8');

    console.log(`[配置生成] 股票品种配置已生成: ${stkCommsPath}`);
  }

  /**
   * 生成节假日配置 (holidays.json)
   */
  async _generateHolidaysConfig() {
    const holidaysConfig = {
      "CHINA": [
        20240101, 20240210, 20240211, 20240212, 20240213, 20240214, 20240215, 20240216, 20240217,
        20240404, 20240405, 20240406, 20240501, 20240502, 20240503, 20240610, 20240917, 20241001,
        20241002, 20241003, 20241004, 20241007, 20241008
      ]
    };

    const holidaysPath = path.join(this.commonConfigPath, 'holidays.json');
    await fs.writeFile(holidaysPath, JSON.stringify(holidaysConfig, null, 2), 'utf8');

    console.log(`[配置生成] 节假日配置已生成: ${holidaysPath}`);
  }

  /**
   * 生成交易时段配置 (sessions.json)
   */
  async _generateSessionsConfig() {
    const sessionsConfig = {
      "TRADING": [
        {
          "from": 900,
          "to": 1015
        },
        {
          "from": 1030,
          "to": 1130
        },
        {
          "from": 1300,
          "to": 1500
        }
      ]
    };

    const sessionsPath = path.join(this.commonConfigPath, 'sessions.json');
    await fs.writeFile(sessionsPath, JSON.stringify(sessionsConfig, null, 2), 'utf8');

    console.log(`[配置生成] 交易时段配置已生成: ${sessionsPath}`);
  }

  /**
   * 生成费用配置 (fees.json)
   */
  async _generateFeesConfig() {
    const feesConfig = {
      "CFFEX": {
        "IC": {
          "open": 0.000023,
          "close": 0.000023,
          "closetoday": 0.000023
        },
        "IF": {
          "open": 0.000023,
          "close": 0.000023,
          "closetoday": 0.000023
        },
        "IH": {
          "open": 0.000023,
          "close": 0.000023,
          "closetoday": 0.000023
        }
      }
    };

    const feesPath = path.join(this.commonConfigPath, 'fees.json');
    await fs.writeFile(feesPath, JSON.stringify(feesConfig, null, 2), 'utf8');

    console.log(`[配置生成] 期货费用配置已生成: ${feesPath}`);
  }

  /**
   * 生成股票费用配置 (fees_stk.json)
   */
  async _generateStockFeesConfig() {
    const stkFeesConfig = {
      "SSE": {
        "STK": {
          "open": 0.0003,
          "close": 0.0013,
          "closetoday": 0.0013
        }
      },
      "SZSE": {
        "STK": {
          "open": 0.0003,
          "close": 0.0013,
          "closetoday": 0.0013
        }
      }
    };

    const stkFeesPath = path.join(this.commonConfigPath, 'fees_stk.json');
    await fs.writeFile(stkFeesPath, JSON.stringify(stkFeesConfig, null, 2), 'utf8');

    console.log(`[配置生成] 股票费用配置已生成: ${stkFeesPath}`);
  }

  /**
   * 获取组合中策略的类型
   */
  async _getGroupStrategyTypes(group) {
    const types = new Set();

    if (group.groupStrategies && group.groupStrategies.length > 0) {
      for (const groupStrategy of group.groupStrategies) {
        const strategy = groupStrategy.strategy;
        if (strategy && strategy.yaml) {
          try {
            const config = yaml.load(strategy.yaml);
            const tradingType = config.trading_type || 'FUTURES';
            types.add(tradingType.toUpperCase());
          } catch (error) {
            console.warn(`[配置生成] 解析策略${strategy.id}的YAML失败:`, error);
            types.add('FUTURES'); // 默认为期货
          }
        }
      }
    }

    return Array.from(types);
  }

  /**
   * 解析策略YAML配置
   */
  _parseStrategyConfig(yamlContent) {
    try {
      if (!yamlContent) {
        return {};
      }

      // 如果是字符串，解析YAML
      if (typeof yamlContent === 'string') {
        return yaml.load(yamlContent) || {};
      }

      // 如果已经是对象，直接返回
      return yamlContent;

    } catch (error) {
      console.warn(`[配置生成] 解析策略配置失败: ${error.message}`);
      return {};
    }
  }

  /**
   * 更新OpenCTP用户密码（真实环境使用）
   */
  async updateOpenCtpCredentials(username, password) {
    try {
      console.log(`[配置生成] 更新OpenCTP凭据...`);
      
      // 更新parsers.yaml
      const parsersPath = path.join(this.commonConfigPath, 'parsers.yaml');
      const parsersContent = await fs.readFile(parsersPath, 'utf8');
      const parsersConfig = yaml.load(parsersContent);
      
      if (parsersConfig.parsers && parsersConfig.parsers[0]) {
        parsersConfig.parsers[0].user = username;
        parsersConfig.parsers[0].pass = password;
      }
      
      await fs.writeFile(parsersPath, yaml.dump(parsersConfig, { indent: 2 }), 'utf8');
      
      // 更新traders.yaml
      const tradersPath = path.join(this.commonConfigPath, 'traders.yaml');
      const tradersContent = await fs.readFile(tradersPath, 'utf8');
      const tradersConfig = yaml.load(tradersContent);
      
      if (tradersConfig.traders && tradersConfig.traders[0]) {
        tradersConfig.traders[0].user = username;
        tradersConfig.traders[0].pass = password;
      }
      
      await fs.writeFile(tradersPath, yaml.dump(tradersConfig, { indent: 2 }), 'utf8');
      
      console.log(`[配置生成] OpenCTP凭据更新完成`);
      
    } catch (error) {
      console.error(`[配置生成] 更新OpenCTP凭据失败:`, error);
      throw error;
    }
  }

  /**
   * 生成数据引擎配置 (dtcfg.yaml)
   */
  async _generateDataKitConfig(group) {
    const datakitConfig = {
      parsers: [{
        active: true,
        id: 'parser',
        module: 'ParserUDP',
        host: '127.0.0.1',
        port: '9001',
        protocol: 1,
        buffsize: 128,
        clientid: 1,
        hbinterval: 15
      }],
      writer: {
        module: 'WtDtStorage',
        async: true,
        groupsize: 20,
        path: '../data_storage',
        savelog: false
      },
      broadcaster: {
        active: true,
        bport: 3997,
        broadcast: [{
          host: '127.0.0.1',
          port: 9001,
          type: 2
        }]
      }
    };

    const datakitPath = path.join(path.dirname(group.configPath), 'dtcfg.yaml');
    const yamlContent = yaml.dump(datakitConfig, { indent: 2 });

    await fs.mkdir(path.dirname(datakitPath), { recursive: true });
    await fs.writeFile(datakitPath, yamlContent, 'utf8');

    console.log(`[配置生成] 数据引擎配置已生成: ${datakitPath}`);
  }

  /**
   * 生成数据引擎日志配置 (logcfgdt.yaml)
   */
  async _generateDataKitLogConfig(group) {
    const logConfig = {
      dyn_pattern: '%d{%Y.%m.%d %H:%M:%S} [%p] <%t> %m%n',
      level: 'debug',
      sinks: [
        {
          filename: 'DtLogs/DataKit.log',
          pattern: '%d{%Y.%m.%d %H:%M:%S} [%p] <%t> %m%n',
          truncate: false,
          type: 'daily_file_sink'
        },
        {
          pattern: '%^%d{%H:%M:%S} [%p] <%t> %m%n%$',
          type: 'ostream_sink'
        }
      ]
    };

    const logConfigPath = path.join(path.dirname(group.configPath), 'logcfgdt.yaml');
    const yamlContent = yaml.dump(logConfig, { indent: 2 });

    await fs.mkdir(path.dirname(logConfigPath), { recursive: true });
    await fs.writeFile(logConfigPath, yamlContent, 'utf8');

    console.log(`[配置生成] 数据引擎日志配置已生成: ${logConfigPath}`);
  }
}

module.exports = GroupConfigGenerator;
