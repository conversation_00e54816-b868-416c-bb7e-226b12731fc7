import { Symbol, MarketType } from '@/shared_types/market';
import { marketService } from './marketService';
import pinyin from 'pinyin';

/**
 * 品种缓存服务
 * 负责前端品种数据的缓存、搜索和管理
 */
class SymbolCacheService {
  private cache: Map<string, Symbol[]> = new Map();
  private localStorageKey = 'symbols_cache';
  private cacheExpireKey = 'symbols_cache_expire';
  private cacheExpireTime = 24 * 60 * 60 * 1000; // 24小时缓存

  constructor() {
    this.initCache();
  }

  /**
   * 获取中文的拼音首字母
   * @param text 中文文本
   * @returns 拼音首字母字符串
   */
  private getPinyinInitials(text: string): string {
    try {
      // 使用pinyin库获取拼音，只取首字母
      const pinyinArray = pinyin(text, {
        style: 'first_letter', // 只取首字母
        heteronym: false // 不使用多音字
      });
      
      // 将拼音数组转换为字符串
      return pinyinArray.flat().join('').toLowerCase();
    } catch (error) {
      console.error('[品种缓存] 拼音转换失败:', error);
      return '';
    }
  }

  /**
   * 检查文本是否匹配拼音首字母
   * @param text 要检查的中文文本
   * @param keyword 搜索关键词
   * @returns 是否匹配
   */
  private isPinyinMatch(text: string, keyword: string): boolean {
    const pinyinInitials = this.getPinyinInitials(text);
    return pinyinInitials.includes(keyword.toLowerCase());
  }

  /**
   * 初始化缓存
   */
  private async initCache() {
    try {
      // 尝试从localStorage恢复缓存
      const cachedData = this.getFromLocalStorage();
      if (cachedData && this.isCacheValid()) {
        console.log('[品种缓存] 从本地存储恢复缓存数据');
        this.cache.set('all', cachedData);
        return;
      }

      // 缓存无效或不存在，从服务器加载
      console.log('[品种缓存] 缓存无效，从服务器加载数据');
      await this.loadAllSymbols();
    } catch (error) {
      console.error('[品种缓存] 初始化缓存失败:', error);
    }
  }

  /**
   * 从localStorage获取缓存数据
   */
  private getFromLocalStorage(): Symbol[] | null {
    try {
      const data = localStorage.getItem(this.localStorageKey);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('[品种缓存] 读取本地存储失败:', error);
      return null;
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    try {
      const expireTime = localStorage.getItem(this.cacheExpireKey);
      if (!expireTime) return false;
      
      const now = Date.now();
      const expire = parseInt(expireTime);
      return now < expire;
    } catch (error) {
      console.error('[品种缓存] 检查缓存有效性失败:', error);
      return false;
    }
  }

  /**
   * 保存缓存到localStorage
   */
  private saveToLocalStorage(symbols: Symbol[]) {
    try {
      localStorage.setItem(this.localStorageKey, JSON.stringify(symbols));
      localStorage.setItem(this.cacheExpireKey, (Date.now() + this.cacheExpireTime).toString());
      console.log('[品种缓存] 缓存数据已保存到本地存储');
    } catch (error) {
      console.error('[品种缓存] 保存到本地存储失败:', error);
    }
  }

  /**
   * 加载所有品种数据
   */
  async loadAllSymbols(): Promise<void> {
    try {
      console.log('[品种缓存] 开始加载所有品种数据');
      
      // 从服务器获取所有品种数据
      const symbols = await marketService.getSymbols('');
      
      if (symbols && symbols.length > 0) {
        // 保存到内存缓存
        this.cache.set('all', symbols);
        
        // 保存到localStorage
        this.saveToLocalStorage(symbols);
        
        console.log(`[品种缓存] 成功加载 ${symbols.length} 个品种数据`);
      } else {
        console.warn('[品种缓存] 未获取到品种数据');
      }
    } catch (error) {
      console.error('[品种缓存] 加载品种数据失败:', error);
      throw error;
    }
  }

  /**
   * 搜索品种
   * @param keyword 搜索关键词
   * @returns 匹配的品种列表
   */
  searchSymbols(keyword: string): Symbol[] {
    if (!keyword || keyword.trim() === '') {
      return [];
    }

    const allSymbols = this.cache.get('all') || [];
    const trimmedKeyword = keyword.trim().toLowerCase();

    // 检查搜索关键词是否全是数字
    const isNumericSearch = /^\d+$/.test(trimmedKeyword);

    let results: Symbol[] = [];

    if (isNumericSearch) {
      // 纯数字搜索，只匹配代码
      results = allSymbols.filter(symbol => 
        symbol.code.toLowerCase().includes(trimmedKeyword)
      );
    } else {
      // 非纯数字搜索，匹配代码、名称和拼音首字母
      results = allSymbols.filter(symbol => {
        const codeMatch = symbol.code.toLowerCase().includes(trimmedKeyword);
        const nameMatch = symbol.name.toLowerCase().includes(trimmedKeyword);
        const pinyinMatch = this.isPinyinMatch(symbol.name, trimmedKeyword);
        
        return codeMatch || nameMatch || pinyinMatch;
      });
    }

    // 按匹配度排序：代码开头匹配 > 名称开头匹配 > 拼音首字母匹配 > 包含匹配
    results.sort((a, b) => {
      const aCodeStartsWith = a.code.toLowerCase().startsWith(trimmedKeyword);
      const bCodeStartsWith = b.code.toLowerCase().startsWith(trimmedKeyword);
      const aNameStartsWith = a.name.toLowerCase().startsWith(trimmedKeyword);
      const bNameStartsWith = b.name.toLowerCase().startsWith(trimmedKeyword);
      const aPinyinMatch = this.isPinyinMatch(a.name, trimmedKeyword);
      const bPinyinMatch = this.isPinyinMatch(b.name, trimmedKeyword);

      // 代码开头匹配优先级最高
      if (aCodeStartsWith && !bCodeStartsWith) return -1;
      if (!aCodeStartsWith && bCodeStartsWith) return 1;
      
      // 名称开头匹配次之
      if (aNameStartsWith && !bNameStartsWith) return -1;
      if (!aNameStartsWith && bNameStartsWith) return 1;
      
      // 拼音首字母匹配再次之
      if (aPinyinMatch && !bPinyinMatch) return -1;
      if (!aPinyinMatch && bPinyinMatch) return 1;
      
      return 0;
    });

    // 限制返回数量
    return results.slice(0, 20);
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(): { hasData: boolean; count: number; isValid: boolean } {
    const symbols = this.cache.get('all') || [];
    return {
      hasData: symbols.length > 0,
      count: symbols.length,
      isValid: this.isCacheValid()
    };
  }

  /**
   * 强制刷新缓存
   */
  async refreshCache(): Promise<void> {
    console.log('[品种缓存] 强制刷新缓存');
    this.cache.clear();
    localStorage.removeItem(this.localStorageKey);
    localStorage.removeItem(this.cacheExpireKey);
    await this.loadAllSymbols();
  }

  /**
   * 获取所有品种数据
   */
  getAllSymbols(): Symbol[] {
    return this.cache.get('all') || [];
  }

  /**
   * 测试拼音功能
   * @param text 要测试的中文文本
   * @returns 拼音首字母
   */
  testPinyin(text: string): string {
    return this.getPinyinInitials(text);
  }

  /**
   * 详细测试拼音功能
   * @param text 要测试的中文文本
   * @returns 测试结果
   */
  debugPinyin(text: string): { original: string; pinyin: string; initials: string } {
    try {
      const pinyinArray = pinyin(text, {
        style: 'first_letter',
        heteronym: false
      });
      
      const fullPinyin = pinyin(text, {
        style: 'normal',
        heteronym: false
      });
      
      return {
        original: text,
        pinyin: fullPinyin.flat().join(''),
        initials: pinyinArray.flat().join('')
      };
    } catch (error) {
      console.error('[品种缓存] 拼音调试失败:', error);
      return {
        original: text,
        pinyin: '',
        initials: ''
      };
    }
  }
}

// 创建单例实例
export const symbolCacheService = new SymbolCacheService(); 