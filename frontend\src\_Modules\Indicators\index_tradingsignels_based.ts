import { KLineData, IndicatorType } from '@/shared_types/market';
import { 
  EMA as TradingEMA, 
  SMA as TradingSMA, 
  WMA as TradingWMA, 
  RSI as TradingRSI, 
  BollingerBands,
  MACD as TradingMACD,
  StochasticRSI,
  StochasticOscillator
} from 'trading-signals';

// 将K线数据转换为指标库需要的格式
const convertKLineData = (data: KLineData[]) => {
  return data.map(item => ({
    time: item.time,
    open: item.open,
    high: item.high,
    low: item.low,
    close: item.close,
    volume: item.volume
  }));
};

// 辅助函数：统一处理指标计算
const calculateIndicator = (calculator: any, lastTime: number, time: number, values: number | number[]) => {
  const args = Array.isArray(values) ? values : [values];
  
  if (lastTime === 0 || time > lastTime) {
    calculator.add(...args);
  } else if (time === lastTime) {
    calculator.replace(...args);
  }
  
  const result = calculator.getResult();
  //console.log('calculator period:', calculator.period, 'isReady:', calculator.isReady, 'result:', result);
  return result;
};

// MACD指标
export const MACD = {
  type: IndicatorType.MACD,
  defaultParams: {
    fast: 12,    // 快线周期
    slow: 26,    // 慢线周期
    signal: 9    // 信号线周期
  },
  createCalculator: (params: Record<string, number>) => {
    const { fast = 12, slow = 26, signal = 9 } = params;
    return {
      calculator: new TradingMACD({
        indicator: TradingEMA,    // 使用 EMA 作为基础指标
        shortInterval: fast,      // 快线周期
        longInterval: slow,       // 慢线周期
        signalInterval: signal    // 信号线周期
      }),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData) => {
    const result = calculateIndicator(calculator.calculator, calculator.lastTime, kline.time, kline.close);
    calculator.lastTime = kline.time;
    
    if (result === undefined || !result) {
      return {
        dif: undefined,
        dea: undefined,
        macd: undefined
      };
    }

    return {
      dif: Number(result.macd),
      dea: Number(result.signal),
      macd: Number(result.histogram)
    };
  }
};

// RSI指标
export const RSI = {
  type: IndicatorType.RSI,
  defaultParams: {
    period: 14   // RSI计算周期
  },
  createCalculator: (params: Record<string, number>) => {
    const { period = 14 } = params;
    return {
      calculator: new TradingRSI(period),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData) => {
    const result = calculateIndicator(calculator.calculator, calculator.lastTime, kline.time, kline.close);
    calculator.lastTime = kline.time;
    return { rsi: result ? Number(result) : undefined };
  }
};

// MA指标
export const MA = {
  type: IndicatorType.MA,
  defaultParams: {
    period: 20    // 计算周期
  },
  createCalculator: (params: Record<string, number>) => {
    const { period = 20 } = params;
    return {
      calculator: new TradingSMA(period),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData) => {
    const result = calculateIndicator(calculator.calculator, calculator.lastTime, kline.time, kline.close);
    //console.log('result', result);
    calculator.lastTime = kline.time;
    return { ma: result ? Number(result) : undefined };
  }
};

// EMA指标
export const EMA = {
  type: IndicatorType.EMA,
  defaultParams: {
    period: 20    // 计算周期
  },
  createCalculator: (params: Record<string, number>) => {
    const { period = 20 } = params;
    return {
      calculator: new TradingEMA(period),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData) => {
    const result = calculateIndicator(calculator.calculator, calculator.lastTime, kline.time, kline.close);
    calculator.lastTime = kline.time;
    return { ema: result ? Number(result) : undefined };
  }
};

// BOLL指标
export const BOLL = {
  type: IndicatorType.BOLL,
  defaultParams: {
    period: 20,   // 计算周期
    band: 2       // 带宽
  },
  createCalculator: (params: Record<string, number>) => {
    const { period = 20, band = 2 } = params;
    return {
      calculator: new BollingerBands(period, band),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData) => {
    const result = calculateIndicator(calculator.calculator, calculator.lastTime, kline.time, kline.close);
    calculator.lastTime = kline.time;
    
    return {
      middle: Number(result.middle),
      upper: Number(result.upper),
      lower: Number(result.lower)
    };
  }
};

// VOL指标
export const VOL = {
  type: IndicatorType.VOL,
  defaultParams: {},
  createCalculator: () => ({}),
  calculate: (calculator: any, kline: KLineData) => {
    return { volume: kline.volume };
  }
};

// KDJ指标
export const KDJ = {
  type: IndicatorType.KDJ,
  defaultParams: {
    period: 9,        // N - RSV计算周期
    kPeriod: 3,       // M - K值平滑周期
    dPeriod: 3,       // P - D值平滑周期
  },
  createCalculator: (params: Record<string, number>) => {
    const { 
      period = 9,     // N - 默认9周期
      kPeriod = 3,    // M - 默认3周期
      dPeriod = 3     // P - 默认3周期
    } = params;
    return {
      calculator: new StochasticOscillator(period, kPeriod, dPeriod),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData) => {
    const result = calculateIndicator(
      calculator.calculator, 
      kline.time, 
      calculator.lastTime, 
      [kline.high, kline.low, kline.close]
    );
    calculator.lastTime = kline.time;

    if (result === undefined || !result) {
      return {
        k: undefined,
        d: undefined,
        j: undefined
      };
    }

    const k = Number(result.k);
    const d = Number(result.d);
    const j = 3 * k - 2 * d;
    
    return { k, d, j };
  }
};

// SWMA指标
export const SWMA = {
  type: IndicatorType.SWMA,
  defaultParams: {
    wmaLength: 30,  // WMA周期
    emaLength: 20   // EMA周期
  },
  createCalculator: (params: Record<string, number>) => {
    const { wmaLength = 30, emaLength = 20 } = params;
    return {
      
      wma: new TradingWMA(wmaLength),
      ema: new TradingEMA(emaLength),
      
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData) => {
    const wmaResult = calculateIndicator(calculator.wma, calculator.lastTime, kline.time, kline.close);
    const wmaValue = wmaResult ? Number(wmaResult) : undefined;
    if (wmaValue === undefined) {
      return { swma: undefined };
    }

    const swmaResult = calculateIndicator(calculator.ema, calculator.lastTime, kline.time, wmaValue);
    const swmaValue = swmaResult ? Number(swmaResult) : undefined;
    
    calculator.lastTime = kline.time;
    return { swma: swmaValue };
  }
};

// WMA指标
export const WMA = {
  type: IndicatorType.WMA,
  defaultParams: {
    period: 20    // 计算周期
  },
  createCalculator: (params: Record<string, number>) => {
    const { period = 20 } = params;
    return {
      calculator: new TradingWMA(period),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData) => {
    const result = calculateIndicator(calculator.calculator, calculator.lastTime, kline.time, kline.close);
    calculator.lastTime = kline.time;
    return { wma: result ? Number(result) : undefined };
  }
};