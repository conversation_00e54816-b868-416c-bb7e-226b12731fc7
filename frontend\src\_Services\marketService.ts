import axios from 'axios';
import { EventBus } from '../events/eventBus';
import {
  KLineData,
  KLineInterval,
  Symbol,
  MarketType,
  LocationResult,
  ExchangeType,
  GeneCombination,
  GeneType
} from '@/shared_types/market';
import { Indicator } from '@/shared_types/indicator';
import { MarketEvents } from '../events/events';
import { getToken } from '@/utils/auth';
import { SignalConfig } from '@/shared_types/market';
import marketDispatcher from '@/_Dispatchers/marketDispatcher';

class MarketService {
  // 获取交易品种列表
  async getSymbols(search: string): Promise<Symbol[]> {
    try {

      console.log('MARKETSERVICE>>>: getSymbols search', search);

      const token = getToken();
      const response = await axios.get('/api/market/symbols', {
        params: { 
          search
        },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.error || 'Failed to fetch symbols');
    } catch (error) {
      console.error('获取交易品种列表失败:', error);
      throw error;
    }
  }

  // 搜索交易品种
  async searchSymbols(keyword: string, market: MarketType = MarketType.STOCK): Promise<Symbol[]> {
    return this.getSymbols(keyword);
  }

  // 获取指标列表
  async getIndicators(): Promise<Indicator[]> {
    try {
      const token = getToken();
      const response = await axios.get('/api/market/indicators', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.error || 'Failed to fetch indicators');
    } catch (error) {
      console.error('获取指标列表失败:', error);
      throw error;
    }
  }

  // 保存基因组合
  async saveGeneCombination(gene: GeneCombination): Promise<void> {
    try {
      const token = getToken();
      const response = await axios.post('/api/market/genes', gene, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to save gene combination');
      }
    } catch (error) {
      console.error('保存基因组合失败:', error);
      throw error;
    }
  }

  // 获取基因组合列表
  async getGeneCombinations(): Promise<GeneCombination[]> {
    try {
      const token = getToken();
      const response = await axios.get('/api/market/genes', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.error || 'Failed to fetch gene combinations');
    } catch (error) {
      console.error('获取基因组合列表失败:', error);
      throw error;
    }
  }

  // 删除基因组合
  async deleteGeneCombination(id: string): Promise<void> {
    try {
      const token = getToken();
      const response = await axios.delete(`/api/market/genes/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to delete gene combination');
      }
    } catch (error) {
      console.error('删除基因组合失败:', error);
      throw error;
    }
  }

  // 定位基因组合
  async locateGeneCombination(
    code: string, 
    geneId: string, 
    startTime: number, 
    endTime: number
  ): Promise<LocationResult[]> {
    try {
      const token = getToken();
      const response = await axios.get('/api/market/genes/locate', {
        params: { code, geneId, startTime, endTime },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.error || 'Failed to locate gene combination');
    } catch (error) {
      console.error('定位基因组合失败:', error);
      throw error;
    }
  }

  /**
   * 计算历史信号
   * @param symbol 交易品种
   * @param signalConfig 信号配置
   * @returns 历史信号列表
   */
  async calculateHistoricalSignals(symbol: Symbol, signalConfig: SignalConfig): Promise<MarketEvents.SignalItem[]> {
    try {
      // 先设置监听器
      const resultPromise = new Promise<MarketEvents.SignalItem[]>((resolve, reject) => {
        const unsubscribe = marketDispatcher.onSignalsResult((payload) => {
          unsubscribe.unsubscribe();
          resolve(payload.signals);
        });

        const errorUnsubscribe = marketDispatcher.onSignalsError((payload) => {
          errorUnsubscribe.unsubscribe();
          reject(new Error(payload.message));
        });

        // 30秒超时
        setTimeout(() => {
          unsubscribe.unsubscribe();
          errorUnsubscribe.unsubscribe();
          reject(new Error('Calculate signals timeout'));
        }, 30000);
      });

      // 然后发送计算历史信号请求
      EventBus.emit(MarketEvents.Types.CALCULATE_SIGNALS, {
        symbol,
        signalConfig
      });

      // 等待信号计算结果
      return resultPromise;
    } catch (error) {
      console.error('Calculate historical signals failed:', error);
      throw error;
    }
  }
}

export const marketService = new MarketService(); 