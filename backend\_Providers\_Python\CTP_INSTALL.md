# CTP行情获取安装说明

## 安装CTP库

### 方法1: 安装 openctp_ctp (推荐)
```bash
pip install openctp_ctp
```

### 方法2: 安装 py_ctp
```bash
pip install py_ctp
```

## 运行测试

```bash
cd backend/_Providers/_Python
python testpyctp.py
```

## 获取SimNow测试账号

1. 访问 SimNow 官网: http://www.simnow.com.cn/
2. 注册7x24小时测试账号
3. 获得用户名和密码

## 常用期货合约代码

- rb2409: 螺纹钢2024年9月
- ag2409: 沪银2024年9月  
- au2409: 沪金2024年9月
- IF2409: 沪深300股指期货
- IC2409: 中证500股指期货

## 注意事项

1. CTP主要用于期货和期权，不支持股票行情
2. 需要有效的期货账号才能获取实时行情
3. SimNow提供免费的模拟环境用于测试
