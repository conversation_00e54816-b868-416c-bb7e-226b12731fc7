# 通达信数据解析

## 期货品种数据获取逻辑

通达信期货数据主要来源于两个文件：`code2name.ini`（普通期货合约）和 `code2qhidx.ini`（主连期货合约）。下面详细说明解析这两个文件的逻辑。

### 1. 交易所代码映射表

首先需要建立一个映射表，将通达信使用的交易所代码转换为标准交易所代码：

```python
exchange_map = {
    # 数字代码映射
    '47': 'CFFEX',  # 中金所
    '28': 'CZCE',   # 郑州商品
    '29': 'DCE',    # 大连商品
    '30': 'SHFE',   # 上海商品
    '66': 'GFE',    # 广州期货
    # 简称映射
    'CZ': 'CFFEX',  # 中金所
    'QZ': 'CZCE',   # 郑州商品
    'QD': 'DCE',    # 大连商品
    'QS': 'SHFE',   # 上海商品
    'GZ': 'GFE',    # 广州期货
    'QG': 'GFE',    # 广州期货 - 需要添加这个
    # 其他可能的简称
    'SHFE': 'SHFE',
    'DCE': 'DCE',
    'CZCE': 'CZCE',
    'CFFEX': 'CFFEX',
    'GFE': 'GFE'
}
```

### 2. 解析 code2name.ini（普通期货品种）

这个文件包含所有普通期货合约的基本信息，每行表示一个品种，字段用逗号分隔。

#### 文件格式
```
代码,名称,交易所代码,...其他字段
```

例如：
```
AF,欧美,CZ,2,,,100,0.0100,3.00,3.00,1.0000,元,万元,107,3,0,...
```

#### 解析步骤
1. 逐行读取文件
2. 分割每行数据（按逗号）
3. 提取前三个字段：代码、名称和交易所代码
4. 利用映射表将通达信交易所代码转换为标准交易所代码
5. 构建标准格式的期货对象

```python
futures = []
ds_file_path = os.path.join(tdx_symbols_path, 'code2name.ini')
with open(ds_file_path, 'r', encoding='gbk', errors='ignore') as f:
    for line in f:
        try:
            # 分割CSV行
            parts = line.strip().split(',')
            if len(parts) >= 3:
                code = parts[0].strip()
                name = parts[1].strip()
                exchange_code = parts[2].strip()
                
                # 转换交易所代码
                exchange = exchange_map.get(exchange_code, exchange_code)
                
                if code and name:
                    futures.append({
                        'code': code,
                        'name': name,
                        'market': 'FUTURE',
                        'exchange': exchange
                    })
        except Exception as e:
            print(f"Error parsing line in futures file: {str(e)}")
            continue
```

### 3. 解析 code2qhidx.ini（主连期货品种）

这个文件包含主连期货合约信息，使用 INI 文件格式，重点解析 `[FL]` 部分。

#### 文件结构
```ini
[FL]
#市场：郑州QZ，28/大连QD，29/上海QS，30/中金CZ，47/广期，66
Num=10
Name1=金融期货,10
FL1=47_IFL8,47_ICL8,...

Name2=贵金属,2
FL2=30_AUL8,30_AGL8
...
```

#### 解析步骤
1. 使用 configparser 读取 INI 文件
2. 从 `[FL]` 部分获取分类数量 `Num`
3. 遍历每个分类，读取 `Name{i}` 和 `FL{i}` 的值
4. 对每个 `FL{i}` 中的代码进行解析（格式为 `prefix_code`）
5. 提取交易所前缀和品种代码，去除数字后缀
6. 使用映射表将交易所前缀转换为标准交易所代码
7. 构建主连合约对象，添加 `is_main: true` 标记

```python
main_futures = []
qhidx_file_path = os.path.join(tdx_symbols_path, 'code2qhidx.ini')

# 使用配置解析器读取INI文件
import configparser
config = configparser.ConfigParser()
config.read(qhidx_file_path, encoding='gbk', errors='ignore')

# 检查是否有FL部分
if 'FL' in config:
    # 获取分类数量
    category_qty = int(config['FL'].get('Num', '0'))
    
    for i in range(1, category_qty + 1):
        # 读取每个分类
        name_key = f'Name{i}'
        fl_key = f'FL{i}'
        
        if name_key in config['FL'] and fl_key in config['FL']:
            # 获取分类信息
            name_info = config['FL'][name_key]
            name_parts = name_info.split(',')
            if len(name_parts) >= 1:
                category_name = name_parts[0].strip()
            
            # 获取期货代码列表
            fl_codes = config['FL'][fl_key].split(',')
            
            for code_str in fl_codes:
                try:
                    # 解析代码，格式: prefix_code
                    if '_' in code_str:
                        prefix, code = code_str.split('_')
                        # 去掉可能的合约月份后缀，只保留品种代码部分
                        code = ''.join([c for c in code if not c.isdigit()])
                        
                        # 获取交易所代码
                        exchange = exchange_map.get(prefix, 'UNKNOWN')
                        
                        # 构建主连合约记录
                        main_futures.append({
                            'code': f"{code}0",  # 主连合约通常用0表示
                            'name': f"{code}主连",
                            'market': 'FUTURE',
                            'exchange': exchange,
                            'is_main': True
                        })
                except Exception as e:
                    print(f"解析主连期货代码出错: {code_str} - {str(e)}")
                    continue
```

### 4. 合并数据并返回

最后，将普通期货和主连期货列表合并，返回完整的期货品种数据：

```python
all_futures = futures + main_futures
return jsonify({
    'success': True,
    'data': all_futures
})
```

### 关键注意事项

1. 必须添加 `'QG': 'GFE'` 到映射表以正确处理广州期货交易所的品种
2. code2qhidx.ini 中的主连代码格式是 `prefix_codeXX`，需要去除数字部分
3. 主连合约的代码约定是在品种代码后加 "0"（如 `IF0`）
4. 主连合约的名称约定是品种代码后加 "主连"（如 `IF主连`）
5. 所有解析过程中都需要做好错误处理，确保能处理格式异常的数据

## 期货数据文件结构说明

### code2name.ini

包含所有普通期货合约的基本信息，格式为逗号分隔值（CSV），主要字段:
- 第1列：品种代码（如 IF、RB、AU 等）
- 第2列：名称（如 沪深、螺纹、黄金 等）
- 第3列：交易所代码（CZ - 中金所、QD - 大连商品、QS - 上海期货、QZ - 郑州商品、QG - 广州期货）

### code2qhidx.ini

包含主连期货合约的信息，是一个 INI 格式文件，主要部分:
- [FL] 部分：定义了不同类别的主连期货合约
- Num=N：定义了有多少个期货类别
- NameX：第X个类别的名称和品种数量
- FLX：第X个类别包含的期货品种列表，格式为 `交易所前缀_品种代码`
  - 交易所前缀：数字形式（47 - 中金所、28 - 郑商所、29 - 大商所、30 - 上期所、66 - 广期所）
  - 品种代码：通常包含字母和数字，需要去除数字部分获取品种代码
