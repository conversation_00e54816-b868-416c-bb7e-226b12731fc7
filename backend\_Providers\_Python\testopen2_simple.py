# -*- coding: utf-8 -*-
import time
from openctp_ctp import thostmduserapi as mdapi

class MdSpi(mdapi.CThostFtdcMdSpi):
    def __init__(self, api):
        super().__init__()
        self.api = api

    def OnFrontConnected(self):
        print("连接成功")
        req = mdapi.CThostFtdcReqUserLoginField()
        req.BrokerID = ""
        req.UserID = "12362"
        req.Password = "123456"
        self.api.ReqUserLogin(req, 1)

    def OnRspUserLogin(self, pRspUserLogin, pRspInfo, nRequestID, bIsLast):
        print("登录成功")
        instruments = ["600000"]  # 浦发银行
        self.api.SubscribeMarketData([i.encode('gbk') for i in instruments], len(instruments))

    def OnRtnDepthMarketData(self, pDepthMarketData):
        symbol = pDepthMarketData.InstrumentID.decode('gbk')
        print(f"{symbol}: {pDepthMarketData.LastPrice}")

# 创建API
api = mdapi.CThostFtdcMdApi.CreateFtdcMdApi()
spi = MdSpi(api)

# 连接到CTP实盘行情前置（支持股票）
api.RegisterFront("tcp://**************:42213")
api.RegisterSpi(spi)
api.Init()

# 保持运行
while True:
    time.sleep(1)
