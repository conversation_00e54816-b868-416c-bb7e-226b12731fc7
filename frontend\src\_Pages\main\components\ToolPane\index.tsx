import React, { useEffect } from 'react';
import SignalList from '../SignalList';
import SymbolList from '../../../../_Widgets/SymbolList';
import { useAtom } from 'jotai';
import { toolPaneContentAtom, toolPaneVisibleAtom } from '@/store/state';
import { EventBus } from '@/events/eventBus';
import { MarketEvents } from '@/events/events';
import { ChartEvents } from '@/events/events';
import './index.less';

const ToolPane: React.FC = () => {
  const [content, setContent] = useAtom(toolPaneContentAtom);
  const [visible, setVisible] = useAtom(toolPaneVisibleAtom);
  
  // 监听信号计算结果，自动切换到信号列表面板
  useEffect(() => {
    console.log('[ToolPane] 设置信号结果事件监听器');
    
    const signalSubscription = EventBus.on(
      MarketEvents.Types.SIGNALCALCULATED_RESULT,
      (payload: MarketEvents.SignalsCalculatedResultPayload) => {
        console.log('[ToolPane] 收到信号结果，切换到信号列表面板');
        setContent('signal-list');
        setVisible(true);
      }
    );

    // 添加浏览历史切换事件监听
    const historySubscription = EventBus.on(
      ChartEvents.Types.TOGGLE_SYMBOLLIST,
      (payload: ChartEvents.ToggleSymbolListPayload) => {
        console.log('[ToolPane] 收到品种列表切换事件:', payload);
        if (payload.isMobile) {
          // 移动端时，隐藏面板
          setVisible(false);
          setContent(null);
        } else {
          // 桌面端时，根据visible参数控制显示
          if (payload.visible) {
            // 显示品种列表面板
            setContent('symbol-list');
            setVisible(true);
            console.log('[ToolPane] 显示品种列表面板');
          } else {
            // 隐藏面板
            setVisible(false);
            setContent(null);
            console.log('[ToolPane] 隐藏面板');
          }
        }
      }
    );

    return () => {
      console.log('[ToolPane] 清理事件监听器');
      signalSubscription.unsubscribe();
      historySubscription.unsubscribe();
    };
  }, [setContent, setVisible]);

  // 如果面板不可见，不渲染任何内容
  if (!visible) {
    return null;
  }
  
  return (
    <div className="tool-pane">
      {content === 'signal-list' && <SignalList />}
      {content === 'symbol-list' && <SymbolList />}
    </div>
  );
};

export default ToolPane; 