.keyboard-coder {
  width: 400px !important;
  padding: 20px;
  border-radius: 4px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1050;

  .ant-select {
    width: 100%;
  }

  .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    height: 42px;
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 18px;
    display: flex;
    align-items: center;
    border: 1px solid;
  }

  .ant-select-single .ant-select-selector .ant-select-selection-search {
    height: 42px;
    font-size: 18px;
    display: flex;
    align-items: center;
  }

  .ant-select-selection-placeholder {
    font-size: 18px;
    line-height: 30px;
  }

  .ant-select-dropdown {
    .ant-select-item {
      font-size: 16px;
      padding: 8px 16px;
    }
  }

  .ant-select-clear {
    font-size: 16px;
    right: 11px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
  }
}

// 添加内联模式的样式
.keyboard-coder-inline {
  // 内联模式不需要固定定位和居中
  display: inline-block;
  min-width: 150px; // 设置最小宽度，防止缩小
  width: auto;

  .ant-select {
    width: 100% !important; // 确保选择器占满容器宽度
  }

  .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    height: 36px; // 稍微小一点的高度
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    border: 1px solid;
    min-width: 150px; // 设置最小宽度
  }

  .ant-select-single .ant-select-selector .ant-select-selection-search {
    height: 36px;
    font-size: 14px;
    display: flex;
    align-items: center;
    min-width: 150px; // 设置最小宽度
  }

  .ant-select-selection-placeholder {
    font-size: 14px;
    line-height: 28px;
  }

  .ant-select-dropdown {
    .ant-select-item {
      font-size: 14px;
      padding: 6px 12px;
    }
  }

  .ant-select-clear {
    font-size: 14px;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
  }

  // 深色主题样式
  &.dark {
    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      background-color: rgba(45, 45, 45, 0.95);
      border-color: rgba(255, 255, 255, 0.15);
      color: #fff;

      &:hover,
      &:focus-within {
        border-color: rgba(255, 255, 255, 0.25);
      }
    }

    .ant-select-single .ant-select-selector .ant-select-selection-search {
      input {
        color: #fff;
        &::placeholder {
          color: rgba(255, 255, 255, 0.45);
        }
      }
    }

    .ant-select-selection-placeholder {
      color: rgba(255, 255, 255, 0.45);
    }

    .ant-select-clear {
      color: rgba(255, 255, 255, 0.45);
      background-color: rgba(45, 45, 45, 0.95);

      &:hover {
        color: rgba(255, 255, 255, 0.65);
      }
    }
  }

  // 浅色主题样式
  &.light {
    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      background-color: #fff;
      border-color: #d9d9d9;
      color: rgba(0, 0, 0, 0.88);

      &:hover,
      &:focus-within {
        border-color: #40a9ff;
      }
    }

    .ant-select-single .ant-select-selector .ant-select-selection-search {
      input {
        color: rgba(0, 0, 0, 0.88);
        &::placeholder {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }

    .ant-select-selection-placeholder {
      color: rgba(0, 0, 0, 0.25);
    }

    .ant-select-clear {
      color: rgba(0, 0, 0, 0.25);
      background-color: #fff;

      &:hover {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}

.keyboard-coder {
  &.dark {
    background-color: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      background-color: rgba(45, 45, 45, 0.95);
      border-color: rgba(255, 255, 255, 0.15);
      color: #fff;

      &:hover,
      &:focus-within {
        border-color: rgba(255, 255, 255, 0.25);
      }
    }

    .ant-select-single .ant-select-selector .ant-select-selection-search {
      input {
        color: #fff;
        &::placeholder {
          color: rgba(255, 255, 255, 0.45);
        }
      }
    }

    .ant-select-selection-placeholder {
      color: rgba(255, 255, 255, 0.45);
    }

    .ant-select-dropdown {
      background-color: rgba(30, 30, 30, 0.98);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      .ant-select-item {
        color: rgba(255, 255, 255, 0.85);

        &:hover {
          background-color: rgba(255, 255, 255, 0.08);
        }

        &-selected {
          background-color: rgba(255, 255, 255, 0.12) !important;
          color: #fff;
        }
      }
    }

    .ant-select-clear {
      color: rgba(255, 255, 255, 0.45);
      background-color: rgba(45, 45, 45, 0.95);

      &:hover {
        color: rgba(255, 255, 255, 0.65);
      }
    }
  }

  &.light {
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      background-color: #fff;
      border-color: #d9d9d9;
      color: rgba(0, 0, 0, 0.88);

      &:hover,
      &:focus-within {
        border-color: #40a9ff;
      }
    }

    .ant-select-single .ant-select-selector .ant-select-selection-search {
      input {
        color: rgba(0, 0, 0, 0.88);
        &::placeholder {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }

    .ant-select-selection-placeholder {
      color: rgba(0, 0, 0, 0.25);
    }

    .ant-select-clear {
      color: rgba(0, 0, 0, 0.25);
      background-color: #fff;

      &:hover {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}