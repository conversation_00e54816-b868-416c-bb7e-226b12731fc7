/**
 * 夜盘时间调整函数测试
 * 针对期货夜盘交易时间戳调整逻辑
 * TODO: 目前的假设是服务器在中国境内
 */
function adjustNightTradingTime(klineData) {
    
    if (!klineData || klineData.length === 0) {
      console.log('输入数据为空或长度为0，直接返回');
      return klineData;
    }
        
    // 创建结果数组
    const result = [];
    
    // 记录上一个日盘的日期（时间戳，精确到日）
    let lastDaySessionDate = 0;
    
    // 记录调整的数据条数
    let adjustedCount = 0;
        
    for (const kline of klineData) {

      const msTime = kline.time * 1000;

      // 创建日期对象，通过时间戳（秒）,
      const klineDate = new Date(msTime);
      
      // 获取小时
      const year = klineDate.getFullYear();
      const month = klineDate.getMonth();
      const day = klineDate.getDate();
      const hours = klineDate.getHours();
      const minutes = klineDate.getMinutes();
      const seconds = klineDate.getSeconds();
  
      
      const isNightSession = hours >= 17;
      
      
      // 如果是夜盘交易，且有上一个日盘日期记录，需要调整时间
      if (isNightSession) {
  
        if (lastDaySessionDate === 0) {
    
          let previousDay = new Date(year, month, day);
    
          previousDay.setDate(previousDay.getDate() - 1);
            
          while (previousDay.getDay() === 0 || previousDay.getDay() === 6) {
  
            previousDay.setDate(previousDay.getDate() - 1);
  
          }
          
          lastDaySessionDate = Math.floor(previousDay.getTime() / 1000);
  
        }
  
        // 把此条k线时间的日期更换为 lastDaySessionDate
        kline.time = lastDaySessionDate + (hours * 3600 + minutes * 60 + seconds);
        
        adjustedCount++; // 增加调整计数
        
      } else {
                
        lastDaySessionDate = new Date(year, month, day).getTime() / 1000;
                
      }
      
      result.push(kline);
    }
    
    // 添加日志，显示调整了多少夜盘数据
    console.log(`====== 调整了 ${adjustedCount}/${klineData.length} 条夜盘数据 ======`);
        
    return result;
  }

  

// 运行测试
function runTests() {
    res = adjustNightTradingTime([{time: 1741096800, open: 1000, high: 1000, low: 1000, close: 1000, volume: 1000}])
    console.log(res)
}

// 执行所有测试
runTests();

//test
  