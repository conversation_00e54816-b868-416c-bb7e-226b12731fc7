import { EventBus } from '../events/eventBus';
import { UserEvents, ChatEvents } from '../events/events';
import { setToken, getUserInfo, UserInfo, removeToken } from '../utils/auth';
import axios from 'axios';
import CryptoJS from 'crypto-js';
import { v4 as uuidv4 } from 'uuid';
import httpDispatcher from './HttpDispatcher';


interface LoginResponse {
  success: boolean;
  data: {
    id: number;
    username: string;
    email: string;
    avatar?: string;
    token: string;
  };
  error?: string;
}

interface RegisterResponse {
  success: boolean;
  data: {
    id: number;
    username: string;
    email: string;
    avatar?: string;
    token: string;
  };
  error?: string;
}

class UserDispatcher {
  private readonly ACCESS_KEY_ID = 'P7otfBD1C1TUEA7T8PSR3sv842MnSpGa59J71biPaQng9juy4';
  private readonly ACCESS_KEY_SECRET = '';  // 替换为实际的 Secret
  private readonly SMS_API_URL = '/sms';  // 修改为使用代理路径

  constructor() {
    console.log('[UserDispatcher] 构造中...');
    this.setupListeners();
  }

  setupListeners() {
    console.log('[UserDispatcher] 设置事件监听');
    // 先删除所有旧的监听器以避免重复添加
    EventBus.off(UserEvents.Types.LOGIN, this.handleLogin.bind(this));
    EventBus.off(UserEvents.Types.REGISTER, this.handleRegister.bind(this));
    EventBus.off(UserEvents.Types.UPDATE, this.handleUpdate.bind(this));
    EventBus.off(UserEvents.Types.SEND_EMAIL_CODE, this.handleSendEmailCode.bind(this));
    EventBus.off(UserEvents.Types.RESET_PASSWORD, this.handleResetPassword.bind(this));
    
    // 重新添加监听器
    EventBus.on(UserEvents.Types.LOGIN, this.handleLogin.bind(this));
    EventBus.on(UserEvents.Types.REGISTER, this.handleRegister.bind(this));
    EventBus.on(UserEvents.Types.UPDATE, this.handleUpdate.bind(this));
    EventBus.on(UserEvents.Types.SEND_EMAIL_CODE, this.handleSendEmailCode.bind(this));
    EventBus.on(UserEvents.Types.APPROVE_USER, this.handleApproveUser.bind(this));
    EventBus.on(UserEvents.Types.RESET_PASSWORD, this.handleResetPassword.bind(this));
  }

  private generateSignature(params: Record<string, string>): string {
    // 按字典序排序参数
    const sortedParams = Object.keys(params).sort().map(key => 
      `${key}=${params[key]}`
    ).join('&');
    
    // 使用 HmacSHA256 生成签名
    const hash = CryptoJS.HmacSHA256(sortedParams, this.ACCESS_KEY_SECRET);
    return CryptoJS.enc.Base64.stringify(hash);
  }

  async handleUpdate(userData: { username: string; avatar?: string; password?: string; [key: string]: any }): Promise<UserEvents.ResultPayload> {
    
    console.log("handleUpdate 被调用", userData);

    try {
      const response = await httpDispatcher.post('/user/update', userData);
      
      if (response.data.success) {
        EventBus.emit(UserEvents.Types.UPDATE_RESULT, {
          success: true,
          data: response.data.data
        });
        
        return { success: true, data: response.data.data };
      } else {
        console.log("更新用户信息失败，response.data.error：", response.data.error);

        const result: UserEvents.ResultPayload = {
          success: false,
          data: null,
          error: response.data.error || '更新失败'
        };
        EventBus.emit(UserEvents.Types.UPDATE_RESULT, result);
        return result;
      }
    } catch (error) {
      console.error('[UserDispatcher] Update failed:', error);
      const result: UserEvents.ResultPayload = {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : '更新失败'
      };
      EventBus.emit(UserEvents.Types.UPDATE_RESULT, result);
      return result;
    }
  }

  async handleLogin(credentials: UserEvents.Payload) {
    console.log('\n=== API Call: Login ===');
    console.log('[UserDispatcher] Sending login request:', {
      url: '/api/user/login',
      method: 'POST',
      data: {
        username: credentials.username,
        password: '[HIDDEN]'
      }
    });

    try {
        const response = await httpDispatcher.post('/user/login', credentials);
      console.log('[UserDispatcher] Login response.data:', response.data);

      if (response.data.success) {
        // 检查用户状态
        if (response.data.data.status !== 'active') {
          EventBus.emit(UserEvents.Types.LOGIN_RESULT, {
            success: false,
            data: null,
            error: '您的账号尚未激活，请等待管理员审核'
          });
          return;
        }

        // 保存token
        setToken(response.data.data.token);
        
        // 发送登录成功事件
        EventBus.emit(UserEvents.Types.LOGIN_RESULT, {
          success: true,
          data: response.data.data
        });
      } else {
        EventBus.emit(UserEvents.Types.LOGIN_RESULT, {
          success: false,
          data: null,
          error: response.data.error || '登录失败，请稍后重试'
        });
      }

      console.log('=== API Call Complete ===\n');
    } catch (error) {
      console.error('[UserDispatcher] Login failed:', error);

      EventBus.emit(UserEvents.Types.LOGIN_RESULT, {
        success: false,
        data: null,
        error: '网络错误，请检查您的网络连接'
      });
      console.log('=== API Call Failed ===\n');
    }
  }

  async handleApproveUser(payload: UserEvents.ApproveUserPayload) {
    const currentUser = getUserInfo(); // 获取当前用户信息

    // 检查当前用户角色是否为 admin
    if (currentUser?.role !== 'admin') {
      EventBus.emit(UserEvents.Types.APPROVE_USER_RESULT, {
        success: false,
        message: '权限不足，只有管理员可以批准用户'
      });
      return;
    }

    // 发送请求到后端批准用户
    try {
      const response = await httpDispatcher.post('/user/approve', { username: payload.usernametobeapproved });
      if (response.data.success) {
        EventBus.emit(UserEvents.Types.APPROVE_USER_RESULT, {
          success: true,
          message: '用户已批准'
        });
      } else {
        EventBus.emit(UserEvents.Types.APPROVE_USER_RESULT, {
          success: false,
          message: response.data.error || '批准用户失败'
        });
      }
    } catch (error) {
      console.error('[UserDispatcher] Approval request failed:', error);
      EventBus.emit(UserEvents.Types.APPROVE_USER_RESULT, {
        success: false,
        message: '网络错误，请稍后重试'
      });
    }
  }

  async handleSendEmailCode(payload: UserEvents.EmailCodePayload) {
    try {
      console.log('[UserDispatcher] 发送邮箱验证码:', {
        email: payload.email,
        code: '****' // 隐藏验证码
      });

      const response = await httpDispatcher.post('/user/send-email', {
        email: payload.email,
        code: payload.code
      });

    } catch (error) {
      console.error('[UserDispatcher] 发送邮箱验证码失败:', error);
    }
  }

  async handleRegister(userData: UserEvents.Payload) {
    console.log('\n=== API Call: Register ===');
    console.log('[UserDispatcher] Sending register request:', {
      url: '/api/user/register',
      method: 'POST',
      data: {
        username: userData.username,
        email: userData.email,
        password: '[HIDDEN]'
      }
    });

    try {
      const response = await httpDispatcher.post('/user/register', userData);
      console.log('[UserDispatcher] Register response:', {
        status: response.status,
        data: {
          ...response.data,
          token: response.data.token ? 'Bearer ...' : undefined
        }
      });

      if (response.data.success) {
        EventBus.emit(UserEvents.Types.REGISTER_RESULT, {
          success: true,
          data: response.data.data
        });
      } else {
        // 直接从响应中获取错误信息
        const error = response.data.error || '未知错误';
        EventBus.emit(UserEvents.Types.REGISTER_RESULT, {
          success: false,
          data: null,
          error
        });
      }

      console.log('=== API Call Complete ===\n');
    } catch (error) {
      console.error('[UserDispatcher] Register failed:', error);

      // 如果是网络错误，使用默认错误信息
      const networkError = '网络错误，请检查您的网络连接';
      EventBus.emit(UserEvents.Types.REGISTER_RESULT, {
        success: false,
        data: null,
        error: (error as any).response.data.error
      });
      console.log('=== API Call Failed ===\n');
    }
  }

  async handleResetPassword(payload: UserEvents.ResetPasswordPayload) {
    console.log('\n=== API Call: Reset Password ===');
    console.log('[UserDispatcher] Sending reset password request:', {
      url: '/api/user/reset-password',
      method: 'POST',
      data: {
        email: payload.email,
        code: payload.code,
        newPassword: '[HIDDEN]'
      }
    });

    try {
      const response = await httpDispatcher.post('/user/reset-password', payload);
      console.log('[UserDispatcher] Reset password response:', {
        status: response.status,
        data: {
          ...response.data,
          // 隐藏敏感信息
          token: response.data.token ? 'Bearer ...' : undefined
        }
      });

      if (response.data.success) {
        EventBus.emit(UserEvents.Types.RESET_PASSWORD_RESULT, {
          success: true,
          data: null
        });
      } else {
        // 直接从响应中获取错误信息
        const error = response.data.error || '未知错误';
        EventBus.emit(UserEvents.Types.RESET_PASSWORD_RESULT, {
          success: false,
          data: null,
          error: error
        });
      }

      console.log('=== API Call Complete ===\n');
    } catch (error) {
      console.error('[UserDispatcher] Reset password failed:', error);

      EventBus.emit(UserEvents.Types.RESET_PASSWORD_RESULT, {
        success: false,
        data: null,
        error: '网络错误，请检查您的网络连接'
      });
      console.log('=== API Call Failed ===\n');
    }
  }

}

export default new UserDispatcher();
