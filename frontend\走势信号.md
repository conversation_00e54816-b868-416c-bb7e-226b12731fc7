# 走势信号列表

## 1. 动量类信号

### 1.1 移动平均交叉信号 (Moving Average Crossover)
- 描述：通过快速和慢速移动平均线的交叉来判断趋势变化
- 参数：
  - shortPeriod: number  // 快速移动平均周期，如 5
  - longPeriod: number   // 慢速移动平均周期，如 20
- 信号生成：
  - 买入：快线上穿慢线
  - 卖出：快线下穿慢线

### 1.2 价格动量信号 (Price Momentum)
- 描述：计算价格在特定周期内的变化率
- 参数：
  - period: number       // 计算周期，如 14
  - threshold: number    // 信号阈值，如 0.1 (10%)
- 信号生成：
  - 买入：动量大于阈值
  - 卖出：动量小于负阈值

### 1.3 相对强弱指标信号 (RSI)
- 描述：通过计算价格涨跌幅度的相对强弱来判断超买超卖
- 参数：
  - period: number       // 计算周期，如 14
  - overbought: number  // 超买阈值，如 70
  - oversold: number    // 超卖阈值，如 30
- 信号生成：
  - 买入：RSI从超卖区上穿
  - 卖出：RSI从超买区下穿

## 2. 趋势类信号

### 2.1 趋势跟踪信号 (Trend Following)
- 描述：通过价格与移动平均线的关系判断趋势
- 参数：
  - maPeriod: number    // 移动平均周期，如 20
  - threshold: number   // 偏离阈值，如 0.02 (2%)
- 信号生成：
  - 买入：价格上穿MA且偏离度大于阈值
  - 卖出：价格下穿MA且偏离度大于阈值

### 2.2 MACD信号 (MACD)
- 描述：通过MACD指标的金叉死叉和背离来判断趋势转折
- 参数：
  - fastPeriod: number  // 快速EMA周期，如 12
  - slowPeriod: number  // 慢速EMA周期，如 26
  - signalPeriod: number // 信号线周期，如 9
- 信号生成：
  - 买入：MACD金叉或底背离
  - 卖出：MACD死叉或顶背离

### 2.3 布林带信号 (Bollinger Bands)
- 描述：通过价格在布林带中的位置判断趋势强度和可能的反转
- 参数：
  - period: number      // 移动平均周期，如 20
  - stdDev: number     // 标准差倍数，如 2
- 信号生成：
  - 买入：价格触及下轨后回升
  - 卖出：价格触及上轨后回落

## 3. 波动类信号

### 3.1 ATR突破信号 (ATR Breakout)
- 描述：通过真实波幅的突破来捕捉价格突破
- 参数：
  - period: number      // ATR计算周期，如 14
  - multiplier: number // ATR倍数，如 2
- 信号生成：
  - 买入：价格突破上轨（前期高点 + multiplier * ATR）
  - 卖出：价格突破下轨（前期低点 - multiplier * ATR）

### 3.2 波动率通道信号 (Volatility Channel)
- 描述：基于价格波动率构建的动态通道
- 参数：
  - period: number      // 计算周期，如 20
  - multiplier: number // 通道宽度倍数，如 2.5
- 信号生成：
  - 买入：价格突破通道上轨
  - 卖出：价格突破通道下轨

## 4. 成交量类信号

### 4.1 量价背离信号 (Volume Price Divergence)
- 描述：通过成交量和价格的背离关系判断趋势的可持续性
- 参数：
  - period: number      // 观察周期，如 20
  - volumeThreshold: number // 成交量变化阈值，如 1.5
- 信号生成：
  - 买入：价格创新低但成交量萎缩
  - 卖出：价格创新高但成交量萎缩

### 4.2 成交量突破信号 (Volume Breakout)
- 描述：通过成交量的突破来确认价格突破的有效性
- 参数：
  - period: number      // 成交量平均周期，如 20
  - volumeMultiplier: number // 成交量放大倍数，如 2
- 信号生成：
  - 买入：价格上涨且成交量显著放大
  - 卖出：价格下跌且成交量显著放大

## 5. 组合类信号

### 5.1 趋势确认信号 (Trend Confirmation)
- 描述：结合多个技术指标确认趋势
- 参数：
  - maPeriod: number    // 移动平均周期，如 20
  - rsiPeriod: number   // RSI周期，如 14
  - volumePeriod: number // 成交量周期，如 20
- 信号生成：
  - 买入：价格上穿MA + RSI上升 + 成交量确认
  - 卖出：价格下穿MA + RSI下降 + 成交量确认

### 5.2 多重时间框架信号 (Multiple Timeframe)
- 描述：在多个时间周期上确认信号
- 参数：
  - primaryPeriod: string   // 主要周期，如 '15m'
  - secondaryPeriod: string // 次要周期，如 '1h'
  - confirmationDelay: number // 确认延迟，如 3
- 信号生成：
  - 买入：短周期和长周期同时出现买入信号
  - 卖出：短周期和长周期同时出现卖出信号 