#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths内网穿透隧道

该模块实现了内网穿透功能，使远程后端能够与本地同花顺客户端通信。
"""

import time
import json
import logging
import threading
import requests
from flask import Flask, request, jsonify

logger = logging.getLogger("EasyTrader-THS-Tunnel")

class TunnelClient:
    """内网穿透隧道客户端"""
    
    def __init__(self, username, backend_url, local_port, heartbeat_interval=60):
        """
        初始化隧道客户端
        
        Args:
            username: 用户名
            backend_url: 后端URL
            local_port: 本地端口
            heartbeat_interval: 心跳间隔(秒)
        """
        self.username = username
        self.backend_url = backend_url
        self.local_port = local_port
        self.heartbeat_interval = heartbeat_interval
        self.registered = False
        self.running = False
        self.heartbeat_thread = None
        
        logger.info(f"初始化内网穿透隧道客户端: username={username}, backend_url={backend_url}")
    
    def start(self):
        """启动隧道客户端"""
        if self.running:
            logger.warning("隧道客户端已经在运行")
            return
        
        self.running = True
        
        # 注册到后端
        if not self.register():
            logger.error("注册到后端失败，隧道客户端启动失败")
            self.running = False
            return False
        
        # 启动心跳线程
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()
        
        logger.info("隧道客户端启动成功")
        return True
    
    def stop(self):
        """停止隧道客户端"""
        if not self.running:
            logger.warning("隧道客户端未在运行")
            return
        
        self.running = False
        
        # 等待心跳线程结束
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=2)
        
        logger.info("隧道客户端已停止")
    
    def register(self):
        """注册到后端"""
        try:
            data = {
                "username": self.username,
                "client_ip": self._get_local_ip(),
                "client_port": self.local_port,
                "client_type": "easytrader_ths",
                "timestamp": int(time.time())
            }
            
            response = requests.post(
                f"{self.backend_url}/api/register_trading_client",
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    logger.info(f"成功注册到后端: {result.get('message')}")
                    self.registered = True
                    return True
                else:
                    logger.error(f"注册到后端失败: {result.get('message')}")
            else:
                logger.error(f"注册到后端失败，状态码: {response.status_code}")
            
            return False
        except Exception as e:
            logger.error(f"注册到后端时发生错误: {str(e)}")
            return False
    
    def _heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            try:
                self._send_heartbeat()
            except Exception as e:
                logger.error(f"发送心跳时发生错误: {str(e)}")
            
            # 等待下一次心跳
            for _ in range(self.heartbeat_interval):
                if not self.running:
                    break
                time.sleep(1)
    
    def _send_heartbeat(self):
        """发送心跳"""
        try:
            data = {
                "username": self.username,
                "client_ip": self._get_local_ip(),
                "client_port": self.local_port,
                "client_type": "easytrader_ths",
                "timestamp": int(time.time())
            }
            
            response = requests.post(
                f"{self.backend_url}/api/trading_client_heartbeat",
                json=data,
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    logger.debug(f"心跳发送成功: {result.get('message')}")
                else:
                    logger.warning(f"心跳发送失败: {result.get('message')}")
                    # 如果心跳失败，尝试重新注册
                    if not self.registered:
                        self.register()
            else:
                logger.warning(f"心跳发送失败，状态码: {response.status_code}")
                # 如果心跳失败，尝试重新注册
                if not self.registered:
                    self.register()
        except Exception as e:
            logger.error(f"发送心跳时发生错误: {str(e)}")
            # 如果心跳失败，尝试重新注册
            if not self.registered:
                self.register()
    
    def _get_local_ip(self):
        """获取本机IP地址"""
        try:
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"

class TunnelServer:
    """内网穿透隧道服务端"""
    
    def __init__(self, app=None):
        """
        初始化隧道服务端
        
        Args:
            app: Flask应用，如果为None则创建新的应用
        """
        self.app = app or Flask(__name__)
        self.clients = {}  # 用户名 -> 客户端信息
        self.client_lock = threading.Lock()
        self.last_cleanup = time.time()
        self.cleanup_interval = 300  # 5分钟清理一次过期客户端
        
        # 注册路由
        self._register_routes()
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_expired_clients, daemon=True)
        self.cleanup_thread.start()
        
        logger.info("初始化内网穿透隧道服务端")
    
    def _register_routes(self):
        """注册路由"""
        @self.app.route('/api/register_trading_client', methods=['POST'])
        def register_trading_client():
            """注册交易客户端"""
            try:
                data = request.json
                username = data.get('username')
                client_ip = data.get('client_ip')
                client_port = data.get('client_port')
                client_type = data.get('client_type')
                timestamp = data.get('timestamp')
                
                if not all([username, client_ip, client_port, client_type, timestamp]):
                    return jsonify({"success": False, "message": "参数不完整"}), 400
                
                success = self.register_client(username, client_ip, client_port, client_type, timestamp)
                
                if success:
                    return jsonify({"success": True, "message": "注册成功"})
                else:
                    return jsonify({"success": False, "message": "注册失败"}), 500
            except Exception as e:
                logger.error(f"注册交易客户端失败: {str(e)}")
                return jsonify({"success": False, "message": str(e)}), 500
        
        @self.app.route('/api/trading_client_heartbeat', methods=['POST'])
        def trading_client_heartbeat():
            """交易客户端心跳"""
            try:
                data = request.json
                username = data.get('username')
                timestamp = data.get('timestamp')
                
                if not all([username, timestamp]):
                    return jsonify({"success": False, "message": "参数不完整"}), 400
                
                success = self.update_client_heartbeat(username, timestamp)
                
                if success:
                    return jsonify({"success": True, "message": "心跳更新成功"})
                else:
                    return jsonify({"success": False, "message": "心跳更新失败"}), 500
            except Exception as e:
                logger.error(f"更新交易客户端心跳失败: {str(e)}")
                return jsonify({"success": False, "message": str(e)}), 500
        
        @self.app.route('/api/get_trading_clients', methods=['GET'])
        def get_trading_clients():
            """获取所有交易客户端"""
            try:
                clients = self.get_all_clients()
                return jsonify({"success": True, "data": clients})
            except Exception as e:
                logger.error(f"获取交易客户端失败: {str(e)}")
                return jsonify({"success": False, "message": str(e)}), 500
        
        @self.app.route('/api/get_trading_client', methods=['GET'])
        def get_trading_client():
            """获取指定交易客户端"""
            try:
                username = request.args.get('username')
                
                if not username:
                    return jsonify({"success": False, "message": "参数不完整"}), 400
                
                client = self.get_client(username)
                
                if client:
                    return jsonify({"success": True, "data": client})
                else:
                    return jsonify({"success": False, "message": "客户端不存在"}), 404
            except Exception as e:
                logger.error(f"获取交易客户端失败: {str(e)}")
                return jsonify({"success": False, "message": str(e)}), 500
    
    def register_client(self, username, client_ip, client_port, client_type, timestamp):
        """
        注册客户端
        
        Args:
            username: 用户名
            client_ip: 客户端IP
            client_port: 客户端端口
            client_type: 客户端类型
            timestamp: 时间戳
        
        Returns:
            bool: 是否注册成功
        """
        with self.client_lock:
            client_url = f"http://{client_ip}:{client_port}"
            
            # 检查客户端连接
            try:
                response = requests.get(f"{client_url}/api/status", timeout=5)
                if response.status_code != 200:
                    logger.error(f"客户端连接失败: {client_url}")
                    return False
            except Exception as e:
                logger.error(f"客户端连接失败: {client_url}, 错误: {str(e)}")
                return False
            
            # 注册客户端
            self.clients[username] = {
                'username': username,
                'client_ip': client_ip,
                'client_port': client_port,
                'client_url': client_url,
                'client_type': client_type,
                'last_heartbeat': timestamp,
                'registered_at': timestamp
            }
            
            logger.info(f"注册客户端成功: username={username}, client_url={client_url}")
            return True
    
    def update_client_heartbeat(self, username, timestamp):
        """
        更新客户端心跳
        
        Args:
            username: 用户名
            timestamp: 时间戳
        
        Returns:
            bool: 是否更新成功
        """
        with self.client_lock:
            if username not in self.clients:
                logger.warning(f"更新心跳失败: 客户端不存在, username={username}")
                return False
            
            self.clients[username]['last_heartbeat'] = timestamp
            return True
    
    def get_client(self, username):
        """
        获取客户端信息
        
        Args:
            username: 用户名
        
        Returns:
            dict: 客户端信息，如果不存在则返回None
        """
        with self.client_lock:
            return self.clients.get(username)
    
    def get_all_clients(self):
        """
        获取所有客户端信息
        
        Returns:
            list: 客户端信息列表
        """
        with self.client_lock:
            return list(self.clients.values())
    
    def remove_client(self, username):
        """
        移除客户端
        
        Args:
            username: 用户名
        
        Returns:
            bool: 是否移除成功
        """
        with self.client_lock:
            if username in self.clients:
                del self.clients[username]
                logger.info(f"移除客户端: username={username}")
                return True
            return False
    
    def _cleanup_expired_clients(self):
        """清理过期客户端"""
        while True:
            time.sleep(60)  # 每分钟检查一次
            
            now = time.time()
            if now - self.last_cleanup < self.cleanup_interval:
                continue
            
            self.last_cleanup = now
            expired_usernames = []
            
            with self.client_lock:
                for username, client in self.clients.items():
                    # 如果超过10分钟没有心跳，则认为客户端已过期
                    if now - client['last_heartbeat'] > 600:
                        expired_usernames.append(username)
            
            # 移除过期客户端
            for username in expired_usernames:
                self.remove_client(username)
                logger.info(f"清理过期客户端: username={username}")
    
    def run(self, host='0.0.0.0', port=5000, debug=False):
        """
        运行服务端
        
        Args:
            host: 主机
            port: 端口
            debug: 是否开启调试模式
        """
        self.app.run(host=host, port=port, debug=debug)
