{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020", "dom"], "outDir": "./dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "noEmitOnError": true, "typeRoots": ["./node_modules/@types"], "types": ["node", "sequelize"], "baseUrl": ".", "paths": {"*": ["node_modules/*", "src/types/*"]}}, "include": ["_Handlers/**/*", "_Providers/**/*", "database/**/*", "events/**/*", "middleware/**/*", "models/**/*", "routes/**/*", "utils/**/*", "*.ts", "*.js"], "exclude": ["node_modules", "dist"]}