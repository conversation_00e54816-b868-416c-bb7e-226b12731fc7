# 未来因子列表
未来因子是一种特殊的因子类型，它需要"预知"未来数据来生成信号。这类因子仅用于策略研究阶段，不能用于实盘交易。

## 1. 价格预测类因子

### 1.1 未来涨幅因子 (Future Return)
- 描述：预测未来特定周期内的价格涨跌幅
- 参数：
  - forwardPeriods: number  // 向前看的周期数，如 5
  - returnThreshold: number // 涨跌幅阈值，如 0.05 (5%)
- 信号生成：
  - 买入：未来涨幅大于阈值
  - 卖出：未来跌幅大于阈值

### 1.2 最大回撤预警因子 (Future Drawdown)
- 描述：预测未来特定周期内是否会出现大幅回撤
- 参数：
  - forwardPeriods: number  // 向前看的周期数，如 10
  - drawdownThreshold: number // 回撤阈值，如 0.1 (10%)
- 信号生成：
  - 买入：未来不会出现大于阈值的回撤
  - 卖出：未来将出现大于阈值的回撤

## 2. 趋势预测类因子

### 2.1 趋势持续因子 (Trend Continuation)
- 描述：预测当前趋势是否会在未来持续
- 参数：
  - forwardPeriods: number  // 向前看的周期数，如 5
  - trendThreshold: number  // 趋势强度阈值，如 0.03 (3%)
- 信号生成：
  - 买入：上升趋势将持续
  - 卖出：下降趋势将持续

### 2.2 趋势反转因子 (Trend Reversal)
- 描述：预测未来是否会出现趋势反转
- 参数：
  - forwardPeriods: number  // 向前看的周期数，如 3
  - reversalThreshold: number // 反转幅度阈值，如 0.04 (4%)
- 信号生成：
  - 买入：即将出现向上反转
  - 卖出：即将出现向下反转

## 3. 波动预测类因子

### 3.1 未来波动率因子 (Future Volatility)
- 描述：预测未来特定周期内的价格波动率
- 参数：
  - forwardPeriods: number  // 向前看的周期数，如 5
  - volatilityThreshold: number // 波动率阈值，如 0.02 (2%)
- 信号生成：
  - 买入：未来波动率低于阈值
  - 卖出：未来波动率高于阈值

### 3.2 极值预测因子 (Future Extremes)
- 描述：预测未来特定周期内是否会出现价格极值
- 参数：
  - forwardPeriods: number  // 向前看的周期数，如 5
  - extremeThreshold: number // 极值偏离阈值，如 0.05 (5%)
- 信号生成：
  - 买入：未来将出现显著高点
  - 卖出：未来将出现显著低点

## 4. 机会预测类因子

### 4.1 最优买点因子 (Optimal Entry)
- 描述：预测未来特定周期内的最优买入时机
- 参数：
  - forwardPeriods: number  // 向前看的周期数，如 10
  - profitThreshold: number // 盈利阈值，如 0.05 (5%)
- 信号生成：
  - 买入：当前价格是未来周期内的较低买点
  - 卖出：无（仅产生买入信号）

### 4.2 最优卖点因子 (Optimal Exit)
- 描述：预测未来特定周期内的最优卖出时机
- 参数：
  - forwardPeriods: number  // 向前看的周期数，如 10
  - lossThreshold: number   // 止损阈值，如 0.03 (3%)
- 信号生成：
  - 买入：无（仅产生卖出信号）
  - 卖出：当前价格是未来周期内的较高卖点

## 注意事项

1. 未来因子仅用于策略研究阶段，不能用于实盘交易
2. 使用未来因子时必须在回测模式下运行
3. 未来因子可以帮助研究市场特征和优化其他因子的参数
4. 未来因子的信号可以作为其他实盘因子的对照基准
5. 在使用未来因子时要注意避免过度拟合 