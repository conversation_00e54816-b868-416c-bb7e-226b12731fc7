import { useCallback, useEffect, useRef } from 'react';
import { IChartApi } from 'klinecharts';
import { drawingLineService } from '../services/drawingLineService';
import { EventBus } from '../utils/eventBus';
import { ChartEvents } from '../shared_types/chart';

/**
 * 画线管理 Hook
 * @param chartInstance 图表实例
 * @param symbol 交易对
 * @param interval K线周期
 */
export const useDrawingLines = (
  chartInstance: IChartApi | null,
  symbol: string,
  interval: string
) => {
  // 记录当前图表的 overlayIds
  const myOverlayIds = useRef<string[]>([]);

  // 加载画线数据
  const loadDrawingLines = useCallback(async () => {
    if (!chartInstance || !symbol || !interval) return;

    try {
      console.log('[画线] 开始加载画线数据');
      const response = await drawingLineService.getDrawingLines(symbol, interval);
      if (response.success && response.data.length > 0) {
        // 清除现有画线
        myOverlayIds.current.forEach(id => {
          chartInstance.removeOverlay(id);
        });
        myOverlayIds.current = [];

        // 添加新的画线
        response.data.forEach(record => {
          if (record.overlays && record.overlays.length > 0) {
            record.overlays.forEach(overlay => {
              const id = chartInstance.createOverlay(overlay);
              if (id) {
                myOverlayIds.current.push(id);
              }
            });
          }
        });
        console.log('[画线] 画线数据加载完成');
      }
    } catch (error) {
      console.error('[画线] 加载画线数据失败:', error);
    }
  }, [chartInstance, symbol, interval]);

  // 保存画线数据
  const saveDrawingLines = useCallback(async () => {
    if (!chartInstance || !symbol || !interval) return;

    try {
      console.log('[画线] 开始保存画线数据');
      const overlays = chartInstance.getOverlays()
        .filter(overlay => myOverlayIds.current.includes(overlay.id));

      await drawingLineService.saveDrawingLines({
        symbol,
        interval,
        overlays
      });
      console.log('[画线] 画线数据保存完成');
    } catch (error) {
      console.error('[画线] 保存画线数据失败:', error);
    }
  }, [chartInstance, symbol, interval]);

  // 删除画线数据
  const deleteDrawingLines = useCallback(async () => {
    if (!chartInstance || !symbol || !interval) return;

    try {
      console.log('[画线] 开始删除画线数据');
      await drawingLineService.deleteDrawingLines(symbol, interval);
      
      // 清除现有画线
      myOverlayIds.current.forEach(id => {
        chartInstance.removeOverlay(id);
      });
      myOverlayIds.current = [];
      
      console.log('[画线] 画线数据删除完成');
    } catch (error) {
      console.error('[画线] 删除画线数据失败:', error);
    }
  }, [chartInstance, symbol, interval]);

  // 监听画线事件
  useEffect(() => {
    if (!chartInstance) return;

    const handleOverlayComplete = (overlay: any) => {
      console.log('[画线] 画线完成:', overlay);
      if (overlay.id) {
        myOverlayIds.current.push(overlay.id);
        // 自动保存
        saveDrawingLines();
      }
    };

    chartInstance.subscribeAction('onDrawEnd', handleOverlayComplete);

    // 订阅删除画线事件
    const deleteSubscription = EventBus.on(
      ChartEvents.Types.DELETE_DRAWING_LINE,
      async ({ id }: { id: string }) => {
        if (myOverlayIds.current.includes(id)) {
          chartInstance.removeOverlay(id);
          myOverlayIds.current = myOverlayIds.current.filter(overlayId => overlayId !== id);
          // 自动保存
          await saveDrawingLines();
        }
      }
    );

    // 订阅删除所有画线事件
    const deleteAllSubscription = EventBus.on(
      ChartEvents.Types.DELETE_ALL_DRAWING_LINES,
      async () => {
        await deleteDrawingLines();
      }
    );

    // 初始加载画线数据
    loadDrawingLines();

    return () => {
      chartInstance.unsubscribeAction('onDrawEnd', handleOverlayComplete);
      deleteSubscription.unsubscribe();
      deleteAllSubscription.unsubscribe();
    };
  }, [chartInstance, loadDrawingLines, saveDrawingLines, deleteDrawingLines]);

  return {
    loadDrawingLines,
    saveDrawingLines,
    deleteDrawingLines
  };
}; 