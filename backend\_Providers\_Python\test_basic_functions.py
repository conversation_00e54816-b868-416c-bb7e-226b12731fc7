#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试ExtDataLoader的基础功能（不依赖wtpy）
测试配置加载、代码解析、数据路径等基础功能
"""

import os
import sys
import json

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_config_loading():
    """测试配置文件加载"""
    print("=" * 60)
    print("测试配置文件加载")
    print("=" * 60)
    
    try:
        from prepare_csv import load_config
        
        config = load_config()
        if config:
            print("✅ 配置文件加载成功")
            print(f"  默认时区: {config.get('default_timezone', 'N/A')}")
            
            # 检查通达信数据配置
            tdx_config = config.get('tdx_data', {})
            if tdx_config:
                print(f"  通达信数据路径: {tdx_config.get('path', 'N/A')}")
                print(f"  通达信数据启用: {tdx_config.get('enabled', False)}")
            
            # 检查虚拟货币数据配置
            crypto_config = config.get('crypto_data', {})
            if crypto_config:
                print(f"  虚拟货币数据路径: {crypto_config.get('path', 'N/A')}")
                print(f"  虚拟货币数据启用: {crypto_config.get('enabled', False)}")
        else:
            print("❌ 配置文件加载失败")
            
    except ImportError as e:
        print(f"❌ 无法导入prepare_csv模块: {e}")
    except Exception as e:
        print(f"❌ 配置加载异常: {e}")

def test_code_parsing_logic():
    """测试代码解析逻辑（不依赖ExtDataLoader类）"""
    print("\n" + "=" * 60)
    print("测试代码解析逻辑")
    print("=" * 60)
    
    def parse_wt_code(stdCode: str):
        """简化的代码解析函数"""
        parts = stdCode.split('.')
        
        if len(parts) >= 2:
            exchange = parts[0].upper()
            
            # 虚拟货币
            if exchange == "CRYPTO" and len(parts) >= 3 and parts[1].upper() == "CPT":
                return {
                    'type': 'crypto',
                    'exchange': exchange,
                    'category': parts[1],
                    'symbol': parts[2] if len(parts) > 2 else '',
                    'full_code': stdCode
                }
            
            # 传统金融品种
            else:
                return {
                    'type': 'traditional',
                    'exchange': exchange,
                    'symbol': '.'.join(parts[1:]) if len(parts) > 1 else '',
                    'full_code': stdCode
                }
        
        return {
            'type': 'unknown',
            'full_code': stdCode
        }
    
    test_codes = [
        "SSE.600000",           # 股票
        "SSE.ETF.510300",       # ETF  
        "SZSE.000001",          # 深圳股票
        "CRYPTO.CPT.BTC-USDT",  # 虚拟货币
        "SHFE.fu.HOT",          # 期货
        "NASDAQ.AAPL",          # 美股
        "INVALID",              # 无效代码
    ]
    
    print("代码解析测试:")
    for code in test_codes:
        try:
            info = parse_wt_code(code)
            print(f"  {code:20} -> {info['type']:12} | {info}")
        except Exception as e:
            print(f"  {code:20} -> ERROR: {e}")

def test_period_conversion_logic():
    """测试周期转换逻辑"""
    print("\n" + "=" * 60)
    print("测试周期转换逻辑")
    print("=" * 60)
    
    def convert_period(period: str) -> str:
        """简化的周期转换函数"""
        period_map = {
            'm1': 'min1',
            'm5': 'min5', 
            'd1': 'day',
            'day': 'day'
        }
        return period_map.get(period.lower(), 'day')
    
    test_periods = ['m1', 'm5', 'd1', 'day', 'invalid_period']
    
    print("周期转换测试:")
    for period in test_periods:
        try:
            converted = convert_period(period)
            print(f"  {period:15} -> {converted}")
        except Exception as e:
            print(f"  {period:15} -> ERROR: {e}")

def test_data_path_access():
    """测试数据路径访问"""
    print("\n" + "=" * 60)
    print("测试数据路径访问")
    print("=" * 60)
    
    try:
        from prepare_csv import load_config
        
        config = load_config()
        if not config:
            print("❌ 无法加载配置文件")
            return
        
        # 检查通达信数据路径
        tdx_data_root = config.get('tdx_data', {}).get('path')
        if tdx_data_root:
            if not os.path.isabs(tdx_data_root):
                config_dir = os.path.dirname(os.path.abspath(__file__))
                tdx_data_root = os.path.abspath(os.path.join(config_dir, tdx_data_root))
            
            print(f"通达信数据路径: {tdx_data_root}")
            if os.path.exists(tdx_data_root):
                print("  ✅ 通达信数据目录存在")
                # 列出一些子目录
                try:
                    subdirs = [d for d in os.listdir(tdx_data_root) if os.path.isdir(os.path.join(tdx_data_root, d))]
                    print(f"  子目录: {subdirs[:5]}{'...' if len(subdirs) > 5 else ''}")
                except:
                    pass
            else:
                print("  ❌ 通达信数据目录不存在")
        
        # 检查虚拟货币数据路径
        crypto_data_root = config.get('crypto_data', {}).get('path')
        if crypto_data_root:
            if not os.path.isabs(crypto_data_root):
                config_dir = os.path.dirname(os.path.abspath(__file__))
                crypto_data_root = os.path.abspath(os.path.join(config_dir, crypto_data_root))
            
            print(f"虚拟货币数据路径: {crypto_data_root}")
            if os.path.exists(crypto_data_root):
                print("  ✅ 虚拟货币数据目录存在")
                # 列出一些文件
                try:
                    files = [f for f in os.listdir(crypto_data_root) if f.endswith('.csv')]
                    print(f"  CSV文件: {files[:3]}{'...' if len(files) > 3 else ''}")
                except:
                    pass
            else:
                print("  ❌ 虚拟货币数据目录不存在")
                
    except Exception as e:
        print(f"❌ 数据路径测试异常: {e}")

def test_import_dependencies():
    """测试依赖模块导入"""
    print("\n" + "=" * 60)
    print("测试依赖模块导入")
    print("=" * 60)
    
    modules_to_test = [
        ('prepare_csv', '通达信数据处理模块'),
        ('prepare_csv_cpt', '虚拟货币数据处理模块'),
        ('pandas', 'Pandas数据处理库'),
        ('numpy', 'NumPy数值计算库'),
        ('pytz', '时区处理库'),
    ]
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"  ✅ {description}: {module_name}")
        except ImportError as e:
            print(f"  ❌ {description}: {module_name} - {e}")
        except Exception as e:
            print(f"  ⚠️ {description}: {module_name} - {e}")

def main():
    """主函数"""
    print("ExtDataLoader 基础功能测试")
    print("=" * 60)
    
    # 检查配置文件
    config_path = os.path.join(current_dir, 'config.json')
    if not os.path.exists(config_path):
        print(f"⚠️ 警告: 配置文件不存在: {config_path}")
        print("请先复制 config_template.json 为 config.json")
    else:
        print(f"✅ 配置文件存在: {config_path}")
    
    # 运行测试
    try:
        # 1. 测试依赖模块导入
        test_import_dependencies()
        
        # 2. 测试配置文件加载
        test_config_loading()
        
        # 3. 测试代码解析逻辑
        test_code_parsing_logic()
        
        # 4. 测试周期转换逻辑
        test_period_conversion_logic()
        
        # 5. 测试数据路径访问
        test_data_path_access()
        
        print("\n" + "=" * 60)
        print("✅ 基础功能测试完成!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n❌ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
