const { EventEmitter } = require('events')
const express = require('express')
const { authenticateToken } = require('../middleware/auth')
const { sequelize } = require('../database')

const { DrawingLine, IndicatorList } = require('../models'); // 从 models 统一入口导入模型


class ChartHandler extends EventEmitter {
    constructor() {
      super();
      this.router = express.Router();
      this.initRoutes();
    }

    initRoutes() {
      // 画线相关路由
      this.router.get('/drawinglines', authenticateToken, this.getDrawingLines.bind(this));
      this.router.post('/drawinglines', authenticateToken, this.saveDrawingLines.bind(this));

      // 指标列表相关路由
      this.router.get('/indicatorlist', authenticateToken, this.getIndicatorList.bind(this));
      this.router.post('/indicatorlist', authenticateToken, this.saveIndicatorList.bind(this));
    }

    getRouter() {
        return this.router;
      }

    // 获取指定品种的所有画线记录
    async getDrawingLines(req, res) {
      const { market, exchange, code } = req.query;

      try {
        console.log('[ChartHandler] 获取画线数据，market:', market, 'exchange:', exchange, 'code:', code);

        if (!market || !code) {
          console.error('[ChartHandler] 参数缺失');
          return res.status(400).json({
            success: false,
            error: 'Market and code parameters are required'
          });
        }

        // 构建 symbol 字符串
        const symbolObject = { market, exchange, code };

        // 对键按字母顺序排序，生成稳定 JSON 字符串
        const sortedSymbol = {};
        Object.keys(symbolObject)
          .sort() // 按字母顺序排序键
          .forEach(key => {
            if (symbolObject[key] !== undefined) { // 排除 undefined 字段
              sortedSymbol[key] = symbolObject[key];
            }
          });

        const symbolString = JSON.stringify(sortedSymbol);

        console.log('[ChartHandler] 获取画线数据，symbolString:', symbolString, ' userId:', req.user.id);

        // 获取该品种所有周期的画线记录
        const records = await DrawingLine.findAll({
          where: {
            symbol: symbolString,
            userId: req.user.id
          }
        });

        console.log('[ChartHandler] 查询到画线数据，:', JSON.stringify(records));

        res.json({
          success: true,
          data: records.map(record => ({
            symbol: JSON.parse(record.symbol),
            interval: record.interval,
            overlays: record.overlays
          }))
        });
      } catch (error) {
        console.error('[ChartHandler] 获取画线数据失败:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
      }
    }

    // 保存画线记录（一个品种一个周期一条记录）
    async saveDrawingLines(req, res) {
      const { symbol, interval, overlays } = req.body;

      try {
        let ss = JSON.parse(symbol);
        delete ss.name;

        // 对键按字母顺序排序，生成稳定 JSON 字符串
        const sortedSymbol = {};
        Object.keys(ss)
          .sort() // 按字母顺序排序键
          .forEach(key => {
            sortedSymbol[key] = ss[key];
          });

        const symbolString = JSON.stringify(sortedSymbol);

        console.log('[ChartHandler] 保存画线数据，symbolString:', symbolString, ' overlays:', overlays);

        // 使用 upsert 确保每个品种每个周期只有一条记录
        await DrawingLine.upsert({
          userId: req.user.id,
          symbol: symbolString,
          interval,
          overlays
        });

        res.json({ success: true });
      } catch (error) {
        console.error('[ChartHandler] 保存画线数据失败:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
      }
    }

    // 获取指标列表
    async getIndicatorList(req, res) {
      try {
        console.log('[ChartHandler] 获取指标列表，用户ID:', req.user.id);

        // 查询指标列表
        const record = await IndicatorList.findOne({
          where: { userId: req.user.id }
        });

        if (record) {
          console.log('[ChartHandler] 查询到指标列表');
          res.json({
            success: true,
            data: {
              id: record.id,
              indicators: record.indicators
            }
          });
        } else {
          console.log('[ChartHandler] 未查询到指标列表，返回空数据');
          res.json({
            success: true,
            data: {
              indicators: {}
            }
          });
        }
      } catch (error) {
        console.error('[ChartHandler] 获取指标列表失败:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
      }
    }

    // 保存指标列表
    async saveIndicatorList(req, res) {
      const { indicators } = req.body;

      try {
        console.log('[ChartHandler] 保存指标列表，用户ID:', req.user.id);

        if (!indicators) {
          console.error('[ChartHandler] 参数缺失');
          return res.status(400).json({
            success: false,
            error: 'Indicators parameter is required'
          });
        }

        // 使用 upsert 确保每个用户只有一条记录
        await IndicatorList.upsert({
          userId: req.user.id,
          indicators
        });

        res.json({ success: true });
      } catch (error) {
        console.error('[ChartHandler] 保存指标列表失败:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
      }
    }
  }

const chartHandler = new ChartHandler();
module.exports = { chartHandler };
