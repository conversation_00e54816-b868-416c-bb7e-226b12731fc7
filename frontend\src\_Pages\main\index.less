.main-page {
  :global {
    .ant-pro-page-container-children-content {
      margin: 0;
    }
  }
}

.main-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .main-content {
    flex: 1;
    background: #fff;
    position: relative;
    
    .chart-content {
      position: relative;
      height: 100%;

      // Drawer 触发器样式
      .drawer-trigger {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 60px;
        background: #fff;
        border: 1px solid #f0f0f0;
        border-right: none;
        border-radius: 4px 0 0 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 999;
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);

        &:hover {
          background: #fafafa;
        }

        .anticon {
          color: #666;
          font-size: 12px;
        }
      }
    }
  }
}

// Drawer 样式覆盖
.main-drawer {
  position: absolute;
  
  .ant-drawer-content-wrapper {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  }
  
  .ant-drawer-body {
    padding: 0;
    height: 100%;
    overflow: auto;
  }
  
  .ant-drawer-header {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }
} 