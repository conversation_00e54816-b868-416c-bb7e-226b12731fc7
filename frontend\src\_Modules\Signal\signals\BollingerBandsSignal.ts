import { KLine, KLineData } from '@/shared_types/market';
import { BaseSignal, SignalParameter, SignalType } from '../BaseSignal';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';

/**
 * 布林带突破信号
 * 使用布林带判断价格突破
 * 当价格突破上轨时产生卖出信号（超买）
 * 当价格突破下轨时产生买入信号（超卖）
 */
export class BollingerBandsSignal extends BaseSignal {
  private period: number;
  private stdDev: number;
  private bounceRatio: number;

  constructor(config: SignalConfig) {
    super(config);
    
    // 设置参数
    this.period = this.parameters.period;
    this.stdDev = this.parameters.stdDev;
    this.bounceRatio = this.parameters.bounceRatio;

    // 验证参数
    if (this.stdDev <= 0) {
      throw new Error('Standard deviation multiplier must be positive');
    }
    if (this.bounceRatio <= 0 || this.bounceRatio >= 1) {
      throw new Error('Bounce ratio must be between 0 and 1');
    }
  }

  /**
   * 获取信号参数列表
   */
  static async getParameters(): Promise<SignalParameter[]> {
    return Promise.resolve([
      {
        name: 'period',
        paramType: 'number',
        default: 20,
        description: '计算周期',
        minValue: 2,
        maxValue: 100,
        step: 1
      },
      {
        name: 'stdDev',
        paramType: 'number',
        default: 2,
        description: '标准差倍数',
        minValue: 0.5,
        maxValue: 5,
        step: 0.1
      },
      {
        name: 'bounceRatio',
        paramType: 'number',
        default: 0.02,
        description: '回弹比例',
        minValue: 0.001,
        maxValue: 0.1,
        step: 0.001
      }
    ]);
  }

  /**
   * 计算移动平均线
   */
  private calculateMA(data: number[], period: number): number[] {
    const result: number[] = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
        continue;
      }
      
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += data[i - j];
      }
      result.push(sum / period);
    }
    return result;
  }

  /**
   * 计算标准差
   */
  private calculateStdDev(data: number[], ma: number[], period: number): number[] {
    const result: number[] = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
        continue;
      }

      let sumSquares = 0;
      for (let j = 0; j < period; j++) {
        sumSquares += Math.pow(data[i - j] - ma[i], 2);
      }
      result.push(Math.sqrt(sumSquares / period));
    }
    return result;
  }

  calculateSignals(kline: KLine, params: Record<string, any>): MarketEvents.SignalItem[] {
    const signals: MarketEvents.SignalItem[] = [];
    const period = params.period as number;
    const stdDev = params.stdDev as number;
    const bounceRatio = params.bounceRatio as number;

    const data = kline.data;

    // 提取收盘价
    const closes = data.map(k => k.close);

    // 计算中轨（移动平均线）
    const ma = this.calculateMA(closes, period);
    
    // 计算标准差
    const sd = this.calculateStdDev(closes, ma, period);

    // 计算布林带上下轨
    const upper = ma.map((value, i) => value + stdDev * sd[i]);
    const lower = ma.map((value, i) => value - stdDev * sd[i]);

    // 从period开始遍历，寻找突破点
    for (let i = period; i < data.length; i++) {
      const price = closes[i];
      const prevPrice = closes[i - 1];
      
      // 计算价格变化比例
      const priceChange = (price - prevPrice) / prevPrice;

      // 从下轨突破回升
      if (prevPrice < lower[i - 1] && price > lower[i] && priceChange > bounceRatio) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.BUY,
          price: price,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      }
      // 从上轨突破回落
      else if (prevPrice > upper[i - 1] && price < upper[i] && priceChange < -bounceRatio) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.SELL,
          price: price,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      }
    }

    return signals;
  }
} 