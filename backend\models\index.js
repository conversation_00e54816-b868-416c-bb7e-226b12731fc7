'use strict';

const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const process = require('process');
const { sequelize } = require('../database'); // 导入配置好的 sequelize 实例
const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
// const config = require(__dirname + '/../config/config.json')[env]; // 如果使用 config 文件
const db = {};

// let sequelize;
// if (config.use_env_variable) {
//   sequelize = new Sequelize(process.env[config.use_env_variable], config);
// } else {
//   sequelize = new Sequelize(config.database, config.username, config.password, config);
// }

fs
  .readdirSync(__dirname)
  .filter(file => {
    return (
      file.indexOf('.') !== 0 &&            // 不是隐藏文件
      file !== basename &&                 // 不是 index.js 自身
      file.slice(-3) === '.js' &&          // 是 JS 文件
      file.indexOf('.test.js') === -1       // 排除测试文件 (如果存在)
    );
  })
  .forEach(file => {
    // const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
    // 导入模型定义函数
    const modelDefinition = require(path.join(__dirname, file));
    // 调用函数，传入 sequelize 和 DataTypes
    const model = modelDefinition(sequelize, Sequelize.DataTypes);
    // 检查 model 是否有效以及 model.name 是否存在
    if (model && model.name) {
      db[model.name] = model; // 使用模型名作为 key (例如 'User', 'ChatMessage')
      console.log(`[模型加载] Loaded model: ${model.name} from ${file}`);
    } else {
      console.warn(`[模型加载警告] File ${file} did not export a valid Sequelize model.`);
    }
  });

const User = require(path.join(__dirname, 'User'))(sequelize, Sequelize.DataTypes);
const StrategyOwnership = require(path.join(__dirname, 'StrategyOwnership'))(sequelize, Sequelize.DataTypes);
const DrawingLine = require(path.join(__dirname, 'DrawingLine'))(sequelize, Sequelize.DataTypes);
const Shape = require(path.join(__dirname, 'Shape'))(sequelize, Sequelize.DataTypes);
const Pool = require(path.join(__dirname, 'Pool'))(sequelize, Sequelize.DataTypes);
const SystemPoolMetadata = require(path.join(__dirname, 'SystemPoolMetadata'))(sequelize, Sequelize.DataTypes);
const IndicatorList = require(path.join(__dirname, 'IndicatorList'))(sequelize, Sequelize.DataTypes);
const LiveStrategy = require(path.join(__dirname, 'LiveStrategy'))(sequelize, Sequelize.DataTypes);
// 新增：多组合相关模型
const Group = require(path.join(__dirname, 'Group'))(sequelize, Sequelize.DataTypes);
const GroupStrategy = require(path.join(__dirname, 'GroupStrategy'))(sequelize, Sequelize.DataTypes);
const GroupPosition = require(path.join(__dirname, 'GroupPosition'))(sequelize, Sequelize.DataTypes);

db.User = User;
db.StrategyOwnership = StrategyOwnership;
db.DrawingLine = DrawingLine;
db.Shape = Shape;
db.Pool = Pool;
db.SystemPoolMetadata = SystemPoolMetadata;
db.IndicatorList = IndicatorList;
db.LiveStrategy = LiveStrategy;
// 新增：多组合相关模型
db.GroupStrategy = GroupStrategy;
db.GroupPosition = GroupPosition;

Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    // 调用 associate 方法建立关联，传入包含所有模型的 db 对象
    db[modelName].associate(db);
    console.log(`[模型关联] ${modelName} associations applied.`);
  } else {
    console.log(`[模型关联] ${modelName} has no associate method.`);
  }
});

// 将 sequelize 实例和 Sequelize 类添加到 db 对象中
db.sequelize = sequelize;
db.Sequelize = Sequelize;

console.log('[模型加载完成] 所有有效模型已加载并关联:', Object.keys(db).filter(k => k !== 'sequelize' && k !== 'Sequelize').join(', '));

module.exports = db; // 导出包含所有模型和 sequelize 实例的 db 对象