import { SignalDefinition } from './BaseSignal';

// 导入所有信号实现类
import { MACrossSignal } from './signals/MACrossSignal';
import { RSISignal } from './signals/RSISignal';
import { MACDSignal } from './signals/MACDSignal';
import { BollingerBandsSignal } from './signals/BollingerBandsSignal';
import { DonchianChannelSignal } from './signals/DonchianChannelSignal';
import { VWAPDeviationSignal } from './signals/VWAPDeviationSignal';
import { FutureReturnSignal } from './signals/FutureReturnSignal';
import { ShapeSignal } from './signals/ShapeSignal';

// 可用的信号配置基础定义
export const SIGNAL_DEFINITIONS = [
  {
    name: 'MA交叉',
    description: '双均线交叉信号，通过快慢均线的交叉来判断趋势转折点',
    signalClass: MACrossSignal,
    signalClassName: 'MACrossSignal'
  },
  {
    name: 'RSI超买超卖',
    description: 'RSI超买超卖信号，通过RSI指标的超买超卖状态判断交易时机',
    signalClass: RSISignal,
    signalClassName: 'RSISignal'
  },
  {
    name: 'MACD金叉死叉',
    description: 'MACD金叉死叉信号，通过MACD指标的金叉死叉来判断趋势转折点',
    signalClass: MACDSignal,
    signalClassName: 'MACDSignal'
  },
  {
    name: '布林带突破',
    description: '布林带突破信号，通过价格突破布林带上下轨来判断交易时机',
    signalClass: BollingerBandsSignal,
    signalClassName: 'BollingerBandsSignal'
  },
  {
    name: '唐奇安通道',
    description: '唐奇安通道突破信号，通过价格突破最高价和最低价通道来判断趋势',
    signalClass: DonchianChannelSignal,
    signalClassName: 'DonchianChannelSignal'
  },
  {
    name: 'VWAP偏离',
    description: 'VWAP偏离信号，通过价格与成交量加权平均价的偏离程度来判断超买超卖',
    signalClass: VWAPDeviationSignal,
    signalClassName: 'VWAPDeviationSignal'
  },
  {
    name: '未来涨幅',
    description: '未来涨幅信号，检查未来x周期内的价格涨幅是否达到指定阈值',
    signalClass: FutureReturnSignal,
    signalClassName: 'FutureReturnSignal'
  },
  {
    name: '形态匹配',
    description: '形态匹配信号，通过匹配已保存的形态配置来判断当前市场状态',
    signalClass: ShapeSignal,
    signalClassName: 'ShapeSignal'
  }
];

// 异步加载所有信号的参数定义
export async function loadSignalDefinitions(): Promise<SignalDefinition[]> {
  const definitions: SignalDefinition[] = [];
  
  for (const def of SIGNAL_DEFINITIONS) {
    try {
      const parameters = await def.signalClass.getParameters();

      console.log('获取信号参数定义：', parameters);

      definitions.push({
        name: def.name,
        description: def.description,
        parameters,
        signalClassName: def.signalClassName,
        klines: []
      });
    } catch (error) {
      console.error(`Failed to load parameters for ${def.name}:`, error);
    }
  }
  
  return definitions;
}

// 导出所有信号类
export {
  MACrossSignal,
  RSISignal,
  MACDSignal,
  BollingerBandsSignal,
  DonchianChannelSignal,
  VWAPDeviationSignal,
  FutureReturnSignal,
  ShapeSignal
};
