// frontend/src/_Dispatchers/MockLiveStrategyData.ts
import { StrategyEvents } from '../events/events';

// 模拟实盘策略数据
let mockLiveStrategies: StrategyEvents.LiveStrategyInfo[] = [];

// 生成随机ID
const generateId = () => Math.random().toString(36).substring(2, 15);

// 部署策略到实盘（模拟）
export const mockDeployToLive = (config: StrategyEvents.LiveStrategyConfig): Promise<{success: boolean, message?: string}> => {
  return new Promise((resolve) => {
    // 模拟网络延迟
    setTimeout(() => {
      try {
        // 检查策略是否已存在
        const existingIndex = mockLiveStrategies.findIndex(s => s.strategyId === config.strategyId);
        
        if (existingIndex >= 0) {
          // 更新现有策略
          mockLiveStrategies[existingIndex] = {
            ...mockLiveStrategies[existingIndex],
            accountId: config.accountId,
            lastUpdateTime: new Date().toISOString()
          };
          
          resolve({
            success: true,
            message: '策略配置已更新'
          });
        } else {
          // 创建新策略
          const newStrategy: StrategyEvents.LiveStrategyInfo = {
            id: generateId(),
            strategyId: config.strategyId,
            name: `策略 ${mockLiveStrategies.length + 1}`,
            status: 'stopped',
            accountId: config.accountId,
            lastUpdateTime: new Date().toISOString(),
            performance: {
              totalReturn: 0,
              dailyReturn: 0,
              positions: []
            }
          };
          
          mockLiveStrategies.push(newStrategy);
          
          resolve({
            success: true,
            message: '策略已成功部署到实盘'
          });
        }
      } catch (error) {
        resolve({
          success: false,
          message: error instanceof Error ? error.message : '部署失败'
        });
      }
    }, 1000); // 1秒延迟
  });
};

// 获取实盘策略列表（模拟）
export const mockGetLiveStrategies = (): Promise<{success: boolean, data: StrategyEvents.LiveStrategyInfo[]}> => {
  return new Promise((resolve) => {
    // 模拟网络延迟
    setTimeout(() => {
      // 如果没有数据，创建一些模拟数据
      if (mockLiveStrategies.length === 0) {
        mockLiveStrategies = [
          {
            id: generateId(),
            strategyId: 'strategy-1',
            name: '多因子轮动策略 1',
            status: 'running',
            accountId: 'account1',
            startTime: new Date(Date.now() - ******** * 5).toISOString(), // 5天前
            lastUpdateTime: new Date().toISOString(),
            performance: {
              totalReturn: 8.45,
              dailyReturn: 0.75,
              positions: [
                { symbol: 'SSE.510300', volume: 100, price: 4.25, profit: 125 },
                { symbol: 'SSE.510500', volume: 200, price: 7.85, profit: 230 }
              ]
            }
          },
          {
            id: generateId(),
            strategyId: 'strategy-2',
            name: '趋势跟踪策略',
            status: 'stopped',
            accountId: 'account2',
            startTime: new Date(Date.now() - ******** * 10).toISOString(), // 10天前
            lastUpdateTime: new Date(Date.now() - ******** * 2).toISOString(), // 2天前
            performance: {
              totalReturn: -2.35,
              dailyReturn: 0,
              positions: []
            }
          }
        ];
      }
      
      // 更新一些动态数据
      mockLiveStrategies = mockLiveStrategies.map(strategy => {
        if (strategy.status === 'running') {
          // 为运行中的策略生成一些随机变化
          const dailyChange = (Math.random() * 2 - 1) * 0.5; // -0.5% 到 0.5% 的随机变化
          const totalReturn = strategy.performance?.totalReturn || 0;
          
          return {
            ...strategy,
            lastUpdateTime: new Date().toISOString(),
            performance: {
              ...strategy.performance,
              dailyReturn: dailyChange,
              totalReturn: totalReturn + dailyChange
            }
          };
        }
        return strategy;
      });
      
      resolve({
        success: true,
        data: mockLiveStrategies
      });
    }, 800); // 0.8秒延迟
  });
};

// 启动实盘策略（模拟）
export const mockStartLiveStrategy = (id: string): Promise<{success: boolean, message?: string}> => {
  return new Promise((resolve) => {
    // 模拟网络延迟
    setTimeout(() => {
      const strategyIndex = mockLiveStrategies.findIndex(s => s.id === id);
      
      if (strategyIndex >= 0) {
        // 更新策略状态
        mockLiveStrategies[strategyIndex] = {
          ...mockLiveStrategies[strategyIndex],
          status: 'running',
          startTime: new Date().toISOString(),
          lastUpdateTime: new Date().toISOString()
        };
        
        resolve({
          success: true,
          message: '策略已成功启动'
        });
      } else {
        resolve({
          success: false,
          message: '未找到指定策略'
        });
      }
    }, 1200); // 1.2秒延迟
  });
};

// 停止实盘策略（模拟）
export const mockStopLiveStrategy = (id: string): Promise<{success: boolean, message?: string}> => {
  return new Promise((resolve) => {
    // 模拟网络延迟
    setTimeout(() => {
      const strategyIndex = mockLiveStrategies.findIndex(s => s.id === id);
      
      if (strategyIndex >= 0) {
        // 更新策略状态
        mockLiveStrategies[strategyIndex] = {
          ...mockLiveStrategies[strategyIndex],
          status: 'stopped',
          lastUpdateTime: new Date().toISOString()
        };
        
        resolve({
          success: true,
          message: '策略已成功停止'
        });
      } else {
        resolve({
          success: false,
          message: '未找到指定策略'
        });
      }
    }, 1200); // 1.2秒延迟
  });
};
