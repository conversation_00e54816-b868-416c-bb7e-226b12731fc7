import sys
import os
# 确保可以import strategy.base包
strategy_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
if strategy_dir not in sys.path:
    sys.path.insert(0, strategy_dir)
import yaml
import json
import importlib
import logging
import redis
import time
import pandas as pd
from typing import Dict, Any, Set, Optional
import argparse
import threading
import codecs
import pytz
from datetime import datetime
import queue
import requests

# 新增：引入基础类
from strategy.base.BaseCtaStrategy import BaseCtaStrategy
from strategy.base.LiveContext import LiveContext

from redis import AuthenticationError, ConnectionError

os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# ===================== 日志配置(每日分文件，独立写入) =====================
class BeijingTimeFormatter(logging.Formatter):
    """自定义日志格式化器，使用北京时间并包含毫秒"""
    def converter(self, timestamp):
        dt = datetime.fromtimestamp(timestamp, tz=pytz.utc)
        return dt.astimezone(pytz.timezone('Asia/Shanghai'))

    def formatTime(self, record, datefmt=None):
        dt = self.converter(record.created)
        if datefmt:
            s = dt.strftime(datefmt)
        else:
            s = dt.strftime('%Y-%m-%d %H:%M:%S')
        return f"{s}.{int(record.msecs):03d}"

class DailyRotatingFileHandler(logging.Handler):
    """
    自定义日志Handler，每次emit前检查日期，日期变更时切换日志文件，严格按天分文件。
    """
    def __init__(self, log_dir, formatter):
        super().__init__()
        self.log_dir = log_dir
        self.formatter = formatter
        self.current_date = None
        self.file_handler = None
        self._open_new_file()

    def _get_today(self):
        beijing_tz = pytz.timezone('Asia/Shanghai')
        return datetime.now(beijing_tz).strftime('%Y-%m-%d')

    def _open_new_file(self):
        today = self._get_today()
        if self.file_handler:
            self.file_handler.close()
        log_file = os.path.join(self.log_dir, f'{today}.log')
        self.file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        self.file_handler.setFormatter(self.formatter)
        self.current_date = today

    def emit(self, record):
        today = self._get_today()
        if today != self.current_date or self.file_handler is None:
            self._open_new_file()
        if self.file_handler:
            self.file_handler.emit(record)

    def close(self):
        if self.file_handler:
            self.file_handler.close()
        super().close()

def setup_logging(user_id: str):
    """
    配置日志系统，每天一个日志文件，文件名为 logs/YYYY-MM-DD.log，
    日志目录为 live_engine.py 所在目录下 logs/。
    只写文件，不输出到控制台。
    """
    log_root = logging.getLogger()
    log_root.setLevel(logging.INFO)
    if log_root.hasHandlers():
        log_root.handlers.clear()
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    formatter = BeijingTimeFormatter(
        '[用户引擎][%(asctime)s] [%(levelname)s] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    daily_handler = DailyRotatingFileHandler(log_dir, formatter)
    log_root.addHandler(daily_handler)
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成，日志文件: {os.path.join(log_dir, datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d') + '.log')}")

    # ========== 新增：重定向 stdout/stderr 到日志文件 ==========
    import sys
    class LoggerWriter:
        def __init__(self, logger_func):
            self.logger_func = logger_func
            self._buffer = ""
        def write(self, message):
            self._buffer += message
            while '\n' in self._buffer:
                line, self._buffer = self._buffer.split('\n', 1)
                self.logger_func(line)
        def flush(self):
            if self._buffer:
                self.logger_func(self._buffer)
                self._buffer = ""
    sys.stdout = LoggerWriter(logger.info)
    sys.stderr = LoggerWriter(logger.error)

# 全局变量，将在 main 函数中被初始化
logger = logging.getLogger(__name__)

# ===================== 策略管理器 =====================
class StrategyEngine:
    '''
    用户独立实盘执行引擎，负责：
    - 动态添加/删除策略
    - 维护运行中策略实例列表
    - 通过Redis订阅数据推送，按k线级别分发
    '''
    def convert_sets_to_lists(self, obj):
        """递归将所有set转为list，保证json序列化兼容"""
        if isinstance(obj, dict):
            return {k: self.convert_sets_to_lists(v) for k, v in obj.items()}
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, list):
            return [self.convert_sets_to_lists(i) for i in obj]
        else:
            return obj

    def convert_lists_to_sets(self, obj):
        """递归将所有list转为set，供需要set的字段恢复用"""
        if isinstance(obj, dict):
            return {k: self.convert_lists_to_sets(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            # 只在明确需要set的地方调用
            return set(obj)
        else:
            return obj

    def save_engine_snapshot(self):
        """保存引擎快照到live_engine.py同级目录，原子写入，仅包含运行时状态（不含任何策略元数据）"""
        engine_dir = os.path.dirname(os.path.abspath(__file__))
        tmp_path = os.path.join(engine_dir, f'engine_snapshot_{self.user_id}.json.tmp')
        final_path = os.path.join(engine_dir, f'engine_snapshot_{self.user_id}.json')
        # 只收集运行时状态，不保存任何元数据
        strategies_info = []

        # for sid, iid in self.livestrategy_id_to_instance_id.items():
        for live_strategy_id, instance in self.instancies.items():

            context = self.strategy_contexts.get(live_strategy_id)

            # 新增：获取运行状态
            status = self.strategy_status.get(live_strategy_id, 'running') if hasattr(self, 'strategy_status') else 'running'
            info = {
                "strategy_id": live_strategy_id,
                # "instance_id": iid,
                "channel_id": self.strategy_channel_map.get(live_strategy_id),
                "initial_capital": getattr(context, 'initial_capital', None) if context else None,
                "commodity_info": getattr(context, 'commodity_info', None) if context else None,
                "symbols": list(self.strategy_symbols.get(live_strategy_id, [])),
                "timeframe": getattr(self.instancies.get(live_strategy_id), 'timeframe', None),
                "status": status  # 新增字段
            }

            strategies_info.append(info)

        snapshot = {
            "strategies": strategies_info,
            # "strategy_id_to_instance_id": self.livestrategy_id_to_instance_id,
            "strategy_channel_map": self.strategy_channel_map,
            "strategy_ready_map": self.strategy_ready_map,
            "strategy_symbols": self.strategy_symbols,
            "strategy_positions": self.strategy_positions,
            "logical_positions": self.logical_positions
        }

        try:
            snapshot_serializable = self.convert_sets_to_lists(snapshot)
            with open(tmp_path, 'w', encoding='utf-8') as f:
                json.dump(snapshot_serializable, f, ensure_ascii=False, indent=2)
            os.replace(tmp_path, final_path)
            self.logger.info(f"[持久化] 已保存引擎快照到 {final_path}")
        except Exception as e:
            self.logger.error(f"[持久化] 保存引擎快照失败: {e}")

    def load_engine_snapshot(self):
        """启动时自动恢复快照，仅恢复运行时状态，所有策略元数据由调用端传入"""
        engine_dir = os.path.dirname(os.path.abspath(__file__))
        snapshot_path = os.path.join(engine_dir, f'engine_snapshot_{self.user_id}.json')
        if not os.path.exists(snapshot_path):
            self.logger.info(f"[持久化] 未检测到快照文件 {snapshot_path}，按全新状态启动")
            return
        try:
            with open(snapshot_path, 'r', encoding='utf-8') as f:
                snapshot = json.load(f)

            # self.livestrategy_id_to_instance_id = snapshot.get("strategy_id_to_instance_id", {})
            self.strategy_channel_map = snapshot.get("strategy_channel_map", {})
            self.strategy_ready_map = snapshot.get("strategy_ready_map", {})
            raw_symbols = snapshot.get("strategy_symbols", {})
            self.strategy_symbols = {k: set(v) if isinstance(v, list) else v for k, v in raw_symbols.items()}
            self.strategy_positions = snapshot.get("strategy_positions", {})
            self.logical_positions = snapshot.get("logical_positions", {})
            
            # 新增：恢复策略状态
            strategies_info = snapshot.get("strategies", [])
            for strategy_info in strategies_info:
                strategy_id = strategy_info.get("strategy_id")
                status = strategy_info.get("status", "running")
                if strategy_id:
                    self.strategy_status[strategy_id] = status
                    self.logger.info(f"[持久化] 恢复策略 {strategy_id} 状态为: {status}")
            
            self.logger.info(f"[持久化] 已恢复引擎快照 {snapshot_path}")
        except Exception as e:
            self.logger.error(f"[持久化] 加载引擎快照失败: {e}")

    def __init__(self, user_id: str, redis_host='localhost', redis_port=6379, redis_db=0, redis_password=None):
        self.user_id = user_id
        self.instancies: Dict[str, BaseCtaStrategy] = {}  # id -> 实例

        # self.livestrategy_id_to_instance_id: Dict[str, str] = {}  # 新增：保存strategy_id到instance_id的映射
        self.redis = redis.StrictRedis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db,
            password=redis_password,
            decode_responses=True
        )
        self.pubsub = self.redis.pubsub()
        self.logger = logger  # 让LiveContext等可以通过self._engine.logger访问日志
        self.jwt_token = None  # 新增：用于存储最新的JWT token
        self.strategy_contexts = {}  # 新增：保存每个策略的唯一context
        self.strategy_channel_map = {}  # 新增：保存strategy_id到channel_id的映射
        self.strategy_status = {}  # 新增：保存每个策略的运行状态 live_strategy_id -> status

        # 打印详细连接参数
        logger.info('引擎初始化完成，连接Redis %s:%d, db=%d', redis_host, redis_port, redis_db)

        # 本地数据缓存，key: (symbol, period)，value: list of bar dicts
        self.data_cache: Dict[tuple, Any] = {}
        # 每个策略的品种准备状态表，key: strategy_id, value: {symbol: bool}
        self.strategy_ready_map: Dict[str, Dict[str, bool]] = {}
        # 每个策略需要的品种列表，key: strategy_id, value: set(symbol)
        self.strategy_symbols: Dict[str, set] = {}
        self.data_queue = queue.Queue()  # 新增：用于线程间传递行情数据

        # 新增：每策略每品种的目标仓位
        self.strategy_positions = {}  # {strategy_id: {symbol_id: target_position}}
        # 新增：每通道每品种的总目标仓位
        self.logical_positions = {}   # {channel_id: {symbol_id: total_target_position}}

        # 增加实际策略运行仓位
        self.real_positions = {}


        # 启动时尝试加载持久化实际仓位
        try:
            file_path = f'actual_positions_{user_id}.json'
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.logical_positions = json.load(f)
                logger.info(f"[持久化] 已加载本地持久化实际仓位: {self.logical_positions}")
        except Exception as e:
            logger.error(f"[持久化] 加载本地实际仓位失败: {e}")

        # 启动时自动恢复快照
        self.load_engine_snapshot()

    # 频道名生成函数，必须与推送方逻辑完全一致
    def gen_kline_channel(self, period, symbol):
        """
        symbol: 形如 'SSE.ETF.518880' 或 'SZSE.ETF.159509'，或 'sh:600000' 等
        返回频道名如 'kline.1D.sh:600000'
        """
        # 统一周期为1D
        if period == 'day':
            period = '1D'
        # 先转为 tdx 格式
        if symbol.startswith('SSE.ETF.'):
            tdx_symbol = 'sh:' + symbol.split('.')[-1]
        elif symbol.startswith('SZSE.ETF.'):
            tdx_symbol = 'sz:' + symbol.split('.')[-1]
        elif symbol.startswith('sh:') or symbol.startswith('sz:'):
            tdx_symbol = symbol
        else:
            tdx_symbol = symbol  # 其它情况直接用原始
        return f"kline.{period}.{tdx_symbol}"

    @staticmethod
    def adapt_symbol_for_market(symbol: str) -> str:
        """
        行情数据源symbol适配中心：
        - ETF: 'SSE.ETF.513090' -> 'sh:513090', 'SZSE.ETF.159650' -> 'sz:159650'
        - 已是 'sh:xxxxxx' 或 'sz:xxxxxx' 直接返回
        - 其它原样返回
        """
        if symbol.startswith('SSE.ETF.'):
            return 'sh:' + symbol.split('.')[-1]
        elif symbol.startswith('SZSE.ETF.'):
            return 'sz:' + symbol.split('.')[-1]
        elif symbol.startswith('sh:') or symbol.startswith('sz:'):
            return symbol
        else:
            return symbol

    @staticmethod
    def adapt_and_call_market_api(symbol: str, period: str, action: str, count: int = 300, include_unclosed: bool = False, key: str = ''):
        """
        统一适配并调用行情数据源API。
        action: 'subscribe' 或 'unsubscribe'
        """
        tdx_symbol = StrategyEngine.adapt_symbol_for_market(symbol)
        if action == 'subscribe':
            url = 'http://127.0.0.1:5003/subscribe_kline'
            payload = {"period": period, "symbol": tdx_symbol, "count": count, "include_unclosed": include_unclosed, "key": key}
        elif action == 'unsubscribe':
            url = 'http://127.0.0.1:5003/unsubscribe_kline'
            payload = {"period": period, "symbol": tdx_symbol, "key": key}
        else:
            raise ValueError(f"不支持的action: {action}")
        try:
            resp = requests.post(url, json=payload)
            logging.getLogger(__name__).info(f"[行情适配中心] {action} 品种 {tdx_symbol}，period={period}，key={key}，返回: {resp.text}")
            return resp
        except Exception as e:
            logging.getLogger(__name__).error(f"[行情适配中心] {action} 品种 {tdx_symbol} 时HTTP请求失败: {e}")
            return None

    def add_strategy(self, live_strategy_id, yaml, strategy_type, jwt_token, channel_id, commodity, timeframe, initial_capital):
        '''
        添加策略实例，yaml_path为策略配置文件路径，strategy_type为策略类型，channel_id为交易通道。
        '''
        logger.info('add_strategy被调用，live_strategy_id: %s, strategy_type: %s, channel_id: %s, initial_capital: %s', live_strategy_id, strategy_type, channel_id, initial_capital)

        try:
            #with open(yaml_path, 'r', encoding='utf-8') as f:
            #    yaml_config = yaml.safe_load(f)

            # 分发到具体策略类型的实例化函数
            # 修改：接收commodity_info
            instance, symbols, timeframe, commodity_info = self.create_strategy_instance(live_strategy_id, strategy_type, yaml)

            if instance is None or symbols is None or timeframe is None:
                logger.error(f'策略实例创建失败: type={strategy_type}, live_strategy_id={live_strategy_id}')
                return
            
            # 1. 先本地订阅相关频道（用统一频道名生成函数）
            channels_to_subscribe = [self.gen_kline_channel(timeframe, s) for s in symbols]
            if channels_to_subscribe:
                self.pubsub.subscribe(*channels_to_subscribe)
                logger.info(f"策略 {live_strategy_id} 已自动订阅频道: {channels_to_subscribe}")
            # 2. 再通过 HTTP API 注册所有品种订阅
            for s in symbols:
                self.adapt_and_call_market_api(s, timeframe, 'subscribe', count=300, include_unclosed=False, key=live_strategy_id)
            logger.info(f"添加策略: 实盘策略ID={live_strategy_id}, type={strategy_type}, timeframe={timeframe}, symbols={symbols}，instance={instance}")

            # 创建strategy_id到instance_id的映射
            # self.livestrategy_id_to_instance_id[live_strategy_id] = instance_id

            # 注册到引擎
            self.instancies[live_strategy_id] = instance
            self.strategy_symbols[live_strategy_id] = set(symbols)
            self.strategy_ready_map[live_strategy_id] = {symbol: False for symbol in symbols}
            # 新增：实例化LiveContext并调用on_init
            context = LiveContext(live_strategy_id, self, channel_id)

            # 新增：赋值初始资金
            context.initial_capital = initial_capital

            # 新增：挂commodity_info到context
            context.commodity_info = commodity_info

            self.strategy_channel_map[live_strategy_id] = channel_id  # 新增：维护strategy_id到channel_id的映射
            self.strategy_contexts[live_strategy_id] = context  # 保存唯一context

            # 新增：设置运行状态
            self.strategy_status[live_strategy_id] = 'running'
            # 新增：保存JWT token
            if jwt_token:
                self.jwt_token = jwt_token
                logger.info(f"[add_strategy] 已保存JWT token，长度: {len(jwt_token)}")
            instance.on_init(context)
            logger.info(f'添加策略: id={live_strategy_id}, type={strategy_type}, timeframe={timeframe}, symbols={symbols}, initial_capital={context.initial_capital}')
            self.save_engine_snapshot() # 保存快照
        except Exception as e:
            logger.error(f'添加策略失败: {e}', exc_info=True)

    def create_strategy_instance(self, live_strategy_id, strategy_type: str, yaml_str: str):
        """
        根据策略类型分发到具体的实例化函数，返回(实例, strategy_id, symbols, timeframe)
        """
        if strategy_type == 'portfolio':
            # 解析YAML字符串为字典
            try:
                yaml_config = yaml.safe_load(yaml_str) if isinstance(yaml_str, str) else yaml_str
                return self.create_portfolio_strategy_instance(live_strategy_id, yaml_config)
            except Exception as e:
                logger.error(f"解析YAML配置失败: {e}")
                return None, None, None, {}
        # 未来可扩展其他类型
        logger.error(f"暂不支持的策略类型: {strategy_type}")
        return None, None, None, {}

    def create_portfolio_strategy_instance(self, live_strategy_id, yaml_config: dict):
        """
        创建多因子（portfolio）类型策略实例，解析yaml_config，返回(实例, strategy_id, symbols, timeframe)
        """
        try:
            import os, json
            # 兼容顶层和 params 嵌套两种格式
            params = yaml_config.get('params', {})
            name = yaml_config.get('name') or yaml_config.get('strategy_name') or params.get('name') or params.get('strategy_name', 'portfolio')

            if not live_strategy_id:
                logger.error("实盘策略ID不能为空")
                return None, None, None, {}
            codes = yaml_config.get('universe') or params.get('universe') or yaml_config.get('symbols') or params.get('symbols') or []
            if not codes:
                logger.error(f"策略 {live_strategy_id} 缺少品种列表 (universe/symbols)")
                return None, None, None, {}
            
            timeframe = yaml_config.get('data_freq') or params.get('data_freq') or yaml_config.get('timeframe') or params.get('timeframe', '1D')
            # ===== 新增：周期转换 day/week/month -> 1D/1W/1M =====
            period_map = {'day': '1D', 'week': '1W', 'month': '1M'}
            if isinstance(timeframe, str):
                timeframe = period_map.get(timeframe.lower(), timeframe)
            barCnt = yaml_config.get('bar_count') or params.get('bar_count') or yaml_config.get('bar_cnt') or params.get('bar_cnt') or yaml_config.get('window') or params.get('window') or yaml_config.get('lookback') or params.get('lookback') or 100

            # 新增：解析 order_by、buy_rules、sell_rules 配置
            order_by_config = yaml_config.get('order_by', {}) or params.get('order_by', {})
            buy_rules_config = yaml_config.get('buy_rules', {}) or params.get('buy_rules', {})
            sell_rules_config = yaml_config.get('sell_rules', {}) or params.get('sell_rules', {})
            top_n = yaml_config.get('top_n', 1) if 'top_n' in yaml_config or 'top_n' not in params else params.get('top_n', 1)
            weighting_scheme = yaml_config.get('weighting_scheme', 'equal') if 'weighting_scheme' in yaml_config or 'weighting_scheme' not in params else params.get('weighting_scheme', 'equal')
            rebalance_interval = yaml_config.get('rebalance_interval', 'daily') if 'rebalance_interval' in yaml_config or 'rebalance_interval' not in params else params.get('rebalance_interval', 'daily')

            # ====== 新增：收集commodity信息 ======
            # 1. 获取类别字段，优先trading_type
            trading_type = yaml_config.get('trading_type') or params.get('trading_type')
            if trading_type:
                ttype = trading_type.strip().upper()
                if ttype == 'ETF':
                    commodity_key = 'ETF'
                elif ttype == 'STOCK':
                    commodity_key = 'STOCK'
                elif ttype == 'CRYPTO':
                    commodity_key = 'CRYPTO'
                else:
                    commodity_key = ttype  # 兜底
            else:
                # 默认ETF
                commodity_key = 'ETF'
            # 2. 查找commodity.json
            def find_commodity_json():
                # 使用与portfolio_center.py相同的backend目录查找逻辑
                cur = os.path.abspath(os.path.dirname(__file__))
                backend_dir = None
                for _ in range(10):
                    if os.path.basename(cur) == 'backend':
                        backend_dir = cur
                        break
                    parent = os.path.dirname(cur)
                    if parent == cur:
                        break
                    cur = parent
                
                if backend_dir:
                    # 从backend目录构建commodity.json的路径
                    candidate = os.path.join(backend_dir, '_Providers', '_Python', 'strategy', 'commodity.json')
                    if os.path.exists(candidate):
                        return candidate
                
                return None
            cjson_path = find_commodity_json()
            commodity_info = {}
            if cjson_path:
                with open(cjson_path, 'r', encoding='utf-8') as f:
                    cdata = json.load(f)
                cinfo = cdata.get(commodity_key, {})
                # 只提取需要的字段
                for k in ["order_quantity_step", "settlement_time", "t_plus_rule", "trading_days", "digits", "timezone"]:
                    commodity_info[k] = cinfo.get(k)
            else:
                logger.warning(f"[策略添加] 未找到commodity.json，commodity_info为空")
            # ====== END ======

            # 导入策略模块和类
            module_path = "strategy.base.MultiFactorsCTA_RT"
            module = importlib.import_module(module_path)
            strategy_class = getattr(module, "MultiFactorsCTA")
            logger.info(f'[DataThread] 策略实例化，参数：{name}, {codes}, {barCnt}, {timeframe}, {order_by_config}, {buy_rules_config}, {sell_rules_config}, {top_n}, {weighting_scheme}, {rebalance_interval}')
            # 实例化，传递所有参数
            instance = strategy_class(
                name,
                codes,
                commodity_info.get('digits', 2),
                barCnt,
                timeframe,
                order_by_config=order_by_config,
                buy_rules_config=buy_rules_config,
                sell_rules_config=sell_rules_config,
                top_n=top_n,
                weighting_scheme=weighting_scheme,
                rebalance_interval=rebalance_interval
            )
            # 新增：挂commodity_info到策略实例
            instance.commodity_info = commodity_info

            self.logger.info(f"已创建多因子策略实例: {live_strategy_id}, name={name}, codes={codes}, barCnt={barCnt}, timeframe={timeframe}, commodity_info={commodity_info}")

            return instance, codes, timeframe, commodity_info
        
        except Exception as e:
            self.logger.error(f"创建多因子策略实例失败: {e}", exc_info=True)
            return None, None, None, {}

    def remove_strategy(self, live_strategy_id: str):
        '''
        删除指定id的策略实例，并取消订阅相关数据。
        '''

        # 通过 strategy_id_to_instance_id 获取 instance_id
        # instance_id = self.livestrategy_id_to_instance_id.get(strategy_id)

        self.logger.info(f"[策略删除] 开始删除策略: live_strategy_id={live_strategy_id}")

        instance = self.instancies[live_strategy_id]
        
        if instance:
            timeframe = instance.timeframe
            symbols = self.strategy_symbols.get(live_strategy_id, set())
            # ====== 新增：自动平仓并清理本地持仓 ======
            positions = self.strategy_positions.get(live_strategy_id, {})
            channel_id = self.strategy_channel_map.get(live_strategy_id)

            for symbol, qty in positions.items():
                if qty != 0:
                    # 获取策略的初始资金比例，将逻辑仓位转换为实际仓位
                    context = self.strategy_contexts.get(live_strategy_id)
                    initial_capital = getattr(context, 'initial_capital', 100000)
                    ratio = initial_capital / 100000
                    real_qty = qty * ratio
                    
                    self.logger.info(f"[策略删除] 策略{live_strategy_id} 持仓 {symbol} 逻辑数量 {qty} 实际数量 {real_qty}，自动下达平仓指令（目标仓位=0）")
                    self.place_order(None, channel_id, symbol, -real_qty, True) # 平仓无所谓 commodity 设置

            # 删除本地持仓缓存
            if live_strategy_id in self.strategy_positions:
                del self.strategy_positions[live_strategy_id]

            # 重新聚合逻辑仓位
            self.aggregate_logical_positions()
            self.logger.info(f"[策略删除] 策略{live_strategy_id} 所有持仓已清零并同步逻辑仓位")

            # ====== 原有订阅/退订逻辑 ======
            # 自动取消订阅
            channels_to_unsubscribe = [self.gen_kline_channel(timeframe, s) for s in symbols]

            self.logger.info(f"[策略删除]频道退订， 策略{live_strategy_id} 取消订阅频道: {channels_to_unsubscribe}")

            if channels_to_unsubscribe:
                self.pubsub.unsubscribe(*channels_to_unsubscribe)
                self.logger.info(f"策略 {live_strategy_id} 已取消订阅频道: {channels_to_unsubscribe}")
            # 新增：通知tdxserver退订
            if timeframe is not None:
                for s in symbols:
                    self.adapt_and_call_market_api(s, timeframe, 'unsubscribe', key=live_strategy_id)
            else:
                self.logger.warning(f"[remove_strategy] timeframe 为 None，跳过退订 tdxserver，strategy_id={live_strategy_id}")

            # 新增：设置运行状态为stopped
            self.strategy_status[live_strategy_id] = 'stopped'
            del self.instancies[live_strategy_id]
            self.strategy_ready_map.pop(live_strategy_id, None)
            self.strategy_symbols.pop(live_strategy_id, None)
            self.logger.info(f'删除策略: live_strategy_id={live_strategy_id}')
            # ====== 所有状态变更完成后再保存快照 ======
            self.save_engine_snapshot()
        else:
            self.logger.warning(f'删除策略失败，未找到strategy_id={live_strategy_id}')

    def listen_for_control_commands(self):
        """在一个独立的线程中运行，监听控制指令"""
        logger.info("[ControlThread] 控制指令监听线程已启动，准备进入主循环...")
        control_pubsub = None
        try:
            control_channel = f"user_engine_control:{self.user_id}"
            control_pubsub = self.redis.pubsub()
            control_pubsub.subscribe(control_channel)
            logger.info(f"[ControlThread] 已成功订阅频道: {control_channel}")

            # 订阅成功后，立刻发布 "ready" 信号
            try:
                ready_signal_channel = f"user_engine_status:{self.user_id}"
                ready_message = json.dumps({"status": "ready", "pid": os.getpid()})
                self.redis.publish(ready_signal_channel, ready_message)
                logger.info(f"[ControlThread] 成功发布 'ready' 信号到频道 {ready_signal_channel}")
            except Exception as e:
                logger.error(f"[ControlThread] 发布 'ready' 信号失败: {e}", exc_info=True)

            while True:
                message = control_pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
                if message is None:
                    continue # 没有消息，继续下一次轮询
            
                try:
                    logger.debug(f"[ControlThread] 收到原始控制指令数据: {message['data']}")
                    command = json.loads(message['data'])
                    action = command.get('action')
                    logger.info(f"[ControlThread] 收到并解析控制指令: {command}")

                    # 新增：接收并保存jwt_token
                    if 'jwt_token' in command and command['jwt_token']:
                        self.jwt_token = command['jwt_token']
                        logger.info(f"[ControlThread] 已更新JWT token: {self.jwt_token[:20]}... (仅显示前20字符)")

                    if action == 'ping':
                        try:
                            # 如果ping命令中包含jwt_token，则更新
                            if 'jwt_token' in command and command['jwt_token']:
                                self.jwt_token = command['jwt_token']
                                logger.info(f"[ControlThread] ping时更新JWT token: {self.jwt_token[:20]}... (仅显示前20字符)")
                            
                            pong_channel = f"user_engine_status:{self.user_id}"
                            pong_message = json.dumps({"status": "pong", "pid": os.getpid()})
                            self.redis.publish(pong_channel, pong_message)
                            logger.info(f"[ControlThread] 收到ping指令，已回复pong到频道 {pong_channel}")
                        except Exception as e:
                            logger.error(f"[ControlThread] 回复pong失败: {e}", exc_info=True)

                    elif action == 'add':
                        #yaml_path = command.get('yaml_path')
                        yaml = command.get('yaml')
                        live_strategy_id = command.get('live_strategy_id')  # 修正：补充获取实盘策略 strategy_id
                        strategy_type = command.get('strategy_type', 'portfolio')  # 从命令中获取策略类型，默认为portfolio
                        channel_id = command.get('channel_id')  # 新增：接收channel_id
                        jwt_token = command.get('jwt_token') # 新增：接收jwt_token
                        commodity = command.get('commodity') # 新增：接收commodity
                        timeframe = command.get('timeframe') # 新增：接收timeframe
                        initial_capital = command.get('initial_capital') # 新增：获取初始资金

                        self.add_strategy(live_strategy_id, yaml, strategy_type, jwt_token, channel_id, commodity, timeframe, initial_capital)

                        logger.info(f"[ControlThread] 添加策略，使用策略类型: {strategy_type}，channel_id: {channel_id}")
                    elif action == 'remove':
                        live_strategy_id = command.get('strategy_id')
                        if live_strategy_id:
                            self.remove_strategy(live_strategy_id)
                        else:
                            logger.error("[ControlThread] 控制指令 'remove' 缺少 'strategy_id' 参数")
                    
                    else:
                        logger.warning(f"[ControlThread] 收到未知的控制指令: {action}")

                except json.JSONDecodeError as e:
                    logger.error(f"[ControlThread] 解析控制指令JSON失败: {e}. 原始数据: {message.get('data')}", exc_info=True)
                except Exception as e:
                    logger.error(f"[ControlThread] 处理单条控制指令时出错: {e}", exc_info=True)
                    
                time.sleep(0.01) # 防止CPU空转

        # 然后修改异常处理：
        except AuthenticationError as e:
            logger.error(f"[ControlThread] Redis认证失败！请检查密码。线程将终止。错误: {e}", exc_info=True)
        except ConnectionError as e:
            logger.error(f"[ControlThread] 无法连接到Redis！请检查主机和端口。线程将终止。错误: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"[ControlThread] 控制指令监听线程发生未知严重错误，线程将终止: {e}", exc_info=True)
        finally:
            if control_pubsub:
                control_pubsub.close()
            logger.info("[ControlThread] 线程已终止并清理资源。")

    def listen_for_market_data(self):
        """阻塞式监听行情数据频道，始终监听，永不退出"""
        logger.info('[MarketDataThread] 行情数据接收线程已启动...')
        # 启动时先订阅一个占位频道，保证listen阻塞
        self.pubsub.subscribe('kline.placeholder.dummy')
        logger.info(f'[MarketDataThread] 当前已订阅频道: {list(self.pubsub.channels.keys())}')
        while True:
            for message in self.pubsub.listen():  # 阻塞式
                if message['type'] != 'message':
                    continue
                logger.info(f'[MarketDataThread] 收到频道: {message["channel"]}')
                try:
                    channel = message['channel']
                    channel_parts = channel.split('.')
                    if len(channel_parts) != 3 or channel_parts[0] != 'kline':
                        continue
                    period, symbol = channel_parts[1], channel_parts[2]
                    kline_data = json.loads(message['data'])
                    logger.info(f'[MarketDataThread] 收到频道: {channel}，k线条数: {len(kline_data) if hasattr(kline_data, "__len__") else "?"}')
                    if not kline_data:
                        logger.warning(f"[MarketDataThread] 收到空K线数据，跳过")
                        continue
                    # 推送到本地队列
                    self.data_queue.put((symbol, period, kline_data))
                except Exception as e:
                    logger.error(f"[MarketDataThread] 处理行情数据时异常: {e}", exc_info=True)

    def tdx_symbol_to_original(self, tdx_symbol):
        # 只处理ETF品种，按推送方和策略配置的映射规则
        if tdx_symbol.startswith('sh:'):
            return f'SSE.ETF.{tdx_symbol.split(":")[-1]}'
        elif tdx_symbol.startswith('sz:'):
            return f'SZSE.ETF.{tdx_symbol.split(":")[-1]}'
        else:
            return tdx_symbol

    def aggregate_logical_positions(self):
        '''按通道ID和品种ID累加所有策略的实际目标仓位，更新self.logical_positions，并持久化到本地文件'''
        logical_positions = {}
        for strategy_id, symbol_map in self.strategy_positions.items():
            channel_id = self.strategy_channel_map.get(strategy_id)
            context = self.strategy_contexts.get(strategy_id)
            # 计算该策略的资金比例
            initial_capital = getattr(context, 'initial_capital', 100000)
            ratio = initial_capital / 100000
            
            if channel_id not in logical_positions:
                logical_positions[channel_id] = {}
            for symbol_id, qty in symbol_map.items():
                # 将逻辑仓位转换为实际仓位后再累加
                real_qty = qty * ratio
                logical_positions[channel_id][symbol_id] = logical_positions[channel_id].get(symbol_id, 0) + real_qty
        self.logical_positions = logical_positions
        # 持久化到本地JSON文件
        try:
            file_path = f'actual_positions_{self.user_id}.json'
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.logical_positions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"[持久化] 保存实际仓位到本地文件失败: {e}")
        self.save_engine_snapshot() # 保存快照

    def place_order(self, commodity_info, channel_id, symbol_id, diff, is_close=False):
        '''真实下单接口，调用Node后端 /api/trade/execute_trade 实现下单
        channel_id 必须为 logical_positions 遍历时的真实通道ID，严禁为'default'或兜底值
        diff 已经是实际仓位差值，无需再进行资金比例换算
        '''
        # ========== 下单数量处理 ==========
       
        min_order_quantity = commodity_info == None and 1 or commodity_info.get('min_order_quantity', 1)
        order_quantity_step = commodity_info == None and 1 or commodity_info.get('order_quantity_step', 1)

        # 3. 规整化数量处理
        original_qty = int(diff)
        self.logger.info(f"[规整化] 通道:{channel_id} 品种:{symbol_id} 原始数量:{original_qty}, 步长:{order_quantity_step}")
        
        # 使用commodity_info中的步长规整化数量
        # 仅在开仓的时候，需要进行规整
        if not is_close:
            if order_quantity_step > 1:
                # 规整化：向下取整到步长的倍数
                order_qty = (original_qty // order_quantity_step) * order_quantity_step
                self.logger.info(f"[规整化] 通道:{channel_id} 品种:{symbol_id} 规整后数量:{order_qty} (原始:{original_qty}, 步长:{order_quantity_step})")
            elif order_quantity_step < 1:
                # 对于小数步长（如CRYPTO的0.0001），需要特殊处理
                # 将原始数量转换为步长的倍数
                order_qty = int(original_qty / order_quantity_step) * order_quantity_step
                self.logger.info(f"[规整化] 通道:{channel_id} 品种:{symbol_id} 规整后数量:{order_qty} (原始:{original_qty}, 步长:{order_quantity_step})")
            else:
                order_qty = original_qty
                self.logger.info(f"[规整化] 通道:{channel_id} 品种:{symbol_id} 无需规整，数量:{order_qty} (步长:{order_quantity_step})")
        else:
            # 平仓时也需要考虑步长，但使用向下取整避免持仓不足
            if order_quantity_step > 1:
                # 平仓时向下取整到步长的倍数，避免持仓不足
                abs_qty = abs(original_qty)
                order_qty = (abs_qty // order_quantity_step) * order_quantity_step
                # 保持原来的符号
                order_qty = order_qty if original_qty > 0 else -order_qty
                self.logger.info(f"[规整化] 通道:{channel_id} 品种:{symbol_id} 平仓规整后数量:{order_qty} (原始:{original_qty}, 步长:{order_quantity_step})")
            elif order_quantity_step < 1:
                # 对于小数步长，向下取整
                abs_qty = abs(original_qty)
                order_qty = int(abs_qty / order_quantity_step) * order_quantity_step
                order_qty = order_qty if original_qty > 0 else -order_qty
                self.logger.info(f"[规整化] 通道:{channel_id} 品种:{symbol_id} 平仓规整后数量:{order_qty} (原始:{original_qty}, 步长:{order_quantity_step})")
            else:
                order_qty = original_qty
                self.logger.info(f"[规整化] 通道:{channel_id} 品种:{symbol_id} 平仓无需规整，数量:{order_qty} (步长:{order_quantity_step})")

        # ====== 详细调试日志 ======
        self.logger.info(f"[调试] 下单参数: diff(实际仓位差值)={diff}, original_qty(原始数量)={original_qty}, order_qty(规整后数量)={order_qty}, min_order_quantity={min_order_quantity}, order_quantity_step={order_quantity_step}")

        if order_qty == 0 or abs(order_qty) < min_order_quantity:
            self.logger.info(f"[下单][实盘下单] 通道:{channel_id} 品种:{symbol_id} 规整后数量{order_qty}小于最小下单量{min_order_quantity}，不发送指令")
            return
        
        # ========== 买卖方向判断，基于只做多 ==========
        if order_qty > 0:
            action = 'buy'
            amount = order_qty
        elif order_qty < 0:
            action = 'sell'
            amount = abs(order_qty)
        else:
            self.logger.info(f"[下单] 通道:{channel_id} 品种:{symbol_id} 净下单量为0，不下单")
            return
        
        # 新增调试日志：实际下单payload
        self.logger.info(f"[调试] 下单payload: channelId={channel_id}, action={action}, code={symbol_id}, amount={amount}, price=0")
        import requests
        try:
            url = 'http://127.0.0.1:3000/api/trade/execute_trade'
            payload = {
                'channelId': channel_id,  # 必须为真实通道ID，严禁'default'
                'action': action,
                'params': {
                    'code': symbol_id,
                    'amount': amount,
                    'is_close': is_close,
                    'price': 0  # 市价单
                }
            }
            headers = {}
            if self.jwt_token:
                headers['Authorization'] = self.jwt_token
                self.logger.info(f"[下单] 使用JWT token进行认证")
            else:
                self.logger.warning(f"[下单] JWT token为空，尝试无token下单（可能失败）")
            response = requests.post(url, json=payload, headers=headers, timeout=5)
            if response.status_code == 200 and response.json().get('success'):
                self.logger.info(f"[下单] 已成功调用Node后端API: {response.json()}")
            else:
                self.logger.error(f"[下单] Node后端API返回异常: {response.status_code}, {response.text}")
        except Exception as e:
            self.logger.error(f"[下单] 调用Node后端API异常: {e}")

    def get_commodity_info_by_symbol(self, symbol_id: str) -> dict:
        """
        根据 symbol_id 获取对应的 commodity_info
        """
        # 根据 symbol_id 推断品类类型
        if symbol_id.startswith('SSE.ETF.') or symbol_id.startswith('SZSE.ETF.'):
            commodity_key = 'ETF'
        elif symbol_id.startswith('sh:') or symbol_id.startswith('sz:'):
            commodity_key = 'STOCK'  # 或者根据实际情况判断
        else:
            commodity_key = 'ETF'  # 默认ETF
        
        # 复用现有的 commodity.json 读取逻辑
        def find_commodity_json():
            cur = os.path.abspath(os.path.dirname(__file__))
            backend_dir = None
            for _ in range(10):
                if os.path.basename(cur) == 'backend':
                    backend_dir = cur
                    break
                parent = os.path.dirname(cur)
                if parent == cur:
                    break
                cur = parent
            
            if backend_dir:
                candidate = os.path.join(backend_dir, '_Providers', '_Python', 'strategy', 'commodity.json')
                if os.path.exists(candidate):
                    return candidate
            return None
        
        cjson_path = find_commodity_json()
        commodity_info = {}
        if cjson_path:
            with open(cjson_path, 'r', encoding='utf-8') as f:
                cdata = json.load(f)
            cinfo = cdata.get(commodity_key, {})
            for k in ["order_quantity_step", "settlement_time", "t_plus_rule", "trading_days", "digits", "timezone"]:
                commodity_info[k] = cinfo.get(k)
        
        return commodity_info            

    def run(self):
        '''
        主循环，从本地队列获取数据并分发到策略实例。
        '''
        logger.info('[DataThread] 主循环已启动，等待行情数据...')
        last_logical_positions = {}
        while True:
            try:
                symbol, period, kline_data = self.data_queue.get()  # 阻塞式获取
                logger.info(f'[DataThread] 收到队列数据: symbol={symbol}, period={period}, kline_data_len={len(kline_data) if hasattr(kline_data, "__len__") else "?"}')
                # 转回策略配置用的symbol格式
                original_symbol = self.tdx_symbol_to_original(symbol)
                # 2. 更新全局数据缓存
                cache_key = (original_symbol, period)
                self.data_cache[cache_key] = kline_data
                logger.info(f"[数据缓存] 进行缓存，cache_key={cache_key}")
                logger.info(f"[DataThread] 更新缓存: {original_symbol} on {period}, 共 {len(kline_data)} 条数据")

                # 3. 遍历策略，更新就绪状态
                strategies_to_check = []

                logger.info(f'[DataThread] 数据级别：{period}，品种：{original_symbol}，品种列表：{self.strategy_symbols}')

                for live_strategy_id, instance in self.instancies.items():
                    logger.info(f'[DataThread] 策略实例周期：{instance.timeframe}')
                    if instance.timeframe == period and original_symbol in self.strategy_symbols.get(live_strategy_id, set()):
                        self.strategy_ready_map[live_strategy_id][original_symbol] = True
                        strategies_to_check.append(live_strategy_id)
                        logger.info(f"[DataThread] 策略 live_strategy_id={live_strategy_id} 的品种 {original_symbol} 数据已就绪")

                # 4. 检查策略是否完全就绪，并执行计算
                for live_strategy_id in strategies_to_check:
                    is_fully_ready = all(self.strategy_ready_map[live_strategy_id].values())
                    if is_fully_ready:
                        logger.info(f"[DataThread] 策略 {live_strategy_id} 所有品种数据已就绪, 执行 on_calculate")
                        context = self.strategy_contexts[live_strategy_id]  # 只用已保存的context
                        # 设置当前K线的日期和时间（取主品种最新一根bar）
                        main_symbol = list(self.strategy_symbols[live_strategy_id])[0]
                        cache_key = (main_symbol, instance.timeframe)
                        bars = self.data_cache.get(cache_key, [])
                        if bars:
                            last_bar = bars[-1]

                            logger.info(f"[DataThread] 获取的最后一根k线数据：{last_bar}")

                            # 直接用 time 字段（秒级时间戳）转为北京时间，赋值 _current_bar_date 和 _current_bar_time
                            bar_time_val = last_bar.get('time')
                            if bar_time_val:
                                dt = datetime.fromtimestamp(int(bar_time_val), pytz.timezone('Asia/Shanghai'))
                                context._current_bar_date = int(dt.strftime('%Y%m%d'))
                                context._current_bar_time = int(dt.strftime('%H%M'))
                            else:
                                context._current_bar_date = None
                                context._current_bar_time = None
                        else:
                            context._current_bar_date = None
                            context._current_bar_time = None

                        
                        logger.info(f"开始进入策略计算流程，当前K线日期：{context._current_bar_date}，当前K线时间：{context._current_bar_time}")
                        self.instancies[live_strategy_id].on_calculate(context)
                        logger.info(f"------------------------- on_calculate 结束 -------------------------")

                        for s in self.strategy_ready_map[live_strategy_id]:
                            self.strategy_ready_map[live_strategy_id][s] = False
                        logger.info(f"[DataThread] 策略 {live_strategy_id} 执行完毕，就绪状态已重置，等待下一周期")

                # 新增：每轮聚合逻辑仓位
                self.aggregate_logical_positions()

                # 计算净下单量，调用真实下单接口
                # ------- 新的撮合规则，由于不同策略初始资金不同，所以，撮合必须针对于计算了比率之后的数量进行撮合！------
                #### 注意事项，进行到这个步骤，已经撮合完毕，下面的代码与策略ID已经无关，仅仅用于下单！！！

                for channel_id, symbol_map in self.logical_positions.items():
                    for symbol_id, target_qty in symbol_map.items():
                        last_qty = last_logical_positions.get(channel_id, {}).get(symbol_id, 0)
                        diff = target_qty - last_qty
                        if diff != 0:
                            logger.info(f"[撮合] 通道:{channel_id} 品种:{symbol_id} 净下单量:{diff} (目标:{target_qty}, 上轮:{last_qty})")

                            # commodity_info 本质上是品类的属性，这里就等于是 symbol_id 所属品类的属性，要如何获得？
                            commondity_info = self.get_commodity_info_by_symbol(symbol_id)

                            logger.info(f"[下单调试] 使用 symbol_id={symbol_id} 寻找 commondity：{commondity_info}")

                            self.place_order(commondity_info, channel_id, symbol_id, diff)

                # place_order后，立即更新last_logical_positions
                last_logical_positions = {ch: syms.copy() for ch, syms in self.logical_positions.items()}

            except Exception as e:
                logger.error(f"[DataThread] 处理数据时异常: {e}", exc_info=True)

            time.sleep(0.01) # 防止CPU空转

    def get_strategy_positions_with_ratio(self, live_strategy_id):
        """
        获取指定策略的逻辑持仓，并根据初始资金与10万的比例计算预期持仓
        @param live_strategy_id: 实盘策略ID
        @return: 预期持仓列表
        """
        try:
            # 1. 获取策略的逻辑持仓
            strategy_positions = self.strategy_positions.get(live_strategy_id, {})
            if not strategy_positions:
                logger.info(f"[持仓查询] 策略 {live_strategy_id} 暂无逻辑持仓")
                return []
            
            # 2. 获取策略上下文（包含初始资金信息）
            context = self.strategy_contexts.get(live_strategy_id)
            if not context:
                logger.warning(f"[持仓查询] 策略 {live_strategy_id} 上下文不存在")
                return []
            
            # 3. 计算资金比例
            initial_capital = getattr(context, 'initial_capital', 100000)
            ratio = initial_capital / 100000
            logger.info(f"[持仓查询] 策略 {live_strategy_id} 初始资金: {initial_capital}, 比例: {ratio}")
            
            # 4. 计算预期持仓
            expected_positions = []
            for symbol_id, logical_position in strategy_positions.items():
                # 根据比例计算预期持仓
                expected_position = logical_position * ratio
                
                # 获取当前价格（用于计算市值等）
                current_price = 0.0
                try:
                    # 尝试从缓存获取最新价格
                    timeframe = getattr(self.instancies.get(live_strategy_id), 'timeframe', '1D')
                    cache_key = (symbol_id, timeframe)
                    if cache_key in self.data_cache and self.data_cache[cache_key]:
                        current_price = self.data_cache[cache_key][-1].get('close', 0.0)
                except Exception as e:
                    logger.warning(f"[持仓查询] 获取 {symbol_id} 价格失败: {e}")
                
                # 构建持仓信息
                position_info = {
                    'symbol': symbol_id,
                    'name': symbol_id,  # 可以从symbol_id提取名称
                    'logical_position': logical_position,  # 逻辑持仓
                    'expected_position': expected_position,  # 预期持仓
                    'ratio': ratio,  # 资金比例
                    'initial_capital': initial_capital,  # 初始资金
                    'current_price': current_price,  # 当前价格
                    'market_value': expected_position * current_price if current_price > 0 else 0  # 市值
                }
                expected_positions.append(position_info)
            
            logger.info(f"[持仓查询] 策略 {live_strategy_id} 返回 {len(expected_positions)} 个持仓")
            return expected_positions
            
        except Exception as e:
            logger.error(f"[持仓查询] 获取策略 {live_strategy_id} 持仓失败: {e}")
            return []

def main():
    parser = argparse.ArgumentParser(description='实盘策略执行引擎')
    parser.add_argument('--user_id', type=str, required=True, help='用户ID')
    parser.add_argument('--redis_host', type=str, default='localhost', help='Redis主机')
    parser.add_argument('--redis_port', type=int, default=6379, help='Redis端口')
    parser.add_argument('--redis_db', type=int, default=0, help='Redis数据库')
    parser.add_argument('--redis_password', type=str, default=None, help='Redis密码')
    parser.add_argument('--http_port', type=int, default=5001, help='HTTP服务端口')
    
    args = parser.parse_args()
    
    # 在获取到user_id后，立即初始化日志系统
    setup_logging(args.user_id)

    try:
        # 创建策略引擎
        engine = StrategyEngine(args.user_id, args.redis_host, args.redis_port, args.redis_db, args.redis_password)
        
        # 启动HTTP服务
        from flask import Flask, jsonify, request
        app = Flask(__name__)
        
        @app.route('/positions/<live_strategy_id>', methods=['GET'])
        def get_strategy_positions(live_strategy_id):
            """获取策略持仓的HTTP接口"""
            try:
                positions = engine.get_strategy_positions_with_ratio(live_strategy_id)
                return jsonify({
                    'success': True,
                    'data': positions
                })
            except Exception as e:
                logger.error(f"[HTTP接口] 获取策略持仓失败: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @app.route('/health', methods=['GET'])
        def health_check():
            """健康检查接口"""
            return jsonify({
                'success': True,
                'status': 'running',
                'user_id': args.user_id,
                'timestamp': time.time()
            })
        
        # 启动HTTP服务线程
        import threading
        def run_http_server():
            app.run(host='0.0.0.0', port=args.http_port, debug=False, use_reloader=False)
        
        http_thread = threading.Thread(target=run_http_server, daemon=True)
        http_thread.start()
        logger.info(f"[HTTP服务] 已启动HTTP服务，端口: {args.http_port}")
        
        # 创建并启动控制指令监听线程
        control_thread = threading.Thread(target=engine.listen_for_control_commands, daemon=True)
        control_thread.start()

        # 创建并启动行情数据接收线程
        market_thread = threading.Thread(target=engine.listen_for_market_data, daemon=True)
        market_thread.start()

        # 创建并启动主循环线程
        data_thread = threading.Thread(target=engine.run, daemon=True)
        data_thread.start()

        # 主线程健康检查
        while True:
            if not control_thread.is_alive() or not market_thread.is_alive() or not data_thread.is_alive():
                logger.error("有线程已停止！引擎即将退出。")
                break
            time.sleep(10)

    except Exception as e:
        # 此时logger必定已初始化
        logger.error(f'引擎启动或运行过程中发生致命错误: {e}', exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    main() 