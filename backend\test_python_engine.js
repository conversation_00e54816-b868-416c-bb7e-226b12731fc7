#!/usr/bin/env node

/**
 * Python引擎测试 - 验证run_group.py脚本和Wonder Trader引擎
 * 使用文字模式输出来验证功能
 */

const path = require('path');
const fs = require('fs').promises;
const { spawn } = require('child_process');
const { v4: uuidv4 } = require('uuid');

// 导入相关模块
const LiveStrategyAdapter = require('./services/LiveStrategyAdapter');
const { LiveStrategy } = require('./models');

async function testPythonEngine() {
  console.log('🐍 开始Python引擎测试...\n');

  try {
    // 1. 准备测试环境
    console.log('📋 第一步：准备测试环境...');
    
    const userId = 1;
    const adapter = LiveStrategyAdapter;
    
    // 创建测试策略
    const mockStrategyYaml = `
trading_type: etf
universe:
  - "SSE.ETF.510300"
  - "SSE.ETF.510500"
  - "SZSE.ETF.159919"
bar_count: 50
data_freq: "day"
order_by:
  formula: "roc_20 * 0.6 + vol_20 * 0.4"
buy_rules:
  formulas:
    - "roc_20 > 0.05"
    - "vol_20 < 0.3"
  at_least_count: 2
sell_rules:
  formulas:
    - "roc_20 < -0.03"
  at_least_count: 1
top_n: 2
weighting_scheme: "equal"
rebalance_interval: "daily"
`;

    const liveStrategyId = uuidv4();
    await LiveStrategy.create({
      id: liveStrategyId,
      userId,
      username: 'test_user',
      strategyId: 'test_multi_factor',
      name: '实盘-多因子ETF策略',
      status: 'stopped',
      accountId: 'test_account',
      initialCapital: 100000,
      commissionRate: 0.0003,
      riskSettings: { stopLossPercent: 5 },
      yaml: mockStrategyYaml,
      createdAt: new Date(),
      lastUpdateTime: new Date()
    });

    console.log(`✅ 测试策略创建成功: ${liveStrategyId}`);

    // 2. 初始化组合管理器并生成配置
    console.log('\n⚙️ 第二步：生成组合配置...');
    
    const groupManager = await adapter.getUserGroupManager(userId);
    await groupManager._addStrategyToGroup(liveStrategyId);
    await groupManager._generateGroupConfig();
    
    const groupPath = path.dirname(groupManager.group.configPath);
    console.log(`✅ 组合配置已生成: ${groupPath}`);

    // 3. 验证Python脚本存在
    console.log('\n🔍 第三步：验证Python脚本...');
    
    const pythonScript = path.join(__dirname, '_Providers/_Python/strategy/run_group.py');
    try {
      await fs.access(pythonScript);
      console.log(`✅ Python脚本存在: ${pythonScript}`);
    } catch (error) {
      console.log(`❌ Python脚本不存在: ${pythonScript}`);
      return;
    }

    // 4. 准备策略配置参数
    console.log('\n📝 第四步：准备策略配置参数...');
    
    const strategiesConfig = [{
      id: liveStrategyId,
      yaml: mockStrategyYaml
    }];
    
    console.log('策略配置参数:');
    console.log(JSON.stringify(strategiesConfig, null, 2));

    // 5. 测试Python脚本参数解析（干运行模式）
    console.log('\n🧪 第五步：测试Python脚本参数解析...');
    
    const pythonPath = process.env.PYTHON_PATH || 'python';
    console.log(`Python路径: ${pythonPath}`);
    console.log(`工作目录: ${groupPath}`);
    console.log(`脚本参数:`);
    console.log(`  --group_path: ${groupPath}`);
    console.log(`  --strategies: ${JSON.stringify(strategiesConfig)}`);
    console.log(`  --log_level: INFO`);

    // 6. 检查Python环境和依赖
    console.log('\n🔧 第六步：检查Python环境...');
    
    await testPythonEnvironment(pythonPath);

    // 7. 模拟引擎启动过程（不实际启动）
    console.log('\n🚀 第七步：模拟引擎启动过程...');
    
    console.log('模拟启动步骤:');
    console.log('1. 切换到组合目录');
    console.log('2. 读取config.yaml配置文件');
    console.log('3. 初始化Wonder Trader引擎');
    console.log('4. 解析策略YAML配置');
    console.log('5. 创建MultiFactorsCTA实例');
    console.log('6. 添加策略到引擎');
    console.log('7. 启动引擎运行');

    // 8. 验证配置文件内容
    console.log('\n📄 第八步：验证配置文件内容...');
    
    await verifyConfigFiles(groupPath);

    // 9. 模拟策略实例化过程
    console.log('\n🎯 第九步：模拟策略实例化...');
    
    await simulateStrategyInstantiation(strategiesConfig[0]);

    // 10. 测试完整命令行（仅显示，不执行）
    console.log('\n💻 第十步：完整命令行预览...');
    
    const fullCommand = [
      pythonPath,
      pythonScript,
      '--group_path', groupPath,
      '--strategies', JSON.stringify(strategiesConfig),
      '--log_level', 'INFO'
    ].join(' ');
    
    console.log('完整命令行:');
    console.log(fullCommand);
    console.log('\n如果要实际执行，可以运行上述命令');

    console.log('\n🎉 Python引擎测试完成！');
    console.log('\n📊 测试结果总结:');
    console.log('✅ 测试环境准备完成');
    console.log('✅ 组合配置生成成功');
    console.log('✅ Python脚本路径验证');
    console.log('✅ 参数格式验证');
    console.log('✅ 配置文件内容验证');
    console.log('✅ 策略实例化逻辑验证');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误堆栈:', error.stack);
  } finally {
    // 关闭数据库连接
    try {
      const { sequelize } = require('./database');
      await sequelize.close();
      console.log('\n✅ 数据库连接已关闭');
    } catch (error) {
      console.log('⚠️ 关闭数据库连接时出错:', error.message);
    }
  }
}

/**
 * 测试Python环境
 */
async function testPythonEnvironment(pythonPath) {
  return new Promise((resolve) => {
    console.log('检查Python版本...');
    
    const process = spawn(pythonPath, ['--version'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    process.stdout.on('data', (data) => {
      output += data.toString();
    });

    process.stderr.on('data', (data) => {
      output += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ Python版本: ${output.trim()}`);
      } else {
        console.log(`❌ Python不可用 (退出代码: ${code})`);
      }
      resolve();
    });

    process.on('error', (error) => {
      console.log(`❌ Python执行错误: ${error.message}`);
      resolve();
    });
  });
}

/**
 * 验证配置文件内容
 */
async function verifyConfigFiles(groupPath) {
  const configFiles = [
    'config.yaml',
    'contracts.json',
    'executers.yaml',
    'tdparsers.yaml',
    'tdtraders.yaml'
  ];

  for (const fileName of configFiles) {
    const filePath = path.join(groupPath, fileName);
    try {
      const content = await fs.readFile(filePath, 'utf8');
      console.log(`✅ ${fileName} (${content.length}字符)`);
      
      // 显示关键内容片段
      if (fileName === 'contracts.json') {
        console.log(`   品种列表: ${content.trim()}`);
      } else if (fileName === 'config.yaml') {
        const lines = content.split('\n').slice(0, 5);
        console.log(`   配置预览: ${lines.join(' | ')}`);
      }
    } catch (error) {
      console.log(`❌ ${fileName} - 读取失败: ${error.message}`);
    }
  }
}

/**
 * 模拟策略实例化过程
 */
async function simulateStrategyInstantiation(strategyConfig) {
  console.log('模拟解析策略YAML...');
  
  try {
    const yaml = require('js-yaml');
    const config = yaml.load(strategyConfig.yaml);
    
    console.log('✅ YAML解析成功');
    console.log(`   交易类型: ${config.trading_type}`);
    console.log(`   品种数量: ${config.universe?.length || 0}`);
    console.log(`   数据频率: ${config.data_freq}`);
    console.log(`   选股数量: ${config.top_n}`);
    
    console.log('\n模拟创建MultiFactorsCTA实例...');
    console.log('✅ 策略实例参数准备完成');
    console.log(`   name: ${strategyConfig.id}`);
    console.log(`   codes: ${config.universe?.join(', ')}`);
    console.log(`   barCnt: ${config.bar_count}`);
    console.log(`   period: ${config.data_freq}`);
    
  } catch (error) {
    console.log(`❌ YAML解析失败: ${error.message}`);
  }
}

// 运行测试
if (require.main === module) {
  testPythonEngine().then(() => {
    console.log('\n测试结束');
    process.exit(0);
  }).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = testPythonEngine;
