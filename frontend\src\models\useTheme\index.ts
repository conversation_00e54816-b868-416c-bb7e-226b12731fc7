import { atom, useAtom } from 'jotai';

export type ThemeType = 
  | 'light-default' | 'light-blue' | 'light-tech' 
  | 'dark-default' | 'dark-blue' | 'dark-tech';

// 从 localStorage 获取保存的主题
const getSavedTheme = (): ThemeType => {
  const savedTheme = localStorage.getItem('app-theme') as ThemeType;
  if (savedTheme && [
    'light-default', 'light-blue', 'light-tech',
    'dark-default', 'dark-blue', 'dark-tech'
  ].includes(savedTheme)) {
    return savedTheme;
  }
  return 'light-default';
};

// 创建主题原子，使用保存的主题作为初始值
export const themeAtom = atom<ThemeType>(getSavedTheme());

// 创建主题 hook
export const useTheme = () => {
  const [theme, setTheme] = useAtom(themeAtom);

  const changeTheme = (newTheme: ThemeType) => {
    setTheme(newTheme);
    localStorage.setItem('app-theme', newTheme);
  };

  return {
    theme,
    changeTheme,
  };
}; 