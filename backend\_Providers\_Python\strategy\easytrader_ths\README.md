# EasyTrader同花顺交易适配器

该模块提供了基于easytrader的同花顺交易接口，支持内网穿透功能，使远程后端能够与本地同花顺客户端通信。

## 功能特点

- 基于easytrader实现同花顺客户端的自动化交易
- 支持内网穿透，实现远程后端与本地客户端的通信
- 提供Wonder Trader适配器，无缝集成到Wonder Trader框架
- 支持多用户管理，每个用户可以有自己的交易客户端
- 提供完整的API接口，支持账户查询、下单、撤单等操作

## 系统架构

系统分为客户端和服务端两部分：

1. **客户端**：运行在Windows系统上，负责控制同花顺客户端进行交易
2. **服务端**：运行在后端服务器上，负责接收客户端的反向连接，并提供API接口

通信流程：

1. 客户端启动后，向服务端注册自己的信息（用户名、IP、端口等）
2. 客户端定期向服务端发送心跳，保持连接
3. 服务端接收到交易请求后，通过客户端注册的信息，向客户端发送请求
4. 客户端接收到请求后，控制同花顺客户端执行交易操作，并返回结果

## 安装依赖

```bash
pip install easytrader flask requests
```

## 使用方法

### 客户端配置

1. 在Windows系统上安装同花顺客户端并登录
2. 创建配置文件`easytrader_ths_config.json`：

```json
{
  "username": "your_username",
  "backend_url": "http://your_backend_server:8000",
  "local_port": 8888,
  "heartbeat_interval": 60,
  "ths_path": "C:/path/to/ths/xiadan.exe"
}
```

3. 运行客户端程序：

```bash
python client.py
```

### 服务端配置

1. 在Wonder Trader配置文件中添加交易通道配置：

```json
{
  "traders": {
    "easytrader_ths": {
      "active": true,
      "module": "TraderEasyTraderTHS",
      "username": "your_username",
      "client_url": "http://client_ip:8888",
      "timeout": 10
    }
  }
}
```

2. 在品种与通道映射中指定使用该通道：

```json
{
  "product_mappings": {
    "SSE.600000": "easytrader_ths",
    "SZSE.000001": "easytrader_ths"
  }
}
```

## API接口

客户端提供以下API接口：

- `/api/balance` - 获取账户余额
- `/api/position` - 获取持仓信息
- `/api/buy` - 买入股票
- `/api/sell` - 卖出股票
- `/api/cancel` - 撤销订单
- `/api/today_entrusts` - 获取今日委托
- `/api/today_trades` - 获取今日成交
- `/api/refresh` - 刷新接口
- `/api/status` - 获取客户端状态

服务端提供以下API接口：

- `/api/register_trading_client` - 注册交易客户端
- `/api/trading_client_heartbeat` - 交易客户端心跳
- `/api/get_trading_clients` - 获取所有交易客户端
- `/api/get_trading_client` - 获取指定交易客户端

## 注意事项

1. 客户端需要在Windows系统上运行，并且已安装同花顺客户端
2. 客户端和服务端的时间需要同步，否则可能导致心跳检测失败
3. 客户端需要能够访问服务端，服务端也需要能够访问客户端
4. 同花顺客户端需要保持登录状态，否则交易操作会失败
5. 建议使用专用账号运行客户端，避免与手动操作冲突

## 常见问题

1. **客户端无法连接到服务端**
   - 检查网络连接
   - 检查服务端是否正常运行
   - 检查配置文件中的backend_url是否正确

2. **服务端无法连接到客户端**
   - 检查客户端是否正常运行
   - 检查客户端的IP和端口是否正确
   - 检查防火墙设置，确保服务端可以访问客户端

3. **交易操作失败**
   - 检查同花顺客户端是否正常登录
   - 检查账户余额是否足够
   - 检查股票代码是否正确
   - 查看客户端日志，了解具体错误原因

## 开发计划

- [ ] 支持更多交易操作（如融资融券、ETF申购赎回等）
- [ ] 优化内网穿透机制，提高稳定性
- [ ] 添加更多安全措施，如加密通信、身份验证等
- [ ] 提供图形界面，方便配置和监控
- [ ] 支持更多券商的交易客户端
