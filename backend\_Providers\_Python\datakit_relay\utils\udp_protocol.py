#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
UDP 协议实现
实现 Wonder Trader ParserUDP 协议
"""

import struct
import logging
from typing import Optional, Tuple, Dict, Any, List, Union

class UDPProtocol:
    """UDP 协议实现"""
    
    def __init__(self):
        """初始化协议"""
        self.logger = logging.getLogger(__name__)
        
        # 协议常量
        self.HEADER = b'WTPY'
        self.SUBSCRIBE_COMMAND = 1001
        self.UNSUBSCRIBE_COMMAND = 1002
        self.TICK_DATA_TYPE = 2001
        self.BAR_DATA_TYPE = 2002
    
    def parse_request(self, data: bytes) -> Optional[Tuple[int, str]]:
        """
        解析请求数据
        返回 (命令, 品种代码) 元组，或者 None（如果解析失败）
        """
        try:
            # 检查数据长度
            if len(data) < 12:
                self.logger.warning(f"数据长度不足: {len(data)} < 12")
                return None
            
            # 解析头部
            header = data[:4]
            if header != self.HEADER:
                self.logger.warning(f"无效的头部: {header}")
                return None
            
            # 解析命令
            command = struct.unpack('<I', data[4:8])[0]
            
            # 解析正文长度
            body_length = struct.unpack('<I', data[8:12])[0]
            
            # 检查数据长度
            if len(data) < 12 + body_length:
                self.logger.warning(f"正文长度不足: {len(data) - 12} < {body_length}")
                return None
            
            # 解析正文（品种代码）
            body = data[12:12+body_length].decode('utf-8')
            
            return (command, body)
        
        except Exception as e:
            self.logger.error(f"解析请求数据时出错: {e}")
            return None
    
    def create_tick_data(self, exchange: str, code: str, tick_data: Dict[str, Any]) -> bytes:
        """
        创建 Tick 数据包
        格式: [Header(4字节)][DataType(4字节)][BodyLength(4字节)][Body(变长)]
        Body: [Exchange(16字节)][Code(32字节)][ActionDate(8字节)][ActionTime(9字节)][Price(8字节)][Volume(8字节)][Turnover(8字节)][OpenInterest(8字节)][BidPrice(8字节*10)][BidQty(8字节*10)][AskPrice(8字节*10)][AskQty(8字节*10)]
        """
        try:
            # 创建缓冲区
            buffer = bytearray(1024)  # 预分配足够大的缓冲区
            
            # 写入头部
            struct.pack_into('<4s', buffer, 0, self.HEADER)
            
            # 写入数据类型
            struct.pack_into('<I', buffer, 4, self.TICK_DATA_TYPE)
            
            # 跳过正文长度，稍后填充
            body_start = 12
            
            # 写入交易所
            exchange_bytes = exchange.encode('utf-8').ljust(16, b'\0')
            buffer[body_start:body_start+16] = exchange_bytes
            
            # 写入代码
            code_bytes = code.encode('utf-8').ljust(32, b'\0')
            buffer[body_start+16:body_start+48] = code_bytes
            
            # 写入日期
            date_str = tick_data.get('date', '').ljust(8, '\0')
            buffer[body_start+48:body_start+56] = date_str.encode('utf-8')
            
            # 写入时间
            time_str = tick_data.get('time', '').ljust(9, '\0')
            buffer[body_start+56:body_start+65] = time_str.encode('utf-8')
            
            # 写入价格
            struct.pack_into('<d', buffer, body_start+65, float(tick_data.get('price', 0.0)))
            
            # 写入成交量
            struct.pack_into('<d', buffer, body_start+73, float(tick_data.get('volume', 0.0)))
            
            # 写入成交额
            struct.pack_into('<d', buffer, body_start+81, float(tick_data.get('turnover', 0.0)))
            
            # 写入持仓量
            struct.pack_into('<d', buffer, body_start+89, float(tick_data.get('open_interest', 0.0)))
            
            # 写入买价
            bid_prices = tick_data.get('bid_prices', [0.0] * 10)
            for i in range(10):
                price = bid_prices[i] if i < len(bid_prices) else 0.0
                struct.pack_into('<d', buffer, body_start+97+i*8, float(price))
            
            # 写入买量
            bid_volumes = tick_data.get('bid_volumes', [0.0] * 10)
            for i in range(10):
                volume = bid_volumes[i] if i < len(bid_volumes) else 0.0
                struct.pack_into('<d', buffer, body_start+177+i*8, float(volume))
            
            # 写入卖价
            ask_prices = tick_data.get('ask_prices', [0.0] * 10)
            for i in range(10):
                price = ask_prices[i] if i < len(ask_prices) else 0.0
                struct.pack_into('<d', buffer, body_start+257+i*8, float(price))
            
            # 写入卖量
            ask_volumes = tick_data.get('ask_volumes', [0.0] * 10)
            for i in range(10):
                volume = ask_volumes[i] if i < len(ask_volumes) else 0.0
                struct.pack_into('<d', buffer, body_start+337+i*8, float(volume))
            
            # 计算正文长度
            body_length = 16 + 32 + 8 + 9 + 8*4 + 8*40
            
            # 写入正文长度
            struct.pack_into('<I', buffer, 8, body_length)
            
            # 返回数据
            return bytes(buffer[:body_start+body_length])
        
        except Exception as e:
            self.logger.error(f"创建 Tick 数据包时出错: {e}")
            return b''
    
    def create_bar_data(self, exchange: str, code: str, period: str, bar_data: Dict[str, Any]) -> bytes:
        """
        创建 Bar 数据包
        格式: [Header(4字节)][DataType(4字节)][BodyLength(4字节)][Body(变长)]
        Body: [Exchange(16字节)][Code(32字节)][Period(8字节)][ActionDate(8字节)][ActionTime(9字节)][Open(8字节)][High(8字节)][Low(8字节)][Close(8字节)][Volume(8字节)][Turnover(8字节)][OpenInterest(8字节)]
        """
        try:
            # 创建缓冲区
            buffer = bytearray(512)  # 预分配足够大的缓冲区
            
            # 写入头部
            struct.pack_into('<4s', buffer, 0, self.HEADER)
            
            # 写入数据类型
            struct.pack_into('<I', buffer, 4, self.BAR_DATA_TYPE)
            
            # 跳过正文长度，稍后填充
            body_start = 12
            
            # 写入交易所
            exchange_bytes = exchange.encode('utf-8').ljust(16, b'\0')
            buffer[body_start:body_start+16] = exchange_bytes
            
            # 写入代码
            code_bytes = code.encode('utf-8').ljust(32, b'\0')
            buffer[body_start+16:body_start+48] = code_bytes
            
            # 写入周期
            period_bytes = period.encode('utf-8').ljust(8, b'\0')
            buffer[body_start+48:body_start+56] = period_bytes
            
            # 写入日期
            date_str = bar_data.get('date', '').ljust(8, '\0')
            buffer[body_start+56:body_start+64] = date_str.encode('utf-8')
            
            # 写入时间
            time_str = bar_data.get('time', '').ljust(9, '\0')
            buffer[body_start+64:body_start+73] = time_str.encode('utf-8')
            
            # 写入开盘价
            struct.pack_into('<d', buffer, body_start+73, float(bar_data.get('open', 0.0)))
            
            # 写入最高价
            struct.pack_into('<d', buffer, body_start+81, float(bar_data.get('high', 0.0)))
            
            # 写入最低价
            struct.pack_into('<d', buffer, body_start+89, float(bar_data.get('low', 0.0)))
            
            # 写入收盘价
            struct.pack_into('<d', buffer, body_start+97, float(bar_data.get('close', 0.0)))
            
            # 写入成交量
            struct.pack_into('<d', buffer, body_start+105, float(bar_data.get('volume', 0.0)))
            
            # 写入成交额
            struct.pack_into('<d', buffer, body_start+113, float(bar_data.get('turnover', 0.0)))
            
            # 写入持仓量
            struct.pack_into('<d', buffer, body_start+121, float(bar_data.get('open_interest', 0.0)))
            
            # 计算正文长度
            body_length = 16 + 32 + 8 + 8 + 9 + 8*7
            
            # 写入正文长度
            struct.pack_into('<I', buffer, 8, body_length)
            
            # 返回数据
            return bytes(buffer[:body_start+body_length])
        
        except Exception as e:
            self.logger.error(f"创建 Bar 数据包时出错: {e}")
            return b''
