/**
 * 市场相关数据结构定义
 * 与前端保持一致
 */

export enum MarketType {
  STOCK = 'STOCK',      // 股票
  FUTURE = 'FUTURE',    // 期货
  CRYPTO = 'CRYPTO',    // 加密货币
  INDEX = 'INDEX'       // 指数
}

export enum ExchangeType {
  // 中国股票交易所
  SSE = 'SSE',         // 上海证券交易所
  SZSE = 'SZSE',       // 深圳证券交易所
  BSE = 'BSE',         // 北京证券交易所
  
  // 中国期货交易所
  SHFE = 'SHFE',       // 上海期货交易所
  DCE = 'DCE',         // 大连商品交易所
  CZCE = 'CZCE',       // 郑州商品交易所
  CFFEX = 'CFFEX',     // 中国金融期货交易所
  INE = 'INE',         // 上海国际能源交易中心
  
  // 加密货币交易所
  BINANCE = 'BINANCE', // 币安
  OKX = 'OKX',         // OKX
  HUOBI = 'HUOBI',     // 火币
  BYBIT = 'BYBIT',     // BYBIT
  GATE = 'GATE',       // GATE.IO
}

export interface Symbol {
  code: string          // 代码
  name: string          // 名称
  market: MarketType    // 市场类型
  exchange: ExchangeType // 交易所
}

export interface KLineData {
  time: number          // 时间戳
  open: number         // 开盘价
  high: number         // 最高价
  low: number          // 最低价
  close: number        // 收盘价
  volume: number       // 成交量
}