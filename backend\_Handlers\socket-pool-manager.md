# Socket 连接池管理模块说明文档

## 1. 概述

Socket 连接池管理模块 (`socketHandler.js`) 是系统中的核心组件，负责管理 WebSocket/Socket.IO 连接池，为客户端分配可用端口，并将客户端连接传递给相应的业务模块。该模块对客户端透明，客户端（如 `@utils\easytrader_ths\client.py`）只需要知道如何连接和与业务模块通信，而不需要了解连接池的内部工作原理。

## 2. 主要功能

1. **端口池管理**：
   - 维护一个可用端口池
   - 为客户端分配可用端口
   - 回收不再使用的端口

2. **Socket.IO 服务器管理**：
   - 在分配的端口上启动 Socket.IO 服务器
   - 创建不同的命名空间 (`/realtime`, `/trade` 等)
   - 停止不再使用的 Socket.IO 服务器

3. **连接传递**：
   - 将客户端连接传递给相应的业务模块
   - 确保业务模块能够接收和处理客户端请求

4. **资源清理**：
   - 定期检查并清理不活跃的端口分配
   - 断开长时间不活跃的连接

## 3. 工作流程

### 3.1 端口分配流程

1. 客户端（如 `@utils\easytrader_ths\client.py`）通过 HTTP 请求登录后端
2. 登录成功后，客户端请求获取一个可用的 WebSocket 端口 (`/api/socket/get_port`)
3. 连接池管理器分配一个可用端口并返回给客户端
4. 客户端使用这个端口建立 Socket.IO 连接
5. 连接池管理器接收到连接后，根据命名空间（如 `/trade`）将连接传递给相应的业务模块

### 3.2 连接传递流程

1. 连接池管理器在启动 Socket.IO 服务器时，为不同的命名空间设置处理逻辑
2. 当客户端连接到特定命名空间时，连接池管理器将连接传递给相应的业务模块
3. 业务模块接收连接并处理客户端请求

### 3.3 端口回收流程

1. 客户端断开连接或长时间不活跃
2. 连接池管理器检测到连接断开或不活跃
3. 连接池管理器停止 Socket.IO 服务器并回收端口
4. 端口重新标记为可用，可以分配给其他客户端

## 4. 关键组件

### 4.1 端口池管理

```javascript
// 端口池配置
const PORT_POOL_CONFIG = config.websocket_pool || {
    start_port: 8000,
    end_port: 8100
};

// 端口状态映射
const portStatus = new Map();

// 初始化端口池
function initPortPool() {
    // 初始化所有端口为可用状态
    for (let port = PORT_POOL_CONFIG.start_port; port <= PORT_POOL_CONFIG.end_port; port++) {
        portStatus.set(port, {
            status: 'available', // available, in_use, error
            assigned_to: null,   // 分配给哪个用户
            assigned_at: null,   // 分配时间
            last_activity: null, // 最后活动时间
            error: null          // 错误信息
        });
    }
}
```

### 4.2 Socket.IO 服务器管理

```javascript
// Socket.IO服务器实例映射
const ioServers = new Map();

// 启动Socket.IO服务器
function startWebSocketServer(port) {
    // 创建HTTP服务器
    const server = http.createServer();

    // 创建Socket.IO服务器
    const io = socketIo(server);

    // 创建命名空间并设置处理器
    const namespaces = {};

    // 创建实时数据命名空间
    const realtimeNamespace = io.of(NAMESPACES.REALTIME);
    namespaces[NAMESPACES.REALTIME] = realtimeNamespace;

    // 初始化实时数据处理器
    marketHandler.initSocketIO(realtimeNamespace);

    // 创建交易命名空间
    const tradeNamespace = io.of(NAMESPACES.TRADE);
    namespaces[NAMESPACES.TRADE] = tradeNamespace;

    // 初始化交易处理器
    tradeHandler.initSocketIO(tradeNamespace);

    // 启动服务器
    server.listen(port);

    // 存储服务器实例
    ioServers.set(port, { server, io, namespaces });
}
```

### 4.3 连接传递

业务模块需要实现 `initSocketIO` 方法，接受一个命名空间参数：

```javascript
// 交易模块 (tradeHandler)
initSocketIO: (namespace) => socketIO.initSocketIO(namespace, tradingClients)

// 实时数据模块 (marketHandler)
initSocketIO(io) {
    // 检查是否已经是命名空间
    const realtimeNamespace = io.name === '/realtime' ? io : this.io.of('/realtime');

    // 处理连接事件
    realtimeNamespace.on('connection', (socket) => {
        // 处理客户端请求
    });
}
```

## 5. 业务模块接口规范

为了确保 socketHandler 能够以统一的方式处理所有业务模块的连接，业务模块需要遵循以下接口规范：

### 5.1 必须实现的方法

1. **initSocketIO(namespace)**：
   - 参数：Socket.IO 命名空间
   - 功能：初始化 Socket.IO 服务，处理客户端连接和请求

### 5.2 推荐实现的方法

1. **handleClientRegistration(socket, data)**：
   - 参数：Socket.IO 连接和注册数据
   - 功能：处理客户端注册请求

2. **handleClientDisconnect(socket, reason)**：
   - 参数：Socket.IO 连接和断开原因
   - 功能：处理客户端断开连接

## 6. 客户端使用示例

以通达信客户端模块 (`@utils\easytrader_ths\client.py`) 为例：

```python
# 1. 登录后端
def login_to_backend():
    # 构建登录API URL
    login_url = f"{config['backend_url']}/api/user/login"
    if not login_url.startswith(("http://", "https://")):
        login_url = f"http://{login_url}"

    # 发送登录请求
    response = requests.post(
        login_url,
        json={
            "username": config["username"],
            "password": password
        }
    )

    # 检查响应
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            # 保存token
            if result.get("token"):
                config["auth_token"] = result["token"]
            elif result.get("data") and result["data"].get("token"):
                config["auth_token"] = result["data"]["token"]
            return True
    return False

# 2. 获取可用端口
def get_socket_port():
    # 构建获取端口API URL
    port_url = f"{config['backend_url']}/api/socket/get_port"
    if not port_url.startswith(("http://", "https://")):
        port_url = f"http://{port_url}"

    # 发送请求
    response = requests.post(
        port_url,
        headers={
            "Authorization": f"Bearer {config['auth_token']}"
        }
    )

    # 检查响应
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            # 保存端口
            if result.get("port"):
                config["socket_port"] = result["port"]
            elif result.get("data") and result["data"].get("port"):
                config["socket_port"] = result["data"]["port"]
            return True
    return False

# 3. 连接到Socket.IO服务器
def connect_to_socketio():
    # 构建Socket.IO URL
    socketio_url = f"http://localhost:{config['socket_port']}"

    # 连接到Socket.IO服务器
    sio.connect(
        socketio_url,
        socketio_path="/socket.io",
        namespaces=["/trade"],
        wait=True,
        wait_timeout=5
    )

    # 等待连接建立
    for i in range(20):
        if socket_connected:
            # 连接成功后，主动发送一个心跳包
            heartbeat_data = {
                "username": config["username"],
                "client_type": "easytrader_ths",
                "timestamp": int(time.time())
            }
            sio.emit('heartbeat', heartbeat_data, namespace='/trade')
            return True
        time.sleep(1)
    return False

# 4. 注册客户端
def register_via_socketio():
    data = {
        "username": config["username"],
        "client_type": "easytrader_ths",
        "timestamp": int(time.time())
    }

    # 在 /trade 命名空间中发送注册消息
    sio.emit('register', data, namespace='/trade')
    return True

# 5. 处理命令
@sio.on('execute_command', namespace='/trade')
def on_execute_command(data):
    action = data.get('action')
    params = data.get('params', {})

    # 执行相应的操作
    result = execute_command(action, params)

    # 发送响应
    sio.emit('command_response', {
        'success': True,
        'action': action,
        'result': result
    }, namespace='/trade')
```

## 7. 注意事项

1. **端口池配置**：
   - 确保端口范围不与其他服务冲突
   - 端口范围应足够大，以支持多个客户端同时连接

2. **资源清理**：
   - 定期检查并清理不活跃的端口分配，避免资源泄漏
   - 客户端应在不再使用时主动释放端口

3. **错误处理**：
   - 处理端口分配失败的情况
   - 处理 Socket.IO 服务器启动失败的情况

4. **安全性**：
   - 确保只有经过认证的用户才能获取端口
   - 考虑添加额外的安全措施，如连接超时和请求限制

## 8. 扩展性

Socket 连接池管理模块设计为可扩展的，可以轻松添加新的业务模块：

1. 在 `NAMESPACES` 常量中添加新的命名空间
2. 实现新的业务模块，遵循接口规范
3. 在 `startWebSocketServer` 函数中添加新的命名空间初始化代码

## 9. 故障排除

1. **端口分配失败**：
   - 检查端口池配置
   - 检查端口是否被其他服务占用

2. **连接传递失败**：
   - 检查业务模块是否正确实现了 `initSocketIO` 方法
   - 检查命名空间是否正确

3. **客户端连接失败**：
   - 检查端口是否正确
   - 检查命名空间是否正确
   - 检查认证信息是否正确
