/**
 * An exponential moving average (EMA) is a type of moving average (MA)
 * that places a greater weight and significance on the most recent data points.
 * The exponential moving average is also referred to as the exponentially weighted moving average.
 * An exponentially weighted moving average reacts more significantly to recent price changes
 * than a simple moving average (SMA), which applies an equal weight to all observations in the period.
 */
export class EMA {
    private smooth: number;
    private ema: number | undefined;

    constructor(private period: number) {
        // α = 2/(N+1), N 是 EMA 的周期
        this.smooth = 2 / (this.period + 1);
    }

    /**
     * Get next value for closed candle
     * affect all next calculations
     * EMA = Price * α + EMA(prev) * (1-α)
     */
    nextValue(value: number): number {
        // 第一个值直接作为 EMA 的初始值
        if (this.ema === undefined) {
            this.ema = value;
            return value;
        }

        // EMA = Price * α + EMA(prev) * (1-α)
        this.ema = value * this.smooth + this.ema * (1 - this.smooth);
        return this.ema;
    }

    /**
     * Get next value for non closed (tick) candle
     * does not affect any next calculations
     */
    momentValue(value: number): number {
        if (this.ema === undefined) {
            return value;
        }
        
        // 使用同样的公式但不保存结果
        return value * this.smooth + this.ema * (1 - this.smooth);
    }
}
