import React, { useEffect, useState } from 'react';
import { Button, Space, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { LoginForm, ProFormText, ProFormInstance } from '@ant-design/pro-components';
import { UserOutlined, LockOutlined, GithubOutlined } from '@ant-design/icons';
import { EventBus } from '../../events/eventBus';
import { UserEvents } from '../../events/events';
import { FrontendConfig } from '@/shared_types/enviorment';
import { useTheme } from '@/models/useTheme';

const ApprovalPage: React.FC = () => {
  const formRef = React.useRef<ProFormInstance>();
  const navigate = useNavigate();
  const { theme, isDarkMode } = useTheme();
  const [config, setConfig] = useState<FrontendConfig | null>(null);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  useEffect(() => {
    // 从public目录获取配置文件
    fetch('/config.json')
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        setConfig(data); // 设置配置数据
      })
      .catch(error => {
        console.error('Error fetching config:', error);
      });
  }, []);

  const handleApprove = () => {
    EventBus.emit(UserEvents.Types.APPROVE_USER, { usernametobeapproved: username });
  };

  useEffect(() => {
    const handleApproveUserResult = (result: UserEvents.ApproveUserResultPayload) => {
      if (result.success) {
        message.success(result.message || '用户已批准！');
      } else {
        message.error(result.message || '批准用户失败，请重试');
      }
    };

    // 监听 APPROVE_USER_RESULT 事件
    EventBus.on(UserEvents.Types.APPROVE_USER_RESULT, handleApproveUserResult);

    return () => {
      // 清理事件监听
      EventBus.off(UserEvents.Types.APPROVE_USER_RESULT, handleApproveUserResult);
    };
  }, []);

  return (
    <div style={{ 
      height: '100vh',
      background: isDarkMode ? '#141414' : '#f0f2f5',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <LoginForm
        formRef={formRef}
        logo={<GithubOutlined style={{ fontSize: 48, color: isDarkMode ? '#fff' : '#000' }} />}
        title={<div style={{ color: isDarkMode ? '#fff' : '#000' }}>{config?.title || "QuantQuart"}</div>}
        subTitle={<div style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.45)',  fontSize: '24px', }}>用户审批</div>}
        style={{
          backgroundColor: isDarkMode ? '#1f1f1f' : '#fff',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: isDarkMode 
            ? '0 2px 8px rgba(0, 0, 0, 0.45)' 
            : '0 2px 8px rgba(0, 0, 0, 0.15)'
        }}
        submitter={{
          searchConfig: {
            submitText: '批准用户',
          },
        }}
        onFinish={handleApprove}
        actions={
          <Space direction="vertical" align="center" style={{ width: '100%', color: isDarkMode ? '#fff' : '#000' }}>
            <div>
              还没有账号？
              <a onClick={() => navigate('/register')} style={{ fontWeight: 'bold', color: isDarkMode ? '#1890ff' : '#1677ff' }}>
                立即注册
              </a>
            </div>
          </Space>
        }
      >
        <ProFormText
          name="username"
          fieldProps={{
            size: 'large',
            prefix: <UserOutlined />,
            onChange: e => setUsername(e.target.value),
          }}
          placeholder="待审批用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        />
        
      </LoginForm>
    </div>
  );
};

export default ApprovalPage;