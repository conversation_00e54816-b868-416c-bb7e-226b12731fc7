import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Card, Space, Radio, Select, Button, Divider, FloatButton, Tooltip, Dropdown, MenuProps, Progress } from 'antd';
import { init, dispose, registerIndicator, getFigureClass, LayoutChildType, FormatDateType } from 'klinecharts';
import {
  LineChartOutlined,
  BarChartOutlined,
  AreaChartOutlined,
  ReloadOutlined,
  SyncOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { registerMultiSymbolLines } from '@/_Modules/Indicators/klinechart_customIndicators/MultiSymbolLines';
import { RxDoubleArrowRight } from 'react-icons/rx';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';
import { GoGear } from 'react-icons/go';
import { IoMdClose } from 'react-icons/io';
import { EventBus } from '@/events/eventBus';
import { ChartEvents, MarketEvents, IndicatorEvents, RealtimeEvents, SymbolSelectEvents } from '@/events/events';
import { KLineInterval, Symbol as MarketSymbol, IndicatorType } from '@/shared_types/market';
import { ChartTimeRange, DrawingLineRecord } from '@/shared_types/chart';
import { selectedSymbolAtom, selectedPeriodAtom, indicatorInstancesAtom, jotaiStore, activeToolbarButtonAtom, initialDrawingLinesAtom } from '@/store/state';
import { useAtom, useAtomValue } from 'jotai';
import { registerDynamicIndicators } from '@/_Modules/Indicators/klinechart_customIndicators/UniversalIndicator';
import { IndicatorWrapper, INDICATORS } from '@/_Modules/Indicators/IndicatorWrapper';
import indicatorListManager from '@/_Modules/Indicators/IndicatorListManager';
import { getTradingViewStyles, ictStyle } from '@/config/chartStyles';
import { indicatorIdMap } from '@/_Modules/Indicators/klinechart_customIndicators/UniversalIndicator';
import { ToolbarButtonType } from '@/shared_types/ui';
import './SignalOverlays'; // 导入信号覆盖物定义
import { registerTriangleDown, registerTriangleUp } from './SignalOverlays';
import { registerPriceChannel } from './RectOverlay';
import { registerMeasure } from './MeasureOverlay';
import { currentSymbolAtom, currentPeriodAtom } from '@/store/state';
import { klineAtom } from '@/store/state';
import { setIndicatorInstances, getIndicatorInstances } from '@/store/global';
import './index.less';
import { isMobileDevice } from '@/App';  // 添加导入
import IndicatorParamsModal from './IndicatorParamsModal';  // 导入参数配置对话框组件
import { MarketType } from '@/shared_types/market'; // 导入 MarketType
import { getCurrentStepInterval } from '../ToolBar/index';
import PlaybackFloatingWindow from '../PlaybackFloatingWindow';

// 定义信号类型
interface Signal {
  time: number;
  type: 'BUY' | 'SELL';
  price: number;
  index: number;
}

// 使用本地变量保存当前的品种和周期数据
let currentSymbolVar: MarketSymbol;
let currentPeriodVar: KLineInterval;

// 维护副图面板ID列表
let panes: string[] = [];

/**
 * 走势分析面板组件
 * 基于 klinecharts v10 版本实现
 */
const KLineChartPanel: React.FC = () => {
  // 图表实例引用
  const chartRef = useRef<any>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // 将 signalIndicatorIds 移动到组件内部
  const signalIndicatorIds = useRef<string[]>([]);

  //const [indicatorInstances, setIndicatorInstances] = useAtom(indicatorInstancesAtom);

  // 添加 myOverlayIds ref 用于管理画线，更换品种的时候清空
  const myOverlayIds = useRef<string[]>([]);

  const [selectedSymbol, setSelectedSymbol] = useAtom(selectedSymbolAtom);
  const [selectedPeriod, setSelectedPeriod] = useAtom(selectedPeriodAtom);

  const currentSymbol = useAtomValue(selectedSymbolAtom);
  const currentPeriod = useAtomValue(selectedPeriodAtom);

  const kline = useAtomValue(klineAtom);

  // 使用全局工具状态
  const [activeToolbarButton, setActiveToolbarButton] = useAtom(activeToolbarButtonAtom);

  // 区域选择状态
  const rangeStartRef = useRef<{ x: number; time: number; index: number } | null>(null);
  const [selectedRange, setSelectedRange] = useState<ChartTimeRange | null>(null);
  // 添加一个状态来跟踪是否是第一个点还是第二个点
  const [isFirstPoint, setIsFirstPoint] = useState(true);

  const [isScrollButtonVisible, setIsScrollButtonVisible] = useState(false);

  // 添加参数配置对话框的状态
  const [paramsModalVisible, setParamsModalVisible] = useState(false);
  const [currentPaneId, setCurrentPaneId] = useState<string>('');
  const [currentIndicators, setCurrentIndicators] = useState<any[]>([]);
  // 添加实时数据状态
  const [realtimeStatus, setRealtimeStatus] = useState<'hidden' | 'loading' | 'error'>('hidden');

  const [isPlaybackMode, setIsPlaybackMode] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackStep, setPlaybackStep] = useState(10); // 秒
  const [playbackIndex, setPlaybackIndex] = useState(0);
  const playbackTimer = useRef<NodeJS.Timeout | null>(null);
  const fullKlineData = useRef<any[]>([]); // 保存完整K线数据

  const [isSelectingPlaybackStart, setIsSelectingPlaybackStart] = useState(false);
  const [selectedPlaybackStartIndex, setSelectedPlaybackStartIndex] = useState<number | null>(null);

  // 悬浮窗状态管理
  const [floatingWindowVisible, setFloatingWindowVisible] = useState(false);

  // 选股进度状态
  const [stockSelectionProgress, setStockSelectionProgress] = useState<{
    visible: boolean;
    current?: number;
    total?: number;
    selectedCount?: number;
    stage?: string;
    taskId?: string;
  }>({
    visible: false,
    current: 0,
    total: 0,
    selectedCount: 0,
    stage: ''
  });

  // 持久化isPlaying和playbackIndex，防止事件监听闭包
  const isPlayingRef = useRef(isPlaying);
  const playbackIndexRef = useRef(playbackIndex);
  useEffect(() => { isPlayingRef.current = isPlaying; }, [isPlaying]);
  useEffect(() => { playbackIndexRef.current = playbackIndex; }, [playbackIndex]);

  // 在组件顶部添加useRef声明
  const subStepRef = useRef(0);

  // 在组件顶部添加ref持久化playbackStep
  const playbackStepRef = useRef(playbackStep);
  useEffect(() => { playbackStepRef.current = playbackStep; }, [playbackStep]);

  // 注册成交量指标
  registerIndicator({
    name: 'volume_in_main_chart',
    shortName: '',
    zLevel: -1,
    figures: [],
    calc: dataList => dataList.map(data => ({ volume: data.volume, close: data.close, open: data.open })),

    draw: ({ ctx, chart, indicator, bounding, xAxis }) => {
      const { realFrom, realTo } = chart.getVisibleRange();
      const { gapBar, halfGapBar } = chart.getBarSpace()
      const { result } = indicator;
      let maxVolume = 0;
      for (let i = realFrom; i < realTo; i++) {
        const data = result[i];
        if (data) {
          maxVolume = Math.max(maxVolume, data.volume || 0);
        }
      }
      const totalHeight = bounding.height * 0.25;
      const Rect = getFigureClass('rect');
      for (let i = realFrom; i < realTo; i++) {
        const data = result[i];
        if (data) {
          const height = Math.round((data.volume || 0) / maxVolume * totalHeight);
          const color = data.open < data.close ? 'rgba(255, 82, 82, 0.2)' : 'rgba(0, 150, 136, 0.2)';

          if (Rect) {
            new Rect({
              name: 'rect',
              attrs: {
                x: xAxis.convertToPixel(i) - halfGapBar,
                y: bounding.height - height,
                width: gapBar,
                height
              },
              styles: { color }
            }).draw(ctx);
          }
        }
      }
      return true;
    }
  });

  // 删除所有信号指示器
  const removeSignalIndicators = useCallback(() => {
    if (!chartRef.current) return;

    console.log('[KLineChartPanel] 删除所有信号指示器');

    // 删除所有记录的信号指示器
    signalIndicatorIds.current.forEach(id => {
      chartRef.current?.removeOverlay(id);
    });

    // 清空ID数组
    signalIndicatorIds.current = [];
  }, []);

  // 创建买卖信号指示器
  const createSignalIndicators = useCallback((signals: Signal[]) => {
    if (!chartRef.current) return;

    console.log('[KLineChartPanel] 创建信号指示器:', signals);

    // 先删除现有的信号指示器
    removeSignalIndicators();

    // 准备买卖信号的覆盖物配置
    const overlays = signals.map(signal => ({
      name: signal.type === 'BUY' ? 'triangleup' : 'triangledown',
      paneId: 'candle_pane',
      points: [
        {
          timestamp: signal.time < 10000000000 ? signal.time * 1000 : signal.time,
          value: signal.price
        }
      ]
    }));

    console.log('[KLineChartPanel] 创建信号指示器数据结构:', overlays);

    // 逐个创建，并且收集id
    overlays.forEach(overlay => {
      const id = chartRef.current.createOverlay(overlay);
      if (id) {
        signalIndicatorIds.current.push(id);
      }
    });
  }, [removeSignalIndicators]);

  // 切换图表类型
  const changeChartType = (type: string) => {
    if (chartRef.current) {
      chartRef.current.setStyles({
        candle: {
          type
        }
      });
    }
  };

  // 处理区域选择确认
  const handleRangeConfirm = useCallback(() => {
    if (!selectedRange) return;

    // 发送区域选择确认事件
    EventBus.emit(ChartEvents.Types.RANGE_SELECTED, {
      range: selectedRange
    });

    // 重置工具状态
    setActiveToolbarButton(ToolbarButtonType.NONE);

    // 清除选择
    setSelectedRange(null);
    // 重置为第一个点
    setIsFirstPoint(true);

    // 恢复图表的滚动和缩放
    if (chartRef.current) {
      chartRef.current.setStyles({
        handleScroll: true,
        handleScale: true,
      });
    }
  }, [selectedRange, setActiveToolbarButton]);

  // 清除所有技术指标
  const clearAllIndicators = () => {
    if (chartRef.current) {
      // 获取当前所有指标
      const indicators = chartRef.current.getIndicators();
      console.log('[指标] 开始清除所有技术指标, 当前数量:', indicators.length);
      // 反向遍历避免索引变化问题
      indicators.reverse().forEach((indicator: any) => {
        // 如果 shortname 是 main_volume 则不删除
        // 还需要删除对应的 indicatorMap 映射表项目
        if (indicator.shortName !== 'main_volume') {
          console.log(`[指标] 正在删除指标: ${indicator.name} (ID: ${indicator.id}, shortName: ${indicator.shortName})`);
          chartRef.current?.removeIndicator(indicator.id);

          // 删除对应的 indicatorMap 映射表项目
          indicatorIdMap.delete(indicator.id);

          // 删除对应的 indicatorInstancesAtom 实例
          setIndicatorInstances(getIndicatorInstances().filter(instance => instance.id !== indicator.id));
        }
      });

      // 清空面板ID数组
      panes = [];

      console.log('[指标] 清除技术指标完成');
    }
  };

  // 删除单个指标，参数是

  // 为副图面板添加按钮
  const addButtonsToSubPane = (paneId: string) => {
    if (!chartRef.current) return;

    try {
      // 获取面板DOM元素
      const paneDOM = chartRef.current.getDom(paneId, 'main');
      if (!paneDOM) {
        console.error(`[指标] 无法找到面板DOM, paneId=${paneId}`);
        return;
      }

      // 创建按钮容器
      const buttonContainer = document.createElement('div');
      buttonContainer.className = 'indicator-pane-buttons';
      buttonContainer.style.position = 'absolute';
      buttonContainer.style.top = '5px';
      buttonContainer.style.right = '5px';
      buttonContainer.style.display = 'flex';
      buttonContainer.style.gap = '2px'; // 减小按钮间距，使按钮靠近
      buttonContainer.style.zIndex = '10';

      // 创建配置按钮
      const configButton = document.createElement('button');
      configButton.className = 'indicator-button';
      configButton.innerHTML = '<svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" height="12px" width="12px" xmlns="http://www.w3.org/2000/svg"><path d="M7.429 1.525a6.593 6.593 0 0 1 1.142 0c.036.003.108.036.137.146l.289 1.105c.147.56.55.967.997 1.189.174.086.341.183.501.29.417.278.97.423 1.53.27l1.102-.303c.11-.03.175.016.195.046.219.31.41.641.573.989.014.031.022.11-.059.19l-.815.806c-.411.406-.562.957-.53 1.456a4.588 4.588 0 0 1 0 .582c-.032.499.119 1.05.53 1.456l.815.806c.08.08.073.159.059.19a6.494 6.494 0 0 1-.573.99c-.02.029-.086.074-.195.045l-1.103-.303c-.559-.153-1.112-.008-1.529.27-.16.107-.327.204-.5.29-.449.222-.851.628-.998 1.189l-.289 1.105c-.029.11-.101.143-.137.146a6.613 6.613 0 0 1-1.142 0c-.036-.003-.108-.037-.137-.146l-.289-1.105c-.147-.56-.55-.967-.997-1.189a4.502 4.502 0 0 1-.501-.29c-.417-.278-.97-.423-1.53-.27l-1.102.303c-.11.03-.175-.016-.195-.046a6.492 6.492 0 0 1-.573-.989c-.014-.031-.022-.11.059-.19l.815-.806c.411-.406.562-.957.53-1.456a4.587 4.587 0 0 1 0-.582c.032-.499-.119-1.05-.53-1.456l-.815-.806c-.08-.08-.073-.159-.059-.19a6.44 6.44 0 0 1 .573-.99c.02-.029.086-.075.195-.045l1.103.303c.559.153 1.112.008 1.529-.27.16-.107.327-.204.5-.29.449-.222.851-.628.998-1.189l.289-1.105c.029-.11.101-.143.137-.146zM8 0c-.236 0-.47.01-.701.03-.743.065-1.29.615-1.458 1.261l-.29 1.106c-.017.066-.078.158-.211.224a5.994 5.994 0 0 0-.668.386c-.123.082-.233.09-.3.071L3.27 2.776c-.644-.177-1.392.02-1.82.63a7.977 7.977 0 0 0-.704 1.217c-.315.675-.111 1.422.363 1.891l.815.806c.05.048.098.147.088.294a6.084 6.084 0 0 0 0 .772c.01.147-.038.246-.088.294l-.815.806c-.474.469-.678 1.216-.363 1.891.2.428.436.835.704 1.218.428.609 1.176.806 1.82.63l1.103-.303c.066-.019.176-.011.299.071.213.143.436.272.668.386.133.066.194.158.212.224l.289 1.106c.169.646.715 1.196 1.458 1.26a8.094 8.094 0 0 0 1.402 0c.743-.064 1.29-.614 1.458-1.26l.29-1.106c.017-.066.078-.158.211-.224a5.98 5.98 0 0 0 .668-.386c.123-.082.233-.09.3-.071l1.102.302c.644.177 1.392-.02 1.82-.63.268-.382.505-.789.704-1.217.315-.675.111-1.422-.364-1.891l-.814-.806c-.05-.048-.098-.147-.088-.294a6.1 6.1 0 0 0 0-.772c-.01-.147.039-.246.088-.294l.814-.806c.475-.469.679-1.216.364-1.891a7.992 7.992 0 0 0-.704-1.218c-.428-.609-1.176-.806-1.82-.63l-1.103.303c-.066.019-.176.011-.299-.071a5.991 5.991 0 0 0-.668-.386c-.133-.066-.194-.158-.212-.224L10.16 1.29C9.99.645 9.444.095 8.701.031A8.094 8.094 0 0 0 8 0zm1.5 8a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zM11 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"></path></svg>';
      configButton.title = '配置指标';
      configButton.onclick = () => configIndicator(paneId);

      // 创建关闭按钮
      const closeButton = document.createElement('button');
      closeButton.className = 'indicator-button';
      closeButton.innerHTML = '<svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" height="12px" width="12px" xmlns="http://www.w3.org/2000/svg"><path d="M278.6 256l68.2-68.2c6.2-6.2 6.2-16.4 0-22.6-6.2-6.2-16.4-6.2-22.6 0L256 233.4l-68.2-68.2c-6.2-6.2-16.4-6.2-22.6 0-3.1 3.1-4.7 7.2-4.7 11.3 0 4.1 1.6 8.2 4.7 11.3l68.2 68.2-68.2 68.2c-3.1 3.1-4.7 7.2-4.7 11.3 0 4.1 1.6 8.2 4.7 11.3 6.2 6.2 16.4 6.2 22.6 0l68.2-68.2 68.2 68.2c6.2 6.2 16.4 6.2 22.6 0 6.2-6.2 6.2-16.4 0-22.6L278.6 256z"></path></svg>';
      closeButton.title = '关闭指标';
      closeButton.onclick = () => closePane(paneId);

      // 添加按钮到容器
      buttonContainer.appendChild(configButton);
      buttonContainer.appendChild(closeButton);

      // 添加容器到面板
      paneDOM.appendChild(buttonContainer);

      console.log(`[指标] 成功为面板 ${paneId} 添加了按钮`);
    } catch (error) {
      console.error(`[指标] 为面板添加按钮失败:`, error);
    }
  };

  // 配置指标
  const configIndicator = (paneId: string) => {
    console.log(`[指标] 配置指标面板 paneId: ${paneId}`);

    if (!chartRef.current) return;

    try {
      // 获取指定面板的所有指标
      const indicators = chartRef.current.getIndicators({ paneId });
      console.log(`[指标] 获取到面板 ${paneId} 的指标:`, indicators);

      // 获取每个指标的参数
      // 为 indicator 参数添加 any 类型注解
      const indicatorConfigs = indicators.map((indicator: any) => {
        // 从映射表获取 wrapper 信息
        const wrapperInfo = indicatorIdMap.get(indicator.id);
        if (!wrapperInfo) {
          console.warn(`[指标] 未找到指标 ${indicator.id} 的wrapper信息`);
          return null;
        }

        // 获取 IndicatorWrapper 实例
        const wrapper = getIndicatorInstances().find(instance => instance.id === wrapperInfo.wrapperId);
        if (!wrapper) {
          console.warn(`[指标] 未找到指标实例 ${wrapperInfo.wrapperId}`);
          return null;
        }

        return {
          indicatorId: indicator.id,
          wrapperId: wrapperInfo.wrapperId,
          type: wrapperInfo.type,
          params: wrapper.getParams()
        };
      }).filter((config: any) => config !== null); // 为 config 参数添加 any 类型注解

      console.log(`[指标] 获取到的指标配置:`, indicatorConfigs);

      // 发送事件，显示参数配置对话框
      // 添加 as any 类型断言以匹配实际发送的数据结构
      EventBus.emit(IndicatorEvents.Types.SHOW_PARAMS_MODAL, {
        paneId,
        indicators: indicatorConfigs
      } as any);

    } catch (error) {
      console.error(`[指标] 获取指标配置失败:`, error);
    }
  };

  // 关闭指标面板
  const closePane = (indicatorId: string) => {
    console.log(`[指标] 关闭指标面板 ID: ${indicatorId}`);
    if (chartRef.current) {
      // 移除指标
      chartRef.current.removeIndicator({ paneId: indicatorId });

      // 从面板ID列表中移除
      panes = panes.filter(id => id !== indicatorId);

      // 删除对应的 indicatorMap 映射表项目
      indicatorIdMap.delete(indicatorId);

      // 删除对应的 indicatorInstancesAtom 实例
      setIndicatorInstances(getIndicatorInstances().filter(instance => instance.id !== indicatorId));

      console.log(`[指标] 已关闭副图指标, ID: ${indicatorId}`);
    }
  };

  // 为主图添加配置按钮
  const addButtonToMain = () => {
    if (!chartRef.current) return;

    try {
      // 获取主图DOM元素
      const mainPaneDOM = chartRef.current.getDom('candle_pane', 'main');
      if (!mainPaneDOM) {
        console.error('[指标] 无法找到主图DOM');
        return;
      }

      // 检查是否已经添加了按钮
      if (mainPaneDOM.querySelector('.indicator-pane-buttons')) {
        console.log('[指标] 主图已有配置按钮，不重复添加');
        return;
      }

      // 创建按钮容器
      const buttonContainer = document.createElement('div');
      buttonContainer.className = 'indicator-pane-buttons';
      buttonContainer.style.position = 'absolute';
      buttonContainer.style.top = '5px';
      buttonContainer.style.right = '5px';
      buttonContainer.style.display = 'flex';
      buttonContainer.style.gap = '2px';
      buttonContainer.style.zIndex = '10';

      // 创建配置按钮
      const configButton = document.createElement('button');
      configButton.className = 'indicator-button';
      configButton.innerHTML = '<svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" height="12px" width="12px" xmlns="http://www.w3.org/2000/svg"><path d="M7.429 1.525a6.593 6.593 0 0 1 1.142 0c.036.003.108.036.137.146l.289 1.105c.147.56.55.967.997 1.189.174.086.341.183.501.29.417.278.97.423 1.53.27l1.102-.303c.11-.03.175.016.195.046.219.31.41.641.573.989.014.031.022.11-.059.19l-.815.806c-.411.406-.562.957-.53 1.456a4.588 4.588 0 0 1 0 .582c-.032.499.119 1.05.53 1.456l.815.806c.08.08.073.159.059.19a6.494 6.494 0 0 1-.573.99c-.02.029-.086.074-.195.045l-1.103-.303c-.559-.153-1.112-.008-1.529.27-.16.107-.327.204-.5.29-.449.222-.851.628-.998 1.189l-.289 1.105c-.029.11-.101.143-.137.146a6.613 6.613 0 0 1-1.142 0c-.036-.003-.108-.037-.137-.146l-.289-1.105c-.147-.56-.55-.967-.997-1.189a4.502 4.502 0 0 1-.501-.29c-.417-.278-.97-.423-1.53-.27l-1.102.303c-.11.03-.175-.016-.195-.046a6.492 6.492 0 0 1-.573-.989c-.014-.031-.022-.11.059-.19l.815-.806c.411-.406.562-.957.53-1.456a4.587 4.587 0 0 1 0-.582c.032-.499-.119-1.05-.53-1.456l-.815-.806c-.08-.08-.073-.159-.059-.19a6.44 6.44 0 0 1 .573-.99c.02-.029.086-.075.195-.045l1.103.303c.559.153 1.112.008 1.529-.27.16-.107.327-.204.5-.29.449-.222.851-.628.998-1.189l.289-1.105c.029-.11.101-.143.137-.146zM8 0c-.236 0-.47.01-.701.03-.743.065-1.29.615-1.458 1.261l-.29 1.106c-.017.066-.078.158-.211.224a5.994 5.994 0 0 0-.668.386c-.123.082-.233.09-.3.071L3.27 2.776c-.644-.177-1.392.02-1.82.63a7.977 7.977 0 0 0-.704 1.217c-.315.675-.111 1.422.363 1.891l.815.806c.05.048.098.147.088.294a6.084 6.084 0 0 0 0 .772c.01.147-.038.246-.088.294l-.815.806c-.474.469-.678 1.216-.363 1.891.2.428.436.835.704 1.218.428.609 1.176.806 1.82.63l1.103-.303c.066-.019.176-.011.299.071.213.143.436.272.668.386.133.066.194.158.212.224l.289 1.106c.169.646.715 1.196 1.458 1.26a8.094 8.094 0 0 0 1.402 0c.743-.064 1.29-.614 1.458-1.26l.29-1.106c.017-.066.078-.158.211-.224a5.98 5.98 0 0 0 .668-.386c.123-.082.233-.09.3-.071l1.102.302c.644.177 1.392-.02 1.82-.63.268-.382.505-.789.704-1.217.315-.675.111-1.422-.364-1.891l-.814-.806c-.05-.048-.098-.147-.088-.294a6.1 6.1 0 0 0 0-.772c-.01-.147.039-.246.088-.294l.814-.806c.475-.469.679-1.216.364-1.891a7.992 7.992 0 0 0-.704-1.218c-.428-.609-1.176-.806-1.82-.63l-1.103.303c-.066.019-.176.011-.299-.071a5.991 5.991 0 0 0-.668-.386c-.133-.066-.194-.158-.212-.224L10.16 1.29C9.99.645 9.444.095 8.701.031A8.094 8.094 0 0 0 8 0zm1.5 8a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zM11 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"></path></svg>';
      configButton.title = '配置指标';
      configButton.onclick = () => configIndicator('candle_pane');

      // 添加按钮到容器
      buttonContainer.appendChild(configButton);

      // 添加容器到主图
      mainPaneDOM.appendChild(buttonContainer);

      console.log('[指标] 成功为主图添加了配置按钮');
    } catch (error) {
      console.error('[指标] 为主图添加按钮失败:', error);
    }
  };

  // 调入配置就绪，处理函数
  const addConfiguredIndicators = (savedIndicators: IndicatorWrapper[]) => {
    console.log('[指标] addConfiguredIndicators - 开始根据保存的配置添加指标:', savedIndicators);
    savedIndicators.forEach(indicator => {
      let indicatorId = null;
      const indicatorName = indicator.getName();
      console.log(`[指标] addConfiguredIndicators - 准备添加: ${indicatorName}, 主图: ${indicator.getIsMain()}`);

      try {
        if (indicator.getIsMain()) {
          console.log(`[指标] addConfiguredIndicators - 调用 createIndicator, 参数: ${indicatorName}, true, { id: 'candle_pane' }`);
          indicatorId = chartRef.current?.createIndicator(indicatorName, true, { id: 'candle_pane' });
          // 为主图添加配置按钮
          addButtonToMain();
        } else {
          // 为副图创建一个新的面板ID
          const paneId = `pane_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
          panes.push(paneId);

          console.log(`[指标] addConfiguredIndicators - 调用 createIndicator, 参数: ${indicatorName}, false, { id: ${paneId} }`);
          indicatorId = chartRef.current?.createIndicator(indicatorName, false, { id: paneId });

          if (indicatorId) {
            // 为副图添加按钮
            addButtonsToSubPane(paneId);
          }
        }

        // 创建从 定制指标Id 到 IndicatorWrapper实例Id 的映射
        if (indicatorId) {
          console.log(`[指标] addConfiguredIndicators - 创建映射: klinecharts ID ${indicatorId} -> Wrapper ID ${indicator.id} (Type: ${indicator.type})`);
          indicatorIdMap.set(indicatorId, { type: indicator.type, wrapperId: indicator.id });
        } else {
          console.warn(`[指标] addConfiguredIndicators - 未能为 ${indicatorName} 创建 klinecharts 指标实例`);
        }
      } catch (error) {
        console.error(`[指标] addConfiguredIndicators - 添加指标 ${indicatorName} 失败:`, error);
      }
    });
    console.log('[指标] addConfiguredIndicators - 完成添加保存的指标');
  };

  // 将 公共状态的指标实例 添加到图表
  // 与上面函数重复
  const applyIndicatorsToChart = () => {
    if (!chartRef.current || !getIndicatorInstances()) return;

    console.log('[指标] applyIndicatorsToChart - 开始根据全局状态添加指标:', getIndicatorInstances());
    getIndicatorInstances().forEach(instance => {
      if (!instance) return;

      try {
        const indicatorName = `Custom_${instance.type}`;
        console.log(`[指标] applyIndicatorsToChart - 准备添加: ${indicatorName}, 主图: ${instance.getIsMain()}`);

        let indicatorId = null;
        if (instance.getIsMain()) {
          console.log(`[指标] applyIndicatorsToChart - 调用 createIndicator, 参数: ${indicatorName}, true, { id: 'candle_pane' }`);
          indicatorId = chartRef.current.createIndicator(indicatorName, true, { id: 'candle_pane' });
          // 为主图添加配置按钮
          addButtonToMain();
        } else {
          // 为副图创建一个新的面板ID
          const paneId = `pane_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
          panes.push(paneId);

          console.log(`[指标] applyIndicatorsToChart - 调用 createIndicator, 参数: ${indicatorName}, false, { id: ${paneId} }`);
          indicatorId = chartRef.current.createIndicator(indicatorName, false, { id: paneId });

          if (indicatorId) {
            // 为副图添加按钮
            addButtonsToSubPane(paneId);
          }
        }

        // 创建从 定制指标Id 到 IndicatorWrapper实例Id 的映射
        if (indicatorId) {
          console.log(`[指标] applyIndicatorsToChart - 创建映射: klinecharts ID ${indicatorId} -> Wrapper ID ${instance.id} (Type: ${instance.type})`);
          indicatorIdMap.set(indicatorId, { type: instance.type, wrapperId: instance.id });
        } else {
           console.warn(`[指标] applyIndicatorsToChart - 未能为 ${indicatorName} 创建 klinecharts 指标实例`);
        }
      } catch (error) {
        console.error(`[指标] applyIndicatorsToChart - 添加指标 ${instance.type} 失败:`, error);
      }
    });
    console.log('[指标] applyIndicatorsToChart - 完成添加全局状态的指标');
  };

  // 调整图表布局，减少顶部空白并确保正确填充容器
  const adjustChartLayout = () => {
    if (chartRef.current) {
      // 获取容器实际高度
      const containerHeight = chartContainerRef.current?.clientHeight || 0;
      const containerWidth = chartContainerRef.current?.clientWidth || 0;

      console.log('[KLineChartPanel] 调整图表布局，容器尺寸:', containerWidth, 'x', containerHeight);

      // 设置图表的自定义布局参数
      chartRef.current.setStyles({
        yAxis: {
          paddingTop: 0,
          paddingBottom: 10
        },
        candle: {
          margin: {
            top: 0.05,
            bottom: 0.05
          }
        }
      });

      // 确保图表尺寸与容器匹配
      chartRef.current.resize(containerWidth, containerHeight);
    }
  };

  // 鼠标事件处理
  useEffect(() => {
    if (!chartRef.current || !chartContainerRef.current) return;

    const container = chartContainerRef.current;

    // 通用的点击处理函数
    const handlePointSelection = (x: number, clientX: number, clientY: number) => {
      // 只有在区域选择模式下才处理
      if (activeToolbarButton !== ToolbarButtonType.RANGE_SELECT) return;

      const rect = container.getBoundingClientRect();
      const relativeX = clientX - rect.left;

      // 获取对应的时间和索引
      if (chartRef.current) {
        // 将x坐标转换为时间戳和索引
        const result = chartRef.current.convertFromPixel({ x: relativeX }, { paneId: 'candle_pane' });
        const timestamp = result.timestamp;
        const dataIndex = result.dataIndex; // 获取数据索引

        if (timestamp && dataIndex !== undefined) {
          // 如果是第一个点
          if (isFirstPoint) {
            // 保存第一个点
            rangeStartRef.current = { x: relativeX, time: timestamp, index: dataIndex };
            // 禁用图表的滚动和缩放
            chartRef.current.setStyles({
              handleScroll: false,
              handleScale: false,
            });
            // 标记下一次是第二个点
            setIsFirstPoint(false);
          } else {
            // 如果是第二个点且有起始点
            if (rangeStartRef.current) {
              // 最终确认选择范围
              const range: any = {
                fromTime: Math.min(rangeStartRef.current.time, timestamp),
                toTime: Math.max(rangeStartRef.current.time, timestamp),
                fromIndex: Math.min(rangeStartRef.current.index, dataIndex),
                toIndex: Math.max(rangeStartRef.current.index, dataIndex),
                fromX: Math.min(rangeStartRef.current.x, relativeX),
                toX: Math.max(rangeStartRef.current.x, relativeX),
                top: 0,
                bottom: container.clientHeight
              };

              // 设置选择范围
              setSelectedRange(range);
              // 重置为第一个点，为了下一次新的选择
              setIsFirstPoint(true);
            }
          }
        }
      }
    };

    // 处理鼠标点击事件（桌面端）
    const handleClick = (e: MouseEvent) => {
      if (isMobileDevice()) return; // 移动端不处理鼠标事件
      handlePointSelection(e.clientX - container.getBoundingClientRect().left, e.clientX, e.clientY);
    };

    // 处理触摸事件（移动端）
    const handleTouch = (e: TouchEvent) => {
      if (!isMobileDevice()) return; // 桌面端不处理触摸事件
      e.preventDefault(); // 阻止默认行为
      if (e.touches.length === 1) {
        const touch = e.touches[0];
        handlePointSelection(touch.clientX - container.getBoundingClientRect().left, touch.clientX, touch.clientY);
      }
    };

    // 添加鼠标移动事件来实时更新区域显示
    const handleMouseMove = (e: MouseEvent) => {
      if (isMobileDevice()) return; // 移动端不处理鼠标事件
      // 只有在已选择第一个点且在区域选择模式下才处理
      if (isFirstPoint || !rangeStartRef.current || activeToolbarButton !== ToolbarButtonType.RANGE_SELECT) return;

      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;

      if (chartRef.current) {
        // 将x坐标转换为时间戳和索引
        const result = chartRef.current.convertFromPixel({ x }, { paneId: 'candle_pane' });
        const timestamp = result.timestamp;
        const dataIndex = result.dataIndex; // 获取数据索引

        if (timestamp && dataIndex !== undefined) {
          const range: any = {
            fromTime: Math.min(rangeStartRef.current.time, timestamp),
            toTime: Math.max(rangeStartRef.current.time, timestamp),
            fromIndex: Math.min(rangeStartRef.current.index, dataIndex),
            toIndex: Math.max(rangeStartRef.current.index, dataIndex),
            fromX: Math.min(rangeStartRef.current.x, x),
            toX: Math.max(rangeStartRef.current.x, x),
            top: 0,
            bottom: container.clientHeight
          };

          // 实时更新选择范围
          setSelectedRange(range);
        }
      }
    };

    // 处理触摸移动事件（移动端）
    const handleTouchMove = (e: TouchEvent) => {
      if (!isMobileDevice()) return; // 桌面端不处理触摸事件
      e.preventDefault(); // 阻止默认行为
      if (e.touches.length === 1) {
        const touch = e.touches[0];
        const rect = container.getBoundingClientRect();
        const x = touch.clientX - rect.left;

        // 只有在已选择第一个点且在区域选择模式下才处理
        if (isFirstPoint || !rangeStartRef.current || activeToolbarButton !== ToolbarButtonType.RANGE_SELECT) return;

        if (chartRef.current) {
          // 将x坐标转换为时间戳和索引
          const result = chartRef.current.convertFromPixel({ x }, { paneId: 'candle_pane' });
          const timestamp = result.timestamp;
          const dataIndex = result.dataIndex;

          if (timestamp && dataIndex !== undefined) {
            const range: any = {
              fromTime: Math.min(rangeStartRef.current.time, timestamp),
              toTime: Math.max(rangeStartRef.current.time, timestamp),
              fromIndex: Math.min(rangeStartRef.current.index, dataIndex),
              toIndex: Math.max(rangeStartRef.current.index, dataIndex),
              fromX: Math.min(rangeStartRef.current.x, x),
              toX: Math.max(rangeStartRef.current.x, x),
              top: 0,
              bottom: container.clientHeight
            };

            // 实时更新选择范围
            setSelectedRange(range);
          }
        }
      }
    };

    // 使用click事件选择点，mousemove事件实时更新区域
    container.addEventListener('click', handleClick);
    document.addEventListener('mousemove', handleMouseMove);

    // 添加触摸事件监听（移动端）
    container.addEventListener('touchstart', handleTouch);
    container.addEventListener('touchmove', handleTouchMove);

    return () => {
      container.removeEventListener('click', handleClick);
      document.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('touchstart', handleTouch);
      container.removeEventListener('touchmove', handleTouchMove);
    };
  }, [activeToolbarButton, isFirstPoint]);

  // 工具状态变化时的处理
  useEffect(() => {
    if (activeToolbarButton !== ToolbarButtonType.RANGE_SELECT) {
      // 清除选择状态
      setSelectedRange(null);
      rangeStartRef.current = null;
      // 重置为第一个点
      setIsFirstPoint(true);

      // 恢复图表的滚动和缩放
      if (chartRef.current) chartRef.current.setScrollEnabled(true);
    }
    else
      if (chartRef.current) chartRef.current.setScrollEnabled(false);

  }, [activeToolbarButton]);

  // 检查是否需要显示滚动到最新的按钮
  const checkScrollButtonVisibility = useCallback(() => {
    if (!chartRef.current) return;
    try {
      const chart = chartRef.current;
      const dataList = chart.getDataList();
      const visibleRange = chart.getVisibleRange();
      if (!dataList || dataList.length === 0 || !visibleRange) {
        setIsScrollButtonVisible(false);
        return;
      }
      // 可见范围的最后一个索引 (realTo 是开区间，所以要减 1)
      const lastVisibleIndex = visibleRange.realTo > 0 ? visibleRange.realTo - 1 : 0;
      const lastDataIndex = dataList.length - 1;

      // 如果最后一个可见索引小于最后一个数据索引，则显示按钮
      setIsScrollButtonVisible(lastVisibleIndex < lastDataIndex);
    } catch (error) {
      console.error("[KLineChartPanel] 检查滚动按钮可见性失败:", error);
      setIsScrollButtonVisible(false);
    }
  }, []);

  // 滚动到最新的 K 线
  const scrollToLatest = useCallback(() => {
    if (!chartRef.current) return;
    try {
      const dataList = chartRef.current.getDataList();
      if (dataList && dataList.length > 0) {
        chartRef.current.scrollToDataIndex(dataList.length - 1, 200);
      }
    } catch (error) {
      console.error("[KLineChartPanel] 滚动到最新失败:", error);
    }
  }, []);

  // 监听选股进度事件
  useEffect(() => {
    console.log('[KLineChartPanel] [DEBUG] 开始监听选股进度事件');

    // 订阅选股开始事件，立即显示浮窗
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_STARTED, (payload: SymbolSelectEvents.StockSelectionStartedPayload) => {
      console.log('[KLineChartPanel] [DEBUG] 收到选股开始通知:', payload);
      
      // 立即显示选股进度浮窗
      setStockSelectionProgress({
        visible: true,
        taskId: payload.taskId,
        current: 0,
        total: 0,
        selectedCount: 0,
        stage: '开始选股...'
      });
      
      console.log('[KLineChartPanel] [DEBUG] 已显示选股进度浮窗');
    });

    // 订阅选股进度更新事件
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS, (progress: SymbolSelectEvents.StockSelectionProgress) => {
      console.log('[KLineChartPanel] [DEBUG] 收到选股进度更新:', progress);
      console.log('[KLineChartPanel] [DEBUG] 进度详情:', {
        taskId: progress.taskId,
        stage: progress.stage,
        current: progress.current,
        total: progress.total,
        selectedCount: progress.selectedCount
      });

      // 更新进度状态
      setStockSelectionProgress({
        visible: true,
        taskId: progress.taskId,
        current: progress.current,
        total: progress.total,
        selectedCount: progress.selectedCount,
        stage: progress.stage
      });

      // 添加调试日志
      console.log('[KLineChartPanel] [DEBUG] 已更新选股进度状态，浮窗应该显示');
    });

    // 订阅选股完成事件，隐藏进度条
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_COMPLETED, (result: SymbolSelectEvents.StockSelectionResult) => {
      console.log('[KLineChartPanel] 收到选股完成通知:', result);
      
      // 隐藏选股进度浮窗
      setStockSelectionProgress({
        visible: false,
        taskId: '',
        current: 0,
        total: 0,
        selectedCount: 0,
        stage: ''
      });
    });

    // 订阅选股错误事件，隐藏进度条
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, (error: SymbolSelectEvents.StockSelectionError) => {
      console.log('[KLineChartPanel] 收到选股错误通知:', error);
      
      // 隐藏选股进度浮窗
      setStockSelectionProgress({
        visible: false,
        taskId: '',
        current: 0,
        total: 0,
        selectedCount: 0,
        stage: ''
      });
    });

    // 订阅选股停止事件，隐藏进度条
    const stoppedUnsubscribe = EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_STOPPED, () => {
      console.log('[KLineChartPanel] 选股被停止，隐藏进度条');
      setStockSelectionProgress(prev => ({ ...prev, visible: false }));
    });

    return () => {
      stoppedUnsubscribe.unsubscribe();
    };
  }, []);

  // 初始化图表
  useEffect(() => {
    // 确保组件挂载时创建图表
    console.log('初始化K线图...');

    // 注册所有动态指标
    registerDynamicIndicators();

    // 注册多品种重叠指标
    registerMultiSymbolLines();

    // 初始化图表
    chartRef.current = init('kline-chart-container', {
      layout: [
        {
          type: 'candle' as any,
          options: {
            dragEnabled: false,
            axis: {
              gap: {
                top: 40,
                bottom: 20,
              },
              scrollZoomEnabled: false, // 禁用Y轴纵向缩放
            },
          },
        }
      ],
      // 自定义日期格式化函数，添加星期几
      customApi: {
        formatDate: (timestamp: number, format: string, type) => {
          const date = new Date(timestamp);
          const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
          const weekDay = weekDays[date.getDay()];

          // 只修改十字光标的日期格式
          if (type === FormatDateType.Crosshair) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day} 周${weekDay} ${hours}:${minutes}`;
          }

          // 其他类型的日期格式保持默认
          return format
            .replace('YYYY', String(date.getFullYear()))
            .replace('MM', String(date.getMonth() + 1).padStart(2, '0'))
            .replace('DD', String(date.getDate()).padStart(2, '0'))
            .replace('HH', String(date.getHours()).padStart(2, '0'))
            .replace('mm', String(date.getMinutes()).padStart(2, '0'))
            .replace('ss', String(date.getSeconds()).padStart(2, '0'));
        }
      }
    });

    // 初始化后设置全局指标样式
    chartRef.current.setStyles({
      indicator: {
        lines: [
          { smooth: true },
          { smooth: true },
          { smooth: true },
          { smooth: true },
          { smooth: true },
          { smooth: true },
          { smooth: true },
        ]
      }
    });

    // 监听画线工具选择事件
    const drawingToolSubscription = EventBus.on(
      ChartEvents.Types.DRAWING_TOOL_SELECTED,
      (payload: ChartEvents.DrawingToolSelectedPayload) => {
        console.log('[KLineChartPanel] 收到画线工具选择事件:', payload);
        if (chartRef.current && payload.overlayName) {
          // 调用 createOverlay 进入画线状态，添加 onDrawEnd 回调
          chartRef.current.createOverlay({

            name: payload.overlayName,

            onDrawEnd: (event: { overlay: { id: string } }) => {
              console.log('[KLineChartPanel] 画线完成:', event);
              // 获取并保存 overlay id
              const overlayId = event.overlay.id;
              myOverlayIds.current.push(overlayId);
              console.log('[KLineChartPanel] 添加画线 ID:', overlayId, '到 myOverlayIds');
              // 发送画线完成事件
              EventBus.emit(ChartEvents.Types.DRAWING_COMPLETED, {
                overlayName: payload.overlayName
              });

              // 先保存当前画线数据
              saveCurrentDrawingLines();

              return true;
            },

            /*onRemoved: (event: { overlay: { id: string } }) => {
              console.log('[KLineChartPanel] 画线被删除:', event);

              myOverlayIds.current = myOverlayIds.current.filter(id => id !== event.overlay.id);

              console.log('[KLineChartPanel] 画线被删除:', event, myOverlayIds.current);

              // 保存画线数据
              saveCurrentDrawingLines();
            }*/
          });
        }
      }
    );

    // 设置基本样式和减小顶部空白
    chartRef.current.setStyles({
      grid: {
        horizontal: {
          show: true, // 显示水平网格线
          interval: 10, // 水平网格线的间隔（像素）
        },
        vertical: {
          show: true, // 显示垂直网格线
          interval: 10, // 垂直网格线的间隔（像素）
        },
      },
      yAxis: {
        // 减小顶部空白
        paddingTop: 0,
        paddingBottom: 10,
      },
      candle: {
        margin: {
          top: 0.05,
          bottom: 0.05
        }
      }
    });

    // 应用TradingView风格
    chartRef.current.setStyles(ictStyle);

    // 创建成交量指标，属于主图的一部分，不属于自定义指标系统
    chartRef.current.createIndicator('volume_in_main_chart', false, { id: 'candle_pane' });

    // 为主图添加配置按钮
    addButtonToMain();

    // 初始化后立即调整大小以适应容器
    setTimeout(() => {
      if (chartRef.current && chartContainerRef.current) {
        const width = chartContainerRef.current.clientWidth;
        const height = chartContainerRef.current.clientHeight;
        console.log('[KLineChartPanel] 初始化后调整图表大小:', width, 'x', height);
        chartRef.current.resize(width, height);
        adjustChartLayout();

        // 加载保存的图表配置
        console.log('[KLineChartPanel] 图表初始化完成，开始加载保存的配置');
        loadChartConfig();
      }
    }, 100);

    // 数据加载完成后调整图表布局
    const klinesReadySubscription = EventBus.on(
      MarketEvents.Types.KLINES_READY,
      (payload) => {
        console.log('[KLineChartPanel] 收到K线数据:', payload);
        if (chartRef.current && payload.kline && payload.kline.data) {

          console.log('[KLineChartPanel] 使用接收到的真实K线数据更新图表:', payload.kline.data);
          // 使用接收到的真实K线数据更新图表
          // 数据中的time 要抓成 timestamp
          chartRef.current.applyNewData(payload.kline.data.map((item: any) => ({
            ...item,
            timestamp: item.time * 1000
          })));

          console.log('[KLineChartPanel] K线图已更新 ', payload.kline.data);

          // 强制刷新图表，并传入容器尺寸
          setTimeout(() => {
            if (chartRef.current && chartContainerRef.current) {
              const width = chartContainerRef.current.clientWidth;
              const height = chartContainerRef.current.clientHeight;
              chartRef.current.resize(width, height);
              // 调整图表布局
              adjustChartLayout();
            }
          }, 100);

          // 数据更新后检查按钮可见性
          setTimeout(checkScrollButtonVisibility, 100); // 延迟检查确保渲染完成
        }
      }
    );

    // 不再监听配置就绪事件

    // 监听走势图滚动事件
    const chartScrollToIndexSubscription = EventBus.on(
      ChartEvents.Types.CHART_SCROLL_TO_INDEX,
      (payload) => {
        console.log('[KLineChartPanel] 收到走势图滚动事件:', payload);
        if (chartRef.current) {
          chartRef.current.scrollToDataIndex(payload.index + 20, 200);
        }
      }
    );

    // 监听创建多品种重叠指标事件
    const createMultiSymbolOverlaySubscription = EventBus.on(
      ChartEvents.Types.CREATE_MULTI_SYMBOL_OVERLAY as any,
      (payload: ChartEvents.CreateMultiSymbolOverlayPayload) => {
        console.log('[KLineChartPanel] 收到创建多品种重叠指标事件:', payload);

        // 确保payload.symbols不为空
        if (!payload.symbols || payload.symbols.trim() === '') {
          console.error('[KLineChartPanel] 创建多品种重叠指标失败: 品种参数为空');
          return;
        }

        if (chartRef.current) {
          // 先检查是否已经存在MultiSymbolOverlay指标，如果存在则移除
          const existingIndicators = chartRef.current.getIndicators({ name: 'MultiSymbolOverlay' });
          if (existingIndicators && existingIndicators.length > 0) {
            console.log('[KLineChartPanel] 移除已存在的多品种重叠指标');
            existingIndicators.forEach((indicator: any) => {
              chartRef.current?.removeIndicator({ id: indicator.id });
            });
          }

          // 创建新的多品种重叠指标
          console.log('[KLineChartPanel] 创建新的多品种重叠指标，参数:', payload.symbols);
          const paneId = `pane_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
          panes.push(paneId);

          try {

            console.log('[KLineChartPanel] 创建指标 createIndicator, 参数:', payload.symbols);
            // 创建新指标 - 使用官方推荐的调用方式
            const indicatorId = chartRef.current.createIndicator({
              name: 'MultiSymbolLines',
              shouldOhlc: false,
              calcParams: [payload.symbols]
            },
            false,
            {
              id: paneId
            });

            console.log('[KLineChartPanel] 创建指标结果, creaetIndicator result ID:', indicatorId);

            if (indicatorId) {
              // 为副图添加按钮
              addButtonsToSubPane(paneId);
            } else {
              console.error('[KLineChartPanel] 创建多品种重叠指标失败: 返回的ID为空');
            }
          } catch (error) {
            console.error('[KLineChartPanel] 创建多品种重叠指标时发生错误:', error);
          }
        }
      }
    );

    // 监听信号标记显示状态变化
    const signalMarkersVisibleSubscription = EventBus.on(
      MarketEvents.Types.SIGNAL_MARKERS_VISIBLE,
      (payload) => {
        console.log('[KLineChartPanel] 收到信号标记显示状态变化:', payload);
        if (payload.visible) {
          // 显示信号标记
          if (payload.signals) {
            createSignalIndicators(payload.signals);
          }
        } else {
          // 隐藏信号标记
          removeSignalIndicators();
        }
      }
    );

    // 监听滚动和缩放事件来更新按钮可见性
    chartRef.current.subscribeAction('onScroll', checkScrollButtonVisibility);
    //chartRef.current.subscribeAction('zoom', checkScrollButtonVisibility);

    // ---> 添加实时数据事件监听 <---
    const realtimeStartSubscription = EventBus.on(
      RealtimeEvents.Types.REALTIME_DATA_START,
      () => {
        console.log('[KLineChartPanel] 实时数据开始获取');
        setRealtimeStatus('loading');
      }
    );

    const realtimeErrorSubscription = EventBus.on(
      RealtimeEvents.Types.REALTIME_ERROR,
      (payload) => {
        console.log('[KLineChartPanel] 实时数据获取失败:', payload?.message);
        setRealtimeStatus('error');
      }
    );

    const realtimeEndSubscription = EventBus.on(
      RealtimeEvents.Types.REALTIME_DATA_END,
      () => {
        console.log('[KLineChartPanel] 实时数据处理完成');
        setRealtimeStatus('hidden');
      }
    );

    const klinesUpdatedSubscription = EventBus.on(
      MarketEvents.Types.KLINES_UPDATED,
      (payload: MarketEvents.KLinesUpdated) => { // 添加 payload 类型提示
        console.log('[KLineChartPanel] 收到实时K线更新事件, newDataIndex:', payload.newDataIndex);
        setRealtimeStatus('hidden'); // 更新成功，隐藏状态

        // 检查 chart 实例、数据、以及 newDataIndex 是否有效
        if (chartRef.current && payload.kline?.data && payload.kline.data.length > 0 && payload.newDataIndex !== undefined && payload.newDataIndex >= 0) {

          const allData = payload.kline.data;
          const startIndex = payload.newDataIndex; // 使用 realtimeKline.ts 提供的起始索引

          console.log(`[KLineChartPanel] 从索引 ${startIndex} 开始更新 ${allData.length - startIndex} 条数据`);

          // 如果处于回放模式，禁止实时数据更新
          if (isPlaybackMode) {
            console.log('[KLineChartPanel] 当前为回放模式，禁止实时数据更新');
            return;
          }

          // 从 newDataIndex 开始遍历所有需要更新的数据
          for (let i = startIndex; i < allData.length; i++) {
            const bar = allData[i];
            // 转换为 klinecharts 需要的格式（带 timestamp in ms）
            const updateData = {
              ...bar,
              // 确保时间戳是毫秒。假设 payload.kline.data[*].time 是秒
              timestamp: bar.time * 1000
            };
            console.log(`[KLineChartPanel] 更新图表数据 (索引 ${i}):`, updateData);
            // 逐条更新图表数据
            chartRef.current.updateData(updateData);
          }

          // 数据更新后检查滚动按钮可见性
          // 延迟检查以确保图表渲染完成
          setTimeout(checkScrollButtonVisibility, 100);
        } else {
          // 如果数据或索引无效，打印警告
          console.warn('[KLineChartPanel] 收到无效的K线更新数据或索引，无法更新图表:', payload);
        }
      }
    );
    // ---> 结束实时数据事件监听 <---

    // 组件卸载时清理
    return () => {
      console.log('销毁K线图...');
      if (chartRef.current) {
        // 删除所有信号指示器
        removeSignalIndicators();
        // 清理所有 overlays
        /*myOverlayIds.current.forEach(id => {
          chartRef.current?.removeOverlay(id);
        });*/


        console.log('[KLineChartPanel] 销毁K线图，清除 myOverlayIds');
        myOverlayIds.current = [];


        dispose('kline-chart-container');
        chartRef.current = null;

        // 取消事件订阅
        klinesReadySubscription.unsubscribe();
        // configSubscription 已移除
        chartScrollToIndexSubscription.unsubscribe();
        signalMarkersVisibleSubscription.unsubscribe();
        drawingToolSubscription.unsubscribe();
        createMultiSymbolOverlaySubscription.unsubscribe();

        if (chartRef.current) {
          chartRef.current.unsubscribeAction('onScroll', checkScrollButtonVisibility);
          //chartRef.current.unsubscribeAction('zoom', checkScrollButtonVisibility);
        }

        // ---> 清理新增的订阅 <---
        realtimeStartSubscription.unsubscribe();
        realtimeErrorSubscription.unsubscribe();
        realtimeEndSubscription.unsubscribe();
        klinesUpdatedSubscription.unsubscribe();
        // ---> 结束清理订阅 <---
      }
    };
  }, [removeSignalIndicators, createSignalIndicators, checkScrollButtonVisibility]);

  // 监听指标状态并添加到图表
  useEffect(() => {

    console.log('[指标] 监听指标状态并添加到图表:', getIndicatorInstances());

    if (!chartRef.current || !getIndicatorInstances()) return;

    console.log('[指标] 2');

    // 获取当前图表中的指标
    try {
      const currentIndicators = chartRef.current.getIndicators() || [];

      // 如果图表上没有指标或者指标数量明显少于状态中的指标数量，则应用状态中的指标
      if (currentIndicators.length <= 1 && getIndicatorInstances().length > 0) {
        console.log('[指标] 检测到状态中有指标配置，但图表中没有对应指标，正在添加...');
        applyIndicatorsToChart();
      }
    } catch (error) {
      console.error('[指标] 检查图表指标失败:', error);
    }
  }, []);

  // 监听图表容器大小变化
  useEffect(() => {
    // 确保 DOM 元素已经加载
    if (!chartContainerRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (chartRef.current) {
          const width = entry.contentRect.width;
          const height = entry.contentRect.height;
          console.log('[KLineChartPanel] 检测到容器大小变化，调整图表大小:', width, 'x', height);

          // 调用 resize 时传入具体的宽高
          chartRef.current.resize(width, height);

          // 延迟一点应用样式，确保 resize 已完成
          setTimeout(() => {
            adjustChartLayout();
          }, 50);
        }
      }
    });

    // 开始观察图表容器
    resizeObserver.observe(chartContainerRef.current);

    // 注册覆盖物
    registerTriangleUp();
    registerTriangleDown();
    registerPriceChannel();
    registerMeasure();

    // 监听删除所有画线事件
    const deleteAllDrawingLinesSubscription = EventBus.on(
      ChartEvents.Types.DELETE_ALL_DRAWING_LINES,
      () => {
        console.log('[KLineChartPanel] 收到删除所有画线事件');
        if (chartRef.current) {
          // 删除所有画线
          const overlays = chartRef.current.getOverlays();
          overlays.forEach((overlay: any) => {
            chartRef.current?.removeOverlay(overlay.id);
          });

          console.log('[KLineChartPanel] 删除所有画线，清除 myOverlayIds');
          myOverlayIds.current = [];

          // 保存当前状态（空状态）
          saveCurrentDrawingLines();
        }
      }
    );

    // 组件卸载时停止观察和取消事件订阅
    return () => {
      resizeObserver.disconnect();
      deleteAllDrawingLinesSubscription.unsubscribe();
    };
  }, []); // 空依赖数组，确保只在组件挂载时执行一次

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current && chartContainerRef.current) {
        const width = chartContainerRef.current.clientWidth;
        const height = chartContainerRef.current.clientHeight;
        //console.log('[KLineChartPanel] 检测到窗口大小变化，调整图表大小:', width, 'x', height);

        // 调用 resize 时传入具体的宽高
        chartRef.current.resize(width, height);

        // 延迟一点调整布局，等待DOM渲染完成
        setTimeout(() => {
          adjustChartLayout();
        }, 100);
      }
    };

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 组件挂载后先执行一次调整
    setTimeout(handleResize, 200);

    // 组件卸载时移除监听
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 从 localStorage 加载图表配置
  const loadChartConfig = () => {
    try {
      // 1. 从 localStorage 读取配置
      const savedConfig = localStorage.getItem('fullChartConfig');
      if (!savedConfig) {
        console.log('[KLineChartPanel] 未找到保存的配置，使用默认品种和级别');
        return;
      }

      // 2. 解析配置
      const config = JSON.parse(savedConfig);
      console.log('[KLineChartPanel] 加载保存的配置:', config);

      // 3. 应用配置中的品种和级别（如果有）
      if (config.symbol && config.period) {
        console.log('[KLineChartPanel] 使用配置中的品种和级别:', config.symbol, config.period);
        setSelectedSymbol(config.symbol);
        setSelectedPeriod(config.period);
      } else {
        console.log('[KLineChartPanel] 配置中没有品种和级别信息，使用默认值');
      }

      // 4. 应用图表样式
      if (config.styles) {
        console.log('[KLineChartPanel] 应用图表样式:', config.styles);
        chartRef.current.setStyles(config.styles);
      }

      // 5. 应用指标配置
      if (config.indicators && Array.isArray(config.indicators)) {
        console.log('[KLineChartPanel] 应用指标配置:', config.indicators);

        // 清除现有指标（除了成交量指标）
        const currentIndicators = chartRef.current.getIndicators() || [];
        currentIndicators.forEach((indicator: { name: string, id: string }) => {
          if (indicator.name !== 'volume_in_main_chart') {
            chartRef.current.removeIndicator({ id: indicator.id });
          }
        });

        // 清除现有的指标实例和映射
        setIndicatorInstances([]);
        indicatorIdMap.clear();

        // 应用保存的指标 - 使用现有的 addIndicator 函数
        config.indicators.forEach((indicator: any) => {
          try {
            // 从指标名称中提取类型（去掉 'Custom_' 前缀）
            const indicatorType = indicator.name.replace('Custom_', '') as IndicatorType;

            // 检查是否是有效的指标类型
            if (Object.values(IndicatorType).includes(indicatorType)) {
              console.log(`[KLineChartPanel] 恢复指标 ${indicatorType}`);

              // 调用现有的 addIndicator 函数
              addIndicator(indicatorType);
            } else {
              console.warn(`[KLineChartPanel] 未知的指标类型: ${indicatorType}`);
            }
          } catch (error) {
            console.error(`[KLineChartPanel] 恢复指标失败:`, error);
          }
        });
      }
    } catch (error) {
      console.error('[KLineChartPanel] 加载图表配置失败:', error);
    }
  };

  // 保存图表配置到 localStorage
  const saveChartConfig = useCallback(() => {
    if (!chartRef.current || !currentSymbolVar || !currentPeriodVar) {
      console.log('[KLineChartPanel] 当前图表、品种或周期为空，不保存配置');
      return;
    }

    try {
      // 1. 获取所有指标配置
      // getIndicators 返回的是一个 Indicator[] 数组
      let indicators = chartRef.current.getIndicators() || [];

      // 过滤掉成交量指标（volume_in_main_chart）
      indicators = indicators.filter((indicator: { name: string }) =>
        indicator.name !== 'volume_in_main_chart'
      );

      console.log('[KLineChartPanel] 获取到所有指标(已排除成交量):', indicators);

      // 2. 获取图表样式
      const styles = chartRef.current.getStyles() || {};
      console.log('[KLineChartPanel] 获取到图表样式:', styles);

      // 3. 创建完整配置对象
      const fullConfig = {
        // 品种信息
        symbol: currentSymbolVar,
        // 周期信息（单独保存）
        period: currentPeriodVar,
        // 指标配置 - 使用过滤后的 indicators 数组
        indicators: indicators,
        // 图表样式
        styles: styles
      };

      // 4. 保存到 localStorage
      // 使用 JSON.stringify 将对象转换为字符串
      // 这是必要的，因为 localStorage 只能存储字符串
      localStorage.setItem('fullChartConfig', JSON.stringify(fullConfig));
      console.log('[KLineChartPanel] 图表完整配置保存成功:', fullConfig);
    } catch (error) {
      console.error('[KLineChartPanel] 保存图表配置失败:', error);
    }
  }, [currentSymbolVar, currentPeriodVar]);

  // 保存当前画线数据
  // 目前改为不使用 状态依赖，直接用变量，什么状态状态 pp
  const saveCurrentDrawingLines = useCallback(() => {
    if (!chartRef.current || !currentSymbolVar || !currentPeriodVar) return;

    console.log('[KLineChartPanel]Var 开始保存画线数据，currentSymbolVar=', currentSymbolVar, ' currentPeriodVar=', currentPeriodVar);

    // 获取所有 overlays
    const allOverlays = chartRef.current.getOverlays();
    console.log('[KLineChartPanel] 获取到所有 overlays:', allOverlays, ' myOverlayIds=', myOverlayIds.current);

    // 过滤出需要保存的 overlays
    const overlaysToSave = allOverlays.filter((overlay: any) => myOverlayIds.current.includes(overlay.id));

    // 遍历 overlaysToSave，每一个数组元素中取值为null的字段全部删掉
    // 删除每个 overlay 中的 null 值字段
    const cleanedOverlays = overlaysToSave.map((overlay: any) => {
      return Object.fromEntries(
        Object.entries(overlay).filter(
          ([key, value]) =>
            ['name', 'paneId', 'points', 'visible'].includes(key) &&
            value !== null
        )
      );
    });

    console.log('[KLineChartPanel] 过滤后需要保存的 overlays:', cleanedOverlays, ' symbol=', currentSymbolVar, ' interval=', currentPeriodVar);

    if (!currentSymbolVar || !currentPeriodVar) {
      console.log('[KLineChartPanel] 当前品种或周期为空，不保存画线数据');
      return;
    }

    // 发送保存事件
    EventBus.emit(ChartEvents.Types.SAVE_DRAWING_LINES, {
      symbol: currentSymbolVar,
      interval: currentPeriodVar,
      overlays: cleanedOverlays
    });

  }, []); // 调用依赖于这两个数组的变化

  // 数据更新时重新调整大小
  useEffect(() => {

    if (chartRef.current && chartContainerRef.current) {
      const width = chartContainerRef.current.clientWidth;
      const height = chartContainerRef.current.clientHeight;

      // 延迟执行以确保容器已经完全渲染
      setTimeout(() => {
        chartRef.current.resize(width, height);
        adjustChartLayout();
      }, 200);
    }

    // 当所选择的品种和周期发生变化的时候，保存当前画线数据
    console.log('[KLineChartPanel]Var 检测到 selectedSymbol selectedPeriod 变化，保存当前画线数据');
    saveCurrentDrawingLines();

    // 同时保存图表配置
    console.log('[KLineChartPanel] 检测到 selectedSymbol selectedPeriod 变化，保存图表配置');
    saveChartConfig();

  }, [selectedSymbol, selectedPeriod, saveChartConfig]);  // 再选择变化的时候（实际品种还没有发生变化）

  useEffect(() => {
    // 当前品种变化的时候，读入品种的画线数据
    if (currentSymbol && currentPeriod) {

      // 删除所有画线
      const overlays = chartRef.current.getOverlays();
      overlays.forEach((overlay: any) => {
        chartRef.current?.removeOverlay(overlay.id);
      });

      console.log('[KLineChartPanel]Var currentPeriod currentSymbol 变化， 清除 myOverlayIds');
      myOverlayIds.current = [];

      // 读入品种的画线数据
      EventBus.emit(ChartEvents.Types.GET_DRAWING_LINES, {
        symbol: currentSymbol
      });
    }

    // 更新本地变量
    currentSymbolVar = { code: currentSymbol.code, name: currentSymbol.name, market: currentSymbol.market, exchange: currentSymbol.exchange };
    currentPeriodVar = currentPeriod;

    console.log('[KLineChartPanel]Var 更新本地变量为：', currentSymbolVar, currentPeriodVar);

  }, [currentPeriod, currentSymbol]);


  // 监听组件可见性变化
  useEffect(() => {
    // 监听组件可见性变化
    const observer = new IntersectionObserver((entries) => {
      // 当组件进入视口（变为可见）
      if (entries[0].isIntersecting && chartRef.current) {
        console.log('[KLineChartPanel] 检测到组件变为可见，触发重绘');
        // 强制调整图表大小以重新渲染
        chartRef.current.resize();
      }
    }, { threshold: 0.1 }); // 当 10% 的组件可见时触发

    // 如果容器已挂载，开始观察
    if (chartContainerRef.current) {
      observer.observe(chartContainerRef.current);
    }

    // 监听画线数据就绪事件
    const drawingLinesReadySubscription = EventBus.on(
      ChartEvents.Types.APPLY_DRAWING_LINES,
      (payload) => {
        console.log('[KLineChartPanel] 收到画线数据就绪事件:', JSON.stringify(payload), ' currentPeriod=', currentPeriod);
        handleDrawingLinesReady(payload.drawingLines, currentPeriod);
      }
    );

    // 组件卸载时停止观察和取消订阅
    return () => {
      observer.disconnect();
      drawingLinesReadySubscription.unsubscribe(); // 取消注释，确保清理订阅
    };
  }, [ currentPeriod ]); // 移除 currentPeriod 依赖，只在组件挂载时订阅一次

  // 处理画线数据就绪
  const handleDrawingLinesReady = useCallback((drawingLines: DrawingLineRecord[], theInterval: KLineInterval) => {
    console.log('[KLineChartPanel] 收到画线数据:', JSON.stringify(drawingLines));

    if (!chartRef.current) {
      console.log('[KLineChartPanel] 图表实例不存在，无法处理画线数据');
      return;
    }

    // 清除现有的 overlays
    myOverlayIds.current.forEach(id => {
      chartRef.current?.removeOverlay(id);
    });

    console.log('[KLineChartPanel]Var DrawingLinesReady, 清除 myOverlayIds');

    // 清除 myOverlayIds
    myOverlayIds.current = [];

    // 比较周期大小的函数
    const comparePeriods = (period1: KLineInterval, period2: KLineInterval): number => {
      const periodValues: { [key in KLineInterval]: number } = {
        [KLineInterval.MIN1]: 1,
        [KLineInterval.MIN5]: 5,
        [KLineInterval.MIN15]: 15,
        [KLineInterval.MIN30]: 30,
        [KLineInterval.HOUR1]: 60,
        [KLineInterval.HOUR4]: 240,
        [KLineInterval.DAY1]: 1440,
        [KLineInterval.WEEK1]: 10080
      };
      return periodValues[period1] - periodValues[period2];
    };

    // 处理每一条画线数据
    drawingLines.forEach(line => {

      const periodComparison = comparePeriods(line.interval as KLineInterval, theInterval);

      // 如果周期小于当前周期，跳过
      if (periodComparison < 0) {
        console.log(`[KLineChartPanel] 跳过画线，周期 ${line.interval} 小于当前周期 ${theInterval}`);
        return;
      }
      else {
        console.log(`[KLineChartPanel] 处理画线，周期 ${line.interval} 大于等于当前周期 ${theInterval}`);
      }

      // 处理每个 overlay
      line.overlays.forEach(overlay => {
        try {
          console.log('[KLineChartPanel] 从数据中创建画线 overlay:', JSON.stringify(overlay));

          const id = chartRef.current!.createOverlay({
            name: overlay.name,
            paneId: 'candle_pane',
            points: overlay.points
          });

          // 只有当周期相同时，才添加到 myOverlayIds
          if (periodComparison === 0) {
            console.log(`[KLineChartPanel] 添加画线 ID: ${id} 到 myOverlayIds`);
            myOverlayIds.current.push(id);
          }
        } catch (error) {
          console.error('[KLineChartPanel] 创建画线失败:', error);
        }
      });
    });

    console.log('[KLineChartPanel] 画线处理完成，当前维护的画线数量:', myOverlayIds.current.length);

    // 返回 false，阻止事件继续传播
    return false;

  }, []);

  const addIndicator = (indicatorName: IndicatorType) => {

    const indicatorDef = INDICATORS[indicatorName];
    const indicator = new IndicatorWrapper(
      indicatorDef,
      kline,
      indicatorDef.defaultParams
    );

    // 要添加到 indicatorInstancesAtom
    setIndicatorInstances([...getIndicatorInstances(), indicator]);

    console.log('[指标] 添加指标IndicatorWrapper实例:', getIndicatorInstances());

    let indicatorId = null;

    if (indicator) {

      const customIndicatorName = 'Custom_' + indicatorName;

      try {
        if (indicator.getIsMain()) {
          console.log(`[指标] addIndicator - 调用 createIndicator, 参数: ${customIndicatorName}, true, { id: 'candle_pane' }`);
          indicatorId = chartRef.current?.createIndicator(customIndicatorName, true, { id: 'candle_pane' });
        } else {
          // 为副图创建一个新的面板ID
          const paneId = `pane_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
          panes.push(paneId);

          console.log(`[指标] addIndicator - 调用 createIndicator, 参数: ${customIndicatorName}, false, { id: ${paneId} }`);
          indicatorId = chartRef.current?.createIndicator(customIndicatorName, false, { id: paneId });

          if (indicatorId) {
            // 为副图添加按钮
            addButtonsToSubPane(paneId);
          }
        }

        // 创建从 定制指标Id 到 IndicatorWrapper实例Id 的映射
        if (indicatorId) {

          console.log(`[指标] addIndicator - 创建映射: klinecharts ID ${indicatorId} -> Wrapper ID ${indicator.id} (Type: ${indicator.type})`);

          indicatorIdMap.set(indicatorId, { type: indicator.type, wrapperId: indicator.id });

        } else {
          console.warn(`[指标] addIndicator - 未能为 ${customIndicatorName} 创建 klinecharts 指标实例`);
        }
      } catch (error) {
        console.error(`[指标] addIndicator - 添加指标 ${customIndicatorName} 失败:`, error);
      }
    }

    console.log('[指标] addIndicator - 完成添加指标');
  };

  // 4. 组合菜单项
  const addIndicatorMenuItems: MenuProps['items'] = [
    {
      key: 'custom_indicators_group',
      type: 'group',
      label: '自定义指标',
      children: Object.values(IndicatorType).map(type => ({
        key: `custom_${type}`,
        label: type,
        onClick: () => {
          if (!chartRef.current) return;
          console.log(`[指标] 添加自定义指标菜单点击: ${type}`);
          addIndicator(type);
        }
      }))
    }
  ];

  useEffect(() => {
    // 监听参数更新事件
    const paramsUpdatedSubscription = EventBus.on(
      IndicatorEvents.Types.INDICATOR_PARAMS_EDITED,
      // 为 payload 添加 as any 类型断言
      (payload: any) => {
        console.log('[指标] 收到参数更新事件:', payload);

        if (!chartRef.current) return;

        try {
          // 获取 IndicatorWrapper 实例
          const wrapper = getIndicatorInstances().find(instance => instance.id === payload.wrapperId);
          if (!wrapper) {
            console.warn(`[指标] 未找到指标实例 ${payload.wrapperId}`);
            return;
          }

          // 更新参数
          wrapper.updateParams(payload.params);

          // 更新图表显示
          chartRef.current.overrideIndicator(payload.indicatorId);

          console.log(`[指标] 已更新指标 ${payload.wrapperId} 的参数`);
        } catch (error) {
          console.error(`[指标] 更新指标参数失败:`, error);
        }
      }
    );

    // 监听删除指标事件
    const removeIndicatorSubscription = EventBus.on(
      IndicatorEvents.Types.REMOVE_INDICATOR,
      // 为 payload 添加 as any 类型断言
      (payload: any) => {
        console.log('[指标] 收到删除指标事件:', payload);

        if (!chartRef.current) return;

        try {
          // 删除指标
          chartRef.current.removeIndicator({ id: payload.indicatorId });

          // 从面板ID列表中移除
          panes = panes.filter(id => id !== payload.indicatorId);

          // 删除对应的 indicatorMap 映射表项目
          const wrapperInfo = indicatorIdMap.get(payload.indicatorId);
          if (wrapperInfo) {
            // 删除对应的 indicatorInstancesAtom 实例
            setIndicatorInstances(getIndicatorInstances().filter(instance => instance.id !== wrapperInfo.wrapperId));
          }
          indicatorIdMap.delete(payload.indicatorId);

          console.log(`[指标] 已删除指标 ${payload.indicatorId}`);
        } catch (error) {
          console.error(`[指标] 删除指标失败:`, error);
        }
      }
    );

    return () => {
      paramsUpdatedSubscription.unsubscribe();
      removeIndicatorSubscription.unsubscribe();
    };
  }, []);

  // 监听参数配置对话框显示事件
  useEffect(() => {
    const showParamsModalSubscription = EventBus.on(
      IndicatorEvents.Types.SHOW_PARAMS_MODAL,
      // 为 payload 添加 as any 类型断言
      (payload: any) => {
        console.log('[指标参数] 显示参数配置对话框:', payload);
        setCurrentPaneId(payload.paneId);
        setCurrentIndicators(payload.indicators);
        setParamsModalVisible(true);
      }
    );

    return () => {
      showParamsModalSubscription.unsubscribe();
    };
  }, []);

  // 监听历史回放相关事件
  useEffect(() => {
    // 进入回放模式，自动选择起点
    const enterPlayback = EventBus.on(ChartEvents.Types.ENTER_PLAYBACK_MODE, () => {
      if (!chartRef.current) return;
      const data = chartRef.current.getDataList ? chartRef.current.getDataList() : [];
      if (!data || data.length === 0) return;
      fullKlineData.current = [...data];
      // 取中间部分往前的随机起点
      const n = data.length;
      if (n < 10) return; // 数据太少不回放
      const minIdx = Math.floor(n / 4);
      const maxIdx = Math.floor(n / 2);
      const startIndex = minIdx + Math.floor(Math.random() * (maxIdx - minIdx));
      setIsPlaybackMode(true);
      setIsPlaying(false);
      setPlaybackIndex(startIndex);
      // 从sessionStorage读取步长设置，如果没有则使用默认值10
      const savedStep = sessionStorage.getItem('playbackStepInterval');
      const toolbarStep = savedStep ? parseInt(savedStep) : 10;
      setPlaybackStep(toolbarStep);
      playbackStepRef.current = toolbarStep;
      // 只显示到起点K线
      chartRef.current.applyNewData(fullKlineData.current.slice(0, startIndex + 1));
      // 显示悬浮窗
      setFloatingWindowVisible(true);
    });
    // 退出回放模式
    const exitPlayback = EventBus.on(ChartEvents.Types.EXIT_PLAYBACK_MODE, () => {
      if (!chartRef.current) return;
      setIsPlaybackMode(false);
      setIsPlaying(false);
      setPlaybackIndex(0);
      // 恢复完整数据
      if (fullKlineData.current.length > 0) {
        chartRef.current.applyNewData(fullKlineData.current);
      }
      // 清理定时器
      if (playbackTimer.current) {
        clearInterval(playbackTimer.current);
        playbackTimer.current = null;
      }
      // 隐藏悬浮窗
      setFloatingWindowVisible(false);
    });
    // 播放/暂停
    const togglePlayPause = EventBus.on(ChartEvents.Types.TOGGLE_PLAY_PAUSE, ({ playing }) => {
      console.log('[KLineChartPanel] 收到播放/暂停事件:', playing);
      setIsPlaying(playing);
      if (playing) {
        playOneKline(playbackIndexRef.current);
      } else {
        // 暂停
        if (playbackTimer.current) {
          clearInterval(playbackTimer.current);
          playbackTimer.current = null;
        }
      }
    });
    // 步长变更
    const setStep = EventBus.on(ChartEvents.Types.SET_PLAYBACK_STEP, ({ step }) => {

      console.log('K线播放定时器，setStep, step=', step);
      setPlaybackStep(step);

      // 若正在播放，重启定时器
      if (isPlayingRef.current) {
        if (playbackTimer.current) clearInterval(playbackTimer.current);
        playbackTimer.current = setInterval(() => {
          setPlaybackIndex(idx => {
            const next = idx + 1;
            if (fullKlineData.current && next < fullKlineData.current.length) {
              chartRef.current.applyNewData(fullKlineData.current.slice(0, next + 1));
              return next;
            } else {
              setIsPlaying(false);
              if (playbackTimer.current) {
                clearInterval(playbackTimer.current);
                playbackTimer.current = null;
              }
              return idx;
            }
          });
        }, step * 1000);
      }
    });
    return () => {
      console.log('[KLineChartPanel] 卸载回放相关事件监听......');
      enterPlayback.unsubscribe();
      exitPlayback.unsubscribe();
      togglePlayPause.unsubscribe();
      setStep.unsubscribe();
      if (playbackTimer.current) {
        clearInterval(playbackTimer.current);
        playbackTimer.current = null;
      }
    };
  }, []);

  // 选择起点模式下，鼠标移动吸附竖线
  useEffect(() => {
    if (!isSelectingPlaybackStart || !chartRef.current) return;
    const handleMouseMove = (e: MouseEvent) => {
      const rect = chartRef.current._container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      // 获取最近K线索引
      const dataList = fullKlineData.current;
      if (!dataList || dataList.length === 0) return;
      // klinecharts有API: convertFromPixel({x}) 得到dataIndex
      const point = chartRef.current.convertFromPixel([{ x }]);
      let idx = 0;
      if (Array.isArray(point) && point[0] && typeof point[0].dataIndex === 'number') {
        idx = Math.max(0, Math.min(dataList.length - 1, point[0].dataIndex));
      }
      setSelectedPlaybackStartIndex(idx);
      // 这里可以调用chartRef.current.createOverlay渲染临时竖线（如name:'verticalLine'，每次先remove再create）
      if (chartRef.current) {
        if (chartRef.current._playbackSelectLineId) {
          chartRef.current.removeOverlay(chartRef.current._playbackSelectLineId);
        }
        const overlayId = chartRef.current.createOverlay({
          name: 'verticalStraightLine',
          points: [{ dataIndex: idx }],
          styles: { color: '#ff9900', size: 2 }
        });
        chartRef.current._playbackSelectLineId = overlayId;
      }
    };
    const handleMouseDown = (e: MouseEvent) => {
      if (e.button === 0 && selectedPlaybackStartIndex !== null) {
        // 左键确定起点，进入回放
        setIsSelectingPlaybackStart(false);
        setIsPlaybackMode(true);
        setIsPlaying(false);
        setPlaybackIndex(selectedPlaybackStartIndex);
        // 切片显示到选中K线
        if (chartRef.current && fullKlineData.current.length > 0) {
          chartRef.current.applyNewData(fullKlineData.current.slice(0, selectedPlaybackStartIndex + 1));
        }
        // 移除临时竖线
        if (chartRef.current._playbackSelectLineId) {
          chartRef.current.removeOverlay(chartRef.current._playbackSelectLineId);
          chartRef.current._playbackSelectLineId = null;
        }
      } else if (e.button === 2) {
        // 右键取消
        setIsSelectingPlaybackStart(false);
        setSelectedPlaybackStartIndex(null);
        setIsPlaybackMode(false);
        setIsPlaying(false);
        // 移除临时竖线
        if (chartRef.current._playbackSelectLineId) {
          chartRef.current.removeOverlay(chartRef.current._playbackSelectLineId);
          chartRef.current._playbackSelectLineId = null;
        }
      }
    };
    const container = chartRef.current._container;
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mousedown', handleMouseDown);
    // 禁止右键菜单
    const preventContextMenu = (e: MouseEvent) => { if (isSelectingPlaybackStart) e.preventDefault(); };
    container.addEventListener('contextmenu', preventContextMenu);
    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mousedown', handleMouseDown);
      container.removeEventListener('contextmenu', preventContextMenu);
      // 移除临时竖线
      if (chartRef.current && chartRef.current._playbackSelectLineId) {
        chartRef.current.removeOverlay(chartRef.current._playbackSelectLineId);
        chartRef.current._playbackSelectLineId = null;
      }
    };
  }, [isSelectingPlaybackStart, selectedPlaybackStartIndex]);

  // 辅助函数：分段插值生成当前K线状态，真实反映K线内部运动
  function interpolateKline(kline: any, progress: number) {
    // progress: 0~1
    if (!kline) return kline;
    const { open, high, low, close, volume } = kline;
    const isBull = close >= open;
    // 采样点数量，越大越平滑
    const steps = 30;
    const curStep = Math.max(1, Math.round(progress * steps));
    // 生成从开盘到当前帧的所有已走过价格
    const prices: number[] = [];
    for (let i = 0; i < curStep; i++) {
      const p = i / steps;
      let price;
      if (p <= 0) {
        price = open;
      } else if (p < 1 / 3) {
        // 第一段：开盘->极值
        const t = p * 3;
        price = isBull ? open + (low - open) * t : open + (high - open) * t;
      } else if (p < 2 / 3) {
        // 第二段：极值->另一极值
        const t = (p - 1 / 3) * 3 / 2;
        price = isBull ? low + (high - low) * t : high + (low - high) * t;
      } else if (p < 1) {
        // 第三段：极值->收盘
        const t = (p - 2 / 3) * 3;
        price = isBull ? high + (close - high) * t : low + (close - low) * t;
      } else {
        price = close;
      }
      prices.push(price);
    }
    // 当前帧的价格
    let curPrice;
    if (progress <= 0) {
      curPrice = open;
    } else if (progress < 1 / 3) {
      const t = progress * 3;
      curPrice = isBull ? open + (low - open) * t : open + (high - open) * t;
    } else if (progress < 2 / 3) {
      const t = (progress - 1 / 3) * 3 / 2;
      curPrice = isBull ? low + (high - low) * t : high + (low - high) * t;
    } else if (progress < 1) {
      const t = (progress - 2 / 3) * 3;
      curPrice = isBull ? high + (close - high) * t : low + (close - low) * t;
    } else {
      curPrice = close;
    }
    prices.push(curPrice);
    // high/low为已走过所有价格的极值
    const curHigh = Math.max(open, ...prices);
    const curLow = Math.min(open, ...prices);
    return {
      ...kline,
      open,
      high: curHigh,
      low: curLow,
      close: curPrice,
      volume: volume * progress // 体积线性插值
    };
  }

  // 播放一根K线的动态过程，严格按"起点的下一根K线"开始
  function playOneKline(idx: number, subSteps = 10) {
    playbackIndexRef.current = idx;
    console.log('K线播放定时器，playOneKline, idx=', idx);
    if (!fullKlineData.current || idx + 1 >= fullKlineData.current.length) {
      setIsPlaying(false);
      return;
    }
    const prev = fullKlineData.current[idx];
    const next = fullKlineData.current[idx + 1];
    subStepRef.current = subStepRef.current || 0; // 若恢复播放，保持原subStep
    // interval用最新的playbackStepRef.current
    const interval = playbackStepRef.current * 1000 / subSteps;
    if (playbackTimer.current) clearInterval(playbackTimer.current);
    console.log('K线播放定时器，interval=', interval, ' subStep=', subStepRef.current);
    playbackTimer.current = setInterval(() => {
      subStepRef.current++;
      let kline;
      console.log('K线播放定时器内部，subSteps = ', subStepRef.current);
      if (subStepRef.current < subSteps) {
        kline = interpolateKline(next, subStepRef.current / subSteps);
      } else {
        kline = next;
      }
      console.log('K线播放定时器内部，...updateData...');
      chartRef.current.updateData(kline);
      if (subStepRef.current >= subSteps) {
        clearInterval(playbackTimer.current!);
        playbackTimer.current = null;
        subStepRef.current = 0; // 播放完一根后重置
        // 步进到下一根K线
        if (idx + 2 < fullKlineData.current.length) {
          setTimeout(() => playOneKline(idx + 1, subSteps), 0);
        } else {
          setIsPlaying(false);
        }
      }
    }, interval);
  }

  return (
    <Card
      bordered={false}
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}
      bodyStyle={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        padding: 0,
        overflow: 'hidden',
        height: '100%'
      }}
    >
      {/* Chart Container */}
      <div
        id="kline-chart-container"
        ref={chartContainerRef}
        style={{
          flex: 1,
          width: '100%',
          height: '100%',
          position: 'relative',
          touchAction: 'none', // 防止触摸时触发页面缩放
          userSelect: 'none', // 防止文本选择
          WebkitUserSelect: 'none', // Safari兼容
          MozUserSelect: 'none', // Firefox兼容
          msUserSelect: 'none', // IE兼容
        }}
      />

      {/* 显示当前选择状态的指示器 - 当是第二个点时显示 */}
      {!isFirstPoint && rangeStartRef.current && (
        <div
          style={{
            position: 'absolute',
            left: rangeStartRef.current.x,
            top: 0,
            width: 2,
            height: '100%',
            backgroundColor: 'rgba(255, 165, 0, 0.8)',
            pointerEvents: 'none',
            zIndex: 99
          }}
        >
          <div
            style={{
              position: 'absolute',
              top: 5,
              left: 5,
              padding: '2px 6px',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              borderRadius: '2px',
              fontSize: '12px',
              color: '#fff',
              whiteSpace: 'nowrap'
            }}
          >
            起点: {rangeStartRef.current.index}
          </div>
        </div>
      )}

      {/* 显示区域选择框和确认按钮 */}
      {selectedRange && (
        <div
          style={{
            position: 'absolute',
            left: selectedRange.fromX,
            top: 0,
            width: selectedRange.toX - selectedRange.fromX,
            height: '100%',
            backgroundColor: 'rgba(24, 144, 255, 0.1)',
            border: '1px solid rgba(24, 144, 255, 0.3)',
            pointerEvents: 'none',
            zIndex: 99
          }}
        >
          {/* 右上角确认按钮 */}
          <div
            style={{
              position: 'absolute',
              top: 5,
              right: 5,
              width: 20,
              height: 20,
              backgroundColor: 'rgba(128, 128, 128, 0.3)',
              borderRadius: '20%',
              cursor: 'pointer',
              zIndex: 100,
              pointerEvents: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onClick={handleRangeConfirm}
          >
            <span style={{
              fontSize: 14,
              display: 'inline-block',
              marginTop: '-2px',
              color: 'var(--ant-color-text)',
              opacity: 0.85
            }}>✓</span>
          </div>
          {/* 显示索引范围的标签 */}
          <div
            style={{
              position: 'absolute',
              top: 5,
              left: 5,
              padding: '2px 6px',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              borderRadius: '2px',
              fontSize: '12px',
              color: '#fff',
              pointerEvents: 'none'
            }}
          >
            {selectedRange.fromIndex} - {selectedRange.toIndex}
          </div>
        </div>
      )}

      {/* --- Add Indicator Button (Bottom Left) --- */}
      <Tooltip title="添加指标">
        <Dropdown menu={{ items: addIndicatorMenuItems }} trigger={['click']} placement="topLeft">
          <Button
            shape="circle"
            icon={<LineChartOutlined />}
            className="add-indicator-btn"
            style={{
              position: 'absolute',
              left: 10,
              bottom: 30,
              zIndex: 10,
              width: 32,
              height: 32,
              padding: 0,
            }}
          />
        </Dropdown>
      </Tooltip>

      {/* --- 实时数据状态指示器 --- */}
      {realtimeStatus !== 'hidden' && (
        currentSymbol?.market !== MarketType.CRYPTO && <Tooltip title={realtimeStatus === 'loading' ? '正在获取实时数据' : '实时数据连接失败 - 点击重试'}>
          <Button
            shape="circle"
            icon={realtimeStatus === 'loading' ? <SyncOutlined spin /> : <ExclamationCircleOutlined />}
            className="realtime-status-btn"
            style={{
              position: 'absolute',
              // 如果滚动按钮可见，放在其左侧；否则放在滚动按钮的位置
              right: isScrollButtonVisible ? (54 + 32 + 6) : 54, // 54(按钮right) + 32(按钮width) + 6(gap)
              bottom: 30,
              zIndex: 10,
              width: 32,
              height: 32,
              padding: 0,
              backgroundColor: realtimeStatus === 'loading' ? 'rgba(24, 144, 255, 0.1)' : 'rgba(250, 140, 22, 0.1)',
              color: realtimeStatus === 'loading' ? '#1890ff' : '#fa8c16',
              border: 'none',
            }}
            onClick={() => {
              if (realtimeStatus === 'error') {
                console.log('[KLineChartPanel] 点击重试实时数据连接...');
                EventBus.emit(RealtimeEvents.Types.REALTIME_RECONNECT, undefined);
                setRealtimeStatus('loading'); // 立即切换到加载状态
              }
            }}
          />
        </Tooltip>
      )}

      {/* 滚动到最新的浮动按钮 - 使用绝对定位的 Button 替代 FloatButton */}
      {isScrollButtonVisible && (
        <Tooltip title="滚动到最新">
          <Button
            shape="circle" // 使用圆形按钮
            icon={<MdKeyboardDoubleArrowRight />} // 保持图标
            onClick={scrollToLatest} // 保持点击事件
            className="scroll-to-latest-btn" // 保持 CSS 类名，用于样式（透明度、hover、居中）
            style={{
              position: 'absolute', // 绝对定位
              right: 54, // 保持右边距
              bottom: 30, // 保持底边距
              zIndex: 10, // 确保在图表上方
              width: 32, // 保持尺寸
              height: 32, // 保持尺寸
              padding: 0, // 移除内边距，确保图标能更好地居中
              // 移除之前的 border, background, shadow, display, alignItems, justifyContent
              // 这些由 CSS 类或 Button 默认样式处理
            }}
          />
        </Tooltip>
      )}

      {/* 添加参数配置对话框 */}
      <IndicatorParamsModal
        visible={paramsModalVisible}
        onClose={() => setParamsModalVisible(false)}
        paneId={currentPaneId}
        indicators={currentIndicators}
      />

      {/* 回放悬浮窗 */}
      <PlaybackFloatingWindow
        visible={floatingWindowVisible}
        isPlaying={isPlaying}
        stepInterval={playbackStep}
        onClose={() => {
          setFloatingWindowVisible(false);
          EventBus.emit(ChartEvents.Types.EXIT_PLAYBACK_MODE, {});
        }}
      />

      {/* 选股进度浮动窗口 - 顶部中间窄条 */}
      {(() => {
        console.log('[KLineChartPanel] [DEBUG] 渲染选股进度浮窗，状态:', {
          visible: stockSelectionProgress.visible,
          taskId: stockSelectionProgress.taskId,
          current: stockSelectionProgress.current,
          total: stockSelectionProgress.total,
          selectedCount: stockSelectionProgress.selectedCount,
          stage: stockSelectionProgress.stage
        });
        
        return stockSelectionProgress.visible && (
          <div className="stock-selection-progress-top">
            <div className="progress-content">
              {/* 左侧区域：简要信息和进度条 */}
              <div className="progress-info-section">
                <div className="progress-summary">
                  <span className="progress-percentage">
                    {stockSelectionProgress.total ? Math.round((stockSelectionProgress.current || 0) / stockSelectionProgress.total * 100) : 0}%
                  </span>
                  <span className="progress-selected">
                    已选中: {stockSelectionProgress.selectedCount || 0} 只
                  </span>
                </div>
                <div className="progress-bar-container">
                  <Progress 
                    percent={stockSelectionProgress.total ? Math.round((stockSelectionProgress.current || 0) / stockSelectionProgress.total * 100) : 0} 
                    status="active"
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                    showInfo={false}
                    size="small"
                  />
                </div>
              </div>
              
              {/* 右侧中止按钮 */}
              <div className="stop-button-section">
                <Button 
                  type="primary" 
                  danger
                  size="small"
                  onClick={() => {
                    console.log('[KLineChartPanel] 点击中止选股按钮');
                    // 发布中止选股事件
                    EventBus.emit(SymbolSelectEvents.Types.STOP_STOCK_SELECTION, {
                      taskId: stockSelectionProgress.taskId || 'unknown'
                    });
                  }}
                >
                  中止选股
                </Button>
              </div>
            </div>
          </div>
        );
      })()}
    </Card>
  );
};

export default KLineChartPanel;