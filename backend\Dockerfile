    # Use an official Node runtime
    FROM node:18-alpine

    # Create app directory
    WORKDIR /usr/src/app

    # Install app dependencies
    # A wildcard is used to ensure both package.json AND package-lock.json are copied
    # where available (npm@5+)
    COPY package*.json ./

    RUN npm install
    # If you are building your code for production
    # RUN npm ci --only=production

    # Bundle app source
    COPY . .

    # Default command (overridden by docker-compose)
    # Specify the port your Node app listens on
    # EXPOSE 3000
    CMD [ "npm", "run", "dev" ] # Executes the dev script defined in package.json