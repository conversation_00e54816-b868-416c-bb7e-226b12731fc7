import React from 'react';
import { Modal, Row, Col } from 'antd';
import { useAtom } from 'jotai';
import SignalList from './GeneIndicatorList';
import GeneConfigPanel from './GeneConfigPanel';
import { geneDialogVisibleAtom } from '../../models/geneState';
import './index.less';

/**
 * 基因配置弹出窗口组件
 * 包含两个区域：
 * 1. 左侧 - 指标列表
 * 2. 右侧 - 配置面板
 */
const GeneDialog: React.FC = () => {
    const [visible, setVisible] = useAtom(geneDialogVisibleAtom);

    const handleCancel = () => {
        setVisible(false);
    };

    return (
        <Modal
            title="信号观察"
            open={visible}
            onCancel={handleCancel}
            width={800}
            footer={null}
            styles={{
                body: { padding: '12px', height: '600px' }
            }}
        >
            <Row gutter={[16, 16]} style={{ height: '100%' }}>
                <Col flex="280px">
                    <SignalList />
                </Col>
                <Col flex="auto">
                    <GeneConfigPanel />
                </Col>
            </Row>
        </Modal>
    );
};

export default GeneDialog; 