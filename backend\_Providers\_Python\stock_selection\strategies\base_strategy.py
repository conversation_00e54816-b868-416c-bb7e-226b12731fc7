from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseStrategy(ABC):
    """选股策略基类"""
    
    @abstractmethod
    def select_stock(self, klines: List[Dict[str, Any]], params: Dict[str, Any]) -> bool:
        """
        选股策略函数
        
        Args:
            klines: K线数据列表，格式为 [{'time': timestamp, 'open': float, 'high': float, 'low': float, 'close': float, 'volume': float, 'amount': float, 'change': float, 'change_pct': float}, ...]
            params: 策略参数字典
            
        Returns:
            bool: True表示选中，False表示不选中
        """
        pass
    
    @abstractmethod
    def get_required_klines(self) -> int:
        """
        获取策略需要的K线数量
        
        Returns:
            int: 需要的K线数量
        """
        pass 