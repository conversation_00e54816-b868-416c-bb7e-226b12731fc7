import { IndicatorSeries, registerIndicator } from 'klinecharts';
import { INDICATORS } from '@/_Modules/Indicators/IndicatorWrapper';
import { ExchangeType, IndicatorType, KLine, KLineInterval, MarketType } from '@/shared_types/market';
import { IndicatorLineType } from '@/shared_types/indicator';
//import { jotaiStore, indicatorInstancesAtom } from '@/store/state';
import { getIndicatorInstances } from '@/store/global';
import { IndicatorWrapper } from '@/_Modules/Indicators/IndicatorWrapper';
import { Indicator as KLineChartIndicator } from 'klinecharts';

// 存储klinechart指标ID与IndicatorWrapper实例ID的映射
// 这个映射由调用者维护
export const indicatorIdMap = new Map<string, { type: IndicatorType, wrapperId: string }>();

/**
 * 动态创建并注册指标的通用函数
 */
export function registerDynamicIndicators() {
  // 为每种指标类型注册一个自定义指标
  Object.entries(INDICATORS).forEach(([type, indicatorDef]) => {
    // 将IndicatorLineType转换为klinecharts期望的type
    const convertLineType = (lineType: IndicatorLineType): string => {
      switch(lineType) {
        case IndicatorLineType.Line: return 'line';
        case IndicatorLineType.Histogram: return 'bar';
        default: return 'line';
      }
    };

    // 动态生成figures配置
    const figures = indicatorDef.lines.map((line: any) => {
      const base = {
        key: line.name,
        title: `${line.name}: `,
        type: convertLineType(line.type)
      };

      // 为柱状图添加特殊样式处理
      if (line.type === IndicatorLineType.Histogram) {
        return {
          ...base,
          baseValue: 0,
          styles: ({ data }: { data: any }) => {
            const { current } = data;
            const value = current?.[line.name] ?? 0;
            const color = value > 0 ? '#26A69A' : '#EF5350';
            return {
              style: value > 0 ? 'stroke' : 'fill',
              color,
              borderColor: color
            };
          }
        };
      }

      return base;
    });

    console.log(`[UniversalIndicator] 注册指标: Custom_${type} 配置:`, figures);

    // 注册自定义指标
    registerIndicator({
      name: `Custom_${type}`,
      shortName: type.toString(), // 使用枚举值作为短名称
      series: IndicatorSeries.Price, // 根据需要可以动态设置
      figures,

      // 通用计算函数
      calc: (dataList, indicator) => {

        console.log(`[UniversalIndicator] 计算指标1: ${indicator.id}`);

        // 检查是否已有对应的IndicatorWrapper实例
        const mapping = indicatorIdMap.get(indicator.id);
        let wrapperId = mapping?.wrapperId;
        let indicatorType = mapping?.type as IndicatorType || parseInt(type);

        // 如果没有对应实例，创建一个新实例
        if (!wrapperId) {
          console.log(`[UniversalIndicator] 没有找到对应的IndicatorWrapper实例，返回空数据`);
          return dataList.map(() => ({}));
        }
        
        console.log(`[UniversalIndicator] 查找IndicatorWrapper实例, wrapperId: ${wrapperId}`);
        // 查找IndicatorWrapper实例
        const instances = getIndicatorInstances();
        console.log('instances', instances);
        const instance = instances.find((inst: any) => inst && inst.id === wrapperId);

        if (!instance) {
          console.log(`[UniversalIndicator] 没有找到对应的IndicatorWrapper实例，返回空数据`);
          return dataList.map(() => ({}));
        }

        // 将dataList转换为KLine对象，注意timestamp -> time的转换
        const kline: KLine = {
          id: '',
          symbol: { code: '', market: MarketType.STOCK, name: '', exchange: ExchangeType.SHFE },
          period: KLineInterval.MIN1,
          data: dataList.map(item => ({
            time: item.timestamp, // 关键是这里，将timestamp映射为time
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close,
            volume: item.volume || 0
          }))
        };

        // 触发计算
        instance.updateHistoricalData(kline);
        
        console.log(`[UniversalIndicator] 计算指标2: ${instance.getName()} 结果:`, instance.getValues());

        // 获取 IndicatorWrapper 实例的计算结果并转换格式
        const values = instance.getValues();

        // 转换为 KLineChart 格式返回
        return dataList.map((_, index) => {
          const result: Record<string, number | undefined> = {};
          Object.keys(values).forEach(key => {
            result[key] = values[key][index];
          });
          return result;
        });
        
      },

    });
  });
} 