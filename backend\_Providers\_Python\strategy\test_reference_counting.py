#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
引用计数机制测试脚本
专门测试数据引擎管理器的引用计数功能
"""

import os
import sys
import json
import requests
import time

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 策略服务器地址
STRATEGY_SERVER_URL = "http://localhost:5002"

def test_reference_counting_api():
    """测试引用计数机制的API"""
    
    print("=== 引用计数机制测试 ===\n")
    
    # 测试数据
    project_a = "test_project_A"
    project_b = "test_project_B"
    project_c = "test_project_C"
    
    contracts_a = ["SSE.600036", "SZSE.000001", "SSE.510300"]
    contracts_b = ["SSE.600036", "SSE.600519", "SZSE.159934"]  # 与A有重叠
    contracts_c = ["SSE.600036", "SZSE.000001"]  # 与A完全重叠
    
    try:
        print("📊 初始状态检查...")
        check_status("初始状态")
        
        print("\n1️⃣ 项目A订阅合约...")
        subscribe_project(project_a, contracts_a)
        check_status("项目A订阅后")
        
        print("\n2️⃣ 项目B订阅合约（与A有重叠）...")
        subscribe_project(project_b, contracts_b)
        check_status("项目B订阅后")
        
        print("\n3️⃣ 项目C订阅合约（与A完全重叠）...")
        subscribe_project(project_c, contracts_c)
        check_status("项目C订阅后")
        
        print("\n4️⃣ 项目A取消订阅...")
        unsubscribe_project(project_a)
        check_status("项目A取消订阅后")
        
        print("\n5️⃣ 项目C取消订阅...")
        unsubscribe_project(project_c)
        check_status("项目C取消订阅后")
        
        print("\n6️⃣ 项目B取消订阅...")
        unsubscribe_project(project_b)
        check_status("项目B取消订阅后")
        
        print("\n🎯 引用计数测试完成！")
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")

def subscribe_project(project_id, contracts):
    """订阅项目合约"""
    print(f"  订阅项目 {project_id}: {contracts}")
    
    response = requests.post(
        f"{STRATEGY_SERVER_URL}/data_engine/subscribe",
        json={
            "project_id": project_id,
            "contract_codes": contracts
        },
        timeout=10
    )
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"  ✓ 订阅成功")
        else:
            print(f"  ✗ 订阅失败: {result.get('error')}")
    else:
        print(f"  ✗ 请求失败: HTTP {response.status_code}")

def unsubscribe_project(project_id):
    """取消订阅项目"""
    print(f"  取消订阅项目 {project_id}")
    
    response = requests.post(
        f"{STRATEGY_SERVER_URL}/data_engine/unsubscribe",
        json={"project_id": project_id},
        timeout=10
    )
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"  ✓ 取消订阅成功")
        else:
            print(f"  ✗ 取消订阅失败: {result.get('error')}")
    else:
        print(f"  ✗ 请求失败: HTTP {response.status_code}")

def check_status(stage_name):
    """检查当前状态"""
    print(f"  📋 {stage_name}:")
    
    response = requests.get(f"{STRATEGY_SERVER_URL}/data_engine/status", timeout=10)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            data = result.get('data', {})
            
            print(f"    项目数量: {data.get('total_projects')}")
            print(f"    活跃合约数: {data.get('total_contracts')}")
            print(f"    活跃合约: {data.get('subscribed_contracts')}")
            
            # 显示引用计数
            ref_counts = data.get('contract_ref_counts', {})
            if ref_counts:
                print(f"    引用计数: {ref_counts}")
            else:
                print(f"    引用计数: 无")
            
            # 显示项目订阅
            project_subs = data.get('project_subscriptions', {})
            if project_subs:
                print(f"    项目订阅:")
                for proj, contracts in project_subs.items():
                    print(f"      {proj}: {contracts}")
            else:
                print(f"    项目订阅: 无")
        else:
            print(f"    ✗ 状态获取失败: {result.get('error')}")
    else:
        print(f"    ✗ 状态请求失败: HTTP {response.status_code}")

def test_reference_counting_local():
    """测试本地引用计数机制"""
    
    print("=== 本地引用计数机制测试 ===\n")
    
    try:
        # 导入数据引擎管理器
        from strategy_server import DataEngineManager
        
        # 创建管理器实例
        manager = DataEngineManager()
        
        print("1️⃣ 测试项目A订阅...")
        contracts_a = ["SSE.600036", "SZSE.000001", "SSE.510300"]
        manager.register_project_subscription("project_A", contracts_a)
        print(f"  引用计数: {manager.contract_ref_count}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n2️⃣ 测试项目B订阅（有重叠）...")
        contracts_b = ["SSE.600036", "SSE.600519", "SZSE.159934"]
        manager.register_project_subscription("project_B", contracts_b)
        print(f"  引用计数: {manager.contract_ref_count}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n3️⃣ 测试项目C订阅（完全重叠）...")
        contracts_c = ["SSE.600036", "SZSE.000001"]
        manager.register_project_subscription("project_C", contracts_c)
        print(f"  引用计数: {manager.contract_ref_count}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n4️⃣ 测试项目A取消订阅...")
        manager.unregister_project_subscription("project_A")
        print(f"  引用计数: {manager.contract_ref_count}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n5️⃣ 测试项目C取消订阅...")
        manager.unregister_project_subscription("project_C")
        print(f"  引用计数: {manager.contract_ref_count}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n6️⃣ 测试项目B取消订阅...")
        manager.unregister_project_subscription("project_B")
        print(f"  引用计数: {manager.contract_ref_count}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n7️⃣ 测试更新配置文件...")
        success = manager.update_contracts_file()
        print(f"  配置文件更新结果: {success}")
        
        # 检查生成的配置文件
        print(f"  配置文件路径: {manager.contracts_file}")
        if os.path.exists(manager.contracts_file):
            with open(manager.contracts_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"  最终配置文件: {json.dumps(config, ensure_ascii=False, indent=2)}")
        else:
            print(f"  配置文件不存在")
        
        print("\n🎯 本地引用计数测试完成！")
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
    except Exception as e:
        print(f"✗ 本地测试过程中发生错误: {e}")

def test_edge_cases():
    """测试边界情况"""
    
    print("=== 边界情况测试 ===\n")
    
    try:
        from strategy_server import DataEngineManager
        manager = DataEngineManager()
        
        print("1️⃣ 测试重复订阅同一项目...")
        manager.register_project_subscription("project_X", ["SSE.600036", "SZSE.000001"])
        print(f"  第一次订阅后: {manager.contract_ref_count}")
        
        manager.register_project_subscription("project_X", ["SSE.600036", "SSE.600519"])
        print(f"  第二次订阅后: {manager.contract_ref_count}")
        
        print("\n2️⃣ 测试取消不存在的项目...")
        result = manager.unregister_project_subscription("non_existent_project")
        print(f"  取消不存在项目结果: {result}")
        
        print("\n3️⃣ 测试空合约列表...")
        manager.register_project_subscription("empty_project", [])
        print(f"  空合约列表后: {manager.contract_ref_count}")
        
        print("\n🎯 边界情况测试完成！")
        
    except Exception as e:
        print(f"✗ 边界情况测试发生错误: {e}")

if __name__ == "__main__":
    print("引用计数机制测试工具\n")
    print("选择测试模式:")
    print("1. API测试 (需要 strategy_server.py 运行)")
    print("2. 本地测试 (直接测试管理器类)")
    print("3. 边界情况测试")
    print("4. 全部测试")
    
    choice = input("\n请选择 (1/2/3/4): ").strip()
    
    if choice == "1":
        test_reference_counting_api()
    elif choice == "2":
        test_reference_counting_local()
    elif choice == "3":
        test_edge_cases()
    elif choice == "4":
        test_reference_counting_local()
        print("\n" + "="*50 + "\n")
        test_edge_cases()
        print("\n" + "="*50 + "\n")
        test_reference_counting_api()
    else:
        print("无效选择")
