export const CHAT_CONSTANTS = {
    MAX_MESSAGE_LENGTH: 1000,
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    SUPPORTED_FILE_TYPES: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    RECONNECT_INTERVAL: 3000,
    MAX_RETRY_ATTEMPTS: 3
  };
  
  export const CHAT_ERRORS = {
    FILE_TOO_LARGE: '文件大小不能超过10MB',
    UNSUPPORTED_FILE_TYPE: '不支持的文件类型',
    MESSAGE_TOO_LONG: '消息长度不能超过1000字符',
    NETWORK_ERROR: '网络错误，请稍后重试',
    UPLOAD_FAILED: '文件上传失败'
  }; 