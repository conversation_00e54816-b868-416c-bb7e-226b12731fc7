"""BaoStock API Flask 服务

提供基于 BaoStock 的行情数据查询服务，包括：
- 股票列表查询
- K线数据查询
- 指数数据查询
- 因子计算服务
"""

import atexit
import json
import os
import time
from datetime import datetime

import backtrader as bt
import baostock as bs
import pandas as pd
from flask import Flask, jsonify, request

from strategy.gene.base import FactorMode, FactorType, SignalType
from strategy.gene.factors.ma_cross import MACrossFactor

# 读取配置文件
CURRENT_DIR = os.path.dirname(__file__)
CONFIG_PATH = os.path.join(CURRENT_DIR, 'config.json')

with open(CONFIG_PATH, 'r', encoding='utf-8') as f:
    config = json.load(f)

# 获取Python服务配置
PYTHON_CONFIG = config['python_service']
HOST = PYTHON_CONFIG['host']
PORT = PYTHON_CONFIG['port']

# K线周期映射
PERIOD_MAP = {
    "1m": "1",      # 1分钟
    "5m": "5",      # 5分钟
    "15m": "15",    # 15分钟
    "30m": "30",    # 30分钟
    "60m": "60",    # 1小时
    "1D": "d",      # 日线
    "1W": "w",      # 周线
    "1M": "m"       # 月线
}

# 因子类型到类的映射
FACTOR_MAP = {
    'MA_CROSS': MACrossFactor,
    # 后续添加更多因子
}

app = Flask(__name__)

# 全局变量跟踪 BaoStock 连接状态
bs_connected = False

def init_baostock():
    """初始化 BaoStock 连接。
    
    Returns:
        bool: 连接是否成功
    """
    global bs_connected
    try:
        # 如果已经连接，先登出
        if bs_connected:
            bs.logout()
            bs_connected = False

        # 登录
        lg = bs.login()
        
        # 检查登录状态
        if lg.error_code != '0':
            print(f'BaoStock登录失败 - error_code: {lg.error_code}, error_msg: {lg.error_msg}')
            return False
            
        print(f'BaoStock登录成功 - error_code: {lg.error_code}, error_msg: {lg.error_msg}')
        bs_connected = True
        return True
        
    except Exception as e:
        print(f"BaoStock初始化错误: {str(e)}")
        bs_connected = False
        return False

def ensure_baostock_connected():
    """确保 BaoStock 连接有效。
    
    Returns:
        bool: 连接是否有效
    """
    global bs_connected
    try:
        if not bs_connected:
            return init_baostock()
            
        # 尝试进行一个简单的查询来测试连接
        test_query = bs.query_all_stock(day=datetime.now().strftime('%Y-%m-%d'))
        if test_query.error_code == '0':
            return True
            
        # 如果查询失败，尝试重新登录
        print(
            f"BaoStock连接测试失败 - error_code: {test_query.error_code}, "
            f"error_msg: {test_query.error_msg}"
        )
        return init_baostock()
        
    except Exception as e:
        print(f"检查BaoStock连接状态时出错: {str(e)}")
        bs_connected = False
        return False

@atexit.register
def logout():
    """程序退出时确保登出。"""
    global bs_connected
    if bs_connected:
        try:
            bs.logout()
            bs_connected = False
            print("BaoStock已登出")
        except Exception as e:
            print(f"BaoStock登出时发生错误: {str(e)}")

def check_query_status(rs, operation="查询"):
    """检查查询状态。
    
    Args:
        rs: BaoStock查询结果
        operation: 操作描述
        
    Returns:
        bool: 查询是否成功
        
    Raises:
        Exception: 查询失败时抛出异常
    """
    if rs.error_code != '0':
        error_msg = f"{operation}失败 - error_code: {rs.error_code}, error_msg: {rs.error_msg}"
        print(error_msg)
        raise Exception(error_msg)
    return True

def convert_to_baostock_code(code: str, market: str = 'STOCK') -> str:
    """将股票代码转换为 BaoStock 格式。
    
    Args:
        code: 原始股票代码，可能带有 .SH/.SZ 后缀
        market: 市场类型，默认为 STOCK
    
    Returns:
        str: BaoStock 格式的代码 (sh.600000 或 sz.000001)
        
    Raises:
        ValueError: 不支持的股票代码格式
    """
    # 去除可能存在的后缀
    if '.' in code:
        code = code.split('.')[0]
    
    # 根据代码规则判断交易所
    code_prefix = code[:3]
    if code_prefix in ['600', '601', '603', '688', '689']:  # 上海交易所
        exchange = 'sh'
    elif code_prefix in ['000', '001', '002', '003', '300', '301']:  # 深圳交易所
        exchange = 'sz'
    else:
        raise ValueError(f"Unsupported stock code: {code}")
    
    return f"{exchange}.{code}"

def get_minute_kline(symbol, period, market='STOCK'):
    """获取分钟级别K线数据。
    
    Args:
        symbol: 股票代码
        period: K线周期
        market: 市场类型，默认为 STOCK
        
    Returns:
        list: K线数据列表
        
    Raises:
        Exception: 获取数据失败时抛出异常
    """
    try:
        # 确保 BaoStock 连接正常
        if not ensure_baostock_connected():
            raise Exception("Failed to connect to BaoStock service")

        # BaoStock的代码格式转换
        if market == 'INDEX':
            code = symbol.split('.')[0]
            exchange = symbol.split('.')[1].lower()
            bs_symbol = f"{exchange}.{code}"
        else:
            bs_symbol = convert_to_baostock_code(symbol, market)

        print(f"查询分钟K线数据 - 代码: {bs_symbol}, 周期: {period}")

        # 获取当前日期
        end_date = datetime.now().strftime('%Y-%m-%d')
        # 获取30天前的日期作为开始日期
        start_date = (datetime.now() - pd.Timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"查询时间范围: {start_date} 至 {end_date}")

        # 查询分钟K线数据
        fields = (
            "date,time,code,open,high,low,close,volume,amount"  # 添加 code 和 amount 字段
        )
        rs = bs.query_history_k_data_plus(
            bs_symbol,
            fields,
            start_date=start_date,
            end_date=end_date,
            frequency=period,
            adjustflag="3"  # 默认使用后复权
        )
        
        # 检查查询状态
        check_query_status(rs, f"查询分钟K线数据 - {bs_symbol}")
        
        # 获取数据
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
        
        # 转换为DataFrame
        df = pd.DataFrame(data_list, columns=rs.fields)
        print(f"获取到数据条数: {len(df)}")
        
        if df.empty:
            print("警告: 未获取到数据")
            return []
        
        klines = []
        for _, row in df.iterrows():
            # 转换日期时间字符串为时间戳
            time_str = f"{row['date']} {row['time']}"
            timestamp = int(datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S').timestamp())
            klines.append({
                'time': timestamp,
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close']),
                'volume': float(row['volume'])
            })
        return klines
    except Exception as e:
        print(f"Error in get_minute_kline: {str(e)}")
        raise e

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口。
    
    Returns:
        Response: 包含服务状态和时间戳的 JSON 响应
    """
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/stock/list', methods=['GET'])
def get_stock_list():
    """获取A股股票列表。
    
    Returns:
        Response: 包含股票列表的 JSON 响应
        
    Raises:
        Exception: 获取股票列表失败时抛出异常
    """
    try:
        print("开始获取股票列表")
        
        # 获取当前日期
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        # 使用 BaoStock 获取A股股票列表
        rs = bs.query_all_stock(day=current_date)
        check_query_status(rs, "获取股票列表")
            
        # 获取数据
        stock_list = []
        while (rs.error_code == '0') & rs.next():
            stock_list.append(rs.get_row_data())
            
        # 转换为DataFrame
        stock_df = pd.DataFrame(stock_list, columns=rs.fields)
        print(f"获取到股票数量: {len(stock_df)}")
        
        if stock_df.empty:
            print("警告: 未获取到股票列表")
            return jsonify({
                'success': True,
                'data': []
            })
            
        # 一次性获取所有股票的基本信息
        rs_basic = bs.query_stock_basic()
        check_query_status(rs_basic, "获取股票基本信息")
            
        # 获取基本信息数据
        basic_list = []
        while (rs_basic.error_code == '0') & rs_basic.next():
            basic_list.append(rs_basic.get_row_data())
            
        # 转换为DataFrame
        basic_df = pd.DataFrame(basic_list, columns=rs_basic.fields)
        print(f"获取到股票基本信息数量: {len(basic_df)}")
        
        # 创建代码到名称的映射
        code_to_name = dict(zip(basic_df['code'], basic_df['code_name']))
        
        # 转换为所需格式
        stocks = []
        for _, row in stock_df.iterrows():
            try:
                # BaoStock的代码格式为 sh.600000
                market_code = row['code'].split('.')
                if len(market_code) == 2:
                    exchange = market_code[0].upper()
                    code = market_code[1]
                    
                    # 转换交易所代码
                    if exchange == 'SH':
                        exchange = 'SSE'
                    elif exchange == 'SZ':
                        exchange = 'SZSE'
                    
                    # 获取股票名称
                    name = code_to_name.get(row['code'], code)
                    
                    stocks.append({
                        'code': f"{code}.{exchange[:2]}",
                        'name': name,
                        'market': 'STOCK',
                        'exchange': exchange
                    })
            except Exception as e:
                print(f"处理股票数据时出错: {row['code']}, 错误: {str(e)}")
                continue
        
        print(f"成功处理的股票数量: {len(stocks)}")
        return jsonify({
            'success': True,
            'data': stocks
        })
    except Exception as e:
        print(f"获取股票列表时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/stock/kline', methods=['GET'])
def get_stock_kline():
    """获取股票K线数据。
    
    Returns:
        Response: 包含K线数据的 JSON 响应
        
    Raises:
        Exception: 获取K线数据失败时抛出异常
    """
    try:
        print("Request parameters:", dict(request.args))
        
        symbol = request.args.get('symbol')
        period = request.args.get('period', '1D')  # 默认日K
        market = request.args.get('market', 'STOCK').upper()  # 默认STOCK
        adjust = request.args.get('adjust', 'qfq')  # 默认前复权
        
        print(
            f"处理K线请求 - 代码: {symbol}, 周期: {period}, "
            f"市场: {market}, 复权: {adjust}"
        )
        
        if not symbol:
            return jsonify({
                'success': False,
                'error': 'Symbol is required'
            }), 400

        MAX_RETRIES = 3  # 最大重试次数
        RETRY_DELAY = 2  # 重试延迟（秒）

        for retry in range(MAX_RETRIES):
            try:
                # 确保 BaoStock 连接正常
                if not ensure_baostock_connected():
                    if retry < MAX_RETRIES - 1:
                        print(f"第{retry + 1}次连接失败，等待{RETRY_DELAY}秒后重试...")
                        time.sleep(RETRY_DELAY)
                        continue
                    return jsonify({
                        'success': False,
                        'error': 'Failed to connect to BaoStock service'
                    }), 500

                # BaoStock的代码格式转换
                bs_symbol = convert_to_baostock_code(symbol, market)
                print(f"转换后的BaoStock代码: {bs_symbol}")

                # 判断是否是分钟级别的K线
                is_minute_level = period in ['1m', '5m', '15m', '30m', '60m']
                
                if is_minute_level:
                    # 获取分钟级别数据
                    period_num = PERIOD_MAP[period]
                    print(f"获取分钟级别数据, 转换后的周期: {period_num}")
                    klines = get_minute_kline(symbol, period_num, market=market)
                else:
                    # 获取日线及以上周期数据
                    frequency = PERIOD_MAP.get(period)
                    if frequency is None:
                        return jsonify({
                            'success': False,
                            'error': f'Invalid period: {period}'
                        }), 400

                    print(f"获取日线及以上周期数据, 转换后的周期: {frequency}")

                    # 设置复权类型
                    adjustflag = "2" if adjust == "qfq" else "3" if adjust == "hfq" else "1"
                    print(f"复权类型: {adjustflag}")
                    
                    # 获取当前日期
                    end_date = datetime.now().strftime('%Y-%m-%d')
                    # 获取一年前的日期作为开始日期
                    start_date = (datetime.now() - pd.Timedelta(days=365)).strftime('%Y-%m-%d')
                    
                    print(f"查询时间范围: {start_date} 至 {end_date}")

                    # 查询K线数据
                    rs = bs.query_history_k_data_plus(
                        bs_symbol,
                        "date,code,open,high,low,close,volume,amount,adjustflag,turn,pctChg",
                        start_date=start_date,
                        end_date=end_date,
                        frequency=frequency,
                        adjustflag=adjustflag
                    )
                    
                    # 检查查询状态
                    check_query_status(rs, f"查询K线数据 - {bs_symbol}")
                    
                    # 获取数据
                    data_list = []
                    while (rs.error_code == '0') & rs.next():
                        data_list.append(rs.get_row_data())
                    
                    # 转换为DataFrame
                    df = pd.DataFrame(data_list, columns=rs.fields)
                    print(f"获取到数据条数: {len(df)}")
                    
                    if df.empty:
                        print("警告: 未获取到数据")
                        return jsonify({
                            'success': True,
                            'data': []
                        })
                    
                    klines = []
                    for _, row in df.iterrows():
                        klines.append({
                            'time': int(datetime.strptime(row['date'], '%Y-%m-%d').timestamp()),
                            'open': float(row['open']),
                            'high': float(row['high']),
                            'low': float(row['low']),
                            'close': float(row['close']),
                            'volume': float(row['volume'])
                        })
                
                return jsonify({
                    'success': True,
                    'data': klines
                })
                
            except Exception as e:
                print(f"Error fetching data: {str(e)}")
                if retry < MAX_RETRIES - 1:
                    print(f"第{retry + 1}次重试失败，等待{RETRY_DELAY}秒后重试...")
                    time.sleep(RETRY_DELAY)
                    # 重试前确保连接正常
                    ensure_baostock_connected()
                    continue
                return jsonify({
                    'success': False,
                    'error': f"Failed to fetch data: {str(e)}"
                }), 500
            
    except Exception as e:
        print(f"Unexpected error in get_stock_kline: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/index/kline', methods=['GET'])
def get_index_kline():
    """获取指数K线数据"""
    try:
        print("Request parameters for index:", dict(request.args))
        
        symbol = request.args.get('symbol')
        period = request.args.get('period', '1D')  # 默认日K
        
        if not symbol:
            return jsonify({
                'success': False,
                'error': 'Symbol is required'
            }), 400

        MAX_RETRIES = 3  # 最大重试次数
        RETRY_DELAY = 2  # 重试延迟（秒）

        for retry in range(MAX_RETRIES):
            try:
                # 确保 BaoStock 连接正常
                if not ensure_baostock_connected():
                    if retry < MAX_RETRIES - 1:
                        print(f"第{retry + 1}次连接失败，等待{RETRY_DELAY}秒后重试...")
                        time.sleep(RETRY_DELAY)
                        continue
                    return jsonify({
                        'success': False,
                        'error': 'Failed to connect to BaoStock service'
                    }), 500

                # BaoStock的代码格式转换
                code = symbol.split('.')[0]
                exchange = symbol.split('.')[1].lower()
                bs_symbol = f"{exchange}.{code}"
                print(f"转换后的BaoStock代码: {bs_symbol}")

                # 判断是否是分钟级别的K线
                is_minute_level = period in ['1m', '5m', '15m', '30m', '60m']
                
                if is_minute_level:
                    # 获取分钟级别数据
                    period_num = PERIOD_MAP[period]
                    print(f"获取分钟级别数据, 转换后的周期: {period_num}")
                    klines = get_minute_kline(symbol, period_num, market='INDEX')
                else:
                    # 获取日线及以上周期数据
                    frequency = PERIOD_MAP.get(period)
                    if frequency is None:
                        return jsonify({
                            'success': False,
                            'error': f'Invalid period: {period}'
                        }), 400
                    
                    print(f"获取日线及以上周期数据, 转换后的周期: {frequency}")
                    
                    # 获取当前日期
                    end_date = datetime.now().strftime('%Y-%m-%d')
                    # 获取一年前的日期作为开始日期
                    start_date = (datetime.now() - pd.Timedelta(days=365)).strftime('%Y-%m-%d')
                    
                    print(f"查询时间范围: {start_date} 至 {end_date}")

                    # 查询指数K线数据
                    rs = bs.query_history_k_data_plus(
                        bs_symbol,
                        "date,code,open,high,low,close,preclose,volume,amount,pctChg",
                        start_date=start_date,
                        end_date=end_date,
                        frequency=frequency
                    )
                    
                    # 检查查询状态
                    check_query_status(rs, f"查询指数K线数据 - {bs_symbol}")
                    
                    # 获取数据
                    data_list = []
                    while (rs.error_code == '0') & rs.next():
                        data_list.append(rs.get_row_data())
                    
                    # 转换为DataFrame
                    df = pd.DataFrame(data_list, columns=rs.fields)
                    print(f"获取到数据条数: {len(df)}")
                    
                    if df.empty:
                        print("警告: 未获取到数据")
                        return jsonify({
                            'success': True,
                            'data': []
                        })
                    
                    klines = []
                    for _, row in df.iterrows():
                        klines.append({
                            'time': int(datetime.strptime(row['date'], '%Y-%m-%d').timestamp()),
                            'open': float(row['open']),
                            'high': float(row['high']),
                            'low': float(row['low']),
                            'close': float(row['close']),
                            'volume': float(row['volume']),
                            'preclose': float(row['preclose']),
                            'amount': float(row['amount']),
                            'pctChg': float(row['pctChg'])
                        })
                
                return jsonify({
                    'success': True,
                    'data': klines
                })
                
            except Exception as e:
                print(f"Error fetching index data: {str(e)}")
                if retry < MAX_RETRIES - 1:
                    print(f"第{retry + 1}次重试失败，等待{RETRY_DELAY}秒后重试...")
                    time.sleep(RETRY_DELAY)
                    # 重试前确保连接正常
                    ensure_baostock_connected()
                    continue
                return jsonify({
                    'success': False,
                    'error': f"Failed to fetch index data: {str(e)}"
                }), 500
            
    except Exception as e:
        print(f"Unexpected error in get_index_kline: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/gene/factors', methods=['GET'])
def get_factor_list():
    """获取可用的因子列表"""
    try:
        factors = []
        for factor_id, factor_class in FACTOR_MAP.items():
            # 获取因子的参数信息
            params = {}
            for name, default in factor_class.params._getitems():
                if name not in ['factor_type', 'mode']:  # 排除基类参数
                    params[name] = {
                        'default': default,
                        'type': type(default).__name__
                    }
            
            factors.append({
                'id': factor_id,
                'name': factor_class.__name__,
                'description': factor_class.__doc__,
                'parameters': params
            })
        
        return jsonify({
            'success': True,
            'data': factors
        })
    except Exception as e:
        print(f"Error in get_factor_list: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def load_data_with_info(cerebro, dataname, symbol_info, **kwargs):
    """加载数据并记录基本信息。
    
    Args:
        cerebro: Backtrader Cerebro 实例
        dataname: DataFrame 或 CSV 文件路径
        symbol_info: 交易品种信息 {code, name, market, exchange}
        **kwargs: 数据加载参数
        
    Returns:
        Data: 加载的数据对象
    """
    # 加载数据
    if isinstance(dataname, pd.DataFrame):
        data = bt.feeds.PandasData(dataname=dataname, **kwargs)
    else:
        data = bt.feeds.GenericCSVData(dataname=dataname, **kwargs)

    # 记录基本信息
    data.symbol_info = symbol_info
    data.params_info = {
        'dataname': str(dataname),
        'timeframe': kwargs.get('timeframe', bt.TimeFrame.Days),
        'compression': kwargs.get('compression', 1),
        'fromdate': kwargs.get('fromdate'),
        'todate': kwargs.get('todate'),
        'fields': ['open', 'high', 'low', 'close', 'volume', 'openinterest']
    }
    
    print(f"加载数据信息:")
    print(f"- 品种信息: {data.symbol_info}")
    print(f"- 参数信息: {data.params_info}")

    # 将数据添加到 Cerebro
    cerebro.adddata(data)
    return data

@app.route('/gene/locate', methods=['POST'])
def locate_gene():
    """基因定位服务。
    
    Returns:
        Response: 包含基因定位结果的 JSON 响应
        
    Raises:
        Exception: 基因定位失败时抛出异常
    """
    try:
        data = request.get_json()
        
        # 验证必要参数
        required_fields = ['symbol', 'market', 'period', 'factor_id', 'parameters', 'klines']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # 获取因子类
        factor_class = FACTOR_MAP.get(data['factor_id'])
        if not factor_class:
            return jsonify({
                'success': False,
                'error': f'Invalid factor_id: {data["factor_id"]}'
            }), 400
        
        # 创建 Cerebro 引擎
        cerebro = bt.Cerebro()
        
        # 准备数据
        df = pd.DataFrame(data['klines'])
        df['datetime'] = pd.to_datetime(df['time'].apply(
            lambda x: datetime.fromtimestamp(x)
        ))
        df.set_index('datetime', inplace=True)
        
        # 构建品种信息
        symbol_info = {
            'code': data['symbol'],
            'market': data['market'],
            'period': data['period']
        }
        
        # 加载数据
        load_data_with_info(
            cerebro=cerebro,
            dataname=df,
            symbol_info=symbol_info,
            timeframe=bt.TimeFrame.Minutes if data['period'].endswith('m')
                     else bt.TimeFrame.Days,
            compression=int(data['period'].rstrip('mDWM'))
                       if data['period'] != '1D' else 1
        )
        
        # 添加因子
        params = data.get('parameters', {})
        params.update({
            'factor_type': FactorType.TECHNICAL,
            'mode': FactorMode.RESEARCH
        })
        factor = cerebro.addindicator(factor_class, **params)
        
        # 运行回测
        cerebro.run()
        
        # 获取信号
        signals = []
        for signal in factor.get_signals():
            signals.append({
                'time': int(signal.time.timestamp()),
                'type': signal.type.value,
                'price': signal.price,
                'index': signal.index
            })
        
        return jsonify({
            'success': True,
            'data': {
                'signals': signals,
                'factor_values': factor.lines.factor.array.tolist()
            }
        })
        
    except Exception as e:
        print(f"Error in locate_gene: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 初始化时登录
init_baostock()

if __name__ == '__main__':
    app.run(host=HOST, port=PORT) 