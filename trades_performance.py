# -*- coding: utf-8 -*-
"""
策略回测绩效分析脚本
读取当前目录下的 trades.csv，计算并输出常见绩效指标（中文输出）。
"""
import pandas as pd
import numpy as np

# 期初资金
INITIAL_CAPITAL = 100000

# 读取交易明细
df = pd.read_csv('trades.csv')

# 只保留买入和卖出（OPEN/CLOSE）
buy_df = df[(df['action'] == 'OPEN') & (df['direct'] == 'LONG')]
sell_df = df[(df['action'] == 'CLOSE') & (df['direct'] == 'LONG')]

# 按时间排序
buy_df = buy_df.sort_values('time').reset_index(drop=True)
sell_df = sell_df.sort_values('time').reset_index(drop=True)

# 统计手续费
total_fee = df['fee'].fillna(0).sum()

# 统计交易次数
trade_count = len(df[df['action'].isin(['OPEN', 'CLOSE'])])

# 计算每笔交易盈亏
trades = []
for i in range(min(len(buy_df), len(sell_df))):
    buy = buy_df.iloc[i]
    sell = sell_df.iloc[i]
    pnl = (sell['price'] - buy['price']) * buy['qty'] - buy['fee'] - sell['fee']
    trades.append({
        'buy_time': buy['time'],
        'sell_time': sell['time'],
        'buy_price': buy['price'],
        'sell_price': sell['price'],
        'qty': buy['qty'],
        'pnl': pnl
    })

# 计算总盈亏
total_pnl = sum([t['pnl'] for t in trades])
final_capital = INITIAL_CAPITAL + total_pnl

# 计算胜率、盈亏比
win_trades = [t for t in trades if t['pnl'] > 0]
lose_trades = [t for t in trades if t['pnl'] <= 0]
win_rate = len(win_trades) / len(trades) if trades else 0
avg_win = np.mean([t['pnl'] for t in win_trades]) if win_trades else 0
avg_lose = -np.mean([t['pnl'] for t in lose_trades]) if lose_trades else 0
pl_ratio = avg_win / avg_lose if avg_lose > 0 else np.nan

# 生成净值曲线（只用于计算年化和回撤，不输出明细）
capital_curve = [INITIAL_CAPITAL]
for t in trades:
    capital_curve.append(capital_curve[-1] + t['pnl'])
capital_curve = np.array(capital_curve)

# 计算最大回撤
drawdown = np.maximum.accumulate(capital_curve) - capital_curve
max_drawdown = np.max(drawdown)
max_drawdown_pct = max_drawdown / np.maximum.accumulate(capital_curve).max() if np.maximum.accumulate(capital_curve).max() > 0 else 0

# 计算年化收益率
if trades:
    start_date = str(buy_df.iloc[0]['time'])
    end_date = str(sell_df.iloc[-1]['time'])
    start = pd.to_datetime(start_date, format='%Y%m%d%H%M')
    end = pd.to_datetime(end_date, format='%Y%m%d%H%M')
    days = (end - start).days
    years = days / 365.0 if days > 0 else 1
    annual_return = (final_capital / INITIAL_CAPITAL) ** (1 / years) - 1 if years > 0 else 0
else:
    annual_return = 0

# 计算夏普比率（假设无风险利率为0，使用每日收益率）
if len(capital_curve) > 1:
    returns = np.diff(capital_curve) / capital_curve[:-1]
    sharpe = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else np.nan
else:
    sharpe = np.nan

# 输出结果
print('策略回测绩效指标：')
print(f'期初资金：{INITIAL_CAPITAL:.2f} 元')
print(f'期末资金：{final_capital:.2f} 元')
print(f'总盈亏：{total_pnl:.2f} 元')
print(f'年化收益率：{annual_return*100:.2f}%')
print(f'最大回撤：{max_drawdown:.2f} 元（{max_drawdown_pct*100:.2f}%）')
print(f'夏普比率：{sharpe:.2f}')
print(f'胜率：{win_rate*100:.2f}%')
print(f'盈亏比：{pl_ratio:.2f}')
print(f'盈利次数：{len(win_trades)}，亏损次数：{len(lose_trades)}')
print(f'交易次数：{trade_count}')
print(f'手续费总额：{total_fee:.2f} 元') 