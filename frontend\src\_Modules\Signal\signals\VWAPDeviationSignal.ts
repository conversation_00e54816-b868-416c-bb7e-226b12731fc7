import { KLine, KLineData } from '@/shared_types/market';
import { BaseSignal, SignalParameter, SignalType } from '../BaseSignal';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';

/**
 * VWAP偏离信号
 * 使用价格与VWAP的偏离程度来判断超买超卖
 * 当价格显著低于VWAP时产生买入信号（超卖）
 * 当价格显著高于VWAP时产生卖出信号（超买）
 */
export class VWAPDeviationSignal extends BaseSignal {
  private period: number;
  private deviationThreshold: number;
  private volumeThreshold: number;

  constructor(config: SignalConfig) {
    super(config);
    
    // 设置参数
    this.period = this.parameters.period;
    this.deviationThreshold = this.parameters.deviationThreshold;
    this.volumeThreshold = this.parameters.volumeThreshold;

    // 验证参数
    if (this.deviationThreshold <= 0) {
      throw new Error('Deviation threshold must be positive');
    }
    if (this.volumeThreshold <= 1) {
      throw new Error('Volume threshold must be greater than 1');
    }
  }

  /**
   * 获取信号参数列表
   */
  static async getParameters(): Promise<SignalParameter[]> {
    return Promise.resolve([
      {
        name: 'period',
        paramType: 'number',
        default: 14,
        description: 'VWAP计算周期',
        minValue: 2,
        maxValue: 100,
        step: 1
      },
      {
        name: 'deviationThreshold',
        paramType: 'number',
        default: 0.02,
        description: '偏离阈值',
        minValue: 0.001,
        maxValue: 0.1,
        step: 0.001
      }
    ]);
  }

  /**
   * 计算VWAP
   */
  private calculateVWAP(data: KLineData[], period: number): number[] {
    const vwap: number[] = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        vwap.push(NaN);
        continue;
      }

      let sumPV = 0;  // 价格*成交量之和
      let sumV = 0;   // 成交量之和
      
      for (let j = 0; j < period; j++) {
        const typicalPrice = (data[i - j].high + data[i - j].low + data[i - j].close) / 3;
        const volume = data[i - j].volume;
        sumPV += typicalPrice * volume;
        sumV += volume;
      }

      vwap.push(sumPV / sumV);
    }
    return vwap;
  }

  /**
   * 计算平均成交量
   */
  private calculateAverageVolume(data: KLineData[], period: number): number[] {
    const avgVolume: number[] = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        avgVolume.push(NaN);
        continue;
      }

      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += data[i - j].volume;
      }
      avgVolume.push(sum / period);
    }
    return avgVolume;
  }

  // 计算市场信号的方法，输入为K线数据和参数，输出为市场信号数组
  calculateSignals(kline: KLine, params: Record<string, any>): MarketEvents.SignalItem[] {
    // 初始化信号数组
    const signals: MarketEvents.SignalItem[] = [];
    // 从参数中获取周期、偏离阈值和成交量阈值
    const period = params.period as number;
    const deviationThreshold = params.deviationThreshold as number;
    const volumeThreshold = params.volumeThreshold as number;

    const data = kline.data;

    // 计算VWAP（成交量加权平均价格）
    const vwap = this.calculateVWAP(data, period);
    
    // 计算平均成交量
    const avgVolume = this.calculateAverageVolume(data, period);

    // 从period开始遍历，寻找偏离点
    for (let i = period; i < data.length; i++) {
      const price = data[i].close;
      const volume = data[i].volume;
      const vwapPrice = vwap[i];
      
      // 计算价格偏离度
      const deviation = (price - vwapPrice) / vwapPrice;
      
      // 检查成交量是否放大
      const volumeIncrease = volume / avgVolume[i];

      // 价格显著低于VWAP且成交量放大
      if (deviation < -deviationThreshold && volumeIncrease > volumeThreshold) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.BUY,
          price: price,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      }
      // 价格显著高于VWAP且成交量放大
      else if (deviation > deviationThreshold && volumeIncrease > volumeThreshold) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.SELL,
          price: price,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      }
    }

    return signals;
  }
} 