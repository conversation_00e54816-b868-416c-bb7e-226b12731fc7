# 特别注意，目前的所有时间计算返回的都是秒计算
# 与JS的毫秒计算不一致

from flask import Flask, jsonify, request

import pandas as pd
import datetime
import json
import os
import traceback
import pytz
import struct
# import backtrader as bt
import sqlite3 # 添加 sqlite3 导入
# from strategy.gene.base import FactorType, FactorMode, SignalType
# from strategy.gene.factors.ma_cross import MACrossFactor
from dateutil import parser
from dateutil.tz import gettz
import sys
import codecs

# 导入tdx_kline_lib模块
from tdx_kline_lib import (
    get_stock_list as lib_get_stock_list,
    get_future_list as lib_get_future_list,
    get_us_stock_list as lib_get_us_stock_list,
    get_stock_kline as lib_get_stock_kline,
)

os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# 读取配置文件
# 获取当前文件的目录
current_dir = os.path.dirname(__file__)

# 构建 config.json 的路径
config_path = os.path.join(current_dir, 'config.json')

# 加载 config.json
with open(config_path, 'r', encoding='utf-8') as f:
    config = json.load(f)

# 获取Python服务配置
python_config = config['python_service']

# ========== root路径机制 ===========
def find_project_root():
    """向上查找 backend 目录，再往上一层即为 root"""
    cur = os.path.abspath(os.path.dirname(__file__))
    while True:
        if os.path.isdir(os.path.join(cur, 'backend')):
            return cur
        parent = os.path.dirname(cur)
        if parent == cur:
            raise RuntimeError("未找到 backend 目录，无法定位项目根目录")
        cur = parent

PROJECT_ROOT = find_project_root()

def resolve_data_path(config_path):
    """将配置中的 root/xxx 替换为实际根目录路径"""
    if config_path.startswith('root/'):
        return os.path.join(PROJECT_ROOT, config_path[5:])
    return config_path
# ========== end root路径机制 ===========

# 获取TDX符号配置
tdx_symbols_path = resolve_data_path(config['tdx_symbols']['path'])
tdx_data_path = resolve_data_path(config['tdx_data']['path'])

# 定义美股数据库路径
uscodes_db_path = resolve_data_path('root/backend/_Providers/_Python/uscodes.sqlite')

HOST = python_config['host']
PORT = python_config['port']

# K线周期映射
PERIOD_MAP = {
    # AData周期映射（股票用）
    "STOCK": {
        "1D": "1",      # 日线
        "1W": "2",      # 周线
        "1M": "3",      # 月线
        "quarter": "4",      # 季线
        "5m": "5",      # 5分钟
        "15m": "15",     # 15分钟
        "30m": "30",     # 30分钟
        "60m": "60"      # 1小时
    }
}

app = Flask(__name__)

def resolve_path(path):
    """解析任意相对路径（支持 ~、../、./）"""
    expanded = os.path.expanduser(path)  # 先处理 ~
    return os.path.abspath(expanded)     # 再转为绝对路径


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'okk',
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/stock/list', methods=['GET'])
def get_stock_list():
    """获取股票列表，包括A股和香港股票"""
    try:
        # 获取查询参数
        market_filter = request.args.get('market', 'all')  # 默认返回所有市场

        # 调用 tdx_kline_lib 中的函数
        stocks = lib_get_stock_list(market_filter)

        return jsonify({
            'success': True,
            'data': stocks
        })
    except Exception as e:
        print(f"Error in get_stock_list: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/future/list', methods=['GET'])
def get_future_list():
    """获取期货名称列表
    从 code2name.ini 文件读取期货代码、名称和交易所信息
    从 code2qhidx.ini 文件读取主连期货品种信息
    返回格式为 {code, name, market, exchange} 的列表，包含具体交割月份合约
    """
    try:
        # 调用 tdx_kline_lib 中的函数
        all_futures = lib_get_future_list()

        return jsonify({
            'success': True,
            'data': all_futures
        })
    except Exception as e:
        print(f"Error in get_future_list: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/us/list', methods=['GET'])
def get_us_stock_list():
    """从 uscodes.sqlite 数据库获取美股列表"""
    try:
        # 调用 tdx_kline_lib 中的函数
        us_stocks = lib_get_us_stock_list()

        return jsonify({
            'success': True,
            'data': us_stocks
        })

    except Exception as e:
        print(f"[错误] 获取美股列表时发生未知错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'An unexpected error occurred: {str(e)}'
        }), 500

@app.route('/index/kline', methods=['GET'])
@app.route('/stock/kline', methods=['GET'])
@app.route('/future/kline', methods=['GET'])
def get_stock_kline():

    try:

        symbol = request.args.get('symbol')
        period = request.args.get('period', '1D')  # 默认日K
        market = request.args.get('market', 'STOCK').upper()  # 默认STOCK
        exchange = request.args.get('exchange', '').upper()  # 交易所代码
        k_count = int(request.args.get('k_count', 0))  # 获取K线数量，默认0表示获取全部

        # 调用 tdx_kline_lib 中的函数
        klines = lib_get_stock_kline(symbol, period, market, exchange, k_count)

        return jsonify({
            'success': True,
            'data': klines
        })

    except Exception as e:
        print(f"Error in get_stock_kline: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(host=HOST, port=PORT)