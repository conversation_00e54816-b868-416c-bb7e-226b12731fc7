#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DataKit 数据中继服务
连接 Wonder Trader DataKit 与现有数据源的桥梁
"""

import os
import sys
import json
import socket
import threading
import time
import logging
import logging.handlers
from queue import Queue, PriorityQueue
from typing import Dict, List, Any, Optional, Tuple, Set, Union

# 添加当前目录到 sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入自定义模块
from adapters.tdx_adapter import TDXAdapter
from adapters.okx_adapter import OKXAdapter
from converters.bar_converter import BarConverter
from converters.tick_converter import TickConverter
from utils.symbol_parser import SymbolParser
from utils.udp_protocol import UDPProtocol
from utils.queue_manager import QueueManager

# 配置日志
def setup_logging(config):
    """设置日志"""
    log_config = config.get('log', {})
    log_level = getattr(logging, log_config.get('level', 'INFO'))
    log_file = log_config.get('file', 'logs/datakit_relay.log')
    max_size = log_config.get('max_size', 10 * 1024 * 1024)  # 默认 10MB
    backup_count = log_config.get('backup_count', 5)

    # 创建日志目录
    log_dir = os.path.dirname(log_file)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置日志处理器
    handler = logging.handlers.RotatingFileHandler(
        log_file, maxBytes=max_size, backupCount=backup_count, encoding='utf-8'
    )
    formatter = logging.Formatter(
        '[%(asctime)s] [%(levelname)s] [%(module)s] %(message)s'
    )
    handler.setFormatter(formatter)

    # 配置控制台处理器
    console = logging.StreamHandler()
    console.setFormatter(formatter)

    # 配置日志器
    logger = logging.getLogger()
    logger.setLevel(log_level)
    logger.addHandler(handler)
    logger.addHandler(console)

    return logger

class DataKitRelayServer:
    """DataKit 数据中继服务"""

    def __init__(self, config_path: str):
        """初始化服务"""
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)

        # 设置日志
        self.logger = setup_logging(self.config)
        self.logger.info("DataKit 数据中继服务初始化...")

        # UDP 服务器配置
        udp_config = self.config.get('udp_server', {})
        self.query_port = udp_config.get('query_port', 3997)
        self.broadcast_port = udp_config.get('broadcast_port', 9001)
        self.host = udp_config.get('host', '0.0.0.0')

        # 初始化 UDP 协议
        self.udp_protocol = UDPProtocol()

        # 初始化数据源适配器
        data_sources = self.config.get('data_sources', {})
        tdx_config = data_sources.get('tdx', {})
        okx_config = data_sources.get('okx', {})

        self.tdx_adapter = TDXAdapter(tdx_config)
        self.okx_adapter = OKXAdapter(okx_config)

        # 初始化数据转换器
        self.bar_converter = BarConverter()
        self.tick_converter = TickConverter()

        # 初始化品种解析器
        self.symbol_parser = SymbolParser()

        # 初始化请求队列管理器
        queue_config = self.config.get('queue', {})
        self.queue_manager = QueueManager(queue_config)

        # 初始化订阅列表
        self.subscriptions: Set[str] = set()

        # 初始化 UDP 套接字
        self.query_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.broadcast_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

        # 初始化线程
        self.running = False
        self.query_thread = None
        self.data_thread = None

        self.logger.info("DataKit 数据中继服务初始化完成")

    def start(self):
        """启动服务"""
        self.logger.info("启动 DataKit 数据中继服务...")

        # 绑定查询套接字
        try:
            self.query_socket.bind((self.host, self.query_port))
            self.logger.info(f"UDP 查询服务器监听 {self.host}:{self.query_port}")
        except Exception as e:
            self.logger.error(f"绑定 UDP 查询端口失败: {e}")
            return False

        # 设置运行标志
        self.running = True

        # 启动查询线程
        self.query_thread = threading.Thread(target=self._handle_queries)
        self.query_thread.daemon = True
        self.query_thread.start()

        # 启动数据线程
        self.data_thread = threading.Thread(target=self._process_data)
        self.data_thread.daemon = True
        self.data_thread.start()

        self.logger.info("DataKit 数据中继服务启动成功")
        return True

    def stop(self):
        """停止服务"""
        self.logger.info("停止 DataKit 数据中继服务...")

        # 设置停止标志
        self.running = False

        # 关闭套接字
        try:
            self.query_socket.close()
            self.broadcast_socket.close()
        except Exception as e:
            self.logger.error(f"关闭套接字失败: {e}")

        # 等待线程结束
        if self.query_thread and self.query_thread.is_alive():
            self.query_thread.join(timeout=5)

        if self.data_thread and self.data_thread.is_alive():
            self.data_thread.join(timeout=5)

        self.logger.info("DataKit 数据中继服务已停止")

    def _handle_queries(self):
        """处理查询请求"""
        self.logger.info("查询处理线程启动")

        while self.running:
            try:
                # 接收数据
                data, addr = self.query_socket.recvfrom(1024)

                # 解析请求
                request = self.udp_protocol.parse_request(data)
                if not request:
                    continue

                command, symbol = request

                # 处理订阅请求
                if command == self.udp_protocol.SUBSCRIBE_COMMAND:
                    self.logger.info(f"收到订阅请求: {symbol} 来自 {addr}")
                    self._handle_subscribe(symbol)

                # 处理取消订阅请求
                elif command == self.udp_protocol.UNSUBSCRIBE_COMMAND:
                    self.logger.info(f"收到取消订阅请求: {symbol} 来自 {addr}")
                    self._handle_unsubscribe(symbol)

            except Exception as e:
                self.logger.error(f"处理查询请求时出错: {e}")

        self.logger.info("查询处理线程结束")

    def _handle_subscribe(self, symbol: str):
        """处理订阅请求"""
        # 添加到订阅列表
        self.subscriptions.add(symbol)

        # 解析品种
        parsed_symbol = self.symbol_parser.parse(symbol)
        if not parsed_symbol:
            self.logger.warning(f"无法解析品种: {symbol}")
            return

        # 添加周期信息（默认使用1分钟周期）
        parsed_symbol['period'] = 'm1'

        # 添加到请求队列
        self.queue_manager.add_request(parsed_symbol)

    def _handle_unsubscribe(self, symbol: str):
        """处理取消订阅请求"""
        # 从订阅列表中移除
        if symbol in self.subscriptions:
            self.subscriptions.remove(symbol)

    def _process_data(self):
        """处理数据请求"""
        self.logger.info("数据处理线程启动")

        while self.running:
            try:
                # 获取下一个请求
                request = self.queue_manager.get_next_request()
                if not request:
                    time.sleep(0.1)  # 没有请求时休眠
                    continue

                # 获取数据
                data = self._fetch_data(request)
                if not data:
                    continue

                # 转换数据
                converted_data = self._convert_data(request, data)
                if not converted_data:
                    continue

                # 广播数据
                self._broadcast_data(converted_data)

            except Exception as e:
                self.logger.error(f"处理数据请求时出错: {e}")

        self.logger.info("数据处理线程结束")

    def _fetch_data(self, request):
        """获取数据"""
        symbol = request.get('symbol')
        exchange = request.get('exchange')
        market = request.get('market')
        period = request.get('period', 'm1')  # 默认使用1分钟周期

        self.logger.info(f"获取数据: {exchange}.{market}.{symbol}, 周期: {period}")

        # 根据市场类型选择适配器
        if market.lower() == 'crypto':
            return self.okx_adapter.fetch_data(symbol, exchange, period)
        else:
            return self.tdx_adapter.fetch_data(symbol, exchange, market, period)

    def _convert_data(self, request, data):
        """转换数据"""
        symbol = request.get('symbol')
        exchange = request.get('exchange')
        market = request.get('market')

        self.logger.info(f"转换数据: {exchange}.{market}.{symbol}")

        # 根据数据类型选择转换器
        if 'open' in data:  # K线数据
            return self.bar_converter.convert(exchange, symbol, data)
        else:  # Tick数据
            return self.tick_converter.convert(exchange, symbol, data)

    def _broadcast_data(self, data):
        """广播数据"""
        try:
            self.broadcast_socket.sendto(data, ('127.0.0.1', self.broadcast_port))
            self.logger.debug(f"数据广播成功: {len(data)} 字节")
        except Exception as e:
            self.logger.error(f"数据广播失败: {e}")

def main():
    """主函数"""
    # 获取配置文件路径
    config_path = os.path.join(current_dir, 'config.json')

    # 创建服务实例
    server = DataKitRelayServer(config_path)

    # 启动服务
    if server.start():
        try:
            # 保持主线程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            # 捕获 Ctrl+C
            print("接收到退出信号，正在停止服务...")
        finally:
            # 停止服务
            server.stop()

if __name__ == '__main__':
    main()