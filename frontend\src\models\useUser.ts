import { atom, useAtom } from 'jotai';
import { getDefaultStore } from 'jotai/vanilla';
import { getUserInfo, setUserInfo as saveUserInfo } from '@/utils/auth';
import type { UserInfo } from '@/utils/auth';
import { EventBus } from '@/events/eventBus';
import { UserEvents } from '@/events/events';

// 创建用户信息 atom
const userAtom = atom<UserInfo | null>(getUserInfo());

// 获取全局的 store
const store = getDefaultStore();

// 在非 React 环境中更新 atom
const updateUserInfo = (newUserInfo: UserInfo | null) => {
  store.set(userAtom, newUserInfo); // 更新 atom
  if (newUserInfo) {
    saveUserInfo(newUserInfo); // 保存到 localStorage
    console.log('[User] 用户信息已更新并保存:', newUserInfo.username);
  }
};

// 事件处理函数
const handleUpdateResult = (event: UserEvents.ResultPayload) => {
  console.log('[User] Received UPDATE_RESULT event:', event);
  if (event.success && event.data) {
    updateUserInfo(event.data);
  }
};

const handleLoginResult = (event: UserEvents.ResultPayload) => {
  console.log('[User] Received LOGIN_RESULT event:', event);
  if (event.success && event.data) {
    updateUserInfo(event.data);
  }
};

const handleRegisterResult = (event: UserEvents.ResultPayload) => {
  console.log('[User] Received REGISTER_RESULT event:', event);
  if (event.success && event.data) {
    updateUserInfo(event.data);
  }
};

// 跟踪事件监听器初始化状态
let isEventListenersInitialized = false;

// 添加初始化函数
const initializeUserEvents = () => {
  if (isEventListenersInitialized) {
    console.log('[User] Event listeners already initialized, skipping...');
    return;
  }

  console.log('[User] Setting up event listeners...');
  
  // 移除可能存在的旧监听器
  EventBus.off(UserEvents.Types.UPDATE_RESULT, handleUpdateResult);
  EventBus.off(UserEvents.Types.LOGIN_RESULT, handleLoginResult);
  EventBus.off(UserEvents.Types.REGISTER_RESULT, handleRegisterResult);

  // 设置新的监听器
  EventBus.on(UserEvents.Types.UPDATE_RESULT, handleUpdateResult);
  EventBus.on(UserEvents.Types.LOGIN_RESULT, handleLoginResult);
  EventBus.on(UserEvents.Types.REGISTER_RESULT, handleRegisterResult);

  // 标记为已初始化
  isEventListenersInitialized = true;
  console.log('[User] Event listeners initialization completed');
};

// 立即执行初始化
initializeUserEvents();

// 导出 Hook
export const useUser = () => {
  const [userInfo, setUserInfo] = useAtom(userAtom);
  return { userInfo, setUserInfo };
};

// 导出初始化函数以便需要时重新初始化
export { initializeUserEvents, updateUserInfo };