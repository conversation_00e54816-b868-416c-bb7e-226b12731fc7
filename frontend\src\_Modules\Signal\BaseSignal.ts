import { KLine, KLineData } from '@/shared_types/market';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';

/**
 * 形态定义接口
 */
export interface NameAndKlines {
    name: string;        // 形态名称
    klines: KLineData[]; // 形态包含的K线数组
}


/**
 * 信号参数定义
 */
export interface SignalParameter {
  name: string;           // 参数名称
  paramType: 'number' | 'string' | 'boolean';  // 参数类型
  default: number | string | boolean;     // 默认值
  description: string;    // 参数描述
  minValue?: number;     // 数值类型的最小值
  maxValue?: number;     // 数值类型的最大值
  step?: number;         // 数值类型的步进值
  options?: NameAndKlines[];   // 选择类型的选项
}

/**
 * 信号类型
 */
export enum SignalType {
  BUY = 'BUY',
  SELL = 'SELL',
  NONE = 'NONE'
}

/**
 * 交易记录定义
 */
export interface TradeRecord {
  entryTime: Date;       // 入场时间
  entryPrice: number;    // 入场价格
  exitTime?: Date;       // 出场时间
  exitPrice?: number;    // 出场价格
  profit?: number;       // 收益
  profitRatio?: number;  // 收益率
}

/**
 * 信号定义
 */
export interface SignalDefinition {
  name: string;
  description: string;
  parameters: SignalParameter[];
  klines: NameAndKlines[];
  signalClassName: string;
}

/**
 * 信号基类
 * 所有交易信号都需要继承此基类
 */
export abstract class BaseSignal {
  protected name: string;
  protected description: string = '';
  protected parameters: Record<string, any>;
  protected signalTriggers: MarketEvents.SignalItem[] = [];
  protected parameterDefinitions: SignalParameter[] = [];

  /**
   * 基础信号构造函数
   * @param config 信号配置
   */
  constructor(config: SignalConfig) {
    this.name = config.name;
    this.parameters = config.parameters;
    this.signalTriggers = [];
    
    // 初始化时获取参数定义
    (this.constructor as any).getParameters().then((params: SignalParameter[]) => {
      this.parameterDefinitions = params;
    });
  }

  /**
   * 获取信号名称
   */
  getName(): string {
    return this.name;
  }

  /**
   * 获取信号参数列表
   * 每个信号类都必须实现此静态方法
   */
  static getParameters(): Promise<SignalParameter[]> {
    throw new Error('getParameters must be implemented by child class');
  }

  /**
   * 获取信号触发点列表
   */
  getSignalTriggers(): MarketEvents.SignalItem[] {
    return this.signalTriggers;
  }

  /**
   * 验证参数值是否有效
   * @param params 参数值对象
   */
  validateParameters(params: Record<string, any>): boolean {
    // 使用已保存的参数定义进行验证
    for (const param of this.parameterDefinitions) {
      const value = params[param.name];
      
      // 检查必需参数
      if (value === undefined) {
        console.error(`Missing required parameter: ${param.name}`);
        return false;
      }

      // 类型检查
      if (typeof value !== param.paramType) {
        console.error(`Invalid type for parameter ${param.name}: expected ${param.paramType}, got ${typeof value}`);
        return false;
      }

      // 数值范围检查
      if (param.paramType === 'number') {
        if (param.minValue !== undefined && value < param.minValue) {
          console.error(`Parameter ${param.name} below minimum value: ${param.minValue}`);
          return false;
        }
        if (param.maxValue !== undefined && value > param.maxValue) {
          console.error(`Parameter ${param.name} above maximum value: ${param.maxValue}`);
          return false;
        }
      }

      // 选项检查
      if (param.options && !param.options.some((option: NameAndKlines) => option.name === value)) {
        console.error(`Invalid value for parameter ${param.name}: ${value}. Must be one of: ${param.options.map(o => o.name).join(', ')}`);
        return false;
      }
    }

    return true;
  }

  /**
   * 计算信号
   * 每个信号类都必须实现此方法
   * 返回：
   */
  abstract calculateSignals(kline: KLine, params: Record<string, any>): MarketEvents.SignalItem[];

  /**
   * 添加信号触发点
   * @param signal 新的信号触发点
   */
  protected addSignalTrigger(signal: MarketEvents.SignalItem): void {
    this.signalTriggers.push(signal);
  }
} 