#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能重启机制测试脚本
测试数据引擎的智能重启功能：只有当contracts.json发生变化时才重启
"""

import os
import sys
import json
import requests
import time

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 策略服务器地址
STRATEGY_SERVER_URL = "http://localhost:5002"

def test_smart_restart_api():
    """测试智能重启机制的API"""
    
    print("=== 智能重启机制测试 ===\n")
    
    # 测试数据
    project_a = "smart_test_project_A"
    project_b = "smart_test_project_B"
    project_c = "smart_test_project_C"
    
    contracts_a = ["SSE.600036", "SZSE.000001"]
    contracts_b = ["SSE.600036", "SSE.600519"]  # 与A有重叠
    contracts_c = ["SSE.600036"]  # 完全重叠
    
    try:
        print("📊 初始状态检查...")
        check_status("初始状态")
        
        print("\n🔥 场景1: 首次订阅 - 应该重启")
        print("1️⃣ 项目A订阅合约（首次）...")
        result = subscribe_project_smart(project_a, contracts_a)
        print(f"   结果: 重启={result.get('engine_restarted', 'N/A')}, 变化={result.get('contracts_changed', 'N/A')}")
        check_status("项目A订阅后")
        
        print("\n🚀 场景2: 重叠订阅 - 不应该重启")
        print("2️⃣ 项目B订阅合约（与A有重叠）...")
        result = subscribe_project_smart(project_b, contracts_b)
        print(f"   结果: 重启={result.get('engine_restarted', 'N/A')}, 变化={result.get('contracts_changed', 'N/A')}")
        check_status("项目B订阅后")
        
        print("\n🎯 场景3: 完全重叠订阅 - 不应该重启")
        print("3️⃣ 项目C订阅合约（完全重叠）...")
        result = subscribe_project_smart(project_c, contracts_c)
        print(f"   结果: 重启={result.get('engine_restarted', 'N/A')}, 变化={result.get('contracts_changed', 'N/A')}")
        check_status("项目C订阅后")
        
        print("\n🔄 场景4: 部分退订 - 不应该重启")
        print("4️⃣ 项目C取消订阅（其他项目仍在使用）...")
        result = unsubscribe_project_smart(project_c)
        print(f"   结果: 重启={result.get('engine_restarted', 'N/A')}, 变化={result.get('contracts_changed', 'N/A')}")
        check_status("项目C取消订阅后")
        
        print("\n⚡ 场景5: 导致品种移除的退订 - 应该重启")
        print("5️⃣ 项目A取消订阅（会导致SZSE.000001被移除）...")
        result = unsubscribe_project_smart(project_a)
        print(f"   结果: 重启={result.get('engine_restarted', 'N/A')}, 变化={result.get('contracts_changed', 'N/A')}")
        check_status("项目A取消订阅后")
        
        print("\n🧹 清理: 项目B取消订阅...")
        result = unsubscribe_project_smart(project_b)
        print(f"   结果: 重启={result.get('engine_restarted', 'N/A')}, 变化={result.get('contracts_changed', 'N/A')}")
        check_status("全部清理后")
        
        print("\n🎉 智能重启测试完成！")
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")

def subscribe_project_smart(project_id, contracts):
    """使用智能重启订阅项目合约"""
    print(f"  订阅项目 {project_id}: {contracts}")
    
    response = requests.post(
        f"{STRATEGY_SERVER_URL}/data_engine/update_and_restart",
        json={
            "project_id": project_id,
            "contract_codes": contracts
        },
        timeout=30
    )
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"  ✓ 订阅成功: {result.get('message')}")
            return result
        else:
            print(f"  ✗ 订阅失败: {result.get('error')}")
            return {}
    else:
        print(f"  ✗ 请求失败: HTTP {response.status_code}")
        return {}

def unsubscribe_project_smart(project_id):
    """取消订阅项目"""
    print(f"  取消订阅项目 {project_id}")
    
    response = requests.post(
        f"{STRATEGY_SERVER_URL}/data_engine/unsubscribe",
        json={"project_id": project_id},
        timeout=10
    )
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"  ✓ 取消订阅成功: {result.get('message')}")
            return result
        else:
            print(f"  ✗ 取消订阅失败: {result.get('error')}")
            return {}
    else:
        print(f"  ✗ 请求失败: HTTP {response.status_code}")
        return {}

def check_status(stage_name):
    """检查当前状态"""
    print(f"  📋 {stage_name}:")
    
    response = requests.get(f"{STRATEGY_SERVER_URL}/data_engine/status", timeout=10)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            data = result.get('data', {})
            
            print(f"    引擎运行: {data.get('engine_running')}")
            print(f"    项目数量: {data.get('total_projects')}")
            print(f"    活跃合约: {data.get('subscribed_contracts')}")
            
            # 显示引用计数
            ref_counts = data.get('contract_ref_counts', {})
            if ref_counts:
                print(f"    引用计数: {ref_counts}")
            else:
                print(f"    引用计数: 无")
        else:
            print(f"    ✗ 状态获取失败: {result.get('error')}")
    else:
        print(f"    ✗ 状态请求失败: HTTP {response.status_code}")

def test_smart_restart_local():
    """测试本地智能重启机制"""
    
    print("=== 本地智能重启机制测试 ===\n")
    
    try:
        # 导入数据引擎管理器
        from strategy_server import DataEngineManager
        
        # 创建管理器实例
        manager = DataEngineManager()
        
        print("🔥 场景1: 首次订阅")
        success, changed = manager.register_project_subscription("project_A", ["SSE.600036", "SZSE.000001"])
        print(f"  注册成功: {success}, 合约变化: {changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n🚀 场景2: 重叠订阅")
        success, changed = manager.register_project_subscription("project_B", ["SSE.600036", "SSE.600519"])
        print(f"  注册成功: {success}, 合约变化: {changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n🎯 场景3: 完全重叠订阅")
        success, changed = manager.register_project_subscription("project_C", ["SSE.600036"])
        print(f"  注册成功: {success}, 合约变化: {changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n🔄 场景4: 部分退订")
        success, changed = manager.unregister_project_subscription("project_C")
        print(f"  退订成功: {success}, 合约变化: {changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n⚡ 场景5: 导致品种移除的退订")
        success, changed = manager.unregister_project_subscription("project_A")
        print(f"  退订成功: {success}, 合约变化: {changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n🧹 清理")
        success, changed = manager.unregister_project_subscription("project_B")
        print(f"  退订成功: {success}, 合约变化: {changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n🎯 本地智能重启测试完成！")
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
    except Exception as e:
        print(f"✗ 本地测试过程中发生错误: {e}")

def test_edge_cases_smart():
    """测试智能重启的边界情况"""
    
    print("=== 智能重启边界情况测试 ===\n")
    
    try:
        from strategy_server import DataEngineManager
        manager = DataEngineManager()
        
        print("1️⃣ 测试项目更新订阅（部分重叠）...")
        # 第一次订阅
        success, changed = manager.register_project_subscription("project_X", ["SSE.600036", "SZSE.000001"])
        print(f"  首次订阅: 成功={success}, 变化={changed}")
        
        # 更新订阅（部分重叠）
        success, changed = manager.register_project_subscription("project_X", ["SSE.600036", "SSE.600519"])
        print(f"  更新订阅: 成功={success}, 变化={changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n2️⃣ 测试项目更新订阅（完全不同）...")
        success, changed = manager.register_project_subscription("project_X", ["SZSE.159934", "SSE.510300"])
        print(f"  完全更新: 成功={success}, 变化={changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n3️⃣ 测试项目更新订阅（相同合约）...")
        success, changed = manager.register_project_subscription("project_X", ["SZSE.159934", "SSE.510300"])
        print(f"  相同订阅: 成功={success}, 变化={changed}")
        print(f"  活跃合约: {manager.get_all_subscribed_contracts()}")
        
        print("\n🎯 边界情况测试完成！")
        
    except Exception as e:
        print(f"✗ 边界情况测试发生错误: {e}")

if __name__ == "__main__":
    print("智能重启机制测试工具\n")
    print("选择测试模式:")
    print("1. API测试 (需要 strategy_server.py 运行)")
    print("2. 本地测试 (直接测试管理器类)")
    print("3. 边界情况测试")
    print("4. 全部测试")
    
    choice = input("\n请选择 (1/2/3/4): ").strip()
    
    if choice == "1":
        test_smart_restart_api()
    elif choice == "2":
        test_smart_restart_local()
    elif choice == "3":
        test_edge_cases_smart()
    elif choice == "4":
        test_smart_restart_local()
        print("\n" + "="*50 + "\n")
        test_edge_cases_smart()
        print("\n" + "="*50 + "\n")
        test_smart_restart_api()
    else:
        print("无效选择")
