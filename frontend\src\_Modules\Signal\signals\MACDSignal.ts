import { KLine, KLineData } from '@/shared_types/market';
import { BaseSignal, SignalParameter, SignalType } from '../BaseSignal';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';
import { MACD } from '@debut/indicators';

/**
 * MACD金叉死叉信号
 * 使用MACD指标判断趋势转折
 * 当MACD线上穿信号线时产生买入信号（金叉）
 * 当MACD线下穿信号线时产生卖出信号（死叉）
 * 同时检测顶背离和底背离
 */

export class MACDSignal extends BaseSignal {
  /**
   * 获取信号参数列表
   */
  static async getParameters(): Promise<SignalParameter[]> {
    return Promise.resolve([
      {
        name: 'fastPeriod',
        paramType: 'number',
        default: 12,
        description: '快线周期',
        minValue: 2,
        maxValue: 100,
        step: 1
      },
      {
        name: 'slowPeriod',
        paramType: 'number',
        default: 26,
        description: '慢线周期',
        minValue: 5,
        maxValue: 200,
        step: 1
      },
      {
        name: 'signalPeriod',
        paramType: 'number',
        default: 9,
        description: '信号线周期',
        minValue: 2,
        maxValue: 50,
        step: 1
      },
      {
        name: 'divergenceLength',
        paramType: 'number',
        default: 5,
        description: '背离判断期间',
        minValue: 1,
        maxValue: 20,
        step: 1
      },
      {
        name: 'divergenceThreshold',
        paramType: 'number',
        default: 0.1,
        description: '背离确认阈值',
        minValue: 0.01,
        maxValue: 1,
        step: 0.01
      }
    ]);
  }

  // 检查是否形成顶背离
  private checkBearishDivergence(
    prices: number[],
    macdValues: number[],
    index: number,
    length: number,
    threshold: number
  ): boolean {
    if (index < length) return false;

    // 获取当前段的最高价和对应的MACD值
    let currentPriceHigh = prices[index];
    let currentMacdHigh = macdValues[index];
    let previousPriceHigh = -Infinity;
    let previousMacdHigh = -Infinity;

    // 查找前一段的最高点
    for (let i = index - 1; i >= index - length; i--) {
      if (prices[i] > previousPriceHigh) {
        previousPriceHigh = prices[i];
        previousMacdHigh = macdValues[i];
      }
    }

    // 判断是否形成顶背离：
    return (
      currentPriceHigh > previousPriceHigh &&
      currentMacdHigh < previousMacdHigh &&
      (previousMacdHigh - currentMacdHigh) / Math.abs(previousMacdHigh) > threshold
    );
  }

  // 检查是否形成底背离
  private checkBullishDivergence(
    prices: number[],
    macdValues: number[],
    index: number,
    length: number,
    threshold: number
  ): boolean {
    if (index < length) return false;

    // 获取当前段的最低价和对应的MACD值
    let currentPriceLow = prices[index];
    let currentMacdLow = macdValues[index];
    let previousPriceLow = Infinity;
    let previousMacdLow = Infinity;

    // 查找前一段的最低点
    for (let i = index - 1; i >= index - length; i--) {
      if (prices[i] < previousPriceLow) {
        previousPriceLow = prices[i];
        previousMacdLow = macdValues[i];
      }
    }

    // 判断是否形成底背离：
    return (
      currentPriceLow < previousPriceLow &&
      currentMacdLow > previousMacdLow &&
      (currentMacdLow - previousMacdLow) / Math.abs(previousMacdLow) > threshold
    );
  }

  calculateSignals(kline: KLine, params: Record<string, any>): MarketEvents.SignalItem[] {
    const signals: MarketEvents.SignalItem[] = [];
    const data = kline.data;
    const closePrices = data.map(d => d.close);
    
    // 创建 MACD 实例
    const macd = new MACD(Number(params.fastPeriod), Number(params.slowPeriod), 
      Number(params.signalPeriod));

    // 计算 MACD 值 - 使用循环来保持状态
    const macdResults = [];
    for (const price of closePrices) {
      macdResults.push(macd.nextValue(price));
    }

    // 提取 MACD 值数组，用于背离检测
    const macdValues = macdResults.map(r => r.macd);

    // 从第一个有效的MACD值开始
    const startIndex = Math.max(params.slowPeriod, params.divergenceLength || 5);

    for (let i = startIndex; i < data.length; i++) {
      const currentResult = macdResults[i];
      const prevResult = macdResults[i - 1];
      
      if (!currentResult || !prevResult) continue;

      const currentPrice = data[i].close;
      const currentTime = data[i].time;

      // 计算当前和前一个MACD柱状值
      const currentHist = currentResult.histogram;
      const prevHist = prevResult.histogram;

      // 检查金叉和底背离
      if (
        (prevHist < 0 && currentHist > 0) || // 金叉
        this.checkBullishDivergence(
          closePrices,
          macdValues,
          i,
          params.divergenceLength || 5,
          params.divergenceThreshold || 0.1
        ) // 底背离
      ) {
        signals.push({
          time: currentTime,
          type: SignalType.BUY,
          price: currentPrice,
          index: i
        });
      }
      // 检查死叉和顶背离
      else if (
        (prevHist > 0 && currentHist < 0) || // 死叉
        this.checkBearishDivergence(
          closePrices,
          macdValues,
          i,
          params.divergenceLength || 5,
          params.divergenceThreshold || 0.1
        ) // 顶背离
      ) {
        signals.push({
          time: currentTime,
          type: SignalType.SELL,
          price: currentPrice,
          index: i
        });
      }
    }

    return signals;
  }
} 