# K线图面板组件

## 测量线绘制逻辑

### 基本步骤
测量线总共需要5个步骤完成绘制：
1. 第1步：确定第一个点（起点）
2. 第2步：移动鼠标，实时预览
3. 第3步：确定第二个点（中点）
4. 第4步：移动鼠标，实时预览
5. 第5步：确定第三个点（终点）

### 绘制规则
1. 基础线段绘制：
   - 连接点1和点2的线段
   - 连接点2和点3的线段

2. 距离计算：
   - delta = 点2 - 点1（计算两点间的距离）

3. 扩展线段绘制：
   - 以点3为基准点
   - 根据delta的正负决定向上或向下移动
   - 移动距离为|delta|
   - 得到点A
   - 绘制：点A和点3之间的线段
   - 绘制：在点A位置的横线（从左2K线到右2K线）

4. 继续扩展：
   - 以点A为基准点
   - 同样根据delta的正负和大小移动
   - 得到点B
   - 绘制：点B和点A之间的线段
   - 绘制：在点B位置的横线（从左2K线到右2K线）

### 交互功能
1. 绘制过程：
   - 第2步和第4步移动时，实时预览完整图形
   - 所有线段保持等距
   - 方向根据delta自动调整

2. 编辑功能：
   - 鼠标悬停时显示所有定点
   - 可以拖动任意点进行编辑
   - 编辑时保持等距关系
   - 图形会随K线缩放和平移

### 技术实现
1. 覆盖物属性：
   - totalStep: 5（总步骤数）
   - needDefaultPointFigure: true（启用定点显示）
   - needDefaultXAxisFigure: true（启用X轴图形）
   - needDefaultYAxisFigure: true（启用Y轴图形）

2. 关键方法：
   - createPointFigures：负责绘制所有图形
   - performEventMoveForDrawing：处理移动时的实时预览
   - performEventPressedMove：处理点的编辑

3. 数据关联：
   - 每个点都有dataIndex和timestamp
   - 保持与K线数据的同步
   - 支持图表缩放和平移 