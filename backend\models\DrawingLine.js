// backend/models/DrawingLine.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const DrawingLine = sequelize.define('DrawingLine', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users', // 引用 users 表
        key: 'id'
      }
    },
    symbol: {
      type: DataTypes.STRING,  // 存储标识符字符串，例如 "BINANCE:BTCUSDT"
      allowNull: false
    },
    interval: {
      type: DataTypes.STRING,  // K线周期，例如 "1h", "1d"
      allowNull: false
    },
    overlays: {
      type: DataTypes.JSON,    // 存储绘图数据的 JSON 对象数组
      allowNull: false
    }
  }, {
    tableName: 'drawing_lines',
    timestamps: false,
    indexes: [
      // 添加联合唯一索引，确保每个用户的每个品种的每个周期只有一条记录
      {
        unique: true,
        fields: ['userId', 'symbol', 'interval']
      }
    ]
  });

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  DrawingLine.associate = function(models) {
    // DrawingLine 属于一个 User
    DrawingLine.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return DrawingLine;
};