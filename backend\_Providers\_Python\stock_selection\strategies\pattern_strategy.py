import pandas as pd
import numpy as np
from typing import List, Dict, Any

def select_stock(klines: List[Dict[str, Any]], threshold: float, values: List[int]) -> bool:
    """
    形态选股策略
    
    策略逻辑：
    1. 对K线数据进行滑动窗口分析，窗口长度为len(values)
    2. 对每个窗口计算形态序列并与目标序列匹配
    3. 根据匹配度判断是否选中
    
    Args:
        klines: 单个品种的K线数据列表
        threshold: 匹配度阈值
        values: 目标形态序列
            
    Returns:
        bool: True表示选中，False表示不选中
    """
    try:
        # 检查K线数据是否足够
        # 检查values是否为空
        if not values or len(values) == 0:
            print(f"[形态选股策略] values参数为空或无效: {values}")
            return False
        
        
        if len(klines) < len(values):
            print(f"[形态选股策略] K线数据不足，需要{len(values)}根，实际{len(klines)}根")
            return False
        
        # 从values中获取格子数（最大值）
        grid_count = max(values)
        
        # 滑动窗口分析
        for i in range(len(klines) - len(values) + 1):
            # 取出当前窗口的K线数据
            window_klines = klines[i:i + len(values)]
            
            # 获取窗口内K线的最低和最高价
            lows = [k['low'] for k in window_klines]
            highs = [k['high'] for k in window_klines]
            price_min = min(lows)
            price_max = max(highs)
            
            # 避免除零错误
            if price_max == price_min:
                print(f"[形态选股策略] 窗口{i}价格范围为零，跳过")
                continue
            
            # 计算网格大小
            grid_size = (price_max - price_min) / grid_count
            
            # 计算当前窗口的形态序列
            current_sequence = []
            for k in window_klines:
                close_price = k['close']
                # 计算收盘价落在哪个格子（从1开始）
                grid_pos = int((close_price - price_min) // grid_size) + 1
                # 确保格子编号在有效范围内
                grid_pos = max(1, min(grid_count, grid_pos))
                current_sequence.append(grid_pos)
            
            # 计算匹配度
            match_count = sum(1 for a, b in zip(current_sequence, values) if a == b)
            match_score = match_count / len(values)
            
            print(f"[形态选股策略] 窗口{i}: 当前序列={current_sequence}, 目标序列={values}, 匹配度={match_score:.3f}")
            
            # 判断是否达到阈值
            if match_score >= threshold:
                print(f"[形态选股策略] 窗口{i}匹配成功，匹配度{match_score:.3f} >= 阈值{threshold}")
                return True
        
        print(f"[形态选股策略] 所有窗口都未达到阈值{threshold}")
        return False
        
    except Exception as e:
        print(f"[形态选股策略] 策略执行出错: {str(e)}")
        return False