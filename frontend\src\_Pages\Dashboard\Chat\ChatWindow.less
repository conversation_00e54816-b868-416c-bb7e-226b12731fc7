@import '../../../theme/antd5-theme.less';
@import '../../../styles/variables.less';

.chat-window {
  z-index: 9999 !important;
  position: relative;
  
  // 确保所有Popover和Dialog都在指标窗口之上
  .ant-popover, .ant-modal {
    z-index: 10000 !important;
  }

  .chat-messages {
    height: 380px;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 和 Edge */
    padding-bottom: 16px;
    margin-bottom: 12px;
    padding: 8px;
    width: calc(100% - 8px);
    margin: 0 -4px;
    box-sizing: border-box;

    &::-webkit-scrollbar {
      display: none;
    }

    .ant-col {
      .ant-card.message-bubble {
        .ant-card-body {
          padding: @padding-sm @padding-md;
          min-height: 24px;
        }
      }
    }

    .avatar-left {
      text-align: left;
    }

    .avatar-right {
      text-align: right;
    }

    .message-bubble {
      transition: all 0.3s;
    }
  }

  .chat-input {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
  }

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chat-search {
    padding: 8px;
    margin-bottom: 8px;

    .search-results {
      margin-top: 8px;
      max-height: 200px;
      overflow-y: auto;

      .search-result-item {
        padding: 4px 8px;
        cursor: pointer;
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }

  .emoji-picker {
    padding: 8px;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .emoji-item {
      cursor: pointer;
      font-size: 20px;
      padding: 4px;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }

  @media (max-width: 768px) {
    .chat-input {
      .ant-input {
        font-size: 16px;
      }
    }

    .ant-popover {
      z-index: 1050;
    }
  }

  // 同样处理搜索结果的滚动条
  .search-results {
    &::-webkit-scrollbar {
      width: 6px;
      display: none;
    }
    scrollbar-width: none;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }

  .image-preview {
    img {
      transition: transform 0.3s ease;
      &:hover {
        transform: scale(1.02);
      }
    }
  }
} 