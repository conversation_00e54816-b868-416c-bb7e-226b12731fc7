import { CHAT_CONSTANTS, CHAT_ERRORS } from './constants';

export const validateMessage = (content: string): string | null => {
  if (content.length > CHAT_CONSTANTS.MAX_MESSAGE_LENGTH) {
    return CHAT_ERRORS.MESSAGE_TOO_LONG;
  }
  return null;
};

export const validateFile = (file: File): string | null => {
  if (file.size > CHAT_CONSTANTS.MAX_FILE_SIZE) {
    return CHAT_ERRORS.FILE_TOO_LARGE;
  }
  if (!CHAT_CONSTANTS.SUPPORTED_FILE_TYPES.includes(file.type)) {
    return CHAT_ERRORS.UNSUPPORTED_FILE_TYPE;
  }
  return null;
}; 