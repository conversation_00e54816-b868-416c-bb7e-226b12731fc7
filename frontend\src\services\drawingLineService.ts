import { DrawingLineRecord, GetDrawingLinesResponse, SaveDrawingLinesRequest, SaveDrawingLinesResponse } from '../shared_types/chart';
import { request } from '../utils/request';

/**
 * 画线服务
 */
export const drawingLineService = {
  /**
   * 获取画线数据
   * @param symbol 交易对
   * @param interval K线周期
   */
  getDrawingLines: async (symbol: string, interval: string): Promise<GetDrawingLinesResponse> => {
    return request.get('/api/drawing-lines', { params: { symbol, interval } });
  },

  /**
   * 保存画线数据
   * @param data 画线数据
   */
  saveDrawingLines: async (data: SaveDrawingLinesRequest): Promise<SaveDrawingLinesResponse> => {
    return request.post('/api/drawing-lines', data);
  },

  /**
   * 删除画线数据
   * @param symbol 交易对
   * @param interval K线周期
   */
  deleteDrawingLines: async (symbol: string, interval: string): Promise<SaveDrawingLinesResponse> => {
    return request.delete('/api/drawing-lines', { params: { symbol, interval } });
  }
}; 