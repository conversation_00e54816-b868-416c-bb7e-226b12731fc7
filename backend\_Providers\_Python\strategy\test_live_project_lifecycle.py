#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实盘项目生命周期测试脚本
测试启动、状态查询、停止等完整流程
"""

import requests
import time
import json

# 策略服务器地址
STRATEGY_SERVER_URL = "http://localhost:5002"

def test_live_project_lifecycle():
    """测试实盘项目完整生命周期"""
    
    print("=== 实盘项目生命周期测试 ===\n")
    
    project_id = 1
    
    try:
        # 1. 启动实盘项目
        print("1️⃣ 启动实盘项目...")
        start_response = requests.post(
            f"{STRATEGY_SERVER_URL}/api/strategy/live/start/{project_id}",
            timeout=30
        )
        
        if start_response.status_code == 200:
            result = start_response.json()
            if result.get('success'):
                print(f"✓ 启动成功: {result.get('message')}")
                print(f"  项目ID: {result.get('project_id')}")
                print(f"  进程ID: {result.get('process_id')}")
                print(f"  项目目录: {result.get('project_dir')}")
                print(f"  策略数量: {result.get('strategies_count')}")
                print(f"  合约数量: {result.get('contracts_count')}")
            else:
                print(f"✗ 启动失败: {result.get('error')}")
                return False
        else:
            print(f"✗ 启动请求失败: HTTP {start_response.status_code}")
            return False
        
        print()
        
        # 2. 等待一段时间让项目稳定运行
        print("2️⃣ 等待项目稳定运行...")
        time.sleep(5)
        
        # 3. 查询项目状态
        print("3️⃣ 查询项目状态...")
        status_response = requests.get(
            f"{STRATEGY_SERVER_URL}/api/strategy/live/status/{project_id}",
            timeout=10
        )
        
        if status_response.status_code == 200:
            result = status_response.json()
            if result.get('success'):
                print(f"✓ 状态查询成功:")
                print(f"  项目ID: {result.get('project_id')}")
                print(f"  运行状态: {result.get('is_running')}")
                print(f"  进程ID: {result.get('process_id')}")
                print(f"  状态: {result.get('status')}")
            else:
                print(f"✗ 状态查询失败: {result.get('error')}")
        else:
            print(f"✗ 状态查询请求失败: HTTP {status_response.status_code}")
        
        print()
        
        # 4. 查询所有运行中的项目
        print("4️⃣ 查询所有运行中的项目...")
        list_response = requests.get(
            f"{STRATEGY_SERVER_URL}/api/strategy/live/list",
            timeout=10
        )
        
        if list_response.status_code == 200:
            result = list_response.json()
            if result.get('success'):
                projects = result.get('projects', [])
                print(f"✓ 找到 {result.get('total_count')} 个项目:")
                for project in projects:
                    print(f"  项目 {project.get('project_id')}: {project.get('status')} (PID: {project.get('process_id')})")
            else:
                print(f"✗ 项目列表查询失败: {result.get('error')}")
        else:
            print(f"✗ 项目列表查询请求失败: HTTP {list_response.status_code}")
        
        print()
        
        # 5. 停止实盘项目
        print("5️⃣ 停止实盘项目...")
        stop_response = requests.post(
            f"{STRATEGY_SERVER_URL}/api/strategy/live/stop/{project_id}",
            timeout=30
        )
        
        if stop_response.status_code == 200:
            result = stop_response.json()
            if result.get('success'):
                print(f"✓ 停止成功: {result.get('message')}")
                print(f"  项目ID: {result.get('project_id')}")
                print(f"  进程已停止: {result.get('process_stopped')}")
                print(f"  数据已退订: {result.get('data_unsubscribed')}")
                print(f"  合约列表变化: {result.get('contracts_changed')}")
            else:
                print(f"✗ 停止失败: {result.get('error')}")
        else:
            print(f"✗ 停止请求失败: HTTP {stop_response.status_code}")
        
        print()
        
        # 6. 再次查询状态确认已停止
        print("6️⃣ 确认项目已停止...")
        time.sleep(2)
        
        final_status_response = requests.get(
            f"{STRATEGY_SERVER_URL}/api/strategy/live/status/{project_id}",
            timeout=10
        )
        
        if final_status_response.status_code == 200:
            result = final_status_response.json()
            if result.get('success'):
                print(f"✓ 最终状态:")
                print(f"  运行状态: {result.get('is_running')}")
                print(f"  状态: {result.get('status')}")
            else:
                print(f"✗ 最终状态查询失败: {result.get('error')}")
        
        print("\n🎉 实盘项目生命周期测试完成！")
        return True
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到策略服务器，请确保 strategy_server.py 正在运行")
        return False
    except requests.exceptions.Timeout:
        print("✗ 请求超时")
        return False
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        return False

def test_data_engine_integration():
    """测试数据引擎集成"""
    
    print("\n=== 数据引擎集成测试 ===\n")
    
    try:
        # 查询数据引擎状态
        print("1️⃣ 查询数据引擎状态...")
        response = requests.get(f"{STRATEGY_SERVER_URL}/data_engine/status", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"✓ 数据引擎状态:")
                print(f"  引擎运行: {data.get('engine_running')}")
                print(f"  项目数量: {data.get('total_projects')}")
                print(f"  活跃合约: {data.get('total_contracts')}")
                print(f"  订阅合约: {data.get('subscribed_contracts')}")
                print(f"  引用计数: {data.get('contract_ref_counts')}")
            else:
                print(f"✗ 数据引擎状态查询失败: {result.get('error')}")
        else:
            print(f"✗ 数据引擎状态查询请求失败: HTTP {response.status_code}")
        
        print("\n🎯 数据引擎集成测试完成！")
        
    except Exception as e:
        print(f"✗ 数据引擎集成测试发生错误: {e}")

if __name__ == "__main__":
    print("实盘项目生命周期测试工具\n")
    
    # 运行完整生命周期测试
    success = test_live_project_lifecycle()
    
    # 测试数据引擎集成
    test_data_engine_integration()
    
    if success:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 测试失败，请检查日志")
