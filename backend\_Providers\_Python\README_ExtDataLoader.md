# Wonder Trader 统一ExtDataLoader

## 概述

这是一个统一的Wonder Trader扩展数据加载器，支持从本地数据源加载历史数据，包括：

- **股票/ETF**: 通过通达信数据
- **期货**: 通过通达信数据  
- **虚拟货币**: 通过本地CSV文件
- **美股**: 通过通达信数据

## 文件结构

```
backend/_Providers/_Python/
├── unified_ext_data_loader.py    # 主要的ExtDataLoader实现
├── example_with_ext_loader.py    # 使用示例
├── config_template.json          # 配置文件模板
└── README_ExtDataLoader.md       # 本文档
```

## 核心特性

### 1. 统一的数据接口
- 支持多种金融品种类型
- 统一的代码格式解析
- 自动识别数据源类型

### 2. 灵活的配置
- JSON配置文件支持
- 可配置数据路径
- 支持多时区

### 3. 高性能
- 直接从原始数据源读取
- 避免中间文件转换
- 内存优化

## 支持的品种格式

### 传统金融品种
- **股票**: `SSE.600000`, `SZSE.000001`
- **ETF**: `SSE.ETF.510300`, `SZSE.ETF.159919`
- **期货**: `SHFE.fu.HOT`, `CFFEX.IF.2312`
- **美股**: `NASDAQ.AAPL`, `NYSE.TSLA`

### 虚拟货币
- **格式**: `CRYPTO.CPT.{SYMBOL}`
- **示例**: `CRYPTO.CPT.BTC-USDT`, `CRYPTO.CPT.ETH-USDT`

## 配置说明

### 1. 创建配置文件

复制 `config_template.json` 为 `config.json`:

```bash
cp config_template.json config.json
```

### 2. 修改数据路径

编辑 `config.json`，设置正确的数据路径：

```json
{
  "tdx_data": {
    "path": "/path/to/your/tdx_data"
  },
  "crypto_data": {
    "path": "/path/to/your/crypto_data"
  }
}
```

## 使用方法

### 1. 在回测中使用

```python
from wtpy import WtBtEngine, EngineType
from unified_ext_data_loader import get_unified_ext_data_loader

# 创建回测引擎
engine = WtBtEngine(EngineType.ET_CTA)

# 注册扩展数据加载器
loader = get_unified_ext_data_loader()
engine.set_extended_data_loader(loader=loader, bAutoTrans=False)

# 初始化引擎
engine.init('../common/', "configbt.yaml")

# 配置回测
engine.configBacktest(202301010930, 202312311500)
engine.configBTStorage(mode="csv", path="../storage/")
engine.commitBTConfig()

# 添加策略
strategy = YourStrategy(name='test', codes=['SSE.600000', 'CRYPTO.CPT.BTC-USDT'])
engine.set_cta_strategy(strategy)

# 运行回测
engine.run_backtest()
engine.release_backtest()
```

### 2. 在实盘中使用

```python
from wtpy import WtEngine, EngineType
from unified_ext_data_loader import get_unified_ext_data_loader

# 创建实盘引擎
engine = WtEngine(EngineType.ET_CTA)

# 注册扩展数据加载器
loader = get_unified_ext_data_loader()
engine.set_extended_data_loader(loader)

# 初始化引擎
engine.init('../common/', "config.yaml")

# 添加策略
strategy = YourStrategy(name='live', codes=['SSE.600000'])
engine.add_cta_strategy(strategy)

# 运行实盘
engine.run()
```

## 数据流程

### 1. 数据请求流程
```
Wonder Trader策略 → ExtDataLoader → 本地数据源 → 返回K线数据
```

### 2. 数据转换流程
```
原始数据格式 → 标准化处理 → WTSBarStruct → Wonder Trader引擎
```

## 优势

### 1. 相比默认数据机制
- **灵活性**: 不依赖Wonder Trader的数据格式
- **实时性**: 可以实时获取最新数据
- **统一性**: 回测和实盘使用相同数据源

### 2. 相比CSV转换方式
- **效率**: 避免中间文件生成
- **内存**: 减少磁盘I/O操作
- **维护**: 减少文件管理复杂度

## 扩展说明

### 1. 添加新的数据源

在 `UnifiedExtDataLoader` 类中添加新的加载方法：

```python
def _load_new_data_source(self, code_info: Dict[str, str], period: str) -> List[Dict]:
    # 实现新数据源的加载逻辑
    pass
```

### 2. 支持新的品种类型

在 `_parse_wt_code` 方法中添加新的解析逻辑：

```python
def _parse_wt_code(self, stdCode: str) -> Dict[str, str]:
    # 添加新的代码格式解析
    pass
```

## 注意事项

1. **配置文件**: 确保 `config.json` 文件存在且路径正确
2. **数据格式**: 确保原始数据文件格式正确
3. **内存使用**: 大量数据加载时注意内存使用
4. **错误处理**: 检查日志输出，及时处理错误

## 测试

运行测试示例：

```bash
cd backend/_Providers/_Python/
python example_with_ext_loader.py
```

## 故障排除

### 1. 数据加载失败
- 检查配置文件路径
- 确认数据文件存在
- 查看错误日志

### 2. 性能问题
- 调整缓存配置
- 限制单次加载数据量
- 检查磁盘I/O性能

### 3. 内存不足
- 减少 `max_bars_per_load` 配置
- 增加系统内存
- 优化数据处理逻辑
