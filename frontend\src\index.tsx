import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import { ProConfigProvider } from '@ant-design/pro-components';
import zhCN from 'antd/locale/zh_CN';
//import 'antd/dist/antd.less';
import App from './App';
import './global.less';
import './index.less';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  //<React.StrictMode>
    <ProConfigProvider>
      <ConfigProvider locale={zhCN}>
          <App />
      </ConfigProvider>
    </ProConfigProvider>
  //</React.StrictMode>
); 