import React, { useEffect, useState, useRef } from 'react';
import { Card, Input, Button, Upload, Badge, message, Tooltip, Popconfirm, Image, Popover, Avatar, Row, Col, Typography, Divider, Space } from 'antd';
import { SendOutlined, PaperClipOutlined, DeleteOutlined, SearchOutlined, SmileOutlined, UserOutlined, FileOutlined, DownloadOutlined } from '@ant-design/icons';
import { EventBus } from '@/events/eventBus';
import { ChatEvents } from '@/events/events';
import { getUserInfo, UserInfo, getToken } from '@/utils/auth';
import { formatFileSize } from '@/utils/format';
import { useUser } from '@/models/useUser';
import { useAtom } from 'jotai';
import { themeAtom } from '@/models/useTheme';
import { chatVisibleAtom } from '@/store/state';
import './ChatWindow.less';

const { Search } = Input;
const { Text } = Typography;

const ChatWindow: React.FC = () => {
  const [currentTheme] = useAtom(themeAtom);
  const [chatVisible] = useAtom(chatVisibleAtom);
  const [messages, setMessages] = useState<ChatEvents.Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { userInfo } = useUser();
  const [unreadCount, setUnreadCount] = useState(0);
  const [searchVisible, setSearchVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState<ChatEvents.Message[]>([]);
  const [emojiVisible, setEmojiVisible] = useState(false);
  const messagesRef = useRef<ChatEvents.Message[]>([]);

  // 使用 ref 来保存消息，确保在组件隐藏时不丢失
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  useEffect(() => {
    if (chatVisible) {
      // 如果显示时消息为空，但 ref 中有消息，则恢复消息
      if (messages.length === 0 && messagesRef.current.length > 0) {
        setMessages(messagesRef.current);
      }
      // 显示时滚动到底部
      setTimeout(scrollToBottom, 100);
    }
  }, [chatVisible]);

  // 根据主题和用户获取气泡样式
  const getBubbleStyle = (isCurrentUser: boolean) => {
    const isDarkTheme = currentTheme.startsWith('dark');
    if (isCurrentUser) {
      return {
        backgroundColor: isDarkTheme ? '#2c5699' : '#95ec69',
        color: isDarkTheme ? '#fff' : '#000',
        borderRadius: '6px 2px 6px 6px',
        position: 'relative' as const,
        boxShadow: isDarkTheme ? '0 3px 6px rgba(0,0,0,0.4)' : '0 2px 4px rgba(0,0,0,0.1)'
      };
    }
    return {
      backgroundColor: isDarkTheme ? '#2d2d2d' : '#fff',
      color: isDarkTheme ? '#fff' : '#000',
      borderRadius: '2px 6px 6px 6px',
      position: 'relative' as const,
      boxShadow: isDarkTheme ? '0 3px 6px rgba(0,0,0,0.4)' : '0 2px 4px rgba(0,0,0,0.1)'
    };
  };

  // 强制重新渲染以更新气泡颜色
  const [, forceUpdate] = useState({});
  useEffect(() => {
    forceUpdate({});
  }, [currentTheme]);

  const prevUsernameRef = useRef<string | null>(null);
  const currentUsername = userInfo?.username || null;

  useEffect(() => {
    console.log('ChatWindow装载完成');
    console.log('ChatWindow mounted, userInfo:', getToken());
    
    const handleHistory = (history: ChatEvents.Message[]) => {
      setMessages(history);
      setTimeout(scrollToBottom, 500);
    };

    const handleNewMessage = (data: ChatEvents.Message) => {
      setMessages(prev => [...prev, data]);
      setTimeout(scrollToBottom, 500);
    };

    const handleRecall = (messageId: number) => {
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, recalled: true } : msg
      ));
    };

    const handleSearchResults = (results: ChatEvents.Message[]) => {
      setSearchResults(results);
    };

    const subscriptions = [
      EventBus.on(ChatEvents.Types.CHAT_HISTORY_RECEIVED, handleHistory),
      EventBus.on(ChatEvents.Types.RECEIVE_MESSAGE, handleNewMessage),
      EventBus.on(ChatEvents.Types.MESSAGE_RECALLED, handleRecall),
      EventBus.on(ChatEvents.Types.SEARCH_RESULT, handleSearchResults)
    ];

    prevUsernameRef.current = currentUsername;

    return () => {
      subscriptions.forEach(subscription => subscription.unsubscribe());
    };
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSend = () => {
    if (!inputText.trim() || !userInfo) return;

    const message: ChatEvents.Message = {
      id: Date.now(), // 临时ID
      content: inputText.trim(),
      type: 'text',
      userId: userInfo.id,
      username: userInfo.username,
      avatar: userInfo.avatar,
      timestamp: new Date().toISOString(),
      status: 'sending'
    };

    EventBus.emit(ChatEvents.Types.SEND_MESSAGE, message);
    setInputText('');
  };

  const handleFileUpload = (file: File) => {
    if (!userInfo) return false;
    EventBus.emit(ChatEvents.Types.UPLOAD_FILE, file);
    return false;
  };

  const shouldShowTime = (currentMsg: ChatEvents.Message, prevMsg?: ChatEvents.Message): boolean => {
    if (!prevMsg) return true;
    
    const currentTime = new Date(currentMsg.timestamp || Date.now()).getTime();
    const prevTime = new Date(prevMsg.timestamp || Date.now()).getTime();
    
    return currentTime - prevTime > 300000;
  };

  const handleEmojiSelect = (emoji: string) => {
    setInputText(prev => prev + emoji);
    setEmojiVisible(false);
  };

  const emojiContent = (
    <div className="emoji-picker">
      {['😀', '😂', '🤣', '😊', '😍', '🤔', '😴', '😭', '😱', '😡'].map(emoji => (
        <span 
          key={emoji} 
          onClick={() => handleEmojiSelect(emoji)}
          className="emoji-item"
        >
          {emoji}
        </span>
      ))}
    </div>
  );

  useEffect(() => {
    const handleFileUploaded = (fileMessage: ChatEvents.FileUploaded) => {
      const message: ChatEvents.Message = {
        id: Date.now(),
        content: fileMessage.name,
        type: 'file',
        userId: userInfo?.id || 0,
        username: userInfo?.username,
        avatar: userInfo?.avatar,
        timestamp: new Date().toISOString(),
        status: 'sending',
        fileUrl: fileMessage.url,
        fileName: fileMessage.name,
        fileSize: fileMessage.fileSize
      };

      EventBus.emit(ChatEvents.Types.SEND_MESSAGE, message);
    };

    const fileUploadedSubscription = EventBus.on(ChatEvents.Types.FILE_UPLOADED, handleFileUploaded);

    return () => {
      fileUploadedSubscription.unsubscribe();
    };
  }, [userInfo]);

  // 添加文件类型判断函数
  const isImageFile = (fileName: string) => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    return imageExtensions.some(ext => 
      fileName.toLowerCase().endsWith(ext)
    );
  };

  return (
    <div className="chat-window" ref={chatContainerRef} onClick={() => setUnreadCount(0)}>
      <Badge count={unreadCount}>
        <Card 
          title={
            <div className="chat-header">
              <span>聊天窗口</span>
              <Button 
                type="text" 
                icon={<SearchOutlined />} 
                onClick={() => setSearchVisible(!searchVisible)}
              />
            </div>
          }
          extra={
            <Button 
              type="text" 
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? '展开' : '收起'}
            </Button>
          }
          style={{ 
            width: 320,
            position: 'fixed', 
            right: 20, 
            bottom: 20,
            height: isCollapsed ? 'auto' : 500,
            zIndex: 9999
          }}
          styles={{ 
            body: { 
              padding: isCollapsed ? 0 : '12px',
              display: isCollapsed ? 'none' : 'block'
            }
          }}
        >
          {searchVisible && (
            <div className="chat-search">
              <Search
                placeholder="搜索消息..."
                value={searchText}
                onChange={e => setSearchText(e.target.value)}
                onSearch={value => EventBus.emit(ChatEvents.Types.SEARCH_MESSAGES, value)}
              />
              {searchResults.length > 0 && (
                <div className="search-results">
                  {searchResults.map((msg, index) => (
                    <div key={index} className="search-result-item">
                      {msg.content}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
          <div className="chat-messages" style={{ 
            height: 380, 
            overflowY: 'auto',
            paddingBottom: '16px',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}>
            <style>
              {`
                .chat-messages::-webkit-scrollbar {
                  display: none;
                }
              `}
            </style>
            <Row gutter={[0, 16]}>
              {messages.map((msg, index) => (
                <Col span={24} key={msg.id || index}>
                  <React.Fragment>
                    {shouldShowTime(msg, messages[index - 1]) && (
                      <Divider style={{ borderColor: currentTheme.indexOf('dark') >= 0 ? '#1f1f1f' : '#f0f0f000' }} plain>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          {msg.timestamp ? new Date(msg.timestamp).toLocaleTimeString() : ''}
                        </Text>
                      </Divider>
                    )}
                    
                    <Row wrap={false} style={{ gap: '16px 8px' }}>
                      <Col flex="28px">
                        {(() => {
                          const isOtherUser = msg.userId !== userInfo?.id;
                          return isOtherUser && (
                            <Avatar 
                              className="avatar-left" 
                              icon={<UserOutlined />} 
                              src={msg.avatar ? `/uploads/avatars/${msg.avatar}` : undefined}
                              size={24} 
                            />
                          );
                        })()}
                      </Col>
                      
                      <Col flex="auto">
                        <div style={{ 
                          display: 'flex', 
                          justifyContent: msg.userId === userInfo?.id ? 'flex-end' : 'flex-start'
                        }}>
                          <Card 
                            className="message-bubble"
                            size="small"
                            bordered={false}
                            styles={{
                              body: { padding: '6px 12px' }
                            }}
                            style={{
                              ...getBubbleStyle(msg.userId === userInfo?.id),
                              maxWidth: '80%',
                              display: 'inline-block'
                            }}
                          >
                            {msg.type === 'file' ? (
                              <Space direction="vertical" size="small">
                                {isImageFile(msg.fileName || '') ? (
                                  <div className="image-preview">
                                    <Image
                                      src={msg.fileUrl}
                                      alt={msg.fileName}
                                      style={{ 
                                        maxWidth: '200px',
                                        maxHeight: '150px',
                                        objectFit: 'cover',
                                        borderRadius: '4px',
                                        cursor: 'pointer'
                                      }}
                                      preview={{
                                        mask: '查看大图'
                                      }}
                                    />
                                  </div>
                                ) : (
                                  <>
                                    <Typography.Text strong style={{ color: 'inherit' }}>{msg.fileName}</Typography.Text>
                                    <Space>
                                      <FileOutlined style={{ color: 'inherit' }} />
                                      <Typography.Text type="secondary" style={{ color: currentTheme.indexOf('dark') >= 0 ? 'rgba(255,255,255,0.65)' : undefined }}>
                                        {formatFileSize(msg.fileSize || 0)}
                                      </Typography.Text>
                                      {msg.fileUrl && (
                                        <Button 
                                          type="link" 
                                          size="small"
                                          icon={<DownloadOutlined />}
                                          onClick={() => window.open(msg.fileUrl)}
                                          style={{ color: currentTheme.indexOf('dark') >= 0 ? '#fff' : undefined }}
                                        >
                                          Download
                                        </Button>
                                      )}
                                    </Space>
                                  </>
                                )}
                              </Space>
                            ) : (
                              <Typography.Text style={{ 
                                lineHeight: '20px',
                                color: 'inherit'
                              }}>
                                {msg.content}
                              </Typography.Text>
                            )}
                          </Card>
                        </div>
                      </Col>
                      
                      <Col flex="28px">
                        {msg.userId === userInfo?.id && (
                          <Avatar 
                            className="avatar-right" 
                            icon={<UserOutlined />} 
                            src={userInfo?.avatar ? `/uploads/avatars/${userInfo.avatar}` : undefined}
                            size={24} 
                          />
                        )}
                      </Col>
                    </Row>
                  </React.Fragment>
                </Col>
              ))}
            </Row>
            <div ref={messagesEndRef} style={{ marginBottom: '8px' }} />
          </div>
          <div className="chat-input" style={{ marginTop: '12px' }}>
            <Space.Compact block>
              <Popover
                content={emojiContent}
                trigger="click"
                open={emojiVisible}
                onOpenChange={setEmojiVisible}
                placement="topLeft"
              >
                <Button icon={<SmileOutlined />} />
              </Popover>
              <Input
                value={inputText}
                onChange={e => setInputText(e.target.value)}
                onPressEnter={handleSend}
                style={{ width: 'calc(100% - 108px)' }}
                placeholder="输入消息..."
              />
              <Upload
                beforeUpload={handleFileUpload}
                showUploadList={false}
              >
                <Button icon={<PaperClipOutlined />} />
              </Upload>
              <Button 
                type="primary" 
                icon={<SendOutlined />} 
                onClick={handleSend}
              />
            </Space.Compact>
          </div>
        </Card>
      </Badge>
    </div>
  );
};

export default ChatWindow;