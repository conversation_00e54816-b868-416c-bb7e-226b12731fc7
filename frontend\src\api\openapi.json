{"openapi": "3.0.0", "info": {"title": "QuantQuart API", "version": "1.0.0", "description": "API documentation for QuantQuart frontend"}, "servers": [{"url": "/api", "description": "Main API server"}], "paths": {"/auth/login": {"post": {"summary": "User login", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}}}, "/auth/register": {"post": {"summary": "User registration", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"201": {"description": "Registration successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}}}}}}}, "/dashboard": {"get": {"summary": "Get dashboard data", "tags": ["Dashboard"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Dashboard data retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardResponse"}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer"}}, "schemas": {"LoginRequest": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"]}, "LoginResponse": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}, "RegisterRequest": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "email": {"type": "string"}}, "required": ["username", "password", "email"]}, "RegisterResponse": {"type": "object", "properties": {"id": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}}}, "DashboardResponse": {"type": "object", "properties": {"marketData": {"$ref": "#/components/schemas/MarketData"}, "userInfo": {"$ref": "#/components/schemas/User"}}}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "avatar": {"type": "string"}}}, "MarketData": {"type": "object", "properties": {"symbol": {"type": "string"}, "price": {"type": "number"}, "change": {"type": "number"}}}}}}