const express = require('express');
const router = express.Router();
const tradeHandler = require('./index'); // 用于访问 tradingClients

/**
 * 交易API：通过指定通道ID下发交易指令到已登记的客户端
 * POST /api/trade
 * body: { channelId: string, action: string, params: object }
 */
router.post('/', async (req, res) => {
    try {
        const { channelId, action, params } = req.body;
        if (!channelId || !action || !params) {
            return res.status(400).json({ success: false, error: '缺少必要参数' });
        }
        // 获取已登记的客户端
        const clientInfo = tradeHandler.tradingClients.get(channelId);
        if (!clientInfo || !clientInfo.socket || !clientInfo.socket.connected) {
            return res.status(404).json({ success: false, error: '指定通道未连接或不可用' });
        }
        // 通过socket发送交易指令
        clientInfo.socket.emit('trade', { action, params });
        // 这里可根据业务需求等待回执或直接返回
        return res.json({ success: true, message: '已下发交易指令' });
    } catch (err) {
        return res.status(500).json({ success: false, error: err.message });
    }
});

module.exports = router; 