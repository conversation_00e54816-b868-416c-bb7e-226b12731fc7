import { EventBus } from '../events/eventBus';
import { ChatEvents, UserEvents } from '../events/events';
import { getToken, isLoggedIn } from './auth';

class WebSocketManager {
  private ws: WebSocket | null = null;
  private sessionId: number | null = null;
  private reconnectAttempts = 0;
  private readonly MAX_RECONNECT_ATTEMPTS = 5;
  private readonly RECONNECT_INTERVAL = 3000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private readonly HEARTBEAT_INTERVAL = 30000; // 30秒发送一次心跳
  private messageQueue: { type: string; data: any; sessionId?: number }[] = []; // 修正消息队列类型

  constructor() {
    // 监听登出事件
    EventBus.on(UserEvents.Types.LOGOUT, () => {
      console.log('[WebSocket] User logged out, disconnecting...');
      this.disconnect();
    });
  }

  public connect(sessionId: number) {
    // 检查登录状态
    if (!isLoggedIn()) {
      console.log('[WebSocket] User not logged in, cannot establish connection');
      return;
    }

    console.log('[前端WebSocket] Connecting...');

    if (this.sessionId !== sessionId || this.ws?.readyState !== WebSocket.OPEN) {
      console.log('[前端WebSocket] Switching to new session:', sessionId);
      
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
      }
      this.reconnectAttempts = 0;
      
      this.sessionId = sessionId;
      this.establishConnection();
    } else {
      console.log('[前端WebSocket] Already connected to session:', sessionId);
    }
  }

  private setupHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    this.heartbeatInterval = setInterval(() => {
      console.log('[WebSocket] Sending heartbeat PING');
      this.sendHeartbeat();
    }, this.HEARTBEAT_INTERVAL);

    console.log('[WebSocket] Heartbeat interval set, state=', this.ws?.readyState);
  }

  private establishConnection() {
    if (this.ws) {
      this.ws.close();
    }

    // 检查登录状态
    if (!isLoggedIn()) {
      console.log('[WebSocket] User not logged in, cannot establish connection');
      return;
    }

    const token = getToken();
    if (!token) {
      console.error('[WebSocket] No token available');
      return;
    }

    const wsUrl = `/chat?sessionId=${this.sessionId}`;
    
    console.log('[WebSocket] Attempting to connect with:', {
      url: wsUrl,
      sessionId: this.sessionId,
      hasToken: !!token
    });

    try {
      this.ws = new WebSocket(wsUrl, token);

      this.ws.onopen = () => {
        console.log('[WebSocket] Connected successfully');
        this.reconnectAttempts = 0;
        this.setupHeartbeat();
        
        // 连接成功后发送所有排队的消息
        this.processMessageQueue();
      };

      this.ws.onmessage = (event) => {
        try {
          console.log('[前端WebSocket] Received raw message');
          const data = JSON.parse(event.data);
          if (data.type === 'history') {
            EventBus.emit(ChatEvents.Types.CHAT_HISTORY_RECEIVED, data.messages);
          } else if (data.type === 'message') {
            EventBus.emit(ChatEvents.Types.RECEIVE_MESSAGE, data.message);
          }
        } catch (error) {
          console.error('[前端WebSocket] Error processing message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('[WebSocket] Disconnected with code:', event.code, 'reason:', event.reason);
        this.handleReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('[前端WebSocket] Error details:', {
          error,
          readyState: this.ws?.readyState,
          bufferedAmount: this.ws?.bufferedAmount
        });
      };

    } catch (error) {
      console.error('[前端WebSocket] Connection error:', error);
    }
  }

  private processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const pendingMessage = this.messageQueue.shift();
      if (pendingMessage && this.ws?.readyState === WebSocket.OPEN) {
        const { sessionId, ...messageData } = pendingMessage;
        const formattedMessage = {
          type: messageData.type,
          data: messageData.data
        };
        console.log('[WebSocket] Sending queued message:', formattedMessage);
        this.ws.send(JSON.stringify(formattedMessage));
      }
    }
  }

  private handleReconnect() {
    console.log('[WebSocket] handleReconnect');
    if (this.reconnectAttempts < this.MAX_RECONNECT_ATTEMPTS) {
      this.reconnectAttempts++;
      console.log(`[WebSocket] Attempting to reconnect (${this.reconnectAttempts}/${this.MAX_RECONNECT_ATTEMPTS})`);
      setTimeout(() => this.establishConnection(), this.RECONNECT_INTERVAL);
    } else {
      console.error('[前端WebSocket] Max reconnection attempts reached');
    }
  }

  public sendMessage(message: { type: string; data: any; sessionId?: number }) {
    console.log('[WebSocket] Sending message:', message);

    // 如果消息中包含sessionId，且与当前sessionId不同，需要切换连接
    if (message.sessionId && message.sessionId !== this.sessionId) {
      this.connect(message.sessionId);
      this.messageQueue.push(message);
      return;
    }

    if (this.ws?.readyState === WebSocket.OPEN) {
      const { sessionId, ...messageData } = message;
      const formattedMessage = {
        type: messageData.type,
        data: messageData.data
      };
      
      console.log('[WebSocket] Sending formatted message:', formattedMessage);
      this.ws.send(JSON.stringify(formattedMessage));
    } else {
      console.log('[WebSocket] Connection not ready, queueing message');
      this.messageQueue.push(message);
      
      if (this.ws?.readyState !== WebSocket.CONNECTING) {
        console.log('[WebSocket] Attempting to reconnect...');
        this.reconnectAttempts = 0;
        this.establishConnection();
      } else {
        console.log('[WebSocket] Connection is already being established');
      }
    }
  }

  private sendHeartbeat() {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const heartbeat = {
        type: 'ping',
        isHeartbeat: true
      };
      this.ws.send(JSON.stringify(heartbeat));
    }
  }

  public disconnect() {
    console.log('[WebSocket] Disconnecting...');
    
    // 清空消息队列
    this.messageQueue = [];

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.sessionId = null;
    this.reconnectAttempts = 0;
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }
}

export const wsManager = new WebSocketManager(); 