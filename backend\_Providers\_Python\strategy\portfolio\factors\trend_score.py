import numpy as np
from typing import Optional

def calculate_value(opens: Optional[np.ndarray] = None,
                    highs: Optional[np.ndarray] = None,
                    lows: Optional[np.ndarray] = None,
                    closes: Optional[np.ndarray] = None,
                    volumes: Optional[np.ndarray] = None,
                    **kwargs) -> Optional[float]:
    """
    计算趋势评分 (Trend Score = Annualized Returns * R-squared) 在最后一个时间点的数值。
    符合 '正常计算模式的优化效能因子框架.md' 规范。

    Args:
        opens/highs/lows/closes/volumes (Optional[np.ndarray]): 价格/成交量序列。
        **kwargs:
            period (int): 计算窗口大小 (默认 25).
            column (str): 指定使用哪个序列 (e.g., 'close').

    Returns:
        Optional[float]: 计算出的最后一个时间点的 Trend Score 值，或 np.nan。
    """
    period = kwargs.get('period', 25)
    column = kwargs.get('column', 'close')

    # --- Input Selection ---
    input_array = None
    is_price_col = False
    if column == 'close' and closes is not None:
        input_array = closes
        is_price_col = True
    elif column == 'open' and opens is not None:
        input_array = opens
        is_price_col = True
    elif column == 'high' and highs is not None:
        input_array = highs
        is_price_col = True
    elif column == 'low' and lows is not None:
        input_array = lows
        is_price_col = True
    elif column == 'volume' and volumes is not None:
        input_array = volumes
    else:
        # print(f"[因子计算:TrendScore] 输入错误: 列 '{column}' 无效或对应数据为 None。")
        return np.nan
    # --- End Input Selection ---

    # --- Input Validation ---
    if not isinstance(period, int) or period < 2:
        # print(f"[因子计算:TrendScore] 参数错误: period ({period}) 必须是 >= 2 的整数。")
        return np.nan
    if len(input_array) < period:
        # print(f"[因子计算:TrendScore] 数据不足: 需要至少 {period} 条数据，实际只有 {len(input_array)} 条。")
        return np.nan
    # --- End Validation ---

    # Select the last 'period' data points
    y_window = input_array[-period:].astype(np.float64) # Ensure float64

    # Check for NaNs/Infs in the window
    if np.isnan(y_window).any() or np.isinf(y_window).any():
        # print(f"[因子计算:TrendScore] 输入窗口包含 NaN 或 Inf。")
        return np.nan
        
    # --- Calculation Logic (adapted from _calculate_single_series_trend_score) ---
    try:
        # Use log for price data stability
        if is_price_col:
            with np.errstate(divide='ignore', invalid='ignore'):
                y = np.log(y_window)
            # Check again after log transform
            if np.isnan(y).any() or np.isinf(y).any():
                # print(f"[因子计算:TrendScore] 对数转换后窗口包含 NaN 或 Inf。")
                return np.nan
        else:
            y = y_window # Use original data if not price
            
        x = np.arange(period, dtype=float)
        n = float(period) # Use float for calculations
        
        sum_x = np.sum(x)
        sum_y = np.sum(y)
        sum_xy = np.sum(x * y)
        sum_x2 = np.sum(x * x)
        sum_y2 = np.sum(y * y)

        denominator = n * sum_x2 - sum_x * sum_x
        if np.isclose(denominator, 0):
            # print(f"[因子计算:TrendScore] 回归分母接近零。")
            return 0.0 # Or np.nan? Returning 0 R-squared -> 0 score

        # Calculate slope and intercept
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        # intercept = (sum_y - slope * sum_x) / n # Intercept not needed for R^2 or return

        # Calculate R-squared
        ss_tot_term = sum_y * sum_y / n
        ss_tot = sum_y2 - ss_tot_term
        ss_err_term1 = sum_y2
        ss_err_term2 = -2 * slope * sum_xy
        # Correct term from y = mx+c -> need intercept if used directly 
        # Simplified way: ss_err = ss_tot - ss_reg where ss_reg = slope * (sum_xy - sum_x * sum_y / n)
        ss_reg = slope * (sum_xy - sum_x * sum_y / n)
        ss_err = ss_tot - ss_reg # Sum of squares of residuals
        
        r_squared = 0.0
        if not np.isclose(ss_tot, 0):
             with np.errstate(invalid='ignore'): # Handle potential 0/0 or neg/neg
                r_squared = 1.0 - (ss_err / ss_tot)
        
        r_squared = np.clip(r_squared, 0.0, 1.0) # Ensure R^2 is between 0 and 1

        # Calculate Annualized Returns
        if is_price_col:
            # Assuming daily data for 250 trading days
            annualized_returns = np.exp(slope * 250.0) - 1.0 
        else:
            annualized_returns = slope # Or define behavior for non-price data
            
        # Calculate Trend Score
        trend_score = annualized_returns * r_squared

        # Ensure returning float or np.nan
        return float(trend_score) if not np.isnan(trend_score) else np.nan

    except Exception as e:
        # print(f"[因子计算:TrendScore] 计算时出错 ({column}, {period}): {e}")
        return np.nan # Calculation error returns NaN


# --- Vectorized function removed/commented out as per new spec ---
# def _calculate_single_series_trend_score(...)
# def calculate_series_vectorized(...):
#     ...

# --- 旧函数已移除 ---
# def calculate_series(...) -> ...:
#     pass
# def calculate_value(...) -> ...:
#     pass 