#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths打印客户端

该程序接收同花顺交易指令并打印到控制台，不执行任何实际交易操作。
用于调试和监控交易指令的发送情况。
"""

import os
import time
import json
import logging
import argparse
import threading
import datetime
import random
import requests
from decimal import Decimal, ROUND_HALF_UP
from flask import Flask, request, jsonify
import socketio
import codecs
import sys


os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# 配置日志
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_file_handler = logging.FileHandler("easytrader_ths_client.log", encoding='utf-8')
log_file_handler.setFormatter(log_formatter)
log_stream_handler = logging.StreamHandler()
log_stream_handler.setFormatter(log_formatter)

logger = logging.getLogger("EasyTrader-THS-MockClient")
logger.setLevel(logging.INFO)
logger.addHandler(log_file_handler)
logger.addHandler(log_stream_handler)

# 全局变量
app = Flask(__name__)
socket_connected = False

# 创建Socket.IO客户端
sio = socketio.Client()

# 新增全局变量
last_heartbeat_success = True
heartbeat_event = threading.Event()

config = {
    "username": "",
    "backend_url": "",  # 后端服务URL
    "local_port": 8889,  # 默认使用8889端口，避免与真实客户端冲突
    "socket_port": None,  # Socket.IO端口，从后端获取
    "heartbeat_interval": 30,  # 心跳间隔（秒）
    "reconnect_attempts": 3,  # 重连尝试次数
    "reconnect_delay": 5,  # 重连延迟（秒）
    "connection_timeout": 10,  # 连接超时（秒）
    "initial_balance": 1000000.0,  # 初始资金100万
    "commission_rate": 0.0003,  # 佣金费率0.03%
    "slippage": 0.002,  # 滑点0.2%
    "price_limit": 0.1,  # 涨跌幅限制10%
    "min_trade_volume": 100,  # 最小交易量100股
    "trade_volume_step": 100,  # 交易量步长100股
}

# 认证token只在内存中保存，不写入配置文件
auth_token = ""

# Socket.IO事件处理
@sio.on('connect', namespace='/trade')
def connect_trade():
    """Socket.IO /trade 命名空间连接成功事件"""
    global socket_connected
    logger.info("Socket.IO /trade 命名空间连接成功")
    socket_connected = True

    # 连接成功后立即注册
    register_via_socketio()

@sio.event
def connect_error(error):
    """Socket.IO默认命名空间连接错误事件"""
    logger.error(f"Socket.IO默认命名空间连接错误: {error}")

@sio.on('connect_error', namespace='/trade')
def connect_error_trade(error):
    """Socket.IO /trade 命名空间连接错误事件"""
    global socket_connected
    logger.error(f"Socket.IO /trade 命名空间连接错误: {error}")
    socket_connected = False

@sio.event
def disconnect():
    """Socket.IO默认命名空间断开连接事件"""
    logger.info("Socket.IO默认命名空间断开连接")

@sio.on('disconnect', namespace='/trade')
def disconnect_trade():
    """Socket.IO /trade 命名空间断开连接事件"""
    global socket_connected, last_heartbeat_success
    logger.info("Socket.IO /trade 命名空间断开连接")
    socket_connected = False
    last_heartbeat_success = False

@sio.on('check_health', namespace='/trade')
def on_check_health(data):
    """接收健康检查请求"""
    logger.info(f"收到健康检查请求: {data}")
    try:
        # 发送健康检查响应
        sio.emit('health_response', {
            'success': True,
            'data': {
                'status': 'healthy',
                'timestamp': int(time.time()),
                'trader': {
                    'status': 'connected',
                    'last_update': int(time.time())
                }
            }
        }, namespace='/trade')
        logger.info("健康检查响应已发送")
    except Exception as e:
        logger.error(f"发送健康检查响应失败: {str(e)}")
        try:
            sio.emit('health_response', {
                'success': False,
                'error': f"发送健康检查响应失败: {str(e)}"
            }, namespace='/trade')
        except:
            pass

@sio.on('command', namespace='/trade')
def on_command(data):
    """接收后端发送的命令 - 仅打印，不执行"""
    print(f"\n=== [打印客户端] 收到指令 ===")
    print(f"完整指令数据: {data}")
    
    logger.info(f"[打印客户端] 收到后端命令: {data}")
    
    try:
        action = data.get('action')
        params = data.get('params', {})
        
        print(f"指令类型: {action}")
        print(f"指令参数: {params}")

        if action == 'health_check' or action == 'check_health':
            print("[打印客户端] 健康检查指令 - 仅打印，不执行")
            # 健康检查命令仍然需要响应
            logger.info("收到健康检查命令，立即响应")
            sio.emit('response', {
                'success': True,
                'action': 'health_check',
                'data': {
                    'status': 'healthy',
                    'timestamp': int(time.time()),
                    'trader': {
                        'status': 'connected',
                        'last_update': int(time.time())
                    }
                }
            }, namespace='/trade')

            # 同时发送特定的健康检查响应事件
            sio.emit('health_response', {
                'success': True,
                'data': {
                    'status': 'healthy',
                    'timestamp': int(time.time()),
                    'trader': {
                        'status': 'connected',
                        'last_update': int(time.time())
                    }
                }
            }, namespace='/trade')

            logger.info("健康检查响应已发送")
        elif action == 'buy':
            print(f"[打印客户端] 买入指令 - 仅打印，不执行")
            print(f"  证券代码: {params.get('code')}")
            print(f"  买入价格: {params.get('price')}")
            print(f"  买入数量: {params.get('amount')}")
            
            # 发送模拟成功响应
            sio.emit('response', {
                'success': True,
                'action': 'buy',
                'message': '[打印客户端] 买入指令已打印，未实际执行',
                'data': {
                    'entrust_no': f"PRINT_{int(time.time())}",
                    'note': '这是打印客户端，未实际执行交易'
                }
            }, namespace='/trade')
        elif action == 'sell':
            print(f"[打印客户端] 卖出指令 - 仅打印，不执行")
            print(f"  证券代码: {params.get('code')}")
            print(f"  卖出价格: {params.get('price')}")
            print(f"  卖出数量: {params.get('amount')}")
            
            # 发送模拟成功响应
            sio.emit('response', {
                'success': True,
                'action': 'sell',
                'message': '[打印客户端] 卖出指令已打印，未实际执行',
                'data': {
                    'entrust_no': f"PRINT_{int(time.time())}",
                    'note': '这是打印客户端，未实际执行交易'
                }
            }, namespace='/trade')
        else:
            print(f"[打印客户端] 未知指令: {action} - 仅打印")
            # 未知命令
            logger.warning(f"收到未知命令: {action}")
            sio.emit('response', {
                'success': True,
                'action': action,
                'message': f'[打印客户端] 未知指令已打印: {action}'
            }, namespace='/trade')
        
        print("=== 指令处理完成 ===\n")
        
    except Exception as e:
        print(f"[打印客户端] 处理指令时发生错误: {str(e)}")
        logger.error(f"处理命令时发生错误: {str(e)}")
        sio.emit('response', {
            'success': False,
            'action': data.get('action', 'unknown'),
            'message': f'处理命令时发生错误: {str(e)}'
        }, namespace='/trade')

@sio.on('heartbeat_response', namespace='/trade')
def on_heartbeat_response(data):
    global last_heartbeat_success
    last_heartbeat_success = True
    heartbeat_event.set()  # 通知主线程收到回复

# 模拟交易数据
mock_data = {
    "account": {
        "总资产": config["initial_balance"],
        "可用金额": config["initial_balance"],
        "冻结金额": 0.0,
        "股票市值": 0.0,
        "浮动盈亏": 0.0
    },
    "positions": [],  # 持仓列表
    "entrusts": [],   # 委托列表
    "trades": []      # 成交列表
}

# 模拟股票行情数据
mock_quotes = {
    "sh600000": {"name": "浦发银行", "price": 10.50, "high": 10.80, "low": 10.20, "open": 10.40, "close": 10.50, "volume": 1000000},
    "sh601398": {"name": "工商银行", "price": 5.20, "high": 5.30, "low": 5.10, "open": 5.15, "close": 5.20, "volume": 2000000},
    "sz000001": {"name": "平安银行", "price": 15.80, "high": 16.00, "low": 15.60, "open": 15.70, "close": 15.80, "volume": 1500000},
    "sz000002": {"name": "万科A", "price": 20.50, "high": 21.00, "low": 20.00, "open": 20.30, "close": 20.50, "volume": 800000}
}

def load_config():
    """加载配置文件"""
    global config
    config_path = "easytrader_ths_config.json"

    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
                config.update(loaded_config)
                logger.info(f"已加载配置文件: {config_path}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
    else:
        logger.warning(f"配置文件不存在: {config_path}，将使用默认配置")
        # 保存默认配置
        save_config()

def save_config():
    """保存配置到文件 - 只保存必要的配置项"""
    config_path = "easytrader_ths_mock_config.json"
    try:
        # 只保存需要持久化的配置项
        config_to_save = {
            "username": config.get("username", ""),
            "backend_url": config.get("backend_url", ""),
            "initial_balance": config.get("initial_balance", 1000000.0),
            "commission_rate": config.get("commission_rate", 0.0003)
        }

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_to_save, f, indent=4)
            logger.debug(f"已保存必要配置到文件: {config_path}")
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {str(e)}")
        return False

def login_to_backend():
    """登录到后端获取token"""
    global config, auth_token

    # 如果已有token且不为空，直接返回
    if auth_token:
        logger.info("已有认证token，无需重新登录")
        return True

    # 提示用户输入登录信息
    print("\n===== 登录到量化平台 =====")

    # 如果配置中没有后端URL，提示输入
    if not config.get("backend_url"):
        backend_url = input("请输入后端服务器地址 (例如: http://example.com:3000): ")
        config["backend_url"] = backend_url.strip()
    else:
        print(f"后端服务器地址: {config['backend_url']}")
        change = input("是否修改后端地址? (y/n): ").lower()
        if change == 'y':
            backend_url = input("请输入新的后端服务器地址: ")
            config["backend_url"] = backend_url.strip()

    # 如果配置中没有用户名，提示输入
    if not config.get("username"):
        username = input("请输入用户名: ")
        config["username"] = username.strip()
    else:
        print(f"用户名: {config['username']}")
        change = input("是否修改用户名? (y/n): ").lower()
        if change == 'y':
            username = input("请输入新的用户名: ")
            config["username"] = username.strip()

    # 提示输入密码
    password = input("请输入密码: ")  # 在模拟环境中，我们不使用getpass，以便于测试

    # 尝试登录
    try:
        # 构建登录API URL
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            login_url = f"{base_url}/api/user/login"
        else:
            # 否则直接添加 /api/user/login
            login_url = f"{backend_url}/api/user/login"

        if not login_url.startswith(("http://", "https://")):
            login_url = f"http://{login_url}"

        logger.info(f"尝试登录到: {login_url}")

        # 发送登录请求
        response = requests.post(
            login_url,
            json={
                "username": config["username"],
                "password": password
            },
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            logger.info(f"登录响应: {result}")

            # 检查响应格式
            if result.get("success"):
                # 检查 token 是在根级别还是在 data 对象中
                if result.get("token"):
                    auth_token = result["token"]
                    logger.info("登录成功，已获取认证token")
                    # 保存配置
                    save_config()
                    return True
                elif result.get("data") and result["data"].get("token"):
                    auth_token = result["data"]["token"]
                    logger.info("登录成功，已获取认证token")
                    # 保存配置
                    save_config()
                    return True
                else:
                    logger.error("登录响应中未找到token字段")
                    print("登录响应中未找到token字段")
            else:
                error_msg = result.get('error') or result.get('message') or '未知错误'
                logger.error(f"登录失败: {error_msg}")
                print(f"登录失败: {error_msg}")
        else:
            logger.error(f"登录请求失败，状态码: {response.status_code}")
            print(f"登录请求失败，状态码: {response.status_code}")

        # 如果登录失败，生成一个模拟token
        print("使用模拟token继续...")
        auth_token = f"mock_token_{int(time.time())}"
        logger.info("使用模拟token")

        # 保存配置
        save_config()

        return True
    except Exception as e:
        logger.error(f"登录过程中发生错误: {str(e)}")
        print(f"登录过程中发生错误: {str(e)}")

        # 如果发生错误，生成一个模拟token
        print("使用模拟token继续...")
        auth_token = f"mock_token_{int(time.time())}"
        logger.info("使用模拟token")

        # 保存配置
        save_config()

        return True

def get_socket_port():
    """从服务器获取可用的Socket.IO端口"""
    global config, auth_token

    # 确保已登录并获取token
    if not auth_token and not login_to_backend():
        logger.error("未能获取认证token，无法获取Socket.IO端口")
        return False

    try:
        # 构建获取端口API URL
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            port_url = f"{base_url}/api/socket/get_port"
        else:
            # 否则直接添加 /api/socket/get_port
            port_url = f"{backend_url}/api/socket/get_port"

        if not port_url.startswith(("http://", "https://")):
            port_url = f"http://{port_url}"

        logger.info(f"尝试获取Socket.IO端口: {port_url}")

        # 添加认证头
        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }

        # 发送请求
        response = requests.post(
            port_url,
            headers=headers,
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            logger.info(f"获取Socket.IO端口响应: {result}")

            # 检查响应格式
            if result.get("success"):
                # 检查 port 是在根级别还是在 data 对象中
                if result.get("port"):
                    config["socket_port"] = result["port"]
                    logger.info(f"成功获取Socket.IO端口: {config['socket_port']}")
                    return True
                elif result.get("data") and result["data"].get("port"):
                    config["socket_port"] = result["data"]["port"]
                    logger.info(f"成功获取Socket.IO端口: {config['socket_port']}")
                    return True
                else:
                    logger.error("获取Socket.IO端口响应中未找到port字段")
                    print("获取Socket.IO端口响应中未找到port字段")
            else:
                error_msg = result.get('error') or result.get('message') or '未知错误'
                logger.error(f"获取Socket.IO端口失败: {error_msg}")
                print(f"获取Socket.IO端口失败: {error_msg}")
        elif response.status_code in [401, 403]:
            logger.error(f"认证失败，token可能已过期，状态码: {response.status_code}")
            print(f"认证失败，token可能已过期，状态码: {response.status_code}")
            # 清除token并尝试重新登录
            auth_token = ""
            if login_to_backend():
                logger.info("重新登录成功，尝试重新获取Socket.IO端口")
                # 重新尝试获取端口
                return get_socket_port()
            else:
                logger.error("重新登录失败，无法获取Socket.IO端口")
                print("重新登录失败，无法获取Socket.IO端口")
        else:
            logger.error(f"获取Socket.IO端口失败，状态码: {response.status_code}")
            print(f"获取Socket.IO端口失败，状态码: {response.status_code}")

        # 如果获取端口失败，使用默认端口
        config["socket_port"] = 3005  # 默认使用3005端口
        logger.info(f"使用默认Socket.IO端口: {config['socket_port']}")
        return True
    except Exception as e:
        logger.error(f"获取Socket.IO端口时发生错误: {str(e)}")
        print(f"获取Socket.IO端口时发生错误: {str(e)}")

        # 如果发生错误，使用默认端口
        config["socket_port"] = 3005  # 默认使用3005端口
        logger.info(f"使用默认Socket.IO端口: {config['socket_port']}")
        return True

def reconnect_socketio():
    """重新连接到Socket.IO服务器"""
    global socket_connected, config

    logger.info("尝试重新连接Socket.IO服务器...")

    # 断开现有连接（如果有）
    try:
        if hasattr(sio, 'connected') and sio.connected:
            logger.info("断开现有Socket.IO连接")
            sio.disconnect()
            time.sleep(1)  # 等待连接完全断开
    except Exception as e:
        logger.error(f"断开现有Socket.IO连接时发生错误: {str(e)}")

    socket_connected = False

    # 强制重新获取Socket.IO端口
    logger.info("强制重新获取Socket.IO端口")
    config["socket_port"] = None  # 清除现有端口配置，强制重新获取

    if not get_socket_port():
        logger.error("重新获取Socket.IO端口失败，无法重连")
        return False

    # 尝试重新连接
    return connect_to_socketio()

def connect_to_socketio():
    """连接到Socket.IO服务器"""
    global socket_connected, config

    if socket_connected:
        logger.debug("Socket.IO已连接，无需重新连接")
        return True

    # 确保已登录并获取token
    if not auth_token and not login_to_backend():
        logger.error("未能获取认证token，无法连接Socket.IO")
        return False

    # 确保有Socket.IO端口
    if not config.get("socket_port") and not get_socket_port():
        logger.error("未能获取Socket.IO端口，无法连接Socket.IO")
        return False

    try:
        # 从backend_url中提取主机名
        backend_url = config["backend_url"]

        # 移除协议前缀
        if backend_url.startswith(("http://", "https://")):
            backend_url = backend_url.replace("http://", "").replace("https://", "")

        # 移除路径部分，只保留主机名
        if "/" in backend_url:
            backend_url = backend_url.split("/")[0]

        # 移除端口部分，只保留主机名
        if ":" in backend_url:
            backend_url = backend_url.split(":")[0]

        # 构建Socket.IO URL
        socketio_url = f"http://{backend_url}:{config['socket_port']}"
        logger.info(f"构建Socket.IO URL: {socketio_url}")

        # 检查端口是否可访问
        try:
            logger.info(f"检查端口 {config['socket_port']} 是否可访问...")
            import requests
            response = requests.get(f"http://{backend_url}:{config['socket_port']}", timeout=3)
            logger.info(f"端口检查响应: {response.status_code}")
        except Exception as e:
            logger.warning(f"端口 {config['socket_port']} 检查失败: {e}")
            # 端口不可访问，尝试重新获取
            logger.info("尝试重新获取端口...")
            config["socket_port"] = None
            if not get_socket_port():
                logger.error("重新获取端口失败")
                return False
            # 更新连接URL
            socketio_url = f"http://{backend_url}:{config['socket_port']}"
            logger.info(f"使用新端口连接Socket.IO服务器: {socketio_url}")

        # 连接到Socket.IO服务器
        logger.info("开始连接Socket.IO服务器...")
        connection_success = False

        # 尝试使用不同的传输方式连接
        for transport_mode in [["websocket", "polling"], ["polling", "websocket"]]:
            try:
                logger.info(f"尝试使用传输方式 {transport_mode} 连接...")

                # 断开现有连接（如果有）
                if hasattr(sio, 'connected') and sio.connected:
                    logger.info("断开现有Socket.IO连接")
                    try:
                        sio.disconnect()
                        time.sleep(1)  # 等待连接完全断开
                    except Exception as disc_error:
                        logger.warning(f"断开现有连接时出错: {disc_error}")

                # 尝试连接
                sio.connect(
                    socketio_url,
                    socketio_path="/socket.io",
                    namespaces=["/trade"],  # 使用 trade 命名空间
                    wait=True,  # 阻塞等待连接，确保连接建立
                    wait_timeout=10,  # 设置连接超时时间为10秒
                    transports=transport_mode
                )
                logger.info(f"使用传输方式 {transport_mode} 连接初始化成功")
                connection_success = True
                break
            except Exception as connect_error:
                logger.error(f"使用传输方式 {transport_mode} 连接失败: {connect_error}")
                # 短暂等待后尝试下一种传输方式
                time.sleep(2)

        if not connection_success:
            logger.error("所有传输方式都连接失败")
            return False

        # 等待连接建立
        logger.info("等待Socket.IO连接完全建立...")
        for i in range(30):  # 最多等待30秒
            if socket_connected:
                logger.info(f"Socket.IO连接成功，用时 {i+1} 秒")
                # 连接成功后，主动发送一个心跳包
                try:
                    # 使用固定的客户端ID格式：username_client_type
                    client_id = f"{config['username']}_print_client"

                    heartbeat_data = {
                        "username": config["username"],
                        "client_type": "easytrader_ths",
                        "client_id": client_id,
                        "timestamp": int(time.time())
                    }
                    logger.info("发送初始心跳包...")
                    sio.emit('heartbeat', heartbeat_data, namespace='/trade')
                    logger.info("初始心跳包发送成功")
                except Exception as e:
                    logger.error(f"发送初始心跳包失败: {e}")
                    # 发送心跳失败不应该影响连接状态的判断
                return True
            logger.debug(f"等待Socket.IO连接... {i+1}/30 秒")
            time.sleep(1)

        if not socket_connected:
            logger.warning("Socket.IO连接超时，30秒内未收到连接成功事件")
            return False

        return True
    except Exception as e:
        logger.error(f"连接Socket.IO服务器失败: {str(e)}")
        return False

def register_via_socketio():
    """通过Socket.IO注册客户端"""
    try:
        # 使用固定的客户端ID格式：username_client_type
        client_id = f"{config['username']}_print_client"

        data = {
            "username": config["username"],
            "client_type": "easytrader_ths",
            "client_id": client_id,
            "timestamp": int(time.time())
        }

        logger.info(f"通过Socket.IO注册客户端，使用ID: {client_id}")
        # 在 /trade 命名空间中发送注册消息
        sio.emit('register', data, namespace='/trade')
        return True
    except Exception as e:
        logger.error(f"通过Socket.IO注册客户端失败: {str(e)}")
        return False

def register_to_backend():
    """向后端注册本客户端"""
    # 确保已登录并获取token
    if not auth_token and not login_to_backend():
        logger.error("未能获取认证token，无法注册客户端")
        return False

    # 通过Socket.IO连接和注册
    if connect_to_socketio():
        logger.info("已通过Socket.IO连接到后端")
        return True

    logger.error("无法通过Socket.IO连接到后端")
    return False

def heartbeat_thread():
    """定时向后端发送心跳"""
    global socket_connected, auth_token, last_heartbeat_success

    consecutive_failures = 0
    max_failures = 2
    heartbeat_interval = config.get("heartbeat_interval", 30)
    reconnect_delay = 5

    while True:
        try:
            if not auth_token:
                logger.warning("没有认证token，尝试登录")
                if not login_to_backend():
                    logger.error("登录失败，无法发送心跳")
                    time.sleep(reconnect_delay)
                    consecutive_failures += 1
                    continue

            if not socket_connected:
                logger.warning("Socket.IO未连接，尝试重新连接")
                if consecutive_failures >= max_failures:
                    logger.warning(f"连续 {consecutive_failures} 次失败，尝试重新获取端口并重连")
                    config["socket_port"] = None
                    if not reconnect_socketio():
                        logger.error("重新获取端口并重连失败")
                        time.sleep(reconnect_delay)
                        consecutive_failures += 1
                        continue
                else:
                    if not connect_to_socketio():
                        logger.error("Socket.IO连接失败，无法发送心跳")
                        time.sleep(reconnect_delay)
                        consecutive_failures += 1
                        continue

            # 发送心跳并等待服务端回复
            try:
                client_id = f"{config['username']}_print_client"
                last_heartbeat_success = False
                heartbeat_event.clear()
                sio.emit('heartbeat', {
                    'username': config["username"],
                    'client_type': "easytrader_ths",
                    'client_id': client_id,
                    'timestamp': int(time.time())
                }, namespace='/trade')

                # 等待服务端回复，超时时间5秒
                if heartbeat_event.wait(timeout=5):
                    logger.info("Socket.IO连接状态正常，心跳收到服务端回复")
                    consecutive_failures = 0
                else:
                    logger.warning("心跳超时，未收到服务端回复，判定为断开")
                    last_heartbeat_success = False
                    socket_connected = False
                    consecutive_failures += 1
            except Exception as emit_error:
                logger.error(f"发送心跳失败: {str(emit_error)}")
                socket_connected = False  # 标记连接断开
                last_heartbeat_success = False
                consecutive_failures += 1
                logger.info("心跳发送失败，立即尝试重连")
                if not connect_to_socketio():
                    logger.error("重连失败，等待下一次心跳")
                continue

            time.sleep(heartbeat_interval)
        except Exception as e:
            logger.error(f"心跳线程发生错误: {str(e)}")
            consecutive_failures += 1
            socket_connected = False  # 标记连接断开
            last_heartbeat_success = False
            time.sleep(reconnect_delay)

def parse_arguments():
    """解析命令行参数"""
    global config
    parser = argparse.ArgumentParser(description='EasyTrader同花顺打印客户端')
    parser.add_argument('--username', help='用户名')
    parser.add_argument('--backend-url', help='后端URL')
    parser.add_argument('--local-port', type=int, help='本地端口')
    parser.add_argument('--balance', type=float, help='初始资金')

    args = parser.parse_args()

    # 更新配置
    if args.username:
        config["username"] = args.username
    if args.backend_url:
        config["backend_url"] = args.backend_url
    if args.local_port:
        config["local_port"] = args.local_port
    if args.balance:
        config["initial_balance"] = args.balance
        mock_data["account"]["总资产"] = args.balance
        mock_data["account"]["可用金额"] = args.balance

def prompt_for_local_port():
    """提示用户输入本地端口"""
    print("\n===== 本地服务配置 =====")

    if not config.get("local_port"):
        try:
            port_str = input(f"请输入本地服务端口 (默认: 8889): ")
            if port_str.strip():
                config["local_port"] = int(port_str.strip())
            else:
                config["local_port"] = 8889
        except ValueError:
            print("端口必须是数字，使用默认端口8889")
            config["local_port"] = 8889
    else:
        print(f"当前本地服务端口: {config['local_port']}")
        change = input("是否修改本地服务端口? (y/n): ").lower()
        if change == 'y':
            try:
                port_str = input("请输入新的本地服务端口: ")
                if port_str.strip():
                    config["local_port"] = int(port_str.strip())
            except ValueError:
                print("端口必须是数字，保持原端口不变")

    # 本地端口不需要保存到配置文件
    return True

def get_quote(code):
    """获取股票行情"""
    if code in mock_quotes:
        # 随机波动价格，模拟市场变化
        quote = mock_quotes[code].copy()
        price_change = random.uniform(-0.02, 0.02)  # 随机±2%的价格变化
        quote["price"] = round(quote["price"] * (1 + price_change), 2)
        return quote
    else:
        # 如果没有预设行情，生成随机行情
        price = round(random.uniform(5.0, 50.0), 2)
        return {
            "name": f"模拟股票{code}",
            "price": price,
            "high": round(price * 1.05, 2),
            "low": round(price * 0.95, 2),
            "open": round(price * 0.98, 2),
            "close": price,
            "volume": random.randint(100000, 5000000)
        }

def update_account():
    """更新账户信息"""
    # 计算股票市值和浮动盈亏
    stock_value = 0.0
    float_profit = 0.0

    for pos in mock_data["positions"]:
        code = pos["证券代码"]
        quote = get_quote(code)
        current_price = quote["price"]

        # 更新持仓的当前价格
        pos["当前价格"] = current_price

        # 计算市值
        volume = float(pos["股票余额"])
        market_value = volume * current_price
        pos["市值"] = market_value
        stock_value += market_value

        # 计算浮动盈亏
        cost = float(pos["成本价"]) * volume
        profit = market_value - cost
        pos["浮动盈亏"] = profit
        float_profit += profit

    # 更新账户信息
    mock_data["account"]["股票市值"] = stock_value
    mock_data["account"]["浮动盈亏"] = float_profit
    mock_data["account"]["总资产"] = mock_data["account"]["可用金额"] + mock_data["account"]["冻结金额"] + stock_value

def process_entrusts():
    """处理委托单，立即以委托价成交"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")

    for entrust in mock_data["entrusts"]:
        # 只处理已报委托
        if entrust["委托状态"] != "已报":
            continue

        code = entrust["证券代码"]
        entrust_price = float(entrust["委托价格"])
        entrust_volume = int(entrust["委托数量"])
        traded_volume = int(entrust["成交数量"])
        remaining_volume = entrust_volume - traded_volume

        if remaining_volume <= 0:
            continue

        # 获取股票信息
        quote = get_quote(code)
        # 使用股票名称

        # 立即全部成交
        trade_volume = remaining_volume
        trade_price = entrust_price  # 使用委托价格作为成交价格
        trade_amount = trade_volume * trade_price

        # 更新委托的成交数量
        entrust["成交数量"] = str(entrust_volume)

        # 生成成交记录
        trade = {
            "成交编号": f"T{int(time.time())}{random.randint(1000, 9999)}",
            "委托编号": entrust["委托编号"],
            "证券代码": code,
            "证券名称": entrust["证券名称"],
            "操作": entrust["操作"],
            "成交价格": str(trade_price),
            "成交数量": str(trade_volume),
            "成交金额": str(trade_amount),
            "成交时间": current_time
        }

        mock_data["trades"].append(trade)

        # 更新委托状态为已成
        entrust["委托状态"] = "已成"

        # 更新持仓和资金
        update_position(trade)

    # 更新账户信息
    update_account()

def update_position(trade):
    """根据成交更新持仓和资金"""
    code = trade["证券代码"]
    trade_price = float(trade["成交价格"])
    trade_volume = int(trade["成交数量"])
    trade_amount = float(trade["成交金额"])
    is_buy = "买入" in trade["操作"]

    # 计算手续费
    commission = trade_amount * config["commission_rate"]
    commission = Decimal(str(commission)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    commission = float(commission)

    if is_buy:
        # 买入：增加持仓，减少资金
        position_found = False
        for pos in mock_data["positions"]:
            if pos["证券代码"] == code:
                # 更新现有持仓
                old_volume = int(pos["股票余额"])
                old_cost = float(pos["成本价"]) * old_volume
                new_volume = old_volume + trade_volume
                new_cost = old_cost + trade_amount + commission
                pos["股票余额"] = str(new_volume)
                pos["可用余额"] = str(new_volume)  # 简化处理，假设立即可用
                pos["成本价"] = str(round(new_cost / new_volume, 3))
                position_found = True
                break

        if not position_found:
            # 新增持仓
            position = {
                "证券代码": code,
                "证券名称": trade["证券名称"],
                "股票余额": str(trade_volume),
                "可用余额": str(trade_volume),  # 简化处理，假设立即可用
                "成本价": str(round((trade_amount + commission) / trade_volume, 3)),
                "当前价格": str(trade_price),
                "市值": str(trade_volume * trade_price),
                "浮动盈亏": "0.00"
            }
            mock_data["positions"].append(position)

        # 更新资金
        total_cost = trade_amount + commission
        mock_data["account"]["可用金额"] -= total_cost
        mock_data["account"]["冻结金额"] -= total_cost  # 解冻之前冻结的资金
    else:
        # 卖出：减少持仓，增加资金
        for pos in mock_data["positions"]:
            if pos["证券代码"] == code:
                # 更新持仓
                old_volume = int(pos["股票余额"])
                new_volume = old_volume - trade_volume
                pos["股票余额"] = str(new_volume)
                pos["可用余额"] = str(new_volume)  # 简化处理，假设立即可用

                # 如果持仓为0，从列表中移除
                if new_volume <= 0:
                    mock_data["positions"].remove(pos)
                break

        # 更新资金
        net_income = trade_amount - commission
        mock_data["account"]["可用金额"] += net_income
        # 卖出不需要解冻资金，因为卖出时不冻结资金

# Flask API路由
@app.route('/api/status', methods=['GET'])
def api_status():
    """获取交易接口状态"""
    return jsonify({
        "success": True,
        "data": {
            "trader_status": "connected",
            "client_type": "easytrader_ths",
            "username": config["username"],
            "timestamp": int(time.time())
        }
    })

@app.route('/api/health', methods=['GET'])
def api_health():
    """健康检查"""
    # 处理挂单，模拟市场变化
    process_entrusts()
    update_account()

    return jsonify({
        "success": True,
        "data": {
            "status": "healthy",
            "trader_status": "connected",
            "account_status": "normal",
            "timestamp": int(time.time())
        }
    })

@app.route('/api/balance', methods=['GET'])
def api_balance():
    """获取账户余额"""
    # 处理挂单，模拟市场变化
    process_entrusts()
    update_account()

    return jsonify({
        "success": True,
        "data": mock_data["account"]
    })

@app.route('/api/position', methods=['GET'])
def api_position():
    """获取持仓信息"""
    # 处理挂单，模拟市场变化
    process_entrusts()
    update_account()

    return jsonify({
        "success": True,
        "data": mock_data["positions"]
    })

@app.route('/api/today_entrusts', methods=['GET'])
def api_today_entrusts():
    """获取当日委托"""
    # 处理挂单，模拟市场变化
    process_entrusts()

    return jsonify({
        "success": True,
        "data": mock_data["entrusts"]
    })

@app.route('/api/today_trades', methods=['GET'])
def api_today_trades():
    """获取当日成交"""
    # 处理挂单，模拟市场变化
    process_entrusts()

    return jsonify({
        "success": True,
        "data": mock_data["trades"]
    })

@app.route('/api/buy', methods=['POST'])
def api_buy():
    """买入股票"""
    try:
        data = request.get_json(silent=True)
        if data is None:
            return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400

        code = data.get('code')
        price = float(data.get('price', 0))
        amount = int(data.get('amount', 0))

        if not all([code, price > 0, amount > 0]):
            return jsonify({"success": False, "message": "参数不完整或无效"}), 400

        # 检查资金是否足够
        total_cost = price * amount * (1 + config["commission_rate"])
        if mock_data["account"]["可用金额"] < total_cost:
            return jsonify({"success": False, "message": "可用资金不足"}), 400

        # 检查交易量是否符合规则
        if amount < config["min_trade_volume"] or amount % config["trade_volume_step"] != 0:
            return jsonify({"success": False, "message": f"交易量必须是{config['trade_volume_step']}的整数倍，且不小于{config['min_trade_volume']}"}), 400

        # 获取股票信息
        quote = get_quote(code)

        # 冻结资金
        mock_data["account"]["可用金额"] -= total_cost
        mock_data["account"]["冻结金额"] += total_cost

        # 创建委托记录
        entrust_no = f"E{int(time.time())}{random.randint(1000, 9999)}"
        entrust = {
            "委托编号": entrust_no,
            "证券代码": code,
            "证券名称": quote["name"],
            "操作": "买入",
            "委托价格": str(price),
            "委托数量": str(amount),
            "成交数量": "0",
            "委托状态": "已报",
            "委托时间": datetime.datetime.now().strftime("%H:%M:%S")
        }

        mock_data["entrusts"].append(entrust)

        # 立即处理委托，确保立即成交
        process_entrusts()

        # 查找成交记录
        trade_info = None
        for trade in mock_data["trades"]:
            if trade["委托编号"] == entrust_no:
                trade_info = trade
                break

        return jsonify({
            "success": True,
            "message": "委托已成交",
            "data": {
                "entrust_no": entrust_no,
                "trade_info": trade_info
            }
        })
    except Exception as e:
        logger.error(f"买入失败: {str(e)}")
        return jsonify({"success": False, "message": f"买入失败: {str(e)}"}), 500

@app.route('/api/sell', methods=['POST'])
def api_sell():
    """卖出股票"""
    try:
        data = request.get_json(silent=True)
        if data is None:
            return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400

        code = data.get('code')
        price = float(data.get('price', 0))
        amount = int(data.get('amount', 0))

        if not all([code, price > 0, amount > 0]):
            return jsonify({"success": False, "message": "参数不完整或无效"}), 400

        # 检查持仓是否足够
        position_found = False
        available_amount = 0
        for pos in mock_data["positions"]:
            if pos["证券代码"] == code:
                position_found = True
                available_amount = int(pos["可用余额"])
                break

        if not position_found or available_amount < amount:
            return jsonify({"success": False, "message": "可用持仓不足"}), 400

        # 检查交易量是否符合规则
        if amount < config["min_trade_volume"] or amount % config["trade_volume_step"] != 0:
            return jsonify({"success": False, "message": f"交易量必须是{config['trade_volume_step']}的整数倍，且不小于{config['min_trade_volume']}"}), 400

        # 获取股票信息
        quote = get_quote(code)

        # 减少可用持仓
        for pos in mock_data["positions"]:
            if pos["证券代码"] == code:
                pos["可用余额"] = str(int(pos["可用余额"]) - amount)
                break

        # 创建委托记录
        entrust_no = f"E{int(time.time())}{random.randint(1000, 9999)}"
        entrust = {
            "委托编号": entrust_no,
            "证券代码": code,
            "证券名称": quote["name"],
            "操作": "卖出",
            "委托价格": str(price),
            "委托数量": str(amount),
            "成交数量": "0",
            "委托状态": "已报",
            "委托时间": datetime.datetime.now().strftime("%H:%M:%S")
        }

        mock_data["entrusts"].append(entrust)

        # 立即处理委托，确保立即成交
        process_entrusts()

        # 查找成交记录
        trade_info = None
        for trade in mock_data["trades"]:
            if trade["委托编号"] == entrust_no:
                trade_info = trade
                break

        return jsonify({
            "success": True,
            "message": "委托已成交",
            "data": {
                "entrust_no": entrust_no,
                "trade_info": trade_info
            }
        })
    except Exception as e:
        logger.error(f"卖出失败: {str(e)}")
        return jsonify({"success": False, "message": f"卖出失败: {str(e)}"}), 500

@app.route('/api/cancel', methods=['POST'])
def api_cancel():
    """撤销委托"""
    try:
        data = request.get_json(silent=True)
        if data is None:
            return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400

        entrust_no = data.get('entrust_no')

        if not entrust_no:
            return jsonify({"success": False, "message": "参数不完整"}), 400

        # 查找委托
        entrust_found = False
        for entrust in mock_data["entrusts"]:
            if entrust["委托编号"] == entrust_no:
                entrust_found = True

                # 只能撤销"已报"或"部成"状态的委托
                if entrust["委托状态"] in ["已报", "部成"]:
                    # 更新委托状态
                    entrust["委托状态"] = "废单"

                    # 解冻资金（如果是买入委托）
                    if "买入" in entrust["操作"]:
                        price = float(entrust["委托价格"])
                        remaining_volume = int(entrust["委托数量"]) - int(entrust["成交数量"])
                        remaining_cost = price * remaining_volume * (1 + config["commission_rate"])

                        mock_data["account"]["冻结金额"] -= remaining_cost
                        mock_data["account"]["可用金额"] += remaining_cost

                    # 恢复可用持仓（如果是卖出委托）
                    if "卖出" in entrust["操作"]:
                        code = entrust["证券代码"]
                        remaining_volume = int(entrust["委托数量"]) - int(entrust["成交数量"])

                        for pos in mock_data["positions"]:
                            if pos["证券代码"] == code:
                                pos["可用余额"] = str(int(pos["可用余额"]) + remaining_volume)
                                break

                    return jsonify({
                        "success": True,
                        "message": "撤单成功",
                        "data": {
                            "entrust_no": entrust_no
                        }
                    })
                else:
                    return jsonify({"success": False, "message": f"无法撤销状态为'{entrust['委托状态']}'的委托"}), 400

        if not entrust_found:
            return jsonify({"success": False, "message": "委托不存在"}), 404
    except Exception as e:
        logger.error(f"撤单失败: {str(e)}")
        return jsonify({"success": False, "message": f"撤单失败: {str(e)}"}), 500

@app.route('/api/refresh', methods=['POST'])
def api_refresh():
    """刷新接口"""
    # 处理挂单，模拟市场变化
    process_entrusts()
    update_account()

    return jsonify({
        "success": True,
        "message": "刷新成功"
    })

def start_server():
    """启动服务器"""
    # 加载配置
    load_config()

    # 启动定时任务，定期处理委托和更新账户信息
    def scheduled_task():
        while True:
            try:
                process_entrusts()
                update_account()
            except Exception as e:
                logger.error(f"定时任务执行失败: {str(e)}")
            time.sleep(5)  # 每5秒执行一次

    # 启动定时任务线程
    task_thread = threading.Thread(target=scheduled_task)
    task_thread.daemon = True
    task_thread.start()

    # 启动Flask服务器
    logger.info(f"启动模拟交易服务器，监听端口: {config['local_port']}")
    app.run(host='0.0.0.0', port=config['local_port'])

def connection_monitor_thread():
    """监控Socket.IO连接状态"""
    global socket_connected  # 声明使用全局变量

    reconnect_interval = 30  # 每30秒检查一次连接状态
    check_interval = 10      # 每10秒检查一次连接状态
    last_reconnect_time = 0  # 上次重连时间

    while True:
        try:
            current_time = time.time()

            # 检查连接状态
            if not socket_connected:
                # 如果距离上次重连已经过了reconnect_interval秒，则尝试重连
                if current_time - last_reconnect_time > reconnect_interval:
                    logger.warning("检测到Socket.IO连接断开，尝试重新连接")
                    # 强制重新获取端口
                    config["socket_port"] = None
                    reconnect_result = reconnect_socketio()
                    last_reconnect_time = current_time

                    if reconnect_result:
                        logger.info("Socket.IO重新连接成功")
                    else:
                        logger.error("Socket.IO重新连接失败")
            else:
                # 连接正常，检查是否可以发送心跳
                try:
                    # 尝试发送一个测试心跳，验证连接是否真的正常
                    sio.emit('heartbeat', {
                        'username': config["username"],
                        'client_type': "easytrader_ths",
                        'timestamp': int(current_time),
                        'test': True  # 标记为测试心跳
                    }, namespace='/trade')
                    logger.debug("Socket.IO连接状态正常，测试心跳发送成功")
                except Exception as emit_error:
                    logger.warning(f"Socket.IO连接可能已断开，测试心跳发送失败: {emit_error}")
                    socket_connected = False  # 标记连接断开
        except Exception as e:
            logger.error(f"监控Socket.IO连接状态时发生错误: {str(e)}")

        # 等待下一次检查
        time.sleep(check_interval)

def main():
    """主函数"""
    # 加载配置
    load_config()
    parse_arguments()

    print("\n===== 同花顺打印客户端 =====")
    print("该程序将接收交易指令并打印到控制台，不执行任何实际交易操作")

    # 登录到后端获取token
    global auth_token
    # 强制每次启动都要求登录
    auth_token = ""
    if not login_to_backend():
        logger.error("登录失败，程序退出")
        return

    # 提示输入本地端口
    prompt_for_local_port()

    # 心跳间隔
    print("\n===== 打印客户端配置 =====")
    heartbeat_input = input(f"请输入心跳间隔（秒）[默认: {config['heartbeat_interval']}]: ")
    if heartbeat_input.strip():
        try:
            heartbeat = float(heartbeat_input)
            config['heartbeat_interval'] = heartbeat
        except ValueError:
            print(f"心跳间隔必须是数字，使用默认值: {config['heartbeat_interval']}")

    # 保存配置
    save_config()

    print("\n===== 初始化打印客户端 =====")
    print("注意: 此客户端仅打印接收到的指令，不执行任何实际交易操作")

    # 启动定时任务，定期处理委托和更新账户信息
    def scheduled_task():
        while True:
            try:
                process_entrusts()
                update_account()
            except Exception as e:
                logger.error(f"定时任务执行失败: {str(e)}")
            time.sleep(5)  # 每5秒执行一次

    # 启动定时任务线程
    task_thread = threading.Thread(target=scheduled_task)
    task_thread.daemon = True
    task_thread.start()

    print("\n===== 连接到后端服务器 =====")
    # 获取Socket.IO端口并连接
    if not get_socket_port():
        logger.warning("获取Socket.IO端口失败，将继续运行但可能无法被后端发现")
        print("警告: 获取Socket.IO端口失败，将继续运行但可能无法被后端发现")
    else:
        # 注册到后端
        if not register_to_backend():
            logger.warning("注册到后端失败，将继续运行但可能无法被后端发现")
            print("警告: 注册到后端失败，将继续运行但可能无法被后端发现")
        else:
            print("已成功注册到后端服务器")

            # 启动心跳线程
            hb_thread = threading.Thread(target=heartbeat_thread, daemon=True)
            hb_thread.start()
            print("心跳线程已启动")

            # 启动连接监控线程
            monitor_thread = threading.Thread(target=connection_monitor_thread, daemon=True)
            monitor_thread.start()
            print("连接监控线程已启动")

    # 启动API服务
    print(f"\n===== 启动打印客户端服务 =====")
    print(f"打印客户端已启动，监听端口: {config['local_port']}")
    print("现在将接收并打印来自量化平台的交易指令")
    print("请保持此窗口开启，关闭窗口将断开与后端的连接")

    logger.info(f"启动API服务，监听端口: {config['local_port']}")
    try:
        app.run(host='0.0.0.0', port=config['local_port'])
    except Exception as flask_e:
        logger.error(f"启动 Flask 服务失败: {flask_e}")
        print(f"错误: 启动本地服务失败: {flask_e}")

    # 如果Flask服务器停止，确保Socket.IO也断开连接
    if socket_connected:
        logger.info("断开Socket.IO连接")
        sio.disconnect()

if __name__ == "__main__":
    main()
