#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Wonder Trader 多因子实盘运行中心
负责实盘策略的启动、停止和管理
与 portfolio_center.py 对应，专注于实盘逻辑
"""

import os
import sys
import yaml
import json
import logging
import subprocess
import threading
import time
import shutil
import sqlite3
from pathlib import Path
from typing import Dict, List, Optional, Any, Set, Union
from datetime import datetime
from collections import defaultdict

# ===== 新增：全局读取 config.json 的 xtp_trade_server 配置 =====
CONFIG_JSON_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../config.json'))
_xtp_server_config = None

def get_xtp_server_config():
    global _xtp_server_config
    if _xtp_server_config is None:
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config = json.load(f)
                _xtp_server_config = config.get('xtp_trade_server', {})
        except Exception as e:
            _xtp_server_config = {}
            print(f"[live_center] 读取 config.json 失败: {e}")
    return _xtp_server_config

# 添加当前目录到Python路径，确保能导入MultiFactorsCTA
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 添加strategy目录到Python路径，unified_ext_data_loader在../../
strategy_dir = os.path.join(current_dir, '..', '..')
if strategy_dir not in sys.path:
    sys.path.insert(0, strategy_dir)

# 导入Wonder Trader
try:
    from wtpy import WtEngine, EngineType
    from MultiFactorsCTA import MultiFactorsCTA
    # 导入统一数据加载器
    from strategy.unified_ext_data_loader import get_unified_ext_data_loader
except ImportError as e:
    print(f"导入Wonder Trader模块失败: {e}")
    sys.exit(1)

# 全局变量：管理实盘进程
live_processes: Dict[str, Dict[str, Any]] = {}
live_processes_lock = threading.Lock()

# ===== 全局品种管理 =====
# 品种计数器：{symbol: count}
_symbol_counters = defaultdict(int)

# 项目品种映射：{project_id: set(symbols)}
_project_symbols: Dict[str, Set[str]] = {}

# 线程锁保护全局状态
_symbols_lock = threading.Lock()

# contracts.json 文件路径（live_DT目录下的全局文件）
_contracts_json_path = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ===== 数据库查询函数 =====

def get_user_trading_config(user_id: int, channel_type: str) -> Optional[Dict[str, Any]]:
    """
    从数据库获取用户的交易通道配置

    Args:
        user_id: 用户ID
        channel_type: 通道类型，如 'openctp', 'miniQMT' 等

    Returns:
        Dict: 交易通道配置，如果未找到返回None
    """
    try:
        # 数据库文件路径（相对于项目根目录）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.join(current_dir, '..', '..', '..', '..', '..')
        db_path = os.path.join(project_root, 'backend', 'database', 'database.sqlite')

        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            return None

        # 连接数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        cursor = conn.cursor()

        # 查询用户的交易通道配置
        query = """
        SELECT channelType, configName, broker, username, password,
               appid, authcode, frontAddress
        FROM user_trading_configs
        WHERE userId = ? AND channelType = ? AND isActive = 1
        """

        cursor.execute(query, (user_id, channel_type))
        result = cursor.fetchone()

        conn.close()

        if result:
            config = {
                'channelType': result['channelType'],
                'configName': result['configName'],
                'broker': result['broker'] or '',
                'username': result['username'],
                'password': result['password'],
                'appid': result['appid'] or '',
                'authcode': result['authcode'] or '',
                'frontAddress': result['frontAddress']
            }
            logger.info(f"获取用户 {user_id} 的 {channel_type} 配置成功")
            return config
        else:
            logger.warning(f"未找到用户 {user_id} 的 {channel_type} 配置")
            return None

    except Exception as e:
        logger.error(f"获取用户交易配置失败: {e}")
        return None

def get_trading_channel_module_mapping(channel_type: str) -> Dict[str, Any]:
    """
    根据交易通道类型获取对应的模块配置
    """
    # 交易通道类型到Wonder Trader模块的映射
    xtp_cfg = get_xtp_server_config()
    channel_mapping = {
        'openctp': {
            'module': 'TraderCTP',
            'default_host': '***************',
            'default_port': 10130,
            'quick': True
        },
        'miniQMT': {
            'module': 'TraderXTP',
            'default_host': xtp_cfg.get('host', '127.0.0.1'),
            'default_port': xtp_cfg.get('port', 6001),
            'quick': True
        },
        'easytrader_ths': {
            'module': 'TraderCustom',
            'default_host': xtp_cfg.get('host', '127.0.0.1'),
            'default_port': xtp_cfg.get('port', 6001),
            'quick': True,
            'is_custom': True
        },
        'simnow': {
            'module': 'TraderCTP',
            'default_host': '***************',
            'default_port': 10130,
            'quick': True
        }
    }
    return channel_mapping.get(channel_type, {
        'module': 'TraderCTP',
        'default_host': '***************',
        'default_port': 10130,
        'quick': True
    })

# ===== 品种管理函数 =====

def initialize_contracts_json_path():
    """初始化contracts.json文件路径"""
    global _contracts_json_path
    if _contracts_json_path is None:
        # contracts.json应该在live_DT目录下
        current_dir = os.path.dirname(os.path.abspath(__file__))
        live_dt_dir = os.path.join(current_dir, 'live_DT')
        _contracts_json_path = os.path.join(live_dt_dir, 'contracts.json')
        logger.info(f"contracts.json路径: {_contracts_json_path}")
    return _contracts_json_path

def add_project_symbols(project_id: str, symbols: Set[str]):
    """
    添加项目的品种列表到全局管理

    Args:
        project_id: 项目ID，格式如 "user_1_group_2"
        symbols: 品种集合
    """
    global _symbol_counters, _project_symbols, _symbols_lock

    with _symbols_lock:
        # 保存项目品种映射
        _project_symbols[project_id] = symbols.copy()

        # 更新品种计数器
        for symbol in symbols:
            _symbol_counters[symbol] += 1

        logger.info(f"项目 {project_id} 添加品种: {symbols}")
        logger.info(f"当前品种计数器: {dict(_symbol_counters)}")

        # 更新contracts.json
        _update_contracts_json()

def remove_project_symbols(project_id: str):
    """
    移除项目的品种列表

    Args:
        project_id: 项目ID
    """
    global _symbol_counters, _project_symbols, _symbols_lock

    with _symbols_lock:
        if project_id not in _project_symbols:
            logger.warning(f"项目 {project_id} 不存在于品种映射中")
            return

        symbols = _project_symbols[project_id]

        # 递减品种计数器
        for symbol in symbols:
            if _symbol_counters[symbol] > 0:
                _symbol_counters[symbol] -= 1

            # 如果计数为0，删除该品种
            if _symbol_counters[symbol] == 0:
                del _symbol_counters[symbol]

        # 删除项目映射
        del _project_symbols[project_id]

        logger.info(f"项目 {project_id} 移除品种: {symbols}")
        logger.info(f"当前品种计数器: {dict(_symbol_counters)}")

        # 更新contracts.json
        _update_contracts_json()

def _update_contracts_json():
    """
    根据当前品种计数器更新contracts.json文件
    只有计数器大于0的品种才会被写入
    使用Wonder Trader标准的嵌套对象格式
    """
    global _symbol_counters

    try:
        contracts_path = initialize_contracts_json_path()

        # 获取所有活跃品种（计数器 > 0）
        active_symbols = [symbol for symbol, count in _symbol_counters.items() if count > 0]

        # 按交易所分类，使用嵌套对象格式
        contracts_data = {
            "SSE": {},
            "SZSE": {}
        }

        for symbol in active_symbols:
            # 去掉可能的交易所前缀，直接使用代码
            clean_code = symbol.split('.')[-1] if '.' in symbol else symbol

            # 根据代码判断交易所和产品类型
            if clean_code.startswith('0') or clean_code.startswith('3'):
                # 深交所
                exchg = "SZSE"
                if clean_code.startswith('0'):
                    product = "STK"  # 股票
                    name = f"深股{clean_code}"
                else:  # 3开头
                    product = "STK"  # 创业板股票
                    name = f"创业板{clean_code}"
            else:
                # 上交所
                exchg = "SSE"
                if clean_code.startswith('6'):
                    product = "STK"  # 股票
                    name = f"沪股{clean_code}"
                elif clean_code.startswith('51') or clean_code.startswith('50'):
                    product = "ETF"  # ETF
                    name = f"ETF{clean_code}"
                else:
                    product = "IDX"  # 指数
                    name = f"指数{clean_code}"

            # 构建品种信息对象
            contract_info = {
                "code": clean_code,
                "exchg": exchg,
                "name": name,
                "product": product
            }

            # 添加到对应交易所
            contracts_data[exchg][clean_code] = contract_info

        # 确保目录存在
        os.makedirs(os.path.dirname(contracts_path), exist_ok=True)

        # 写入文件
        with open(contracts_path, 'w', encoding='utf-8') as f:
            json.dump(contracts_data, f, ensure_ascii=False, indent=2)

        sse_count = len(contracts_data['SSE'])
        szse_count = len(contracts_data['SZSE'])
        logger.info(f"更新contracts.json: {len(active_symbols)}个品种")
        logger.info(f"SSE: {sse_count}个, SZSE: {szse_count}个")

        # 重启 runDT 数据引擎以重新加载品种订阅
        restart_success = restart_rundt_service()
        if restart_success:
            logger.info("runDT 数据引擎重启成功，品种订阅已更新")
        else:
            logger.warning("runDT 数据引擎重启失败，可能需要手动重启")

    except Exception as e:
        logger.error(f"更新contracts.json失败: {e}")

def get_current_symbols_status():
    """
    获取当前品种状态

    Returns:
        Dict: 包含品种计数器和项目映射的状态信息
    """
    global _symbol_counters, _project_symbols, _symbols_lock

    with _symbols_lock:
        return {
            'symbol_counters': dict(_symbol_counters),
            'project_symbols': {k: list(v) for k, v in _project_symbols.items()},
            'total_projects': len(_project_symbols),
            'total_symbols': len(_symbol_counters)
        }

def restart_rundt_service():
    """
    重启 runDT.py 数据引擎服务
    更新 contracts.json 后需要重启数据引擎以重新加载品种订阅
    """
    try:
        # 获取 live_DT 目录路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        live_dt_dir = os.path.join(current_dir, 'live_DT')
        rundt_script = os.path.join(live_dt_dir, 'runDT.py')

        if not os.path.exists(rundt_script):
            logger.error(f"runDT.py 脚本不存在: {rundt_script}")
            return False

        logger.info("正在重启 runDT 数据引擎服务...")

        # 查找并终止现有的 runDT 进程
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info['cmdline']
                    if cmdline and 'runDT.py' in ' '.join(cmdline):
                        logger.info(f"终止现有 runDT 进程: PID {proc.info['pid']}")
                        proc.terminate()
                        proc.wait(timeout=5)
                        logger.info(f"runDT 进程已终止: PID {proc.info['pid']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue
        except ImportError:
            logger.warning("psutil 模块未安装，无法自动终止现有进程")

        # 启动新的 runDT 进程
        logger.info(f"启动新的 runDT 进程: {rundt_script}")

        # 修正：不重定向输出，让runDT的print输出显示在主控制台
        process = subprocess.Popen(
            [sys.executable, 'runDT.py'],
            cwd=live_dt_dir,
            # 移除stdout和stderr重定向，让输出直接显示在控制台
        )

        # 等待一小段时间检查进程是否正常启动
        time.sleep(3)
        if process.poll() is None:
            logger.info(f"runDT 数据引擎重启成功, PID: {process.pid}")
            return True
        else:
            logger.error(f"runDT 数据引擎启动失败，进程已退出")
            # 由于没有重定向输出，错误信息应该已经显示在控制台
            return False

    except Exception as e:
        logger.error(f"重启 runDT 数据引擎失败: {e}")
        return False

def setup_live_environment():
    """
    设置实盘运行环境
    确保能正确访问 ../common/ 配置目录（与 portfolio_center.py 相同）
    """
    try:
        # 当前工作目录应该是 portfolio/
        current_dir = os.getcwd()
        logger.info(f"当前工作目录: {current_dir}")
        
        # ../common/ 的路径（与 portfolio_center.py 相同的路径逻辑）
        common_path = os.path.abspath("../common/")
        logger.info(f"Common配置目录路径: {common_path}")
        
        if not os.path.exists(common_path):
            raise FileNotFoundError(f"Common配置目录不存在: {common_path}")
        
        # 检查关键配置文件
        required_files = [
            "stk_comms.json",
            "holidays.json", 
            "sessions.json",
            "fees_stk.json"
        ]
        
        for file_name in required_files:
            file_path = os.path.join(common_path, file_name)
            if not os.path.exists(file_path):
                logger.warning(f"配置文件不存在: {file_path}")
            else:
                logger.info(f"配置文件检查通过: {file_path}")
        
        return common_path
        
    except Exception as e:
        logger.error(f"设置实盘环境失败: {e}")
        raise

def validate_live_config(live_config: Dict[str, Any]) -> bool:
    """
    验证实盘配置的有效性
    """
    try:
        required_fields = ['group_id', 'user_id', 'trading_channel']
        
        for field in required_fields:
            if field not in live_config:
                logger.error(f"实盘配置缺少必需字段: {field}")
                return False
        
        # 验证交易通道配置
        trading_channel = live_config.get('trading_channel', {})
        if not trading_channel.get('id'):
            logger.error("交易通道配置无效: 缺少通道ID")
            return False
        
        logger.info("实盘配置验证通过")
        return True
        
    except Exception as e:
        logger.error(f"验证实盘配置失败: {e}")
        return False

def create_live_group_config(group_config: Dict[str, Any], strategies_config: List[Dict[str, Any]], token: str) -> str:
    """
    启动实盘组合策略
    配置文件由Node.js端生成，这里只负责策略运行
    
    Args:
        group_config: 组合配置（包含group_id, user_id, group_path等）
        strategies_config: 策略配置列表（暂不使用，由Node.js处理）
        token: 认证token
    
    Returns:
        str: 运行状态信息
    """
    try:
        print("="*60)
        print("[TRACE] 进入 create_live_group_config 函数")
        print(f"[TRACE] 接收到的 group_config: {json.dumps(group_config, indent=2, ensure_ascii=False)}")
        print(f"[TRACE] 接收到的 token: {'******' if token else 'None'}")
        print(f"[TRACE] 策略配置数量: {len(strategies_config)}")
        print("="*60)
        
        group_id = group_config['group_id']
        user_id = group_config['user_id']
        print(f"[TRACE] 解析得到 group_id: {group_id}, user_id: {user_id}")
        
        # 组合配置目录（在 portfolio/ 目录下创建）
        group_dir = f"./live_groups/user_{user_id}/group_{group_id}"
        group_dir_abs = os.path.abspath(group_dir)
        
        # 创建目录
        os.makedirs(group_dir_abs, exist_ok=True)
        logger.info(f"创建组合配置目录: {group_dir_abs}")
        
        # 复制模板文件到项目目录
        template_dir = os.path.dirname(os.path.abspath(__file__))  # portfolio 目录
        template_group_dir = os.path.join(template_dir, 'tmpl', 'portfolio_0')

        # 复制 config.yaml 模板 - 与Node.js端保持一致
        config_template = os.path.join(template_group_dir, 'config.yaml')
        config_file = os.path.join(group_dir_abs, 'config.yaml')
        if os.path.exists(config_template):
            shutil.copy2(config_template, config_file)
            logger.info(f"复制主配置文件: {config_file}")
        else:
            logger.error(f"config.yaml 模板不存在: {config_template}")
            raise FileNotFoundError(f"配置模板文件不存在: {config_template}")
        
        # 收集项目品种并添加到全局管理
        all_codes = set()
        for strategy in strategies_config:
            strategy_yaml = yaml.safe_load(strategy.get('yaml', ''))
            universe = strategy_yaml.get('universe', [])
            all_codes.update(universe)

        # 生成项目ID
        project_id = f"user_{user_id}_group_{group_id}"

        # 添加到全局品种管理
        add_project_symbols(project_id, all_codes)

        # 生成本地contracts.json（用于引擎初始化）
        # 使用Wonder Trader标准的嵌套对象格式
        contracts_data = {
            "SSE": {},
            "SZSE": {}
        }

        for code in all_codes:
            # 去掉可能的交易所前缀，直接使用代码
            clean_code = code.split('.')[-1] if '.' in code else code

            # 根据代码判断交易所和产品类型
            if clean_code.startswith('0') or clean_code.startswith('3'):
                # 深交所
                exchg = "SZSE"
                if clean_code.startswith('0'):
                    product = "STK"  # 股票
                    name = f"深股{clean_code}"
                else:  # 3开头
                    product = "STK"  # 创业板股票
                    name = f"创业板{clean_code}"
            else:
                # 上交所
                exchg = "SSE"
                if clean_code.startswith('6'):
                    product = "STK"  # 股票
                    name = f"沪股{clean_code}"
                elif clean_code.startswith('51') or clean_code.startswith('50'):
                    product = "ETF"  # ETF
                    name = f"ETF{clean_code}"
                else:
                    product = "IDX"  # 指数
                    name = f"指数{clean_code}"

            # 构建品种信息对象
            contract_info = {
                "code": clean_code,
                "exchg": exchg,
                "name": name,
                "product": product
            }

            # 添加到对应交易所
            contracts_data[exchg][clean_code] = contract_info

        contracts_file = os.path.join(group_dir_abs, 'contracts.json')
        with open(contracts_file, 'w', encoding='utf-8') as f:
            json.dump(contracts_data, f, ensure_ascii=False, indent=2)

        sse_count = len(contracts_data['SSE'])
        szse_count = len(contracts_data['SZSE'])
        logger.info(f"生成本地品种列表文件: {contracts_file}")
        logger.info(f"项目 {project_id} 品种数量: {len(all_codes)} (SSE: {sse_count}, SZSE: {szse_count})")
        
        # 生成执行器配置 executers.yaml
        trading_channel = group_config.get('trading_channel', {})
        channel_id = trading_channel.get('id', 'openctp')
        channel_type = trading_channel.get('type', 'openctp')
        print(f"[TRACE] 解析交易通道信息:")
        print(f"[TRACE]   trading_channel: {trading_channel}")
        print(f"[TRACE]   channel_id: {channel_id}")
        print(f"[TRACE]   channel_type: {channel_type}")

        executers_data = {
            'executers': [
                {
                    'active': True,
                    'id': f'exec_{channel_id}',
                    'trader': channel_id,
                    'scale': 1,
                    'local': True,
                    'policy': {
                        'default': {
                            'name': 'WtExeFact.WtMinImpactExeUnit',
                            'offset': 0,
                            'expire': 5,
                            'pricemode': 1,
                            'span': 500,
                            'byrate': False,
                            'lots': 1,
                            'rate': 0
                        }
                    }
                }
            ]
        }

        executers_file = os.path.join(group_dir_abs, 'executers.yaml')
        with open(executers_file, 'w', encoding='utf-8') as f:
            yaml.dump(executers_data, f, allow_unicode=True, sort_keys=False)

        logger.info(f"生成执行器配置文件: {executers_file}")

        # 生成解析器配置 tdparsers.yaml - 使用UDP通道连接统一数据引擎
        parsers_data = {
            'parsers': [
                {
                    'active': True,
                    'id': 'parser1',
                    'module': 'ParserUDP',
                    'host': '127.0.0.1',
                    'bport': 9001,  # 广播端口
                    'sport': 3997,  # 查询端口
                    'filter': ''
                }
            ]
        }

        parsers_file = os.path.join(group_dir_abs, 'tdparsers.yaml')
        with open(parsers_file, 'w', encoding='utf-8') as f:
            yaml.dump(parsers_data, f, allow_unicode=True, sort_keys=False)

        logger.info(f"生成解析器配置文件: {parsers_file}")

        # 生成交易通道配置 tdtraders.yaml
        user_id = group_config.get('user_id')
        if not user_id:
            logger.error("group_config 中缺少 user_id")
            raise ValueError("group_config 中缺少 user_id")

        # 检查是否是动态注册的交易客户端
        print(f"[TRACE] 准备检查 channel_type='{channel_type}' 是否以 'easytrader' 开头")
        print(f"[TRACE] 条件判断: channel_type.startswith('easytrader') = {channel_type.startswith('easytrader')}")
        if channel_type.startswith(f"easytrader"):
            # 动态注册的交易客户端，例如: "user1_easytrader_ths"
            # username = f"user{user_id}"
            # client_type = channel_id.split('_', 1)[1]  # 提取client_type，例如: "easytrader_ths"

            # 获取模块配置
            module_config = get_trading_channel_module_mapping(channel_type)

            traders_data = {
                'traders': [
                    {
                        'active': True,
                        'client': 1,
                        'id': channel_id,
                        'module': module_config['module'],  # 'TraderCustom'
                        'host': module_config['default_host'],  # '127.0.0.1'
                        'port': module_config['default_port'],  # 3000
                        'user': channel_id,                       # channel_id作为user传入
                        'pass': 'dynamic_client',               # 占位符密码，没有作用
                        'acckey': token,                   # token作为acckey传入
                        'quick': module_config.get('quick', True),
                        # 添加风险监控配置
                        'riskmon': {
                            'active': True,
                            'policy': {
                                'default': {
                                    'cancel_stat_timespan': 10,
                                    'cancel_times_boundary': 20,
                                    'cancel_total_limits': 470,
                                    'order_stat_timespan': 10,
                                    'order_times_boundary': 20
                                }
                            }
                        }
                    }
                ]
            }
            logger.info(f"生成动态交易客户端配置: {channel_id}")
        else:
            print("[TRACE] 条件不匹配！进入标准外部交易通道配置分支")
            # 标准的外部交易通道配置
            user_config = get_user_trading_config(int(user_id), channel_id)

            if user_config:
                # 使用用户配置
                logger.info(f"使用用户 {user_id} 的 {channel_id} 交易通道配置")

                # 获取通道模块映射
                module_config = get_trading_channel_module_mapping(channel_id)

                # 解析前置地址
                front_address = user_config.get('frontAddress', '')
                if ':' in front_address:
                    host, port_str = front_address.split(':', 1)
                    try:
                        port = int(port_str)
                    except ValueError:
                        host = module_config['default_host']
                        port = module_config['default_port']
                        logger.warning(f"前置地址端口解析失败，使用默认值: {host}:{port}")
                else:
                    host = module_config['default_host']
                    port = module_config['default_port']
                    logger.warning(f"前置地址格式不正确，使用默认值: {host}:{port}")

                traders_data = {
                    'traders': [
                        {
                            'active': True,
                            'client': 1,
                            'id': channel_id,
                            'module': module_config['module'],
                            'host': host,
                            'port': port,
                            'user': user_config['username'],
                            'pass': user_config['password'],
                            'broker': user_config.get('broker', ''),
                            'appid': user_config.get('appid', ''),
                            'authcode': user_config.get('authcode', ''),
                            'quick': module_config.get('quick', True)
                        }
                    ]
                }
                logger.info(f"交易通道配置: {channel_id} -> {module_config['module']} @ {host}:{port}")
            else:
                # 使用默认配置（用户未配置交易通道）
                logger.warning(f"用户 {user_id} 未配置 {channel_id} 交易通道，使用默认配置")
                module_config = get_trading_channel_module_mapping(channel_id)

                traders_data = {
                    'traders': [
                        {
                            'active': True,
                            'client': 1,
                            'id': channel_id,
                            'module': module_config['module'],
                            'host': module_config['default_host'],
                            'port': module_config['default_port'],
                            'user': '',  # 需要用户配置
                            'pass': '',  # 需要用户配置
                            'broker': '',
                            'appid': '',
                            'authcode': '',
                            'quick': module_config.get('quick', True)
                        }
                    ]
                }

        print(f"[TRACE] 即将写入 tdtraders.yaml 的数据:")
        print(f"[TRACE] traders_data = {json.dumps(traders_data, indent=2, ensure_ascii=False)}")
        
        traders_file = os.path.join(group_dir_abs, 'tdtraders.yaml')
        with open(traders_file, 'w', encoding='utf-8') as f:
            yaml.dump(traders_data, f, allow_unicode=True, sort_keys=False)

        print(f"[TRACE] 已成功写入文件: {traders_file}")
        logger.info(f"生成交易通道配置文件: {traders_file}")

        # 生成过滤器配置 filters.yaml
        filters_data = {
            'code_filters': {},
            'strategy_filters': {}
        }

        filters_file = os.path.join(group_dir_abs, 'filters.yaml')
        with open(filters_file, 'w', encoding='utf-8') as f:
            yaml.dump(filters_data, f, allow_unicode=True, sort_keys=False)

        logger.info(f"生成过滤器配置文件: {filters_file}")

        # 生成交易行为策略配置 actpolicy.yaml
        actpolicy_data = {
            'default': {
                'order': [
                    {
                        'action': 'close',
                        'limit': 0
                    },
                    {
                        'action': 'open',
                        'limit': 0
                    }
                ]
            }
        }

        actpolicy_file = os.path.join(group_dir_abs, 'actpolicy.yaml')
        with open(actpolicy_file, 'w', encoding='utf-8') as f:
            yaml.dump(actpolicy_data, f, allow_unicode=True, sort_keys=False)

        logger.info(f"生成交易行为策略配置文件: {actpolicy_file}")

        # 生成策略配置文件 strategies.yaml
        strategies_data = {
            'strategies': []
        }

        for strategy in strategies_config:
            strategies_data['strategies'].append({
                'id': strategy.get('id'),
                'yaml': strategy.get('yaml', '')
            })

        strategies_file = os.path.join(group_dir_abs, 'strategies.yaml')
        with open(strategies_file, 'w', encoding='utf-8') as f:
            yaml.dump(strategies_data, f, allow_unicode=True, sort_keys=False)

        logger.info(f"生成策略配置文件: {strategies_file}")

        # 复制 run.py 模板
        run_template = os.path.join(template_group_dir, 'run.py')
        run_target = os.path.join(group_dir_abs, 'run.py')
        if os.path.exists(run_template):
            shutil.copy2(run_template, run_target)
            logger.info(f"复制启动脚本: {run_target}")
        else:
            logger.error(f"run.py 模板不存在: {run_template}")

        # 复制 logcfg.yaml 模板
        logcfg_template = os.path.join(template_group_dir, 'logcfg.yaml')
        logcfg_target = os.path.join(group_dir_abs, 'logcfg.yaml')
        if os.path.exists(logcfg_template):
            shutil.copy2(logcfg_template, logcfg_target)
            logger.info(f"复制日志配置: {logcfg_target}")
        else:
            logger.error(f"logcfg.yaml 模板不存在: {logcfg_template}")

        # 创建日志目录
        logs_dir = os.path.join(group_dir_abs, 'logs')
        os.makedirs(logs_dir, exist_ok=True)
        logger.info(f"创建日志目录: {logs_dir}")

        print(f"[TRACE] 函数即将结束，返回配置文件路径: {config_file}")
        print("="*60)
        print("[TRACE] create_live_group_config 函数执行完成")
        return config_file

    except Exception as e:
        print(f"[TRACE] 函数执行异常: {e}")
        logger.error(f"创建实盘组合配置失败: {e}")
        raise

def start_live_project_process(group_dir: str):
    """
    启动实盘项目进程

    Args:
        group_dir: 项目目录路径

    Returns:
        subprocess.Popen: 进程对象，如果启动失败则返回None
    """
    try:
        run_script = os.path.join(group_dir, 'run.py')
        if not os.path.exists(run_script):
            logger.error(f"启动脚本不存在: {run_script}")
            return None

        logger.info(f"启动实盘项目进程: {group_dir}")

        # 启动子进程 - 修正：不重定向输出，让run.py的输出显示在主控制台
        process = subprocess.Popen(
            [sys.executable, 'run.py'],
            cwd=group_dir,
            # 移除stdout和stderr重定向，让输出直接显示在控制台
        )

        # 等待一小段时间检查进程是否正常启动
        time.sleep(2)
        if process.poll() is None:
            logger.info(f"实盘项目进程启动成功, PID: {process.pid}")
            return process
        else:
            logger.error(f"实盘项目进程启动失败，进程已退出")
            # 由于没有重定向输出，错误信息应该已经显示在控制台
            return None

    except Exception as e:
        logger.error(f"启动实盘项目进程失败: {e}")
        return None

def stop_live_project_process(process, project_id: Optional[str] = None) -> bool:
    """
    停止实盘项目进程并清理品种订阅

    Args:
        process: subprocess.Popen 进程对象
        project_id: 项目ID，格式如 "user_1_group_2"，用于清理品种订阅

    Returns:
        bool: 是否成功停止
    """
    try:
        if process and process.poll() is None:
            logger.info(f"停止实盘项目进程, PID: {process.pid}")

            # 优雅关闭
            process.terminate()

            # 等待进程结束，最多等待10秒
            try:
                process.wait(timeout=10)
                logger.info(f"实盘项目进程已优雅停止, PID: {process.pid}")
            except subprocess.TimeoutExpired:
                logger.warning(f"进程未在10秒内停止，强制终止, PID: {process.pid}")
                process.kill()
                process.wait()
                logger.info(f"实盘项目进程已强制停止, PID: {process.pid}")
        else:
            logger.info("实盘项目进程未运行或已停止")

        # 清理品种订阅
        if project_id:
            logger.info(f"清理项目 {project_id} 的品种订阅")
            remove_project_symbols(project_id)
        else:
            logger.warning("未提供project_id，无法清理品种订阅")

        return True

    except Exception as e:
        logger.error(f"停止实盘项目进程失败: {e}")
        return False

def stop_live_project_with_cleanup(group_config: Dict[str, Any], process) -> bool:
    """
    停止实盘项目并清理品种订阅

    Args:
        group_config: 组合配置，包含 user_id 和 group_id
        process: 进程对象

    Returns:
        bool: 是否成功停止
    """
    try:
        # 生成项目ID
        user_id = group_config.get('user_id')
        group_id = group_config.get('group_id')
        project_id = f"user_{user_id}_group_{group_id}"

        logger.info(f"停止实盘项目: {project_id}")

        # 停止进程并清理品种
        return stop_live_project_process(process, project_id)

    except Exception as e:
        logger.error(f"停止实盘项目失败: {e}")
        return False

def do_run_live_group(group_config: Dict[str, Any], strategies_config: List[Dict[str, Any]], token: str, config_dir: str = None) -> Dict[str, Any]:
    """
    实盘组合运行核心函数
    配置文件由Node.js端生成，这里只负责策略运行逻辑
    """
    try:
        logger.info("=" * 50)
        logger.info("Wonder Trader 实盘组合运行器启动")
        logger.info(f"组合ID: {group_config.get('group_id')}")
        logger.info(f"用户ID: {group_config.get('user_id')}")
        logger.info(f"策略数量: {len(strategies_config)}")
        logger.info(f"配置目录: {config_dir}")
        logger.info("=" * 50)

        # 1. 设置实盘环境
        common_path = setup_live_environment()

        # 2. 验证配置
        if not validate_live_config(group_config):
            raise ValueError("实盘配置验证失败")

        # 3. 确定配置目录
        if config_dir:
            # 使用传入的配置目录（由Node.js生成）
            group_dir = config_dir
            config_file = os.path.join(group_dir, 'config.yaml')
        else:
            # 兼容旧版本：如果没有传入配置目录，则使用默认路径
            group_id = group_config['group_id']
            user_id = group_config['user_id']
            group_dir = f"./live_groups/user_{user_id}/group_{group_id}"
            group_dir = os.path.abspath(group_dir)
            config_file = os.path.join(group_dir, 'config.yaml')

        # 4. 检查配置文件是否存在
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")

        logger.info(f"实盘组合目录: {group_dir}")
        logger.info(f"配置文件: {config_file}")

        # 5. 管理品种订阅（保留这部分逻辑）
        all_codes = set()
        for strategy in strategies_config:
            strategy_yaml = yaml.safe_load(strategy.get('yaml', ''))
            universe = strategy_yaml.get('universe', [])
            all_codes.update(universe)

        # 生成项目ID并添加到全局品种管理
        user_id = group_config['user_id']
        group_id = group_config['group_id']
        project_id = f"user_{user_id}_group_{group_id}"
        add_project_symbols(project_id, all_codes)

        # 6. 启动独立的run.py进程
        logger.info("启动独立的实盘项目进程...")
        process = start_live_project_process(group_dir)

        if process is None:
            raise RuntimeError("实盘项目进程启动失败")

        logger.info(f"实盘组合启动成功, PID: {process.pid}")

        return {
            'success': True,
            'message': '实盘组合启动成功',
            'group_id': group_config.get('group_id'),
            'strategies_count': len(strategies_config),
            'process_id': process.pid,
            'group_dir': group_dir
        }

    except Exception as e:
        logger.error(f"实盘组合运行失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def do_run_live_single(strategy_id: str, strategy_yaml: str, live_config: Dict[str, Any], token: str) -> Dict[str, Any]:
    """
    单策略实盘运行 (实际上是单策略组合)
    """
    try:
        # 构造单策略的组合配置
        group_config = {
            'group_id': 1,  # 默认组合1
            'user_id': live_config.get('user_id'),
            'trading_channel': live_config.get('trading_channel', {})
        }
        
        strategies_config = [{
            'id': strategy_id,
            'yaml': strategy_yaml
        }]
        
        return do_run_live_group(group_config, strategies_config, token)
        
    except Exception as e:
        logger.error(f"单策略实盘运行失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def test_trading_config():
    """
    测试交易通道配置功能
    """
    print("=== 测试交易通道配置功能 ===")

    # 测试获取用户配置
    user_id = 5
    channel_type = 'openctp'

    print(f"测试获取用户 {user_id} 的 {channel_type} 配置...")
    config = get_user_trading_config(user_id, channel_type)

    if config:
        print("✅ 获取配置成功:")
        for key, value in config.items():
            if key == 'password':
                print(f"  {key}: {'*' * len(str(value))}")  # 隐藏密码
            else:
                print(f"  {key}: {value}")
    else:
        print("❌ 未找到配置")

    # 测试模块映射
    print(f"\n测试 {channel_type} 模块映射...")
    module_config = get_trading_channel_module_mapping(channel_type)
    print("✅ 模块配置:")
    for key, value in module_config.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    # 测试代码
    print("Wonder Trader 实盘运行中心")
    print("请通过 strategy_server.py 调用相关功能")

    # 运行测试
    test_trading_config()
