import os
import time
import subprocess
import argparse
import datetime

def log_message(message):
    """打印带时间戳的日志信息"""
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {message}") # 使用更标准的日期时间格式

def process_zip_file(zip_file_path, target_dir):
    """处理单个 ZIP 文件：解压并删除"""
    zip_filename = os.path.basename(zip_file_path)
    log_message(f"检测到 ZIP 文件: {zip_filename}")

    try:
        # 构建解压命令: unzip -o <zip_file_path> -d <target_dir>
        # -o: 覆盖现有文件而不询问
        # -d: 指定解压目录 (这里是脚本所在的目录)
        # 使用绝对路径确保命令在任何工作目录下都能正确找到文件和目标
        command = ['unzip', '-o', os.path.abspath(zip_file_path), '-d', os.path.abspath(target_dir)]
        log_message(f"执行解压命令: {' '.join(command)}")

        # 使用 subprocess.run 执行命令，并检查返回码
        # check=True 会在命令返回非零退出码时抛出 CalledProcessError
        # capture_output=True 捕获标准输出和标准错误
        # text=True 使输出和错误为字符串格式
        result = subprocess.run(command, check=True, capture_output=True, text=True, encoding='utf-8', errors='ignore')

        log_message(f"成功解压: {zip_filename}")
        # print(f"  解压输出:\n{result.stdout}") # 可选：打印解压过程输出

        # --- 解压成功后删除 ZIP 文件 ---
        try:
            log_message(f"删除 ZIP 文件: {zip_filename}")
            os.remove(zip_file_path)
            log_message(f"成功删除 ZIP 文件: {zip_filename}")
        except OSError as e:
            log_message(f"[错误] 删除 ZIP 文件失败 {zip_filename}: {e}")

    except FileNotFoundError:
        # 如果 'unzip' 命令不存在
        log_message(f"[严重错误] 'unzip' 命令未找到。请确保 unzip 工具已安装并位于系统的 PATH 环境变量中。")
        # 可以在这里选择退出脚本或继续尝试其他文件
        # exit(1) # 如果希望在这种情况下停止脚本
    except subprocess.CalledProcessError as e:
        # 如果 unzip 命令执行失败 (例如，zip 文件损坏、权限问题等)
        log_message(f"[错误] 解压文件失败 {zip_filename}:")
        log_message(f"  命令: {' '.join(e.cmd)}")
        log_message(f"  返回码: {e.returncode}")
        # 打印 unzip 工具的错误输出，帮助诊断
        if e.stderr:
            log_message(f"  错误信息: {e.stderr.strip()}")
        if e.stdout:
             log_message(f"  输出信息: {e.stdout.strip()}")
        # 不删除失败的 zip 文件，以便手动检查
    except Exception as e:
        # 捕获其他可能的意外错误
        log_message(f"[错误] 处理文件 {zip_filename} 时发生意外错误: {e}")

def watch_directory(target_dir, interval_seconds):
    """主监控函数"""
    # --- 处理路径并检查有效性 ---
    try:
        # 首先，展开波浪号 ~ (如果存在)
        expanded_dir = os.path.expanduser(target_dir)
        # 然后，获取绝对路径
        abs_target_dir = os.path.abspath(expanded_dir)
    except Exception as e:
        log_message(f"[错误] 处理输入目录路径 '{target_dir}' 时发生错误: {e}")
        return # 无法处理路径，直接退出函数

    log_message(f"准备监控目录: {abs_target_dir} (来自输入: '{target_dir}')")
    log_message(f"监控间隔: {interval_seconds} 秒")

    # 检查路径是否存在且为目录
    if not os.path.isdir(abs_target_dir):
        log_message(f"[错误] 指定的路径不是一个有效的目录: {abs_target_dir}")
        log_message("请确保目录存在并且脚本有权限访问。")
        # --- 打印用法提示 ---
        log_message(f"用法示例: python {os.path.basename(__file__)} <要监控的目录> [-i <间隔秒数>]")
        return # 退出函数

    # --- 主循环 ---
    while True:
        try:
            # 获取目录下所有文件和目录名
            all_entries = os.listdir(abs_target_dir)
            # 筛选出是文件且以 .zip 结尾（不区分大小写）
            zip_files = [
                f for f in all_entries
                if os.path.isfile(os.path.join(abs_target_dir, f)) and f.lower().endswith('.zip')
            ]

            if zip_files:
                log_message(f"发现 {len(zip_files)} 个待处理 ZIP 文件") # 提示发现了文件
                for filename in zip_files:
                    full_path = os.path.join(abs_target_dir, filename)
                    # 调用处理函数 (内部会打印处理日志)
                    process_zip_file(full_path, abs_target_dir)
                log_message("本轮文件处理完成。") # 提示处理结束
            # else: # 如果没有文件，这里不需要打印任何信息

            # --- 统一在 try 块成功结束前打印监控状态 ---
            # 无论本轮是否处理了文件，只要 try 块正常执行到这里，就表示要进入等待状态
            log_message(f"正在监控 {abs_target_dir} ... (下次检查间隔 {interval_seconds}s)")

            # 等待指定时间后再次检查
            time.sleep(interval_seconds)

        except FileNotFoundError:
            log_message(f"[错误] 监控目录丢失: {abs_target_dir}。脚本将退出。")
            break
        except PermissionError:
            log_message(f"[错误] 权限不足，无法读取目录: {abs_target_dir}。请检查权限。")
            log_message(f"将在 {interval_seconds * 5} 秒后重试...")
            # 注意：这里执行 sleep 后会进入下一个循环，并在 try 块开始处重新尝试
            time.sleep(interval_seconds * 5)
            # 错误处理后不应打印 "正在监控..."，因为它还没进入正常的监控等待状态
            continue # 跳过本次循环剩余部分，直接开始下一次尝试
        except KeyboardInterrupt:
             log_message("收到退出信号 (Ctrl+C)，停止监控。")
             break
        except Exception as e:
            log_message(f"[错误] 监控目录时发生意外错误: {e}")
            log_message(f"将在 {interval_seconds} 秒后重试...")
            # 注意：这里执行 sleep 后会进入下一个循环，并在 try 块开始处重新尝试
            time.sleep(interval_seconds)
            # 错误处理后不应打印 "正在监控..."
            continue # 跳过本次循环剩余部分，直接开始下一次尝试


if __name__ == "__main__":
    # --- 命令行参数解析 ---
    parser = argparse.ArgumentParser(description="监控指定目录，自动解压新出现的 ZIP 文件并删除原文件。")
    parser.add_argument("directory", help="要监控的目标目录路径。")
    parser.add_argument("-i", "--interval", type=int, default=5,
                        help="检查新文件的间隔时间（秒）。默认为 5 秒。")

    args = parser.parse_args()

    # --- 启动监控 ---
    watch_directory(args.directory, args.interval)

    log_message("脚本执行结束。") # 添加结束日志