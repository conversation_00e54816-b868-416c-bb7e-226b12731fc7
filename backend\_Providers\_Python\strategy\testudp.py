import socket
import struct
import signal
import sys
import time

def signal_handler(sig, frame):
    print("\n程序被用户中断，正在退出...")
    sock.close()
    sys.exit(0)

# 注册信号处理器，捕获 Ctrl+C
signal.signal(signal.SIGINT, signal_handler)

# 创建 UDP 客户端
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

# 设置超时，这样 recvfrom 不会无限阻塞
sock.settimeout(5.0)  # 5秒超时

# 构造订阅请求
# 根据模拟数据提供器的协议格式构造请求
# 头部必须是 'WTPY'，后面是命令代码和正文长度
# 命令代码 1001 表示订阅
code = "SZSE.ETF.159915"
code_bytes = code.encode('utf-8')
command = 1001  # 订阅命令
body_length = len(code_bytes)

# 构造请求：头部 + 命令 + 正文长度 + 正文
request = b'WTPY' + struct.pack('<I', command) + struct.pack('<I', body_length) + code_bytes

try:
    # 发送请求
    print(f"发送订阅请求: {code} 到 127.0.0.1:3997")
    sock.sendto(request, ('127.0.0.1', 3997))

    # 接收响应
    print("等待响应，按 Ctrl+C 可随时退出...")
    try:
        response, addr = sock.recvfrom(1024)
        print(f"收到来自 {addr} 的响应: {response}")
    except socket.timeout:
        print("接收超时，未收到响应")

    # 持续接收广播数据
    print("开始监听广播数据，按 Ctrl+C 退出...")
    sock.settimeout(None)  # 移除超时，但允许键盘中断

    # 创建一个新的套接字用于接收广播
    broadcast_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    broadcast_sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    broadcast_sock.bind(('0.0.0.0', 9001))  # 绑定到广播端口
    broadcast_sock.settimeout(3.0)  # 设置3秒超时，允许检查键盘中断

    while True:
        try:
            data, addr = broadcast_sock.recvfrom(4096)
            print(f"收到广播数据，长度: {len(data)} 字节")

            # 解析数据
            if len(data) >= 12:  # 至少包含头部(4) + 命令(4) + 长度(4)
                header = data[:4]
                if header == b'WTPY':
                    command = struct.unpack('<I', data[4:8])[0]
                    body_length = struct.unpack('<I', data[8:12])[0]
                    print(f"命令: {command}, 正文长度: {body_length}")

                    if len(data) >= 12 + body_length:
                        body = data[12:12+body_length]
                        try:
                            body_str = body.decode('utf-8')
                            print(f"正文: {body_str}")
                        except:
                            print(f"正文(二进制): {body}")
                    else:
                        print(f"数据不完整，期望长度: {12 + body_length}，实际长度: {len(data)}")
                else:
                    print(f"无效的头部: {header}")
            else:
                print(f"数据太短: {len(data)} < 12")
        except socket.timeout:
            # 超时，继续循环，允许检查键盘中断
            print(".", end="", flush=True)
            continue
        except Exception as e:
            print(f"接收数据时出错: {e}")
            break
except Exception as e:
    print(f"发生错误: {e}")
finally:
    sock.close()
    if 'broadcast_sock' in locals():
        broadcast_sock.close()
    print("程序已退出")