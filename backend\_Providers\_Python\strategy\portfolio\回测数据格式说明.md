# 回测结果数据格式说明

`portfolio_analyzer.py` 中的 `analyze_backtest_results` 函数在成功分析回测结果后，会返回一个包含以下键值对的 Python 字典：

```python
{
    # --- 核心绩效指标 ---
    "total_return_pct": float,  # 百分比总回报率 (例如: 25.5 代表 25.5%)
    "max_drawdown_pct": float,  # 百分比最大回撤 (始终为正数或零, 例如: 15.2 代表 15.2%)
    "annualized_return_pct": float | None, # 百分比年化回报率 (例如: 12.8 代表 12.8%), 如果无法计算则为 None
    "sharpe_ratio": float | None,      # 夏普比率, 如果无法计算则为 None
    
    # --- 交易统计指标 ---
    "total_trades": int,               # 总交易次数
    "winning_trades": int,             # 盈利交易次数
    "losing_trades": int,              # 亏损交易次数 (包括盈亏为0的交易)
    "win_rate_pct": float,             # 百分比胜率 (盈利交易次数 / 总交易次数 * 100)
    "profit_factor": float | str | None, # 盈亏比 (总盈利 / 总亏损), 如果无亏损则为 'inf', 如果无盈利也无亏损则为 None
    "average_profit_per_trade": float,   # 平均每笔交易利润
    "average_profit_winning_trade": float, # 平均盈利交易利润
    "average_loss_losing_trade": float,  # 平均亏损交易亏损 (始终为正数或零)
    
    # --- 辅助信息 ---
    "equity_curve_data": list[dict], # 资金曲线数据列表, 用于绘图
                                       # 每个字典格式为: {"date": "YYYY-MM-DD", "equity": float}
    "backtest_period_start": str | None, # 回测开始日期 ("YYYY-MM-DD"), 如果无法确定则为 None
    "backtest_period_end": str | None,   # 回测结束日期 ("YYYY-MM-DD"), 如果无法确定则为 None
    "initial_capital": float           # 回测使用的初始资金 (注意: 此处是原始值，未经过 /10000 处理)
}
```

**注意:**

*   所有百分比指标 (`_pct` 后缀) 都是直接的百分比数值，而不是小数 (例如，25.5 代表 25.5%)。
*   如果分析过程中发生严重错误（例如，无法读取 `funds.csv`），函数将返回一个只包含 `"error"` 键的字典，其值为错误描述字符串:
    ```python
    {"error": "错误描述信息..."}
    ```
*   金额相关的指标（如 `average_profit_per_trade` 等）是基于 `closes.csv` 中 `profit` 列除以 10000 后的值计算的。
*   `initial_capital` 反映的是 `btenv.json` 中或代码中设置的初始资金**原始值**。
*。
*   资金曲线数据 (`equity_curve_data`) 中的 `equity` 值是根据**原始** `initial_capital` 加上从 `funds.csv` 读取并**调整后**的 `closeprofit` 和 `positionprofit` (或 `dynbalance`) 计算得出的。 