# 多因子轮动策略配置规范说明 (`index.yaml`)

本文档旨在说明用于 `MultiFactorsCTA.py` 策略的 `index.yaml` 配置文件的结构和参数规范。

## 概述

`index.yaml` 文件使用 YAML 格式定义了多因子轮动策略的具体行为，包括交易品种、因子计算、排序规则、买卖条件、调仓逻辑和回测参数等。

## 顶层配置

这些参数直接位于 `index.yaml` 的顶层。

*   `strategy_id` (字符串, 必选):
    策略在界面或日志中显示的名称。
    ```yaml
    strategy_id: "我的多因子策略"
    ```

*   `trading_type` (字符串, 可选):
    交易标的的类型，可能影响交易逻辑或数据获取。常见值如 `"etf"`, `"stock"`, `"futures"`。
    ```yaml
    trading_type: "etf"
    ```

*   `universe` (列表, 必选):
    包含所有参与轮动计算的品种代码的列表。代码需要使用 **WonderTrader 标准格式** (例如 `SSE.ETF.510300`, `SZSE.STK.000001`, `CFFEX.IF.HOT`)。
    ```yaml
    universe:
      - SSE.ETF.518880
      - SZSE.ETF.159915
      - SSE.ETF.513100
    ```

*   `benchmark` (字符串, 可选):
    用于策略绩效比较的基准品种代码（WonderTrader 标准格式）。
    ```yaml
    benchmark: SSE.ETF.510300
    ```

*   `is_for_stk` (布尔值, 可选, 默认 `false`):
    指示是否按股票模式处理交易单位（例如，买卖数量必须是 100 的整数倍）。对于 ETF 或 股票，通常设置为 `true`。
    ```yaml
    is_for_stk: true
    ```

## 排序规则 (`order_by`)

此部分定义了如何在每个调仓周期对 `universe` 中的品种进行排序。

*   `formula` (字符串, 必选):
    用于计算每个品种排序得分的**因子公式**。公式可以包含一个或多个基础因子调用（如 `roc(close, 10)`）、数学运算符 (`+`, `-`, `*`, `/`) 和常数。
    ```yaml
    order_by:
      formula: "trend_score(close,26)*0.4 + (roc(close,5)+roc(close,10))*0.2 + ma(volume,5)/ma(volume,20)"
      # 或者简单公式
      # formula: "roc(close, 20)"
    ```
    **注意:** 公式中使用的所有基础因子（如 `trend_score`, `roc`, `ma`）必须在 `factors/` 目录下有对应的 `.py` 实现文件，且包含符合接口规范的 `calculate_value` 函数。

*   `sort_direction` (字符串, 可选, 默认 `descending`):
    排序方向。
    *   `descending`: 按得分从高到低排序。
    *   `ascending`: 按得分从低到高排序。
    ```yaml
    order_by:
      formula: "roc(close, 20)"
      sort_direction: descending
    ```

## 调仓与执行规则

定义策略的调仓频率和选股数量。

*   `rebalance_interval` (字符串, 必选):
    调仓频率。支持的值：
    *   `daily`: 每个交易日调仓。
    *   `weekly`: 每周的第一个交易日调仓。
    *   `monthly`: 每月的第一个交易日调仓。
    *   `Xd`: 每 X 个交易日调仓 (例如 `5d` 表示每 5 个交易日)。
    ```yaml
    rebalance_interval: daily
    ```

*   `top_n` (整数, 必选):
    根据 `order_by` 排序后，目标持有的品种数量上限。
    ```yaml
    top_n: 3 # 目标持有得分最高的3只
    ```

*   `weighting_scheme` (字符串, 可选, 默认 `equal`):
    目标持仓的权重分配方案。
    *   `equal`: 对最终选出的目标品种进行等权重分配资金。
    *   *(未来可能支持其他方案)*
    ```yaml
    weighting_scheme: equal
    ```

## 买入规则 (`buy_rules`)

可选部分，用于在 TopN 初选后进一步筛选买入目标。

*   `formulas` (列表, 可选):
    一个包含 0 条或多条买入条件判断公式的**字符串列表**。每个公式的评估结果应为布尔值（True/False）。
    ```yaml
    buy_rules:
      formulas:
        - "roc(close, 10) > 0"          # 规则1：10日涨幅大于0
        - "ma(close, 5) > ma(close, 20)" # 规则2：5日均线上穿20日均线
    ```

*   `at_least_count` (整数, 可选):
    表示一个品种需要**至少**满足 `formulas` 列表中的多少条规则才算通过买入筛选。
    *   如果**未指定**此参数，则默认需要满足 `formulas` 中的**所有**规则。
    *   如果 `formulas` 为空或未定义，则相当于没有买入规则筛选。
    ```yaml
    buy_rules:
      formulas:
        - "roc(close, 10) > 0"
        - "ma(close, 5) > ma(close, 20)"
      at_least_count: 1 # 满足上述两个规则中的至少一个即可
    ```
    *   **执行逻辑:** 先按 `order_by` 选出 TopN，然后对这 TopN 中的每个品种，检查其满足了 `buy_rules.formulas` 中多少条规则，只有满足数量达到 `buy_rules.at_least_count` 的品种才会最终被买入或持有。

## 卖出规则 (`sell_rules`)

可选部分，用于定义触发主动卖出当前持仓的条件。

*   `formulas` (列表, 可选):
    一个包含 0 条或多条卖出条件判断公式的**字符串列表**。每个公式的评估结果应为布尔值（True/False）。
    ```yaml
    sell_rules:
      formulas:
        - "roc(close, 18) > 0.15" # 规则1：18日涨幅超过15% (止盈)
        - "ma(close, 5) < ma(close, 60)" # 规则2：5日线下穿60日线 (止损)
    ```

*   `at_least_count` (整数, 可选):
    表示一个**当前持有**的品种需要**至少**满足 `formulas` 列表中的多少条规则就会被强制卖出。
    *   如果**未指定**此参数，则默认需要满足 `formulas` 中的**所有**规则才能触发卖出。
    *   如果 `formulas` 为空或未定义，则没有基于条件的显式卖出规则。
    ```yaml
    sell_rules:
      formulas:
        - "roc(close, 18) > 0.15"
        - "ma(close, 5) < ma(close, 60)"
      at_least_count: 1 # 满足上述两个规则中的任何一个就卖出
    ```
    *   **执行逻辑:** 对每个当前持有的品种，独立检查其是否满足了 `sell_rules.formulas` 中至少 `sell_rules.at_least_count` 条规则。如果满足，则触发卖出。**同时**，如果一个持仓品种在本轮调仓中跌出了 TopN 排序名单，也会被卖出。

## 数据参数

定义因子计算所需的数据频率和长度。

*   `data_freq` (字符串, 必选):
    用于计算因子的 K 线数据频率。需要与 WonderTrader 支持的周期字符串一致 (例如 `day`, `1h`, `30m`, `5m` 等)。
    ```yaml
    data_freq: day
    ```

*   `bar_count` (整数, 必选):
    每次计算因子时，需要获取的 K 线数量。这个值**必须**大于等于所有 `order_by`, `buy_rules`, `sell_rules` 公式中因子计算所需的最大周期 (`period`)。建议在此基础上增加少量缓冲（例如 +5 或 +10）。
    ```yaml
    # 假设公式中最大 period 是 60 (例如 ma(close, 60))
    bar_count: 70
    ```

## 数据存储配置 (`data`) (可选)

如果不由外部框架统一管理，可以在此配置数据源信息。

*   `path` (字符串): 数据存储的相对或绝对路径。
*   `mode` (字符串): 数据格式，例如 `csv` 或 `dsb`。

```yaml
data:
  path: ../../storage/ # 相对于 index.yaml 文件所在目录
  mode: csv
```

## 回测参数 (`backtest`)

定义回测时的特定参数。

*   `stime` (整数或字符串): 回测开始时间，格式 `YYYYMMDDHHMM` (例如 `202001010930`)。
*   `etime` (整数或字符串, 可选): 回测结束时间，格式 `YYYYMMDDHHMM`。如果留空，通常使用当前系统时间。
*   `initial_capital` (浮点数): 回测初始资金。
*   `commission_rate` (浮点数): 单边交易手续费率。
*   `slippage_rate` (浮点数): 单边交易滑点比率。

```yaml
backtest:
  stime: 202101010930
  etime: 202312311500
  initial_capital: 1000000.0
  commission_rate: 0.00025
  slippage_rate: 0.0001
```

