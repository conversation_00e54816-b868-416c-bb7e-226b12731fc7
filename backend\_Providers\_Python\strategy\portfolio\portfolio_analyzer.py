import pandas as pd
import numpy as np
import os
import json

def calculate_metrics(equity_curve: pd.Series, closes_df: pd.DataFrame, risk_free_rate: float = 0.02):
    """
    Calculates performance metrics from equity curve and closed trades data.

    Args:
        equity_curve (pd.Series): Series of equity values, indexed by date/time.
        closes_df (pd.DataFrame): DataFrame containing closed trade information (requires 'profit' column).
        risk_free_rate (float): Annual risk-free rate for Sharpe ratio calculation.

    Returns:
        dict: Dictionary containing calculated performance metrics.
    """
    metrics = {}

    # --- Equity Curve Metrics ---
    if equity_curve.empty or equity_curve.iloc[0] == 0: # Avoid division by zero if start equity is 0
        print("[Analyzer] Warning: Equity curve is empty or starts at zero. Cannot calculate equity-based metrics.")
        # Return default/error metrics
        metrics['total_return_pct'] = 0.0
        metrics['max_drawdown_pct'] = 0.0
        metrics['annualized_return_pct'] = 0.0
        metrics['sharpe_ratio'] = None
        # Continue to trade metrics
    else:
        total_return = (equity_curve.iloc[-1] / equity_curve.iloc[0]) - 1
        metrics['total_return_pct'] = round(total_return * 100, 2)

        # Calculate Drawdown
        cumulative_max = equity_curve.cummax()
        drawdown = (equity_curve - cumulative_max) / cumulative_max
        # Replace potential -inf if cumulative_max is 0
        drawdown = drawdown.replace([np.inf, -np.inf], 0)
        max_drawdown = drawdown.min()
        metrics['max_drawdown_pct'] = round(abs(max_drawdown) * 100, 2) if pd.notna(max_drawdown) else 0.0

        # Calculate Annualized Return (requires date index)
        if isinstance(equity_curve.index, pd.DatetimeIndex):
            years = (equity_curve.index[-1] - equity_curve.index[0]).days / 365.25
            if years > 0:
                # Compounded Annual Growth Rate (CAGR)
                annualized_return = ((equity_curve.iloc[-1] / equity_curve.iloc[0]) ** (1 / years)) - 1
                metrics['annualized_return_pct'] = round(annualized_return * 100, 2)
            else:
                 metrics['annualized_return_pct'] = 0.0 # Avoid division by zero for short periods
        else:
            print("[Analyzer] Warning: Equity curve index is not DatetimeIndex. Cannot calculate Annualized Return accurately.")
            metrics['annualized_return_pct'] = None

        # Calculate Sharpe Ratio (simplified, assumes daily data for volatility calculation if possible)
        returns = equity_curve.pct_change().dropna()
        if not returns.empty and len(returns) > 1:
            # Assuming 252 trading days per year for annualizing volatility
            volatility = returns.std() * np.sqrt(252)
            # Use the calculated annualized return
            calculated_annual_return = metrics.get('annualized_return_pct')
            if calculated_annual_return is not None and volatility > 0:
                annualized_excess_return = calculated_annual_return / 100 - risk_free_rate
                sharpe_ratio = annualized_excess_return / volatility
                metrics['sharpe_ratio'] = round(sharpe_ratio, 2)
            else:
                metrics['sharpe_ratio'] = None # Cannot calculate if volatility is zero or return is None
        else:
             metrics['sharpe_ratio'] = None

    # --- Trade Metrics (Common part for both cases) ---
    if closes_df.empty:
        print("[Analyzer] Warning: Closed trades data is empty. Cannot calculate trade-based metrics.")
        metrics.update({
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate_pct': 0.0,
            'profit_factor': None,
            'average_profit_per_trade': 0.0,
            'average_profit_winning_trade': 0.0,
            'average_loss_losing_trade': 0.0
        })
    else:
        metrics['total_trades'] = len(closes_df)
        winning_trades = closes_df[closes_df['profit'] > 0]
        losing_trades = closes_df[closes_df['profit'] <= 0] # Include zero profit trades as non-winning

        metrics['winning_trades'] = len(winning_trades)
        metrics['losing_trades'] = len(losing_trades)

        if metrics['total_trades'] > 0:
            win_rate = metrics['winning_trades'] / metrics['total_trades']
            metrics['win_rate_pct'] = round(win_rate * 100, 2)
        else:
            metrics['win_rate_pct'] = 0.0

        total_profit = winning_trades['profit'].sum()
        total_loss = abs(losing_trades['profit'].sum())

        if total_loss > 0:
            profit_factor = total_profit / total_loss
            metrics['profit_factor'] = round(profit_factor, 2)
        else:
            # Handle cases with no losses (infinite profit factor)
            metrics['profit_factor'] = 'inf' if total_profit > 0 else None

        if metrics['total_trades'] > 0:
            metrics['average_profit_per_trade'] = round(closes_df['profit'].mean(), 2)
        else:
            metrics['average_profit_per_trade'] = 0.0

        if metrics['winning_trades'] > 0:
             metrics['average_profit_winning_trade'] = round(winning_trades['profit'].mean(), 2)
        else:
             metrics['average_profit_winning_trade'] = 0.0

        if metrics['losing_trades'] > 0:
             metrics['average_loss_losing_trade'] = round(abs(losing_trades['profit'].mean()), 2)
        else:
             metrics['average_loss_losing_trade'] = 0.0

    return metrics

# 用trades.csv数据复现资金权益曲线
def generate_equity_curve_from_trades(trades_file, initial_cash=100000):
    """
    用trades.csv数据复现资金权益曲线，返回与funds_df生成的equity_curve一致的pd.Series（索引为pd.DatetimeIndex，值为权益）
    """
    import pandas as pd
    import datetime

    trades = pd.read_csv(trades_file)
    # 按时间排序
    trades = trades.sort_values('time')
    # 只保留有用字段
    trades = trades[['time', 'code', 'direct', 'action', 'price', 'qty', 'fee']]
    # 资金和持仓
    cash = initial_cash
    positions = {}  # code -> qty
    last_price = {} # code -> last price
    equity_curve = []

    for idx, row in trades.iterrows():
        code = row['code']
        price = row['price']
        qty = row['qty']
        fee = row['fee']
        action = row['action']
        direct = row['direct']
        time = row['time']

        # 扣除手续费
        cash -= fee

        # 买入/卖出
        if action == 'OPEN':
            cash -= price * qty
            positions[code] = positions.get(code, 0) + qty
        elif action == 'CLOSE':
            cash += price * qty
            positions[code] = positions.get(code, 0) - qty
        last_price[code] = price

        # 计算当前总权益
        equity = cash
        for c, q in positions.items():
            if q != 0 and c in last_price:
                equity += q * last_price[c]
        equity_curve.append({'time': time, 'equity': equity})

    # 转为DataFrame
    equity_df = pd.DataFrame(equity_curve)
    # 将time列转为datetime，支持YYYYMMDDHHMM和YYYYMMDD
    equity_df['date_parsed'] = pd.to_datetime(equity_df['time'].astype(str), format='%Y%m%d%H%M', errors='coerce')
    if equity_df['date_parsed'].isna().any():
        equity_df['date_parsed'] = pd.to_datetime(equity_df['time'].astype(str), format='%Y%m%d', errors='coerce')
    equity_df = equity_df.dropna(subset=['date_parsed'])
    equity_df = equity_df.sort_values('date_parsed')
    # 返回Series，索引为date_parsed，值为equity
    equity_series = pd.Series(equity_df['equity'].values, index=equity_df['date_parsed'])
    equity_series = equity_series.sort_index()
    return equity_series

def analyze_backtest_results(output_dir: str, trading_type: str) -> dict:
    """
    Analyzes the backtest output files from a given directory.

    Args:
        output_dir (str): The absolute path to the strategy's output directory
                          (e.g., '/path/to/outputs_bt/strategy_id').
        trading_type (str): The type of the traded instrument ('etf', 'stock', 'option').

    Returns:
        dict: A dictionary containing the calculated performance metrics and trade history.
              Returns {"error": "..."} if essential files are missing or unreadable.
    """
    print(f"[Analyzer] Analyzing backtest results in: {output_dir} for trading type: {trading_type}")

    # === 新增：根据trading_type查找commodity.json的digits ===
    digits = 4  # 默认4位
    equity_curve = None

    try:
        # 查找backend目录
        def find_backend_dir(start_dir, max_depth=10):
            current = start_dir
            for _ in range(max_depth):
                if os.path.basename(current) == 'backend':
                    return current
                parent = os.path.dirname(current)
                if parent == current:
                    break
                current = parent
            return None
        start_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = find_backend_dir(start_dir)
        if backend_dir:
            commodity_path = os.path.join(backend_dir, '_Providers', '_Python', 'strategy', 'commodity.json')
            if os.path.exists(commodity_path):
                with open(commodity_path, 'r', encoding='utf-8') as f:
                    commodity_cfg = json.load(f)
                    # trading_type 可能为 etf/stock/crypto，需大写
                    key = trading_type.upper()
                    if key in commodity_cfg and 'digits' in commodity_cfg[key]:
                        digits = int(commodity_cfg[key]['digits'])
    except Exception as e:
        print(f"[Analyzer] [Warning] 获取commodity.json精度失败: {e}，使用默认4位")
    print(f"[Analyzer] 成交明细价格精度设置为: {digits} 位 (根据{trading_type})")

    # 根据 trading_type 确定 volscale 除数
    # 现在约定股票和etf都是1，因为在 common 的定义那里统一使用 stk_comms.json，里面股票和etf都是1
    # 因为一个python进程初始化一种类型的引擎以后，只会按照最早一次的comms 文件规范去解析，之后意图变换是无效的
    if trading_type.lower() == "etf":
        # volscale_divisor = 10000.0
        volscale_divisor = 1
    elif trading_type.lower() == "stock":
        volscale_divisor = 1.0
    elif trading_type.lower() == "crypto":
        volscale_divisor = 1.0 # 虚拟货币的 volscale 为 1
    elif trading_type.lower() == "option":
        volscale_divisor = 1.0 # 假设期权的 volscale 为 1，如果不同需要调整
    else:
        print(f"[Analyzer] Warning: Unknown trading_type '{trading_type}'. Assuming volscale divisor of 1.0.")
        volscale_divisor = 1.0
    
    print(f"[Analyzer] 使用的成交量放大倍数：{volscale_divisor} for amount calculations.")

    funds_file = os.path.join(output_dir, 'funds.csv')
    closes_file = os.path.join(output_dir, 'closes.csv')
    trades_file = os.path.join(output_dir, 'trades.csv')
    btenv_file = os.path.join(output_dir, 'btenv.json')
    # Optional: Add signals_file, etc. if needed later
    # signals_file = os.path.join(output_dir, 'signals.csv')

    trade_history = [] # Initialize trade history list
    isDayAndOver = True # 是否是日频交易

    # --- Generate Trade History from trades.csv (Priority) ---
    if os.path.exists(trades_file):
        print(f"[Analyzer] Found trades.csv at {trades_file}. Using it for trade history.")
        try:
            trades_df = pd.read_csv(trades_file)
            print(f"[Analyzer] Successfully loaded trades.csv ({len(trades_df)} rows). Columns: {trades_df.columns.tolist()}")
            
            # Define required columns for full history
            required_hist_cols = {'time', 'code', 'action', 'price', 'qty'}
            
            if required_hist_cols.issubset(trades_df.columns):
                hist_df = trades_df.copy()
                
                # 1. Parse Date (assuming time is YYYYMMDDHHMM integer/string)
                try:
                    hist_df['date'] = pd.to_datetime(hist_df['time'].astype(str), format='%Y%m%d%H%M', errors='coerce')
                    # Handle potential extra digits if format is YYYYMMDDHHMMSS
                    if hist_df['date'].isna().any(): 
                        print("[Analyzer] Warning: Some dates failed initial parsing with %Y%m%d%H%M, trying %Y%m%d%H%M%S")
                        hist_df['date'] = pd.to_datetime(hist_df['time'].astype(str), format='%Y%m%d%H%M%S', errors='coerce')
                    
                    hist_df = hist_df.dropna(subset=['date'])
                except Exception as date_e:
                    print(f"[Analyzer] Warning: Could not parse 'time' column in trades.csv: {date_e}. Trade history dates might be incomplete.")
                    hist_df['date'] = pd.NaT
                
                # 2. Action Mapping (OPEN -> Buy, CLOSE -> Sell)
                action_map = {'OPEN': 'Buy', 'CLOSE': 'Sell'}
                hist_df['action'] = hist_df['action'].map(action_map).fillna(hist_df['action']) # Keep original if not in map

                # 3. Symbol & Name
                hist_df['symbol'] = hist_df['code']
                hist_df['name'] = hist_df['code']

                # 4. Price (Assuming NO scaling needed for trades.csv price)
                hist_df['price'] = pd.to_numeric(hist_df['price'], errors='coerce')

                # 5. Quantity & Amount (Assuming NO scaling needed for trades.csv qty)
                hist_df['qty'] = pd.to_numeric(hist_df['qty'], errors='coerce')
                hist_df['amount'] = hist_df['price'] * hist_df['qty']
                hist_df = hist_df.dropna(subset=['price', 'qty', 'amount']) # Drop if price/qty non-numeric

                # 6. Select, Sort, Limit
                hist_df = hist_df[['date', 'action', 'symbol', 'name', 'price', 'amount']]
                hist_df = hist_df.sort_values(by='date', ascending=False)
                
                # 7. Convert to list of dicts (and format date)
                hist_df['date'] = hist_df['date'].apply(lambda x: x.isoformat() if pd.notna(x) else None)
                # Round numeric values for cleaner output
                hist_df['price'] = hist_df['price'].round(digits) # 动态精度
                hist_df['amount'] = hist_df['amount'].round(2) # 保持原有
                trade_history = hist_df.to_dict('records')
                print(f"[Analyzer] Generated trade history with {len(trade_history)} records from trades.csv.")

                # 通过读取trades.csv的日期范围，判断是否是日频交易
                # 获取前4个不同的时间，计算时间间隔，只要有2个间隔小于4小时，则认为不是日频交易
                # --- 日频判断逻辑 ---
                if os.path.exists(trades_file):
                    try:
                        trades_df = pd.read_csv(trades_file)
                        times = trades_df['time'].dropna().unique()
                        times = list(times)
                        import datetime
                        if len(times) >= 4:
                            dt_list = [datetime.datetime.strptime(str(t), '%Y%m%d%H%M') for t in times[:4]]
                            intervals = [(dt_list[i+1] - dt_list[i]).total_seconds() for i in range(3)]
                            short_count = sum(ival < 4*3600 for ival in intervals)
                            if short_count >= 2:
                                isDayAndOver = False
                                print(f"[Analyzer] 检测到日线以下级别交易，将使用trades.csv生成权益曲线")
                            else:
                                print(f"[Analyzer] 检测到日线级别交易，将使用funds.csv生成权益曲线")
                    except Exception as e:
                        print(f"[策略回测] trades.csv日频判断异常: {e}")

                # 只有在日线以下级别时，才使用trades.csv生成权益曲线
                if not isDayAndOver:
                    print(f"[Analyzer] 使用trades.csv生成权益曲线（日线以下级别）")
                    equity_curve = generate_equity_curve_from_trades(trades_file, initial_cash=100000)
                else:
                    print(f"[Analyzer] 跳过trades.csv权益曲线生成，将使用funds.csv（日线级别）")
                    equity_curve = None

            else:
                missing_cols = required_hist_cols - set(trades_df.columns)
                print(f"[Analyzer] Warning: Cannot generate full trade history from trades.csv. Missing columns: {missing_cols}")
                trade_history = [] # Fallback to empty history

        except Exception as e:
            print(f"[Analyzer] Error reading or processing trades.csv: {e}. Trade history might be incomplete.")
            trade_history = [] # Fallback to empty history
    else:
        print(f"[Analyzer] Warning: trades.csv not found at {trades_file}. Trade history will be empty.")
        trade_history = []
    # --- End Trade History Generation ---


    # --- Load Closes Data (Still needed for metrics calculation) ---
    closes_df = pd.DataFrame() # Default to empty
    if not os.path.exists(closes_file):
        print(f"[Analyzer] Warning: closes.csv not found at {closes_file}. Trade metrics calculation might be affected.")
    else:
         try:
             closes_df = pd.read_csv(closes_file)
             print(f"[Analyzer] Successfully loaded closes.csv ({len(closes_df)} rows) for metrics calculation.")
             if 'profit' not in closes_df.columns:
                  print("[Analyzer] Error: 'profit' column missing in closes.csv. Cannot calculate trade metrics.")
                  closes_df = pd.DataFrame() # Reset to empty if profit is missing
             else:
                 # --- 金额处理: closes.csv 中所有金额字段除以 volscale_divisor ---
                 money_cols_closes = ['profit', 'maxprofit', 'maxloss', 'totalprofit']
                 for col in money_cols_closes:
                     if col in closes_df.columns:
                         # 类型安全处理
                         closes_df[col] = pd.to_numeric(closes_df[col], errors='coerce').fillna(0)
                         orig_sample = closes_df[col].iloc[0] if not closes_df.empty else 'N/A'
                         closes_df[col] = closes_df[col] / volscale_divisor # 使用动态除数
                         scaled_sample = closes_df[col].iloc[0] if not closes_df.empty else 'N/A'
                         print(f"[Analyzer] 交易金额单位转换 (/{volscale_divisor}): '{col}' 列已处理，样例值: {orig_sample} → {scaled_sample}")
                 # --- 处理结束 ---

         except Exception as e:
             print(f"[Analyzer] Error reading or processing closes.csv for metrics: {e}. Trade metrics calculation might be affected.")
             closes_df = pd.DataFrame() # Ensure it's empty on error

    # --- Load Funds Data and Prepare Equity Curve ---
    if not os.path.exists(funds_file):
        print(f"[Analyzer] Error: funds.csv not found at {funds_file}. Cannot calculate performance.")
        return {"error": f"funds.csv not found in output directory: {output_dir}"}

    try:
        funds_df = pd.read_csv(funds_file)
        print(f"[Analyzer] 加载 funds.csv 成功: 行数={funds_df.shape[0]}，日期范围: {funds_df['date'].min()}-{funds_df['date'].max()}")

        # --- 金额处理: funds.csv 所有金额字段除以 volscale_divisor --- 
        money_cols_funds = ['closeprofit', 'positionprofit', 'dynbalance', 'fee']
        for col in money_cols_funds:
            if col in funds_df.columns:
                # 类型安全处理
                funds_df[col] = pd.to_numeric(funds_df[col], errors='coerce').fillna(0)
                # 样本值记录(转换前)
                sample_before = funds_df[col].iloc[10] if len(funds_df) > 10 else funds_df[col].iloc[0] if not funds_df.empty else 'N/A'
                # 单位转换
                funds_df[col] = funds_df[col] / volscale_divisor # 使用动态除数
                # 样本值记录(转换后)
                sample_after = funds_df[col].iloc[10] if len(funds_df) > 10 else funds_df[col].iloc[0] if not funds_df.empty else 'N/A'
                print(f"[Analyzer] 资金单位转换 (/{volscale_divisor}): '{col}' 列已处理，样例值: {sample_before} → {sample_after}")
        # --- 处理结束 ---

        # --- 强制固定初始资金为10万元 ---
        initial_capital = 100000.0  # 不从环境配置读取，直接固定为10万元
        print(f"[Analyzer] 强制初始资金: {initial_capital} 元")
        
        # --- 调试日期处理 --- 
        # 保存原始第一行日期用于后续比较
        original_first_date_str = str(funds_df['date'].iloc[0]) if not funds_df.empty else 'N/A'
        
        # Use errors='coerce' to handle potential unparseable dates gracefully
        funds_df['date_parsed'] = pd.to_datetime(funds_df['date'], format='%Y%m%d', errors='coerce')
        print(f"[Analyzer] 日期解析: Shape={funds_df.shape}")
        
        # 检查有多少日期解析失败 (变为 NaT)
        nat_count = funds_df['date_parsed'].isna().sum()
        print(f"[Analyzer] 日期解析失败数量: {nat_count}")

        # 删除解析失败的行
        original_rows = len(funds_df)
        funds_df = funds_df.dropna(subset=['date_parsed']) 
        rows_after_drop = len(funds_df)
        print(f"[Analyzer] 删除无效日期行: 删除前={original_rows}, 删除后={rows_after_drop}, 差异={original_rows - rows_after_drop}")
        
        if funds_df.empty:
            print("[Analyzer] Error: No valid dates found in funds.csv after parsing and dropna.")
            return {"error": "No valid dates found in funds.csv after cleaning."} 
        
        # 检查清理后的日期范围
        print(f"[Analyzer] 有效日期范围: 从 {funds_df['date_parsed'].min()} 到 {funds_df['date_parsed'].max()}")

        # --- 基于 WonderTrader 逻辑重新计算权益曲线 ---
        if 'closeprofit' in funds_df.columns and 'positionprofit' in funds_df.columns:
             # 确保 closeprofit 和 positionprofit 已缩小10000倍
             
             # --- 修正：移除错误的二次累加 --- 
             # 1. 计算累计平仓盈亏 (不再需要单独计算，closeprofit 本身就是累计值)
             # funds_df['cumulative_closeprofit'] = funds_df['closeprofit'].cumsum() 
             
             # 2. 计算权益 = 初始资金(10万) + 累计平仓盈亏 (closeprofit) + 当日持仓盈亏 (positionprofit)
             funds_df['equity'] = initial_capital + funds_df['closeprofit'] + funds_df['positionprofit']
             print(f"[Analyzer] 权益计算 (WT逻辑修正): 初始资金({initial_capital}) + 累计平仓盈亏(closeprofit列) + 当前持仓盈亏(positionprofit列)")
             
             # 详细验证首尾和中间点
             first_row_equity = funds_df['equity'].iloc[0]
             last_row_equity = funds_df['equity'].iloc[-1]
             mid_index = len(funds_df) // 2
             mid_row_equity = funds_df['equity'].iloc[mid_index]
             print(f"[Analyzer] 权益曲线关键点校验: 首日={first_row_equity:.2f}, 中间日(索引{mid_index})={mid_row_equity:.2f}, 末日={last_row_equity:.2f}")
             # 检查首日权益是否接近10万
             if not (initial_capital * 0.99 < first_row_equity < initial_capital * 1.01): # 更严格的校验 +/- 1%
                 print(f"[Analyzer] 警告: 首日计算权益({first_row_equity:.2f})与初始资金({initial_capital})偏差较大！请检查 funds.csv 首行数据或计算逻辑。")

        # 暂时注释掉 dynbalance 的回退逻辑，优先确保主逻辑正确
        # elif 'dynbalance' in funds_df.columns:
        #      # ... (之前的 dynbalance 回退逻辑) ...
        else:
            print("[Analyzer] Error: Cannot determine equity curve. Missing required columns ('closeprofit'/'positionprofit' or 'dynbalance') in funds.csv")
            return {"error": "Cannot determine equity curve from funds.csv columns."}

        # Set index to date (使用 date_parsed)
        if 'equity_curve' in locals() and equity_curve is not None:
            print("[Analyzer] equity_curve已存在，跳过funds_df生成权益曲线的计算。")
        else:
            # ---------权益曲线的部分---------
            try:
                equity_curve = funds_df.set_index('date_parsed')['equity']
                equity_curve = equity_curve.sort_index() # Ensure chronological order
                print(f"[Analyzer] 权益曲线创建成功: 点数={len(equity_curve)}, 起始={equity_curve.index.min()}, 结束={equity_curve.index.max()}")
            except Exception as e:
                print(f"[Analyzer] Error processing date column or setting index: {e}")
                return {"error": f"Error processing date column in funds.csv: {e}"}

            # Add initial capital row (逻辑不变，但基于解析后的日期比较)
            # 需要用解析后的第一个有效日期
            first_valid_parsed_date = funds_df['date_parsed'].iloc[0]
            if not equity_curve.empty and equity_curve.index[0] > first_valid_parsed_date:
                start_date = first_valid_parsed_date - pd.Timedelta(days=1)
                print(f"[Analyzer] 添加初始资金记录: {initial_capital} 元 (日期: {start_date})")
                initial_series = pd.Series([initial_capital], index=[start_date])
                equity_curve = pd.concat([initial_series, equity_curve])
                equity_curve = equity_curve.sort_index() 
                print(f"[Analyzer] 添加初始记录后权益曲线: 点数={len(equity_curve)}, 起始={equity_curve.index.min()}, 结束={equity_curve.index.max()}")

            # ---------权益曲线的部分结束---------

        # Calculate metrics (基于最终的 equity_curve)
        print(f"[Analyzer] 开始计算绩效指标...")
        metrics = calculate_metrics(equity_curve, closes_df)
        print(f"[Analyzer] 绩效指标计算完成: {metrics}")

        # --- Calculate Metrics --- 
        # Pass the original closes_df for metrics calculation (已经缩放过)
        metrics_df = closes_df
        performance_metrics = calculate_metrics(equity_curve, metrics_df)
        print(f"[Analyzer] 绩效指标计算完成: {performance_metrics}")


        # --- Prepare Final Result Dictionary ---
        results = performance_metrics.copy() # Start with calculated metrics

        # Add backtest period info
        results['backtest_period_start'] = equity_curve.index.min().strftime('%Y-%m-%d') if isinstance(equity_curve.index, pd.DatetimeIndex) else None
        results['backtest_period_end'] = equity_curve.index.max().strftime('%Y-%m-%d') if isinstance(equity_curve.index, pd.DatetimeIndex) else None
        results['initial_capital'] = initial_capital # Add initial capital used

        # --- Generate Equity Curve Thumbnail (智能采样) ---
        # 检测数据频率（通过时间间隔）
        total_points = len(equity_curve)
        is_intraday = False

        if total_points >= 2 and isinstance(equity_curve.index, pd.DatetimeIndex):
            try:
                time_diff = equity_curve.index[1] - equity_curve.index[0]
                time_diff_seconds = time_diff.total_seconds()

                # 判断是否为日内数据（小于1天）
                if time_diff_seconds < 86400:  # 小于24小时
                    is_intraday = True
                    print(f"[Analyzer] 检测到日内数据，时间间隔: {time_diff_seconds}秒")
                else:
                    print(f"[Analyzer] 检测到日线及以上数据，时间间隔: {time_diff_seconds}秒")
            except Exception as e:
                print(f"[Analyzer] 检测数据频率时出错: {e}, 默认为日线数据")

        # ---------缩略图的生成---------
        # 固定使用100个点的采样（足够显示趋势）
        thumbnail_points = 100
        if total_points > thumbnail_points:
            step = total_points // thumbnail_points
            thumbnail_data = equity_curve.iloc[::step].reset_index()
        else:
            thumbnail_data = equity_curve.reset_index() # Use all points if fewer than limit

        # Convert to required format
        thumbnail_data.columns = ['date', 'equity'] # Rename columns
        # ---------缩略图的生成结束---------

        # 关键修复：根据数据类型选择正确的日期格式
        if is_intraday:
            # 日内数据：必须包含时间信息，避免同日数据点重复
            thumbnail_data['date'] = thumbnail_data['date'].dt.strftime('%Y-%m-%d %H:%M:%S')
            print(f"[Analyzer] 日内数据使用完整时间格式: %Y-%m-%d %H:%M:%S")
        else:
            # 日线及以上：只保留日期
            thumbnail_data['date'] = thumbnail_data['date'].dt.strftime('%Y-%m-%d')
            print(f"[Analyzer] 日线数据使用日期格式: %Y-%m-%d")

        thumbnail_data['equity'] = thumbnail_data['equity'].round(digits)

        results['equity_curve_thumbnail_data'] = thumbnail_data.to_dict('records')
        print(f"[Analyzer] 生成权益曲线（数据点：{len(equity_curve)}）缩略图: {len(results['equity_curve_thumbnail_data'])} 个数据点 (原始: {total_points}, 日内: {is_intraday})")

        # --- Add Trade History to results ---
        results['trade_history'] = trade_history # Add the generated list
        print(f"[Analyzer] 添加交易历史: {len(trade_history)} 条记录")

        # Optional: Save full equity curve data if needed elsewhere (but remove before sending to frontend)
        equity_curve_full = equity_curve.reset_index()
        equity_curve_full.columns = ['date', 'equity']
        # 使用与缩略图一致的日期格式
        if is_intraday:
            # 包含时间信息
            equity_curve_full['date'] = equity_curve_full['date'].dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            # 只保留日期
            equity_curve_full['date'] = equity_curve_full['date'].dt.strftime('%Y-%m-%d')
        equity_curve_full['equity'] = equity_curve_full['equity'].round(2)
        results['equity_curve_data'] = equity_curve_full.to_dict('records')
        print(f"[Analyzer] 添加完整权益曲线数据: {len(results['equity_curve_data'])} 个数据点 (日内数据: {is_intraday})")

        # --- 添加数据摘要 ---
        if not funds_df.empty:
            print("\n[数据摘要] 资金数据统计:")
            funds_summary = funds_df[['closeprofit', 'positionprofit', 'equity']].describe().round(2)
            print(funds_summary)
        
        if not closes_df.empty:
            print("\n[数据摘要] 交易数据统计:")
            trades_summary = closes_df[['profit', 'maxprofit', 'maxloss']].describe().round(2)
            print(trades_summary)

        print("[Analyzer] 分析完成.")
        return results

    except Exception as e:
        print(f"[Analyzer] Error processing funds.csv or calculating metrics: {e}")
        import traceback
        print(traceback.format_exc())
        return {"error": f"Failed to process funds.csv or calculate metrics: {e}"}

# Example usage (for testing)
if __name__ == '__main__':
    # Create dummy data resembling the structure for testing
    dummy_output_dir = './dummy_output_bt/test_strategy'
    os.makedirs(dummy_output_dir, exist_ok=True)

    # Dummy funds.csv
    dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=100, freq='D').strftime('%Y%m%d'), format='%Y%m%d')
    initial_cap = 100000
    pnl = np.random.randn(100).cumsum() * 1000 # Random walk PnL
    equity = initial_cap + pnl
    funds_data = pd.DataFrame({
        'date': dates.strftime('%Y%m%d'),
        'closeprofit': pnl, # Simplified, using pnl directly
        'positionprofit': np.zeros(100),
        'dynbalance': pnl, # Simplified
        'fee': np.random.rand(100) * 10
    })
    # Insert initial state
    initial_row = pd.DataFrame([{'date': (dates[0] - pd.Timedelta(days=1)).strftime('%Y%m%d'), 'closeprofit': 0, 'positionprofit': 0, 'dynbalance': 0, 'fee': 0}])
    funds_data = pd.concat([initial_row, funds_data], ignore_index=True)
    funds_data.to_csv(os.path.join(dummy_output_dir, 'funds.csv'), index=False)

    # Dummy closes.csv
    closes_data = pd.DataFrame({
        'profit': np.random.randn(20) * 5000 # 20 trades
    })
    closes_data.to_csv(os.path.join(dummy_output_dir, 'closes.csv'), index=False)

     # Dummy btenv.json
    env_data = {'capital': initial_cap}
    with open(os.path.join(dummy_output_dir, 'btenv.json'), 'w') as f:
        json.dump(env_data, f)


    print(f"Created dummy data in {dummy_output_dir}")
    # 传递 trading_type 参数给测试调用
    results = analyze_backtest_results(dummy_output_dir, trading_type="etf") 
    print("\nAnalysis Results:")
    print(json.dumps(results, indent=2))

    # Clean up dummy data
    # import shutil
    # shutil.rmtree('./dummy_output_bt') 