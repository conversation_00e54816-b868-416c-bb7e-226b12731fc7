/**
 * 股票池和分类管理处理模块
 *
 * 实现股票池和分类的CRUD操作
 */

const { EventEmitter } = require('events');
const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { sequelize } = require('../database');
const { Pool, PoolCategory } = require('../models');
const { Op } = require('sequelize');
const _ = require('lodash');

class PoolHandler extends EventEmitter {
  constructor() {
    super();
    this.router = express.Router();
    this.initRoutes();
  }

  initRoutes() {
    // 分类相关路由
    this.router.get('/categories', authenticateToken, this.getAllCategories.bind(this));
    this.router.get('/categories/:id', authenticateToken, this.getCategoryById.bind(this));
    this.router.post('/categories', authenticateToken, this.createCategory.bind(this));
    this.router.put('/categories/:id', authenticateToken, this.updateCategory.bind(this));
    this.router.delete('/categories/:id', authenticateToken, this.deleteCategory.bind(this));
    this.router.put('/categories/:id/pools', authenticateToken, this.updateCategoryPools.bind(this));

    // 池子相关路由
    this.router.get('/pools/:id', authenticateToken, this.getPoolById.bind(this));
    this.router.post('/pools', authenticateToken, this.createPool.bind(this));
    this.router.put('/pools/:id', authenticateToken, this.updatePool.bind(this));
    this.router.delete('/pools/:id', authenticateToken, this.deletePool.bind(this));

    // 品种池相关路由 (从marketHandler移动过来)
    this.router.get('/symbollist', authenticateToken, this.getSymbolList.bind(this));
    this.router.post('/updatesymbollist', authenticateToken, this.updateSymbolList.bind(this));
    this.router.put('/symbollist/item', authenticateToken, this.updateSymbolListItem.bind(this));

    // 新增：获取池索引数据
    this.router.get('/getpoolindex', authenticateToken, this.getPoolIndex.bind(this));
  }

  getRouter() {
    return this.router;
  }

  /**
   * 获取所有分类(系统+用户)
   */
  async getAllCategories(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id || null;

      // 查询条件：系统分类 + 当前用户的分类
      const where = {
        [Op.or]: [
          { type: 'system' },
          { type: 'user', user_id: userId }
        ]
      };

      const categories = await PoolCategory.findAll({
        where,
        order: [
          ['type', 'ASC'],  // 系统分类排前面
          ['sort_order', 'ASC'],  // 按排序字段排序
          ['name', 'ASC']  // 最后按名称排序
        ]
      });

      res.json({ success: true, data: categories });
    } catch (error) {
      console.error('[股票池] 获取分类失败:', error);
      res.status(500).json({ success: false, message: '获取分类失败', error: error.message });
    }
  }

  /**
   * 获取特定分类详情
   */
  async getCategoryById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id || null;

      // 查询条件：指定ID且(是系统分类或属于当前用户)
      const where = {
        category_id: id,
        [Op.or]: [
          { type: 'system' },
          { type: 'user', user_id: userId }
        ]
      };

      const category = await PoolCategory.findOne({ where });

      if (!category) {
        return res.status(404).json({ success: false, message: '分类不存在或无权访问' });
      }

      res.json({ success: true, data: category });
    } catch (error) {
      console.error('[股票池] 获取分类详情失败:', error);
      res.status(500).json({ success: false, message: '获取分类详情失败', error: error.message });
    }
  }

  /**
   * 创建新分类(仅用户分类)
   */
  async createCategory(req, res) {
    try {
      const { name, description, icon, sort_order } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ success: false, message: '未授权操作' });
      }

      // 创建新分类，类型固定为user
      const newCategory = await PoolCategory.create({
        name,
        description,
        icon,
        sort_order: sort_order || 0,
        type: 'user',
        user_id: userId,
        pools_json: JSON.stringify([]),
        created_at: new Date()
      });

      res.json({ success: true, data: newCategory });
    } catch (error) {
      console.error('[股票池] 创建分类失败:', error);
      res.status(500).json({ success: false, message: '创建分类失败', error: error.message });
    }
  }

  /**
   * 更新分类信息(仅用户自己的分类)
   */
  async updateCategory(req, res) {
    try {
      const { id } = req.params;
      const { name, description, icon, sort_order } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ success: false, message: '未授权操作' });
      }

      // 查找并确认是用户自己的分类
      const category = await PoolCategory.findOne({
        where: {
          category_id: id,
          type: 'user',
          user_id: userId
        }
      });

      if (!category) {
        return res.status(404).json({ success: false, message: '分类不存在或无权修改' });
      }

      // 更新分类
      await category.update({
        name: name || category.name,
        description: description !== undefined ? description : category.description,
        icon: icon || category.icon,
        sort_order: sort_order !== undefined ? sort_order : category.sort_order
      });

      res.json({ success: true, data: category });
    } catch (error) {
      console.error('[股票池] 更新分类失败:', error);
      res.status(500).json({ success: false, message: '更新分类失败', error: error.message });
    }
  }

  /**
   * 删除分类(仅用户自己的分类)
   */
  async deleteCategory(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ success: false, message: '未授权操作' });
      }

      // 查找并确认是用户自己的分类
      const category = await PoolCategory.findOne({
        where: {
          category_id: id,
          type: 'user',
          user_id: userId
        }
      });

      if (!category) {
        return res.status(404).json({ success: false, message: '分类不存在或无权删除' });
      }

      // 删除分类
      await category.destroy();

      res.json({ success: true, message: '分类已删除' });
    } catch (error) {
      console.error('[股票池] 删除分类失败:', error);
      res.status(500).json({ success: false, message: '删除分类失败', error: error.message });
    }
  }

  /**
   * 更新分类中的池子列表
   */
  async updateCategoryPools(req, res) {
    try {
      const { id } = req.params;
      const { pools } = req.body; // 期望格式: [{pool_id: 1, pool_name: '沪深300'}, ...]
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ success: false, message: '未授权操作' });
      }

      // 查找分类
      const category = await PoolCategory.findOne({
        where: {
          category_id: id,
          [Op.or]: [
            { type: 'system', user_id: null }, // 管理员可修改系统分类
            { type: 'user', user_id: userId }  // 用户可修改自己的分类
          ]
        }
      });

      if (!category) {
        return res.status(404).json({ success: false, message: '分类不存在或无权修改' });
      }

      // 验证传入的池ID是否有效 (池存在且属于用户或是系统池)
      if (pools && Array.isArray(pools)) {
        const poolIds = pools.map(p => p.pool_id);

        if (poolIds.length > 0) {
          const validPools = await Pool.findAll({
            where: {
              pool_id: poolIds,
              [Op.or]: [
                { user_id: null },      // 系统池
                { user_id: userId }     // 用户自己的池
              ]
            },
            attributes: ['pool_id', 'name']
          });

          const validPoolIds = validPools.map(p => p.pool_id);

          // 过滤掉无效的池
          const validPoolsData = pools.filter(p => validPoolIds.includes(p.pool_id));

          // 更新分类的pools_json字段
          await category.update({
            pools_json: JSON.stringify(validPoolsData)
          });
        } else {
          // 清空池列表
          await category.update({
            pools_json: JSON.stringify([])
          });
        }
      }

      res.json({ success: true, data: category });
    } catch (error) {
      console.error('[股票池] 更新分类池列表失败:', error);
      res.status(500).json({ success: false, message: '更新分类池列表失败', error: error.message });
    }
  }

  /**
   * 获取特定池子详情
   */
  async getPoolById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id || null;

      // 查询条件：指定ID且(是系统池或属于当前用户)
      const where = {
        pool_id: id,
        [Op.or]: [
          { user_id: null },  // 系统池
          { user_id: userId } // 用户自己的池
        ]
      };

      const pool = await Pool.findOne({ where });

      if (!pool) {
        return res.status(404).json({ success: false, message: '股票池不存在或无权访问' });
      }

      res.json({ success: true, data: pool });
    } catch (error) {
      console.error('[股票池] 获取池详情失败:', error);
      res.status(500).json({ success: false, message: '获取股票池详情失败', error: error.message });
    }
  }

  /**
   * 创建新池子(仅用户池)
   */
  async createPool(req, res) {
    try {
      const { name, description, is_public, symbols } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ success: false, message: '未授权操作' });
      }

      // 创建新池子
      const newPool = await Pool.create({
        name,
        description: description || '',
        is_public: is_public || false,
        user_id: userId,
        symbols_json: JSON.stringify(symbols || [])
      });

      res.json({ success: true, data: newPool });
    } catch (error) {
      console.error('[股票池] 创建池失败:', error);
      res.status(500).json({ success: false, message: '创建股票池失败', error: error.message });
    }
  }

  /**
   * 更新池子(仅用户自己的池)
   */
  async updatePool(req, res) {
    try {
      const { id } = req.params;
      const { name, description, is_public, symbols } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ success: false, message: '未授权操作' });
      }

      // 查找并确认是用户自己的池
      const pool = await Pool.findOne({
        where: {
          pool_id: id,
          user_id: userId
        }
      });

      if (!pool) {
        return res.status(404).json({ success: false, message: '股票池不存在或无权修改' });
      }

      // 更新池子
      const updateData = {};
      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (is_public !== undefined) updateData.is_public = is_public;
      if (symbols !== undefined) updateData.symbols_json = JSON.stringify(symbols);

      await pool.update(updateData);

      res.json({ success: true, data: pool });
    } catch (error) {
      console.error('[股票池] 更新池失败:', error);
      res.status(500).json({ success: false, message: '更新股票池失败', error: error.message });
    }
  }

  /**
   * 删除池子(仅用户自己的池)
   */
  async deletePool(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ success: false, message: '未授权操作' });
      }

      // 查找并确认是用户自己的池
      const pool = await Pool.findOne({
        where: {
          pool_id: id,
          user_id: userId
        }
      });

      if (!pool) {
        return res.status(404).json({ success: false, message: '股票池不存在或无权删除' });
      }

      // 删除池子
      await pool.destroy();

      // 可选：从所有分类中移除该池子的引用
      const userCategories = await PoolCategory.findAll({
        where: {
          user_id: userId
        }
      });

      for (const category of userCategories) {
        try {
          let poolsJson = JSON.parse(category.pools_json || '[]');
          const filtered = poolsJson.filter(p => p.pool_id !== parseInt(id));

          if (poolsJson.length !== filtered.length) {
            await category.update({
              pools_json: JSON.stringify(filtered)
            });
          }
        } catch (err) {
          console.error(`[股票池] 从分类中移除池引用失败: ${err.message}`);
        }
      }

      res.json({ success: true, message: '股票池已删除' });
    } catch (error) {
      console.error('[股票池] 删除池失败:', error);
      res.status(500).json({ success: false, message: '删除股票池失败', error: error.message });
    }
  }

  /**
   * 获取用户的符号列表 (例如: 品种池)
   * 必须提供 listName 参数
   */
  async getSymbolList(req, res) {
    try {
      // 1. 验证用户是否登录
      if (!req.user?.id) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      // 2. 获取请求的列表名称，并强制要求提供
      const listName = req.query.listName;
      const isSystemList = req.query.isSystemList === 'true';
      if (!listName) {
          return res.status(400).json({
              success: false,
              error: 'Missing required query parameter: listName'
          });
      }
      console.log(`[股票池] 请求获取列表: ${listName} isSystemList: ${isSystemList} for user ${req.user.id}`);

      // 3. 查询用户的指定名称的池
      const userPool = await Pool.findOne({
        where: {
          user_id: isSystemList ? null : req.user.id,
          name: listName // 使用 listName 进行查询
        }
      });

      // 4. 准备要返回的数据 (默认为 null 或空数组，取决于你希望前端如何处理未找到的情况)
      let listData = null; // 或者 []

      // 5. 如果找到池子并且包含 symbols_json 数据
      if (userPool && userPool.symbols_json !== undefined && userPool.symbols_json !== null) {
          // 直接使用数据库中的原始数据
          listData = userPool.symbols_json;
          console.log(`[股票池] 找到列表 "${listName}" for user ${req.user.id}`);
      } else {
          console.log(`[股票池] 未找到列表 "${listName}" 或列表为空 for user ${req.user.id}, 返回 ${listData === null ? 'null' : '[]'}`);
      }

      // 6. 返回结果 (包含原始的 symbols_json 数据)
      res.json({
        success: true,
        listName: listName,
        description: userPool?.description || '',
        data: listData // 直接返回 symbols_json 的内容
      });

    } catch (error) {
      console.error(`[股票池] 获取列表 "${req.query.listName}" 失败:`, error);
      res.status(500).json({
        success: false,
        error: `Failed to fetch symbol list (${req.query.listName})`
      });
    }
  }

  /**
   * 更新用户或系统符号列表
   * 从 req.body 接收 listName, theList, 和 isSystemList (boolean)
   * 后端仅存储 theList，不验证其内部结构。
   */
  async updateSymbolList(req, res) {
    try {
      // 1. 验证用户是否登录 (基础认证)
      if (!req.user?.id) {
        return res.status(401).json({ success: false, error: 'User not authenticated' });
      }

      const userId = req.user.id; // User performing the action
      const { listName, theList, isSystemList = false, description = '' } = req.body; // Default to user list if flag is missing

      // 2. 基本参数验证 (仅检查存在性和类型)
      if (!listName || typeof listName !== 'string' || listName.trim() === '') {
        return res.status(400).json({ success: false, error: 'Missing or invalid required parameter: listName (must be a non-empty string)' });
      }
      // 只检查 theList 是否存在，不再检查类型或内容
      if (theList === undefined || theList === null) {
          return res.status(400).json({ success: false, error: 'Missing required parameter: theList' });
      }
      if (typeof isSystemList !== 'boolean') {
          return res.status(400).json({ success: false, error: 'Invalid parameter type: isSystemList must be a boolean' });
      }

      // --- !!! SECURITY WARNING !!! ---
      // Updating system lists should be restricted to authorized administrators.
      // Add role/permission check here before proceeding if isSystemList is true.
      if (isSystemList) {
          console.warn(`[股票池] WARNING: User ${userId} is attempting to update system list '${listName}'. Authorization check is required!`);
          // Example (replace with your actual authorization logic):
          // if (!req.user.isAdmin) {
          //   return res.status(403).json({ success: false, error: 'Forbidden: User does not have permission to update system lists.' });
          // }
      }
      // --- End Security Warning ---

      const targetUserId = isSystemList ? null : userId; // Determine target based on flag
      const listType = isSystemList ? 'System' : 'User';

      console.log(`[股票池] Request to update ${listType} list: ${listName} by User ${userId}. Target UserID: ${targetUserId}.`);

      // 3. 查找或创建/更新列表
      const transaction = await sequelize.transaction();
      try {
        const [pool, created] = await Pool.findOrCreate({
          where: {
            user_id: targetUserId,
            name: listName
          },
          defaults: {
            user_id: targetUserId,
            name: listName,
            symbols_json: theList, // 直接存储传入的 theList
            description: description, // 新增：在创建时设置描述
            is_public: isSystemList
          },
          transaction
        });

        if (!created) {
          // 如果列表已存在，则更新 symbols_json 和 description
          console.log(`[股票池] Found existing ${listType} list "${listName}" (Target UserID: ${targetUserId}), updating...`);
          pool.symbols_json = theList; // 直接用传入的 theList 更新
          pool.description = description; // 新增：更新描述
          await pool.save({ transaction });
        } else {
          console.log(`[股票池] Created new ${listType} list "${listName}" (Target UserID: ${targetUserId})`);
        }

        // 提交事务
        await transaction.commit();

        // 4. 返回成功响应，包含新创建或更新后的列表ID和名称
        res.json({
          success: true,
          message: `${listType} list '${listName}' ${created ? 'created' : 'updated'} successfully.`,
          data: {
            id: pool.pool_id, // <--- 返回pool_id作为id
            listName: pool.name, // <--- 返回实际保存的名称
            description: pool.description,
            isSystemList: pool.is_public // <--- 返回实际保存的公开状态
          }
        });

      } catch (dbError) {
        // 回滚事务
        await transaction.rollback();
        console.error(`[股票池] Database operation failed for ${listType} list "${listName}" (Target UserID: ${targetUserId}):`, dbError);
        // Provide more specific error if possible, e.g., from Sequelize validation
        const errorMessage = dbError.message || `Database error updating ${listType} list '${listName}'`;
        res.status(500).json({ success: false, error: errorMessage });
      }

    } catch (error) {
      console.error(`[股票池] Error processing update symbol list request:`, error);
      res.status(500).json({ success: false, error: 'Internal server error handling symbol list update' });
    }
  }

  /**
   * 更新符号列表中的单个项目 (添加或更新)
   * @param {Object} req - Express 请求对象. body 包含 { listName: string, isSystemList: boolean, symbol: string, name?: string, data?: any }
   * @param {Object} res - Express 响应对象
   */
  async updateSymbolListItem(req, res) {
    try {
      const { listName, isSystemList = false, symbol, name, data } = req.body;
      const userId = req.user?.id;

      console.log(`[股票池] 请求更新列表 '${listName}' (System: ${isSystemList}) 的 symbol: ${symbol} by user ${userId}`);

      // 1. 验证用户和输入
      if (!userId && !isSystemList) { // 必须登录才能修改用户列表
        return res.status(401).json({ success: false, message: '未授权操作' });
      }
      if (!listName || typeof listName !== 'string') {
        return res.status(400).json({ success: false, message: '缺少或无效的 listName 参数' });
      }
      if (!symbol || typeof symbol !== 'string') {
        return res.status(400).json({ success: false, message: '缺少或无效的 symbol 参数' });
      }
      // 对系统列表的权限检查 (重要!)
      if (isSystemList) {
        console.warn(`[股票池] WARNING: User ${userId} is attempting to update system list item '${listName}'. Authorization check needed!`);
        // 在此添加管理员权限检查逻辑
        // if (!req.user.isAdmin) {
        //   return res.status(403).json({ success: false, error: 'Forbidden: User does not have permission to update system lists.' });
        // }
      }

      // 2. 查找目标列表 (Pool)
      const targetUserId = isSystemList ? null : userId;
      const pool = await Pool.findOne({
        where: {
          name: listName,
          user_id: targetUserId
        }
      });

      if (!pool) {
        return res.status(404).json({ success: false, message: `列表 '${listName}' 不存在或无权修改` });
      }

      // 3. 解析现有的 symbols_json
      let symbolsArray = [];
      try {
        if (pool.symbols_json) {
          // 确保解析前 symbols_json 是字符串
          const jsonString = typeof pool.symbols_json === 'string' ? pool.symbols_json : JSON.stringify(pool.symbols_json);
          symbolsArray = JSON.parse(jsonString);
          if (!Array.isArray(symbolsArray)) {
             console.warn(`[股票池] 列表 '${listName}' 的 symbols_json 格式无效，重置为空数组`);
             symbolsArray = [];
          }
        }
      } catch (parseError) {
        console.error(`[股票池] 解析列表 '${listName}' 的 symbols_json 失败:`, parseError);
        return res.status(500).json({ success: false, message: '无法解析列表数据' });
      }

      // 4. 使用 lodash 查找 symbol 索引
      const index = _.findIndex(symbolsArray, { symbol: symbol });

      if (index !== -1) {
        // 4.1. Symbol 存在，更新 data (如果提供了 data)
        console.log(`[股票池] 找到 symbol ${symbol} 在列表 '${listName}'，更新 data`);
        if (data !== undefined) { // 检查 data 是否在请求中提供
          symbolsArray[index].data = data;
        }
        // 可选：如果提供了 name，也更新 name
        if (name !== undefined && name !== null) {
             symbolsArray[index].name = name;
        }
      } else {
        // 4.2. Symbol 不存在，添加新条目
        console.log(`[股票池] symbol ${symbol} 不在列表 '${listName}'，添加新条目`);
        // 添加新条目时，name 是必需的
        if (!name || typeof name !== 'string') {
          // 如果没提供 name，尝试从 symbol 中提取代码作为 name
          const codeFromName = symbol.split('.').pop();
          console.warn(`[股票池] 添加新 symbol ${symbol} 时缺少 name，将使用代码 '${codeFromName}' 作为默认名称`);
          name = codeFromName || 'Unknown'; // 提供一个回退值
          // return res.status(400).json({ success: false, message: '添加新 symbol 时缺少 name 参数' });
        }
        const newItem = {
          symbol: symbol,
          name: name, // 使用提供的 name 或提取的 code
          data: data !== undefined ? data : {} // 如果没有提供 data，使用空对象
        };
        symbolsArray.push(newItem);
      }

      // 5. 更新数据库
      await pool.update({
        symbols_json: JSON.stringify(symbolsArray) // 确保存回的是字符串
      });

      // 6. 返回成功响应
      res.json({ success: true, message: `列表 '${listName}' 中的 symbol ${symbol} 已更新`, data: symbolsArray }); // 返回更新后的 symbols_json 数组

    } catch (error) {
      console.error(`[股票池] 更新列表项失败:`, error);
      res.status(500).json({ success: false, message: '更新列表项失败', error: error.message });
    }
  }

  /**
   * 获取池索引数据
   * 返回分类和池子的结构，以便前端可以显示分类树
   * @param {Object} req - Express 请求对象. query 包含 { is_system: boolean }
   * @param {Object} res - Express 响应对象
   */
  async getPoolIndex(req, res) {
    try {
      // 1. 验证用户是否登录
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ success: false, message: '未授权操作' });
      }

      // 2. 获取请求参数
      const isSystem = req.query.is_system === 'true';
      console.log(`[股票池] 请求获取${isSystem ? '系统' : '用户'}池索引数据，用户ID: ${userId}`);

      // 3. 查询分类
      const categories = await PoolCategory.findAll({
        where: {
          type: isSystem ? 'system' : 'user',
          user_id: isSystem ? null : userId
        },
        order: [
          ['sort_order', 'ASC'],
          ['name', 'ASC']
        ]
      });

      // 4. 构建返回结构
      const result = [];
      for (const category of categories) {
        try {
          // 解析 pools_json 字段
          let pools = [];
          if (category.pools_json) {
            try {
              const jsonString = typeof category.pools_json === 'string' ? category.pools_json : JSON.stringify(category.pools_json);
              pools = JSON.parse(jsonString);
              if (!Array.isArray(pools)) {
                console.warn(`[股票池] 分类 '${category.name}' 的 pools_json 格式无效，重置为空数组`);
                pools = [];
              }
            } catch (parseError) {
              console.error(`[股票池] 解析分类 '${category.name}' 的 pools_json 失败:`, parseError);
              pools = [];
            }
          }

          // 添加到结果中
          result.push({
            categoryname: category.name,
            lists: pools.map(pool => ({
              poolid: pool.pool_id,
              poolname: pool.pool_name
            }))
          });
        } catch (categoryError) {
          console.error(`[股票池] 处理分类 '${category.name}' 时出错:`, categoryError);
          // 继续处理其他分类
        }
      }

      // 5. 返回结果
      console.log(`[股票池] 成功获取${isSystem ? '系统' : '用户'}池索引数据，共 ${result.length} 个分类`);
      res.json(result);

    } catch (error) {
      console.error(`[股票池] 获取池索引数据失败:`, error);
      res.status(500).json({ success: false, message: '获取池索引数据失败', error: error.message });
    }
  }
}

// 创建并导出实例
const poolHandler = new PoolHandler();
module.exports = { poolHandler };
