// backend/models/IndicatorList.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const IndicatorList = sequelize.define('IndicatorList', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users', // 引用 users 表
        key: 'id'
      }
    },
    indicators: {
      type: DataTypes.JSON,    // 存储指标数据的 JSON 对象
      allowNull: false
    }
  }, {
    tableName: 'indicator_lists',
    timestamps: false,
    indexes: [
      // 添加唯一索引，确保每个用户只有一条记录
      {
        unique: true,
        fields: ['userId']
      }
    ]
  });

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  IndicatorList.associate = function(models) {
    // IndicatorList 属于一个 User
    IndicatorList.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return IndicatorList;
};
