from pytdx.exhq import TdxExHq_API
from pytdx.hq import TdxHq_API  # 添加标准行情接口
from pytdx.params import TDXParams
import pandas as pd
import requests

# 使用扩展行情服务器
TDX_HOST = '*************'
TDX_PORT = 7727

# 使用标准行情服务器
TDX_HQ_HOST = '**************'
TDX_HQ_PORT = 7709

# 测试的期货品种列表
TEST_FUTURES = [
    {'market': 30, 'code': 'AU2508'},  # 上期所 黄金
    {'market': 28, 'code': 'M2508'},   # 大商所 豆粕
    {'market': 29, 'code': 'CF401'},   # 郑商所 棉花
    {'market': 47, 'code': 'IF2508'},  # 中金所 沪深300股指期货
]

# 测试的ETF列表（标准行情）
TEST_ETFS = [
    {'market': 0, 'code': '159509'},  # 纳指科技ETF (深圳)
    {'market': 1, 'code': '518880'},  # 黄金ETF (上海)
    {'market': 1, 'code': '512480'},  # 半导体ETF (上海)
    {'market': 0, 'code': '159531'},  # 中证2000ETF (深圳)
]

def print_markets(api):
    """打印支持的市场列表"""
    try:
        markets = api.get_markets()
        if markets:
            df = api.to_df(markets)
            print("\n可用市场列表：")
            if not df.empty:
                print(df[['market', 'name', 'short_name']].to_string())
            else:
                print("市场列表为空")
        else:
            print("未获取到市场列表")
    except Exception as e:
        print(f"获取市场列表失败: {e}")

def test_future_quote(api, market_id, future_code):
    """测试期货行情查询"""
    try:
        # 只查询K线数据（日线）
        print(f"\n{future_code} 最近5天日K线：")
        kline = api.get_instrument_bars(TDXParams.KLINE_TYPE_DAILY, market_id, future_code, 0, 5)
        if kline:
            df = api.to_df(kline)
            print(df.to_string())
        else:
            print("未获取到K线数据")

    except Exception as e:
        print(f"查询失败: {e}")

def test_etf_quote_standard(api, market_id, etf_code):
    """使用标准行情接口测试ETF行情查询"""
    try:
        # 查询K线数据（日线）
        print(f"\n{etf_code} 最近5天日K线：")
        kline = api.get_security_bars(4, market_id, etf_code, 0, 5)  # 4 表示日线
        if kline:
            df = api.to_df(kline)
            print(df.to_string())
        else:
            print("未获取到K线数据")

        # 查询分笔成交
        print(f"\n{etf_code} 最近10笔分笔成交：")
        tick = api.get_transaction_data(market_id, etf_code, 0, 10)  # 从0开始取10笔
        if tick:
            df = api.to_df(tick)
            # 重命名列以便于阅读
            df.rename(columns={
                'time': '时间',
                'price': '价格',
                'vol': '成交量',
                'buyorsell': '买卖方向',
                'amount': '成交额'
            }, inplace=True)
            print(df.to_string())
        else:
            print("未获取到分笔数据")

    except Exception as e:
        print(f"查询失败: {e}")

def test_all_futures():
    """测试所有配置的期货品种"""
    api = TdxExHq_API(heartbeat=True)  # 启用心跳包
    try:
        print(f"正在连接到扩展行情服务器 {TDX_HOST}:{TDX_PORT}...")
        if api.connect(TDX_HOST, TDX_PORT):
            print("连接扩展行情服务器成功！")
            
            # 获取市场列表
            print_markets(api)
            
            # 获取商品数量
            count = api.get_instrument_count()
            print(f"\n市场商品数量: {count}")

            print("\n开始测试期货行情...")
            for future in TEST_FUTURES:
                print("\n" + "="*50)
                test_future_quote(api, future['market'], future['code'])
                print("="*50)

            # 显示流量统计
            stats = api.get_traffic_stats()
            print("\n流量统计:")
            for k, v in stats.items():
                print(f"{k}: {v}")

        else:
            print("连接服务器失败")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        api.disconnect()

def test_all_etfs_standard():
    """使用标准行情接口测试所有ETF"""
    api = TdxHq_API(heartbeat=True)
    try:
        print(f"\n正在连接到标准行情服务器 {TDX_HQ_HOST}:{TDX_HQ_PORT}...")
        if api.connect(TDX_HQ_HOST, TDX_HQ_PORT):
            print("连接标准行情服务器成功！")
            
            print("\n开始测试ETF行情...")
            for etf in TEST_ETFS:
                print("\n" + "="*50)
                test_etf_quote_standard(api, etf['market'], etf['code'])
                print("="*50)

        else:
            print("连接服务器失败")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        api.disconnect()

def test_us_stock(api, code='GOOG'):
    """测试美股行情查询"""
    try:
        # 美股市场代码为 74
        market_id = 74
        print(f"\n{code} 最近5天日K线：")
        kline = api.get_instrument_bars(TDXParams.KLINE_TYPE_DAILY, market_id, code, 0, 5)
        if kline:
            df = api.to_df(kline)
            print(df.to_string())
        else:
            print("未获取到K线数据")

        # 获取实时行情
        print(f"\n{code} 实时行情：")
        quote = api.get_instrument_quote(market_id, code)
        if quote:
            df = api.to_df(quote)
            print(df.to_string())
        else:
            print("未获取到实时行情")

    except Exception as e:
        print(f"查询失败: {e}")

def test_goog():
    """测试谷歌股票数据"""
    api = TdxExHq_API(heartbeat=True)
    try:
        print(f"正在连接到扩展行情服务器 {TDX_HOST}:{TDX_PORT}...")
        if api.connect(TDX_HOST, TDX_PORT):
            print("连接扩展行情服务器成功！")
            test_us_stock(api)
        else:
            print("连接服务器失败")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        api.disconnect()

if __name__ == "__main__":
    # test_all_futures()
    # test_all_etfs_standard()
    test_goog()