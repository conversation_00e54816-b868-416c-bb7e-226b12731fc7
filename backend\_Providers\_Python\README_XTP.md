# 自定义XTP交易服务器

## 概述

这是一个Python实现的自定义XTP交易服务器，用于将Wonder Trader的交易指令转发到现有的tradeHandler系统。

## 架构设计

```
Wonder Trader (策略引擎)
    ↓ XTP协议
自定义XTP交易服务器 (端口6001)
    ↓ HTTP API
tradeHandler (端口3000)
    ↓ Socket.IO
交易客户端 (copy_client, mock_client等)
```

## 核心功能

### 1. XTP协议模拟
- 支持登录认证（无验证，纯路由）
- 支持下单、撤单操作
- 支持资金、持仓查询
- 提供标准的订单和成交回报

### 2. 通道路由
- 通过`user`字段识别交易通道
- 自动转发到对应的tradeHandler接口
- 支持多个交易通道并发处理

### 3. 协议转换
- XTP协议 ↔ HTTP API
- 订单状态映射
- 错误处理和日志记录

## 文件说明

### 核心文件
- `xtp_trade_server.py` - XTP交易服务器主程序
- `start_xtp_server.py` - 服务器启动脚本
- `test_xtp_client.py` - 测试客户端

### 配置文件
- 服务器地址: `127.0.0.1:6001`
- tradeHandler地址: `http://127.0.0.1:3000`

## 使用方法

### 1. 启动XTP服务器

```bash
cd backend/_Providers/_Python/
python start_xtp_server.py
```

### 2. 配置Wonder Trader

在`tdtraders.yaml`中配置：

```yaml
traders:
- active: true
  id: user5_print_trade_client
  module: TraderXTP
  host: 127.0.0.1
  port: 6001
  user: user5_print_trade_client  # 交易通道标识符
  pass: dummy                     # 不使用
  acckey: dummy                   # 不使用
  client: 1
  quick: true
```

### 3. 测试功能

```bash
python test_xtp_client.py
```

## 工作流程

### 1. 登录流程
```
Wonder Trader → XTP服务器 → 生成session_id → 建立通道映射
```

### 2. 交易流程
```
策略下单 → Wonder Trader → XTP服务器 → 解析通道ID → 
调用tradeHandler → 转发到交易客户端 → 返回结果 → 
生成XTP回报 → 通知Wonder Trader → 策略回调
```

### 3. 通道映射
```python
# user字段直接作为通道标识符
user = "user5_print_trade_client"
↓
tradeHandler请求: {
    "client_key": "user5_print_trade_client",
    "action": "buy",
    "params": {...}
}
```

## 支持的操作

### 交易操作
- `order_insert` - 下单
- `order_cancel` - 撤单

### 查询操作
- `query_asset` - 资金查询
- `query_position` - 持仓查询

### 回报类型
- `order_response` - 订单回报
- `trade_response` - 成交回报
- `cancel_response` - 撤单回报
- `asset_response` - 资金回报
- `position_response` - 持仓回报

## 配置参数

### XTP常量
```python
# 订单状态
ORDER_STATUS_INIT = 0           # 初始化
ORDER_STATUS_ALLTRADED = 1      # 全部成交
ORDER_STATUS_REJECTED = 5       # 已拒绝

# 买卖方向
SIDE_BUY = 1    # 买入
SIDE_SELL = 2   # 卖出

# 订单类型
ORDER_TYPE_LIMIT = 2   # 限价单
```

### 服务器配置
```python
host = '127.0.0.1'              # 服务器地址
port = 6001                     # 服务器端口
trade_handler_url = "http://127.0.0.1:3000"  # tradeHandler地址
```

## 日志记录

### 日志文件
- `xtp_trade_server.log` - 服务器运行日志

### 日志级别
- INFO: 重要操作和状态变化
- DEBUG: 详细的消息内容
- ERROR: 错误和异常情况

## 扩展说明

### 添加新的交易通道
1. 在tradeHandler中注册新的交易客户端
2. 在live_center中配置新的通道类型
3. XTP服务器会自动支持新通道（无需修改）

### 自定义协议
可以根据需要扩展XTP协议支持：
- 添加新的消息类型
- 扩展订单字段
- 增加查询功能

## 注意事项

1. **无认证设计**: 服务器不验证密码和密钥，仅用于内部路由
2. **简化回报**: 目前假设所有订单立即成交，可根据需要扩展
3. **错误处理**: 建议在生产环境中增强错误处理和重连机制
4. **性能优化**: 大量并发时可考虑使用异步IO

## 故障排除

### 常见问题
1. **连接失败**: 检查端口6001是否被占用
2. **转发失败**: 检查tradeHandler服务是否运行
3. **通道未找到**: 检查交易客户端是否已注册

### 调试方法
1. 查看服务器日志文件
2. 使用测试客户端验证功能
3. 检查tradeHandler的API响应
