/**
 * 股票池分类初始数据填充脚本
 * 
 * 使用方法:
 * 1. 直接运行: node seedCategories.js
 * 2. 作为模块导入: const seedCategories = require('./seedCategories'); seedCategories();
 */

const { Sequelize, DataTypes } = require('sequelize');
const path = require('path');
const fs = require('fs');

// 初始分类数据
const initialCategories = [
  // 系统分类 - A股相关
  {
    name: 'A股',
    type: 'system',
    user_id: null,
    sort_order: 10,
    description: '中国A股市场',
    icon: 'stock',
    pools_json: [], // 初始为空数组
  },
  {
    name: '沪深指数',
    type: 'system',
    user_id: null,
    sort_order: 11,
    description: '沪深主要指数',
    icon: 'line-chart',
    pools_json: [], // 初始为空数组
  },
  {
    name: '行业板块',
    type: 'system',
    user_id: null,
    sort_order: 12,
    description: 'A股行业板块',
    icon: 'appstore',
    pools_json: [], // 初始为空数组
  },
  
  // 系统分类 - 海外市场
  {
    name: '美股',
    type: 'system',
    user_id: null,
    sort_order: 20,
    description: '美国股票市场',
    icon: 'global',
    pools_json: [], // 初始为空数组
  },
  {
    name: '港股',
    type: 'system',
    user_id: null,
    sort_order: 21,
    description: '香港股票市场',
    icon: 'global',
    pools_json: [], // 初始为空数组
  },
  
  // 系统分类 - 加密货币
  {
    name: '加密货币',
    type: 'system',
    user_id: null,
    sort_order: 30,
    description: '数字加密货币',
    icon: 'dollar',
    pools_json: [], // 初始为空数组
  },
  
  // 用户分类示例 (系统预设，但type为user)
  {
    name: '我的自选',
    type: 'user',
    user_id: null, // 创建用户时会设置
    sort_order: 1,
    description: '用户自选股票',
    icon: 'star',
    pools_json: [], // 初始为空数组
  },
  {
    name: '量化策略',
    type: 'user',
    user_id: null, // 创建用户时会设置
    sort_order: 2,
    description: '量化选股结果',
    icon: 'robot',
    pools_json: [], // 初始为空数组
  },
  {
    name: '机构推荐',
    type: 'user',
    user_id: null, // 创建用户时会设置
    sort_order: 3,
    description: '券商、机构推荐',
    icon: 'team',
    pools_json: [], // 初始为空数组
  }
];

/**
 * 填充分类初始数据
 * @param {Object} options 配置选项
 * @param {string} options.dbPath SQLite数据库路径，默认使用当前目录下的database.sqlite
 * @param {boolean} options.force 是否强制重新创建，默认false
 * @returns {Promise<void>}
 */
async function seedCategories(options = {}) {
  // 默认配置
  const config = {
    dbPath: path.join(__dirname, 'database.sqlite'),
    force: false,
    ...options
  };

  console.log('开始填充股票池分类初始数据...');
  console.log(`使用数据库: ${config.dbPath}`);

  // 检查数据库文件是否存在
  if (!fs.existsSync(config.dbPath)) {
    console.error(`错误: 数据库文件不存在: ${config.dbPath}`);
    console.log('提示: 请先创建数据库或者指定正确的数据库路径');
    return;
  }

  // 连接数据库
  const sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: config.dbPath,
    logging: false
  });

  try {
    // 测试连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 定义分类模型
    const PoolCategory = sequelize.define('PoolCategory', {
      category_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false
      },
      type: {
        type: DataTypes.STRING(10), // ENUM in actual model
        allowNull: false
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      sort_order: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      description: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      icon: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      pools_json: {
        type: DataTypes.JSON,
        allowNull: true
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    }, {
      tableName: 'pool_categories',
      timestamps: false
    });

    // 检查表是否存在
    try {
      await sequelize.query('SELECT 1 FROM pool_categories LIMIT 1');
      console.log('表 pool_categories 已存在');
    } catch (error) {
      console.log('表 pool_categories 不存在，正在创建...');
      await PoolCategory.sync();
      console.log('表 pool_categories 创建成功');
    }

    // 如果force为true，则清空表
    if (config.force) {
      console.log('强制模式，清空现有数据...');
      await sequelize.query('DELETE FROM pool_categories WHERE type = "system"');
    }

    // 检查是否已有数据
    const existingCount = await PoolCategory.count({
      where: { type: 'system' }
    });

    if (existingCount > 0 && !config.force) {
      console.log(`已存在 ${existingCount} 条系统分类数据，跳过填充。`);
      console.log('提示: 使用 { force: true } 选项可强制重新填充数据');
    } else {
      // 准备数据
      const categories = initialCategories.map(category => ({
        ...category,
        pools_json: JSON.stringify(category.pools_json),
        created_at: new Date()
      }));

      // 填充数据
      console.log(`正在填充 ${categories.length} 条分类数据...`);
      await PoolCategory.bulkCreate(categories);
      console.log('分类数据填充完成');
    }

    // 关闭连接
    await sequelize.close();
    console.log('数据库连接已关闭');

  } catch (error) {
    console.error('发生错误:', error);
  }
}

// 如果直接运行此脚本，则执行填充
if (require.main === module) {
  seedCategories()
    .then(() => {
      console.log('脚本执行完毕');
      process.exit(0);
    })
    .catch(err => {
      console.error('脚本执行失败:', err);
      process.exit(1);
    });
}

// 导出函数供其他模块使用
module.exports = seedCategories; 