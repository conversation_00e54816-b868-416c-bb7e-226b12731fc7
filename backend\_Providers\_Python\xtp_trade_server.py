#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义XTP交易服务器
模拟XTP协议，将Wonder Trader的交易指令转发到tradeHandler
"""

import json
import time
import threading
import requests
import logging
from datetime import datetime
from typing import Dict, Optional, Any
import socket
import struct
import codecs
import sys
import os

# --- 新的、更精细的日志配置 ---

# 1. 创建一个顶级的logger
logger = logging.getLogger("XTPTradeServer")
logger.setLevel(logging.INFO) # 设置logger的最低处理级别

# 2. 创建一个格式化器
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 3. 创建一个处理器用于stdout (处理INFO和DEBUG)
class InfoFilter(logging.Filter):
    def filter(self, record):
        return record.levelno in (logging.DEBUG, logging.INFO)

stdout_handler = logging.StreamHandler(sys.stdout)
stdout_handler.setLevel(logging.INFO) # 此处理器只处理INFO及以上级别
stdout_handler.addFilter(InfoFilter()) # 使用过滤器精确匹配INFO
stdout_handler.setFormatter(formatter)

# 4. 创建一个处理器用于stderr (处理WARNING及以上)
stderr_handler = logging.StreamHandler(sys.stderr)
stderr_handler.setLevel(logging.WARNING)
stderr_handler.setFormatter(formatter)

# 5. 将处理器添加到logger
logger.addHandler(stdout_handler)
logger.addHandler(stderr_handler)

# --- 旧的logger定义将被替换 ---
# logger = logging.getLogger("XTPTradeServer")

# XTP协议常量定义
class XTPConstants:
    # 订单状态
    ORDER_STATUS_INIT = 0           # 初始化
    ORDER_STATUS_ALLTRADED = 1      # 全部成交
    ORDER_STATUS_PARTTRADEDQUEUEING = 2  # 部分成交
    ORDER_STATUS_NOTRADEQUEUEING = 3     # 未成交
    ORDER_STATUS_CANCELED = 4       # 已撤销
    ORDER_STATUS_REJECTED = 5       # 已拒绝
    
    # 买卖方向
    SIDE_BUY = 1    # 买入
    SIDE_SELL = 2   # 卖出
    
    # 订单类型
    ORDER_TYPE_MARKET = 1  # 市价单
    ORDER_TYPE_LIMIT = 2   # 限价单

class XTPOrder:
    """XTP订单结构"""
    def __init__(self):
        self.order_xtp_id = 0
        self.order_client_id = 0
        self.ticker = ""
        self.market = 0
        self.price = 0.0
        self.quantity = 0
        self.side = 0
        self.order_type = 0
        self.order_status = XTPConstants.ORDER_STATUS_INIT

class XTPTrade:
    """XTP成交结构"""
    def __init__(self):
        self.order_xtp_id = 0
        self.trade_id = 0
        self.ticker = ""
        self.market = 0
        self.price = 0.0
        self.quantity = 0
        self.side = 0
        self.trade_time = 0

class XTPTradeServer:
    """自定义XTP交易服务器"""
    
    def __init__(self, host='127.0.0.1', port=6001):
        self.host = host
        self.port = port
        self.server_socket = None
        self.running = False
        
        # 会话管理
        self.sessions: Dict[int, Dict] = {}  # session_id -> session_info
        self.session_counter = 1000
        
        # 订单管理
        self.orders: Dict[int, XTPOrder] = {}  # order_id -> order
        self.order_counter = 1000
        
        # 通道路由配置
        self.trade_handler_url = "http://127.0.0.1:3000"
        
        logger.info(f"XTP交易服务器初始化完成: {host}:{port}")
    
    def start(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)
            self.running = True
            
            logger.info(f"XTP交易服务器启动成功: {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    logger.info(f"新客户端连接: {client_address}")
                    
                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address),
                        daemon=True
                    )
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        logger.error(f"接受客户端连接时出错: {e}")
                        
        except Exception as e:
            logger.error(f"启动XTP服务器失败: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        logger.info("XTP交易服务器已停止")
    
    def handle_client(self, client_socket, client_address):
        """处理客户端连接"""
        try:
            while self.running:
                # 接收消息头（消息长度）
                header_data = client_socket.recv(4)
                if not header_data:
                    break
                
                msg_length = struct.unpack('!I', header_data)[0]
                
                # 接收消息体
                msg_data = b''
                while len(msg_data) < msg_length:
                    chunk = client_socket.recv(msg_length - len(msg_data))
                    if not chunk:
                        break
                    msg_data += chunk
                
                if len(msg_data) == msg_length:
                    # 处理消息
                    self.process_message(client_socket, msg_data)
                    
        except Exception as e:
            logger.error(f"处理客户端 {client_address} 时出错: {e}")
        finally:
            client_socket.close()
            logger.info(f"客户端 {client_address} 连接已关闭")
    
    def process_message(self, client_socket, msg_data):
        """处理XTP协议消息"""
        try:
            # 解析消息（简化的JSON格式）
            message = json.loads(msg_data.decode('utf-8'))
            msg_type = message.get('type')
            
            logger.debug(f"收到消息: {message}")
            
            if msg_type == 'login':
                self.handle_login(client_socket, message)
            elif msg_type == 'order_insert':
                self.handle_order_insert(client_socket, message)
            elif msg_type == 'order_cancel':
                self.handle_order_cancel(client_socket, message)
            elif msg_type == 'query_asset':
                self.handle_query_asset(client_socket, message)
            elif msg_type == 'query_position':
                self.handle_query_position(client_socket, message)
            else:
                logger.warning(f"未知消息类型: {msg_type}")
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.info(f"收到的原始二进制数据 (HEX): {msg_data.hex()}")
                
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
    
    def handle_login(self, client_socket, message):
        """处理登录请求"""
        user = message.get('user', '')
        password = message.get('password', '')
        acckey = message.get('acckey', '')  # 新增：获取 token

        
        logger.info(f"收到登录请求: user={user}")
        
        # 生成session_id（无验证，直接成功）
        session_id = self.session_counter
        self.session_counter += 1
        
        # 保存会话信息（包含 token）
        self.sessions[session_id] = {
            'user': user,
            'channel_id': user,
            'acckey': acckey,  # 保存 token
            'client_socket': client_socket,
            'login_time': time.time()
        }
        
        # 发送登录响应
        response = {
            'type': 'login_response',
            'session_id': session_id,
            'result': 0,  # 0表示成功
            'message': '登录成功'
        }
        
        self.send_response(client_socket, response)
        logger.info(f"用户 {user} 登录成功, session_id: {session_id}")
    
    def handle_order_insert(self, client_socket, message):
        """处理下单请求"""
        session_id = message.get('session_id')
        order_info = message.get('order_info', {})
        
        if session_id not in self.sessions:
            logger.error(f"无效的session_id: {session_id}")
            return
        
        session = self.sessions[session_id]
        channel_id = session['user']
        
        logger.info(f"收到下单请求: channel={channel_id}, order={order_info}")
        
        # 生成订单ID
        order_id = self.order_counter
        self.order_counter += 1
        
        # 创建订单对象
        order = XTPOrder()
        order.order_xtp_id = order_id
        order.ticker = order_info.get('ticker', '')
        order.price = float(order_info.get('price', 0))
        order.quantity = int(order_info.get('quantity', 0))
        order.side = int(order_info.get('side', XTPConstants.SIDE_BUY))
        order.order_type = int(order_info.get('order_type', XTPConstants.ORDER_TYPE_LIMIT))
        
        self.orders[order_id] = order
        
        # 转发到tradeHandler（传递 session_id）
        success = self.forward_to_trade_handler(session_id, channel_id, order)  # 修改这里
        
        if success:
            order.order_status = XTPConstants.ORDER_STATUS_ALLTRADED
            # 发送订单回报
            self.send_order_response(client_socket, order)
            # 发送成交回报
            self.send_trade_response(client_socket, order)
        else:
            order.order_status = XTPConstants.ORDER_STATUS_REJECTED
            self.send_order_response(client_socket, order)

    def handle_order_cancel(self, client_socket, message):
        """处理撤单请求"""
        session_id = message.get('session_id')
        order_xtp_id = message.get('order_xtp_id')

        logger.info(f"收到撤单请求: session={session_id}, order_id={order_xtp_id}")

        # 简化处理：直接返回撤单成功
        response = {
            'type': 'cancel_response',
            'order_xtp_id': order_xtp_id,
            'result': 0,
            'message': '撤单成功'
        }

        self.send_response(client_socket, response)

    def handle_query_asset(self, client_socket, message):
        """处理资金查询请求"""
        session_id = message.get('session_id')

        logger.info(f"收到资金查询请求: session={session_id}")

        # 模拟资金数据
        asset_data = {
            'type': 'asset_response',
            'total_asset': 1000000.0,
            'buying_power': 800000.0,
            'security_asset': 200000.0,
            'fund_buy_amount': 0.0,
            'fund_sell_amount': 0.0
        }

        self.send_response(client_socket, asset_data)

    def handle_query_position(self, client_socket, message):
        """处理持仓查询请求"""
        session_id = message.get('session_id')
        ticker = message.get('ticker', '')

        logger.info(f"收到持仓查询请求: session={session_id}, ticker={ticker}")

        # 模拟持仓数据
        position_data = {
            'type': 'position_response',
            'ticker': ticker,
            'total_qty': 0,
            'sellable_qty': 0,
            'avg_price': 0.0,
            'unrealized_pnl': 0.0
        }

        self.send_response(client_socket, position_data)

    def forward_to_trade_handler(self, session_id: int, channel_id: str, order: XTPOrder) -> bool:  # 修改签名
        """转发交易指令到tradeHandler"""
        try:
            # 通过 session_id 获取会话
            session = self.sessions.get(session_id, {})
            token = session.get('acckey', '')  # 获取保存的 token
            
            # 构造tradeHandler请求
            action = "buy" if order.side == XTPConstants.SIDE_BUY else "sell"
            
            request_data = {
                "client_key": channel_id,
                "action": action,
                "params": {
                    "code": order.ticker,
                    "price": order.price,
                    "quantity": order.quantity
                }
            }
            
            url = f"{self.trade_handler_url}/api/trade/execute_trade"
            
            logger.info(f"转发交易指令到tradeHandler: {url}")
            logger.debug(f"请求数据: {request_data}")
            
            # 添加认证头
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                url,
                json=request_data,
                timeout=10,
                headers=headers  # 添加认证头
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"tradeHandler响应: {result}")
                return result.get('success', False)
            else:
                logger.error(f"tradeHandler请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"转发到tradeHandler时出错: {e}")
            return False

    
    def send_response(self, client_socket, response):
        """发送响应消息"""
        try:
            response_data = json.dumps(response).encode('utf-8')
            msg_length = len(response_data)
            
            # 发送消息头（长度）
            header = struct.pack('!I', msg_length)
            client_socket.send(header)
            
            # 发送消息体
            client_socket.send(response_data)
            
        except Exception as e:
            logger.error(f"发送响应时出错: {e}")
    
    def send_order_response(self, client_socket, order: XTPOrder):
        """发送订单回报"""
        response = {
            'type': 'order_response',
            'order_xtp_id': order.order_xtp_id,
            'ticker': order.ticker,
            'price': order.price,
            'quantity': order.quantity,
            'side': order.side,
            'order_status': order.order_status,
            'timestamp': int(time.time() * 1000)
        }
        
        self.send_response(client_socket, response)
        logger.info(f"发送订单回报: order_id={order.order_xtp_id}, status={order.order_status}")
    
    def send_trade_response(self, client_socket, order: XTPOrder):
        """发送成交回报"""
        trade = XTPTrade()
        trade.order_xtp_id = order.order_xtp_id
        trade.trade_id = order.order_xtp_id  # 简化：使用相同ID
        trade.ticker = order.ticker
        trade.price = order.price
        trade.quantity = order.quantity
        trade.side = order.side
        trade.trade_time = int(time.time() * 1000)
        
        response = {
            'type': 'trade_response',
            'order_xtp_id': trade.order_xtp_id,
            'trade_id': trade.trade_id,
            'ticker': trade.ticker,
            'price': trade.price,
            'quantity': trade.quantity,
            'side': trade.side,
            'trade_time': trade.trade_time
        }
        
        self.send_response(client_socket, response)
        logger.info(f"发送成交回报: trade_id={trade.trade_id}")

def main():
    """主函数"""
    server = XTPTradeServer()
    
    try:
        logger.info("启动XTP交易服务器...")
        server.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止服务器...")
    except Exception as e:
        logger.error(f"服务器运行时出错: {e}")
    finally:
        server.stop()

if __name__ == "__main__":
    main()
