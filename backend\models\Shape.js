const { DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  // 定义 Shape 模型
  const Shape = sequelize.define('Shape', {
    shapeId: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true // 确保形态名称唯一
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false, // 关联用户ID
      references: {
        model: 'users', // 引用 users 表
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'shapes', // 指定数据库表名
    timestamps: false // 不使用自动生成的时间戳
  });

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  Shape.associate = function(models) {
    // Shape 属于一个 User
    Shape.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  // 导出模型
  return Shape;
}; 