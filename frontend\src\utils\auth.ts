// 用户状态管理
export const TOKEN_KEY = 'user_token';
export const USER_INFO_KEY = 'user_info';

export const getToken = () => {
  const token = localStorage.getItem(TOKEN_KEY);
  // 只打印 token 是否存在，不暴露具体内容
  console.log('[Auth] Token exists:', !!token);
  return token;
};

// 获取用户角色
export const getUserRole = () => {
  const userInfo = localStorage.getItem(USER_INFO_KEY);
  return userInfo ? JSON.parse(userInfo).role : null;
};

export const setToken = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token);
};

export const removeToken = () => {
  localStorage.removeItem(TOKEN_KEY);
  console.log('[Auth] Token removed');
};

export const isLoggedIn = () => {
  const token = localStorage.getItem(TOKEN_KEY);
  // 不打印 token 内容，只显示状态
  console.log('[Auth] Login status:', !!token);
  return !!token;
};

export interface UserInfo {
  /** 用户ID */
  id: number;
  /** 用户名 */
  username: string;
  /** 邮箱 */
  email: string;
  /** 头像 */
  avatar?: string;
  /** JWT令牌 */
  token: string;
  /** 角色 */
  role: string;
}

/** 
 * 获取用户信息
 * @returns {UserInfo} 用户信息对象或null
 * @example
 * // 返回示例
 * {
 *   "id": 5,
 *   "username": "imaltai",
 *   "email": "<EMAIL>",
 *   "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 * }
 */
export const getUserInfo = (): UserInfo | null => {
  const userInfo = localStorage.getItem(USER_INFO_KEY);
  return userInfo ? JSON.parse(userInfo) : null;
};

export const setUserInfo = (userInfo: UserInfo) => {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
}; 