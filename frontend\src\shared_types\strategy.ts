import { MarketType } from "./market";

/**
 * 策略类型枚举
 * 与后端 shared_types/strategy.js 中的 STRATEGY_TYPE 保持一致
 */
export const STRATEGY_TYPE = {
  PORTFOLIO: 'portfolio',
  // 未来可扩展更多类型
} as const;

/**
 * 策略类型信息定义
 * 包含类型值、显示标签和详细说明
 */
export const STRATEGY_TYPE_INFO = {
  [STRATEGY_TYPE.PORTFOLIO]: {
    value: STRATEGY_TYPE.PORTFOLIO,
    label: '多因子策略',
    description: '基于多个技术指标和基本面因子的综合量化策略，适用于股票、期货等多种资产类别',
    category: '量化策略'
  },
  // 未来可扩展更多类型
  // MOMENTUM: {
  //   value: 'momentum',
  //   label: '动量策略',
  //   description: '基于价格和成交量动量的趋势跟踪策略',
  //   category: '趋势策略'
  // }
} as const;

/**
 * 策略类型选项，用于前端下拉选择
 */
export const STRATEGY_TYPE_OPTIONS = Object.values(STRATEGY_TYPE_INFO).map(info => ({
  value: info.value,
  label: info.label,
  description: info.description,
  category: info.category
}));

/**
 * 获取策略类型信息
 * @param strategyType 策略类型值
 * @returns 策略类型信息对象
 */
export const getStrategyTypeInfo = (strategyType: string) => {
  return Object.values(STRATEGY_TYPE_INFO).find(info => info.value === strategyType) || {
    value: strategyType,
    label: '未知策略类型',
    description: '未定义的策略类型',
    category: '其他'
  };
};

/**
 * 表示策略列表中的单个策略项
 */
export interface StrategyListItem {
  name: string;
  id?: string;
  desc?: string;
  author?: string;
  category?: string;
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
  universe?: string[];
  backtest_result?: BacktestResultThumbnail | null;
}

/**
 * 基础回测指标
 */
export interface BaseBacktestMetrics {
    total_return_pct?: number;
    max_drawdown_pct?: number;
    annualized_return_pct?: number;
    sharpe_ratio?: number | null;
    total_trades?: number;
    winning_trades?: number;
    losing_trades?: number;
    win_rate_pct?: number;
    profit_factor?: number | string | null;
    average_profit_per_trade?: number;
    average_profit_winning_trade?: number;
    average_loss_losing_trade?: number;
    backtest_period_start?: string | null;
    backtest_period_end?: string | null;
    initial_capital?: number;
}

// 定义一个MarketType到MarketCode3的映射
export const MarketCode3 = {
  [MarketType.ETF]: 'ETF',          // 指数基金
  [MarketType.STOCK]: 'STK',      // 股票
  [MarketType.STOCK_US]: 'US', // 美股
  [MarketType.FUTURE]: 'OPT',    // 期货
  [MarketType.CRYPTO]: 'CYP',    // 加密货币
  [MarketType.INDEX]: 'IDX'       // 指数
}

/**
 * 权益曲线数据点
 */
export interface EquityPoint {
    date: string;
    equity: number;
}

/**
 * 列表项中使用的回测结果缩略图类型
 */
export interface BacktestResultThumbnail extends BaseBacktestMetrics {
    equity_curve_thumbnail_data?: EquityPoint[];
}

/**
 * 表示单条交易记录的结构
 */
export interface TradeRecord {
  date: string | null; // ISO 格式日期字符串或 null
  action: 'Buy' | 'Sell' | string; // 买入/卖出或其他动作
  symbol: string; // 交易代码
  name: string; // 交易名称 (可能与 symbol 相同)
  price: number; // 交易价格
  amount: number; // 交易金额
  // 可以根据需要添加 qty, fee 等其他字段
}

// --- 修正：策略详情数据结构 (Flattened) --- 
// 包含基础信息、所有指标、完整权益曲线和交易历史, 以及原始配置YAML
export interface StrategyDetails extends BaseBacktestMetrics { 
    id: string;                         // 策略 ID (必须)
    name: string;                       // 策略名称 (必须)
    desc?: string;                      // --- 重新添加：策略描述 ---
    equity_curve_data?: EquityPoint[];         // 完整权益曲线
    equity_curve_thumbnail_data?: EquityPoint[]; // 缩略图 (也包含在详情中)
    trade_history?: TradeRecord[];             // 交易历史
    strategy_config_yaml?: string | null; // 完整的策略配置 YAML 字符串 或 null
    // 包含所有从 BaseBacktestMetrics 继承的字段
    // 以及 Python 可能返回的任何其他特定于详情的字段
}
