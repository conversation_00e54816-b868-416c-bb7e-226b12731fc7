import React, { useEffect, useState, useRef } from 'react';
import { Card, Form, InputNumber, Select, Button, Spin } from 'antd';
import { AimOutlined } from '@ant-design/icons';
import { useAtom } from 'jotai';
import { selectedIndicatorAtom, observingSignalAtom, signalListVisibleAtom, geneDialogVisibleAtom } from '../../models/geneState';
import { currentSymbolAtom } from '../../../../store/state';
import { SignalParameter } from '@/_Modules/Signal/BaseSignal';
import { loadSignalDefinitions } from '@/_Modules/Signal';
import { marketService } from '@/_Services/marketService';
import { EventBus } from '@/events/eventBus';
import { MarketEvents } from '@/events/events';
import { init, dispose, Chart } from 'klinecharts';
import { useTheme } from '@/models/useTheme';

const { Option } = Select;

/**
 * 基因配置面板组件
 * 用于配置选中指标的参数
 */
const GeneConfigPanel: React.FC = () => {
    const [form] = Form.useForm();
    const [selectedIndicator] = useAtom(selectedIndicatorAtom);
    const [currentSymbol] = useAtom(currentSymbolAtom);
    const [, setObservingSignal] = useAtom(observingSignalAtom);
    const [, setSignalListVisible] = useAtom(signalListVisibleAtom);
    const [dialogVisible, setVisible] = useAtom(geneDialogVisibleAtom);
    const [parameters, setParameters] = useState<SignalParameter[]>([]);
    const [loading, setLoading] = useState(false);
    const [selectedKlines, setSelectedKlines] = useState<any[]>([]);
    const chartRef = useRef<Chart | null>(null);
    const chartContainerRef = useRef<HTMLDivElement | null>(null);
    const { isDarkMode } = useTheme();

    // 监听选项删除成功事件
    useEffect(() => {
        const subscription = EventBus.on(
            MarketEvents.Types.SIGNAL_OPTIONREMOVED,
            (payload: { paramName: string; option: string }) => {
                setParameters(prevParams => {
                    return prevParams.map(param => {
                        // 如果是目标参数且有options属性
                        if (param.name === payload.paramName && param.options) {
                            // 创建新的参数对象，更新options
                            const updatedOptions = param.options.filter(opt => opt.name !== payload.option);
                            
                            // 如果删除后没有选项了，清空表单对应字段的值
                            if (updatedOptions.length === 0) {
                                form.setFieldValue(param.name, undefined);
                            } else if (form.getFieldValue(param.name) === payload.option) {
                                // 如果删除的是当前选中的值，也清空表单字段
                                form.setFieldValue(param.name, undefined);
                            }
                            
                            return {
                                ...param,
                                options: updatedOptions
                            };
                        }
                        return param;
                    });
                });
            }
        );

        return () => subscription.unsubscribe();
    }, [form]);

    // 当选中的信号改变时，重置表单并加载参数
    useEffect(() => {
        if (selectedIndicator) {
            setLoading(true);
            form.resetFields();
            
            loadSignalDefinitions().then(definitions => {


                console.log('[GeneConfigPanel]loadSignalDefinitions 已加载信号定义列表:', definitions);

                const signalDef = definitions.find(s => s.name === selectedIndicator.name);
                if (signalDef) {
                    setParameters(signalDef.parameters);
                    
                    // 打印形态配置信息
                    if (signalDef.name === 'ShapeSignal' && signalDef.parameters.length > 0) {
                        const shapeParam = signalDef.parameters.find(p => p.name === 'shapeName');
                        if (shapeParam && shapeParam.options) {
                            console.log('[GeneConfigPanel] 已加载形态配置列表:', {
                                count: shapeParam.options.length,
                                shapes: shapeParam.options.map(opt => ({
                                    name: opt.name,
                                    hasKlines: opt.klines && opt.klines.length > 0,
                                    klinesCount: opt.klines ? opt.klines.length : 0
                                }))
                            });
                        }
                    }
                } else {
                    setParameters([]);
                }
                setLoading(false);
            }).catch(error => {
                console.error('Failed to load signal parameters:', error);
                setParameters([]);
                setLoading(false);
            });
        }
    }, [selectedIndicator, form, dialogVisible]);

    // 监听表单值变化，更新K线数据
    const updateKlineData = () => {
        // 查找包含形态配置名称的参数
        const shapeNameParam = parameters.find(param => param.name === 'shapeName');
        if (!shapeNameParam || !shapeNameParam.options) {
            setSelectedKlines([]);
            return;
        }

        // 获取当前选中的形态名称
        const selectedName = form.getFieldValue('shapeName');
        if (!selectedName) {
            setSelectedKlines([]);
            return;
        }

        // 查找对应的形态配置
        const selectedOption = shapeNameParam.options.find(opt => opt.name === selectedName);
        if (selectedOption && selectedOption.klines && selectedOption.klines.length > 0) {
            setSelectedKlines(selectedOption.klines);
        } else {
            setSelectedKlines([]);
        }
    };

    // 当参数改变或表单值改变时，更新K线数据
    useEffect(() => {
        updateKlineData();
    }, [parameters, form.getFieldValue('shapeName')]);

    // 初始化和更新K线图表
    useEffect(() => {
        if (!chartContainerRef.current || selectedKlines.length === 0) return;

        // 清理现有图表
        if (chartRef.current && chartContainerRef.current) {
            dispose(chartContainerRef.current);
            chartRef.current = null;
        }

        // 创建新图表
        const chart = init(chartContainerRef.current);
        chartRef.current = chart;
        
        if (chart) {
            // 设置图表样式
            chart.applyNewData(selectedKlines.map(item => ({
                timestamp: item.time,
                open: item.open,
                high: item.high,
                low: item.low,
                close: item.close,
                volume: item.volume || 0
            })));

            // 设置主题
            chart.setStyles({
                grid: {
                    show: true,
                    horizontal: {
                        show: true,
                        color: isDarkMode ? '#2B2B43' : '#E6E6E6',
                        size: 1
                    },
                    vertical: {
                        show: true,
                        color: isDarkMode ? '#2B2B43' : '#E6E6E6',
                        size: 1
                    }
                },
                candle: {
                    priceMark: {
                        show: false
                    }
                },
                indicator: {
                    bars: [],
                    lines: []
                }
            });

            // 自动缩放让所有K线刚好可见
            setTimeout(() => {
                if (chart && selectedKlines.length > 0) {
                    // 获取容器宽度
                    const containerWidth = chartContainerRef.current?.clientWidth || 200;
                    
                    // 计算合适的K线间距
                    // 假设每根K线需要的最小宽度为6像素（包括间距）
                    const minBarWidth = 6;
                    const totalBars = selectedKlines.length;
                    const availableWidth = containerWidth - 40; // 减去左右边距
                    
                    // 计算每根K线应该占用的宽度
                    //const optimalBarWidth = Math.max(minBarWidth, availableWidth / totalBars);
                    const optimalBarWidth = availableWidth / totalBars;
                    
                    // 计算缩放比例（相对于默认K线间距）
                    const defaultBarSpace = 10; // klinecharts默认K线间距
                    const scale = optimalBarWidth / defaultBarSpace;
                    
                    console.log('[GeneConfigPanel] 自动缩放参数:', {
                        containerWidth,
                        totalBars,
                        availableWidth,
                        optimalBarWidth,
                        scale
                    });
                    
                    // 应用缩放
                    if (scale > 0) {
                        chart.zoomAtCoordinate(scale, { x: containerWidth / 2, y: 100 });
                        
                        // 缩放后自动滚动到合适位置，让所有K线居中显示
                        setTimeout(() => {
                            // 滚动到数据中间位置
                            //const middleIndex = Math.floor(totalBars / 2);
                            chart.scrollToDataIndex(totalBars - 1, 200);
                        }, 50);
                    }
                }
            }, 100);
        }

        // 清理函数
        return () => {
            if (chartRef.current && chartContainerRef.current) {
                dispose(chartContainerRef.current);
                chartRef.current = null;
            }
        };
    }, [selectedKlines, isDarkMode]);

    // 响应窗口大小变化
    useEffect(() => {
        const handleResize = () => {
            if (chartRef.current) {
                chartRef.current.resize();
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const handleLocate = async () => {
        if (!selectedIndicator || !currentSymbol) return;

        const values = form.getFieldsValue();
        const signalConfig = {
            name: selectedIndicator.name,
            parameters: values
        };

        // 保存当前观察信号
        setObservingSignal(signalConfig);
        
        // 关闭基因配置对话框
        setVisible(false);

        // 显示信号触发列表
        setSignalListVisible(true);

        // 计算历史信号
        try {
            await marketService.calculateHistoricalSignals(currentSymbol, signalConfig);
        } catch (error) {
            console.error('Failed to calculate historical signals:', error);
        }
    };

    const handleStockSelection = async () => {
        if (!selectedIndicator || !currentSymbol) return;

        const formValues = form.getFieldsValue();
        const signalConfig = {
            signalClassName: selectedIndicator.signalClassName,
            parameters: formValues,
        };

        console.log('[GeneConfigPanel] 开始选股，信号配置:', signalConfig);

        try {
            // 导入选股事件
            const { SymbolSelectEvents } = await import('@/events/events');
            
            // 发送选股请求事件
            EventBus.emit(SymbolSelectEvents.Types.START_STOCK_SELECTION, {
                candidateType: 0, // 0=A股, 1=期货, 2=美股
                signalConfig: signalConfig
            });
            
            console.log('[GeneConfigPanel] 选股请求已发送');
            
            // 关闭基因配置对话框
            setVisible(false);
            
        } catch (error) {
            console.error('Failed to perform stock selection:', error);
        }
    };

    if (!selectedIndicator) {
        return (
            <Card title="参数配置" style={{ height: '100%' }}>
                <div style={{ textAlign: 'center', marginTop: '20px' }}>
                    请先选择一个指标
                </div>
            </Card>
        );
    }

    // 检查是否有形态参数，且当前选中的形态有K线数据
    const hasKlineData = selectedKlines.length > 0;

    return (
        <Card 
            title="参数配置2" 
            size="small"
            bodyStyle={{ 
                padding: '12px',
                height: 'calc(100% - 37px)',
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden' // 添加此行防止整体溢出
            }}
            style={{ height: '100%' }}
        >
            <div style={{ flex: 1, overflowY: 'auto', marginBottom: '16px' }}>
                {loading ? (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                        <Spin tip="加载参数中..." />
                    </div>
                ) : (
                    <Form
                        form={form}
                        layout="vertical"
                        onValuesChange={updateKlineData}
                    >
                        {parameters.map(param => (
                            <Form.Item
                                key={param.name}
                                name={param.name}
                                label={param.description}
                                initialValue={param.default}
                                rules={[{ required: true, message: `请输入${param.description}` }]}
                            >
                                {param.options ? (
                                    <Select>
                                        {param.options.map(option => (
                                            <Option key={option.name} value={option.name}>
                                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', minWidth: '120px' }}>
                                                    <span>{option.name}</span>
                                                    <span
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            // 发送删除选项事件
                                                            EventBus.emit(MarketEvents.Types.SIGNAL_REMOVEOPTION, {
                                                                paramName: param.name,
                                                                option: option.name
                                                            });
                                                            // 更新本地状态
                                                            const updatedOptions = param.options?.filter(item => item.name !== option.name);
                                                            if (updatedOptions) {
                                                                param.options = updatedOptions;
                                                            }
                                                        }}
                                                        style={{
                                                            marginLeft: 16,
                                                            color: 'var(--ant-color-primary)',
                                                            fontSize: '16px',
                                                            cursor: 'pointer',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            padding: '4px'
                                                        }}
                                                    >
                                                        ×
                                                    </span>
                                                </div>
                                            </Option>
                                        ))}
                                    </Select>
                                ) : (
                                    <InputNumber
                                        style={{ width: '100%' }}
                                        min={param.minValue}
                                        max={param.maxValue}
                                        step={param.step}
                                    />
                                )}
                            </Form.Item>
                        ))}
                    </Form>
                )}

                {/* K线图显示区域 - 只在有K线数据时显示 */}
                {hasKlineData && (
                    <div style={{ 
                        flex: 'none', // 禁止拉伸
                        height: '240px', // 固定高度
                        borderTop: '1px solid #d9d9d9',
                        paddingTop: '16px'
                      }}>
                        <h4 style={{ fontSize: '14px', margin: '0 0 8px 0' }}>形态K线走势图</h4>
                        <div 
                          ref={chartContainerRef} 
                          style={{ 
                            width: '100%', 
                            height: '200px',
                            borderRadius: '4px',
                            overflow: 'hidden'
                          }}
                        />
                        <div style={{ marginTop: '8px', fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)', textAlign: 'center' }}>
                          {`共 ${selectedKlines.length} 根K线`}
                        </div>
                      </div>
                )}
            </div>
            <div style={{ display: 'flex', gap: '8px' }}>
                <Button 
                    type="primary" 
                    icon={<AimOutlined />}
                    onClick={handleLocate}
                    disabled={!selectedIndicator || loading}
                    style={{ flex: 1 }}
                >
                    信号定位
                </Button>
                <Button 
                    type="default" 
                    onClick={handleStockSelection}
                    disabled={!selectedIndicator || loading}
                    style={{ width: '80px' }}
                >
                    选股
                </Button>
            </div>
        </Card>
    );
};

export default GeneConfigPanel; 