const { DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  // 定义 StrategyOwnership 模型
  const StrategyOwnership = sequelize.define('StrategyOwnership', { // 模型名建议大写开头 StrategyOwnership
    strategy_id: { // This will store the UUID generated by the Python service
      type: DataTypes.STRING, // Using STRING, suitable for UUIDs
      primaryKey: true,
      allowNull: false,
      comment: '策略的唯一标识符 (UUID), 对应 Python 服务中的 strategy_id'
    },
    owner_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users', // 表名应为 users
        key: 'id'
      },
      comment: '策略拥有者的用户ID'
    },
    shared_with_ids: {
      type: DataTypes.TEXT, // Can store comma-separated IDs
      allowNull: true,
      comment: '共享给的用户ID列表 (逗号分隔)'
    }
    // Timestamps (createdAt, updatedAt) are handled by the options below
  }, {
    tableName: 'strategy_ownership',
    timestamps: true, // Enable timestamps for tracking creation/update
    comment: '记录自定义策略的拥有权和共享信息'
  });

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  StrategyOwnership.associate = function(models) {
    // StrategyOwnership 属于一个 User (owner)
    StrategyOwnership.belongsTo(models.User, {
      foreignKey: 'owner_id',
      as: 'owner'
    });
    // 注意：shared_with_ids 是文本字段，不能直接建立 Sequelize 关联。
    // 如果需要更复杂的共享关系，可能需要一个单独的 StrategyShare 表。
  };

  // 导出模型
  return StrategyOwnership;
}; 