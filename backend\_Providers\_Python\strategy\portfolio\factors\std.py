# std.py
import numpy as np
import talib
from typing import Optional

def calculate_value(opens: Optional[np.ndarray] = None,
                   highs: Optional[np.ndarray] = None,
                   lows: Optional[np.ndarray] = None,
                   closes: Optional[np.ndarray] = None,
                   volumes: Optional[np.ndarray] = None,
                   **kwargs) -> Optional[float]:
    """
    计算标准差 (Standard Deviation - STD) 在最后一个时间点的数值。
    符合 '正常计算模式的优化效能因子框架.md' 规范。

    Args:
        opens/highs/lows/closes/volumes (Optional[np.ndarray]): 价格/成交量序列。
        **kwargs:
            period (int): 计算周期 (默认 20)。
            column (str): 指定使用哪个序列 (e.g., 'close')。
            nbdev (float): 标准差的倍数 (默认 1.0)。

    Returns:
        Optional[float]: 计算出的最后一个时间点的 STD 值，或 np.nan。
    """
    period = kwargs.get('period', 20)
    column = kwargs.get('column', 'close')
    nbdev = kwargs.get('nbdev', 1.0)

    # --- Input Selection ---
    input_array = None
    if column == 'close' and closes is not None:
        input_array = closes
    elif column == 'open' and opens is not None:
        input_array = opens
    elif column == 'high' and highs is not None:
        input_array = highs
    elif column == 'low' and lows is not None:
        input_array = lows
    elif column == 'volume' and volumes is not None:
        input_array = volumes
    else:
        # print(f"[因子计算:STD] 输入错误: 列 '{column}' 无效或对应数据为 None。")
        return np.nan
    # --- End Input Selection ---

    # --- Input Validation ---
    if not isinstance(period, int) or period <= 1:  # 至少需要2个点才能计算标准差
        # print(f"[因子计算:STD] 参数错误: period ({period}) 必须是 >= 2 的整数。")
        return np.nan
    if len(input_array) < period:
        # print(f"[因子计算:STD] 数据不足: 需要至少 {period} 条数据，实际只有 {len(input_array)} 条。")
        return np.nan
    # --- End Validation ---

    try:
        # 使用 TA-Lib 计算标准差
        # talib.STDDEV 需要 float64 输入
        std_series = talib.STDDEV(
            input_array.astype(np.float64),
            timeperiod=period,
            nbdev=nbdev
        )
        
        # 返回最后一个值
        last_std = std_series[-1]
        
        return float(last_std) if not np.isnan(last_std) else np.nan
    except Exception as e:
        # print(f"[因子计算:STD] 计算 STD({column}, {period}) 时出错: {e}")
        return np.nan