const { User } = require('../models/User');

const getUser = async (req, res) => {
  const { id } = req.params;

  try {
    console.log(JSON.stringify(User));
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    console.log(JSON.stringify(user));
    res.json(user.toJSON());
  } catch (error) {
    res.status(500).json({ message: '获取用户信息失败' });
  }
};

const uploadAvatar = async (req, res) => {
  const { id } = req.params;
  const { file } = req;

  try {
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    user.avatar = file.path;
    await user.save();

    res.json(user.toJSON());
  } catch (error) {
    res.status(500).json({ message: '上传头像失败' });
  }
};

module.exports = {
  getUser,
  uploadAvatar
};
