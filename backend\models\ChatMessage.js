const { DataTypes, Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  // 定义 ChatMessage 模型
  const ChatMessage = sequelize.define('ChatMessage', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    sessionid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {        // 添加外键引用
        model: 'chatsessions', // 引用 chatsessions 表
        key: 'id'
      }
    },
    senderid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {        // 添加外键引用
        model: 'users', // 引用 users 表
        key: 'id'
      }
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false // 例如 'text', 'image', 'file'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true // 文本消息的内容
    },
    fileurl: {
      type: DataTypes.STRING,
      allowNull: true // 文件消息的URL
    },
    filename: {
      type: DataTypes.STRING,
      allowNull: true // 文件消息的文件名
    },
    filesize: {
      type: DataTypes.INTEGER,
      allowNull: true // 文件消息的文件大小 (字节)
    },
    filetype: {
      type: DataTypes.STRING,
      allowNull: true // 文件消息的文件类型 (MIME type)
    },
    createdat: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'chatmessages',
    timestamps: false // 使用 createdat 字段，禁用自动时间戳
  });

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  ChatMessage.associate = function(models) {
    // 一条消息属于一个聊天会话
    ChatMessage.belongsTo(models.ChatSession, {
      foreignKey: 'sessionid',
      as: 'session'
    });
    // 一条消息属于一个发送者 (User)
    ChatMessage.belongsTo(models.User, {
      foreignKey: 'senderid',
      as: 'sender'
    });
  };

  // ------------ ChatMessageService ------------
  // 注意：服务类通常放在单独的 service 文件中更好。
  // 此处保留是为了暂时维持结构，但 findOne/update/delete 中的 sessionDBManager 逻辑已移除，需要后续处理。

  class ChatMessageService {
    /**
     * 创建新消息并返回最新的消息列表
     * @param {number} sessionId - 会话ID
     * @param {Object} messageData - 消息数据
     * @returns {Promise<Array>} - 返回最新的消息列表
     */
    static async create(sessionId, messageData) {
      try {
        // 1. 保存新消息 (使用本文件定义的 ChatMessage 模型)
        const newMessage = await ChatMessage.create({
          sessionid: sessionId,
          senderid: messageData.senderid,
          type: messageData.type,
          content: messageData.content,
          fileurl: messageData.fileurl,
          filename: messageData.filename,
          filesize: messageData.filesize,
          filetype: messageData.filetype,
          createdat: new Date()
        });

        // 2. 获取最新的20条消息，包含发送者信息 (需要 User 模型，通过 associate 关联)
        const latestMessages = await ChatMessage.findAll({
          where: {
            sessionid: sessionId
          },
          include: [{
            model: sequelize.models.User, // 通过 sequelize.models 访问关联模型
            as: 'sender',
            attributes: ['id', 'username', 'avatar']
          }],
          order: [['createdat', 'DESC']],
          limit: 20
          // raw: true, nest: true 在包含关联时可能导致问题，先移除
        });

        // 3. 格式化消息为前端期望的格式
        return latestMessages.reverse().map(msg => ({
          id: msg.id,
          userId: msg.senderid,
          type: msg.type,
          content: msg.content,
          timestamp: msg.createdat,
          status: 'sent', // 状态可能需要单独管理
          fileUrl: msg.fileurl,
          fileName: msg.filename,
          fileSize: msg.filesize,
          fileType: msg.filetype,
          // 访问关联数据
          username: msg.sender ? msg.sender.username : '未知用户',
          avatar: msg.sender ? msg.sender.avatar : 'default-avatar.png'
        }));

      } catch (error) {
        console.error('[ChatMessageService] Failed to create message:', error);
        throw error;
      }
    }

    /**
     * 获取会话的消息列表
     * @param {number} sessionId - 会话ID
     * @param {Object} options - 查询选项
     * @returns {Promise<Array>} - 返回消息列表
     */
    static async findAll(sessionId, options = {}) {
      try {
        const messages = await ChatMessage.findAll({
          where: {
            sessionid: sessionId,
            ...(options.where || {})
          },
          include: [{
            model: sequelize.models.User, // 通过 sequelize.models 访问关联模型
            as: 'sender',
            attributes: ['id', 'username', 'avatar']
          }],
          order: [['createdat', 'DESC']],
          limit: options.limit || 20
          // raw: true, nest: true 在包含关联时可能导致问题，先移除
        });

        return messages.reverse().map(msg => ({
          id: msg.id,
          userId: msg.senderid,
          type: msg.type,
          content: msg.content,
          timestamp: msg.createdat,
          status: 'sent', // 状态可能需要单独管理
          fileUrl: msg.fileurl,
          fileName: msg.filename,
          fileSize: msg.filesize,
          fileType: msg.filetype,
          username: msg.sender ? msg.sender.username : '未知用户',
          avatar: msg.sender ? msg.sender.avatar : 'default-avatar.png'
        }));

      } catch (error) {
        console.error('[ChatMessageService] Failed to find messages:', error);
        throw error;
      }
    }

    // 注意：以下方法依赖于 sessionDBManager，该逻辑已被移除，需要根据实际需求重写或删除
    static async findOne(sessionId, options = {}) {
      console.warn('[ChatMessageService] findOne method needs review: sessionDBManager logic removed.');
      // const { ChatMessage } = await sessionDBManager.getSessionDB(sessionId);
      // return await ChatMessage.findOne(options);
      return await ChatMessage.findOne({ where: { sessionid: sessionId, ...options.where }, ...options }); // 临时实现，可能不符合原意
    }

    static async update(sessionId, values, options = {}) {
      console.warn('[ChatMessageService] update method needs review: sessionDBManager logic removed.');
      // const { ChatMessage } = await sessionDBManager.getSessionDB(sessionId);
      // return await ChatMessage.update(values, options);
      return await ChatMessage.update(values, { where: { sessionid: sessionId, ...options.where }, ...options }); // 临时实现
    }

    static async delete(sessionId, options = {}) {
      console.warn('[ChatMessageService] delete method needs review: sessionDBManager logic removed.');
      // const { ChatMessage } = await sessionDBManager.getSessionDB(sessionId);
      // return await ChatMessage.destroy(options);
      return await ChatMessage.destroy({ where: { sessionid: sessionId, ...options.where }, ...options }); // 临时实现
    }
  }

  // 将模型和（修改后的）服务一起导出可能不是最佳实践，但暂时保留
  // 更好的做法是将 Service 移到单独文件，并让模型文件只导出模型
  ChatMessage.Service = ChatMessageService; 

  return ChatMessage;
}; 