#!/usr/bin/env node

/**
 * 完整的实盘策略流程测试
 * 测试从部署到启动的完整流程
 */

const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

// 导入相关模块
const LiveStrategyAdapter = require('./services/LiveStrategyAdapter');
const UserGroupManager = require('./services/UserGroupManager');
const { LiveStrategy, TradingGroup, GroupStrategy } = require('./database');

async function testCompleteFlow() {
  try {
    console.log('🚀 开始完整实盘策略流程测试...\n');

    // 测试用户ID
    const userId = 1;
    const username = 'test_user';

    // 创建适配器实例
    const adapter = new LiveStrategyAdapter();

    // 1. 测试部署策略到实盘
    console.log('📦 第一步：部署策略到实盘...');
    const deployData = {
      strategyId: 'test_strategy_multi_factor',
      accountId: 'openctp_test',
      initialCapital: 100000,
      commissionRate: 0.0003,
      riskSettings: {
        maxOrderSize: 100,
        maxDailyTrades: 10,
        stopLossPercent: 5
      }
    };

    // 模拟策略YAML
    const mockStrategyYaml = `
trading_type: etf
universe:
  - "SSE.ETF.510300"
  - "SSE.ETF.510500"
  - "SZSE.ETF.159919"
bar_count: 50
data_freq: "day"
order_by:
  formula: "roc_20 * 0.6 + vol_20 * 0.4"
buy_rules:
  formulas:
    - "roc_20 > 0.05"
    - "vol_20 < 0.3"
  at_least_count: 2
sell_rules:
  formulas:
    - "roc_20 < -0.03"
  at_least_count: 1
top_n: 2
weighting_scheme: "equal"
rebalance_interval: "daily"
`;

    // 创建实盘策略记录（模拟部署过程）
    const liveStrategyId = uuidv4();
    const liveStrategy = await LiveStrategy.create({
      id: liveStrategyId,
      userId,
      username,
      strategyId: deployData.strategyId,
      name: `实盘-多因子ETF策略`,
      status: 'stopped',
      accountId: deployData.accountId,
      initialCapital: deployData.initialCapital,
      commissionRate: deployData.commissionRate,
      riskSettings: deployData.riskSettings,
      yaml: mockStrategyYaml,
      createdAt: new Date(),
      lastUpdateTime: new Date()
    });

    console.log(`✅ 策略部署成功，ID: ${liveStrategyId}`);

    // 2. 测试获取实盘策略列表
    console.log('\n📋 第二步：获取实盘策略列表...');
    const strategiesResult = await adapter.getLiveStrategies(userId);
    if (strategiesResult.success) {
      console.log(`✅ 获取到 ${strategiesResult.data.length} 个策略`);
      strategiesResult.data.forEach(strategy => {
        console.log(`  - ${strategy.name} (${strategy.status})`);
      });
    } else {
      console.error(`❌ 获取策略列表失败: ${strategiesResult.error}`);
      return;
    }

    // 3. 测试启动策略
    console.log('\n🚀 第三步：启动实盘策略...');
    console.log(`启动策略: ${liveStrategyId}`);
    
    try {
      const startResult = await adapter.startStrategy(userId, liveStrategyId);
      if (startResult.status === 'success') {
        console.log(`✅ 策略启动成功: ${startResult.message}`);
      } else {
        console.error(`❌ 策略启动失败: ${startResult.error}`);
        return;
      }
    } catch (error) {
      console.error(`❌ 策略启动异常: ${error.message}`);
      return;
    }

    // 4. 检查生成的配置文件
    console.log('\n📁 第四步：检查生成的配置文件...');
    const groupManager = await adapter.getUserGroupManager(userId);
    const groupStatus = await groupManager.getGroupStatus();
    
    console.log('组合状态:', groupStatus);
    
    // 检查配置文件是否生成
    const groupPath = path.dirname(groupManager.group.configPath);
    console.log(`组合目录: ${groupPath}`);
    
    const expectedFiles = [
      'config.yaml',
      'contracts.json',
      'executers.yaml',
      'tdparsers.yaml',
      'tdtraders.yaml',
      'actpolicy.yaml',
      'filters.yaml',
      'logcfg.yaml'
    ];

    console.log('\n检查配置文件:');
    for (const fileName of expectedFiles) {
      const filePath = path.join(groupPath, fileName);
      try {
        await fs.access(filePath);
        console.log(`✅ ${fileName} - 存在`);
      } catch (error) {
        console.log(`❌ ${fileName} - 不存在`);
      }
    }

    // 5. 显示关键配置文件内容
    console.log('\n📄 第五步：显示关键配置文件内容...');
    
    // 显示contracts.json
    try {
      const contractsPath = path.join(groupPath, 'contracts.json');
      const contractsContent = await fs.readFile(contractsPath, 'utf8');
      console.log('\ncontracts.json 内容:');
      console.log(contractsContent);
    } catch (error) {
      console.log('❌ 无法读取contracts.json');
    }

    // 显示config.yaml的关键部分
    try {
      const configPath = path.join(groupPath, 'config.yaml');
      const configContent = await fs.readFile(configPath, 'utf8');
      console.log('\nconfig.yaml 内容:');
      console.log(configContent.substring(0, 500) + '...');
    } catch (error) {
      console.log('❌ 无法读取config.yaml');
    }

    // 6. 等待一段时间后停止策略
    console.log('\n⏱️ 第六步：等待5秒后停止策略...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('🛑 停止实盘策略...');
    try {
      const stopResult = await adapter.stopStrategy(userId, liveStrategyId);
      if (stopResult.status === 'success') {
        console.log(`✅ 策略停止成功: ${stopResult.message}`);
      } else {
        console.error(`❌ 策略停止失败: ${stopResult.error}`);
      }
    } catch (error) {
      console.error(`❌ 策略停止异常: ${error.message}`);
    }

    // 7. 最终状态检查
    console.log('\n📊 第七步：最终状态检查...');
    const finalStatus = await groupManager.getGroupStatus();
    console.log('最终组合状态:', finalStatus);

    const finalStrategy = await LiveStrategy.findByPk(liveStrategyId);
    console.log('最终策略状态:', {
      id: finalStrategy.id,
      name: finalStrategy.name,
      status: finalStrategy.status,
      startTime: finalStrategy.startTime,
      stopTime: finalStrategy.stopTime
    });

    console.log('\n🎉 完整流程测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testCompleteFlow().then(() => {
    console.log('\n测试结束');
    process.exit(0);
  }).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = testCompleteFlow;
