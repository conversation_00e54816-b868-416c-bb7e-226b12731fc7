#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tick数据处理模块
提供独立的tick数据获取、处理和管理功能
保证与主服务器逻辑的隔离
"""

import datetime
import logging
import time
import pytz
from typing import Dict, List, Optional, Any
from pytdx.hq import TdxHq_API
from pytdx.exhq import TdxExHq_API

# 配置日志
logger = logging.getLogger(__name__)

# 时区定义
SHANGHAI_TZ = pytz.timezone('Asia/Shanghai')

class TickDataHandler:
    """Tick数据处理器 - 独立的tick数据获取和处理逻辑"""
    
    def __init__(self, market_map: Dict[str, int], hq_connection_func, exhq_connection_func):
        """
        初始化Tick数据处理器
        
        Args:
            market_map: 市场代码映射字典
            hq_connection_func: 获取HQ API连接的函数
            exhq_connection_func: 获取ExHQ API连接的函数
        """
        self.market_map = market_map
        self.get_hq_api = hq_connection_func
        self.get_exhq_api = exhq_connection_func
        
        # Tick数据缓存
        self.tick_cache = {}
        self.cache_expire_seconds = 1  # 缓存1秒过期
        
        logger.info("[TickDataHandler] Tick数据处理器初始化完成")
    
    def get_api_type(self, market_code: int) -> str:
        """根据市场代码确定API类型"""
        # Standard HQ markets (SZ, SH, BJ A-shares/indices)
        if market_code in [0, 1, 2]:
            return 'hq'
        # Most others seem to be Extended ExHq
        else:
            return 'ex'
    
    def get_real_tick_data(self, market: str, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取真实的tick数据（基于实时报价）
        
        Args:
            market: 市场代码字符串
            symbol: 证券代码
            
        Returns:
            tick数据字典或None
        """
        try:
            # 检查缓存
            cache_key = f"{market}:{symbol}"
            current_time = time.time()
            
            if cache_key in self.tick_cache:
                cached_data, cache_time = self.tick_cache[cache_key]
                if current_time - cache_time < self.cache_expire_seconds:
                    return cached_data
            
            # 解析市场代码
            market_code = self._parse_market_code(market)
            if market_code is None:
                logger.warning(f"[TickDataHandler] 无效的市场代码: {market}")
                return None
            
            # 获取API类型和实例
            api_type = self.get_api_type(market_code)
            api = self._get_api_instance(api_type)
            
            if not api:
                logger.warning(f"[TickDataHandler] 无法连接到 TDX {api_type.upper()} 服务器")
                return None
            
            try:
                # 获取实时报价数据
                raw_data = self._fetch_quote_data(api, api_type, market_code, symbol)
                
                if raw_data:
                    # 转换为标准tick格式
                    tick_data = self._convert_to_tick_format(raw_data, market, symbol)
                    
                    # 更新缓存
                    self.tick_cache[cache_key] = (tick_data, current_time)
                    
                    logger.debug(f"[TickDataHandler] 获取tick数据成功: {market}:{symbol}")
                    return tick_data
                
            finally:
                # 确保断开连接
                if api and hasattr(api, 'disconnect'):
                    api.disconnect()
            
            return None
            
        except Exception as e:
            logger.error(f"[TickDataHandler] 获取 {market}:{symbol} tick数据错误: {e}")
            return None
    
    def get_historical_tick_data(self, market: str, symbol: str, date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取历史tick数据（如果支持）
        
        Args:
            market: 市场代码字符串
            symbol: 证券代码
            date: 日期字符串 (YYYY-MM-DD)，默认为今天
            
        Returns:
            tick数据列表
        """
        try:
            if date is None:
                date = datetime.datetime.now().strftime('%Y-%m-%d')
            
            # 解析市场代码
            market_code = self._parse_market_code(market)
            if market_code is None:
                logger.warning(f"[TickDataHandler] 无效的市场代码: {market}")
                return []
            
            # 获取API类型和实例
            api_type = self.get_api_type(market_code)
            api = self._get_api_instance(api_type)
            
            if not api:
                logger.warning(f"[TickDataHandler] 无法连接到 TDX {api_type.upper()} 服务器")
                return []
            
            try:
                # 尝试获取历史tick数据
                tick_list = []
                
                if api_type == 'hq' and hasattr(api, 'get_history_transaction_data'):
                    # 使用HQ API获取历史成交数据
                    raw_data = api.get_history_transaction_data(market_code, symbol, 0, 2000)
                    if raw_data:
                        tick_list = self._convert_transaction_to_tick(raw_data, market, symbol, date)
                
                elif api_type == 'ex' and hasattr(api, 'get_transaction_data'):
                    # 使用ExHQ API获取成交数据
                    raw_data = api.get_transaction_data(market_code, symbol, 0, 2000)
                    if raw_data:
                        tick_list = self._convert_transaction_to_tick(raw_data, market, symbol, date)
                
                logger.info(f"[TickDataHandler] 获取历史tick数据成功: {market}:{symbol}, 数量: {len(tick_list)}")

            finally:
                # 确保断开连接
                if api and hasattr(api, 'disconnect'):
                    api.disconnect()

            return tick_list
            
        except Exception as e:
            logger.error(f"[TickDataHandler] 获取 {market}:{symbol} 历史tick数据错误: {e}")
            return []
    
    def _parse_market_code(self, market: str) -> Optional[int]:
        """解析市场代码"""
        try:
            # 尝试直接转换为整数
            market_int = int(market)
            if market_int >= 0:
                return market_int
        except ValueError:
            # 尝试从映射表获取
            return self.market_map.get(market.lower())
        
        return None
    
    def _get_api_instance(self, api_type: str):
        """获取API实例"""
        if api_type == 'hq':
            return self.get_hq_api()
        elif api_type == 'ex':
            return self.get_exhq_api()
        return None
    
    def _fetch_quote_data(self, api, api_type: str, market_code: int, symbol: str):
        """获取报价数据"""
        if api_type == 'hq':
            # 使用 TdxHq_API 的 get_security_quotes 方法
            assert isinstance(api, TdxHq_API)
            data = api.get_security_quotes([(market_code, symbol)])
            return data[0] if data and len(data) > 0 else None
        else:
            # 使用 TdxExHq_API 的 get_instrument_quote 方法
            assert isinstance(api, TdxExHq_API)
            return api.get_instrument_quote(market_code, symbol)
    
    def _convert_to_tick_format(self, raw_data: Dict, market: str, symbol: str) -> Dict[str, Any]:
        """将原始数据转换为标准tick格式"""
        current_time = datetime.datetime.now(SHANGHAI_TZ)
        
        return {
            'code': symbol,
            'market': market,
            'name': raw_data.get('name', ''),
            'price': float(raw_data.get('price', 0)),
            'last_close': float(raw_data.get('last_close', 0)),
            'open': float(raw_data.get('open', 0)),
            'high': float(raw_data.get('high', 0)),
            'low': float(raw_data.get('low', 0)),
            'volume': int(raw_data.get('vol', 0)),
            'amount': float(raw_data.get('amount', 0)),
            'bid1': float(raw_data.get('bid1', 0)),
            'bid_vol1': int(raw_data.get('bid_vol1', 0)),
            'ask1': float(raw_data.get('ask1', 0)),
            'ask_vol1': int(raw_data.get('ask_vol1', 0)),
            'bid2': float(raw_data.get('bid2', 0)),
            'bid_vol2': int(raw_data.get('bid_vol2', 0)),
            'ask2': float(raw_data.get('ask2', 0)),
            'ask_vol2': int(raw_data.get('ask_vol2', 0)),
            'bid3': float(raw_data.get('bid3', 0)),
            'bid_vol3': int(raw_data.get('bid_vol3', 0)),
            'ask3': float(raw_data.get('ask3', 0)),
            'ask_vol3': int(raw_data.get('ask_vol3', 0)),
            'bid4': float(raw_data.get('bid4', 0)),
            'bid_vol4': int(raw_data.get('bid_vol4', 0)),
            'ask4': float(raw_data.get('ask4', 0)),
            'ask_vol4': int(raw_data.get('ask_vol4', 0)),
            'bid5': float(raw_data.get('bid5', 0)),
            'bid_vol5': int(raw_data.get('bid_vol5', 0)),
            'ask5': float(raw_data.get('ask5', 0)),
            'ask_vol5': int(raw_data.get('ask_vol5', 0)),
            'timestamp': int(current_time.timestamp() * 1000),  # 毫秒时间戳
            'time': current_time.strftime('%H:%M:%S'),
            'date': current_time.strftime('%Y-%m-%d'),
            'datetime': current_time.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def _convert_transaction_to_tick(self, raw_data: List, market: str, symbol: str, date: str) -> List[Dict[str, Any]]:
        """将成交数据转换为tick格式"""
        tick_list = []
        
        for item in raw_data:
            try:
                # 解析时间
                time_str = item.get('time', '')
                if time_str:
                    # 假设时间格式为 "HH:MM:SS" 或类似格式
                    datetime_str = f"{date} {time_str}"
                    dt = datetime.datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                    dt = SHANGHAI_TZ.localize(dt)
                else:
                    dt = datetime.datetime.now(SHANGHAI_TZ)
                
                tick_data = {
                    'code': symbol,
                    'market': market,
                    'price': float(item.get('price', 0)),
                    'volume': int(item.get('vol', 0)),
                    'amount': float(item.get('amount', 0)),
                    'type': item.get('type', ''),  # 买卖类型
                    'timestamp': int(dt.timestamp() * 1000),
                    'time': dt.strftime('%H:%M:%S'),
                    'date': dt.strftime('%Y-%m-%d'),
                    'datetime': dt.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                tick_list.append(tick_data)
                
            except Exception as e:
                logger.warning(f"[TickDataHandler] 转换成交数据错误: {e}")
                continue
        
        return tick_list
    
    def clear_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        for key, (_, cache_time) in self.tick_cache.items():
            if current_time - cache_time >= self.cache_expire_seconds:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.tick_cache[key]
        
        if expired_keys:
            logger.debug(f"[TickDataHandler] 清理过期缓存: {len(expired_keys)} 项")
