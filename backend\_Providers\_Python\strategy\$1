Traceback (most recent call last):
  File "D:\projects\quantquart\backend\_Providers\_Python\strategy\strategy_server.py", line 33, in <module>
    from portfolio.portfolio_center import run_backtest
  File "D:\projects\quantquart\backend\_Providers\_Python\strategy\portfolio\portfolio_center.py", line 5, in <module>
    import pandas as pd
  File "D:\projects\quantquart\python_venv\lib\site-packages\pandas\__init__.py", line 22, in <module>
    from pandas.compat import (
  File "D:\projects\quantquart\python_venv\lib\site-packages\pandas\compat\__init__.py", line 15, in <module>
    from pandas.compat.numpy import (
  File "D:\projects\quantquart\python_venv\lib\site-packages\pandas\compat\numpy\__init__.py", line 7, in <module>
    from pandas.util.version import Version
  File "D:\projects\quantquart\python_venv\lib\site-packages\pandas\util\__init__.py", line 7, in <module>
    from pandas.core.util.hashing import (  # noqa
  File "D:\projects\quantquart\python_venv\lib\site-packages\pandas\core\util\hashing.py", line 17, in <module>
    from pandas._libs import lib
  File "pandas\_libs\lib.pyx", line 86, in init pandas._libs.lib
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 699, in _load_unlocked
KeyboardInterrupt
^C