初始化: 设置日志记录器。
加载策略配置: 读取指定的策略 `index.yaml` 文件。
解析配置: 从加载的配置中提取策略名称 (`strategy_id`)、策略参数 (`universe`, `factors` 等，打包到 `strategy_params` 字典)、回测参数 (`stime`, `etime`, `capital` 等) 和数据参数 (`mode`, `path`)。
设置输出目录: 根据提取的 `strategy_id` 创建或确认输出目录 (默认为 `outputs/[strategy_id]`)。
动态生成日志配置: 在输出目录中创建 `logcfgbt.yaml` 文件，用于配置回测过程中的日志记录。
动态生成引擎配置: 在输出目录中创建 `configbt.yaml` 文件，包含从 `index.yaml` 提取的初始资金、手续费率、滑点率等引擎级别的基础设置。
创建回测引擎: 实例化 `WtBtEngine`，明确指定引擎类型为 `EngineType.ET_SEL`，并关联上一步生成的日志配置文件路径 (如果生成成功)。
初始化引擎: 调用 `engine.init()` 方法，传入 `common` 目录路径和上一步生成的 `configbt.yaml` 文件的路径 (如果生成成功)。引擎会加载基础元数据和引擎配置。
配置引擎参数: 
    - 调用 `engine.configBacktest()` 设置回测的开始和结束时间 (从 `index.yaml` 的 `backtest` 部分提取)。
    - 调用 `engine.configBTStorage()` 设置历史数据的模式 (`mode`) 和根目录路径 (`path`) (从 `index.yaml` 的 `data` 部分提取，并会解析相对路径)。
提交引擎配置: 调用 `engine.commitBTConfig()` 应用所有引擎配置。
创建策略实例: 根据提取的 `strategy_id` 实例化 `MultiFactorStrategy` 类。
数据准备 (占位符): 如果数据模式 (`data_mode`) 配置为 'csv'，则调用 `prepare_csv` 占位符函数，传入标的列表 (`universe`) 和数据根目录路径 (`path`)。 **注意: `prepare_csv` 当前仅为占位符，需要用户实现具体的数据检查和准备逻辑，确保 CSV 文件按 `wtpy` 要求存放。**
设置策略: 调用 `engine.set_sel_strategy()`，将创建的策略实例、策略名称 (`strategy_id`) 和策略参数字典 (`strategy_params`) **按位置顺序**传递给引擎。引擎会调用策略的 `on_init` 方法并传入参数。
运行回测: 调用 `engine.run_backtest()` 启动完整的回测循环。引擎会根据配置加载数据、驱动时间、调用策略的 `on_calculate` 等回调函数。
释放资源: 回测结束后，调用 `engine.release_backtest()` 释放引擎占用的资源。
返回结果: 从策略实例 (`MultiFactorStrategy` 实例内部的 `backtest_results` 属性) 获取收集到的回测指标和记录，并将其作为字典返回给调用者。