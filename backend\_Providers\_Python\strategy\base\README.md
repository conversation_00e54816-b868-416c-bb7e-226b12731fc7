# base 目录说明

## 1. live_engine.py 及其架构要点

### 数据订阅适配中心
- 所有行情数据的订阅与退订，均通过 live_engine 内部的“适配中心”统一处理。
- 适配中心负责对接不同的数据渠道（如通达信、第三方API等），实现多渠道的集中适配和模块化管理，便于扩展和维护。
- 任何策略的数据需求变更，均通过适配中心发起，保证接口一致性和可控性。

### 用户独立进程
- 每一个用户在实盘运行时，系统会为其启动一个独立的 live_engine 进程。
- 每个 live_engine 进程运行在用户专属的独立目录下，互不干扰，便于资源隔离和问题定位。

### 日志管理
- 每个 live_engine 进程的相关日志，均保存在其独立目录下的 logs 子目录中。
- 日志内容涵盖主流程、线程入口、异常、退出点等，便于排查和追踪。

### 策略类集中管理
- 一个 live_engine 进程可同时管理和运行多个策略。
- 不同类别的策略有对应的类代码文件，所有策略类均统一放置在 strategy/base 目录下，集中管理，避免重复和分散。
- 策略类的扩展和维护，均在本目录下进行，便于统一规范和版本控制。

---

如需扩展新的数据渠道或策略类型，请遵循上述架构原则，在本目录下进行集中适配和管理。 