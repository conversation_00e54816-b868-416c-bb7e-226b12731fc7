import { KLine, KLineData } from '@/shared_types/market';
import { BaseSignal, SignalParameter, SignalType } from '../BaseSignal';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';

/**
 * 未来涨幅信号
 * 检查未来x周期内的价格涨幅是否达到指定阈值
 * 当未来涨幅达到阈值时产生买入信号
 * 注意：此信号仅用于研究模式，不能用于实盘
 */
export class FutureReturnSignal extends BaseSignal {
  private period: number;
  private threshold: number;

  /**
   * 获取信号参数列表
   */
  static async getParameters(): Promise<SignalParameter[]> {
    return Promise.resolve([
      {
        name: 'period',
        paramType: 'number',
        default: 20,
        description: '计算周期',
        minValue: 5,
        maxValue: 100,
        step: 1
      },
      {
        name: 'threshold',
        paramType: 'number',
        default: 2,
        description: '收益率阈值%',
        minValue: 0.1,
        maxValue: 100,
        step: 1
      }
    ]);
  }

  constructor(config: SignalConfig) {
    super(config);
    
    // 设置参数
    this.period = this.parameters.period;
    this.threshold = this.parameters.threshold;

    // 验证参数
    if (this.period <= 0) {
      throw new Error('Look forward period must be positive');
    }
    if (this.threshold <= 0) {
      throw new Error('Threshold must be positive');
    }
  }

  calculateSignals(kline: KLine, params: Record<string, any>): MarketEvents.SignalItem[] {
    const signals: MarketEvents.SignalItem[] = [];
    const period = params.period as number;
    const threshold = params.threshold as number;
    const PREVIOUS_PERIODS = 7; // 前7个周期
    const data = kline.data;

    console.log('[FutureReturnSignal] 计算信号 len：', data.length - period );

    // 遍历每个K线，计算未来涨幅
    for (let i = PREVIOUS_PERIODS; i < data.length - period; i++) {
      const currentPrice = data[i].close;
      
      // 检查是否突破前7个周期的最高价
      let previousHigh = -Infinity;
      for (let k = i - PREVIOUS_PERIODS; k < i; k++) {
        if (data[k].close > previousHigh) {
          previousHigh = data[k].close;
        }
      }
      
      // 如果当前价格没有突破前7个周期的最高价，跳过
      if (currentPrice <= previousHigh) {
        continue;
      }
      
      // 在未来lookForward周期内寻找最高收盘价
      let maxFuturePrice = currentPrice;
      let maxFutureIndex = i;
      
      for (let j = i + 1; j <= i + period && j < data.length; j++) {
        if (data[j].close > maxFuturePrice) {
          maxFuturePrice = data[j].close;
          maxFutureIndex = j;
        }
      }

      // 计算最大涨幅
      const maxReturn = (maxFuturePrice - currentPrice) / currentPrice * 100;
      
      // 如果最大涨幅达到阈值，生成买入信号
      if (maxReturn >= threshold) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.BUY,
          price: currentPrice,
          index: i
        };
        
        this.addSignalTrigger(signal);
        signals.push(signal);
        
        // 跳过到下一个周期
        i += period;
      }
    }

    return signals;
  }
} 