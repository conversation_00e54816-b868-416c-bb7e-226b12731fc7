import json
import time
import requests
import logging
from typing import Union, Dict, List, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Connection': 'keep-alive',
}

# --- Helper Functions ---

def get_exchange(code: str) -> Optional[str]:
    """Determine the exchange based on the stock code prefix."""
    if code.startswith('6'):
        return 'SSE'
    elif code.startswith('0') or code.startswith('3'):
        return 'SZSE'
    elif code.startswith('8') or code.startswith('4'): # BSE (Beijing Stock Exchange)
        return 'BSE'
    else:
        logging.warning(f"Could not determine exchange for code: {code}")
        return None # Or handle other exchanges if necessary

def format_stock_data(code: str, name: str) -> Optional[Dict[str, str]]:
    """Format stock data into the required dictionary structure."""
    exchange = get_exchange(code)
    if exchange:
        return {
            "symbol": f"{exchange}.STOCK.{code}",
            "name": name.strip()
        }
    return None

def save_to_json(data: List[Dict[str, str]], filename: str):
    """Save the list of stock data to a JSON file."""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logging.info(f"Successfully saved {len(data)} stocks to {filename}")
    except IOError as e:
        logging.error(f"Error saving data to {filename}: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred while saving to {filename}: {e}")

def fetch_url(url: str, headers=None, retries=3, delay=2) -> Optional[str]:
    """Fetch URL content with retries."""
    if headers is None:
        headers = DEFAULT_HEADERS
    for attempt in range(retries):
        try:
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
            # Try decoding with common encodings
            try:
                return response.content.decode('gbk')
            except UnicodeDecodeError:
                try:
                    return response.content.decode('utf-8')
                except UnicodeDecodeError:
                     logging.warning(f"Could not decode content from {url} using gbk or utf-8. Using raw content.")
                     return response.text # Fallback to requests' best guess
        except requests.exceptions.RequestException as e:
            logging.warning(f"Request failed for {url} (attempt {attempt + 1}/{retries}): {e}")
            if attempt < retries - 1:
                time.sleep(delay)
            else:
                logging.error(f"Failed to fetch {url} after {retries} attempts.")
                return None
    return None # Should not be reached if retries > 0, but added for explicit return 