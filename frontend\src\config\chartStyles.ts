/**
 * 图表样式配置
 * 定义了K线图表的样式，包括颜色、线条宽度等
 */

// TradingView风格的颜色配置
export const tradingViewColors = {
  // 涨跌颜色
  upColor: '#555555',     // 上涨蜡烛填充色（绿色）
  downColor: '#ef5350',   // 下跌蜡烛填充色（红色）
  noChangeColor: '#888888', // 无变化颜色
  
  // 边框颜色
  upBorderColor: '#b5b5b5',    // 上涨蜡烛边框色（浅灰色）
  downBorderColor: '#888888',  // 下跌蜡烛边框色（深灰色）
  
  // 线宽设置
  borderWidth: 1,        // 蜡烛边框宽度
  
  // 背景色
  backgroundColor: '#ffffff',  // 图表背景色
  
  // 网格线
  gridLineColor: '#f0f0f0',    // 网格线颜色
  
  // 十字线
  crosshairLineColor: '#888888', // 十字线颜色
  crosshairTextBackgroundColor: '#686d76', // 十字线文本背景色
  
  // 时间轴
  timeAxisTextColor: '#76808f',  // 时间轴文本颜色
  
  // 价格轴
  priceAxisTextColor: '#76808f',  // 价格轴文本颜色
  priceAxisLineColor: '#ededed',  // 价格轴线条颜色
};

export const ictStyle = {
    candle: {
        bar: {
          upColor: "#37c034",
          upBorderColor: "#888888",
          upWickColor: "#888888",
          downColor: "#828282",
          downBorderColor: "#505050",
          downWickColor: "#505050",
        },
        margin: {
          top: 0.05, // 减小顶部空白
          bottom: 0.05 // 减小底部空白
        }
    },
    yAxis: {
      paddingTop: 0, // 减小顶部边距
      paddingBottom: 10 // 底部留少量空间
    }
}

// 获取TradingView风格的图表配置
export const getTradingViewStyles = () => {
  // 定义线条样式
  const lineStyles = {
    // 水平和垂直线段使用小圆点虚线
    dotted: {
      style: 'dashed',
      dashedValue: [2, 2],
      size: 1,
      color: '#888888'
    },
    // 价格通道使用实线
    solid: {
      style: 'solid',
      size: 1,
      color: '#888888'
    }
  };

  return {
    grid: {
      show: true,
      horizontal: {
        show: true,
        color: tradingViewColors.gridLineColor,
        size: 1
      },
      vertical: {
        show: true,
        color: tradingViewColors.gridLineColor,
        size: 1
      }
    },
    candle: {
      type: 'candle_solid',
      // 设置边框，无论涨跌
      styles: {
        upColor: tradingViewColors.upColor,
        downColor: tradingViewColors.downColor,
        noChangeColor: tradingViewColors.noChangeColor,
        upBorderColor: tradingViewColors.upBorderColor,
        downBorderColor: tradingViewColors.downBorderColor,
        noChangeBorderColor: tradingViewColors.noChangeColor,
        borderWidth: tradingViewColors.borderWidth
      }
    },
    technicalIndicator: {
      bar: {
        upColor: tradingViewColors.upColor,
        downColor: tradingViewColors.downColor,
        noChangeColor: tradingViewColors.noChangeColor
      },
      line: {
        size: 1
      }
    },
    xAxis: {
      axisLine: {
        color: tradingViewColors.priceAxisLineColor
      },
      tickLine: {
        color: tradingViewColors.priceAxisLineColor
      },
      tickText: {
        color: tradingViewColors.timeAxisTextColor
      }
    },
    yAxis: {
      axisLine: {
        color: tradingViewColors.priceAxisLineColor
      },
      tickLine: {
        color: tradingViewColors.priceAxisLineColor
      },
      tickText: {
        color: tradingViewColors.priceAxisTextColor
      }
    },
    crosshair: {
      horizontal: {
        line: {
          color: tradingViewColors.crosshairLineColor
        },
        text: {
          backgroundColor: tradingViewColors.crosshairTextBackgroundColor
        }
      },
      vertical: {
        line: {
          color: tradingViewColors.crosshairLineColor
        },
        text: {
          backgroundColor: tradingViewColors.crosshairTextBackgroundColor
        }
      }
    }
  };
};