import React, { useState, useEffect } from 'react';
import { Tabs, Table, Card, Row, Col, Statistic, Tag, Empty, Spin, Space, Button, Tooltip, message } from 'antd';
import {
  FileTextOutlined,
  StockOutlined,
  HistoryOutlined,
  LineChartOutlined,
  SignalFilled,
  ReloadOutlined
} from '@ant-design/icons';
import { LiveStrategyInfo } from '@/shared_types/trade';
import { EventBus } from '@/events/eventBus';
import { StrategyEvents } from '@/events/events';

interface ExpandedRowProps {
  record: LiveStrategyInfo;
}

interface RuntimeData {
  strategy_id: string;
  strategy_type: string;
  signals: Array<{
    strategy_id: string;
    timestamp: string;
    target_symbols: string[];
    logical_positions: Record<string, number>;
    close_positions: string[];
    open_positions: Array<{symbol: string, quantity: number, price: number}>; // 更新为对象数组
    commission: number;
    final_balance: number;
    ratio: number;
    actual_initial_capital: number;
    base_capital: number;
  }>;
  latest_signal: any;
  total_signals: number;
  initial_capital: number;
  base_capital: number;
  ratio: number;
  error?: string;
}

const ExpandedRow: React.FC<ExpandedRowProps> = ({ record }) => {
  const [runtimeData, setRuntimeData] = useState<RuntimeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 新增：刷新持仓数据函数
  const handleRefreshPositions = () => {
    setLoading(true);
    setError(null);
    
    console.log('[前端-运行时数据] 手动刷新持仓数据，策略ID:', record.id);
    
    // 使用事件总线获取运行时数据
    EventBus.emit(StrategyEvents.Types.GET_RUNTIME_DATA, {
      liveStrategyId: record.id,
      callback: (success: boolean, data?: any, error?: string) => {
        console.log('[前端-运行时数据] 刷新回调:', { success, error, dataReceived: !!data });
        
        if (success && data) {
          console.log('[前端-运行时数据] 刷新获取到后端数据:', data);
          setRuntimeData(data);
          message.success('持仓数据刷新成功');
        } else {
          const errorMsg = error || '刷新持仓数据失败';
          console.error('[前端-运行时数据] 刷新失败:', errorMsg);
          setError(errorMsg);
          message.error(`刷新失败: ${errorMsg}`);
        }
        setLoading(false);
      }
    });
  };

  useEffect(() => {
    const fetchRuntimeData = () => {
      setLoading(true);
      setError(null);
      
      console.log('[前端-运行时数据] 发送获取运行时数据事件，策略ID:', record.id);
      
      // 使用事件总线获取运行时数据
      EventBus.emit(StrategyEvents.Types.GET_RUNTIME_DATA, {
        liveStrategyId: record.id,
        callback: (success: boolean, data?: any, error?: string) => {
          console.log('[前端-运行时数据] 收到回调:', { success, error, dataReceived: !!data });
          
          if (success && data) {
            console.log('[前端-运行时数据] 获取到后端数据:', data);
            console.log('[前端-运行时数据] 数据结构详情:', {
              strategy_id: data?.strategy_id,
              strategy_type: data?.strategy_type,
              signals_count: data?.signals?.length || 0,
              total_signals: data?.total_signals,
              signals: data?.signals,
              initial_capital: data?.initial_capital,
              base_capital: data?.base_capital,
              ratio: data?.ratio
            });
            if (data?.signals && data.signals.length > 0) {
              console.log('[前端-运行时数据] 第一个信号详情:', data.signals[0]);
              console.log('[前端-运行时数据] 最后一个信号详情:', data.signals[data.signals.length - 1]);
            }
            setRuntimeData(data);
          } else {
            const errorMsg = error || '获取运行数据失败';
            console.error('[前端-运行时数据] 获取数据失败:', errorMsg);
            setError(errorMsg);
          }
          setLoading(false);
        }
      });
    };
    
    fetchRuntimeData();
  }, [record.id]);

  const positionColumns = [
    { title: '股票代码', dataIndex: 'symbol', key: 'symbol', width: '12%' },
    { title: '股票名称', dataIndex: 'name', key: 'name', width: '12%' },
    { 
      title: '逻辑持仓', 
      dataIndex: 'logicalPosition', 
      key: 'logicalPosition', 
      width: '10%',
      render: (value: number) => value ? value.toFixed(0) : '-'
    },
    { 
      title: '预期持仓', 
      dataIndex: 'expectedPosition', 
      key: 'expectedPosition', 
      width: '10%',
      render: (value: number) => value ? value.toFixed(0) : '-'
    },
    { 
      title: '资金比例', 
      dataIndex: 'ratio', 
      key: 'ratio', 
      width: '8%',
      render: (value: number) => value ? `${(value * 100).toFixed(1)}%` : '-'
    },
    { 
      title: '当前价格', 
      dataIndex: 'currentPrice', 
      key: 'currentPrice', 
      width: '10%', 
      render: (p: number) => p ? p.toFixed(2) : '-'
    },
    { 
      title: '市值', 
      dataIndex: 'marketValue', 
      key: 'marketValue', 
      width: '12%', 
      render: (v: number) => v ? v.toFixed(2) : '-'
    },
    // 兼容旧格式的字段
    { title: '持仓数量', dataIndex: 'quantity', key: 'quantity', width: '10%' },
    { title: '成本价格', dataIndex: 'cost', key: 'cost', width: '10%', render: (c: number) => c ? c.toFixed(2) : '-' },
    {
      title: '浮动盈亏',
      dataIndex: 'profit',
      key: 'profit',
      width: '10%',
      render: (p: number) => <span style={{ color: p >= 0 ? '#52c41a' : '#f5222d' }}>{p.toFixed(2)}</span>,
    },
    {
      title: '盈亏比例',
      dataIndex: 'profitPercent',
      key: 'profitPercent',
      width: '10%',
      render: (p: number) => <span style={{ color: p >= 0 ? '#52c41a' : '#f5222d' }}>{p.toFixed(2)}%</span>,
    },
  ];

  const tradeColumns = [
    { title: '交易时间', dataIndex: 'time', key: 'time', width: '18%' },
    { title: '股票代码', dataIndex: 'symbol', key: 'symbol', width: '12%' },
    { title: '股票名称', dataIndex: 'name', key: 'name', width: '12%' },
    {
      title: '方向',
      dataIndex: 'direction',
      key: 'direction',
      width: '10%',
      render: (d: string) => <Tag color={d === 'buy' ? '#108ee9' : '#f50'}>{d === 'buy' ? '买入' : '卖出'}</Tag>,
    },
    { title: '数量', dataIndex: 'quantity', key: 'quantity', width: '10%' },
    { title: '价格', dataIndex: 'price', key: 'price', width: '12%', render: (p: number) => p.toFixed(2) },
    { title: '金额', dataIndex: 'amount', key: 'amount', width: '15%', render: (a: number) => a.toFixed(2) },
    { title: '手续费', dataIndex: 'fee', key: 'fee', width: '12%', render: (f: number) => f ? f.toFixed(2) : '0.00' },
  ];

  const renderLogs = () => (
    <Card size="small" bodyStyle={{ padding: 12, maxHeight: 300, overflow: 'auto' }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: 30 }}><Spin size="small" tip="加载日志..." /></div>
      ) : error ? (
        <Empty description={error} />
      ) : !runtimeData ? (
        <Empty description="暂无运行数据" />
      ) : (
        <>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={8}>
              <Card size="small">
                <Statistic title="策略类型" value={runtimeData.strategy_type || '未知'} />
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small">
                <Statistic title="信号总数" value={runtimeData.total_signals || 0} />
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small">
                <Statistic title="初始资金" value={runtimeData.initial_capital || 0} precision={2} suffix="元" />
              </Card>
            </Col>
          </Row>

          <div style={{ background: '#f5f5f5', padding: 12, borderRadius: 4, fontSize: 12, color: '#666' }}>
              策略运行状态: 正常 | 最后更新: {runtimeData.latest_signal?.timestamp || '未知'}
          </div>
        </>
      )}
    </Card>
  );

  const renderPositions = () => (
    <Card 
      size="small" 
      bodyStyle={{ padding: 12 }}
      title={
        <Space>
          <StockOutlined />
          <span>持仓明细</span>
          <Tooltip title="刷新持仓数据">
            <Button
              icon={<ReloadOutlined />}
              size="small"
              type="text"
              onClick={handleRefreshPositions}
              loading={loading}
            />
          </Tooltip>
        </Space>
      }
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: 30 }}><Spin size="small" tip="加载持仓数据..." /></div>
      ) : error ? (
        <Empty description={error} />
      ) : !runtimeData?.signals?.length ? (
        <Empty description="暂无持仓" />
      ) : (
        <Table
          columns={positionColumns}
          dataSource={(() => {
            // 从最新信号中获取当前持仓信息
            const latestSignal = runtimeData.signals[0]; // 最新信号在数组第一个位置
            const currentPositions = latestSignal.logical_positions || {};
            
            console.log('[前端-持仓明细] 原始持仓数据:', currentPositions);
            console.log('[前端-持仓明细] 持仓数据类型:', typeof currentPositions);
            console.log('[前端-持仓明细] 策略初始资金:', latestSignal.actual_initial_capital);
            console.log('[前端-持仓明细] 基准资金:', latestSignal.base_capital);
            
            // 计算资金比例
            const baseCapital = latestSignal.base_capital || 100000;
            const actualInitialCapital = latestSignal.actual_initial_capital || 100000;
            const capitalRatio = actualInitialCapital / baseCapital;
            
            console.log('[前端-持仓明细] 资金比例:', capitalRatio);
            
            // 转换为表格数据格式
            const positions: any[] = [];
            
            // 检查logical_positions的类型
            if (Array.isArray(currentPositions)) {
              // 如果是数组，处理数组格式
              currentPositions.forEach((item, index) => {
                if (typeof item === 'string') {
                  // 处理字符串格式，如 "'SZSE.ETF.159857': 154700"
                  const match = item.match(/'([^']+)':\s*(\d+)/);
                  if (match) {
                    const symbol = match[1];
                    const logicalQuantity = parseInt(match[2]);
                    const actualQuantity = Math.round(logicalQuantity * capitalRatio); // 转换为实际持仓数量
                    positions.push({
                      symbol: symbol,
                      name: symbol.split('.').pop() || symbol,
                      logicalPosition: logicalQuantity,  // 逻辑持仓
                      expectedPosition: actualQuantity,   // 预期持仓
                      ratio: capitalRatio,               // 资金比例
                      currentPrice: 0,                   // 当前价格（暂不获取）
                      marketValue: 0,                    // 市值（暂不计算）
                      quantity: actualQuantity,          // 兼容旧格式
                      price: 0,                          // 兼容旧格式
                      cost: 0,                           // 兼容旧格式
                      profit: 0,                         // 兼容旧格式
                      profitPercent: 0,                  // 兼容旧格式
                      key: symbol,
                    });
                  }
                }
              });
            } else if (typeof currentPositions === 'object' && currentPositions !== null) {
              // 如果是对象，直接处理
              Object.entries(currentPositions).forEach(([symbol, quantity]) => {
                const logicalQuantity = typeof quantity === 'number' ? quantity : parseInt(quantity) || 0;
                const actualQuantity = Math.round(logicalQuantity * capitalRatio); // 转换为实际持仓数量
                positions.push({
                  symbol: symbol,
                  name: symbol.split('.').pop() || symbol,
                  logicalPosition: logicalQuantity,  // 逻辑持仓
                  expectedPosition: actualQuantity,   // 预期持仓
                  ratio: capitalRatio,               // 资金比例
                  currentPrice: 0,                   // 当前价格（暂不获取）
                  marketValue: 0,                    // 市值（暂不计算）
                  quantity: actualQuantity,          // 兼容旧格式
                  price: 0,                          // 兼容旧格式
                  cost: 0,                           // 兼容旧格式
                  profit: 0,                         // 兼容旧格式
                  profitPercent: 0,                  // 兼容旧格式
                  key: symbol,
                });
              });
            }
            
            console.log('[前端-持仓明细] 解析后的持仓数据:', positions);
            return positions;
          })()}
          pagination={false}
          size="small"
          rowKey="key"
        />
      )}
    </Card>
  );

  const renderTrades = () => (
    <Card size="small" bodyStyle={{ padding: 12 }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: 30 }}><Spin size="small" tip="加载交易记录..." /></div>
      ) : error ? (
        <Empty description={error} />
      ) : !runtimeData?.signals?.length ? (
        <Empty description="暂无交易记录" />
      ) : (
        <Table
          columns={tradeColumns}
          dataSource={(() => {
            const trades: any[] = [];
            
            // 遍历所有信号，提取交易记录
            runtimeData.signals.forEach((signal, signalIndex) => {
              const timestamp = signal.timestamp;
              
              // 计算资金比例（与持仓明细使用相同的逻辑）
              const baseCapital = signal.base_capital || 100000;
              const actualInitialCapital = signal.actual_initial_capital || 100000;
              const capitalRatio = actualInitialCapital / baseCapital;
              
              // 处理开仓品种（买入）- 新格式：对象数组
              if (Array.isArray(signal.open_positions)) {
                signal.open_positions.forEach((position: any) => {
                  // 兼容新旧格式
                  if (typeof position === 'string') {
                    // 旧格式：字符串数组
                    const logicalQuantity = signal.logical_positions[position] || 0;
                    const actualQuantity = Math.round(logicalQuantity * capitalRatio);
                    trades.push({
                      time: timestamp,
                      symbol: position,
                      name: position.split('.').pop() || position,
                      direction: 'buy',
                      quantity: actualQuantity, // 转换为实际数量
                      price: 0, // 旧格式没有价格信息
                      amount: 0,
                      fee: 0, // 旧格式没有手续费信息
                      key: `buy-${timestamp}-${position}-${signalIndex}`,
                    });
                  } else if (position && typeof position === 'object' && position.symbol) {
                    // 新格式：对象数组 {symbol, quantity, price}
                    const logicalQuantity = position.quantity || 0;
                    const actualQuantity = Math.round(logicalQuantity * capitalRatio);
                    trades.push({
                      time: timestamp,
                      symbol: position.symbol,
                      name: position.symbol.split('.').pop() || position.symbol,
                      direction: 'buy',
                      quantity: actualQuantity, // 转换为实际数量
                      price: position.price || 0,
                      amount: actualQuantity * (position.price || 0), // 使用实际数量计算金额
                      fee: position.fee || 0, // 使用新格式手续费
                      key: `buy-${timestamp}-${position.symbol}-${signalIndex}`,
                    });
                  }
                });
              }
              
              // 处理平仓品种（卖出）- 暂时保持原有逻辑，因为平仓通常没有价格信息
              signal.close_positions.forEach((symbol: string) => {
                const logicalQuantity = Math.abs(signal.logical_positions[symbol] || 0);
                const actualQuantity = Math.round(logicalQuantity * capitalRatio);
                trades.push({
                  time: timestamp,
                  symbol: symbol,
                  name: symbol.split('.').pop() || symbol,
                  direction: 'sell',
                  quantity: actualQuantity, // 转换为实际数量
                  price: 0, // 平仓价格通常需要从其他渠道获取
                  amount: 0, // 需要计算
                  fee: 0, // 平仓没有手续费
                  key: `sell-${timestamp}-${symbol}-${signalIndex}`,
                });
              });
            });
            
            return trades;
          })()}
          pagination={{ pageSize: 10, size: 'small' }}
          size="small"
          rowKey="key"
        />
      )}
    </Card>
  );

  const renderSignals = () => {
    // 添加调试信息
    console.log('[前端-信号历史] 渲染信号历史，数据:', {
      signalsCount: runtimeData?.signals?.length || 0,
      signals: runtimeData?.signals,
      totalSignals: runtimeData?.total_signals
    });

    // 对信号按时间排序（最新的在前）
    const sortedSignals = runtimeData?.signals ? [...runtimeData.signals].sort((a, b) => {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return timeB - timeA; // 降序排列，最新的在前
    }) : [];

    console.log('[前端-信号历史] 排序后的信号:', sortedSignals);

    return (
      <Card size="small" bodyStyle={{ padding: 12 }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: 30 }}><Spin size="small" tip="加载信号数据..." /></div>
        ) : error ? (
          <Empty description={error} />
        ) : !sortedSignals?.length ? (
          <Empty description="暂无信号记录" />
        ) : (
          <Table
            columns={[
              { title: '时间', dataIndex: 'timestamp', key: 'timestamp', width: '20%' },
              {
                title: '目标品种',
                dataIndex: 'target_symbols',
                key: 'target_symbols',
                width: '25%',
                render: (symbols: string[]) => symbols.map(sym => <Tag key={sym}>{sym}</Tag>),
              },
              {
                title: '期待买入',
                dataIndex: 'open_positions',
                key: 'open_positions',
                width: '20%',
                render: (pos: any[]) => {
                  if (!pos || pos.length === 0) {
                    return <span style={{ color: '#999' }}>无</span>;
                  }
                  return pos.map((p: any) => {
                    if (typeof p === 'string') {
                      // 旧格式：字符串
                      return <Tag key={p} color="green">{p}</Tag>;
                    } else if (p && typeof p === 'object' && p.symbol) {
                      // 新格式：对象 {symbol, quantity, price}
                      const displayText = p.price ? `${p.symbol}@${p.price.toFixed(2)}` : p.symbol;
                      return <Tag key={p.symbol} color="green">{displayText}</Tag>;
                    }
                    return null;
                  });
                },
              },
              {
                title: '期待卖出',
                dataIndex: 'close_positions',
                key: 'close_positions',
                width: '20%',
                render: (pos: string[]) => pos.length > 0 ? pos.map(p => <Tag key={p} color="red">{p}</Tag>) : <span style={{ color: '#999' }}>无</span>,
              },
            ]}
            dataSource={sortedSignals}
            pagination={{ 
              pageSize: 10, 
              size: 'small',
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }}
            size="small"
            rowKey={(r, i) => `${r.timestamp}-${i}`}
          />
        )}
      </Card>
    );
  };

  const renderPerformance = () => (
    <Card size="small" bodyStyle={{ padding: 12 }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: 30 }}><Spin size="small" tip="加载绩效数据..." /></div>
      ) : error ? (
        <Empty description={error} />
      ) : !runtimeData?.signals?.length ? (
        <Empty description="暂无绩效数据" />
      ) : (
        <>
          <Row gutter={16}>
            <Col span={6}>
              <Card size="small"><Statistic title="策略类型" value={runtimeData?.strategy_type || '未知'} /></Card>
            </Col>
            <Col span={6}>
              <Card size="small"><Statistic title="初始资金" value={runtimeData?.initial_capital || 0} precision={2} suffix="元" /></Card>
            </Col>
            <Col span={6}>
              <Card size="small"><Statistic title="信号总数" value={runtimeData?.total_signals || 0} /></Card>
            </Col>
            <Col span={6}>
              <Card size="small"><Statistic title="当前余额" value={runtimeData?.latest_signal?.final_balance || 0} precision={2} suffix="元" /></Card>
            </Col>
          </Row>
          
          {/* 收益曲线图表 */}
          <div style={{ marginTop: 16 }}>
            <Card size="small" title="收益曲线">
              <div style={{ height: 200, display: 'flex', justifyContent: 'center', alignItems: 'center', background: '#f5f5f5' }}>
                <div style={{ textAlign: 'center', color: '#999' }}>
                  <div>收益曲线图表</div>
                  <div style={{ fontSize: 12, marginTop: 8 }}>
                    需要价格数据来计算收益曲线
                  </div>
                  <div style={{ fontSize: 12, marginTop: 4 }}>
                    当前数据点: {runtimeData.signals.length} 个
                  </div>
                </div>
              </div>
            </Card>
          </div>
          
          {/* 交易统计 */}
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={12}>
              <Card size="small" title="交易统计">
                <Statistic title="总交易次数" value={(() => {
                  let totalTrades = 0;
                  runtimeData.signals.forEach(signal => {
                    totalTrades += signal.open_positions.length + signal.close_positions.length;
                  });
                  return totalTrades;
                })()} />
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title="手续费统计">
                <Statistic
                  title="总手续费" 
                  value={(() => {
                    return runtimeData.signals.reduce((sum, signal) => sum + signal.commission, 0);
                  })()} 
                  precision={2} 
                  suffix="元" 
                />
              </Card>
            </Col>
          </Row>
          
          {/* 最新信号详情 */}
          {runtimeData?.latest_signal && (
            <Card size="small" title="最新信号详情" style={{ marginTop: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <div><strong>时间:</strong> {runtimeData.latest_signal.timestamp}</div>
                </Col>
                <Col span={8}>
                  <div><strong>目标品种:</strong> {runtimeData.latest_signal.target_symbols.join(', ')}</div>
              </Col>
                <Col span={8}>
                  <div><strong>手续费:</strong> {runtimeData.latest_signal.commission.toFixed(2)}</div>
              </Col>
            </Row>
            </Card>
          )}
        </>
      )}
    </Card>
  );

  return (
    <div className="expanded-row-container">
      <Tabs
        defaultActiveKey="logs"
        size="small"
        tabBarStyle={{ marginBottom: 16 }}
        items={[
          { key: 'logs', label: <><FileTextOutlined />运行日志</>, children: renderLogs() },
          { key: 'positions', label: <><StockOutlined />持仓明细</>, children: renderPositions() },
          { key: 'trades', label: <><HistoryOutlined />历史成交</>, children: renderTrades() },
          { key: 'signals', label: <><SignalFilled />信号历史</>, children: renderSignals() },
          { key: 'performance', label: <><LineChartOutlined />绩效分析</>, children: renderPerformance() },
        ]}
      />
    </div>
  );
};

export default ExpandedRow;