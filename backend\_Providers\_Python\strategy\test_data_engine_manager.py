#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据引擎管理器测试脚本
用于测试数据引擎管理功能
"""

import os
import sys
import json
import requests
import time

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 策略服务器地址
STRATEGY_SERVER_URL = "http://localhost:5002"

def test_data_engine_api():
    """测试数据引擎管理API"""
    
    print("=== 数据引擎管理器测试 ===\n")
    
    # 测试数据
    test_project_id = "test_live_project_001"
    test_contracts = ["SSE.600036", "SZSE.000001", "SSE.510300", "SZSE.159934"]
    
    try:
        # 1. 测试订阅合约
        print("1. 测试订阅合约...")
        subscribe_data = {
            "project_id": test_project_id,
            "contract_codes": test_contracts
        }
        
        response = requests.post(
            f"{STRATEGY_SERVER_URL}/data_engine/subscribe",
            json=subscribe_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✓ 订阅成功: {result.get('message')}")
                print(f"  合约数量: {result.get('contract_count')}")
            else:
                print(f"✗ 订阅失败: {result.get('error')}")
        else:
            print(f"✗ 订阅请求失败: HTTP {response.status_code}")
        
        print()
        
        # 2. 测试获取状态
        print("2. 测试获取数据引擎状态...")
        response = requests.get(f"{STRATEGY_SERVER_URL}/data_engine/status", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"✓ 状态获取成功:")
                print(f"  引擎运行状态: {data.get('engine_running')}")
                print(f"  进程ID: {data.get('process_id')}")
                print(f"  项目数量: {data.get('total_projects')}")
                print(f"  合约数量: {data.get('total_contracts')}")
                print(f"  订阅的合约: {data.get('subscribed_contracts')}")
            else:
                print(f"✗ 状态获取失败: {result.get('error')}")
        else:
            print(f"✗ 状态请求失败: HTTP {response.status_code}")
        
        print()
        
        # 3. 测试一键更新并重启
        print("3. 测试一键更新并重启数据引擎...")
        update_data = {
            "project_id": f"{test_project_id}_updated",
            "contract_codes": test_contracts + ["SSE.600519", "SZSE.300059"]
        }
        
        response = requests.post(
            f"{STRATEGY_SERVER_URL}/data_engine/update_and_restart",
            json=update_data,
            timeout=30  # 重启可能需要更长时间
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✓ 更新并重启成功: {result.get('message')}")
                print(f"  项目合约数: {result.get('project_contracts')}")
                print(f"  总合约数: {result.get('total_contracts')}")
            else:
                print(f"✗ 更新并重启失败: {result.get('error')}")
        else:
            print(f"✗ 更新并重启请求失败: HTTP {response.status_code}")
        
        print()
        
        # 4. 再次检查状态
        print("4. 重启后状态检查...")
        time.sleep(3)  # 等待重启完成
        
        response = requests.get(f"{STRATEGY_SERVER_URL}/data_engine/status", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                print(f"✓ 重启后状态:")
                print(f"  引擎运行状态: {data.get('engine_running')}")
                print(f"  项目数量: {data.get('total_projects')}")
                print(f"  合约数量: {data.get('total_contracts')}")
            else:
                print(f"✗ 状态获取失败: {result.get('error')}")
        else:
            print(f"✗ 状态请求失败: HTTP {response.status_code}")
        
        print()
        
        # 5. 测试取消订阅
        print("5. 测试取消订阅...")
        unsubscribe_data = {
            "project_id": test_project_id
        }
        
        response = requests.post(
            f"{STRATEGY_SERVER_URL}/data_engine/unsubscribe",
            json=unsubscribe_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✓ 取消订阅成功: {result.get('message')}")
            else:
                print(f"✗ 取消订阅失败: {result.get('error')}")
        else:
            print(f"✗ 取消订阅请求失败: HTTP {response.status_code}")
        
        print("\n=== 测试完成 ===")
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到策略服务器，请确保 strategy_server.py 正在运行")
    except requests.exceptions.Timeout:
        print("✗ 请求超时")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")

def test_local_manager():
    """测试本地数据引擎管理器"""
    
    print("=== 本地数据引擎管理器测试 ===\n")
    
    try:
        # 导入数据引擎管理器
        from strategy_server import DataEngineManager
        
        # 创建管理器实例
        manager = DataEngineManager()
        
        # 测试订阅注册
        print("1. 测试订阅注册...")
        test_contracts = ["SSE.600036", "SZSE.000001", "SSE.510300"]
        success = manager.register_project_subscription("test_project", test_contracts)
        print(f"订阅注册结果: {success}")
        
        # 测试获取所有合约
        print("\n2. 测试获取所有合约...")
        all_contracts = manager.get_all_subscribed_contracts()
        print(f"所有订阅的合约: {all_contracts}")
        
        # 测试生成合约条目
        print("\n3. 测试生成合约条目...")
        for contract in test_contracts:
            entry = manager.generate_contract_entry(contract)
            print(f"{contract}: {entry}")
        
        # 测试更新配置文件
        print("\n4. 测试更新配置文件...")
        success = manager.update_contracts_file()
        print(f"配置文件更新结果: {success}")
        
        # 检查生成的配置文件
        if os.path.exists(manager.contracts_file):
            print(f"\n5. 检查生成的配置文件: {manager.contracts_file}")
            with open(manager.contracts_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"配置文件内容: {json.dumps(config, ensure_ascii=False, indent=2)}")
        
        print("\n=== 本地测试完成 ===")
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
    except Exception as e:
        print(f"✗ 本地测试过程中发生错误: {e}")

if __name__ == "__main__":
    print("数据引擎管理器测试工具\n")
    print("选择测试模式:")
    print("1. API测试 (需要 strategy_server.py 运行)")
    print("2. 本地测试 (直接测试管理器类)")
    print("3. 全部测试")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        test_data_engine_api()
    elif choice == "2":
        test_local_manager()
    elif choice == "3":
        test_local_manager()
        print("\n" + "="*50 + "\n")
        test_data_engine_api()
    else:
        print("无效选择")
