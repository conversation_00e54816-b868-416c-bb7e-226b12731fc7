import requests

# === 配置 ===
API_URL = "http://localhost:3000/api/trade/execute_trade"  # 按需修改端口
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NSwidXNlcm5hbWUiOiJpbWFsdGFpIiwiaWF0IjoxNzUxODY0MjE3LCJleHAiOjE3NTE5NTA2MTd9.afnMeHcJ2ffkKtBAPHFxCyxN_1gjLhgho3pP0yOUCBs"  # 必须替换为有效token

# === 交易参数 ===
payload = {
    "client_key": "imaltai_print_client",  # 你的通道id
    "action": "order",                     # 交易动作
    "params": {
        "symbol": "SSE.ETF.518880",        # 交易品种
        "quantity": 100                    # 下单数量
    }
}

headers = {
    "Authorization": f"Bearer {JWT_TOKEN}",
    "Content-Type": "application/json"
}

# === 发送请求 ===
response = requests.post(API_URL, json=payload, headers=headers)
print("状态码:", response.status_code)
print("返回内容:", response.text)