# DataKit 数据中继服务

## 简介
DataKit 数据中继服务是一个连接 Wonder Trader DataKit 与现有数据源的桥梁。它通过 UDP 协议接收 DataKit 的订阅请求，从通达信和 OKX 等数据源获取实时数据，并将数据转换为 DataKit 期望的格式后通过 UDP 广播。

## 功能特点
- 支持 Wonder Trader ParserUDP 协议
- 支持通达信数据源和 OKX 加密货币数据源
- 实现请求队列管理，优化对单例数据服务的访问
- 支持数据缓存，减少重复请求
- 支持多种品种和周期的数据

## 配置说明
配置文件 `config.json` 包含以下设置：
- `udp_server`: UDP 服务器配置
  - `query_port`: 查询端口 (默认 3997)
  - `broadcast_port`: 广播端口 (默认 9001)
  - `host`: 监听地址 (默认 "0.0.0.0")
- `data_sources`: 数据源配置
  - `tdx`: 通达信数据源配置
    - `url`: tdxserver.py 的 URL (默认 "http://127.0.0.1:5001")
  - `okx`: OKX 数据源配置
    - `url`: okxdata.py 的 URL (默认 "http://127.0.0.1:5001")
- `queue`: 队列配置
  - `max_queue_size`: 最大队列大小 (默认 100)
  - `request_interval`: 请求间隔 (毫秒，默认 200)
  - `max_concurrent_requests`: 最大并发请求数 (默认 5)
- `cache`: 缓存配置
  - `enabled`: 是否启用缓存 (默认 true)
  - `ttl`: 缓存有效期 (秒，默认 5)

## 使用方法
1. 确保 tdxserver.py 和 okxdata.py 服务正在运行
2. 修改 `config.json` 配置文件
3. 运行 `python datakit_relay_server.py`
4. 在 DataKit 的 `tdparsers.yaml` 中配置 ParserUDP

## 依赖项
- Python 3.7+
- 依赖库: socket, json, requests, threading, queue, logging

## 日志
日志文件保存在 `logs` 目录下，包含服务运行状态、错误信息等。