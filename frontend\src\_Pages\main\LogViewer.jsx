import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { fetchLiveLogs } from '../../api/liveApi';

const LogViewer = () => {
  const { userId, groupId } = useParams();
  const [logContent, setLogContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const loadLogs = async () => {
      try {
        setLoading(true);
        const content = await fetchLiveLogs(userId, groupId);
        setLogContent(content);
        setError('');
      } catch (err) {
        setError('加载日志失败');
        console.error('日志加载错误:', err);
      } finally {
        setLoading(false);
      }
    };

    // 初始加载
    loadLogs();
    
    // 设置定时刷新（每10秒）
    const intervalId = setInterval(loadLogs, 10000);
    
    return () => clearInterval(intervalId);
  }, [userId, groupId]);

  if (loading) {
    return <div className="log-loading">加载日志中...</div>;
  }

  if (error) {
    return <div className="log-error">{error}</div>;
  }

  return (
    <div className="log-viewer">
      <div className="log-header">
        <h3>实盘引擎日志</h3>
        <small>显示最新引擎日志内容</small>
      </div>
      <pre className="log-content">{logContent}</pre>
    </div>
  );
};

export default LogViewer; 