/**
 * 股票列表格式验证工具
 * 用于验证JSON格式的股票列表是否符合系统要求
 */

// 支持的交易所列表
const VALID_EXCHANGES: { [key: string]: string } = {
  // 中国市场
  'SSE': '上海证券交易所',
  'SZSE': '深圳证券交易所',
  'BSE': '北京证券交易所',
  'CFFEX': '中国金融期货交易所',
  'SHFE': '上海期货交易所',
  'DCE': '大连商品交易所',
  'CZCE': '郑州商品交易所',
  'INE': '上海国际能源交易中心',
  
  // 美国市场
  'NYSE': '纽约证券交易所',
  'NASDAQ': '纳斯达克证券交易所',
  'AMEX': '美国证券交易所',
  'CBOE': '芝加哥期权交易所',
  'CME': '芝加哥商品交易所',
  
  // 其他国际市场
  'HKEX': '香港交易所',
  'LSE': '伦敦证券交易所',
  'TSE': '东京证券交易所',
  
  // 外汇市场
  'FOREX': '外汇市场',
  
  // 加密货币市场
  'BINANCE': '币安交易所',
  'COINBASE': '比特币基地',
  'HUOBI': '火币交易所',
  'OKX': 'OKX交易所',
  'KRAKEN': 'Kraken交易所',
  'BITFINEX': 'Bitfinex交易所',
  'BYBIT': 'Bybit交易所',
  'KUCOIN': 'KuCoin交易所',
  'GATE': 'Gate.io交易所',
  'BITGET': 'Bitget交易所',
  'CRYPTO': 'Crypto.com交易所',
  'BITMEX': 'BitMEX交易所',
  'POLONIEX': 'Poloniex交易所',
  'GEMINI': 'Gemini交易所',
  'BITSTAMP': 'Bitstamp交易所',
  'MEXC': 'MEXC交易所',
  'DERIBIT': 'Deribit交易所'
};

// 支持的品类列表
const VALID_CATEGORIES: { [key: string]: string } = {
  'STOCK': '股票',
  'ETF': '交易所交易基金',
  'FUTURE': '期货',
  'OPTION': '期权',
  'FOREX': '外汇',
  'CRYPTO': '加密货币'
};

/**
 * 验证交易所代码是否合法
 * @param exchange 交易所代码
 * @returns 是否合法
 */
function isValidExchange(exchange: string): boolean {
  return !!VALID_EXCHANGES[exchange];
}

/**
 * 验证品类是否合法
 * @param category 品类代码
 * @returns 是否合法
 */
function isValidCategory(category: string): boolean {
  return !!VALID_CATEGORIES[category];
}

/**
 * 根据交易所和品类验证代码格式
 * @param exchange 交易所
 * @param category 品类
 * @param code 代码
 * @returns 是否合法
 */
function isValidCode(exchange: string, category: string, code: string): boolean {
  if (!code || code.trim() === '') {
    return false;
  }
  
  // 中国A股市场验证
  if ((exchange === 'SSE' || exchange === 'SZSE') && category === 'STOCK') {
    // 上海A股: 6开头，深圳A股: 0或3开头，创业板: 3开头，科创板: 688开头
    if (exchange === 'SSE') {
      return /^(6\d{5}|688\d{3})$/.test(code);
    } else {
      return /^([03]\d{5})$/.test(code);
    }
  }
  
  // 中国ETF验证
  if ((exchange === 'SSE' || exchange === 'SZSE') && category === 'ETF') {
    // 上海ETF常见前缀：51xxxx (主要)、56xxxx、58xxxx等
    // 深圳ETF常见前缀：15xxxx (主要)、16xxxx等
    if (exchange === 'SSE') {
      return /^(51[0-9]{4}|52[0-9]{4}|56[0-9]{4}|58[0-9]{4})$/.test(code);
    } else {
      return /^(15[0-9]{4}|16[0-9]{4})$/.test(code);
    }
  }
  
  // 美股验证
  if ((exchange === 'NYSE' || exchange === 'NASDAQ' || exchange === 'AMEX') && category === 'STOCK') {
    // 美股代码通常为1-5个字母
    return /^[A-Z]{1,5}$/.test(code);
  }
  
  // 外汇验证
  if (exchange === 'FOREX' && category === 'FOREX') {
    // 外汇对通常为6个字母，如EURUSD, USDJPY等
    return /^[A-Z]{6}$/.test(code);
  }
  
  // 加密货币验证
  if (['BINANCE', 'COINBASE', 'HUOBI', 'OKX', 'KRAKEN', 'BITFINEX', 'BYBIT', 'KUCOIN', 'GATE', 'BITGET', 'CRYPTO', 'BITMEX', 'POLONIEX', 'GEMINI', 'BITSTAMP', 'MEXC', 'DERIBIT'].includes(exchange) && category === 'CRYPTO') {
    // 加密货币对通常为两个代码用-连接，如BTC-USDT
    return /^[A-Z]{2,5}-[A-Z]{2,5}$/.test(code);
  }
  
  // 期货验证 - 简单判断
  if (['CFFEX', 'SHFE', 'DCE', 'CZCE', 'INE', 'CME'].includes(exchange) && category === 'FUTURE') {
    // 期货代码格式不同，这里简单处理，只要不为空即可
    return code.length > 0;
  }
  
  // 期权验证 - 简单判断
  if (['SSE', 'SZSE', 'CBOE'].includes(exchange) && category === 'OPTION') {
    // 期权代码格式复杂，这里简单处理，只要不为空即可
    return code.length > 0;
  }
  
  // 对于未明确定义的组合，只要代码不为空即可
  return true;
}

/**
 * 验证单个股票项
 * @param item 股票项
 * @returns 是否合法
 */
function validateSymbolItem(item: any): boolean {
  // 必须包含symbol和name字段
  if (!item.symbol || !item.name) {
    console.error('[股票池校验] 缺少必要字段symbol或name:', item);
    return false;
  }
  
  // symbol必须是字符串
  if (typeof item.symbol !== 'string') {
    console.error('[股票池校验] symbol必须是字符串:', item.symbol);
    return false;
  }
  
  // 解析symbol格式: 交易所.品类.代码
  const parts = item.symbol.split('.');
  if (parts.length < 3) {
    console.error('[股票池校验] symbol格式错误，应为"交易所.品类.代码":', item.symbol);
    return false;
  }
  
  const exchange = parts[0];
  const category = parts[1];
  const code = parts.slice(2).join('.'); // 代码部分可能包含小数点，如加密货币
  
  // 验证交易所
  if (!isValidExchange(exchange)) {
    console.error('[股票池校验] 不支持的交易所:', exchange);
    return false;
  }
  
  // 验证品类
  if (!isValidCategory(category)) {
    console.error('[股票池校验] 不支持的品类:', category);
    return false;
  }
  
  // 验证代码
  if (!isValidCode(exchange, category, code)) {
    console.error('[股票池校验] 代码格式错误:', code, '对于交易所', exchange, '和品类', category);
    return false;
  }
  
  return true;
}

// --- New Result Type ---
interface ValidationResult {
  isValid: boolean;
  type: 'json' | 'a_share_codes' | 'invalid';
  data: any[] | null; // Holds parsed JSON array or validated code array
  error?: string;     // Error message if invalid
}

/**
 * Validates if a JSON string represents a valid symbol list.
 * @param jsonStr JSON string
 * @returns boolean
 */
export function validateSymbolList(jsonStr: string): boolean {
  try {
    // 尝试解析JSON
    const data = JSON.parse(jsonStr);
    
    // 必须是数组
    if (!Array.isArray(data)) {
      console.error('[股票池校验] 输入必须是数组:', data);
      return false;
    }
    
    // 空数组视为合法
    if (data.length === 0) {
      return true;
    }
    
    // 验证每个项目
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (!validateSymbolItem(item)) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('[股票池校验] JSON解析错误:', error);
    return false;
  }
}

/**
 * Validates if an object array represents a valid symbol list.
 * @param data Array of symbol objects
 * @returns boolean
 */
export function validateSymbolListObject(data: any[]): boolean {
  try {
    // 必须是数组
    if (!Array.isArray(data)) {
      console.error('[股票池校验] 输入必须是数组:', data);
      return false;
    }
    
    // 空数组视为合法
    if (data.length === 0) {
      return true;
    }
    
    // 验证每个项目
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (!validateSymbolItem(item)) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('[股票池校验] 验证过程出错:', error);
    return false;
  }
}

// --- NEW Function: Parses and validates input, accepting JSON or simple code string ---
/**
 * Parses and validates input string, which can be either:
 * 1. A JSON array of { symbol: string, name: string } objects.
 * 2. A comma-separated string of 6-digit A-share/ETF codes.
 * @param inputString The string to parse and validate.
 * @returns ValidationResult object.
 */
export function parseAndValidateInput(inputString: string): ValidationResult {
  if (!inputString || !inputString.trim()) {
    return { isValid: false, type: 'invalid', data: null, error: '输入内容不能为空' };
  }

  const trimmedInput = inputString.trim();

  // 1. Try parsing as JSON array
  if (trimmedInput.startsWith('[') && trimmedInput.endsWith(']')) {
    try {
      const jsonData = JSON.parse(trimmedInput);
      if (Array.isArray(jsonData)) {
        if (validateSymbolListObject(jsonData)) {
           console.log('[输入校验] 输入被识别为有效的标准 JSON 列表');
          return { isValid: true, type: 'json', data: jsonData };
        } else {
           console.warn('[输入校验] JSON 数组格式正确，但内容校验失败');
          return { isValid: false, type: 'invalid', data: null, error: 'JSON 列表内容校验失败，请检查 symbol 和 name 字段及格式' };
        }
      } else {
         console.warn('[输入校验] 输入看起来像 JSON，但不是数组');
         // Fall through to check as code string, maybe? Or return error? Let's return error.
         return { isValid: false, type: 'invalid', data: null, error: '输入格式错误，应为 JSON 数组或 A 股代码串' };
      }
    } catch (e) {
       console.warn('[输入校验] JSON 解析失败，尝试作为代码串处理...');
      // Fall through to treat as code string
    }
  }

  // 2. Try parsing as comma-separated A-share/ETF codes
  const potentialCodes = trimmedInput.split(',').map(code => code.trim()).filter(code => code.length > 0);

  if (potentialCodes.length === 0) {
      return { isValid: false, type: 'invalid', data: null, error: '输入内容无效' };
  }

  const validAShareCodes: string[] = [];
  const invalidCodes: string[] = [];

  for (const code of potentialCodes) {
    // Check 1: Must be 6 digits
    if (!/^[0-9]{6}$/.test(code)) {
      invalidCodes.push(`${code} (非6位数字)`);
      continue;
    }

    // Check 2: Must match A-share/ETF prefixes
    const firstChar = code.charAt(0);
    const firstTwoChars = code.substring(0, 2);

    if (
      firstChar === '6' || // SSE Stock (incl. 688)
      firstChar === '0' || // SZSE Stock
      firstChar === '3' || // SZSE Stock (ChiNext)
      firstChar === '8' || // BSE Stock
      firstChar === '4' || // BSE Stock
      firstChar === '5' || // SSE ETF
      firstTwoChars === '15' // SZSE ETF
    ) {
      validAShareCodes.push(code);
    } else {
      invalidCodes.push(`${code} (前缀不匹配)`);
    }
  }

  if (invalidCodes.length > 0) {
     const errorMsg = `包含无效或不支持的代码: ${invalidCodes.slice(0, 3).join(', ')}${invalidCodes.length > 3 ? '...' : ''}`;
     console.warn(`[输入校验] 作为代码串处理时发现错误: ${errorMsg}`);
    return { isValid: false, type: 'invalid', data: null, error: errorMsg };
  }

  if (validAShareCodes.length > 0) {
     console.log('[输入校验] 输入被识别为有效的 A 股/ETF 代码串');
    return { isValid: true, type: 'a_share_codes', data: validAShareCodes };
  }

  // Should not be reachable if logic is correct, but as a fallback
  return { isValid: false, type: 'invalid', data: null, error: '无法识别的输入格式' };
}

export default validateSymbolList; 