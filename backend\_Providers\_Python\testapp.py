#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import sys

def main():
    """调用app.py中的get_stock_list接口并打印前10个股票"""
    
    # API端点 - 假设服务在本地运行
    url = "http://localhost:5000/stock/list"
    
    try:
        # 获取股票列表
        print("正在获取股票列表...")
        response = requests.get(url)
        
        # 检查响应状态
        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            print(response.text)
            return
        
        # 解析JSON响应
        data = response.json()
        
        # 检查是否成功
        if not data.get('success'):
            print(f"请求失败: {data.get('error', '未知错误')}")
            return
        
        # 获取股票列表
        stocks = data.get('data', [])
        
        # 打印总数
        print(f"成功获取到 {len(stocks)} 只股票")
        
        # 打印前10个股票
        print("\n前10只股票:")
        print("-" * 50)
        print(f"{'代码':<10}{'名称':<20}{'市场':<10}{'交易所':<10}")
        print("-" * 50)
        
        for i, stock in enumerate(stocks[:10]):
            print(f"{stock['code']:<10}{stock['name']:<20}{stock['market']:<10}{stock['exchange']:<10}")
            
    except requests.exceptions.ConnectionError:
        print("连接失败，请确保Flask服务已经启动")
    except json.JSONDecodeError:
        print("JSON解析错误，返回的不是有效的JSON格式")
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    main()