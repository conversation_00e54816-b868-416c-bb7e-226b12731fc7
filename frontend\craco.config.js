const path = require('path');
const CracoLessPlugin = require('craco-less');

module.exports = {
  plugins: [
    {
      plugin: CracoLessPlugin,
      options: {
        lessLoaderOptions: {
          lessOptions: {
            javascriptEnabled: true,
          },
        },
        modifyLessRule: function(lessRule, _context) {
          lessRule.test = /\.(module)\.(less)$/;
          lessRule.exclude = /node_modules/;
          return lessRule;
        },
      },
    },
  ],
  webpack: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@shared_types': path.resolve(__dirname, '../shared_types'),
    },
  },
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        ws: false
      },
      '/chat': {
        target: 'ws://localhost:3000',                        
        changeOrigin: true,
        ws: true
      },
      '/uploads': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        ws: false
      },
      '/socket.io': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        ws: true
      }
    },
    port: 3005,
    allowedHosts: 'all',
    onBeforeSetupMiddleware: (devServer) => {
      if (!devServer) {
        throw new Error('webpack-dev-server 未定义');
      }
      devServer.app.use((req, res, next) => {
        console.log('请求:', req.method, req.url);
        next();
      });
    },
  },
};