// backend/models/UserTradingConfig.js
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const UserTradingConfig = sequelize.define('UserTradingConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '配置ID'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '用户ID'
    },
    channelType: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '通道类型: openctp, miniQMT 等'
    },
    configName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '用户自定义配置名称'
    },
    broker: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '经纪商代码，可为空'
    },
    username: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '交易账户'
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '密码（加密存储）'
    },
    appid: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '应用ID，可为空'
    },
    authcode: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '认证码，可为空'
    },
    frontAddress: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '前置地址'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否激活'
    }
  }, {
    tableName: 'user_trading_configs',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    indexes: [
      {
        unique: true,
        fields: ['userId', 'channelType'],
        name: 'unique_user_channel'
      }
    ]
  });

  // 定义关联关系
  UserTradingConfig.associate = function(models) {
    UserTradingConfig.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return UserTradingConfig;
};
