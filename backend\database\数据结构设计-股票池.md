# 数据库结构设计 - 股票池模块

本文档定义了用于管理用户自选股（股票池）及系统预定义板块/指数的数据库表结构。

## 核心表设计

采用主从表结构，并辅以一个元数据表来管理系统池。

### 1. `pools` 表

存储所有股票池（用户自选池和系统预定义池）的基本信息。

**结构:**

```sql
CREATE TABLE pools (
    pool_id BIGSERIAL PRIMARY KEY,         -- 池子唯一ID (主键，自增)
    user_id BIGINT NULL,                   -- 关联的用户ID。如果为 NULL，表示是系统预定义的池子。
    name VARCHAR(128) NOT NULL,            -- 池子名称。对于用户池，是用户自定义名称；对于系统池，可以是内部标识符 (如 "sys_HS300")。
    is_public BOOLEAN NOT NULL DEFAULT FALSE, -- 仅对用户池有效，表示是否公开分享。系统池此字段通常无意义或固定为 TRUE。
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 创建时间

    -- 外键约束：关联到用户表 (如果 users 表的主键是 user_id)
    -- ON DELETE CASCADE: 删除用户时，级联删除其所有股票池
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);
```

**索引与约束:**

```sql
-- 加速查询某个用户的所有池子列表 (覆盖索引优化)
CREATE INDEX idx_pools_user ON pools(user_id) INCLUDE (name, created_at);

-- 唯一性约束：
-- 1. 保证同一个用户的池子名称唯一 (部分唯一索引)
CREATE UNIQUE INDEX idx_pools_user_name_unique ON pools(user_id, name) WHERE user_id IS NOT NULL;
-- 2. 保证系统池（user_id 为 NULL）的名称全局唯一 (部分唯一索引)
CREATE UNIQUE INDEX idx_pools_system_name_unique ON pools(name) WHERE user_id IS NULL;
```

**说明:**

*   通过 `user_id IS NULL` 来区分系统池和用户池。
*   系统池的展示名称由 `system_pool_metadata` 表管理。

### 2. `pool_symbols` 表

存储每个股票池包含的具体品种（股票、ETF、指数等）信息。

**结构:**

```sql
CREATE TABLE pool_symbols (
    pool_id BIGINT NOT NULL,               -- 外键，关联到 pools 表的 pool_id
    symbol VARCHAR(32) NOT NULL,           -- 品种代码 (推荐采用 '代码.市场后缀' 格式，如 "AAPL.US", "600036.SH")
    added_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 添加到池子的时间
    notes TEXT NULL,                       -- 用户对该品种的备注信息 (主要用于用户池)

    -- 复合主键：确保同一个池子内品种唯一
    PRIMARY KEY (pool_id, symbol),

    -- 外键约束：关联到 pools 表
    -- ON DELETE CASCADE: 删除池子时，级联删除其包含的所有品种条目
    CONSTRAINT fk_pool FOREIGN KEY (pool_id) REFERENCES pools(pool_id) ON DELETE CASCADE
);
```

**索引:**

```sql
-- 加速查询某个池子下的所有品种，并按添加时间倒序排列 (常用默认排序)
CREATE INDEX idx_pool_symbols_time ON pool_symbols(pool_id, added_at DESC);

-- 注意：按 symbol 反查池子的索引 (idx_pool_symbols_symbol) 暂时不创建，以简化结构和减少写入开销。
-- 如果未来反查功能成为核心需求，再考虑添加：
-- CREATE INDEX idx_pool_symbols_symbol ON pool_symbols(symbol);
```

**说明:**

*   品种名称（如 "苹果公司"）不在此表存储，推荐由前端或应用层根据 `symbol` 从行情数据源实时获取，以避免数据冗余和同步问题。
*   暂不包含自定义排序字段 (`sort_order`)，默认使用 `added_at` 排序。

### 3. `system_pool_metadata` 表

存储系统预定义池子（`pools.user_id IS NULL`）的元数据，用于分类、展示和发现。

**结构:**

```sql
CREATE TABLE system_pool_metadata (
    pool_id BIGINT PRIMARY KEY,          -- 主键，同时也是外键，关联到 pools 表中对应的系统池记录
    market_type VARCHAR(20) NOT NULL,   -- 所属市场类型 (参考前端 MarketType 枚举，如 'STOCK_CN', 'STOCK_US', 'INDEX', 'CRYPTO', 'ETF')
    region VARCHAR(10) NULL,            -- 地区代码 (可选，用于细分市场，如 'CN', 'US', 'HK', 'GLOBAL')
    category VARCHAR(50) NULL,          -- 板块/指数类别 (可选，如 '科技', '金融', '宽基指数', '行业ETF')
    display_name VARCHAR(128) NOT NULL, -- 用于前端显示的友好名称 (如 "沪深300指数成分", "美股科技精选")
    description TEXT NULL,              -- 对该池子的描述 (可选)
    display_order INT DEFAULT 0,        -- 用于前端同类展示排序的优先级，数字越小越靠前
    is_featured BOOLEAN DEFAULT FALSE,  -- 是否为精选/推荐板块 (可选)
    updated_at TIMESTAMPTZ DEFAULT NOW(),-- 元数据最后更新时间

    -- 外键约束：关联到 pools 表
    -- ON DELETE CASCADE: 如果系统池在 pools 表被删除，对应元数据也删除
    -- 注意：此约束不能强制 pools.user_id IS NULL，需应用层保证。
    CONSTRAINT fk_system_pool FOREIGN KEY (pool_id) REFERENCES pools(pool_id) ON DELETE CASCADE
);
```

**索引:**

```sql
-- 关键索引：加速按市场、地区、分类查找系统池，并支持排序
CREATE INDEX idx_system_pool_meta_discovery ON system_pool_metadata(market_type, region, category, display_order);

-- 可选索引：如果按 market_type 单独查找非常频繁
-- CREATE INDEX idx_system_pool_meta_market ON system_pool_metadata(market_type);
```

**说明:**

*   此表是对系统池 (`pools` 表中 `user_id` 为 `NULL` 的记录) 的补充说明和管理。
*   应用层在插入 `system_pool_metadata` 记录时，应校验对应的 `pools.pool_id` 确实存在且其 `user_id` 为 `NULL`。

## 未来扩展考虑

*   **分区:** 当 `pool_symbols` 表数据量达到极大规模（如数十亿行）时，需要对该表进行分区（例如按 `pool_id` HASH 分区）以保证性能。建议在建表初期就考虑使用分区表，即使初始分区数很少。
*   **自定义排序:** 如果需要用户自定义池内品种顺序，可在 `pool_symbols` 表添加 `sort_order` 字段及相应索引 `(pool_id, sort_order)`。
*   **品种详细信息:** 如果需要持久化存储品种名称、交易所等详细信息，可以引入 `symbols_master` 表，并通过 `symbol` 进行关联。
*   **更复杂的查询:** 如果需要频繁按 `symbol` 反查池子，可以添加 `idx_pool_symbols_symbol` 索引。 