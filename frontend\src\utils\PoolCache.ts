import { message } from 'antd';
import poolDispatcher from '@/_Dispatchers/poolDispatcher';
import { EventBus } from '@/events/eventBus';
import { MarketEvents } from '@/events/events';
import axios from 'axios';

// 股票池项目接口
export interface PoolItem {
  symbol: string;  // 全编码，例如 SSE.STOCK.600010
  name: string;    // 品种名称
  data?: any;      // 可选的自定义数据
  [key: string]: any; // 允许其他属性
}

// 股票池缓存项接口
interface PoolCacheItem {
  poolName: string;
  isSystemList: boolean;
  items: PoolItem[];
  lastSyncTime: number;
  isDirty: boolean;  // 标记是否有未同步的更改
  poolid?: number;   // 后端返回的 pool_id
}

/**
 * 股票池数据的本地缓存管理类
 */
export class PoolCache {
  private static DB_NAME = 'QuantQuartPoolItemsDB';
  private static STORE_NAME = 'pool_items';
  private static VERSION = 1;
  private static instance: PoolCache;
  private db: IDBDatabase | null = null;
  private pendingSyncs: Set<string> = new Set(); // 跟踪正在同步的池
  private syncInterval: number | null = null;
  private syncIntervalMs = 30000; // 默认30秒同步一次

  // 单例模式
  private constructor() {}

  public static getInstance(): PoolCache {
    if (!PoolCache.instance) {
      PoolCache.instance = new PoolCache();
    }
    return PoolCache.instance;
  }

  /**
   * 初始化缓存
   */
  public async initialize(): Promise<void> {
    if (this.db) return;

    try {
      this.db = await this.openDB();
      console.log('[PoolCache] 初始化成功');

      // 启动定期同步
      this.startSyncInterval();

      // 监听页面关闭事件，尝试同步所有脏数据
      window.addEventListener('beforeunload', this.syncAllDirtyPools.bind(this));
    } catch (error) {
      console.error('[PoolCache] 初始化失败:', error);
      message.error('股票池缓存初始化失败');
    }
  }

  /**
   * 打开IndexedDB数据库
   */
  private openDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(PoolCache.DB_NAME, PoolCache.VERSION);

      request.onerror = () => {
        console.error('[PoolCache] 打开数据库失败:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        console.log('[PoolCache] 数据库打开成功');
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        console.log('[PoolCache] 数据库需要升级');
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(PoolCache.STORE_NAME)) {
          // 创建对象存储，使用复合键 "poolName_isSystemList" 作为键
          const store = db.createObjectStore(PoolCache.STORE_NAME, { keyPath: 'id' });
          store.createIndex('isDirty', 'isDirty', { unique: false });
          console.log('[PoolCache] 股票池存储已创建');
        }
      };
    });
  }

  /**
   * 生成缓存项的唯一ID
   */
  private generateCacheId(poolName: string, isSystemList: boolean): string {
    return `${poolName}_${isSystemList ? 'system' : 'user'}`;
  }

  /**
   * 获取股票池数据，优先从缓存获取，如果缓存不存在则返回空数组
   * 不再自动从后端获取，避免循环依赖
   */
  public async getPoolItems(poolName: string, isSystemList: boolean): Promise<PoolItem[]> {
    if (!this.db) {
      await this.initialize();
    }

    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    const cacheId = this.generateCacheId(poolName, isSystemList);
    console.log(`[PoolCache] 尝试获取池 "${poolName}" 数据，缓存ID: ${cacheId}`);

    try {
      // 尝试从缓存获取
      const cachedItem = await this.getFromCache(cacheId);

      if (cachedItem) {
        console.log(`[PoolCache] 从缓存获取池 "${poolName}" 数据，包含 ${cachedItem.items.length} 个项目`);
        if (cachedItem.items.length > 0) {
          console.log(`[PoolCache] 池 "${poolName}" 第一项数据:`, cachedItem.items[0]);
        }
        return cachedItem.items;
      }

      // 缓存不存在，返回空数组
      console.log(`[PoolCache] 缓存未命中，池 "${poolName}" 不存在`);
      return [];
    } catch (error) {
      console.error(`[PoolCache] 获取池 "${poolName}" 数据失败:`, error);
      // 出错时返回空数组而不是抛出异常，避免中断流程
      return [];
    }
  }

  /**
   * 从后端获取股票池数据并更新缓存
   * 这是一个新方法，用于显式地从后端获取数据
   */
  public async fetchAndCachePoolItems(poolName: string, isSystemList: boolean, backendFetchFn: () => Promise<any>): Promise<PoolItem[]> {
    try {
      console.log(`[PoolCache] 从后端获取池 "${poolName}" 数据`);
      const backendData = await backendFetchFn();

      if (backendData && backendData.data) {
        // 从后端响应中提取 pool_id
        let poolId: number | undefined = undefined;

        // 尝试从不同的字段中获取 pool_id
        if (backendData.poolId !== undefined) {
          poolId = backendData.poolId;
          console.log(`[PoolCache] 从 poolId 字段获取到池子ID: ${poolId}`);
        } else if (backendData.pool_id !== undefined) {
          poolId = backendData.pool_id;
          console.log(`[PoolCache] 从 pool_id 字段获取到池子ID: ${poolId}`);
        } else if (backendData.id !== undefined) {
          poolId = backendData.id;
          console.log(`[PoolCache] 从 id 字段获取到池子ID: ${poolId}`);
        }

        // 存入缓存
        const items = Array.isArray(backendData.data) ? backendData.data : [];

        // 检查items中是否包含data字段
        if (items.length > 0) {
          console.log(`[PoolCache] 从后端获取的第一项数据:`, items[0]);
          console.log(`[PoolCache] 从后端获取的数据中是否包含data字段:`, items.some((item: any) => item.data !== undefined));
          console.log(`[PoolCache] 从后端获取的数据中包含data字段的项目数量:`, items.filter((item: any) => item.data !== undefined).length);
        }

        await this.saveToCache(poolName, isSystemList, items, false, poolId);
        return items;
      }

      return [];
    } catch (error) {
      console.error(`[PoolCache] 从后端获取池 "${poolName}" 数据失败:`, error);
      return [];
    }
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache(cacheId: string): Promise<PoolCacheItem | null> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const transaction = this.db.transaction(PoolCache.STORE_NAME, 'readonly');
      const store = transaction.objectStore(PoolCache.STORE_NAME);
      const request = store.get(cacheId);

      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          console.log(`[PoolCache] 从缓存获取到ID为 "${cacheId}" 的数据`);

          // 确保 items 是数组
          if (!Array.isArray(result.items)) {
            console.warn(`[PoolCache] 缓存项 "${cacheId}" 的 items 不是数组，尝试修复`);
            try {
              // 如果是字符串，尝试解析为JSON
              if (typeof result.items === 'string') {
                result.items = JSON.parse(result.items);
                console.log(`[PoolCache] 成功将字符串解析为数组，长度: ${result.items.length}`);
              } else {
                // 如果不是字符串也不是数组，设置为空数组
                console.warn(`[PoolCache] 无法修复 items，设置为空数组`);
                result.items = [];
              }
            } catch (e) {
              console.error(`[PoolCache] 解析 items 字符串失败:`, e);
              result.items = [];
            }
          }
        }
        resolve(result || null);
      };

      request.onerror = () => {
        console.error('[PoolCache] 从缓存获取数据失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 保存数据到缓存
   * @param poolName 池子名称
   * @param isSystemList 是否系统列表
   * @param items 池子项目
   * @param isDirty 是否脏数据
   * @param poolid 后端返回的池子ID
   */
  private saveToCache(poolName: string, isSystemList: boolean, items: PoolItem[], isDirty: boolean, poolid?: number): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'));
        return;
      }

      const cacheId = this.generateCacheId(poolName, isSystemList);
      const transaction = this.db.transaction(PoolCache.STORE_NAME, 'readwrite');
      const store = transaction.objectStore(PoolCache.STORE_NAME);

      // 先尝试获取现有的缓存项，保留其 poolid
      const getRequest = store.get(cacheId);

      getRequest.onsuccess = () => {
        const existingItem = getRequest.result;
        const existingPoolId = existingItem?.poolid;

        // 使用传入的 poolid，如果没有则使用现有的 poolid
        const finalPoolId = poolid !== undefined ? poolid : existingPoolId;

        const cacheItem: PoolCacheItem & { id: string } = {
          id: cacheId,
          poolName,
          isSystemList,
          items,
          lastSyncTime: Date.now(),
          isDirty,
          poolid: finalPoolId
        };

        const putRequest = store.put(cacheItem);

        putRequest.onsuccess = () => {
          console.log(`[PoolCache] 成功保存池 "${poolName}" 到缓存${finalPoolId ? `，池子ID: ${finalPoolId}` : ''}`);
          resolve();
        };

        putRequest.onerror = () => {
          console.error(`[PoolCache] 保存池 "${poolName}" 到缓存失败:`, putRequest.error);
          reject(putRequest.error);
        };
      };

      getRequest.onerror = () => {
        console.error(`[PoolCache] 获取现有缓存项失败:`, getRequest.error);

        // 即使获取失败，也尝试保存新的缓存项
        const cacheItem: PoolCacheItem & { id: string } = {
          id: cacheId,
          poolName,
          isSystemList,
          items,
          lastSyncTime: Date.now(),
          isDirty,
          poolid: poolid
        };

        const putRequest = store.put(cacheItem);

        putRequest.onsuccess = () => {
          console.log(`[PoolCache] 成功保存池 "${poolName}" 到缓存${poolid ? `，池子ID: ${poolid}` : ''}`);
          resolve();
        };

        putRequest.onerror = () => {
          console.error(`[PoolCache] 保存池 "${poolName}" 到缓存失败:`, putRequest.error);
          reject(putRequest.error);
        };
      };
    });
  }

  /**
   * 更新股票池数据（本地更新）
   * @param poolName 池子名称
   * @param isSystemList 是否系统列表
   * @param items 池子项目
   * @param poolid 后端返回的池子ID
   */
  public async updatePoolItems(poolName: string, isSystemList: boolean, items: PoolItem[], poolid?: number): Promise<void> {
    if (!this.db) {
      await this.initialize();
    }

    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    try {
      // 保存到缓存，标记为脏数据
      await this.saveToCache(poolName, isSystemList, items, true, poolid);

      console.log(`[PoolCache] 本地更新池 "${poolName}" 完成${poolid ? `，池子ID: ${poolid}` : ''}，等待同步到后端`);
    } catch (error) {
      console.error(`[PoolCache] 更新池 "${poolName}" 失败:`, error);
      throw error;
    }
  }

  /**
   * 添加单个项目到股票池
   */
  public async addItemToPool(poolName: string, isSystemList: boolean, item: PoolItem): Promise<void> {
    try {
      // 获取当前池数据
      const currentItems = await this.getPoolItems(poolName, isSystemList);

      // 检查是否已存在相同symbol的项目
      const existingIndex = currentItems.findIndex(i => i.symbol === item.symbol);

      if (existingIndex >= 0) {
        // 如果已存在，替换它
        currentItems[existingIndex] = item;
      } else {
        // 如果不存在，添加到列表
        currentItems.push(item);
      }

      // 更新缓存
      await this.updatePoolItems(poolName, isSystemList, currentItems);
    } catch (error) {
      console.error(`[PoolCache] 添加项目到池 "${poolName}" 失败:`, error);
      throw error;
    }
  }

  /**
   * 从股票池中移除项目
   */
  public async removeItemFromPool(poolName: string, isSystemList: boolean, symbol: string): Promise<void> {
    try {
      // 获取当前池数据
      const currentItems = await this.getPoolItems(poolName, isSystemList);

      // 过滤掉要删除的项目
      const updatedItems = currentItems.filter(item => item.symbol !== symbol);

      // 如果数量没变，说明没找到要删除的项目
      if (updatedItems.length === currentItems.length) {
        console.warn(`[PoolCache] 池 "${poolName}" 中未找到symbol为 "${symbol}" 的项目`);
        return;
      }

      // 更新缓存
      await this.updatePoolItems(poolName, isSystemList, updatedItems);
    } catch (error) {
      console.error(`[PoolCache] 从池 "${poolName}" 移除项目失败:`, error);
      throw error;
    }
  }

  /**
   * 同步单个池的数据到后端
   */
  public async syncPoolToBackend(poolName: string, isSystemList: boolean): Promise<boolean> {
    const cacheId = this.generateCacheId(poolName, isSystemList);
    console.log(`[PoolCache] 请求同步池 "${poolName}" (ID: ${cacheId}) 到后端`);

    if (this.pendingSyncs.has(cacheId)) {
      console.log(`[PoolCache] 池 "${cacheId}" 正在同步中，跳过`);
      return false;
    }

    try {
      this.pendingSyncs.add(cacheId);

      // 获取缓存数据
      const cachedItem = await this.getFromCache(cacheId);

      if (!cachedItem) {
        console.log(`[PoolCache] 池 "${cacheId}" 不存在，无法同步`);
        return false;
      }

      console.log(`[PoolCache] 获取到池 "${cacheId}" 的缓存数据，包含 ${cachedItem.items.length} 个项目，isDirty: ${cachedItem.isDirty}`);

      // 即使不是脏数据，也强制同步浏览历史
      if (!cachedItem.isDirty && poolName !== '浏览历史') {
        console.log(`[PoolCache] 池 "${cacheId}" 不是脏数据，无需同步`);
        return true;
      }

      console.log(`[PoolCache] 开始同步池 "${poolName}" (ID: ${cacheId}) 到后端`);

      try {
        // 确保 items 是数组
        if (!Array.isArray(cachedItem.items)) {
          console.warn(`[PoolCache] 池 "${cacheId}" 的 items 不是数组，尝试修复`);
          if (typeof cachedItem.items === 'string') {
            try {
              cachedItem.items = JSON.parse(cachedItem.items);
            } catch (e) {
              console.error(`[PoolCache] 解析池 "${cacheId}" 的 items 字符串失败:`, e);
              cachedItem.items = [];
            }
          } else {
            cachedItem.items = [];
          }
        }

        console.log(`[PoolCache] 准备同步池 "${poolName}" 到后端，数据:`, {
          name: poolName,
          items_count: cachedItem.items.length,
          is_system: isSystemList,
          pool_id: cachedItem.poolid
        });

        // 调用后端API更新数据
        const response = await poolDispatcher.updateSymbolList({
          name: poolName,
          symbols_json: cachedItem.items,
          is_system: isSystemList,
          pool_id: cachedItem.poolid
        });

        // 从响应中提取 poolid
        let poolId: number | undefined = undefined;
        if (response && typeof response === 'object' && response !== null) {
          poolId = (response as any).id || (response as any).poolId || cachedItem.poolid;
        } else {
          poolId = cachedItem.poolid;
        }
        console.log(`[PoolCache] 从响应中获取池子ID: ${poolId}`);

        // 更新缓存，标记为已同步
        await this.saveToCache(poolName, isSystemList, cachedItem.items, false, poolId);
        console.log(`[PoolCache] 池 "${poolName}" (ID: ${cacheId}) 同步成功，池子ID: ${poolId}`);
        return true;

      } catch (syncError) {
        console.error(`[PoolCache] 同步池 "${poolName}" (ID: ${cacheId}) 时发生错误:`, syncError);
        if (axios.isAxiosError(syncError)) {
          console.error(`[PoolCache] Axios Error Details: Status=${syncError.response?.status}, Data=${JSON.stringify(syncError.response?.data)}`);
        }
        return false;
      }

    } catch (outerError) {
      console.error(`[PoolCache] 处理池 "${poolName}" (ID: ${cacheId}) 同步时发生外部错误:`, outerError);
      return false;
    } finally {
      this.pendingSyncs.delete(cacheId);
    }
  }

  /**
   * 同步所有脏数据到后端
   */
  public async syncAllDirtyPools(): Promise<void> {
    if (!this.db) {
      console.warn('[PoolCache] syncAllDirtyPools called but DB is not initialized.');
      return;
    }

    let dirtyPools: PoolCacheItem[] = [];
    try {
      dirtyPools = await this.getAllDirtyPools();

      if (dirtyPools.length === 0) {
        console.log('[PoolCache] 没有需要同步的脏数据');
        return;
      }

      console.log(`[PoolCache] 开始同步 ${dirtyPools.length} 个脏数据池`);

      const syncPromises = dirtyPools.map(pool =>
        this.syncPoolToBackend(pool.poolName, pool.isSystemList).catch(err => {
          console.error(`[PoolCache] 同步池 ${pool.poolName} (isSystem: ${pool.isSystemList}) 失败 (in Promise.all):`, err);
          return false;
        })
      );

      const results = await Promise.all(syncPromises);
      const successfulSyncs = results.filter(res => res === true).length;
      const failedSyncs = results.length - successfulSyncs;

      console.log(`[PoolCache] 所有脏数据同步尝试完成. 成功: ${successfulSyncs}, 失败: ${failedSyncs}`);

    } catch (error) {
      console.error('[PoolCache] 获取或处理脏数据列表时失败:', error);
    }
  }

  /**
   * 获取所有脏数据池
   */
  private getAllDirtyPools(): Promise<PoolCacheItem[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        resolve([]);
        return;
      }

      const transaction = this.db.transaction(PoolCache.STORE_NAME, 'readonly');
      const store = transaction.objectStore(PoolCache.STORE_NAME);
      const dirtyPools: PoolCacheItem[] = [];

      // 使用游标遍历所有记录，筛选出isDirty为true的记录
      const request = store.openCursor();

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result as IDBCursorWithValue;
        if (cursor) {
          const item = cursor.value;
          if (item.isDirty === true) {
            dirtyPools.push(item);
          }
          cursor.continue();
        } else {
          // 遍历完成，返回结果
          console.log(`[PoolCache] 找到 ${dirtyPools.length} 个脏数据池`);
          resolve(dirtyPools);
        }
      };

      request.onerror = () => {
        console.error('[PoolCache] 获取脏数据池失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 启动定期同步
   */
  private startSyncInterval(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = window.setInterval(() => {
      this.syncAllDirtyPools().catch(error => {
        console.error('[PoolCache] 定期同步失败:', error);
      });
    }, this.syncIntervalMs);

    console.log(`[PoolCache] 启动定期同步，间隔 ${this.syncIntervalMs / 1000} 秒`);
  }

  /**
   * 停止定期同步
   */
  public stopSyncInterval(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('[PoolCache] 停止定期同步');
    }
  }

  /**
   * 设置同步间隔
   */
  public setSyncInterval(intervalMs: number): void {
    this.syncIntervalMs = intervalMs;
    if (this.syncInterval) {
      this.stopSyncInterval();
      this.startSyncInterval();
    }
  }

  /**
   * 强制立即同步所有脏数据
   */
  public async forceSyncNow(): Promise<void> {
    console.log('[PoolCache] 强制立即同步所有脏数据');
    await this.syncAllDirtyPools();
  }

  /**
   * 清除特定池的缓存
   */
  public async clearPoolCache(poolName: string, isSystemList: boolean): Promise<void> {
    if (!this.db) return;

    const cacheId = this.generateCacheId(poolName, isSystemList);
    const db = this.db;

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(PoolCache.STORE_NAME, 'readwrite');
      const store = transaction.objectStore(PoolCache.STORE_NAME);
      const request = store.delete(cacheId);

      request.onsuccess = () => {
        console.log(`[PoolCache] 池 "${poolName}" 的缓存已清除`);
        resolve();
      };

      request.onerror = () => {
        console.error(`[PoolCache] 清除池 "${poolName}" 的缓存失败:`, request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 清除所有缓存
   */
  public async clearCache(): Promise<void> {
    if (!this.db) return;

    const db = this.db;

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(PoolCache.STORE_NAME, 'readwrite');
      const store = transaction.objectStore(PoolCache.STORE_NAME);
      const request = store.clear();

      request.onsuccess = () => {
        console.log('[PoolCache] 所有缓存已清除');
        resolve();
      };

      request.onerror = () => {
        console.error('[PoolCache] 清除所有缓存失败:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * 关闭数据库连接
   */
  public close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }

    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    window.removeEventListener('beforeunload', this.syncAllDirtyPools.bind(this));
  }
}

// 导出单例实例
export const poolCache = PoolCache.getInstance();
