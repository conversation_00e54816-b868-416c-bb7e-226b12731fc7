function jsonParse(jsonStr: string): any {
    // 移除首尾空白字符
    jsonStr = jsonStr.trim();
    
    // 检查是否为空字符串
    if (jsonStr === '') {
        throw new Error('Empty JSON string');
    }

    // 解析器核心
    let index = 0;

    function parseValue(): any {
        skipWhitespace();
        
        const char = jsonStr[index];
        
        if (char === '{') {
            return parseObject();
        } else if (char === '[') {
            return parseArray();
        } else if (char === '"') {
            return parseString();
        } else if (char === 't' || char === 'f') {
            return parseBoolean();
        } else if (char === 'n') {
            return parseNull();
        } else if (char === '-' || isDigit(char)) {
            return parseNumber();
        }
        
        throw new Error(`Unexpected character at position ${index}: ${char}`);
    }

    function skipWhitespace() {
        while (index < jsonStr.length && /\s/.test(jsonStr[index])) {
            index++;
        }
    }

    function parseObject(): Record<string, any> {
        const obj: Record<string, any> = {};
        index++; // 跳过 {
        
        skipWhitespace();
        if (jsonStr[index] === '}') {
            index++;
            return obj;
        }

        while (index < jsonStr.length) {
            skipWhitespace();
            if (jsonStr[index] !== '"') {
                throw new Error(`Expected string key at position ${index}`);
            }
            
            const key = parseString();
            skipWhitespace();
            
            if (jsonStr[index] !== ':') {
                throw new Error(`Expected colon at position ${index}`);
            }
            index++; // 跳过 :
            
            const value = parseValue();
            obj[key] = value;
            
            skipWhitespace();
            if (jsonStr[index] === '}') {
                index++;
                return obj;
            }
            if (jsonStr[index] !== ',') {
                throw new Error(`Expected comma or closing brace at position ${index}`);
            }
            index++; // 跳过 ,
        }
        
        throw new Error('Unterminated object');
    }

    function parseArray(): any[] {
        const arr: any[] = [];
        index++; // 跳过 [
        
        skipWhitespace();
        if (jsonStr[index] === ']') {
            index++;
            return arr;
        }

        while (index < jsonStr.length) {
            const value = parseValue();
            arr.push(value);
            
            skipWhitespace();
            if (jsonStr[index] === ']') {
                index++;
                return arr;
            }
            if (jsonStr[index] !== ',') {
                throw new Error(`Expected comma or closing bracket at position ${index}`);
            }
            index++; // 跳过 ,
        }
        
        throw new Error('Unterminated array');
    }

    function parseString(): string {
        let result = '';
        index++; // 跳过开头的 "
        
        while (index < jsonStr.length) {
            const char = jsonStr[index];
            
            if (char === '"') {
                index++;
                return result;
            }
            
            if (char === '\\') {
                index++;
                const nextChar = jsonStr[index];
                switch (nextChar) {
                    case '"': result += '"'; break;
                    case '\\': result += '\\'; break;
                    case '/': result += '/'; break;
                    case 'b': result += '\b'; break;
                    case 'f': result += '\f'; break;
                    case 'n': result += '\n'; break;
                    case 'r': result += '\r'; break;
                    case 't': result += '\t'; break;
                    default: throw new Error(`Invalid escape sequence at position ${index}`);
                }
            } else {
                result += char;
            }
            index++;
        }
        
        throw new Error('Unterminated string');
    }

    function parseNumber(): number {
        const startIndex = index;
        let isNegative = false;
        
        // 处理负号
        if (jsonStr[index] === '-') {
            isNegative = true;
            index++;
        }
        
        // 处理整数部分
        if (jsonStr[index] === '0') {
            index++;
        } else if (isDigit(jsonStr[index])) {
            while (index < jsonStr.length && isDigit(jsonStr[index])) {
                index++;
            }
        } else {
            throw new Error(`Invalid number at position ${startIndex}`);
        }
        
        // 处理小数部分
        if (jsonStr[index] === '.') {
            index++;
            if (!isDigit(jsonStr[index])) {
                throw new Error(`Invalid number at position ${index}`);
            }
            while (index < jsonStr.length && isDigit(jsonStr[index])) {
                index++;
            }
        }
        
        // 处理指数部分
        if (jsonStr[index] === 'e' || jsonStr[index] === 'E') {
            index++;
            if (jsonStr[index] === '+' || jsonStr[index] === '-') {
                index++;
            }
            if (!isDigit(jsonStr[index])) {
                throw new Error(`Invalid number at position ${index}`);
            }
            while (index < jsonStr.length && isDigit(jsonStr[index])) {
                index++;
            }
        }
        
        // 转换为数字
        const numStr = jsonStr.substring(startIndex, index);
        // 使用原生JSON.parse确保大数值的准确性
        return JSON.parse(numStr);
    }

    function parseBoolean(): boolean {
        if (jsonStr.substr(index, 4) === 'true') {
            index += 4;
            return true;
        }
        if (jsonStr.substr(index, 5) === 'false') {
            index += 5;
            return false;
        }
        throw new Error(`Invalid boolean at position ${index}`);
    }

    function parseNull(): null {
        if (jsonStr.substr(index, 4) === 'null') {
            index += 4;
            return null;
        }
        throw new Error(`Invalid null at position ${index}`);
    }

    function isDigit(char: string): boolean {
        return /[0-9]/.test(char);
    }

    const result = parseValue();
    skipWhitespace();
    
    if (index < jsonStr.length) {
        throw new Error(`Unexpected trailing characters at position ${index}`);
    }
    
    return result;
}

// 测试示例
try {
    const test1 = jsonParse('{"name": "John", "age": 30, "isStudent": false, "grades": [90, 85.5, 95]}');
    console.log(test1);
    
    const test2 = jsonParse('["apple", 42, true, null]');
    console.log(test2);
} catch (e) {
    console.error(e);
}

export default jsonParse;