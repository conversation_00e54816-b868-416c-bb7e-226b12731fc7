# MultiFactorsCTA.py Refactored Code with stra_prepare_bars fix

from wtpy import BaseCtaStrategy
from wtpy import CtaContext
import numpy as np
import pandas as pd
import importlib
import importlib.util # Use importlib.util for loading from file path
import os
import datetime
import json
from typing import Dict, List, Tuple, Any, Optional, Set
import yaml
import math
import sys
import re # For formula parsing
from asteval import Interpreter # For safe formula evaluation

# --- Import the external evaluation function ---
# 从库 strategy.portfolio 中导入函数
from strategy.portfolio.eval_formula import evaluate_formula_string

# --- Setup & Helpers ---
script_path = os.path.abspath(__file__)
script_dir = os.path.dirname(script_path)
parent_dir = os.path.join(script_dir, '..')
sys.path.insert(0, os.path.abspath(parent_dir))

show_log = True # Enable logging for debugging refactor
def print_log(message):
    """Helper function for logging with timestamp."""
    if show_log:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {message}")

# 把周期描述转化成为统一的标识，将会用于数据文件后缀 xxx_m5.csv
def convert_period1(period:str) -> str:
    """Converts human-readable period to WT engine format."""
    if period == "1m": return "m1"
    if period == "5m": return "m5"
    if period == "15m": return "m15"
    if period == "30m": return "m30"
    if period == "1h": return "h1"
    if period == "4h": return "h4"
    if period == "1D": return "d1" # 为什么实际日期是 _d.csv 呢？
    if period == "1W": return "w1"
    if period == "day": return "d1" # 为什么实际日期是 _d.csv 呢？
    if period == "week": return "w1"
    # Note: Monthly might need special handling or map to d1 if data freq is daily
    if period == "month":
        print_log("警告: 月度周期可能需要特殊处理，暂时映射到 d1。")
        return "d1"
    return period
# --- End Helpers ---

# --- Default Commission Rate for internal equity calculation ---
DEFAULT_INTERNAL_COMMISSION_RATE = 0.0003

class MultiFactorsCTA(BaseCtaStrategy):
    """
    Refactored Multi-Factor Strategy using Pre-calculation Framework.
    """
    # --- Method Definitions Moved Up ---

    # --- REVISED: State Synchronization Function (Optimistic Update + Correction) ---
    def __synchronize_state_with_engine__(self, context: CtaContext):
        """
        Checks for discrepancies between the last intended state (logical positions)
        and the current actual positions reported by the engine.
        If discrepancies are found, corrects the internal logical balance (__nominal_equity)
        to reverse the impact of optimistic updates from failed/incomplete trades,
        and forces the logical positions (__intended_positions__) to match the actual state.
        This runs at the beginning of on_calculate.
        """
        current_time_str = f"[{context.stra_get_date()} {context.stra_get_time():06d}]"
        print_log(f"{current_time_str} [状态同步 Cor] 开始检查并修正状态...")

        actual_positions = context.stra_get_all_position()
        actual_positions_filtered = {code: qty for code, qty in actual_positions.items() if abs(qty) > 1e-6}

        # Get the state we *intended* to be in at the end of the last cycle
        previous_intended_positions = self.__intended_positions__.copy()

        all_codes = set(actual_positions_filtered.keys()) | set(previous_intended_positions.keys())

        if not all_codes:
            print_log(f"{current_time_str} [状态同步 Cor] 无实际持仓且无上一轮逻辑仓位，无需修正。")
            # Ensure internal state is clean if nothing is held
            self.__intended_positions__ = {}
            # self.__nominal_equity remains as is
            return actual_positions_filtered

        print_log(f"{current_time_str} [状态同步 Cor] 上一轮逻辑仓位(预期): {previous_intended_positions}")
        print_log(f"{current_time_str} [状态同步 Cor] 引擎当前实际持仓: {actual_positions_filtered}")

        correction_total = 0.0
        codes_corrected = set()

        for code in all_codes:
            actual_qty = actual_positions_filtered.get(code, 0.0)
            intended_details = previous_intended_positions.get(code, {}) # Get dict or empty dict
            intended_qty = intended_details.get('quantity', 0.0) # Get quantity, default 0.0

            if abs(actual_qty - intended_qty) > 1e-6:
                codes_corrected.add(code)
                discrepancy_qty = actual_qty - intended_qty # Positive: engine has more; Negative: engine has less
                print_log(f"{current_time_str} [状态同步 Cor] {code}: 发现差异! 逻辑(预期)={intended_qty:.2f}, 实际={actual_qty:.2f}, 差异={discrepancy_qty:.2f}")

                # We need to reverse the optimistic equity change associated with the discrepancy.
                # This requires knowing the price at the time of the optimistic update.
                # Since we don't store that price reliably with the intended state in this simple model,
                # we have to make an approximation using the CURRENT price.
                # This is a limitation of the simple optimistic update approach without detailed trade logging.
                price = context.stra_get_price(code)
                if price <= 0:
                    print_log(f"{current_time_str} [状态同步 Cor] {code}: 获取当前价格失败 ({price})，无法计算修正值。跳过此品种的余额修正。")
                    # IMPORTANT: Still force logical position sync below
                else:
                    # Calculate value and commission of the DISCREPANCY quantity
                    value_of_discrepancy_abs = self._calculate_nominal_value(context, code, abs(discrepancy_qty), price)
                    commission_on_discrepancy_abs = value_of_discrepancy_abs * self.__commission_rate__

                    correction = 0.0
                    # Example logic (needs careful thought based on optimistic update effects):
                    if discrepancy_qty > 0:
                        # Engine has MORE than intended (e.g., failed close, bought too much)
                        # The optimistic update might have incorrectly ADDED value (failed close)
                        # or incorrectly DEDUCTED cost (failed open where qty ended up > 0 somehow).
                        # To correct: We likely need to reverse an optimistic gain or apply the cost that wasn't fully applied.
                        # If it was a failed close (intended 0, actual > 0), optimistic + (value-comm). Correction needs to reverse this: - (value-comm).
                        # If it was a failed open (intended N, actual > N), optimistic - (cost+comm) for N. Correction needs to apply cost for extra: - (cost+comm for discrepancy).
                        # Let's assume for discrepancy > 0, the optimistic update was wrong by not accounting for the cost of these extra shares.
                        # Correction = apply the cost = -(value + commission)
                        correction = - (value_of_discrepancy_abs + commission_on_discrepancy_abs)
                        print_log(f"{current_time_str} [状态同步 Cor] {code}: 引擎多于预期. 修正逻辑余额: {correction:.2f} (假定未记成本)")

                    elif discrepancy_qty < 0:
                        # Engine has LESS than intended (e.g., failed open, sold too much)
                        # Optimistic update might have incorrectly DEDUCTED cost (failed open)
                        # or incorrectly ADDED value (failed close where qty ended up < 0 somehow).
                        # To correct: Reverse the optimistic cost or apply the gain that wasn't fully applied.
                        # If it was a failed open (intended N > 0, actual < N), optimistic - (cost+comm) for N. Correction needs to reverse cost for unsold part: + (cost+comm for discrepancy).
                        # If it was a failed close (intended 0, actual < 0), optimistic + (value-comm). Correction needs to apply gain for extra short: + (value-comm for discrepancy).
                        # Let's assume for discrepancy < 0, the optimistic update was wrong by applying cost for shares never acquired.
                        # Correction = reverse the cost = +(value + commission) (Note: cost is value+commission here)
                        # Correction = reverse the value deduction = +(value - commission) ??? No, reverse the cost deduction.
                        # Cost was (value + commission). Need to add back the cost part for the discrepancy.
                        correction = + (value_of_discrepancy_abs + commission_on_discrepancy_abs) # Add back the cost that was likely deducted.
                        print_log(f"{current_time_str} [状态同步 Cor] {code}: 引擎少于预期. 修正逻辑余额: {correction:.2f} (假定多记成本)")

                    correction_total += correction

                # --- IMMEDIATELY Update intended state to actual upon detecting discrepancy ---
                print_log(f"{current_time_str} [状态同步 Cor] {code}: 强制更新逻辑仓位以匹配实际: {intended_qty:.2f} -> {actual_qty:.2f}")
                if abs(actual_qty) < 1e-6:
                    # If actual is zero, remove from intended
                    if code in self.__intended_positions__:
                        del self.__intended_positions__[code]
                else:
                    # Update intended with actual qty, marking as synced/corrected
                    self.__intended_positions__[code] = {
                        'quantity': actual_qty,
                        'action': 'corrected_sync' # Mark as corrected/synced
                    }

            # else: actual_qty == intended_qty, no correction needed for this code.
            # The entry in self.__intended_positions__ for this code remains untouched
            # and already reflects the correct quantity and details from the last action.

        # --- Apply corrections and log results ---
        if codes_corrected:
            previous_equity = self.__nominal_equity
            self.__nominal_equity += correction_total
            print_log(f"{current_time_str} [状态同步 Cor] 修正完成。修正的品种: {codes_corrected}")
            print_log(f"{current_time_str} [状态同步 Cor] 逻辑余额修正合计: {correction_total:.2f}. 从 {previous_equity:.2f} -> {self.__nominal_equity:.2f}")
            # Log the final state *only if* corrections were made, as it has changed.
            print_log(f"{current_time_str} [状态同步 Cor] 修正后最终逻辑仓位字典: {self.__intended_positions__}")
        else:
            print_log(f"{current_time_str} [状态同步 Cor] 修正完成。未发现需要修正的品种。当前逻辑余额: {self.__nominal_equity:.2f}")
            # Log the state even if no corrections, for confirmation
            print_log(f"{current_time_str} [状态同步 Cor] 当前逻辑仓位字典(无需修正): {self.__intended_positions__}")

        # REMOVED the final redundant check and overwrite step.
        # The __intended_positions__ dict is now correctly updated within the loop for corrected items,
        # and items without discrepancies are left as they were (already reflecting reality).

        return actual_positions_filtered

    # --- Helper to parse formulas (Moved Up) ---
    def __parse_formula_and_collect_factors__(self, formula: str) -> Tuple[int, Dict[str, Tuple[str, str, Dict[str, Any]]]]:
        """
        Parses a SINGLE formula string to extract base factor calls.
        Adds unique requirements to self.__required_base_factors__.
        Returns the maximum period found in this formula and the parsed factor details for this formula.
        """
        max_period_in_formula = 0
        # Remove local tracking, directly add to global set
        # parsed_factors_for_this_formula = {}

        # Regex to find function calls like factor_name(arg1, arg2, ...)
        pattern = re.compile(r"([a-zA-Z_][a-zA-Z0-9_]*)\s*\(([^)]*)\)")
        matches = pattern.findall(formula)

        for factor_func_name, args_str in matches:
            args = [arg.strip() for arg in args_str.split(',') if arg.strip()]
            if not args:
                # print_log(f"[公式解析] 警告: 因子 {factor_func_name} 在公式 '{formula}' 中缺少参数。")
                continue

            column = args[0] # First argument is always the column name
            params = {}
            current_period = 0
            if len(args) > 1:
                try:
                    # Assuming the second arg is the primary numeric param (period)
                    current_period = int(args[1])
                    params['period'] = current_period
                    max_period_in_formula = max(max_period_in_formula, current_period)
                    # Extend here for more named params like slowperiod=int(args[2]), etc.
                except (ValueError, IndexError):
                    # print_log(f"[公式解析] 警告: 无法解析因子 {factor_func_name} 在公式 '{formula}' 中的数字参数: {args[1:]}")
                    continue

            # Create tuple of sorted param items for unique identification
            params_tuple = tuple(sorted(params.items()))
            # Factor key used in formula evaluation namespace and replacement (needed later)
            # factor_key = f"{factor_func_name}_{column}_{'_'.join(f'{k}{v}' for k, v in params_tuple)}"

            # Store mapping from the key used in formula to its components *locally* for return
            # This part is removed as we don't need the parsed details returned anymore
            # parsed_factors_for_this_formula[factor_key] = (factor_func_name, column, params)

            # Add the unique base factor requirement to the *global* set for the strategy
            self.__required_base_factors__.add((factor_func_name, column, params_tuple))

        # We only need to return the max period found in this formula
        # The parsed factor details aren't strictly needed by on_init anymore
        return max_period_in_formula, {} # Adjusted return value

    # --- Helper to load factor modules (Moved Up) ---
    def __load_required_factor_modules__(self) -> bool:
        """Loads the Python modules needed for the required base factors."""
        # 修改：因子模块统一用库引用方式
        all_loaded = True

        factor_names_to_load = {f_name for f_name, _, _ in self.__required_base_factors__}
        print_log(f"[因子加载] 需要加载的因子模块: {factor_names_to_load}")

        for factor_name in factor_names_to_load:
            if factor_name in self.__factor_modules__: continue

            # 改为库引用方式
            module_path = f"strategy.portfolio.factors.{factor_name}"
            try:
                factor_module = importlib.import_module(module_path)
                if hasattr(factor_module, 'calculate_value'):
                    self.__factor_modules__[factor_name] = factor_module
                    print_log(f"[因子加载] 成功加载因子模块: {factor_name} (需要 calculate_value 函数)")
                else:
                    print_log(f"[因子加载] 错误: 因子模块 {factor_name} 缺少 'calculate_value' 函数。")
                    all_loaded = False
            except Exception as e:
                print_log(f"[因子加载] 加载因子模块 {factor_name} 时出错: {e}")
                import traceback; print_log(traceback.format_exc())
                all_loaded = False
        return all_loaded

    # --- Refactored __init__ (Now defines intended_positions) ---
    def __init__(self, name: str, codes: list, barCnt: int, period: str,
                 order_by_config: dict = {},
                 buy_rules_config: dict = {}, sell_rules_config: dict = {},
                 top_n: int = 1, weighting_scheme: str = 'equal',
                 rebalance_interval: str = 'daily'):
        """
        Initializes the strategy, storing configuration parameters.
        """
        BaseCtaStrategy.__init__(self, name)
        self.__name__ = name
        self.__codes__ = codes # Universe
        self.__bar_cnt__ = barCnt + 5
        self.__period__ = period
        
        self.__order_by_config__ = order_by_config
        self.__buy_rules_config__ = buy_rules_config
        self.__sell_rules_config__ = sell_rules_config
        self.__top_n__ = top_n
        self.__weighting_scheme__ = weighting_scheme
        self.__rebalance_interval__ = rebalance_interval
        self.__commission_rate__ = DEFAULT_INTERNAL_COMMISSION_RATE
        self.__buy_rules_formulas__ = self.__buy_rules_config__.get('formulas', [])
        self.__buy_rules_required_count__ = self.__buy_rules_config__.get('at_least_count', len(self.__buy_rules_formulas__) if self.__buy_rules_formulas__ else 0)
        self.__sell_rules_formulas__ = self.__sell_rules_config__.get('formulas', [])
        self.__sell_rules_required_count__ = self.__sell_rules_config__.get('at_least_count', len(self.__sell_rules_formulas__) if self.__sell_rules_formulas__ else 0)
        # self.__bar_cnt__ = barCnt # Keep initial value, may be adjusted - Handled above
        self.__last_rebalance_date__ = 0
        self.__last_rebalance_timestamp__ = 0 # 增加分钟级时间戳初始化
        self.__last_scores__ = pd.Series(dtype=float)
        self.__nominal_equity = 100000.0
        self.__margin_rate__ = 0.15
        # --- REVISED: Store detailed intended state ---
        # self.__intended_positions__: Dict[str, float] = {} # Old: {code: logical_quantity}
        self.__intended_positions__: Dict[str, Dict[str, Any]] = {} # New: {code: {details...}}
        self.__factor_modules__: Dict[str, Any] = {}
        self.__required_base_factors__: Set[Tuple[str, str, Tuple[Tuple[str, Any], ...]]] = set()
        self.__aeval__ = Interpreter()

        print_log(f"[策略初始化] {name}: Codes={len(codes)}, Period={period}, TopN={top_n}, Rebalance={rebalance_interval}")
        print_log(f"[策略初始化] OrderBy Formula: {self.__order_by_config__.get('formula', 'N/A')}")
        print_log(f"[策略初始化] Buy Rules: {len(self.__buy_rules_formulas__)} formulas, require {self.__buy_rules_required_count__}")
        print_log(f"[策略初始化] Sell Rules: {len(self.__sell_rules_formulas__)} formulas, require {self.__sell_rules_required_count__}")
        print_log(f"[策略初始化] 设置初始逻辑余额: {self.__nominal_equity:.2f}")
        print_log(f"[策略初始化] 设置内部佣金率: {self.__commission_rate__}")
        print_log(f"[策略初始化] 初始化逻辑仓位: {self.__intended_positions__}") # Log initial state

    def __get_timestamp_in_minutes__(self, cur_date: int, cur_time: int) -> int:
        """
        将WonderTrader的日期和时间转换为自epoch以来的总分钟数。

        Args:
            cur_date (int): 格式为 YYYYMMDD 的日期.
            cur_time (int): 格式为 HHMMSS 的时间.

        Returns:
            int: 自epoch以来的总分钟数，如果转换失败则返回0.
        """
        try:
            # 格式化时间以确保它是6位数 (HHMMSS)
            time_str = f"{cur_time:06d}"
            # 组合日期和时间字符串
            datetime_str = f"{cur_date}{time_str}"
            # 解析成datetime对象
            dt_obj = datetime.datetime.strptime(datetime_str, "%Y%m%d%H%M%S")
            # 返回自epoch以来的总分钟数
            return int(dt_obj.timestamp() // 60)
        except (ValueError, TypeError) as e:
            print_log(f"[时间戳转换] 无法将日期 {cur_date} 和时间 {cur_time} 转换为时间戳: {e}")
            return 0

    # --- Restore on_init ---
    def on_init(self, context: CtaContext):
        """
        Strategy initialization: Parses ALL formulas, loads modules, calculates
        required bar count, and prepares bars.
        """

        print("------------------on init---------------")

        print_log(f"[策略回测] {self.__name__} 策略初始化 (on_init)...")

        # --- 1. Reset Requirements and Max Period ---
        self.__required_base_factors__.clear() # Clear previous requirements if any
        max_period_overall = 0

        # --- 2. Parse ALL Formulas (Order By, Buy, Sell) ---
        print_log("[策略准备] 开始解析所有公式 (排序、买入、卖出)...")

        # Parse Order By formula
        order_by_formula = self.__order_by_config__.get('formula')
        if order_by_formula:
            print_log(f"[策略准备] 解析排序公式: {order_by_formula}")
            period, _ = self.__parse_formula_and_collect_factors__(order_by_formula)
            max_period_overall = max(max_period_overall, period)
        else:
            print_log("[策略准备] 警告: 未配置排序公式 (order_by.formula)")
            # Consider returning if order_by is essential

        # Parse Buy Rule formulas
        if self.__buy_rules_formulas__:
            print_log(f"[策略准备] 解析买入规则公式 ({len(self.__buy_rules_formulas__)}条)...")
            for i, buy_formula in enumerate(self.__buy_rules_formulas__):
                 print_log(f"[策略准备]  - 买入规则[{i}]: {buy_formula}")
                 period, _ = self.__parse_formula_and_collect_factors__(buy_formula)
                 max_period_overall = max(max_period_overall, period)
        else:
             print_log("[策略准备] 未配置买入规则公式。")

        # Parse Sell Rule formulas
        if self.__sell_rules_formulas__:
            print_log(f"[策略准备] 解析卖出规则公式 ({len(self.__sell_rules_formulas__)}条)...")
            for i, sell_formula in enumerate(self.__sell_rules_formulas__):
                 print_log(f"[策略准备]  - 卖出规则[{i}]: {sell_formula}")
                 period, _ = self.__parse_formula_and_collect_factors__(sell_formula)
                 max_period_overall = max(max_period_overall, period)
        else:
             print_log("[策略准备] 未配置卖出规则公式。")

        if not self.__required_base_factors__:
            print_log("[策略准备] 错误: 未从任何公式中解析出需要计算的基础因子!")
            # Depending on logic, might need to return here
            # return

        print_log(f"[策略准备] 所有公式解析完成。需要的基础因子: {self.__required_base_factors__}")
        print_log(f"[策略准备] 所有公式中最大周期为: {max_period_overall}")

        # --- 3. Calculate Final Bar Count ---
        # Add buffer (e.g., 5) to the max period found
        self.__bar_cnt__ = max_period_overall + 5
        print_log(f"[策略准备] 计算最终所需 K 线数量 (bar_cnt): {self.__bar_cnt__} (最大周期 {max_period_overall} + 5 缓冲)")

        # --- 4. Load Factor Modules --- (Based on collected factors)
        print_log("[策略准备] 开始加载所需因子模块...")
        if not self.__load_required_factor_modules__():
             print_log("[策略准备] 错误: 加载必需的因子模块失败，无法继续初始化。")
             return
        print_log("[策略准备] 因子模块加载完成。")

        # --- 5. Prepare Bars (Crucial Step for WT Engine) --- (Using calculated bar_cnt)
        print_log(f"[策略准备] 通知引擎准备 {len(self.__codes__)} 个品种的K线数据 (使用计算的 bar_cnt: {self.__bar_cnt__})...")
        for i, code in enumerate(self.__codes__):
            try:
                # 第一个品种设为主K线，其他为辅助K线
                is_main = (i == 0)
                print_log(f"[策略准备] stra_prepare_bars({code}, {convert_period1(self.__period__)}, {self.__bar_cnt__}, isMain={is_main})")
                context.stra_prepare_bars(code, convert_period1(self.__period__), self.__bar_cnt__, isMain=is_main)
            except Exception as e:
                print_log(f"[策略准备] 引擎准备 {code} K线数据时出错: {e}")
        print_log(f"[策略准备] 引擎数据准备通知完成。")

        print_log(f"[策略回测] {self.__name__} 策略初始化完成 (on_init)")

    # ... (Keep other methods like __load_required_factor_modules__, __is_rebalance_time__, etc.) ...
    # ... (__calculate_current_scores__ and __rebalance_portfolio__ will be modified next) ...


    # --- Keep __is_rebalance_time__ ---
    def __is_rebalance_time__(self, context:CtaContext) -> bool:
        cur_date = context.stra_get_date()
        cur_time = context.stra_get_time()

        print_log(f"调仓间隔数值为：{self.__rebalance_interval__}")
        # 打印各种时间戳
        print_log(f"当前时间戳：{cur_date} {cur_time:06d}")
        if self.__rebalance_interval__ in ['5m', '15m', '1h']:
             print_log(f"上次调仓时间戳（分钟）：{self.__last_rebalance_timestamp__}")
        else:
             print_log(f"上次调仓日期：{self.__last_rebalance_date__}")


        # 对于分钟级调仓，需要使用时间戳进行比较
        if self.__rebalance_interval__ in ['1m', '5m', '15m', '1h']:
            cur_timestamp_minutes = self.__get_timestamp_in_minutes__(cur_date, cur_time)
            if cur_timestamp_minutes == 0: # 处理转换失败的情况
                return False

            print_log(f"[调仓检查] 分钟级调仓检查: 间隔={self.__rebalance_interval__}")
            print_log(f"[调仓检查] 当前分钟时间戳: {cur_timestamp_minutes}")
            print_log(f"[调仓检查] 上次调仓分钟时间戳: {self.__last_rebalance_timestamp__}")

            if self.__last_rebalance_timestamp__ == 0:
                self.__last_rebalance_timestamp__ = cur_timestamp_minutes
                print_log(f"[调仓检查] 首次调仓，设置时间戳为 {cur_timestamp_minutes} 并执行")
                return True

            time_diff = cur_timestamp_minutes - self.__last_rebalance_timestamp__

            interval = self.__rebalance_interval__
            should_rebalance = False
            interval_minutes = 0
            if interval == '1m':
                interval_minutes = 1
            if interval == '5m':
                interval_minutes = 5
            elif interval == '15m':
                interval_minutes = 15
            elif interval == '1h':
                interval_minutes = 60
            
            if interval_minutes > 0:
                should_rebalance = time_diff >= interval_minutes
                print_log(f"[调仓检查] {interval} 检查: 时间差={time_diff}分钟 >= {interval_minutes} = {should_rebalance}")
            
            return should_rebalance


        # 对于日级及以上调仓，使用原有逻辑
        if self.__last_rebalance_date__ == 0:
            self.__last_rebalance_date__ = cur_date
            return True
        if cur_date == self.__last_rebalance_date__:
            return False

        try:
            cur_dt = datetime.datetime.strptime(str(cur_date), "%Y%m%d")
            last_dt = datetime.datetime.strptime(str(self.__last_rebalance_date__), "%Y%m%d")
        except ValueError as e:
            print_log(f"[调仓检查] 日期解析错误: cur={cur_date}, last={self.__last_rebalance_date__}, err={e}")
            return False

        interval = self.__rebalance_interval__
        should_rebalance = False
        if interval == 'daily':
            should_rebalance = True
        elif interval == 'weekly':
            should_rebalance = cur_dt.weekday() == 0 # Monday
        elif interval == 'monthly':
            should_rebalance = cur_dt.month != last_dt.month
        elif isinstance(interval, str) and interval.endswith('d'):
            try:
                days = int(interval[:-1])
                delta = (cur_dt - last_dt).days
                should_rebalance = delta >= days
            except ValueError:
                 print_log(f"[调仓检查] 无法解析调仓周期天数: {interval}")
                 should_rebalance = True
        else:
            print_log(f"[调仓检查] 未知的调仓周期: {interval}，执行调仓。")
            should_rebalance = True

        # 更新调仓时间戳
        if self.__rebalance_interval__ in ['1m', '5m', '15m', '1h']:
            # 对于分钟级调仓，更新时间戳
            self.__last_rebalance_timestamp__ = self.__get_timestamp_in_minutes__(cur_date, cur_time)
            print_log(f"[on_calculate] 更新分钟级调仓时间戳为: {self.__last_rebalance_timestamp__}")
        else:
            # 对于日级及以上调仓，更新日期
            self.__last_rebalance_date__ = cur_date

        return should_rebalance

    # --- Renamed: Calculate base factors AND evaluate all rules ---
    def __evaluate_factors_and_rules__(self, context: CtaContext) -> Optional[Dict[str, Any]]:
        """Calculates factor scores for all codes at the current time.
        This method fetches bars for each code individually and calculates factors.
        Assumes factor modules have a 'calculate_value(df, column, **params)' method.
        """
        # --- Define current_time_str at the beginning ---
        current_time_str = f"[{context.stra_get_date()} {context.stra_get_time():06d}]"
        cur_date = context.stra_get_date()  # 新增：用于日志输出
        # print_log(f"{current_time_str} 开始计算基础因子并评估所有规则...")  # 简化日志

        # --- Fetch Bars Data ---
        temp_code_data = {} # Cache bars objects
        valid_codes = []    # 新增：有效品种列表
        fetch_success_count = 0
        for code in self.__codes__:
            try:
                bars = context.stra_get_bars(code, convert_period1(self.__period__), self.__bar_cnt__)
                if bars is not None and len(bars) >= self.__bar_cnt__:
                    temp_code_data[code] = bars
                    valid_codes.append(code)
                    fetch_success_count += 1
                else:
                    print_log(f"[数据忽略] {code}: {cur_date} 本轮K线数据无效，已忽略")
                    temp_code_data[code] = None
            except Exception as e:
                print_log(f"[数据忽略] {code}: {cur_date} 本轮K线数据无效，已忽略")
                temp_code_data[code] = None

        if fetch_success_count == 0:
             print_log(f"{current_time_str} [因子计算] 严重错误: 所有品种的K线数据均获取失败或不足，无法继续计算。")
             # 检查是否是因为没有更多数据
             no_more_data = True
             for code in self.__codes__:
                 try:
                     bars = context.stra_get_bars(code, convert_period1(self.__period__), 1)
                     if bars is not None and len(bars) > 0:
                         no_more_data = False
                         break
                 except:
                     continue

             if no_more_data:
                 # 使用更明确的日志格式，确保这些日志能被注意到
                 print_log("="*80)
                 print_log(f"{current_time_str} [数据结束] 所有品种均无更多数据可用，回测数据已到达终点。")
                 print_log(f"{current_time_str} [数据结束] 最后一个交易日: {context.stra_get_date()}")
                 print_log(f"{current_time_str} [数据结束] 如果这不是预期的结束时间，请检查通达信数据是否更新。")
                 print_log("="*80)
             return None # Return None as per type hint

        # --- 只对有效品种做后续处理 ---
        # 后续所有因子、规则、排序、买卖决策都只对 valid_codes 进行

        # --- Calculate all required base factors ---
        eval_namespace = {}
        base_factor_calculation_errors = False # Initialize flag
        for factor_name, column, params_tuple in self.__required_base_factors__:
            factor_module = self.__factor_modules__.get(factor_name)
            factor_key = f"{factor_name}_{column}_{'_'.join(f'{k}{v}' for k, v in params_tuple)}" # Define key here
            if not factor_module or not hasattr(factor_module, 'calculate_value'):
                print_log(f"{current_time_str} [因子计算] 错误: 因子模块 {factor_name} 未加载或缺少 calculate_value。跳过 ({factor_key})。")
                eval_namespace[factor_key] = pd.Series(np.nan, index=valid_codes)
                base_factor_calculation_errors = True # Set flag
                continue
            params_dict = dict(params_tuple)
            call_params = {'column': column, **params_dict}
            current_factor_values = {}
            for code in valid_codes:
                bars = temp_code_data.get(code)
                if bars is not None:
                    try:
                        value = factor_module.calculate_value(
                            opens=bars.opens, highs=bars.highs, lows=bars.lows,
                            closes=bars.closes, volumes=bars.volumes,
                            **call_params
                        )
                        current_factor_values[code] = value
                    except Exception as e:
                        current_factor_values[code] = np.nan
                else:
                    current_factor_values[code] = np.nan
            eval_namespace[factor_key] = pd.Series(current_factor_values)

        if base_factor_calculation_errors:
             print_log(f"{current_time_str} [因子计算] 警告: 部分基础因子因模块问题未能计算。")

        print_log(f"{current_time_str} [因子计算] 完成 {len(eval_namespace)} 个因子计算")

        # --- 准备原始数据列 ---
        raw_data_columns = {}
        for column in ['open', 'high', 'low', 'close', 'volume']:
            column_data = {}
            for code in valid_codes:
                bars = temp_code_data.get(code)
                if bars is not None:
                    # 获取每个品种的最后一个时间点的数据
                    if column == 'open':
                        column_data[code] = bars.opens[-1] if len(bars.opens) > 0 else np.nan
                    elif column == 'high':
                        column_data[code] = bars.highs[-1] if len(bars.highs) > 0 else np.nan
                    elif column == 'low':
                        column_data[code] = bars.lows[-1] if len(bars.lows) > 0 else np.nan
                    elif column == 'close':
                        column_data[code] = bars.closes[-1] if len(bars.closes) > 0 else np.nan
                    elif column == 'volume':
                        column_data[code] = bars.volumes[-1] if len(bars.volumes) > 0 else np.nan
                else:
                    column_data[code] = np.nan
            raw_data_columns[column] = pd.Series(column_data)

        # --- Evaluate Order By Formula ---
        order_by_formula = self.__order_by_config__.get('formula', '0')
        scores = evaluate_formula_string(order_by_formula, eval_namespace, self.__aeval__, valid_codes, print_log, raw_data_columns)
        if scores is None:
             print_log(f"{current_time_str} [规则评估] 错误: 排序公式评估失败。将使用 NaN 分数。")
             scores = pd.Series(np.nan, index=valid_codes)
        else:
            print_log(f"{current_time_str} [规则评估] 排序得分计算完成")

        # --- Evaluate Buy Rule Formulas ---
        buy_rule_results = []
        if self.__buy_rules_formulas__:
             for i, formula in enumerate(self.__buy_rules_formulas__):
                 result = evaluate_formula_string(formula, eval_namespace, self.__aeval__, valid_codes, print_log, raw_data_columns)
                 buy_rule_results.append(result if result is not None else pd.Series(False, index=valid_codes))

        # --- Evaluate Sell Rule Formulas ---
        sell_rule_results = []
        if self.__sell_rules_formulas__:
             for i, formula in enumerate(self.__sell_rules_formulas__):
                 result = evaluate_formula_string(formula, eval_namespace, self.__aeval__, valid_codes, print_log, raw_data_columns)
                 sell_rule_results.append(result if result is not None else pd.Series(False, index=valid_codes))

        print_log(f"{current_time_str} [规则评估] 完成规则评估")

        # --- Return all results in the correct dictionary structure ---
        return {
            'scores': scores,
            'buy_rule_results': buy_rule_results,
            'sell_rule_results': sell_rule_results
        }
    # --- End of __evaluate_factors_and_rules__ ---

    # --- Keep _calculate_nominal_value ---
    def _calculate_nominal_value(self, context: CtaContext, code: str, quantity: float, price: float) -> float:
        # 检查是否为虚拟货币
        is_stk = '.STK.' in code or '.ETF.' in code
        is_crypto = '.CPT.' in code
        
        if is_stk:
            return quantity * price if price > 0 else 0.0
        elif is_crypto:
            # 虚拟货币：直接用数量乘以价格
            return quantity * price if price > 0 else 0.0
        else:
            # 期货：使用交易乘数
            pInfo = context.stra_get_comminfo(code)
            if pInfo:
                return quantity * price * pInfo.volscale if price > 0 and pInfo.volscale > 0 else 0.0
            else:
                print_log(f"[名义价值计算] {code}: 无法获取品种信息 (期货)")
                return 0.0

    def __check_rules_for_asset__(self,
                                    rule_results: List[pd.Series],
                                    required_count: int,
                                    asset_code: str) -> bool:
        """
        Checks if a specific asset meets the required number of rules.

        Args:
            rule_results: A list where each element is a Pandas Series
                        representing the boolean result of one rule formula
                        across all assets (index=asset_code, value=True/False/NaN).
            required_count: The minimum number of rules that must evaluate to True.
            asset_code: The specific asset code to check.

        Returns:
            bool: True if the asset meets the required number of rules, False otherwise.
        """
        # If no rules are defined, the condition is met only if required_count is 0.
        if not rule_results:
            return required_count <= 0

        # If required_count is 0 or less, the condition is always met.
        if required_count <= 0:
            return True

        satisfied_count = 0
        for rule_series in rule_results:
            # Check if the asset exists in this rule's results and if the result is True
            if asset_code in rule_series.index:
                result = rule_series.loc[asset_code]
                # Ensure we only count explicit True, handling potential NaNs
                if isinstance(result, (bool, np.bool_)) and result is True:
                    satisfied_count += 1

            # Optimization: If we've already met the required count, no need to check further.
            if satisfied_count >= required_count:
                return True

        # After checking all rules, compare the count
        return satisfied_count >= required_count

    # --- NEW: Reusable function to execute closing a position ---
    def __execute_close_position__(self, context: CtaContext, code: str, current_qty: float) -> bool:
        """
        Executes the closing of a position for a given asset code.
        Handles price fetching, value/commission calculation, equity update, and trade execution.

        Args:
            context: The CtaContext object.
            code: The asset code to close.
            current_qty: The current quantity held (used for value calculation).

        Returns:
            bool: True if the close order was successfully placed or position was already zero, False otherwise.
        """
        current_time_str = f"[{context.stra_get_date()} {context.stra_get_time():06d}]"

        if current_qty == 0:
            print_log(f"{current_time_str} [平仓执行] {code}: 当前持仓已为 0，无需平仓。")
            return True # Consider it successful as the state is already achieved

        print_log(f"{current_time_str} [平仓执行] {code}: 准备平仓当前持有数量 {current_qty}...")

        close_price = context.stra_get_price(code)
        if close_price <= 0:
            print_log(f"{current_time_str} [平仓错误] {code}: 获取平仓价格失败 ({close_price})，无法执行平仓。")
            return False

        # Calculate nominal value and commission
        # Use abs(current_qty) in case of short positions later, value should be positive
        close_value = self._calculate_nominal_value(context, code, abs(current_qty), close_price)
        if close_value == 0 and current_qty != 0:
             print_log(f"{current_time_str} [平仓错误] {code}: 计算平仓名义价值为0 (可能缺少品种信息)，无法执行平仓。")
             return False

        commission = close_value * self.__commission_rate__

        # Update nominal equity (Assuming closing increases equity available)
        # This simple model adds the closed value minus commission back to equity
        previous_equity = self.__nominal_equity
        self.__nominal_equity += (close_value - commission) # RESTORED: Optimistic update
        # Log reflects equity *after* optimistic update, but before sync confirmation
        print_log(f"{current_time_str} [平仓执行] {code}: 名义价值={close_value:.2f}, 手续费={commission:.2f}. 逻辑余额(乐观更新后): {self.__nominal_equity:.2f} (前: {previous_equity:.2f})")

        # Execute the closing trade
        try:
            # --- 添加显眼日志 ---
            print_log(f"########## [仓位调整] {code}: 尝试发送平仓指令 (设置目标仓位为 0) ##########")
            context.stra_set_position(code, 0)
            # --- REVISED: Update intended position with details ---
            self.__intended_positions__[code] = {
                'quantity': 0.0,
                'price': close_price, # Price used for this action
                'commission': commission,
                'cost_or_value': close_value, # Value gained
                'action': 'close'
            }
            print_log(f"{current_time_str} [平仓执行] {code}: 发送平仓指令, 更新逻辑仓位详情: {self.__intended_positions__[code]}")
            return True
        except Exception as e:
            print_log(f"{current_time_str} [平仓错误] {code}: 发送平仓指令时发生异常: {e}")
            # Consider reverting equity change if execution fails, though complicates state
            # self.__nominal_equity = previous_equity # Optional rollback
            return False

    # --- Method to process immediate sell triggers (uses the helper) ---
    def __process_immediate_sells__(self, context: CtaContext, sell_rule_results: List[pd.Series]):
        """
        Checks sell rules for currently held positions and executes sells immediately if conditions are met.
        This is called on every calculation cycle (every bar).
        Uses the __execute_close_position__ helper function.

        Returns:
            set: A set of asset codes where close orders were successfully placed in this cycle.
        """
        current_time_str = f"[{context.stra_get_date()} {context.stra_get_time():06d}]"
        print_log(f"{current_time_str} [即时卖出检查] 开始检查当前持仓的卖出触发条件...")

        current_positions = context.stra_get_all_position()
        if not current_positions:
            print_log(f"{current_time_str} [即时卖出检查] 当前无持仓，无需检查。")
            return set() # Return empty set

        codes_sold_this_cycle = set()

        # Iterate over a copy of keys to avoid issues if modifying positions during iteration
        for code in list(current_positions.keys()):
            current_qty = current_positions.get(code, 0)
            if current_qty == 0: continue # Skip if position is already zero

            # Check if this asset meets the sell conditions
            # Ensure sell_rule_results is not empty before checking
            if not sell_rule_results: # No sell rules defined
                 # print_log(f"{current_time_str} [即时卖出检查] {code}: 未定义卖出规则，跳过检查。")
                 continue

            should_sell = self.__check_rules_for_asset__(sell_rule_results, self.__sell_rules_required_count__, code)

            if should_sell:
                print_log(f"{current_time_str} [即时卖出触发] {code}: 满足卖出规则 (需要 {self.__sell_rules_required_count__} 条)，尝试执行平仓...")
                # Call the reusable closing function
                closed_successfully = self.__execute_close_position__(context, code, current_qty)
                if closed_successfully:
                    codes_sold_this_cycle.add(code)
                    # If closed successfully, update local copy of positions for consistency in this loop
                    # though get_all_position in next call will be the source of truth
                    if code in current_positions: del current_positions[code]
                # else: The error is logged within __execute_close_position__

        if codes_sold_this_cycle:
            print_log(f"{current_time_str} [即时卖出检查] 本轮成功发送即时平仓指令的品种: {codes_sold_this_cycle}")
        else:
            # Check if there were positions to begin with
            if any(qty != 0 for qty in context.stra_get_all_position().values()): # Check context again for original state
                print_log(f"{current_time_str} [即时卖出检查] 本轮无持仓触发即时卖出条件或平仓执行失败。")
            # else: No positions initially, already logged

        return codes_sold_this_cycle # Return the set of codes closed


    # --- Keep and Update __rebalance_portfolio__ (will also use __execute_close_position__) ---
    def __rebalance_portfolio__(self, context: CtaContext, eval_results: Dict[str, Any], actual_engine_positions: Dict[str, float]):
        """
        Performs the full portfolio rebalancing logic.
        Relies on __synchronize_state_with_engine__ having run first.

        Args:
            context: The CtaContext object.
            eval_results: Dictionary containing evaluation results ('scores', 'buy_rule_results', 'sell_rule_results').
            actual_engine_positions: Dictionary containing the actual positions from the engine.
        """
        scores = eval_results.get('scores', pd.Series(dtype=float)) # Safely get scores
        current_time_str = f"[{context.stra_get_date()} {context.stra_get_time():06d}]" # Define for logging
        print_log(f"{current_time_str} [调仓执行] === 开始调仓，当前逻辑余额 (同步后): {self.__nominal_equity:.2f} ===")
        COMMISSION_RATE = self.__commission_rate__
        SAFETY_FACTOR = 0.95

        # --- Step 1: Determine Initial Candidates based on Scores/TopN ---
        initial_candidates = set()
        ascending_sort = self.__order_by_config__.get('sort_direction', 'descending') == 'ascending'
        valid_scores = scores.dropna().sort_values(ascending=ascending_sort)
        if not valid_scores.empty:
            initial_candidates = set(valid_scores.head(self.__top_n__).index)
        print_log(f"{current_time_str} [调仓执行] 基于TopN得分的初始候选: {initial_candidates}")

        # --- Step 2: Filter Initial Candidates with Buy/Sell Rules ---
        final_buy_targets = set()
        buy_rule_results = eval_results.get('buy_rule_results', []) # Get buy rule results
        sell_rule_results = eval_results.get('sell_rule_results', []) # Get sell rule results

        if not initial_candidates:
            print_log("[调仓执行] 初始候选列表为空，无需检查买卖规则。")
        elif not self.__buy_rules_formulas__ and not self.__sell_rules_formulas__:
             print_log("[调仓执行] 未定义买入和卖出规则，将使用所有初始候选作为最终目标。")
             final_buy_targets = initial_candidates # Use all candidates if no rules
        else:
            log_msg = f"[调仓执行] 开始检查 {len(initial_candidates)} 个初始候选"
            if self.__buy_rules_formulas__:
                log_msg += f"的买入规则 (需满足 {self.__buy_rules_required_count__} 条)"
            if self.__sell_rules_formulas__:
                 if self.__buy_rules_formulas__: log_msg += " 和"
                 else: log_msg += "的" # If only sell rules are checked (unlikely but possible)
                 log_msg += f"卖出规则 (需不满足 {self.__sell_rules_required_count__} 条)"
            log_msg += "..."
            print_log(log_msg)
            for code in initial_candidates:
                meets_buy_rules = True
                if self.__buy_rules_formulas__ and self.__buy_rules_required_count__ > 0:
                    meets_buy_rules = self.__check_rules_for_asset__(buy_rule_results, self.__buy_rules_required_count__, code)
                meets_sell_rules = False
                if self.__sell_rules_formulas__ and self.__sell_rules_required_count__ > 0:
                    meets_sell_rules = self.__check_rules_for_asset__(sell_rule_results, self.__sell_rules_required_count__, code)
                if meets_buy_rules and not meets_sell_rules:
                    final_buy_targets.add(code)
            print_log(f"[调仓执行] 买卖规则检查完成。最终买入目标 (满足买入且不满足卖出): {final_buy_targets}")

        # --- Step 3: Determine Final Target Codes (what we want to hold) ---
        final_target_codes = final_buy_targets
        print_log(f"[调仓执行] 最终目标持有品种: {final_target_codes}")

        # --- Step 4: Compare Actual vs Target, Identify Actions ---
        # Use the actual positions reported by the engine for decision making
        print_log(f"{current_time_str} [调仓执行] 用于决策的引擎实际持仓 (同步时获取): {actual_engine_positions}")

        # --- Call the check function with actual engine positions ---
        codes_to_close, codes_to_open = self.__check_positions__(final_target_codes, actual_engine_positions)

        print_log(f"{current_time_str} [调仓执行] 识别需平仓品种 (基于是否仍在目标列表): {codes_to_close}")
        print_log(f"{current_time_str} [调仓执行] 识别需新开仓品种: {codes_to_open}")

        # --- Execute Closing Trades for codes_to_close (REVERTED: Always close if not in target) ---
        closed_successfully_count = 0
        if codes_to_close:
            print_log(f"{current_time_str} [调仓执行] --- 开始执行平仓指令 (因不在最终目标列表, {len(codes_to_close)}个) ---")
            for code in codes_to_close:
                # Retrieve the quantity from the *logical* intended positions for execution
                current_logical_qty = self.__intended_positions__.get(code, {}).get('quantity', 0.0)
                if abs(current_logical_qty) > 1e-6: # Check logical quantity before executing close
                    print_log(f"{current_time_str} [调仓执行] {code}: 执行平仓 (目标不再持有). 使用逻辑数量: {current_logical_qty:.2f}") # Log logical quantity
                    # Pass the logical quantity to the execution function
                    closed = self.__execute_close_position__(context, code, current_logical_qty)
                    if closed:
                        closed_successfully_count += 1
                else:
                    # Log if identified for closing based on actual pos, but logical pos is already zero
                    print_log(f"{current_time_str} [调仓执行] {code}: 识别为需平仓(基于实际持仓)，但逻辑仓位已为 0，跳过执行。")
            print_log(f"{current_time_str} [调仓执行] --- 平仓指令发送完成 (成功 {closed_successfully_count}/{len(codes_to_close)}) ---")


        # --- Step 5: Calculate Target Position Sizes for *NEW* Positions (codes_to_open) ---
        print_log("[调仓执行] --- 开始计算新开仓品种的目标仓位数量 ---")
        target_positions_to_open = {} # Dictionary for *new* positions only
        num_target_codes_to_open = len(codes_to_open)
        if num_target_codes_to_open > 0 and self.__nominal_equity > 0:
            
            effective_equity = self.__nominal_equity * SAFETY_FACTOR

            # --- REVERT START: Revert denominator logic --- 
            # Original logic: Always use the number of codes to open as divisor
            capital_divisor = num_target_codes_to_open
            # Removed: if self.__sell_rules_formulas__: ... else: ... logic
            
            # Ensure divisor is not zero (though num_target_codes_to_open is checked > 0 above)
            capital_per_position = 0
            if capital_divisor > 0: 
                capital_per_position = effective_equity / capital_divisor
                print_log(f"[资金分配] 可用逻辑余额(乘以安全系数 {SAFETY_FACTOR}): {effective_equity:.2f}")
                print_log(f"[资金分配] 平均分配给 {capital_divisor} 个新开仓目标, 每个约 {capital_per_position:.2f}")
            else:
                # This case should technically not happen due to the initial check,
                # but keeping for robustness / logging
                 print_log(f"[资金分配] 警告: 资金分配除数为 0 (num_to_open={num_target_codes_to_open})，无法分配资金。")
            # --- REVERT END ---
            

            for code in codes_to_open: # Iterate ONLY over codes to open
                available_capital = capital_per_position # Use the calculated capital per position
                if available_capital <= 0: # Skip if no capital allocated
                    print_log(f"[仓位计算] {code}: 分配到的资金不足 ({available_capital:.2f})，无法计算目标仓位。")
                    continue
                print("3")
                price = context.stra_get_price(code)
                print_log(f"[价格调试] {code}: stra_get_price()返回价格 = {price}")
                
                # 如果价格获取失败，尝试从K线数据获取
                if price <= 0:
                    print_log(f"[价格调试] {code}: 价格获取失败，尝试从K线数据获取...")
                    try:
                        bars = context.stra_get_bars(code, self.__period__, 1)
                        if bars is not None and len(bars.closes) > 0:
                            price = bars.closes[-1]
                            print_log(f"[价格调试] {code}: 从K线获取价格 = {price}")
                        else:
                            print_log(f"[价格调试] {code}: K线数据也无法获取")
                    except Exception as e:
                        print_log(f"[价格调试] {code}: K线获取异常: {e}")
                
                if price <= 0:
                    print_log(f"[仓位计算] {code}: 获取价格失败或无效 ({price})，无法计算目标仓位。")
                    continue
                
                target_units = 0
                is_stk = '.STK.' in code or '.ETF.' in code
                trade_unit = 100 if is_stk else 1
                pInfo = context.stra_get_comminfo(code)
                
                if is_stk:
                    if price > 0:
                        units_for_capital = math.floor(available_capital / price)
                        target_units = math.floor(units_for_capital / trade_unit) * trade_unit
                    else:
                        target_units = 0
                else: # Futures or Crypto
                    # 检查是否为虚拟货币，代码包含 .CPT.
                    is_crypto = '.CPT.' in code
                    
                    if is_crypto:
                        # 虚拟货币：直接用价格计算，支持小数持仓
                        print_log(f"[计算调试] {code}: 进入虚拟货币计算分支")
                        print_log(f"[计算调试] {code}: available_capital = {available_capital}")
                        print_log(f"[计算调试] {code}: price = {price}")
                        
                        if price > 0:
                            target_units_raw = available_capital / price
                            print_log(f"[计算调试] {code}: target_units_raw = {available_capital} / {price} = {target_units_raw}")
                            
                            # 保留足够精度，避免过小的持仓
                            if target_units_raw < 0.001:  # 最小持仓阈值
                                target_units = 0
                                print_log(f"[计算调试] {code}: target_units_raw {target_units_raw} < 0.001，设置为0")
                            else:
                                # 保留6位小数精度
                                target_units = round(target_units_raw, 6)
                                print_log(f"[计算调试] {code}: target_units = round({target_units_raw}, 6) = {target_units}")
                        else:
                            target_units = 0
                            print_log(f"[计算调试] {code}: price <= 0，设置target_units = 0")
                        print_log(f"[仓位计算] {code}: 虚拟货币计算 - 可用资金={available_capital:.2f}, 价格={price:.6f}, 目标数量={target_units:.6f}")
                    else:
                        # 期货：使用保证金计算
                        if pInfo and pInfo.volscale > 0 and price > 0:
                            margin_rate = getattr(pInfo, 'marginrate', self.__margin_rate__)
                            margin_per_unit = pInfo.volscale * price * margin_rate
                            target_units = math.floor(available_capital / margin_per_unit) if margin_per_unit > 0 else 0
                        else:
                            print_log(f"[仓位计算] {code}: 无法获取品种信息或价格无效({price})或交易乘数无效({pInfo.volscale if pInfo else 'N/A'})，无法计算期货目标仓位。")
                            continue

                # 根据品种类型使用不同的判断条件
                is_crypto = code.startswith('CRYPTO.')
                min_threshold = 0.001 if is_crypto else 1  # 虚拟货币允许小数持仓
                
                if target_units >= min_threshold:
                    target_positions_to_open[code] = target_units
                    print_log(f"[仓位计算] {code}: 新开仓目标单位 {target_units}")
                else:
                    if is_crypto:
                        print_log(f"[仓位计算] {code}: 虚拟货币目标单位{target_units:.6f}小于最小阈值{min_threshold}，不设定开仓目标。")
                    else:
                        print_log(f"[仓位计算] {code}: 计算目标单位为 {target_units} 或负数，不设定开仓目标。")
        else:
            print_log("[仓位计算] 无需新开仓品种或逻辑余额不足，不计算开仓目标仓位。")

        # --- Step 6: Execute Opening Trades for *NEW* Target Positions ---
        print_log("[调仓执行] --- 开始执行新开仓指令 ---")
        final_set_codes = set() # Track codes where set_position was attempted
        cost_open_total = 0
        commission_open_total = 0

        for code, target_units in target_positions_to_open.items():
            price = context.stra_get_price(code)
            if price <= 0:
                print_log(f"[调仓执行] {code}: 获取价格失败，无法开仓目标 {target_units}")
                continue

            cost = self._calculate_nominal_value(context, code, target_units, price)
            commission = cost * COMMISSION_RATE
            previous_equity = self.__nominal_equity # For logging only
            self.__nominal_equity -= (cost + commission) # RESTORED: Optimistic update
            cost_open_total += cost
            commission_open_total += commission
            # Log reflects equity *after* optimistic update, but before sync confirmation
            print_log(f"[逻辑余额] {code}: 准备开仓 {target_units} @ {price:.2f}, Cost={cost:.2f}, Comm={commission:.2f}. 逻辑余额(乐观更新后): {self.__nominal_equity:.2f} (前: {previous_equity:.2f})")

            print_log(f"[调仓执行] {code}: 设置目标仓位 {target_units} (开新仓)" )
            try:
                print_log(f"########## [仓位调整] {code}: 尝试发送开仓指令 (设置目标仓位为 {target_units}) ##########")
                context.stra_set_position(code, target_units)
                # --- REVISED: Update intended position with details ---
                self.__intended_positions__[code] = {
                    'quantity': target_units,
                    'price': price, # Price used for this action
                    'commission': commission,
                    'cost_or_value': cost, # Cost incurred
                    'action': 'open'
                }
                current_time_str_open_log = f"[{context.stra_get_date()} {context.stra_get_time():06d}]"
                print_log(f"{current_time_str_open_log} [开仓执行] {code}: 发送开仓指令 (目标 {target_units}), 更新逻辑仓位详情: {self.__intended_positions__[code]}")
                final_set_codes.add(code)
            except Exception as e:
                print_log(f"[调仓执行] {code}: 设置目标仓位 {target_units} 时发生异常: {e}")
                # If open fails, intended_position won't be updated, sync will handle it next cycle

        # --- Final Logging ---
        print_log(f"[调仓执行] --- 调仓指令发送完成 ---")
        print_log(f"[调仓执行] 最终尝试设置新开仓目标的品种: {final_set_codes}")
        print_log(f"[逻辑余额] 新开仓名义成本(近似): {cost_open_total:.2f}, 手续费(近似): {commission_open_total:.2f}")
        # Log equity *after* sending orders and optimistic updates
        print_log(f"[逻辑余额] 调仓指令发送后逻辑余额(乐观更新后): {self.__nominal_equity:.2f}")

        self.__last_rebalance_date__ = context.stra_get_date()
        print_log(f"[调仓执行] === 调仓结束 ===")


    # --- REVISED: Function to compare actual engine positions vs target positions ---
    def __check_positions__(self, final_target_codes: set, actual_engine_positions: Dict[str, float]) -> Tuple[set, set]:
        """
        Compares the actual positions reported by the engine against the
        final target codes for the current rebalance cycle to identify changes.

        Args:
            final_target_codes: Set of asset codes that *should* be held after this rebalance cycle.
            actual_engine_positions: Dictionary holding the actual position quantities reported
                                        by the engine at the start of this rebalance cycle.
                                        {code: quantity}

        Returns:
            Tuple[set, set]: codes_to_close, codes_to_open
        """
        codes_to_close = set()
        codes_to_open = set()

        # Get all codes currently held according to the engine
        currently_held_codes = set(actual_engine_positions.keys())

        # Check codes currently held by the engine to see if they need closing
        for code in currently_held_codes:
            actual_qty = actual_engine_positions.get(code, 0.0)
            # If actually held (qty != 0) and not a target anymore, close it.
            if abs(actual_qty) > 1e-6 and code not in final_target_codes:
                codes_to_close.add(code)
            # If actually held (qty != 0) and IS still a target, do nothing (keep holding).
            # If actual qty is 0, do nothing in this loop regarding closing.

        # Check codes that should be targets to see if they need opening
        for code in final_target_codes:
            # Get current actual qty, defaulting to 0 if code wasn't in the engine report
            actual_qty = actual_engine_positions.get(code, 0.0)
            # If it's a target AND actually not held (qty == 0), open it.
            if abs(actual_qty) < 1e-6:
                codes_to_open.add(code)
            # If it's a target AND actually held (qty != 0), do nothing (already holding).

        return codes_to_close, codes_to_open
    # --- End of revised __check_positions__ ---


    # --- Updated on_calculate to call evaluation and immediate sells ---
    def on_calculate(self, context: CtaContext):
        """
        Main calculation logic: Evaluates factors/rules, processes immediate sells,
        and triggers full rebalance periodically.
        """

        print("------------------on calculate---------------")

        cur_date = context.stra_get_date()
        cur_time = context.stra_get_time()
        current_time_str = f"[{cur_date} {cur_time:06d}]"

        # --- Step 1: Evaluate all factors and rules (Every Bar) ---
        eval_results = self.__evaluate_factors_and_rules__(context)

        # Check if evaluation itself failed critically
        if eval_results is None:
             print_log(f"{current_time_str} 严重错误: 因子/规则评估未能完成，跳过本轮计算。")
             # 检查是否是因为数据不足导致的失败
             data_available = False
             for code in self.__codes__:
                 try:
                     bars = context.stra_get_bars(code, convert_period1(self.__period__), 1)
                     if bars is not None and len(bars) > 0:
                         data_available = True
                         break
                 except:
                     continue

             if not data_available:
                 # 使用更明确的日志格式，确保这些日志能被注意到
                 print_log("="*80)
                 print_log(f"{current_time_str} [数据结束] 所有品种均无更多数据可用，回测到此结束。")
                 print_log(f"{current_time_str} [数据结束] 最后一个交易日: {cur_date}")
                 print_log(f"{current_time_str} [数据结束] 如果这不是预期的结束时间，请检查通达信数据是否更新。")
                 print_log("="*80)
             return

        # --- Step 2: Synchronize state BEFORE trading actions ---
        # This corrects __nominal_equity and returns the actual positions from the engine
        actual_engine_positions = self.__synchronize_state_with_engine__(context)

        # --- Step 3: Process Immediate Sells based on rules (Every Bar) ---
        # Note: This uses the synchronized equity state implicitly if needed
        codes_closed_in_immediate_sell = set() # Still useful to track for logging perhaps
        sell_rule_results = eval_results.get('sell_rule_results')
        if sell_rule_results is not None:
             codes_closed_in_immediate_sell = self.__process_immediate_sells__(context, sell_rule_results)
             if codes_closed_in_immediate_sell:
                 print_log(f"{current_time_str} [on_calculate] 即时卖出已尝试平仓: {codes_closed_in_immediate_sell}")
        else:
             print_log(f"{current_time_str} 警告: 评估结果中缺少 'sell_rule_results'，无法执行即时卖出检查。")


        # --- Step 4: Trigger Full Rebalance Periodically ---
        if self.__is_rebalance_time__(context):
            print_log(f"{current_time_str} === 触发完整调仓周期 ===")

            # Check if scores are valid before proceeding with full rebalance
            scores = eval_results.get('scores')
            if scores is None or scores.isna().all():
                 print_log(f"{current_time_str} 警告: 未能计算出有效排序得分，跳过本次完整调仓。")
                 self.__last_rebalance_date__ = cur_date # Update date even if skipping rebalance
                 return

            # Call the full rebalancing logic
            # Pass evaluation results AND the actual engine positions obtained from sync
            self.__rebalance_portfolio__(context, eval_results, actual_engine_positions)
            # self.__last_scores__ = scores # Store the latest scores from this rebalance cycle - Optional

            # 更新调仓时间戳
            if self.__rebalance_interval__ in ['5m', '15m', '1h']:
                # 对于分钟级调仓，更新时间戳
                # cur_datetime = datetime.datetime.strptime(f"{cur_date}{cur_time:06d}", "%Y%m%d%H%M%S")
                # self.__last_rebalance_timestamp__ = int(cur_datetime.timestamp() // 60)
                self.__get_timestamp_in_minutes__(cur_date, cur_time)
            else:
                # 对于日级及以上调仓，更新日期
                self.__last_rebalance_date__ = cur_date

        else:
            # Optional: Log that it's not full rebalance time
            # print_log(f"{current_time_str} 非完整调仓时间，仅执行同步和即时卖出检查。")
            pass

    def on_tick(self, context: CtaContext, stdCode:str, newTick:dict):
        print(f"on_tick: {stdCode}, {newTick}")
        return
    # ... (Keep other callbacks: on_calculate_done, on_backtest_end, on_tick, on_bar) ...
