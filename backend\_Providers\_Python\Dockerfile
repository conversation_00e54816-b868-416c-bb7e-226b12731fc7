    # Use an official Python runtime as a parent image
    FROM python:3.9-slim

    # Set the working directory in the container
    WORKDIR /app

    # Copy the requirements file into the container at /app
    # (We copy only requirements first to leverage Docker cache)
    COPY requirements.txt .

    # Install any needed packages specified in requirements.txt
    # --no-cache-dir reduces image size
    RUN pip install --no-cache-dir -r requirements.txt

    # Copy the rest of the application code into the container at /app
    # Add a .dockerignore file to exclude venv, __pycache__, etc.
    COPY . .

    # Default command (will be overridden by docker-compose for specific services)
    # Expose ports if needed (docker-compose handles mapping)
    # EXPOSE 5000
    # EXPOSE 5001
    CMD ["python", "app.py"]