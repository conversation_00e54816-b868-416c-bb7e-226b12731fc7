from pytdx.hq import TdxHq_API

# 创建 API 对象
api = TdxHq_API()

# 连接服务器
if api.connect('110.41.4.4', 7709):
    print("连接成功！")
    # 做一些操作

    # 获取单只股票的实时行情
    stock_data = api.get_security_quotes(1, '513400')
    print(stock_data)

    # 获取日 K 线数据
    k_data = api.get_security_bars(9, 1, 'PG2504', 0, 10)
    df = api.to_df(k_data)
    print(df)

    # 获取最近 10 条分笔成交数据
    transaction_data = api.get_transaction_data(1, 'PG2504', 0, 10)
    print(api.to_df(transaction_data))

    api.disconnect()  # 关闭连接
else:
    print("连接失败！")