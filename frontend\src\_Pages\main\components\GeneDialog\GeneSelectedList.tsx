import React from 'react';
import { Card, List, Button, Typography, Space, Divider } from 'antd';
import { DeleteOutlined, AimOutlined } from '@ant-design/icons';
import { useAtom } from 'jotai';
import { geneConfigAtom } from '../../models/geneState';

const { Text } = Typography;

/**
 * 已选基因配置列表组件
 * 显示已添加的基因配置
 */
const GeneSelectedList: React.FC = () => {
    const [geneConfig, setGeneConfig] = useAtom(geneConfigAtom);

    const handleDelete = () => {
        setGeneConfig(null);
    };

    const handleLocate = () => {
        // TODO: 实现基因定位功能
        console.log('定位基因配置:', geneConfig);
    };

    const renderParamValue = (param: any) => {
        if (typeof param === 'object') {
            return param.label || JSON.stringify(param);
        }
        return param;
    };

    return (
        <Card 
            title="已选配置" 
            size="small"
            bodyStyle={{ 
                padding: '8px', 
                height: 'calc(100% - 37px)', 
                display: 'flex',
                flexDirection: 'column'
            }}
            style={{ 
                height: '100%',
                border: '1px solid #8c8c8c',
                boxShadow: '0 0 4px rgba(0, 0, 0, 0.06)'
            }}
            extra={
                geneConfig && (
                    <Button 
                        type="text" 
                        danger 
                        icon={<DeleteOutlined />}
                        onClick={handleDelete}
                    >
                        删除
                    </Button>
                )
            }
        >
            <div style={{ flex: 1, overflow: 'auto', marginBottom: '12px' }}>
                {geneConfig ? (
                    <List
                        size="small"
                        header={
                            <div>
                                <Text strong>{geneConfig.indicator.name}</Text>
                            </div>
                        }
                        dataSource={Object.entries(geneConfig.params)}
                        renderItem={([key, value]) => (
                            <List.Item>
                                <Space>
                                    <Text type="secondary">{key}:</Text>
                                    <Text>{renderParamValue(value)}</Text>
                                </Space>
                            </List.Item>
                        )}
                    />
                ) : (
                    <div style={{ textAlign: 'center', marginTop: '20px' }}>
                        暂无配置
                    </div>
                )}
            </div>
            {geneConfig && (
                <>
                    <Divider style={{ margin: '12px 0' }} />
                    <Button 
                        type="primary" 
                        icon={<AimOutlined />}
                        block
                        onClick={handleLocate}
                    >
                        基因定位
                    </Button>
                </>
            )}
        </Card>
    );
};

export default GeneSelectedList; 