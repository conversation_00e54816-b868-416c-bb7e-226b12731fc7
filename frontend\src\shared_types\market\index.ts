import { Indicator } from "@debut/indicators/lib/src/trendlines";
import { IndicatorLineConfig } from "../indicator";

/**
 * 市场类型
 */
export enum MarketType {
  ETF = 'ETF',          // 指数基金
  STOCK = 'STOCK',      // 股票
  STOCK_US = 'STOCK_US', // 美股
  FUTURE = 'FUTURE',    // 期货
  CRYPTO = 'CRYPTO',    // 加密货币
  INDEX = 'INDEX'       // 指数
}



// 指标样式配置
export interface IndicatorStyle {
  name: string;          // 显示名称，如 'MACD' 中的 'DIF'
  color?: string;        // 颜色 (支持 HEX 或 RGBA 格式)
  style?: 'line' | 'histogram' | 'area' | 'dots';  // 显示样式
  lineWidth?: number;    // 线宽
  visible?: boolean;     // 是否显示
}

// 指标配置类型
export interface IndicatorConfig {
  name: string;         // 指标名称
  type: IndicatorType;  // 指标类型
  values: Record<string, number>;  // 指标计算值
  styles: Record<string, IndicatorStyle>;  // 样式配置
  params: Record<string, number>;  // 指标参数
  position: 'main' | 'sub';  // 显示位置
}

/**
 * 交易所类型
 */
export enum ExchangeType {
  // 中国股票交易所
  SSE = 'SSE',         // 上海证券交易所
  SZSE = 'SZSE',       // 深圳证券交易所
  BSE = 'BSE',         // 北京证券交易所

  // 美国股票交易所
  NASDAQ = 'NASDAQ',   // 纳斯达克
  NYSE = 'NYSE',       // 纽约证券交易所
  AMEX = 'AMEX',       // 美国股票交易所
  ARCA = 'ARCA',       // 纽约股票交易所
  BATS = 'BATS',       // 巴特斯全球市场
  IEXG = 'IEXG',       // 投资者交易所
  ITCH = 'ITCH',       // ITCH 交易平台
  
  // 中国期货交易所
  SHFE = 'SHFE',       // 上海期货交易所
  DCE = 'DCE',         // 大连商品交易所
  CZCE = 'CZCE',       // 郑州商品交易所
  CFFEX = 'CFFEX',     // 中国金融期货交易所
  INE = 'INE',         // 上海国际能源交易中心
  
  // 加密货币交易所
  BINANCE = 'BINANCE', // 币安
  OKX = 'OKX',         // OKX
  HUOBI = 'HUOBI',     // 火币
  BYBIT = 'BYBIT',     // BYBIT
  GATE = 'GATE',       // GATE.IO
}

/**
 * 交易所信息
 */
export interface ExchangeInfo {
  code: ExchangeType    // 交易所代码
  name: string          // 交易所名称
  market: MarketType    // 市场类型
}

/**
 * 交易所列表
 */
export const EXCHANGE_LIST: ExchangeInfo[] = [
  { code: ExchangeType.SSE, name: '上海证券交易所', market: MarketType.STOCK },
  { code: ExchangeType.SZSE, name: '深圳证券交易所', market: MarketType.STOCK },
  { code: ExchangeType.BSE, name: '北京证券交易所', market: MarketType.STOCK },

  { code: ExchangeType.NASDAQ, name: '纳斯达克', market: MarketType.STOCK_US },
  { code: ExchangeType.NYSE, name: '纽约证券交易所', market: MarketType.STOCK_US },
  { code: ExchangeType.AMEX, name: '美国股票交易所', market: MarketType.STOCK_US },
  { code: ExchangeType.ARCA, name: '纽约股票交易所', market: MarketType.STOCK_US },
  { code: ExchangeType.BATS, name: '巴特斯全球市场', market: MarketType.STOCK_US },
  { code: ExchangeType.IEXG, name: '投资者交易所', market: MarketType.STOCK_US },
  { code: ExchangeType.ITCH, name: 'ITCH 交易平台', market: MarketType.STOCK_US },
  
  { code: ExchangeType.SHFE, name: '上海期货交易所', market: MarketType.FUTURE },
  { code: ExchangeType.DCE, name: '大连商品交易所', market: MarketType.FUTURE },
  { code: ExchangeType.CZCE, name: '郑州商品交易所', market: MarketType.FUTURE },
  { code: ExchangeType.CFFEX, name: '中国金融期货交易所', market: MarketType.FUTURE },
  { code: ExchangeType.INE, name: '上海国际能源交易中心', market: MarketType.FUTURE },
  
  { code: ExchangeType.BINANCE, name: 'Binance', market: MarketType.CRYPTO },
  { code: ExchangeType.OKX, name: 'OKX', market: MarketType.CRYPTO },
  { code: ExchangeType.HUOBI, name: 'Huobi', market: MarketType.CRYPTO },
  { code: ExchangeType.BYBIT, name: 'Bybit', market: MarketType.CRYPTO },
  { code: ExchangeType.GATE, name: 'Gate.io', market: MarketType.CRYPTO },
];

/**
 * 交易品种信息
 */
export interface Symbol {
  code: string          // 代码
  name: string          // 名称
  market: MarketType    // 市场类型
  exchange: ExchangeType // 交易所
}

/**
 * K线周期
 */
export enum KLineInterval {
  MIN1 = '1m',    // 1分钟
  MIN5 = '5m',    // 5分钟
  MIN15 = '15m',  // 15分钟
  MIN30 = '30m',  // 30分钟
  HOUR1 = '1h',   // 1小时
  HOUR4 = '4h',
  DAY1 = '1D',    // 日线
  WEEK1 = '1W',    // 周线
  //MONTH1 = '1M'   // 月线
}

/**
 * K线数据
 */
export interface KLineData {
  time: number          // 时间戳，单位秒，注意不是毫秒
  open: number         // 开盘价
  high: number         // 最高价
  low: number          // 最低价
  close: number        // 收盘价
  volume: number       // 成交量
}

export interface KLine {
  id: string;
  data: KLineData[];
  symbol: Symbol;
  period: KLineInterval;
}

export interface SignalConfig {
  name: string;                 // 信号名称
  parameters: Record<string, any>;  // 信号参数
}

/**
 * 基因类型
 */
export enum GeneType {
  PAST = 'PAST',       // 过去指标
  FUTURE = 'FUTURE'    // 未来指标
}

/**
 * 条件运算符
 */
export enum Operator {
  GT = '>',           // 大于
  GTE = '>=',         // 大于等于
  LT = '<',           // 小于
  LTE = '<=',         // 小于等于
  EQ = '=',           // 等于
  NEQ = '!='          // 不等于
}

/**
 * 基因条件
 */
export interface GeneCondition {
  indicator: Indicator           // 指标
  operator: Operator            // 运算符
  value: number                 // 比较值
  params: Record<string, any>   // 指标参数
}

/**
 * 基因组合
 */
export interface GeneCombination {
  id: string                    // 组合ID
  name: string                  // 组合名称
  condition: GeneCondition      // 条件（目前只允许一个）
}

/**
 * 定位结果
 */
export interface LocationResult {
  time: number                  // 时间戳
  price: number                 // 价格
  indicatorValue: number        // 指标值
}

// 指标类型枚举
export enum IndicatorType {
  
  MACD = 'MACD',
  KDJ = 'KDJ',
  RSI = 'RSI',
  VOL = 'VOL',    // 成交量
  BOLL = 'BOLL',  // 布林带
  MA = 'MA',      // 移动平均线
  EMA = 'EMA',    // 指数移动平均
  SWMA = 'SWMA',    // 加权移动平均再平滑
  WMA = 'WMA',
  SHAPE = 'SHAPE'    // 虚拟形态指标
}

// 定义主图指标类型
export const MAIN_INDICATORS = [
  IndicatorType.MA,
  IndicatorType.EMA,
  IndicatorType.WMA,
  IndicatorType.SWMA,
  IndicatorType.BOLL,
];

// 定义副图指标类型
export const SUB_INDICATORS = [
  IndicatorType.MACD,
  IndicatorType.KDJ,
  IndicatorType.RSI,
  IndicatorType.VOL,
]; 

export interface ChartConfig {
  symbol: Symbol;
  period: KLineInterval;
  indicators: {
    main: Array<{
      type: IndicatorType;
      params: Record<string, any>;
      lines: IndicatorLineConfig[];
      }>;
    sub: Array<{
      type: IndicatorType;
      params: Record<string, any>;
      lines: IndicatorLineConfig[];
    }>;
  };
}