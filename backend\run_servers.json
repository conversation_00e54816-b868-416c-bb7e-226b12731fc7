[{"name": "strategy-server", "script": "./_Providers/_Python/strategy/strategy_server.py", "exec_mode": "fork", "cwd": "D:/projects/quantquart2", "autorestart": false, "windowsHide": true, "interpreter": "D:/projects/quantquart2/python_venv/Scripts/python.exe"}, {"name": "tdx-server", "script": "./_Providers/_Python/tdxserver.py", "exec_mode": "fork", "cwd": "D:/projects/quantquart2", "autorestart": false, "windowsHide": true, "interpreter": "D:/projects/quantquart2/python_venv/Scripts/python.exe"}, {"name": "python-server", "script": "./_Providers/_Python/app.py", "exec_mode": "fork", "cwd": "D:/projects/quantquart2", "autorestart": false, "windowsHide": true, "interpreter": "D:/projects/quantquart2/python_venv/Scripts/python.exe"}, {"name": "python-server", "script": "../utils/easytrader_ths/client.py", "exec_mode": "fork", "cwd": "D:/projects/quantquart2", "autorestart": false, "windowsHide": true, "interpreter": "D:/projects/quantquart2/python_venv/Scripts/python.exe"}, {"name": "unzip-monitor", "script": "./_Providers/_Python/unzip_monitor.py", "args": "../stockdata -i 60", "exec_mode": "fork", "autorestart": false, "windowsHide": true, "cwd": "D:/projects/quantquart2", "interpreter": "D:/projects/quantquart2/python_venv/Scripts/python.exe"}, {"name": "node-app", "script": "D:/projects/quantquart2/backend/index.js", "exec_mode": "fork", "autorestart": false, "cwd": "D:/projects/quantquart2/backend", "env": {"NODE_ENV": "development"}, "interpreter": "node", "windowsHide": true}]