# 数据引擎管理系统说明

## 系统架构

### 核心设计理念
- **单一数据引擎**: 整个系统只有一个中心数据引擎 (`live_DT`)
- **统一订阅管理**: 所有实盘项目的品种订阅都通过 `strategy_server.py` 统一管理
- **引用计数机制**: 每个品种维护引用计数器，确保只有真正需要的品种被订阅
- **智能重启机制**: 只有当 `contracts.json` 实际发生变化时才重启数据引擎
- **动态配置更新**: 当有新的实盘项目启动时，自动更新 `contracts.json` 并重启数据引擎
- **集中式管理**: 所有策略相关的主要业务都集中在 `strategy_server.py` 中完成

### 目录结构
```
backend/_Providers/_Python/strategy/
├── strategy_server.py              # 策略服务器 + 数据引擎管理
├── portfolio/
│   ├── live_DT/                    # 中心数据引擎
│   │   ├── runDT.py               # 数据引擎启动脚本
│   │   ├── dtcfg.yaml             # 数据引擎配置
│   │   ├── contracts.json         # 合约配置（动态生成）
│   │   ├── mdparsers.yaml         # 行情解析器配置
│   │   ├── logcfgdt.yaml          # 日志配置
│   │   ├── statemonitor.yaml      # 状态监控配置
│   │   └── README.md              # 详细说明
│   └── ...
├── common/                         # 公共配置文件
│   ├── stk_comms.json            # 股票品种配置
│   ├── holidays.json             # 节假日配置
│   └── stk_sessions.json         # 交易时段配置
├── test_data_engine_manager.py     # 测试脚本
├── data_engine_usage_example.py    # 使用示例
└── 数据引擎管理系统说明.md         # 本文档
```

## 核心组件

### 1. DataEngineManager 类
位于 `strategy_server.py` 中，负责：
- 管理实盘项目的品种订阅注册表
- 维护品种引用计数器 (`contract_ref_count`)
- 动态生成和更新 `contracts.json` 配置文件
- 控制数据引擎进程的启动、停止和重启
- 提供统一的API接口

### 2. 中心数据引擎 (live_DT)
- 基于 Wonder Trader 的 `WtDtEngine`
- 支持多种数据源（通达信、XTP、自定义扩展）
- 提供UDP广播和共享内存数据转发
- 自动生成和存储各周期K线数据

### 3. API接口
提供RESTful API供实盘项目调用：
- `POST /data_engine/subscribe` - 注册品种订阅
- `POST /data_engine/unsubscribe` - 取消品种订阅
- `POST /data_engine/restart` - 重启数据引擎
- `GET /data_engine/status` - 获取引擎状态
- `POST /data_engine/update_and_restart` - 一键更新并重启

## 工作流程

### 引用计数机制
```
项目A订阅: SSE.600036, SZSE.000001
引用计数: {"SSE.600036": 1, "SZSE.000001": 1}

项目B订阅: SSE.600036, SSE.600519
引用计数: {"SSE.600036": 2, "SZSE.000001": 1, "SSE.600519": 1}

项目A停止: SSE.600036, SZSE.000001
引用计数: {"SSE.600036": 1, "SSE.600519": 1}
// SZSE.000001 计数归零，从contracts.json中删除

最终contracts.json只包含: SSE.600036, SSE.600519
```

### 智能重启机制
```
场景1: 项目A订阅 SSE.600036, SZSE.000001
→ contracts.json从空变为有内容 → 需要重启 ✓

场景2: 项目B订阅 SSE.600036, SSE.600519
→ SSE.600036已存在，只新增SSE.600519 → 需要重启 ✓

场景3: 项目C订阅 SSE.600036
→ SSE.600036已存在，无新增品种 → 无需重启 ✗

场景4: 项目C退订
→ SSE.600036仍被其他项目使用 → 无需重启 ✗

场景5: 项目A退订
→ SZSE.000001引用计数归零被移除 → 需要重启 ✓
```

### 实盘项目启动流程
1. 实盘项目启动时，调用 `/data_engine/update_and_restart` 注册需要的合约
2. DataEngineManager 将订阅信息添加到注册表，同时增加合约引用计数
3. 检查操作前后的活跃合约列表是否发生变化
4. **只有当合约列表发生变化时才重启数据引擎**
5. DataEngineManager 基于引用计数生成 `contracts.json`（只包含计数>0的合约）
6. 停止旧的数据引擎进程，启动新的数据引擎进程
7. 新的数据引擎读取更新后的配置，开始订阅所有需要的合约

### 实盘项目停止流程
1. 实盘项目停止时，调用 `/data_engine/unsubscribe` 取消订阅
2. DataEngineManager 从注册表中移除该项目的订阅，同时减少合约引用计数
3. 引用计数归零的合约自动从订阅列表中移除
4. 检查操作前后的活跃合约列表是否发生变化
5. **只有当合约列表发生变化时才重启数据引擎**
6. 数据引擎重启后只订阅剩余有效的合约（引用计数>0）

### 一键操作流程
使用 `/data_engine/update_and_restart` 可以一次完成：
1. 注册/更新项目订阅
2. 更新配置文件
3. 重启数据引擎

## 配置文件说明

### contracts.json 结构
```json
{
  "SSE": {
    "600036": {
      "name": "招商银行",
      "code": "600036",
      "exchg": "SSE",
      "product": "STK",
      "maxlimitqty": 100,
      "maxmarketqty": 20
    }
  },
  "SZSE": {
    "000001": {
      "name": "平安银行",
      "code": "000001",
      "exchg": "SZSE",
      "product": "STK",
      "maxlimitqty": 100,
      "maxmarketqty": 20
    }
  }
}
```

### 合约代码格式
- 标准格式: `交易所.代码` (如 `SSE.600036`, `SZSE.000001`)
- 自动识别: 如果只提供代码，系统会根据代码规则自动判断交易所
  - 6开头 → SSE (上交所)
  - 0、3开头 → SZSE (深交所)

## 使用示例

### Python代码示例
```python
import requests

# 启动实盘项目并订阅数据
response = requests.post("http://localhost:5002/data_engine/update_and_restart", json={
    "project_id": "my_live_project",
    "contract_codes": ["SSE.600036", "SZSE.000001", "SSE.510300"]
})

# 检查结果
if response.json().get('success'):
    print("数据引擎启动成功")
else:
    print(f"启动失败: {response.json().get('error')}")
```

### 命令行测试
```bash
# 测试订阅
curl -X POST http://localhost:5002/data_engine/subscribe \
  -H "Content-Type: application/json" \
  -d '{"project_id": "test", "contract_codes": ["SSE.600036"]}'

# 检查状态
curl http://localhost:5002/data_engine/status
```

## 测试和调试

### 测试脚本
- `test_data_engine_manager.py` - 完整的API测试
- `test_reference_counting.py` - 引用计数机制专项测试
- `test_smart_restart.py` - 智能重启机制专项测试
- `data_engine_usage_example.py` - 使用示例和演示

### 运行测试
```bash
# 启动策略服务器
python strategy_server.py

# 在另一个终端运行测试
python test_data_engine_manager.py

# 测试引用计数机制
python test_reference_counting.py

# 测试智能重启机制
python test_smart_restart.py
```

### 日志查看
- 策略服务器日志: 控制台输出
- 数据引擎日志: `live_DT/DtLogs/` 目录下的日志文件

## 优势特点

1. **统一管理**: 所有实盘项目共享一个数据引擎，避免资源浪费
2. **引用计数**: 精确管理品种订阅，确保只订阅真正需要的数据
3. **智能重启**: 只有当合约列表实际变化时才重启，避免不必要的服务中断
4. **动态配置**: 支持运行时动态添加/删除订阅，无需手动配置
5. **自动重启**: 配置更新后自动重启数据引擎，确保配置生效
6. **容错处理**: 完善的错误处理和进程管理机制
7. **易于集成**: 提供简单的RESTful API，易于与其他系统集成
8. **可扩展性**: 支持多种数据源，可根据需要扩展
9. **资源优化**: 通过引用计数避免无效订阅，节省网络和计算资源
10. **服务稳定**: 智能重启机制减少不必要的服务中断，提高系统稳定性

## 注意事项

1. **进程管理**: 数据引擎作为子进程运行，重启时会有短暂的数据中断
2. **配置文件**: 确保 `common` 目录下的基础配置文件存在
3. **网络端口**: 数据引擎使用UDP端口9001和3997，确保端口可用
4. **权限要求**: 需要文件写入权限和网络访问权限
5. **资源消耗**: 订阅的合约数量会影响内存和网络使用量

## 扩展建议

1. **数据源扩展**: 可以添加更多的行情数据源解析器
2. **监控增强**: 可以添加更详细的性能监控和告警机制
3. **配置优化**: 可以根据实际使用情况优化缓存和存储配置
4. **集群支持**: 未来可以考虑支持多数据引擎集群部署
