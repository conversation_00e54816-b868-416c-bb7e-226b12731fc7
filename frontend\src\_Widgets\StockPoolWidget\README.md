# 股票池组件 (StockPoolWidget)

## 组件概述

StockPoolWidget 是一个用于管理和选择各种股票池、品种列表和板块的独立组件。支持系统定义和用户自定义的股票池，提供了分层级的组织结构和丰富的交互功能。

## 功能特性

- **多层级结构**：
  - 第一层级：系统/用户分类
  - 第二层级：不同类别（如 A股、美股、虚拟货币等）
  - 第三层级：具体的股票池列表（如沪深300、中证500等）

- **全面的股票池管理**：
  - 查看系统预定义的股票池
  - 创建、编辑和删除自定义股票池
  - 向股票池中添加或删除股票

- **股票选择与交互**：
  - 点击股票条目可直接加载到图表中
  - 支持按标签过滤和分类

## 界面设计

- 使用 Ant Design Pro 组件库实现专业、美观的界面
- 采用卡片 + 标签页的组合方式展示层级结构
- 紧凑设计，在有限空间展示丰富信息

## 组件结构

```
StockPoolWidget/
├── index.tsx           # 主组件文件
├── index.less          # 样式文件
└── README.md           # 说明文档
```

## 数据模型

### 股票项 (StockItem)
```typescript
interface StockItem {
  id: string;
  name: string;        // 显示名称，如"中国平安"
  symbol: string;      // 完整代码，如"SSE.STOCK.601318"
  code: string;        // 仅代码部分，如"601318"
  tags?: string[];     // 标签
  addedAt?: number;    // 添加时间
  lastViewed?: number; // 最后查看时间
}
```

### 股票分类 (StockCategory)
```typescript
interface StockCategory {
  id: string;
  name: string;
  description?: string;
  count: number;       // 该分类下的股票池数量
  type: 'system' | 'user';
  icon?: React.ReactNode;
}
```

### 股票池 (StockPool)
```typescript
interface StockPool {
  id: string;
  name: string;
  description?: string;
  items: StockItem[];  // 包含的股票列表
  categoryId: string;  // 所属分类ID
  isDefault?: boolean; // 是否为默认池
  createdAt: number;
  updatedAt: number;
}
```

## 交互流程

1. 用户首先在顶部选择"系统"或"用户"tab
2. 然后从第二层级的分类卡片中选择一个分类
3. 接着从该分类下的标签页中选择一个具体的股票池
4. 最后可以查看股票池详情，或选择其中的股票项

## 使用方式

```tsx
import StockPoolWidget from '@/_Widgets/StockPoolWidget';

// 在应用中使用
<StockPoolWidget />
``` 