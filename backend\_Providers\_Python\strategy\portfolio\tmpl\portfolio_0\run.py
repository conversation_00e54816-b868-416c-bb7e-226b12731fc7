#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实盘项目启动脚本模板
用于启动Wonder Trader引擎并运行多因子CTA策略
"""

import os
import sys
import yaml
import json
import time
import traceback
from wtpy import WtEngine, EngineType
from wtpy.apps import WtBtAnalyst
from wtpy.ExtModuleDefs import BaseExtDataLoader

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))

print(f"[Run] 当前工作目录: {os.getcwd()}")
print(f"[Run] 当前脚本目录: {current_dir}")

def find_backend_dir(start_dir, max_depth=10):
    """
    向上查找_Python目录
    
    Args:
        start_dir: 开始查找的目录
        max_depth: 最大查找深度，默认10层
    
    Returns:
        str: _Python目录的绝对路径，如果找不到返回None
    """
    current = start_dir
    for _ in range(max_depth):
        if os.path.basename(current) == 'backend':
            return current
        parent = os.path.dirname(current)
        if parent == current:  # 已经到达根目录
            break
        current = parent
    return None

def setup_python_import_path():
    """
    设置strategy模块的导入路径
    将_Python目录添加到sys.path，使得可以直接import strategy
    """
    backend_dir = find_backend_dir(current_dir)
    if not backend_dir:
        raise FileNotFoundError("找不到backend目录，无法设置strategy导入路径")
    
    python_dir = os.path.join(backend_dir, '_Providers', '_Python')
    # 将_Python目录添加到sys.path，这样就可以 import strategy
    if python_dir not in sys.path:
        # 将backend/_Providers/_Python/strategy目录添加到sys.path
        sys.path.insert(0, python_dir)
        print(f"[Run] 已将python目录添加到sys.path: {python_dir}")
    else:
        print(f"[Run] python目录已在sys.path中: {python_dir}")

# 设置strategy模块导入路径
setup_python_import_path()

# 导入策略类和ExtDataLoader（已配置为Python库，可直接导入）
from strategy.portfolio import MultiFactorsCTA
from strategy.unified_ext_data_loader import get_unified_ext_data_loader

def find_common_dir():
    """
    查找common目录的位置
    1. 从当前目录向上查找_Python目录
    2. 从_Python目录进入strategy/common
    """
    # 从当前目录开始向上查找_Python目录
    backend_dir = find_backend_dir(current_dir)
    if not backend_dir:
        raise FileNotFoundError("找不到backend目录")

    # 2. 从_Python目录进入strategy/common
    common_dir = os.path.join(backend_dir, '_Providers', '_Python', 'strategy', 'common')
    if not os.path.exists(common_dir) or not os.path.isdir(common_dir):
        raise FileNotFoundError(f"common目录不存在: {common_dir}")

    return common_dir

def load_strategy_configs():
    """
    加载策略配置
    动态读取当前目录下的 strategy_编号.yaml 文件
    """
    strategies = []
    strategy_number = 1
    
    while True:
        strategy_file = os.path.join(current_dir, f'strategy_{strategy_number}.yaml')
        if not os.path.exists(strategy_file):
            break
        
        try:
            print(f"[Run] 正在加载策略文件: {strategy_file}")
            with open(strategy_file, 'r', encoding='utf-8') as f:
                strategy_yaml = f.read()
                
            # 创建策略配置字典
            strategy_config = {
                'id': f'strategy_{strategy_number}',
                'yaml': strategy_yaml
            }
            strategies.append(strategy_config)
            
            print(f"[Run] 成功加载策略 {strategy_number}")
            strategy_number += 1
            
        except Exception as e:
            print(f"[Run] 加载策略文件 {strategy_file} 失败: {e}")
            break
    
    print(f"[Run] 总共加载了 {len(strategies)} 个策略配置")
    return strategies

def create_strategy_instance(engine, strategy_config):
    """
    创建策略实例并添加到引擎

    Args:
        engine: Wonder Trader引擎实例
        strategy_config: 策略配置字典
    """
    try:
        strategy_id = strategy_config.get('id', 'unknown')
        strategy_yaml = strategy_config.get('yaml', '')

        print(f"[Run] 创建策略实例: {strategy_id}")

        # 解析策略YAML配置
        if not strategy_yaml:
            print(f"[Run] 策略 {strategy_id} 的YAML配置为空")
            return False

        parsed_config = yaml.safe_load(strategy_yaml)

        # 提取策略参数
        strategy_name = parsed_config.get('name', strategy_id)
        universe = parsed_config.get('universe', [])
        strategy_type = parsed_config.get('type', 'MultiFactorsCTA')  # 默认使用多因子策略

        print(f"[Run] 策略名称: {strategy_name}")
        print(f"[Run] 策略类型: {strategy_type}")
        print(f"[Run] 交易品种: {universe}")
        print(f"[Run] 策略配置: {parsed_config}")

        strategy_instance = MultiFactorsCTA(
            name=strategy_name,
            codes=universe,
            barCnt=parsed_config.get('bar_count', 50),
            period=parsed_config.get('data_freq', 'day'),
            # 新增参数传递 - 完整的配置传递
            order_by_config=parsed_config.get('order_by', {}),
            buy_rules_config=parsed_config.get('buy_rules', {}),
            sell_rules_config=parsed_config.get('sell_rules', {}),
            top_n=parsed_config.get('top_n', 1),
            weighting_scheme=parsed_config.get('weighting_scheme', 'equal'),
            rebalance_interval=parsed_config.get('rebalance_interval', 'daily')
        )

        # 添加策略到引擎
        engine.add_cta_strategy(strategy_instance)

        print(f"[Run] 策略 {strategy_id} 添加成功")
        return True

    except Exception as e:
        print(f"[Run] 创建策略实例失败: {e}")
        traceback.print_exc()
        return False

def main():
    """
    主函数：初始化引擎并启动策略
    """
    print("=" * 50)
    print("[Run] 启动实盘项目")
    print("=" * 50)
    
    try:
        # 设置工作目录为脚本所在目录
        os.chdir(current_dir)
        print(f"[Run] 工作目录: {current_dir}")
        
        # 检查必要的配置文件
        config_file = os.path.join(current_dir, 'config.yaml')
        if not os.path.exists(config_file):
            print(f"[Run] 主配置文件不存在: {config_file}")
            return False
        
        # 创建Wonder Trader引擎
        print("[Run] 创建Wonder Trader引擎...")
        engine = WtEngine(EngineType.ET_CTA)

        # 注册ExtDataLoader到实盘引擎（在初始化之前）
        print("[Run] 注册ExtDataLoader...")
        try:
            ext_loader = get_unified_ext_data_loader()
            # 检查ext_loader是否是BaseExtDataLoader的实例
            if not isinstance(ext_loader, BaseExtDataLoader):
                print("[Run] ExtDataLoader类型不正确，跳过注册")
            else:
                # 按照官方示例，实盘不使用bAutoTrans参数
                engine.set_extended_data_loader(ext_loader)
                print("[Run] ExtDataLoader已注册到实盘引擎")
        except Exception as e:
            print(f"[Run] ExtDataLoader注册失败: {e}")
            print("[Run] 将继续使用标准数据加载方式")
            traceback.print_exc()

        # 查找common目录
        common_dir = find_common_dir()
        print(f"[Run] 使用common目录: {common_dir}")

        # 初始化引擎
        print("[Run] 初始化引擎...")
        engine.init(common_dir, "config.yaml", commfile="stk_comms.json", contractfile="contracts.json")
        
        # 加载策略配置
        print("[Run] 加载策略配置...")
        strategies = load_strategy_configs()
        
        if not strategies:
            print("[Run] 没有找到策略配置，退出")
            return False
        
        print(f"[Run] 找到 {len(strategies)} 个策略配置")
        
        # 创建并添加策略实例
        success_count = 0
        for strategy_config in strategies:
            if create_strategy_instance(engine, strategy_config):
                success_count += 1
        
        if success_count == 0:
            print("[Run] 没有成功创建任何策略实例，退出")
            return False
        
        print(f"[Run] 成功创建 {success_count} 个策略实例")
        
        # 启动引擎
        print("[Run] 启动引擎...")
        engine.run()
        
        print("[Run] 引擎启动成功，开始运行...")
        
        # 保持运行状态
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[Run] 接收到停止信号，正在关闭...")
            return True
            
    except Exception as e:
        print(f"[Run] 启动过程中发生错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 启动实盘项目
    success = main()
    
    if success:
        print("[Run] 实盘项目已正常关闭")
        sys.exit(0)
    else:
        print("[Run] 实盘项目启动失败")
        sys.exit(1)