// @ts-check
const { WebSocket, WebSocketServer } = require('ws')
const { EventEmitter } = require('events')
const express = require('express')
const { pinyin } = require('pinyin')
const { marketProvider } = require('../_Providers/marketProvider')
const { MarketType, ExchangeType, KLineInterval } = require('../shared_types/market')
const { authenticateToken } = require('../middleware/auth')
const { sequelize } = require('../database')
const { Pool } = require('../models')
const socketIo = require('socket.io')
// 导入socketHandler，用于注册命名空间处理器
let socketHandler = null

// ETF 代码前缀 (A股)
const ETF_PREFIXES = ['51', '511', '513', '518', '56', '588', '15', '16', '159', '1590'];

// 错误处理函数
function getErrorMessage(error) {
  if (error && typeof error === 'object') {
    if ('message' in error) return error.message
    return JSON.stringify(error)
  }
  return String(error)
}

/**
 * 根据代码将 MarketType.STOCK 细分为 'ETF' 或 'STK'
 * @param {object} symbol - 包含 code 和 market 属性的 Symbol 对象
 * @returns {object} 更新了 market 属性的 Symbol 对象
 */
function classifyStockSymbol(symbol) {
  // 只在确定是 A 股 (MarketType.STOCK) 且代码符合 ETF 前缀时才修改 market
  if (symbol && symbol.market === MarketType.STOCK && ETF_PREFIXES.some(prefix => symbol.code.startsWith(prefix))) {
    symbol.market = 'ETF'; // 将 market 修改为 'ETF'
  }
  // 对于非 ETF 的 A 股或其他市场类型，保持原有的 market 不变
  return symbol;
}

// 期货品种列表
const FUTURE_SYMBOLS = [
  { code: 'RB0', name: '螺纹钢', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'AG0', name: '白银', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'AU0', name: '黄金', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'CU0', name: '沪铜', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'AL0', name: '沪铝', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'ZN0', name: '沪锌', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'PB0', name: '沪铅', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'RU0', name: '橡胶', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'FU0', name: '燃油', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'WR0', name: '线材', market: 'FUTURE', exchange: 'SHFE' },
  { code: 'A0', name: '大豆', market: 'FUTURE', exchange: 'DCE' },
  { code: 'M0', name: '豆粕', market: 'FUTURE', exchange: 'DCE' },
  { code: 'Y0', name: '豆油', market: 'FUTURE', exchange: 'DCE' },
  { code: 'J0', name: '焦炭', market: 'FUTURE', exchange: 'DCE' },
  { code: 'C0', name: '玉米', market: 'FUTURE', exchange: 'DCE' },
  { code: 'L0', name: '乙烯', market: 'FUTURE', exchange: 'DCE' },
  { code: 'P0', name: '棕油', market: 'FUTURE', exchange: 'DCE' },
  { code: 'V0', name: 'PVC', market: 'FUTURE', exchange: 'DCE' },
  { code: 'RS0', name: '菜籽', market: 'FUTURE', exchange: 'CZCE' },
  { code: 'RM0', name: '菜粕', market: 'FUTURE', exchange: 'CZCE' },
  { code: 'FG0', name: '玻璃', market: 'FUTURE', exchange: 'CZCE' },
  { code: 'CF0', name: '棉花', market: 'FUTURE', exchange: 'CZCE' },
  { code: 'WS0', name: '强麦', market: 'FUTURE', exchange: 'CZCE' },
  { code: 'ER0', name: '籼稻', market: 'FUTURE', exchange: 'CZCE' },
  { code: 'ME0', name: '甲醇', market: 'FUTURE', exchange: 'CZCE' },
  { code: 'RO0', name: '菜油', market: 'FUTURE', exchange: 'CZCE' },
  { code: 'TA0', name: '甲酸', market: 'FUTURE', exchange: 'CZCE' }
]

/**
 * 获取字符串的拼音和首字母
 * @param {string} str 要转换的中文字符串
 * @returns {{full: string, initial: string}} 返回全拼和首字母拼音
 */
function getPinyin(str) {
  try {
    // 获取完整拼音
    const fullPinyin = pinyin(str, {
      style: pinyin.STYLE_NORMAL,
      heteronym: false
    }).flat().join('')

    // 获取拼音首字母
    const initialPinyin = pinyin(str, {
      style: pinyin.STYLE_FIRST_LETTER,
      heteronym: false
    }).flat().join('')

    return {
      full: fullPinyin.toLowerCase(),
      initial: initialPinyin.toLowerCase()
    }
  } catch (error) {
    console.error('Error in getPinyin:', error)
    return {
      full: '',
      initial: ''
    }
  }
}

/**
 * 检查字符串是否匹配搜索条件
 * @param {string} str 要检查的字符串
 * @param {string} search 搜索关键词
 * @returns {boolean} 是否匹配
 */
function isMatch(str, search) {
  if (!str || !search) return false

  const searchLower = search.toLowerCase()
  const strLower = str.toLowerCase()

  // 直接匹配原字符串开头
  if (strLower.startsWith(searchLower)) {
    return true
  }

  // 获取拼音
  const { full, initial } = getPinyin(str)

  // 匹配全拼或首字母开头
  return full.startsWith(searchLower) || initial.startsWith(searchLower)
}

class MarketHandler extends EventEmitter {
  constructor() {
    super()
    this.router = express.Router()
    this.wss = null
    this.stockCache = null  // 内存缓存
    this.futureCache = null  // 内存缓存
    this.usStockCache = null // 新增: 内存缓存 - 美股
    this.realtimeSubscriptions = new Map() // socket.id -> {symbol, interval}
    this.realtimeDataTimers = new Map() // symbol_interval -> timer
    this.io = null
    this.initRoutes()
    this.initCache()
    //this.setupAutoUpdate()  // 添加自动更新设置
  }

  /**
   * 更新股票缓存
   * @param {number} retryCount - 重试次数，默认为 3
   * @returns {Promise<boolean>} - 更新是否成功
   */
  async updateFutureListCache(retryCount = 3) {
    for (let i = 0; i < retryCount; i++) {
      try {
        console.log(`Updating future symbols cache... (attempt ${i + 1}/${retryCount})`)
        const symbols = await marketProvider.getSymbols(MarketType.FUTURE)

        // 数据验证
        if (!Array.isArray(symbols)) {
          throw new Error('Invalid symbols data: not an array')
        }

        if (symbols.length < 5) {  // 股票数量不应该少于5个
          throw new Error(`Suspicious symbols count: ${symbols.length}`)
        }

        // 验证数据格式
        const isValidFormat = symbols.every(s =>
          s && typeof s.code === 'string' &&
          typeof s.name === 'string' &&
          typeof s.market === 'string'
        )

        if (!isValidFormat) {
          throw new Error('Invalid symbols data format')
        }

        // 更新缓存
        this.futureCache = symbols
        console.log(`Future symbols cache updated successfully with ${symbols.length} symbols at ${new Date().toLocaleString()}`)
        return true

      } catch (error) {
        console.error(`Failed to update future cache (attempt ${i + 1}/${retryCount}):`, error)

        if (i === retryCount - 1) {
          // 所有重试都失败了
          console.error('All update attempts failed, keeping old cache')
          return false
        }

        // 等待 5 秒后重试
        await new Promise(resolve => setTimeout(resolve, 5000))
      }
    }
    return false
  }

  /**
   * 更新股票缓存
   * @param {number} retryCount - 重试次数，默认为 3
   * @returns {Promise<boolean>} - 更新是否成功
   */
  async updateStockListCache(retryCount = 3) {
    for (let i = 0; i < retryCount; i++) {
      try {
        console.log(`Updating stock symbols cache... (attempt ${i + 1}/${retryCount})`)
        const symbols = await marketProvider.getSymbols(MarketType.STOCK)

        // 数据验证
        if (!Array.isArray(symbols)) {
          throw new Error('Invalid symbols data: not an array')
        }

        if (symbols.length < 10) {  // 股票数量不应该少于10个
          throw new Error(`Suspicious symbols count: ${symbols.length}`)
        }

        // 验证数据格式
        const isValidFormat = symbols.every(s =>
          s && typeof s.code === 'string' &&
          typeof s.name === 'string' &&
          typeof s.market === 'string'
        )

        if (!isValidFormat) {
          throw new Error('Invalid symbols data format')
        }

        // 更新缓存
        this.stockCache = symbols
        console.log(`Stock symbols cache updated successfully with ${symbols.length} symbols at ${new Date().toLocaleString()}`)
        return true

      } catch (error) {
        console.error(`Failed to update stock cache (attempt ${i + 1}/${retryCount}):`, error)

        if (i === retryCount - 1) {
          // 所有重试都失败了
          console.error('All update attempts failed, keeping old cache')
          return false
        }

        // 等待 5 秒后重试
        await new Promise(resolve => setTimeout(resolve, 5000))
      }
    }
    return false
  }

  /**
   * 设置自动更新机制
   */
  setupAutoUpdate() {
    // 计算距离下一个凌晨1点的毫秒数
    const now = new Date()
    const nextUpdate = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() + 1, // 下一天
      1, // 凌晨1点
      0,
      0
    )
    const timeUntilNextUpdate = nextUpdate.getTime() - now.getTime()

    // 首次等待到凌晨1点后执行，之后每24小时执行一次
    setTimeout(async () => {
      const success = await this.updateStockListCache()  // 首次更新
      if (!success) {
        console.warn('Initial cache update failed, will retry in 1 hour')
        // 如果首次更新失败，1小时后重试
        setTimeout(() => this.updateStockListCache(), 60 * 60 * 1000)
      }

      // 设置每24小时更新一次
      setInterval(async () => {
        const success = await this.updateStockListCache()
        if (!success) {
          console.warn('Daily cache update failed, will retry in 1 hour')
          // 如果更新失败，1小时后重试
          setTimeout(() => this.updateStockListCache(), 60 * 60 * 1000)
        }
      }, 24 * 60 * 60 * 1000)
    }, timeUntilNextUpdate)

    console.log(`Stock cache will be updated at ${nextUpdate.toLocaleString()}, and every 24 hours thereafter`)
  }

  /**
   * 初始化路由
   */
  initRoutes() {
    // 市场数据相关路由 - 需要认证
    this.router.get('/symbols', authenticateToken, this.handleGetSymbols.bind(this))
    this.router.get('/klines', authenticateToken, this.handleGetKLines.bind(this))
    // 添加手动更新缓存的路由
    this.router.post('/symbols/refresh', authenticateToken, this.handleRefreshSymbols.bind(this))
  }

  /**
   * 服务启动时初始化缓存
   */
  async initCache() {
    try {
      console.log('Initializing stock symbols cache...')
      const symbols = await marketProvider.getSymbols(MarketType.STOCK)
      if (symbols && symbols.length > 0) {
        const cleanedSymbols = symbols.map(s => classifyStockSymbol({
          ...s,
          // 清理名称中的空字符
          name: s.name ? s.name.replace(/\x00+/g, '').trim() : s.name
        }));
        this.stockCache = cleanedSymbols;
        console.log(`Stock symbols cache initialized with ${cleanedSymbols.length} symbols`)
        // 增加日志，显示分类后的统计
        const etfCount = cleanedSymbols.filter(s => s.market === 'ETF').length;
        const stkCount = cleanedSymbols.filter(s => s.market === 'STK').length;
        console.log(` -> Classified as: ${etfCount} ETF, ${stkCount} STK`);
      }
      else {
        console.log('Failed to initialize stock cache: symbols is ', symbols);
      }
    } catch (error) {
      console.error('Failed to initialize stock cache:', error)
    }

    try {
      console.log('Initializing future symbols cache...')
      const symbols = await marketProvider.getSymbols(MarketType.FUTURE)
      if (symbols && symbols.length > 0) {
        const cleanedSymbols = symbols.map(s => ({
          ...s,
          name: s.name ? s.name.replace(/\x00+/g, '').trim() : s.name
        }));
        this.futureCache = cleanedSymbols;
        console.log(`Future symbols cache initialized with ${cleanedSymbols.length} symbols`)
      }
      else {
        console.log('Failed to initialize future cache: symbols is ', symbols);
      }
    } catch (error) {
      console.error('Failed to initialize future cache:', error)
    }

    // 新增: 初始化美股缓存
    try {
      console.log('Initializing US stock symbols cache...')
      // NOTE: Requires getUsSymbols in marketProvider
      const usSymbols = await marketProvider.getUsSymbols()
      if (usSymbols && usSymbols.length > 0) {
        this.usStockCache = usSymbols;
        console.log(`US stock symbols cache initialized with ${usSymbols.length} symbols`)
      }
      else {
        console.log('Failed to initialize US stock cache: symbols is ', usSymbols);
      }
    } catch (error) {
      console.error('Failed to initialize US stock cache:', error)
    }
  }

  /**
   * 手动刷新股票列表缓存
   */
  async handleRefreshSymbols(req, res) {
    // Keep track of individual refresh results
    let stockSuccess = false, futureSuccess = false, usSuccess = false;
    let stockCount = 0, futureCount = 0, usCount = 0;
    let messages = [];

    // Refresh Stock
    try {
      console.log('Manually refreshing stock symbols cache...');
      const stockSymbols = await marketProvider.getSymbols(MarketType.STOCK);
      if (stockSymbols && stockSymbols.length > 0) {
        const cleanedSymbols = stockSymbols.map(s => classifyStockSymbol({
          ...s,
          name: s.name ? s.name.replace(/\x00+/g, '').trim() : s.name
        }));
        this.stockCache = cleanedSymbols;
        stockCount = cleanedSymbols.length;
        // 添加分类统计日志
        const etfCount = cleanedSymbols.filter(s => s.market === 'ETF').length;
        const stkCount = cleanedSymbols.filter(s => s.market === 'STK').length;
        messages.push(`Refreshed ${stockCount} stock symbols (${etfCount} ETF, ${stkCount} STK)`);
        stockSuccess = true;
      } else {
        messages.push('Failed to fetch stock symbols');
      }
    } catch (error) {
      messages.push(`Error refreshing stock symbols: ${getErrorMessage(error)}`);
      console.error('Error refreshing stock symbols:', error);
    }

    // Refresh Future
    try {
      console.log('Manually refreshing future symbols cache...');
      const futureSymbols = await marketProvider.getSymbols(MarketType.FUTURE);
      if (futureSymbols && futureSymbols.length > 0) {
        const cleanedSymbols = futureSymbols.map(s => ({ ...s, name: s.name ? s.name.replace(/\x00+/g, '').trim() : s.name }));
        this.futureCache = cleanedSymbols;
        futureCount = cleanedSymbols.length;
        messages.push(`Refreshed ${futureCount} future symbols`);
        futureSuccess = true;
      } else {
        messages.push('Failed to fetch future symbols');
      }
    } catch (error) {
      messages.push(`Error refreshing future symbols: ${getErrorMessage(error)}`);
      console.error('Error refreshing future symbols:', error);
    }

    // 新增: Refresh US Stock
    try {
      console.log('Manually refreshing US stock symbols cache...');
      // NOTE: Requires getUsSymbols in marketProvider
      const usSymbols = await marketProvider.getUsSymbols();
      if (usSymbols && usSymbols.length > 0) {
          this.usStockCache = usSymbols;
          usCount = usSymbols.length;
          messages.push(`Refreshed ${usCount} US stock symbols`);
          usSuccess = true;
      } else {
          messages.push('Failed to fetch US stock symbols');
      }
    } catch (error) {
        messages.push(`Error refreshing US stock symbols: ${getErrorMessage(error)}`);
        console.error('Error refreshing US stock symbols:', error);
    }

    // Combine results for response
    const overallSuccess = stockSuccess || futureSuccess || usSuccess;
    if (overallSuccess) {
        res.json({
            success: true,
            message: messages.join(' | '),
            counts: { stock: stockCount, future: futureCount, usStock: usCount }
        });
    } else {
        res.status(500).json({
            success: false,
            error: `Manual refresh failed: ${messages.join(' | ')}`
        });
    }
  }

  /**
   * 获取交易品种列表
   */
  async handleGetSymbols(req, res) {
    try {
      const { market, search } = req.query
      let symbols = []

      // 如果没有指定市场，搜索所有市场
      if (!market) {
        // 尝试从缓存获取
        let stockList = this.stockCache || [];
        let futureList = this.futureCache || [];
        let usList = this.usStockCache || []; // 新增获取美股缓存

        // 如果所有相关缓存都为空，尝试重新初始化一次
        if (stockList.length === 0 && futureList.length === 0 && usList.length === 0) {
            console.log('[All Markets Search] Caches empty, attempting re-initialization...');
            await this.initCache(); // Re-init attempts to fill all caches
            // Re-fetch after initialization attempt
            stockList = this.stockCache || [];
            futureList = this.futureCache || [];
            usList = this.usStockCache || [];
        }

        // 合并所有市场的列表用于搜索
        symbols = [...stockList, ...futureList, ...usList]; // 新增合并 usList
        console.log(`[All Markets Search] Searching in ${symbols.length} total symbols (Stock: ${stockList.length}, Future: ${futureList.length}, US: ${usList.length})`); // 更新日志

        // --- 这里可以保留原有的非缓存市场获取逻辑 (CRYPTO, INDEX) ---
        // Example (assuming they don't use cache):
        try {
          const cryptoSymbols = await marketProvider.getSymbols(MarketType.CRYPTO);
          if (cryptoSymbols) symbols = symbols.concat(cryptoSymbols);
        } catch (e) { console.error('Error fetching crypto symbols:', e); }
        // try {
        //   const indexSymbols = await marketProvider.getSymbols(MarketType.INDEX);
        //   if (indexSymbols) symbols = symbols.concat(indexSymbols);
        // } catch (e) { console.error('Error fetching index symbols:', e); }
        // --- End of non-cached market logic ---

      } else {
        // 如果指定了市场，只搜索指定市场 (Keep existing logic for STOCK, FUTURE, CRYPTO, INDEX)
        const market_upper = market.toUpperCase()
        switch (market_upper) {
          case MarketType.STOCK:
            symbols = this.stockCache || []
            if (symbols.length === 0) {
              await this.initCache() // Attempt re-init if specific cache is empty
              symbols = this.stockCache || []
            }
            break
          case MarketType.FUTURE:
            symbols = this.futureCache || []
            if (symbols.length === 0) {
              await this.initCache()
              symbols = this.futureCache || []
            }
            break
          // --- Keep CRYPTO/INDEX cases if they exist ---
          case MarketType.CRYPTO:
            symbols = await marketProvider.getSymbols(MarketType.CRYPTO)
            break
          // case MarketType.INDEX:
          //   symbols = await marketProvider.getSymbols(MarketType.INDEX)
          //   break
          // --- End CRYPTO/INDEX ---
          default:
            // Optionally, add a case for 'US_STOCK' if you want specific US search
            // Or handle unknown markets
            return res.status(400).json({
              success: false,
              error: `Invalid or unsupported market type for cached search: ${market}`
            })
        }
      }

      // 如果有搜索条件，在内存中过滤
      if (search) {
        console.log('Searching with keyword:', search, 'in symbols:', symbols.length)

        // 检查搜索关键词是否全是数字
        const isNumericSearch = /^\d+$/.test(search);

        if (isNumericSearch) {
          // 如果是纯数字搜索，只匹配代码
          symbols = symbols.filter(s => isMatch(s.code, search))
            .map(s => ({
              ...s,
              exchange: s.exchange || s.market // 确保有交易所信息
            }))
            .slice(0, 20);
        } else {
          // 否则同时匹配代码和名称
          symbols = symbols.filter(s =>
            isMatch(s.code, search) || isMatch(s.name, search)
          )
            .map(s => ({
              ...s,
              exchange: s.exchange || s.market // 确保有交易所信息
            }))
            .slice(0, 20);  // 只返回前20条结果
        }

        console.log('Found matches:', symbols.length)
        console.log('Symbols:', symbols)
      } else {
        // 非搜索情况下也确保交易所信息
        symbols = symbols.map(s => ({
          ...s,
          exchange: s.exchange || s.market
        }));
      }

      res.json({
        success: true,
        data: symbols
      })
    } catch (error) {
      console.error('Error in handleGetSymbols:', error)
      res.status(500).json({
        success: false,
        error: getErrorMessage(error)
      })
    }
  }

  /**
   * 获取路由器
   */
  getRouter() {
    return this.router
  }

  /**
   * 获取K线数据
   */
  async handleGetKLines(req, res) {
    try {
      const symbol = req.query.symbol
      const market = (req.query.market || '').toUpperCase() // 转换为大写
      const exchange = (req.query.exchange || '').toUpperCase() // 转换为大写
      const interval = req.query.interval || KLineInterval.DAY1

      // 参数验证
      if (!symbol) {
        return res.status(400).json({
          success: false,
          error: 'Symbol is required'
        })
      }

      if (!market) {
        return res.status(400).json({
          success: false,
          error: 'Market is required'
        })
      }

      if (!Object.values(MarketType).includes(market)) {
        return res.status(400).json({
          success: false,
          error: `Invalid market type: ${market}`
        })
      }

      // 只处理有效的时间戳和限制参数
      const options = {}

      if (req.query.startTime) {
        const startTime = parseInt(req.query.startTime)
        if (!isNaN(startTime)) {
          options.startTime = startTime
        }
      }

      if (req.query.endTime) {
        const endTime = parseInt(req.query.endTime)
        if (!isNaN(endTime)) {
          options.endTime = endTime
        }
      }

      if (req.query.limit) {
        const limit = parseInt(req.query.limit)
        if (!isNaN(limit) && limit > 0) {
          options.limit = limit
        }
      }

      console.log('Fetching klines with params:', {
        symbol,
        market,
        interval,
        options
      })

      const klines = await marketProvider.getKLineHistory(symbol, market, exchange, interval, options)

      if (!klines || klines.length === 0) {
        return res.json({
          success: true,
          data: []
        })
      }

      res.json({
        success: true,
        data: klines
      })
    } catch (error) {
      console.error('Error in handleGetKLines:', error)
      res.status(500).json({
        success: false,
        error: getErrorMessage(error)
      })
    }
  }

  /**
   * 初始化 WebSocket 服务器
   */
  initWebSocket(server) {
    // 保持原有的WebSocket服务器
    const wss = new WebSocketServer({ server, path: '/ws/market' })
    wss.on('connection', this.handleConnection.bind(this))

    // 添加socket.io支持
    this.io = socketIo(server, {
      path: '/socket.io',
      cors: {
        origin: '*',
        methods: ['GET', 'POST']
      }
    })

    this.io.on('connection', (socket) => {
      console.log(`[MarketHandler] Socket.IO连接建立: ${socket.id}`)

      // 处理实时K线订阅
      socket.on('subscribe', (data) => {
        this.handleRealtimeSubscription(socket, data)
      })

      // 处理取消订阅
      socket.on('unsubscribe', (data) => {
        this.handleRealtimeUnsubscription(socket, data)
      })

      // 处理断开连接
      socket.on('disconnect', () => {
        console.log(`[MarketHandler] Socket.IO连接断开: ${socket.id}`)
        this.cleanupSubscription(socket.id)
      })
    })
  }

  /**
   * 处理新的 WebSocket 连接
   */
  handleConnection(ws) {
    console.log('New market data connection')

    ws.on('message', async (message) => {
      try {
        const data = JSON.parse(message)

        switch (data.type) {
          case 'subscribe_kline':
            await this.handleKLineSubscription(ws, data)
            break
          default:
            ws.send(JSON.stringify({
              type: 'error',
              message: 'Unknown message type'
            }))
        }
      } catch (error) {
        console.error('Error handling market websocket message:', error)
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Invalid message format'
        }))
      }
    })

    ws.on('close', () => {
      console.log('Market data connection closed')
    })
  }

  /**
   * 处理K线数据订阅
   */
  async handleKLineSubscription(ws, data) {
    const { symbol, market, interval } = data

    try {
      // 获取初始K线数据
      const klines = await marketProvider.getKLineHistory(symbol, market, interval, {
        limit: 1000  // 获取最近1000条数据
      })

      // 发送初始数据
      ws.send(JSON.stringify({
        type: 'kline_data',
        symbol,
        interval,
        data: klines
      }))

      // 订阅实时数据
      marketProvider.subscribeKLine(symbol, interval, (kline) => {
        ws.send(JSON.stringify({
          type: 'kline_update',
          symbol,
          interval,
          data: kline
        }))
      })
    } catch (error) {
      console.error('Error in handleKLineSubscription:', error)
      ws.send(JSON.stringify({
        type: 'error',
        message: getErrorMessage(error)
      }))
    }
  }

  // 处理实时K线订阅
  handleRealtimeSubscription(socket, data) {
    try {
      const { symbol, period } = data;

      // 验证参数
      if (!symbol || !period) {
        console.log('[MarketHandler] 订阅参数验证失败:', data);
        socket.emit('error', { message: '缺少必要参数: symbol, period' });
        return;
      }

      // 只接受股票数据
      /*if (symbol.market !== 'STOCK') {
        console.log('[MarketHandler] 非股票品种订阅被拒绝:', symbol);
        socket.emit('error', { message: '实时数据仅支持股票品种' });
        return;
      }*/

      // 验证周期
      const validPeriods = ['1m', '5m', '15m', '30m', '1h', '1D', '1W', '1M'];
      if (!validPeriods.includes(period)) {
        console.log('[MarketHandler] 不支持的周期:', period);
        socket.emit('error', { message: `不支持的周期: ${period}` });
        return;
      }

      console.log(`[MarketHandler] 订阅实时K线成功: ${symbol.code} ${period}`);

      // 保存订阅信息
      this.realtimeSubscriptions.set(socket.id, { symbol, period });

      // 获取首次的实时数据
      const dataPackage = this.fetchAndSendRealtimeData(socket, symbol, period)
        .then(() => {
          console.log(`[MarketHandler] 首次数据发送完成: ${symbol.code} ${period}`);
        })
        .catch(err => {
          console.error(`[MarketHandler] 首次数据发送失败: ${symbol.code} ${period}`, err);
        });


      
      // 启动或复用数据抓取定时器
      this.startRealtimeDataFetching(socket, symbol, period);        

      socket.emit('subscribed', { symbol, period });
    } catch (error) {
      console.error('[MarketHandler] 订阅实时K线错误:', error);
      socket.emit('error', { message: getErrorMessage(error) });
    }
  }

  // 处理取消订阅
  handleRealtimeUnsubscription(socket, data) {
    try {
      console.log(`[MarketHandler] 处理取消订阅: ${socket.id}`);
      this.cleanupSubscription(socket.id);
      socket.emit('unsubscribed', {});
      console.log(`[MarketHandler] 取消订阅成功: ${socket.id}`);
    } catch (error) {
      console.error('[MarketHandler] 取消订阅错误:', error);
      socket.emit('error', { message: getErrorMessage(error) });
    }
  }

  // 清理订阅
  cleanupSubscription(socketId) {
    // 删除订阅信息
    this.realtimeSubscriptions.delete(socketId)

    // 检查是否还有其他客户端订阅相同的品种和周期
    const activeSymbolPeriods = new Set()
    for (const { symbol, period } of this.realtimeSubscriptions.values()) {
      activeSymbolPeriods.add(`${symbol.code}_${period}`)
    }

    // 停止不再需要的定时器
    for (const [key, timer] of this.realtimeDataTimers.entries()) {
      if (!activeSymbolPeriods.has(key)) {
        clearInterval(timer)
        this.realtimeDataTimers.delete(key)
        console.log(`[MarketHandler] 停止实时数据抓取: ${key}`)
      }
    }
  }

  // 启动实时数据持续抓取
  startRealtimeDataFetching(socket, symbol, period) {
    const key = `${symbol.code}_${period}`

    // 如果已经有定时器，不重复创建
    if (this.realtimeDataTimers.has(key)) {
      return
    }

    // 根据周期确定适当的抓取间隔
    let fetchInterval = 10000 // 10秒
    /*switch (period) {
      case '1m': fetchInterval = 60000; break  // 1分钟
      case '5m': fetchInterval = 120000; break  // 2分钟
      case '15m': fetchInterval = 300000; break // 5分钟
      case '30m': fetchInterval = 300000; break // 5分钟
      case '1h': fetchInterval = 300000; break // 5分钟
      case '1D': fetchInterval = 300000; break // 5分钟
      case '1W': fetchInterval = 3600000; break // 60分钟
    }*/

    // 创建定时器
    const timer = setInterval(async () => {
      const dataPackage = await this.fetchAndSendRealtimeData(null, symbol, period, 2)

      // [实时K线定时] 检查最后一条K线时间，若小于当前时间则停止定时器
      if (dataPackage && dataPackage.klines && dataPackage.klines.length > 0) {
        const lastKline = dataPackage.klines[dataPackage.klines.length - 1];
        const now = Math.floor(Date.now() / 1000);
        if (lastKline.time < now) {
          clearInterval(timer);
          this.realtimeDataTimers.delete(key);
          console.log(`[实时K线定时] ${symbol.code} ${period} 最后一条K线时间(${lastKline.time}) < 当前时间(${now})，已停止定时器`);
        }
      }
    }, fetchInterval)

    this.realtimeDataTimers.set(key, timer)
    console.log(`[MarketHandler] 启动实时数据抓取: ${key}, 间隔: ${fetchInterval}ms`)
  }

  /**
   * 夜盘时间调整函数测试
   * 针对期货夜盘交易时间戳调整逻辑
   * TODO: 目前的假设是服务器在中国境内
   */
  adjustNightTradingTime(klineData, useShou) {

    if (!klineData || klineData.length === 0) {
      console.log('输入数据为空或长度为0，直接返回');
      return klineData;
    }

    // 创建结果数组
    const result = [];

    // 记录上一个日盘的日期（时间戳，精确到日）
    let lastDaySessionDate = 0;

    // 记录调整的数据条数
    let adjustedCount = 0;

    for (const kline of klineData) {

      const msTime = kline.time * 1000;

      // 创建日期对象，通过时间戳（秒）,
      const klineDate = new Date(msTime);

      // 获取小时
      const year = klineDate.getFullYear();
      const month = klineDate.getMonth();
      const day = klineDate.getDate();
      const hours = klineDate.getHours();
      const minutes = klineDate.getMinutes();
      const seconds = klineDate.getSeconds();


      const isNightSession = hours >= 17;

      // 如果是股票，需要把成交量乘以100
      if (!useShou) kline.volume = kline.volume * 100;

        kline
      // 如果是夜盘交易，且有上一个日盘日期记录，需要调整时间
      if (isNightSession) {

        if (lastDaySessionDate === 0) {

          let previousDay = new Date(year, month, day);

          previousDay.setDate(previousDay.getDate() - 1);

          while (previousDay.getDay() === 0 || previousDay.getDay() === 6) {

            previousDay.setDate(previousDay.getDate() - 1);

          }

          lastDaySessionDate = Math.floor(previousDay.getTime() / 1000);

        }

        // 把此条k线时间的日期更换为 lastDaySessionDate
        kline.time = lastDaySessionDate + (hours * 3600 + minutes * 60 + seconds);
        console.log(`[MarketHandler] 夜盘，${year}-${month}-${day} ${hours}:${minutes}:${seconds} 最近日盘日期=${lastDaySessionDate} 调整夜盘时间: ${msTime / 1000} ${kline.time}`);

        adjustedCount++; // 增加调整计数

      } else {

        lastDaySessionDate = new Date(year, month, day).getTime() / 1000;

      }

      result.push(kline)
    }

    // 添加日志，显示调整了多少夜盘数据
    console.log(`====== 调整了 ${adjustedCount}/${klineData.length} 条夜盘数据 ======`);

    return [result, adjustedCount];
  }

  async fetchAndSendRealtimeData(specificSocket, symbol, period, count = 500) {
    try {
      console.log(`[MarketHandler] 开始获取实时数据: ${symbol.code} ${period}`);

      // 检查交易所类型，只允许国内股票和期货交易所
      const allowedExchanges = [
        'SSE', 'SZSE', 'BSE',  // 国内股票交易所
        'SHFE', 'DCE', 'CZCE', 'CFFEX', 'INE',  // 国内期货交易所
        'NASDAQ', 'NYSE', 'AMEX'  // 美股交易所
      ];

      const useShou = symbol.market == MarketType.FUTURE;

      if (!allowedExchanges.includes(symbol.exchange)) {
        console.warn(`[MarketHandler] 不支持的交易所类型: ${symbol.exchange}，仅支持国内股票和期货交易所`);

        // 如果指定了特定的socket，发送错误消息
        if (specificSocket) {
          specificSocket.emit('error', {
            message: `不支持的交易所类型: ${symbol.exchange}，仅支持国内股票和期货交易所`,
            symbol: symbol,
            period: period
          });
        }

        return;
      }

      // 使用marketProvider获取实时数据，获取到的时间戳是正常时间戳
      const klineData = await marketProvider.getRealTimeKLines(symbol.code, period, symbol.exchange, count);

      if (!klineData || klineData.length === 0) {
        console.warn(`[MarketHandler] 没有获取到 ${symbol.code} ${period} 的实时数据`);

        // 如果指定了特定的socket，发送错误消息
        if (specificSocket) {
          specificSocket.emit('error', {
            message: `没有获取到 ${symbol.code} 的实时数据`,
            symbol: symbol,
            period: period
          });
        }

        return;
      }

      let adjustedKlineData = [];
      let adjustedCount = 0;

      // 对数据有效性进行调整，通达信期货的夜盘会把日期算到次日，就会出现更大的时间在前的情况，需要调整
      console.log(`[MarketHandler] 调整夜盘时间，数据条数：${klineData.length}...`);

      [adjustedKlineData, adjustedCount] = this.adjustNightTradingTime(klineData, useShou);
      console.log(`[MarketHandler] 调整夜盘时间完成，数据条数：${adjustedCount}...`);
      // 构建数据包
      const dataPackage = {
        symbol,
        interval: period,
        klines: adjustedKlineData
      };

      // 记录数据
      console.log(`[MarketHandler] 获取到实时数据: ${symbol.code} ${period}, 数据长度: ${adjustedKlineData.length}, 第一条: ${JSON.stringify(adjustedKlineData[0])}, 最后一条: ${JSON.stringify(adjustedKlineData[adjustedKlineData.length - 1])}`);

      // 发送数据
      if (specificSocket) {
        // 如果指定了特定的socket，只发送给它
        console.log(`[MarketHandler] 发送实时数据到特定客户端: ${specificSocket.id}`);
        specificSocket.emit('realtimeKline', dataPackage);
      } else {
        // 否则发送给所有订阅此品种和周期的客户端
        let sentCount = 0;

        for (const [socketId, sub] of this.realtimeSubscriptions.entries()) {
          if (sub.symbol.code === symbol.code && sub.period === period) {
            // 检查 this.io 是否已经是命名空间
            const namespace = this.io.name === '/realtime' ? this.io : this.io.of('/realtime');
            const targetSocket = namespace.sockets.get(socketId);
            if (targetSocket) {
              console.log(`[MarketHandler] 发送实时数据到客户端: ${socketId}`);
              targetSocket.emit('realtimeKline', dataPackage);
              sentCount++;
            } else {
              console.warn(`[MarketHandler] 找不到socket: ${socketId}，将清理此订阅`);
              this.cleanupSubscription(socketId);
            }
          }
        }

        console.log(`[MarketHandler] 实时数据已发送给 ${sentCount} 个客户端`);
      }

      return dataPackage;
    } catch (error) {
      console.error(`[MarketHandler] 获取实时数据错误: ${symbol.code} ${period}`, error);
      throw error;
    }
  }

  /**
   * 初始化Socket.IO服务
   * @param {SocketIO.Server|SocketIO.Namespace} io Socket.IO服务器实例或命名空间
   */
  initSocketIO(io) {
    console.log('[MarketHandler] 初始化Socket.IO服务');
    this.io = io;

    // 检查是否已经是命名空间
    let realtimeNamespace;
    try {
      realtimeNamespace = io.name === '/realtime' ? io : (typeof io.of === 'function' ? io.of('/realtime') : io);
      console.log(`[MarketHandler] 使用Socket.IO命名空间: ${realtimeNamespace.name || '/realtime'}`);
    } catch (error) {
      console.error('[MarketHandler] 初始化Socket.IO命名空间时出错:', error);
      // 如果出错，假设io已经是命名空间
      console.log('[MarketHandler] 假设传入的io已经是命名空间');
      realtimeNamespace = io;
    }

    realtimeNamespace.on('connection', (socket) => {
      try {
        console.log('[MarketHandler] !!!!! CONNECTION HANDLER ENTERED !!!!!');

        console.log(`[MarketHandler] Socket.IO客户端连接: ${socket.id}`);

        // 记录所有事件
        const onevent = socket.onevent;
        socket.onevent = function (packet) {
          const args = packet.data || [];
          console.log(`[MarketHandler] 收到Socket事件: ${args[0]}`, args.slice(1));
          onevent.call(this, packet);
        };

        // 处理实时K线订阅
        socket.on('subscribe', (data) => {
          console.log(`[MarketHandler] 收到订阅请求:`, data);
          this.handleRealtimeSubscription(socket, data);
        });

        // 处理取消订阅
        socket.on('unsubscribe', (data) => {
          console.log(`[MarketHandler] 收到取消订阅请求:`, data);
          this.handleRealtimeUnsubscription(socket, data);
        });

        // 处理断开连接
        socket.on('disconnect', (reason) => {
          console.log(`[MarketHandler] Socket.IO客户端断开连接: ${socket.id}, 原因: ${reason}`);
          this.cleanupSubscription(socket.id);
        });
      } catch (error) {
        console.error('[MarketHandler] !!!!! CRITICAL ERROR IMMEDIATELY INSIDE CONNECTION HANDLER !!!!!', error);
      }
    });
  }
}

// 创建实例
const marketHandler = new MarketHandler();

// 导出模块
module.exports = {
  marketHandler,
  // 注册命名空间处理器的函数
  registerSocketHandler: (handler) => {
    // 延迟导入socketHandler，避免循环依赖
    socketHandler = handler;

    // 注册实时数据命名空间处理器
    if (socketHandler && socketHandler.registerNamespaceHandler) {
      socketHandler.registerNamespaceHandler(
        '/realtime',
        marketHandler,
        '实时K线数据处理'
      );
      console.log('[MarketHandler] 已注册实时数据命名空间处理器: /realtime');
    }
  }
}