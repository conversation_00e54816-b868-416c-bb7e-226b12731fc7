import requests
import threading
import redis
import time
import json

# tdxserver.py服务地址
BASE_URL = "http://127.0.0.1:5003/subscribe_kline"

# 测试的品种（A股、ETF等，格式 market:code）
symbols = [
    "sh:510300",  # 沪深300ETF
    "sh:600000",  # 浦发银行
    "sz:000001",  # 平安银行
    "sh:510500",  # 中证500ETF
]

period = "day"

def subscribe(symbol):
    resp = requests.post(BASE_URL, json={"period": period, "symbol": symbol})
    print(f"订阅 {symbol} 返回: {resp.json()}")

def listen_redis(symbol):
    channel = f'kline.{period}.{symbol}'
    r = redis.StrictRedis(
        host='localhost',
        port=6379,
        db=0,
        password='quanTquarT2025@Fuzhou',  # 加上密码，和服务端一致
        decode_responses=True
    )
    pubsub = r.pubsub()
    pubsub.subscribe(channel)
    print(f"开始监听频道: {channel}")
    for msg in pubsub.listen():
        if msg['type'] == 'message':
            try:
                data_list = json.loads(msg['data'])
                if isinstance(data_list, list) and data_list:
                    print(f"[{channel}] 收到推送 (共 {len(data_list)} 条), 最后3条: {json.dumps(data_list[-3:], ensure_ascii=False, indent=2)}")
                else:
                    print(f"[{channel}] 收到推送 (空或非列表): {msg['data']}")
            except (json.JSONDecodeError, TypeError):
                 print(f"[{channel}] 收到无法解析的推送: ...{msg['data'][-200:]}")

if __name__ == "__main__":
    # 先发起所有订阅
    for symbol in symbols:
        subscribe(symbol)
        time.sleep(0.2)  # 避免并发过快

    # 每个品种开一个线程监听推送
    threads = []
    for symbol in symbols:
        t = threading.Thread(target=listen_redis, args=(symbol,), daemon=True)
        t.start()
        threads.append(t)

    # 主线程保持运行
    while True:
        time.sleep(60)