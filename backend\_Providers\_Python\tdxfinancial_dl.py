import os
from mootdx.affair import Affair

# 创建本地目录
download_dir = './financial'
os.makedirs(download_dir, exist_ok=True)

# 获取文件列表
def get_file_list():
    try:
        # 使用 mootdx 的 Affair.files() 方法获取文件列表
        files = Affair.files()
        if not files:
            print("未获取到文件列表")
            return None
        return files
    except Exception as e:
        print(f"获取文件列表失败: {e}")
        return None

# 下载文件
def download_file(file_info):
    try:
        filename = file_info['filename']
        print(f'正在下载: {filename}')

        # 使用 mootdx 的 Affair.fetch() 方法下载文件
        Affair.fetch(downdir=download_dir, filename=filename)
        print(f'下载完成: {filename}')
    except Exception as e:
        print(f'下载失败: {filename}, 错误: {e}')

# 主函数
def main():
    # 获取文件列表
    file_list = get_file_list()
    if not file_list:
        return

    # 逐个下载文件
    for file_info in file_list:
        download_file(file_info)

    print("所有文件下载完成！")

# 运行主函数
if __name__ == "__main__":
    main()