# 量化交易系统模块说明

本文档列出所有系统模块，用于日志输出时的模块标识。

## 模块列表

1. **[策略回测]** - 策略回测模块，负责执行策略回测、计算绩效指标
2. **[数据准备]** - 数据准备模块，负责准备回测和实盘所需的数据
3. **[信号计算]** - 信号计算模块，负责计算各种策略信号
4. **[资产配置]** - 资产配置模块，负责计算资产权重配置
5. **[风险控制]** - 风险控制模块，负责计算风险指标和控制
6. **[交易执行]** - 交易执行模块，负责执行交易订单
7. **[系统服务]** - 系统服务模块，负责系统运行和管理
8. **[账户管理]** - 账户管理模块，负责用户账户管理
9. **[性能监控]** - 性能监控模块，负责监控系统性能

## 使用说明

在日志输出时，请在日志前添加对应的模块标识，例如：

```python
print(f"[策略回测] 开始回测策略: {strategy_id}")
```

这样可以清晰地看出日志来自哪个模块，方便定位问题。 