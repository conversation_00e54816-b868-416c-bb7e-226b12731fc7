# backend/_Providers/_Python/backtest_server.py
import os
import json
import logging
from flask import Flask, jsonify, request
import yaml # 需要 PyYAML 库来解析 YAML
from pathlib import Path

# --- 配置日志 ---
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BacktestService")

app = Flask(__name__)

# --- 全局变量 (稍后可以移到配置类或应用上下文中) ---
CONFIG = {}
STRATEGY_DIR = Path(__file__).parent / "strategy" / "portfolio"

# --- 加载配置 --- (在应用启动时执行一次)
def load_config():
    global CONFIG
    config_path = Path(__file__).parent / "config.json"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            CONFIG = json.load(f)
        logger.info(f"Configuration loaded from {config_path}")
        # 可以在这里验证必要的配置项是否存在
        if "python_backtest_service" not in CONFIG:
             logger.warning("'python_backtest_service' not found in config.json, using default host/port.")
        if not STRATEGY_DIR.is_dir():
             logger.warning(f"Strategy directory not found or is not a directory: {STRATEGY_DIR}")

    except FileNotFoundError:
        logger.error(f"Configuration file not found: {config_path}. Service might not work correctly.")
    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from {config_path}. Check the file format.")
    except Exception as e:
        logger.error(f"Error loading configuration: {e}", exc_info=True)

# --- API 端点 --- #

@app.route('/strategies', methods=['GET'])
def get_strategies():
    """获取所有可用策略的列表。"""
    strategies = []
    if not STRATEGY_DIR.is_dir():
        logger.error(f"Strategy directory does not exist: {STRATEGY_DIR}")
        return jsonify({"error": f"Strategy directory not found: {STRATEGY_DIR}"}), 500

    try:
        for yaml_file in STRATEGY_DIR.glob('*.yaml'):
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    content = yaml.safe_load(f)
                    strategy_id = yaml_file.stem # 使用文件名（无扩展名）作为 ID
                    strategy_id = content.get('strategy_id', strategy_id) # 获取名称，若无则用 ID
                    strategies.append({
                        "id": strategy_id,
                        "name": strategy_id
                    })
            except yaml.YAMLError as e:
                logger.warning(f"Error parsing YAML file {yaml_file.name}: {e}")
                # 可以选择跳过这个文件或返回部分列表
            except Exception as e:
                 logger.warning(f"Error processing file {yaml_file.name}: {e}")

        logger.info(f"Found {len(strategies)} strategies.")
        return jsonify(strategies)

    except Exception as e:
        logger.error(f"Error reading strategy directory {STRATEGY_DIR}: {e}", exc_info=True)
        return jsonify({"error": "Failed to retrieve strategies."}), 500

# 添加前端预期的路由路径
@app.route('/strategy/list', methods=['GET'])
def get_strategy_list():
    """与前端路径匹配的策略列表API"""
    logger.info("Received request to /strategy/list, forwarding to strategies handler")
    return get_strategies()

@app.route('/strategy/backtest/<strategy_id>', methods=['POST'])
def run_strategy_backtest(strategy_id):
    """执行特定策略的回测"""
    logger.info(f"[策略回测] 收到策略回测请求: {strategy_id}")
    
    try:
        # 1. 根据 strategy_id 查找对应的YAML配置文件
        yaml_file = STRATEGY_DIR / f"{strategy_id}.yaml"
        if not yaml_file.exists():
            logger.error(f"[策略回测] 未找到策略文件: {yaml_file}")
            return jsonify({"error": f"Strategy {strategy_id} not found"}), 404
            
        # 2. 调用 portfolio_center.py 的 run_backtest 函数
        from strategy.portfolio.portfolio_center import do_run_backtest
        
        # 3. 运行回测
        results = do_run_backtest(str(yaml_file))
        
        # 4. 格式化结果并返回
        return jsonify({
            "summary": {
                "cagr": results.get('returns', {}).get('annual', 0) / 100,  # 从百分比转为小数
                "maxDrawdown": results.get('returns', {}).get('max_drawdown', 0) / 100,
                "sharpe": results.get('risk', {}).get('sharpe', 0),
                "initialCapital": 100000  # 从配置中获取
            },
            "equityCurve": format_equity_curve(results),
            "trades": results.get('trades', {}).get('records', []),
            "usersCount": 0  # 可以从数据库中获取
        })
        
    except Exception as e:
        logger.error(f"[策略回测] 执行策略 {strategy_id} 回测时出错: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500
        
def format_equity_curve(results):
    """将回测结果转换为前端需要的权益曲线格式"""
    daily_data = results.get('positions', [])
    if not daily_data:
        return []
        
    # 转换为前端需要的格式
    equity_curve = []
    for day in daily_data:
        equity_curve.append({
            "date": str(day.get('date')),
            "equity": day.get('net_value', 0) / results.get('returns', {}).get('initial_capital', 100000),
            "benchmark": 1.0  # 可以添加基准指数数据
        })
    
    return equity_curve

# --- 运行服务器 --- #
if __name__ == '__main__':
    load_config()
    service_config = CONFIG.get('python_backtest_service', {})
    host = service_config.get('host', '127.0.0.1')
    port = service_config.get('port', 5002)
    logger.info(f"Starting Flask backtest server on {host}:{port}")
    app.run(host=host, port=port, debug=True) # 使用 debug=True 便于开发 