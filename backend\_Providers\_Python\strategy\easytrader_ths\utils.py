#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths工具函数

该模块提供了一些工具函数，用于辅助easytrader_ths模块的功能。
"""

import os
import json
import logging
import socket
import time
from datetime import datetime

logger = logging.getLogger("EasyTrader-THS-Utils")

def get_local_ip():
    """
    获取本机IP地址
    
    Returns:
        str: 本机IP地址
    """
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

def format_log_message(message, level="INFO"):
    """
    格式化日志消息
    
    Args:
        message: 日志消息
        level: 日志级别
    
    Returns:
        str: 格式化后的日志消息
    """
    return f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [{level}] {message}"

def log_to_file(message, filename, level="INFO"):
    """
    将日志写入文件
    
    Args:
        message: 日志消息
        filename: 日志文件名
        level: 日志级别
    
    Returns:
        bool: 是否写入成功
    """
    try:
        # 确保日志目录存在
        log_dir = os.path.dirname(filename)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 写入日志
        with open(filename, 'a', encoding='utf-8') as f:
            f.write(format_log_message(message, level) + '\n')
        
        return True
    except Exception as e:
        logger.error(f"写入日志失败: {str(e)}")
        return False

def save_json(data, filename):
    """
    保存JSON数据到文件
    
    Args:
        data: 要保存的数据
        filename: 文件名
    
    Returns:
        bool: 是否保存成功
    """
    try:
        # 确保目录存在
        file_dir = os.path.dirname(filename)
        if file_dir and not os.path.exists(file_dir):
            os.makedirs(file_dir)
        
        # 保存数据
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        
        return True
    except Exception as e:
        logger.error(f"保存JSON数据失败: {str(e)}")
        return False

def load_json(filename, default=None):
    """
    从文件加载JSON数据
    
    Args:
        filename: 文件名
        default: 默认值，如果文件不存在或加载失败则返回该值
    
    Returns:
        加载的数据，如果加载失败则返回默认值
    """
    if not os.path.exists(filename):
        return default
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON数据失败: {str(e)}")
        return default

def retry(func, max_retries=3, retry_interval=1, exceptions=(Exception,)):
    """
    重试函数
    
    Args:
        func: 要重试的函数
        max_retries: 最大重试次数
        retry_interval: 重试间隔(秒)
        exceptions: 捕获的异常类型
    
    Returns:
        函数返回值
    
    Raises:
        Exception: 如果重试次数用完仍然失败，则抛出最后一次的异常
    """
    last_exception = None
    
    for i in range(max_retries):
        try:
            return func()
        except exceptions as e:
            last_exception = e
            logger.warning(f"函数执行失败，将进行第{i+1}次重试: {str(e)}")
            time.sleep(retry_interval)
    
    if last_exception:
        raise last_exception

def convert_code_to_ths_format(code):
    """
    将代码转换为同花顺格式
    
    Args:
        code: 代码，如'SSE.600000', 'SZSE.000001'
    
    Returns:
        str: 同花顺格式的代码，如'sh600000', 'sz000001'
    """
    if not code:
        return code
    
    # 提取市场和代码
    parts = code.split('.')
    if len(parts) != 2:
        return code
    
    market, stock_code = parts
    
    # 转换市场代码
    market_map = {
        'SSE': 'sh',
        'SZSE': 'sz',
        'SHFE': 'sh',
        'DCE': 'dc',
        'CZCE': 'zc',
        'CFFEX': 'cf',
        'INE': 'in'
    }
    
    ths_market = market_map.get(market.upper())
    if not ths_market:
        return code
    
    return f"{ths_market}{stock_code}"

def convert_code_from_ths_format(code):
    """
    将同花顺格式的代码转换为标准格式
    
    Args:
        code: 同花顺格式的代码，如'sh600000', 'sz000001'
    
    Returns:
        str: 标准格式的代码，如'SSE.600000', 'SZSE.000001'
    """
    if not code:
        return code
    
    # 提取市场和代码
    if code.startswith('sh'):
        market = 'SSE'
        stock_code = code[2:]
    elif code.startswith('sz'):
        market = 'SZSE'
        stock_code = code[2:]
    elif code.startswith('dc'):
        market = 'DCE'
        stock_code = code[2:]
    elif code.startswith('zc'):
        market = 'CZCE'
        stock_code = code[2:]
    elif code.startswith('cf'):
        market = 'CFFEX'
        stock_code = code[2:]
    elif code.startswith('in'):
        market = 'INE'
        stock_code = code[2:]
    else:
        return code
    
    return f"{market}.{stock_code}"
