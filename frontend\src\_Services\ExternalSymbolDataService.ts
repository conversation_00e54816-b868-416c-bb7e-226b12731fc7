/**
 * 外部品种数据服务
 * 用于处理多品种重叠指标的数据请求
 */

import { KLineInterval, Symbol, KLineData, MarketType, ExchangeType } from '@/shared_types/market';
import httpDispatcher from '@/_Dispatchers/HttpDispatcher';
import { getToken } from '@/utils/auth';

// 数据缓存，避免重复请求
const dataCache = new Map<string, any[]>();

/**
 * 获取K线数据
 * 这个函数是专门为多品种重叠指标设计的，不会影响应用状态
 *
 * @param fullCode 完整的品种代码，格式为 xxx.xxx.xxx
 * @param interval K线周期
 * @param options 可选参数，包括开始时间、结束时间和数据限制
 * @returns 返回K线数据数组
 */
export async function getKLines(
  fullCode: string | Symbol,
  interval: KLineInterval,
  options: {
    startTime?: number;
    endTime?: number;
    limit?: number;
  } = {}
): Promise<{ success: boolean; data?: KLineData[]; error?: string }> {
  try {
    // 构建Symbol对象
    let symbol: Symbol;

    if (typeof fullCode === 'string') {
      // 解析完整代码 xxx.xxx.xxx
      const parts = fullCode.split('.');
      if (parts.length !== 3) {
        throw new Error(`品种代码 ${fullCode} 格式不正确，应为 xxx.xxx.xxx`);
      }

      symbol = {
        code: parts[2],
        market: parts[1] as MarketType, // 类型转换
        exchange: parts[0] as ExchangeType, // 类型转换
        name: parts[2] // 名称默认使用代码
      };
    } else {
      // 已经是Symbol对象
      symbol = fullCode;
    }

    console.log(`[ExternalSymbolDataService] 获取K线数据: ${symbol.exchange}.${symbol.market}.${symbol.code}, 周期: ${interval}`);

    // 检查缓存
    const cacheKey = `${symbol.exchange}.${symbol.market}.${symbol.code}_${interval}`;
    if (dataCache.has(cacheKey)) {
      console.log(`[ExternalSymbolDataService] 使用缓存数据: ${cacheKey}`);
      return {
        success: true,
        data: dataCache.get(cacheKey) as KLineData[]
      };
    }

    // 获取认证Token
    const token = getToken();

    // 调用API获取K线数据
    const response = await httpDispatcher.get('/market/klines', {
      params: {
        symbol: symbol.code,
        market: symbol.market,
        exchange: symbol.exchange,
        interval,
        ...options
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`[ExternalSymbolDataService] K线数据响应状态码: ${response.status}`);

    if (response.data.success) {
      let klineData = response.data.data;

      // 检查数据是否按时间升序排列
      if (klineData.length > 1) {
        const isDescending = klineData[0].time > klineData[1].time;
        if (isDescending) {
          console.log('[ExternalSymbolDataService] 检测到K线数据为倒序，正在转换为升序...');
          klineData = klineData.reverse();
        }
      }

      // 缓存数据
      dataCache.set(cacheKey, klineData);

      console.log(`[ExternalSymbolDataService] 获取到K线数据: ${symbol.exchange}.${symbol.market}.${symbol.code}, 数据点数: ${klineData.length}`);

      return {
        success: true,
        data: klineData
      };
    }

    throw new Error(response.data.error || '获取K线数据失败');
  } catch (error) {
    console.error('[ExternalSymbolDataService] 获取K线数据失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// 清除缓存
export function clearExternalSymbolDataCache() {
  console.log('[ExternalSymbolDataService] 清除数据缓存');
  dataCache.clear();
}
