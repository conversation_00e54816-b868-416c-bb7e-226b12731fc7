import React, { useEffect, useState } from 'react';
import { BrowserRouter, Routes, Route, useNavigate } from 'react-router-dom';
import { ConfigProvider, theme, ThemeConfig, App as AntdApp, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import AppRouter from './router';
import { useAtom } from 'jotai';
import { themeAtom, ThemeType } from './models/useTheme/index';
import GlobalLoading from './components/GlobalLoading';
import { initializeApp } from './init';
import LiveStrategyConfigModal from './_Widgets/LiveStrategyConfigModal';
import TradingChannelConfigModal from './_Widgets/TradingChannelConfigModal';
import { UserEvents } from './events/events';
import { EventBus } from './events/eventBus';
import { removeToken } from './utils/auth';

// 添加移动设备判断函数
export const isMobileDevice = () => window.innerWidth <= 768;

// 预设主题配置
const themeConfig: Record<ThemeType, ThemeConfig> = {
  // 亮色主题 - 默认风格（经典中性）
  'light-default': {
    algorithm: theme.defaultAlgorithm,
    token: {
      colorPrimary: '#1677ff',
      borderRadius: 6,
    },
  },
  // 亮色主题 - 深蓝风格（专业商务）
  'light-blue': {
    algorithm: theme.defaultAlgorithm,
    token: {
      colorPrimary: '#0958d9',
      borderRadius: 6,
    },
    components: {
      Layout: {
        colorBgHeader: '#f0f5ff',
        colorBgBody: '#ffffff',
        colorBgContainer: '#ffffff',
      },
    },
  },
  // 亮色主题 - 科技风格（现代简约）
  'light-tech': {
    algorithm: theme.defaultAlgorithm,
    token: {
      colorPrimary: '#165dff',
      borderRadius: 6,
    },
    components: {
      Layout: {
        colorBgHeader: '#f7f9fc',
        colorBgBody: '#ffffff',
        colorBgContainer: '#ffffff',
      },
    },
  },
  // 暗色主题 - 默认风格（经典暗色）
  'dark-default': {
    algorithm: theme.darkAlgorithm,
    token: {
      colorPrimary: '#1668dc',
      borderRadius: 6,
    },
    components: {
      Layout: {
        colorBgHeader: '#141414',
        colorBgBody: '#1f1f1f',
        colorBgContainer: '#141414',
        colorBgElevated: '#1f1f1f',
      },
      Menu: {
        colorItemBg: '#141414',
        colorSubItemBg: '#1f1f1f',
        colorItemBgSelected: '#1f1f1f',
      },
      Card: {
        colorBgContainer: '#141414',
      },
      Table: {
        colorBgContainer: '#141414',
        colorFillAlter: '#1f1f1f',
      },
    },
  },
  // 暗色主题 - 深蓝风格（专业暗色）
  'dark-blue': {
    algorithm: theme.darkAlgorithm,
    token: {
      colorPrimary: '#1668dc',
      borderRadius: 6,
    },
    components: {
      Layout: {
        colorBgHeader: '#0a1426',
        colorBgBody: '#141d2e',
        colorBgContainer: '#0a1426',
        colorBgElevated: '#141d2e',
      },
      Menu: {
        colorItemBg: '#0a1426',
        colorSubItemBg: '#141d2e',
        colorItemBgSelected: '#141d2e',
      },
      Card: {
        colorBgContainer: '#0a1426',
      },
      Table: {
        colorBgContainer: '#0a1426',
        colorFillAlter: '#141d2e',
      },
    },
  },
  // 暗色主题 - 科技风格（现代暗色）
  'dark-tech': {
    algorithm: theme.darkAlgorithm,
    token: {
      colorPrimary: '#165dff',
      borderRadius: 6,
    },
    components: {
      Layout: {
        colorBgHeader: '#17171a',
        colorBgBody: '#232324',
        colorBgContainer: '#17171a',
        colorBgElevated: '#232324',
      },
      Menu: {
        colorItemBg: '#17171a',
        colorSubItemBg: '#232324',
        colorItemBgSelected: '#232324',
      },
      Card: {
        colorBgContainer: '#17171a',
      },
      Table: {
        colorBgContainer: '#17171a',
        colorFillAlter: '#232324',
      },
    },
  },
};

const AppContent: React.FC = () => {
  const [currentTheme] = useAtom(themeAtom);
  const [isInitialized, setIsInitialized] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // 初始化应用
    initializeApp();
    setIsInitialized(true);

    // 定义登出处理器
    const handleLogout = () => {
      console.log('[App] 接收到登出事件，执行跳转和清理...');
      // 强制跳转到登录页
      navigate('/login', { replace: true });
      // 跳转后，再清理Token
      removeToken();
    };

    // 订阅登出事件
    EventBus.on(UserEvents.Types.LOGOUT, handleLogout);

    // 组件卸载时取消订阅
    return () => {
      EventBus.off(UserEvents.Types.LOGOUT, handleLogout);
    };
  }, [navigate]);

  if (!isInitialized) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="系统初始化中..." />
      </div>
    );
  }

  return (
    <AntdApp>
      <ConfigProvider
        theme={themeConfig[currentTheme]}
        locale={zhCN}
      >
        <AppRouter />
        {/* 添加实盘配置对话框组件 */}
        <LiveStrategyConfigModal />
        {/* 添加交易通道配置对话框组件 */}
        <TradingChannelConfigModal />
      </ConfigProvider>
      <GlobalLoading />
    </AntdApp>
  );
};

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <AppContent />
    </BrowserRouter>
  );
};

export default App;