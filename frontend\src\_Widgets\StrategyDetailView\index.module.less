.centerContent {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.detailContent {
  // Add specific styles for the detail content area if needed
}

.chartContainer {
  margin-bottom: 24px;
  // Ensure chart container has appropriate dimensions if needed
}

.tradeTable {
  :global(.ant-table-tbody > tr > td) {
    padding: 8px 8px; // Reduce padding for smaller table cells
  }
  :global(.ant-table-thead > tr > th) {
    padding: 8px 8px;
  }
} 