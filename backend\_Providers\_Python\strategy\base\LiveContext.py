# LiveContext.py
# 实盘策略上下文，为策略提供访问行情、下单等接口。
# 用于实盘环境下策略与引擎的数据、指令交互。

import time
import json
import pandas as pd
import numpy as np

class BarArray:
    """兼容WTpy的K线批量对象，支持opens/highs/lows/closes/volumes属性访问"""
    def __init__(self, rec):
        self.opens = rec['open'] if 'open' in rec.dtype.names else np.array([])
        self.highs = rec['high'] if 'high' in rec.dtype.names else np.array([])
        self.lows = rec['low'] if 'low' in rec.dtype.names else np.array([])
        self.closes = rec['close'] if 'close' in rec.dtype.names else np.array([])
        self.volumes = rec['volume'] if 'volume' in rec.dtype.names else np.array([])
        self.times = rec['time'] if 'time' in rec.dtype.names else np.array([])
    def __len__(self):
        return len(self.opens)

class LiveContext:
    '''
    实盘策略上下文，为策略提供访问行情、下单等接口。
    '''
    __slots__ = [
        '_strategy_id', '_engine', '_redis', 'current_channel_id', '_current_bar_date', '_current_bar_time',
        # 允许动态添加 commodity_info
        'commodity_info',
        'initial_capital'  # 新增：支持初始资金动态赋值
    ]
    def __init__(self, strategy_id: str, engine, channel_id: str):
        self._strategy_id = strategy_id
        self._engine = engine
        self._redis = engine.redis
        self.current_channel_id = channel_id  # 新增：当前交易通道ID
        self._current_bar_date = None  # 当前K线日期
        self._current_bar_time = None  # 当前K线时间
        self.commodity_info = {}  # 新增：初始化 commodity_info 字典
        self.initial_capital = 0  # 新增：初始化 initial_capital

    def get_current_channel_id(self):
        '''获取当前交易通道ID'''
        return self.current_channel_id

    def stra_get_bars(self, code: str, period: str, bar_cnt: int):
        '''从引擎缓存获取K线数据，并返回兼容WTpy的BarArray对象'''
        cache_key = (code, period)
        
        if cache_key not in self._engine.data_cache:
            self._engine.logger.warning(f"策略 {self._strategy_id} 尝试获取未缓存的数据: {code} on {period}")
            return None
        all_bars = self._engine.data_cache[cache_key]
        if not all_bars:
            return None
        df = pd.DataFrame(all_bars)
        if 'time' in df.columns:
            df = df.sort_values('time', ascending=True)
        rec = df.tail(bar_cnt).to_records(index=False)
        self._engine.logger.info(f"[stra_get_bars] 读取缓存，cache_key={cache_key} bars.len={len(rec)}")
        return BarArray(rec)

    def stra_set_position(self, code: str, target_position: float):
        '''设置本策略在指定通道、品种的目标仓位，通道由current_channel_id决定'''
        strategy_id = self._strategy_id
        
        if strategy_id not in self._engine.strategy_positions:
            self._engine.strategy_positions[strategy_id] = {}
        
        self._engine.strategy_positions[strategy_id][code] = target_position

    # 返回值  当天日期yyyyMMdd，如果是回测模式下，则为回测当时的日期
    def stra_get_date(self):
        '''获取当前K线的日期整数（YYYYMMDD），直接返回 _current_bar_date。'''
        date_val = getattr(self, '_current_bar_date', None)
        if date_val is not None:
            return int(date_val)
        print('[LiveContext][调试] stra_get_date: _current_bar_date 未设置，返回0')
        return 0

    def stra_get_time(self):
        '''获取当前K线的时间整数（HHMM），直接返回 _current_bar_time。'''
        time_val = getattr(self, '_current_bar_time', None)
        if time_val is not None:
            return int(time_val)
        print('[LiveContext][调试] stra_get_time: _current_bar_time 未设置，返回0')
        return 0

    def stra_get_all_position(self):
        '''返回本策略的全部持仓，展平成{symbol: qty}，符合Wonder Trader接口规范'''
        strategy_id = self._strategy_id
        all_positions = self._engine.strategy_positions.get(strategy_id, {})
        return all_positions.copy()

    def stra_get_price(self, code: str):
        '''从缓存的K线中获取最新价格'''
        timeframe = self._engine.instancies[self._strategy_id].timeframe
        cache_key = (code, timeframe)
        
        if cache_key in self._engine.data_cache and self._engine.data_cache[cache_key]:
            self._engine.logger.info(f"[stra_get_price] 读取缓存，cache_key={cache_key}，close={self._engine.data_cache[cache_key][-1].get('close', 0.0)}")
            return self._engine.data_cache[cache_key][-1].get('close', 0.0)
        
        self._engine.logger.warning(f"stra_get_price: 在缓存中未找到 {code} 的价格数据")
        return 0.0

    def stra_get_comminfo(self, code):
        self._engine.logger.warning("stra_get_comminfo 暂未实现")
        return None
        
    def stra_prepare_bars(self, code, period, bar_cnt, isMain=False):
        self._engine.logger.info(f"策略 {self._strategy_id} 声明K线需求: {code} on {period} (此操作在实盘中自动处理)")
        return True 