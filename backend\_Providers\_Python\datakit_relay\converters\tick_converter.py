#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Tick数据转换器
将不同来源的Tick数据转换为 Wonder Trader 格式
"""

import logging
from typing import Dict, Any, Optional, Union
import struct

class TickConverter:
    """Tick数据转换器"""
    
    def __init__(self):
        """初始化转换器"""
        self.logger = logging.getLogger(__name__)
    
    def convert(self, exchange: str, code: str, data: Dict[str, Any]) -> Optional[bytes]:
        """
        转换Tick数据
        返回二进制数据，或者 None（如果转换失败）
        """
        try:
            # 创建缓冲区
            buffer = bytearray(1024)  # 预分配足够大的缓冲区
            
            # 写入头部
            buffer[0:4] = b'WTPY'
            
            # 写入数据类型 (2001 for Tick)
            struct.pack_into('<I', buffer, 4, 2001)
            
            # 跳过正文长度，稍后填充
            body_start = 12
            
            # 写入交易所
            exchange_bytes = exchange.encode('utf-8').ljust(16, b'\0')
            buffer[body_start:body_start+16] = exchange_bytes
            
            # 写入代码
            code_bytes = code.encode('utf-8').ljust(32, b'\0')
            buffer[body_start+16:body_start+48] = code_bytes
            
            # 写入日期
            date_str = data.get('date', '').ljust(8, '\0')
            buffer[body_start+48:body_start+56] = date_str.encode('utf-8')
            
            # 写入时间
            time_str = data.get('time', '').ljust(9, '\0')
            buffer[body_start+56:body_start+65] = time_str.encode('utf-8')
            
            # 写入价格
            struct.pack_into('<d', buffer, body_start+65, float(data.get('price', 0.0)))
            
            # 写入成交量
            struct.pack_into('<d', buffer, body_start+73, float(data.get('volume', 0.0)))
            
            # 写入成交额
            struct.pack_into('<d', buffer, body_start+81, float(data.get('turnover', 0.0)))
            
            # 写入持仓量
            struct.pack_into('<d', buffer, body_start+89, float(data.get('open_interest', 0.0)))
            
            # 写入买价
            bid_prices = data.get('bid_prices', [0.0] * 10)
            for i in range(10):
                price = bid_prices[i] if i < len(bid_prices) else 0.0
                struct.pack_into('<d', buffer, body_start+97+i*8, float(price))
            
            # 写入买量
            bid_volumes = data.get('bid_volumes', [0.0] * 10)
            for i in range(10):
                volume = bid_volumes[i] if i < len(bid_volumes) else 0.0
                struct.pack_into('<d', buffer, body_start+177+i*8, float(volume))
            
            # 写入卖价
            ask_prices = data.get('ask_prices', [0.0] * 10)
            for i in range(10):
                price = ask_prices[i] if i < len(ask_prices) else 0.0
                struct.pack_into('<d', buffer, body_start+257+i*8, float(price))
            
            # 写入卖量
            ask_volumes = data.get('ask_volumes', [0.0] * 10)
            for i in range(10):
                volume = ask_volumes[i] if i < len(ask_volumes) else 0.0
                struct.pack_into('<d', buffer, body_start+337+i*8, float(volume))
            
            # 计算正文长度
            body_length = 16 + 32 + 8 + 9 + 8*4 + 8*40
            
            # 写入正文长度
            struct.pack_into('<I', buffer, 8, body_length)
            
            # 返回数据
            return bytes(buffer[:body_start+body_length])
        
        except Exception as e:
            self.logger.error(f"转换Tick数据时出错: {e}")
            return None
