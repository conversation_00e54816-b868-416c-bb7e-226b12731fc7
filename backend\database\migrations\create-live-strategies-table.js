'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('live_strategies', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        comment: '实盘策略ID (UUID)'
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'CASCADE',
        comment: '用户ID'
      },
      username: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '用户名'
      },
      strategyId: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '原策略ID'
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '策略名称'
      },
      status: {
        type: Sequelize.ENUM('running', 'stopped', 'error', 'deploying'),
        allowNull: false,
        defaultValue: 'stopped',
        comment: '策略状态'
      },
      accountId: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '交易账户ID'
      },
      initialCapital: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        comment: '初始资金'
      },
      commissionRate: {
        type: Sequelize.DECIMAL(10, 6),
        allowNull: false,
        defaultValue: 0.0003,
        comment: '手续费率'
      },
      riskSettings: {
        type: Sequelize.JSON,
        allowNull: false,
        comment: '风险设置 JSON'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '创建时间'
      },
      startTime: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '启动时间'
      },
      lastUpdateTime: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '最后更新时间'
      },
      yaml: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: '策略YAML内容'
      }
    });

    // 创建索引
    await queryInterface.addIndex('live_strategies', ['userId'], {
      name: 'idx_live_strategies_user_id'
    });

    await queryInterface.addIndex('live_strategies', ['strategyId'], {
      name: 'idx_live_strategies_strategy_id'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('live_strategies');
  }
};
