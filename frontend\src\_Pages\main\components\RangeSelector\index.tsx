import React, { useEffect, useRef, useState, useCallback } from 'react';
import { EventBus } from '@/events/eventBus';
import { ChartEvents, MarketEvents } from '@/events/events';
import { ChartTimeRange, RangeSelectOperation } from '@/shared_types/chart';
import { Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import AnalysisModal from './AnalysisModal';
import { ToolbarButtonType } from '@/shared_types/ui';
import { FrontendConfig } from '@/shared_types/enviorment';

// 修改范围选择按钮组件接口
interface RangeSelectButtonProps {
  isActive: boolean;
  activeToolbarButton: ToolbarButtonType;
  onToolbarButtonChange: (buttonType: ToolbarButtonType) => void;
}

// 范围选择按钮组件
const RangeSelectButton: React.FC<RangeSelectButtonProps> = ({
  isActive,
  activeToolbarButton,
  onToolbarButtonChange
}) => {
  const handleClick = useCallback(() => {
    // 调用传入的处理函数来改变工具栏按钮状态
    if (activeToolbarButton === ToolbarButtonType.RANGE_SELECT) {
      onToolbarButtonChange(ToolbarButtonType.NONE);
    } else {
      onToolbarButtonChange(ToolbarButtonType.RANGE_SELECT);
    }
  }, [activeToolbarButton, onToolbarButtonChange]);

  return (
    <div
      style={{
        position: 'absolute',
        top: 5,
        right: 105,
        width: 20,
        height: 20,
        borderRadius: '20%',
        backgroundColor: isActive ? 'rgba(24, 144, 255, 0.3)' : 'rgba(128, 128, 128, 0.3)',
        color: 'var(--ant-color-text)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        zIndex: 100,
      }}
      onClick={handleClick}
    >
      <span style={{
        fontSize: 14,
        display: 'inline-block',
        marginTop: '-2px'
      }}>⊡</span>
    </div>
  );
};

// 获取时区偏移量（单位：小时）
const getTimezoneOffset = (timezone: string): number => {
  try {
    // 创建当前时间的Date对象
    const now = new Date();
    // 使用Intl.DateTimeFormat获取指定时区的时间
    const timeZoneDate = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      hour: 'numeric',
      hour12: false
    }).format(now);

    // 计算本地时间与指定时区时间的差值
    const localHour = now.getHours();
    const timeZoneHour = parseInt(timeZoneDate, 10);
    const offset = (timeZoneHour - localHour);

    return offset;
  } catch (error) {
    console.error('获取时区偏移量失败:', error);
    return 8; // 默认返回0，表示北京时间
  }
};

// 范围选择框组件
const RangeBox: React.FC<{
  range: ChartTimeRange;
}> = ({ range }) => {
  const [isAnalyzeModalVisible, setIsAnalyzeModalVisible] = useState(false);
  const [kLineCount, setKLineCount] = useState(0);
  const [startIndex, setStartIndex] = useState(0);
  const [endIndex, setEndIndex] = useState(0);

  const [config, setConfig] = useState<FrontendConfig | null>(null);

  const hourOffset = getTimezoneOffset(config?.timezone || 'Asia/Shanghai');

  const handleOperation = useCallback((operation: RangeSelectOperation) => {

    // 从lightweight-charts获取的区域时间 range
    // 将区域时间转换为标准时间戳（lightweight-charts中使用的是偏移过的时间戳）
    const fromTimestamp = typeof range.fromTime === 'number' ? range.fromTime :
      typeof range.fromTime === 'string' ? Date.parse(range.fromTime) / 1000 :
        Date.UTC(range.fromTime.year, range.fromTime.month - 1, range.fromTime.day) / 1000;

    const toTimestamp = typeof range.toTime === 'number' ? range.toTime :
      typeof range.toTime === 'string' ? Date.parse(range.toTime) / 1000 :
        Date.UTC(range.toTime.year, range.toTime.month - 1, range.toTime.day) / 1000;


    if (operation === RangeSelectOperation.ANALYZE) {
      openAnalyzeModal();
    } else {
      EventBus.emit(ChartEvents.Types.RANGE_OPERATION, {
        range,
        operation
      });
    }
  }, [range]);

  const handleAnalyzeOk = (shapes: any[], name: string) => {
    console.log('分析结果:', shapes, '名称:', name);
    setIsAnalyzeModalVisible(false);
    // 关闭区域选择模式
    EventBus.emit(ChartEvents.Types.RANGE_SELECT_MODE_CHANGED, {
      enabled: false
    });
  };

  const openAnalyzeModal = () => {
    // 重新触发计算K线数量的事件，确保数据最新
    EventBus.emit(MarketEvents.Types.CALCULATE_BARCOUNT, {
      startTime: range.fromTime,
      endTime: range.toTime,
    });
    // 打开对话框
    setIsAnalyzeModalVisible(true);
    // 注意：不再在此处关闭区域选择模式，保持选择状态直到对话框关闭
  };

  const items: MenuProps['items'] = [
    {
      key: RangeSelectOperation.ANALYZE,
      label: '分析选中区域',
      onClick: () => handleOperation(RangeSelectOperation.ANALYZE)
    },
    {
      key: RangeSelectOperation.ZOOM,
      label: '缩放到选中区域',
      onClick: () => handleOperation(RangeSelectOperation.ZOOM)
    }
  ];

  useEffect(() => {
    // 监听 K线 计算结果事件
    const subscription = EventBus.on(MarketEvents.Types.BARS_COUNT_RESULT, (payload: MarketEvents.BarCountResult) => {
      console.log('K线数量:', payload.count, '开始索引:', payload.startIndex, '结束索引:', payload.endIndex);
      setKLineCount(payload.count);
      setStartIndex(payload.startIndex);
      setEndIndex(payload.endIndex);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // 当range变化时，立即计算K线数量，而不是等到打开对话框时
  useEffect(() => {
    // 发布计算 K线 数量的事件
    EventBus.emit(MarketEvents.Types.CALCULATE_BARCOUNT, {
      startTime: range.fromTime,
      endTime: range.toTime,
    });
  }, [range]);

  return (
    <>
      <div
        style={{
          position: 'absolute',
          left: range.fromX,
          top: 0,
          width: range.toX - range.fromX,
          height: '100%',
          backgroundColor: 'rgba(24, 144, 255, 0.1)',
          border: '1px solid rgba(24, 144, 255, 0.3)',
          pointerEvents: 'none',
          zIndex: 99
        }}
      >
        {/* 添加索引范围标签 */}
        {range.fromIndex !== undefined && range.toIndex !== undefined && (
          <div
            style={{
              position: 'absolute',
              top: 5,
              left: 5,
              padding: '2px 6px',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              borderRadius: '2px',
              fontSize: '12px',
              color: '#fff',
              pointerEvents: 'none'
            }}
          >
            {range.fromIndex} - {range.toIndex}
          </div>
        )}
        
        <Dropdown menu={{ items }} trigger={['click']} placement="bottomRight">
          <div
            style={{
              position: 'absolute',
              top: 5,
              right: 5,
              width: 20,
              height: 20,
              backgroundColor: 'rgba(128, 128, 128, 0.3)',
              borderRadius: '20%',
              cursor: 'pointer',
              zIndex: 100,
              pointerEvents: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span style={{
              fontSize: 14,
              display: 'inline-block',
              marginTop: '-2px',
              color: 'var(--ant-color-text)',
              opacity: 0.85
            }}>⚙</span>
          </div>
        </Dropdown>
      </div>

      <AnalysisModal
        visible={isAnalyzeModalVisible}
        onOk={handleAnalyzeOk}
        onCancel={() => {
          setIsAnalyzeModalVisible(false);
          // 关闭区域选择模式
          EventBus.emit(ChartEvents.Types.RANGE_SELECT_MODE_CHANGED, {
            enabled: false
          });
        }}
        kLineCount={kLineCount}
        startIndex={startIndex}
        endIndex={endIndex}
      />
    </>
  );
};

// 修改主组件接口
interface RangeSelectorProps {
  activeToolbarButton: ToolbarButtonType;
  onToolbarButtonChange: (buttonType: ToolbarButtonType) => void;
}

// 主组件
export const RangeSelector: React.FC<RangeSelectorProps> = ({
  activeToolbarButton,
  onToolbarButtonChange
}) => {
  // 使用activeToolbarButton来确定isActive状态
  const isActive = activeToolbarButton === ToolbarButtonType.RANGE_SELECT;
  const rangeStartRef = useRef<{ x: number; time: Time; dataIndex?: number } | null>(null);
  const [selectedRange, setSelectedRange] = useState<ChartTimeRange | null>(null);

  const handleMouseDown = useCallback((event: ChartEvents.ChartMouseEvent) => {
    const time = event.chartInstance.timeScale().coordinateToTime(event.x);
    if (time) {
      // 尝试获取数据索引
      let dataIndex: number | undefined = undefined;
      try {
        // 使用chart.timeScale().getBaseIndex()获取基准索引
        const baseIndex = event.chartInstance.timeScale().getVisibleLogicalRange();
        if (baseIndex) {
          // 计算相对于baseIndex的索引位置
          dataIndex = Math.round(baseIndex.from + (event.x / event.chartInstance.timeScale().width()) * (baseIndex.to - baseIndex.from));
        }
      } catch (error) {
        console.error('获取数据索引失败:', error);
      }

      rangeStartRef.current = { x: event.x, time, dataIndex };

      // 禁用图表的原有事件
      event.chartInstance.applyOptions({
        handleScroll: false,
        handleScale: false,
      });
    }
  }, []);

  const handleMouseMove = useCallback((event: ChartEvents.ChartMouseEvent) => {
    if (!rangeStartRef.current) return;

    const currentTime = event.chartInstance.timeScale().coordinateToTime(event.x);
    if (currentTime) {
      // 尝试获取当前位置的数据索引
      let currentDataIndex: number | undefined = undefined;
      try {
        // 使用chart.timeScale().getBaseIndex()获取基准索引
        const baseIndex = event.chartInstance.timeScale().getVisibleLogicalRange();
        if (baseIndex) {
          // 计算相对于baseIndex的索引位置
          currentDataIndex = Math.round(baseIndex.from + (event.x / event.chartInstance.timeScale().width()) * (baseIndex.to - baseIndex.from));
        }
      } catch (error) {
        console.error('获取数据索引失败:', error);
      }

      const range: ChartTimeRange = {
        fromTime: rangeStartRef.current.time,
        toTime: currentTime,
        fromX: Math.min(rangeStartRef.current.x, event.x),
        toX: Math.max(rangeStartRef.current.x, event.x),
        top: 0,
        bottom: event.chartInstance.timeScale().height()
      };

      // 添加索引信息（如果有）
      if (rangeStartRef.current.dataIndex !== undefined && currentDataIndex !== undefined) {
        range.fromIndex = Math.min(rangeStartRef.current.dataIndex, currentDataIndex);
        range.toIndex = Math.max(rangeStartRef.current.dataIndex, currentDataIndex);
      }

      setSelectedRange(range);
    }
  }, []);

  const handleMouseUp = useCallback((event: ChartEvents.ChartMouseEvent) => {
    if (selectedRange) {
      EventBus.emit(ChartEvents.Types.RANGE_SELECTED, { range: selectedRange });

      // 恢复图表的原有事件，包括缩放功能
      event.chartInstance.applyOptions({
        handleScroll: true,
        handleScale: true,  // 恢复缩放功能
      });
    }
    rangeStartRef.current = null;
  }, [selectedRange]);

  useEffect(() => {
    const modeSubscription = EventBus.on(
      ChartEvents.Types.RANGE_SELECT_MODE_CHANGED,
      (payload: ChartEvents.RangeSelectModeChanged) => {
        // 注意：此处不再直接设置isActive，因为现在它由activeToolbarButton派生
        if (!payload.enabled) {
          setSelectedRange(null);
          rangeStartRef.current = null;
        }
      }
    );

    const mouseSubscription = EventBus.on(
      ChartEvents.Types.CHART_MOUSE_EVENT,
      (payload: ChartEvents.ChartMouseEvent) => {
        if (!isActive) return;

        switch (payload.type) {
          case 'mousedown':
            handleMouseDown(payload);
            break;
          case 'mousemove':
            handleMouseMove(payload);
            break;
          case 'mouseup':
            handleMouseUp(payload);
            break;
        }
      }
    );

    return () => {
      modeSubscription.unsubscribe();
      mouseSubscription.unsubscribe();
    };
  }, [isActive, handleMouseDown, handleMouseMove, handleMouseUp]);

  return (
    <>
      <RangeSelectButton
        isActive={isActive}
        activeToolbarButton={activeToolbarButton}
        onToolbarButtonChange={onToolbarButtonChange}
      />
      {selectedRange && <RangeBox range={selectedRange} />}
    </>
  );
};

export default RangeSelector; 