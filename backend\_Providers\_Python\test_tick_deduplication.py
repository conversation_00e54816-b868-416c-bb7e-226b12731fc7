#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试tdxserver的tick数据去重功能
验证重复tick数据不会被推送
"""

import socketio
import time
import threading
import requests
import json
from collections import defaultdict

# 测试配置
TDX_SERVER_URL = "http://127.0.0.1:5004"
TDX_API_URL = "http://127.0.0.1:5003"

# 测试品种
TEST_SYMBOL = {"market": "sh", "symbol": "000001", "name": "上证指数"}

class TickDeduplicationTester:
    def __init__(self):
        self.sio = socketio.Client()
        self.connected = False
        self.received_ticks = []
        self.tick_stats = defaultdict(int)
        self.duplicate_count = 0
        self.unique_count = 0
        
        # 注册事件处理
        self.sio.on('connect', self.on_connect)
        self.sio.on('disconnect', self.on_disconnect)
        self.sio.on('tick_data', self.on_tick_data)
        self.sio.on('subscribed', self.on_subscribed)
        self.sio.on('error', self.on_error)
    
    def on_connect(self):
        print("✓ 连接到TDX服务器成功")
        self.connected = True
    
    def on_disconnect(self):
        print("✗ 与TDX服务器断开连接")
        self.connected = False
    
    def on_subscribed(self, data):
        print(f"✓ 订阅成功: {data}")
    
    def on_error(self, data):
        print(f"✗ 错误: {data}")
    
    def on_tick_data(self, data):
        """接收tick数据并分析去重效果"""
        try:
            symbol = data.get('symbol', 'unknown')
            tick_data = data.get('data', {})
            timestamp = data.get('timestamp', 0)
            
            # 提取关键信息
            price = tick_data.get('price', 0)
            volume = tick_data.get('volume', 0)
            time_str = tick_data.get('time', '')
            
            # 创建唯一标识
            tick_key = f"{time_str}_{price}_{volume}"
            
            # 检查是否为重复数据
            if tick_key in self.tick_stats:
                self.duplicate_count += 1
                print(f"⚠️  检测到重复tick: {symbol} 时间={time_str} 价格={price} (第{self.tick_stats[tick_key] + 1}次)")
            else:
                self.unique_count += 1
                print(f"✓ 新tick数据: {symbol} 时间={time_str} 价格={price} 成交量={volume}")
            
            self.tick_stats[tick_key] += 1
            self.received_ticks.append({
                'symbol': symbol,
                'tick_key': tick_key,
                'price': price,
                'volume': volume,
                'time': time_str,
                'timestamp': timestamp,
                'receive_time': time.time()
            })
            
        except Exception as e:
            print(f"✗ 处理tick数据错误: {e}")
    
    def connect(self):
        """连接到服务器"""
        try:
            self.sio.connect(TDX_SERVER_URL)
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.connected:
            self.sio.disconnect()
    
    def subscribe(self, market, symbol):
        """订阅品种"""
        if self.connected:
            self.sio.emit('subscribe_tick', {
                'market': market,
                'symbol': symbol
            })
    
    def get_stats(self):
        """获取统计信息"""
        total_received = len(self.received_ticks)
        unique_ticks = len(self.tick_stats)
        
        return {
            'total_received': total_received,
            'unique_ticks': unique_ticks,
            'duplicate_count': self.duplicate_count,
            'unique_count': self.unique_count,
            'deduplication_rate': f"{(self.duplicate_count / max(total_received, 1)) * 100:.2f}%" if total_received > 0 else "0%"
        }

def get_server_cache_stats():
    """获取服务器端的缓存统计"""
    try:
        response = requests.get(f"{TDX_API_URL}/subscriptions", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return data.get('tick_cache', {})
        else:
            print(f"获取服务器统计失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"获取服务器统计异常: {e}")
        return None

def test_tick_deduplication():
    """测试tick数据去重功能"""
    print("=" * 60)
    print("TDX Tick数据去重功能测试")
    print("=" * 60)
    
    # 检查服务器状态
    print("\n1. 检查服务器状态...")
    try:
        response = requests.get(f"{TDX_API_URL}/quote?market=sh&code=000001", timeout=5)
        if response.status_code != 200:
            print("✗ TDX服务器不可用，请确保tdxserver.py正在运行")
            return
        print("✓ TDX服务器运行正常")
    except Exception as e:
        print(f"✗ 无法连接到TDX服务器: {e}")
        return
    
    # 创建测试客户端
    print("\n2. 创建测试客户端...")
    tester = TickDeduplicationTester()
    
    if not tester.connect():
        print("✗ 无法连接到Socket服务器")
        return
    
    time.sleep(1)
    
    # 订阅测试品种
    print(f"\n3. 订阅测试品种: {TEST_SYMBOL['name']} ({TEST_SYMBOL['market']}:{TEST_SYMBOL['symbol']})")
    tester.subscribe(TEST_SYMBOL['market'], TEST_SYMBOL['symbol'])
    time.sleep(2)
    
    # 监听数据
    print("\n4. 开始监听tick数据...")
    print("   观察时间: 30秒")
    print("   注意: 如果去重功能正常，相同时间戳的tick数据不会重复推送")
    print("-" * 60)
    
    start_time = time.time()
    test_duration = 30  # 测试30秒
    
    while time.time() - start_time < test_duration:
        time.sleep(1)
        
        # 每10秒显示一次统计
        elapsed = int(time.time() - start_time)
        if elapsed % 10 == 0 and elapsed > 0:
            stats = tester.get_stats()
            print(f"\n[{elapsed}s] 当前统计:")
            print(f"  总接收: {stats['total_received']} 条")
            print(f"  唯一tick: {stats['unique_ticks']} 条")
            print(f"  重复检测: {stats['duplicate_count']} 条")
            print(f"  去重率: {stats['deduplication_rate']}")
    
    print("\n" + "-" * 60)
    
    # 最终统计
    print("\n5. 测试结果统计...")
    final_stats = tester.get_stats()
    
    print(f"总接收tick数据: {final_stats['total_received']} 条")
    print(f"唯一tick数据: {final_stats['unique_ticks']} 条")
    print(f"客户端检测到重复: {final_stats['duplicate_count']} 条")
    print(f"去重效果: {final_stats['deduplication_rate']}")
    
    # 获取服务器端统计
    print("\n6. 服务器端缓存统计...")
    cache_stats = get_server_cache_stats()
    if cache_stats:
        print(f"缓存品种数: {cache_stats.get('total_cached_symbols', 'N/A')}")
        print(f"活跃品种数: {cache_stats.get('recent_active_symbols', 'N/A')}")
        print(f"内存使用: {cache_stats.get('cache_memory_usage', 'N/A')}")
    
    # 分析结果
    print("\n7. 结果分析...")
    if final_stats['duplicate_count'] == 0:
        print("✓ 去重功能正常工作，没有检测到重复的tick数据推送")
    else:
        print(f"⚠️  检测到 {final_stats['duplicate_count']} 条重复数据")
        print("   这可能表示去重逻辑需要调整")
    
    if final_stats['unique_count'] > 0:
        print(f"✓ 成功接收到 {final_stats['unique_count']} 条唯一tick数据")
    else:
        print("⚠️  没有接收到任何tick数据，请检查数据源")
    
    # 清理
    print("\n8. 清理连接...")
    tester.disconnect()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_tick_deduplication()
