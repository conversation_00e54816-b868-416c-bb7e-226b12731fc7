FROM python:3.9-slim

WORKDIR /app

# Install DuckDB dependencies if needed (unlikely for Python package, but keep in mind)
# RUN apt-get update && apt-get install -y --no-install-recommends build-essential && rm -rf /var/lib/apt/lists/*

COPY ../requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# Default command (overridden by docker-compose)
# EXPOSE 5002
CMD ["python", "strategy_server.py"]