const yahooFinance = require('yahoo-finance2').default;

async function getAllUSStocks() {
  try {
    console.log("开始获取美股列表...");
    
    // 获取标普500成分股（作为示例）
    const sp500 = await yahooFinance.quote('^GSPC');
    console.log("\n标普500指数信息:");
    console.log(sp500);
    
    // 获取纳斯达克100成分股
    const nasdaq100 = await yahooFinance.quote('^NDX');
    console.log("\n纳斯达克100指数信息:");
    console.log(nasdaq100);
    
    // 获取所有美股（通过搜索功能）
    // 注意：Yahoo Finance 没有直接的"获取所有股票"API
    // 这里使用搜索功能作为替代方案
    const queryOptions = { quotesCount: 10000, newsCount: 0 };
    const allSymbols = await yahooFinance.search('', queryOptions);
    
    console.log("\n美股搜索结果（前20个）:");
    console.table(allSymbols.quotes.slice(0, 20));
    
    // 提取并打印股票代码和名称
    console.log("\n股票代码和名称列表:");
    allSymbols.quques.forEach((quote, index) => {
      if (quote.isYahooFinance) {
        console.log(`${index + 1}. ${quote.symbol}: ${quote.shortName || quote.longName}`);
      }
    });
    
    console.log(`\n共获取到 ${allSymbols.quotes.length} 条股票数据`);
    
  } catch (error) {
    console.error("获取股票数据时出错:", error);
  }
}

// 执行函数
getAllUSStocks();