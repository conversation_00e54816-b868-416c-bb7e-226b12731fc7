const express = require('express');
const { EventEmitter } = require('events');
const { authenticateToken } = require('../middleware/auth');
const { strategyProvider } = require('../_Providers/strategyProvider');
const db = require('../models'); // Import the db object from models/index.js

// 错误处理函数
function getErrorMessage(error) {
  if (error && typeof error === 'object') {
    if ('message' in error) return error.message;
    return JSON.stringify(error);
  }
  return String(error);
}

class StrategyHandler extends EventEmitter {
  constructor() {
    super();
    this.router = express.Router();
    this.initRoutes();
  }

  /**
   * 初始化路由
   */
  initRoutes() {
    // 形态配置相关路由 - 需要认证
    this.router.delete('/shapes', authenticateToken, this.handleDeleteShape.bind(this));
    this.router.post('/shapes', authenticateToken, this.handleSaveShape.bind(this));
    this.router.get('/shapes', authenticateToken, this.handleGetUserShapes.bind(this));
    this.router.get('/shape/details', authenticateToken, this.handleGetShapeDetails.bind(this));

    // 策略相关路由 获取策略列表 获取制定策略回测数据 回测指定策略
    this.router.get('/list', authenticateToken, this.handleGetStrategies.bind(this));
    this.router.post('/backtest', authenticateToken, this.handleRunStrategyBacktest.bind(this));
    this.router.get('/getresult', authenticateToken, this.handleGetStrategyResult.bind(this));
    this.router.get('/details/:strategyName', authenticateToken, this.handleGetStrategyDetails.bind(this));

    // 新增：策略创建、复制、更新路由
    this.router.post('/create', authenticateToken, this.handleCreateStrategy.bind(this));
    this.router.post('/copy', authenticateToken, this.handleCopyStrategy.bind(this));
    this.router.post('/update', authenticateToken, this.handleUpdateStrategy.bind(this));

    // 新增：回测状态查询路由
    this.router.get('/backtest_status', authenticateToken, this.handleGetBacktestStatus.bind(this));
  }

  /**
   * 获取路由器
   */
  getRouter() {
    return this.router;
  }

  /**
   * 删除形态配置
   */
  async handleDeleteShape(req, res) {
    try {
      const { name } = req.query;
      const userId = req.user.id; // 从认证中获取用户ID

      // 参数验证
      if (!name) {
        console.log('[StrategyHandler] 删除形态配置失败: 名称为空');
        return res.status(400).json({
          success: false, 
          error: '形态配置名称不能为空' 
        });
      }

      // 使用 strategyProvider 删除形态配置
      const result = await strategyProvider.deleteShapeConfig(name, userId);

      // 检查操作是否成功
      if (!result.success) {
        console.error('[StrategyHandler] 删除形态配置失败:', result.error);
        return res.status(404).json({
          success: false,
          error: result.error
        });
      }

      // 返回成功结果
      return res.json({
        success: true,
        message: `形态配置删除成功: ${name}`
      });
    } catch (error) {
      // 处理任何其他未预期的异常
      console.error('[StrategyHandler] 删除形态配置时发生未知错误:', error);
      return res.status(500).json({
        success: false,
        error: getErrorMessage(error) || '删除形态配置时发生未知错误'
      });
    }
  }

  /**
   * 保存形态配置
   */
  async handleSaveShape(req, res) {
    try {
      const { name, shapeDetails, klines } = req.body;
      const userId = req.user.id; // 从认证中获取用户ID

      // 参数验证
      if (!name || !shapeDetails || !Array.isArray(shapeDetails)) {
        return res.status(400).json({
          success: false,
          error: 'Name and shapeDetails array are required'
        });
      }

      // 使用 strategyProvider 保存形态配置
      const result = await strategyProvider.saveShapeConfig(name, userId, klines, shapeDetails);

      res.json({
        success: true,
        message: 'Shape configuration saved successfully',
        data: result.data
      });
    } catch (error) {
      console.error('Error saving shape configuration:', error);
      res.status(500).json({
        success: false,
        error: getErrorMessage(error)
      });
    }
  }

  /**
   * 获取指定用户的形态设置
   */
  // 定义一个异步函数handleGetUserShapes，用于处理获取指定用户的形态设置的请求
  async handleGetUserShapes(req, res) {
    try {
      // 打印日志，记录当前操作和用户ID
      console.log('[StrategyHandler] 获取指定用户的形态设置, userId:', req.user.id);

      const userId = req.user.id; // 从认证中获取用户ID
      const result = await strategyProvider.getUserShapes(userId);

      // console.log('[StrategyHandler] 获取指定用户的形态设置, result:', result);

      res.json({
        success: true,
        data: result.data
      });
    } catch (error) {
      console.error('Error fetching user shapes:', error);
      res.status(500).json({
        success: false,
        error: getErrorMessage(error)
      });
    }
  }

  /**
   * 获取指定形态的详细配置
   */
  async handleGetShapeDetails(req, res) {
    try {
      const shapeName = req.query.name;
      if (!shapeName) {
        return res.status(400).json({
          success: false,
          error: '形态名称不能为空'
        });
      }

      const result = await strategyProvider.getShapeDetails(shapeName);

      res.json({
        success: true,
        data: result.data.map(item => ({
          type: item.indicatorType,
          selectedLine: item.lineName,
          params: item.indicatorParam,
          values: item.values,
          weight: item.weight
        }))
      });
    } catch (error) {
      console.error('Error fetching shape details:', error);
      res.status(500).json({
        success: false,
        error: getErrorMessage(error)
      });
    }
  }

  // ---- New Handlers for Strategy Operations ----

  /**
   * 处理获取策略列表的请求
   */
  async handleGetStrategies(req, res) {
    console.log('[StrategyHandler] Handling request to get strategies list');
    const userId = req.user?.id; // Get user ID from authentication

    if (!userId) {
      // Should not happen if authenticateToken middleware is used, but good practice
      return res.status(401).json({ success: false, error: 'User authentication failed or user ID missing' });
    }

    try {
      // --- 新增：获取用户拥有的自定义策略名称 ---
      let ownedCustomNames = [];
      try {
        const ownerships = await db.StrategyOwnership.findAll({
          where: { owner_id: userId },
          attributes: ['strategy_id'] // Only fetch the names
        });
        ownedCustomNames = ownerships.map(o => o.strategy_id);
        console.log(`[StrategyHandler] Found ${ownedCustomNames.length} owned custom strategies for user ${userId}`);
        // 移除详细列表输出，只保留数量信息
      } catch (dbError) {
        console.error(`[StrategyHandler] Error fetching strategy ownership for user ${userId}:`, dbError);
        // Decide if this is a fatal error or if we should proceed without custom strategies
        // For now, let's proceed but log the error. The provider will only fetch system strategies.
      }
      // --- 新增结束 ---

      // --- 修改：将自定义策略名称列表传递给 Provider ---
      const result = await strategyProvider.listStrategies(ownedCustomNames);
      console.log('[StrategyHandler] 获取策略列表返回');
      // --- 修改结束 ---

      if (result.success) {
        res.json({ success: true, data: result.data });
      } else {
        // Provider handled the error, return appropriate status (e.g., 500 for server connection issue)
        res.status(500).json({ success: false, error: result.error });
      }
    } catch (error) {
      // Catch unexpected errors in the handler itself
      console.error('[StrategyHandler] Unexpected error getting strategies list:', error);
      res.status(500).json({ success: false, error: getErrorMessage(error) || 'Internal server error' });
    }
  }

  /**
   * 处理运行策略回测的请求 (POST)
   * Expects strategy name and optional strategy_yaml in request body
   */
  async handleRunStrategyBacktest(req, res) {
    // --- 修改：从 req.body 获取参数 --- 
    const { name: strategyId, strategy_yaml: strategyYaml = '' } = req.body;
    
    console.log(`[StrategyHandler] Handling POST request to run backtest for: ${strategyId}`);

    if (!strategyId) {
      // --- 修改：错误信息反映需要 body 中的 name --- 
      return res.status(400).json({ success: false, error: 'Strategy name (name) is required in the request body' });
    }

    try {
      // 调用 Provider 的逻辑不变，它内部已经是 POST 到 Python 服务
      const result = await strategyProvider.runBacktest(strategyId, strategyYaml);
      if (result.success && result.task_id) {
        res.json({ 
          success: true, 
          task_id: result.task_id 
        });
      } else {
        res.status(500).json({ success: false, error: result.error });
      }
    } catch (error) {
      console.error(`[StrategyHandler] Unexpected error running backtest for ${strategyId}:`, error);
      res.status(500).json({ success: false, error: getErrorMessage(error) || 'Internal server error' });
    }
  }

  /**
   * 处理获取策略回测结果的请求
   */
  async handleGetStrategyResult(req, res) {
    const strategyName = req.query.name;
    console.log(`[StrategyHandler] Handling request to get backtest result for: ${strategyName}`);

    if (!strategyName) {
      return res.status(400).json({ success: false, error: 'Strategy name query parameter (?name=) is required' });
    }

    try {
      const result = await strategyProvider.getBacktestResult(strategyName);
      if (result.success) {
        res.json({ success: true, data: result.data });
      } else {
        // Could be 404 if result not found, or 500 if server error
        // Let's use 404 as a default if the provider failed to get it
        res.status(404).json({ success: false, error: result.error || 'Backtest result not found or error retrieving it' });
      }
    } catch (error) {
      console.error(`[StrategyHandler] Unexpected error getting backtest result for ${strategyName}:`, error);
      res.status(500).json({ success: false, error: getErrorMessage(error) || 'Internal server error' });
    }
  }

  // --- 修改：处理获取策略详细数据的请求 ---
  /**
   * 处理获取策略详细数据的请求 (指标、完整权益曲线、交易记录等)
   * GET /details/:strategyName
   */
  async handleGetStrategyDetails(req, res) {
    const strategyName = req.params.strategyName;
    // const limitQuery = req.query.limit; // 移除 limit 相关代码
    // let limit = 20; // 默认值

    console.log(`[StrategyHandler] Handling request to get details for: ${strategyName}`);

    if (!strategyName) {
      // 虽然路由参数保证了存在，但以防万一
      return res.status(400).json({ success: false, error: 'Strategy name route parameter is required' });
    }

    // // 解析和验证 limit 参数  --> 移除 limit 相关代码
    // if (limitQuery) { 
    //   try {
    //     const parsedLimit = parseInt(limitQuery, 10);
    //     if (!isNaN(parsedLimit) && parsedLimit > 0) {
    //       limit = parsedLimit;
    //     } else {
    //       console.warn(`[StrategyHandler] Invalid limit query parameter: ${limitQuery}. Using default: ${limit}`);
    //     }
    //   } catch (e) {
    //     console.warn(`[StrategyHandler] Error parsing limit query parameter: ${limitQuery}. Using default: ${limit}`);
    //   }
    // }

    try {
      // 假设 strategyProvider 中添加/修改了 getStrategyDetails 方法
      // 它负责调用 Python 服务的 GET /strategy/details/<strategy_Id>
      const result = await strategyProvider.getStrategyDetails(strategyName);
      
      if (result.success) {
        // 直接返回 Python 服务提供的完整数据
        res.json({ success: true, data: result.data });
      } else {
        // 根据 Python 服务返回的错误类型判断状态码可能更优，但 404/500 是合理的默认值
        res.status(result.status || 404).json({ success: false, error: result.error || 'Strategy details not found or error retrieving it' });
      }
    } catch (error) {
      console.error(`[StrategyHandler] Unexpected error getting strategy details for ${strategyName}:`, error);
      res.status(500).json({ success: false, error: getErrorMessage(error) || 'Internal server error' });
    }
  }
  // --- 修改结束 ---

  // ---- 新增 Handlers for Create, Copy, Update ----

  /**
   * 处理创建新策略的请求 (POST /create)
   */
  async handleCreateStrategy(req, res) {
    const { strategy_yaml } = req.body;
    const userId = req.user?.id; // 从认证中获取用户ID

    console.log(`[StrategyHandler] Handling request to create strategy for user: ${userId}`);

    if (!strategy_yaml) {
      return res.status(400).json({ success: false, error: 'strategy_yaml is required in the request body' });
    }
    if (!userId) {
      // 通常 authenticateToken 会保证 userId 存在，但加个保险
      return res.status(401).json({ success: false, error: 'User authentication failed or user ID missing' });
    }

    try {
      const result = await strategyProvider.createStrategy(strategy_yaml, userId);
      if (result.success) {
        // ---- 新增：保存策略拥有权记录 ----
        try {
          await db.StrategyOwnership.create({
            strategy_id: result.new_strategy_id, // 使用 Provider 返回的 UUID
            owner_id: userId,
            shared_with_ids: null // 初始创建时没有共享
          });
          console.log(`[StrategyHandler] Successfully created ownership record for strategy: ${result.new_strategy_id}, owner: ${userId}`);

          // 拥有权记录成功后才返回给前端
          res.status(201).json({ success: true, new_strategy_id: result.new_strategy_id });
        } catch (ownershipError) {
          console.error(`[StrategyHandler] Failed to create ownership record for strategy ${result.new_strategy_id} after creation:`, ownershipError);
          // 注意：此时 Python 服务已创建策略，但 Node 端所有权记录失败，存在不一致性
          // 可以考虑更复杂的补偿逻辑，但目前仅返回错误
          res.status(500).json({ success: false, error: 'Strategy created, but failed to record ownership.' });
        }
        // ---- 新增结束 ----
      } else {
        // 根据 Provider 返回的错误信息决定状态码，400 (无效YAML) 或 500 (其他错误)
        // Provider 内部错误处理可能已包含连接错误
        const statusCode = result.error?.includes('invalid') ? 400 : 500;
        res.status(statusCode).json({ success: false, error: result.error });
      }
    } catch (error) {
      console.error(`[StrategyHandler] Unexpected error creating strategy for user ${userId}:`, error);
      res.status(500).json({ success: false, error: getErrorMessage(error) || 'Internal server error' });
    }
  }

  /**
   * 处理复制策略的请求 (POST /copy)
   */
  async handleCopyStrategy(req, res) {
    // --- 修改这里：从 req.body 中解构 'name' 而不是 'source_strategy_name' ---
    const { new_name, id } = req.body;
    const userId = req.user?.id;

    // --- 修改变量名和日志以保持一致（可选） ---
    const source_strategy_id = id; // 将读取到的 name 赋值给内部变量
    console.log(`[StrategyHandler] Handling request to copy strategy: ${source_strategy_id} for user: ${userId}`);

    // --- 使用 source_strategy_name (即原来的 name) 进行校验 ---
    if (!source_strategy_id) {
      // --- 修改错误信息以反映实际需要的字段名 ---
      return res.status(400).json({ success: false, error: "'id' field is required in the request body" });
    }
    if (!userId) {
      return res.status(401).json({ success: false, error: 'User authentication failed or user ID missing' });
    }

    try {
      // --- 传递正确的 source_strategy_name 给 Provider ---
      const result = await strategyProvider.copyStrategy(source_strategy_id, userId, new_name);
      if (result.success) {
        // ---- 新增：保存策略拥有权记录 ----
        try {
          await db.StrategyOwnership.create({
            strategy_id: result.new_strategy_id, // 使用 Provider 返回的 UUID
            owner_id: userId,
            shared_with_ids: null // 初始复制时没有共享
          });
          console.log(`[StrategyHandler] Successfully created ownership record for copied strategy: ${result.new_strategy_id}, owner: ${userId}`);

          // 拥有权记录成功后才返回给前端
          res.status(201).json({ success: true, new_strategy_id: result.new_strategy_id });
        } catch (ownershipError) {
          console.error(`[StrategyHandler] Failed to create ownership record for strategy ${result.new_strategy_id} after copy:`, ownershipError);
          // 同样存在不一致性风险
          res.status(500).json({ success: false, error: 'Strategy copied, but failed to record ownership.' });
        }
        // ---- 新增结束 ----
      } else {
        // 可能是 404 (源策略不存在) 或 500 (读取/保存错误)
        const statusCode = result.error?.includes('not found') ? 404 : 500;
        res.status(statusCode).json({ success: false, error: result.error });
      }
    } catch (error) {
      console.error(`[StrategyHandler] Unexpected error copying strategy ${source_strategy_id} for user ${userId}:`, error);
      res.status(500).json({ success: false, error: getErrorMessage(error) || 'Internal server error' });
    }
  }

  /**
   * 处理更新策略的请求 (PUT /update)
   */
  async handleUpdateStrategy(req, res) {
    const { strategy_id: strategy_id, strategy_yaml } = req.body;
    const userId = req.user?.id; // 虽然 Python 端不强制使用 userId，但可以记录是谁操作的

    console.log(`[StrategyHandler] Handling request to update strategy: ${strategy_id} by user: ${userId}`);

    if (!strategy_id) {
      return res.status(400).json({ success: false, error: 'strategy_id is required in the request body' });
    }
    if (!strategy_yaml) {
      return res.status(400).json({ success: false, error: 'strategy_yaml is required in the request body' });
    }
    if (!userId) {
      // 增加 userId 校验，因为需要用它来检查所有权
      return res.status(401).json({ success: false, error: 'User authentication failed or user ID missing' });
    }

    try {
      // ---- 新增：检查策略所有权 ----
      console.log(`[StrategyHandler] Checking ownership for strategy: ${strategy_id}, user: ${userId}`);
      const ownership = await db.StrategyOwnership.findOne({ where: { strategy_id: strategy_id } });

      if (!ownership) {
        console.log(`[StrategyHandler] Ownership check failed: Strategy ${strategy_id} not found in ownership records.`);
        // 注意：这里返回 404，即使策略可能存在于 Python 端但无主。
        // 或者也可以返回 403？404 更符合资源未找到的语义。
        return res.status(404).json({ success: false, error: 'Strategy not found or ownership record missing.' });
      }

      if (ownership.owner_id !== userId) {
        console.log(`[StrategyHandler] Ownership check failed: User ${userId} does not own strategy ${strategy_id} (owner: ${ownership.owner_id}).`);
        return res.status(403).json({ success: false, error: 'You do not have permission to update this strategy.' });
      }
      console.log(`[StrategyHandler] Ownership check passed for strategy: ${strategy_id}, user: ${userId}`);
      // ---- 新增结束 ----

      // 所有权检查通过，继续调用 Provider
      const result = await strategyProvider.updateStrategy(strategy_id, strategy_yaml);
      if (result.success) {
        // 成功时返回 200 OK
        res.status(200).json({ success: true, message: result.message });
      } else {
        // 可能是 400 (无效YAML/名称不匹配), 403 (系统策略), 404 (未找到), 500 (DB错误)
        let statusCode = 500; // Default
        if (result.error) {
          if (result.error.includes('invalid') || result.error.includes('match')) statusCode = 400;
          else if (result.error.includes('cannot be modified')) statusCode = 403;
          else if (result.error.includes('not found')) statusCode = 404;
        }
        res.status(statusCode).json({ success: false, error: result.error });
      }
    } catch (error) {
      console.error(`[StrategyHandler] Unexpected error updating strategy ${strategy_id}:`, error);
      res.status(500).json({ success: false, error: getErrorMessage(error) || 'Internal server error' });
    }
  }

  // 新增：回测状态查询处理函数
  async handleGetBacktestStatus(req, res) {
    const taskId = req.query.task_id;
    if (!taskId) {
      return res.status(400).json({ success: false, error: 'task_id query parameter is required' });
    }
    try {
      const result = await strategyProvider.getBacktestStatus(taskId);
      if (result.success) {
        res.json({ success: true, status: result.status, result: result.result, error: result.error });
      } else {
        res.status(500).json({ success: false, error: result.error });
      }
    } catch (error) {
      console.error(`[StrategyHandler] Unexpected error getting backtest status for ${taskId}:`, error);
      res.status(500).json({ success: false, error: getErrorMessage(error) || 'Internal server error' });
    }
  }
}

// 创建并导出实例
const strategyHandler = new StrategyHandler();
module.exports = { strategyHandler };
