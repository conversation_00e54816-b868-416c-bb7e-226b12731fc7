import pandas as pd
import datetime
import json
import os
import traceback
import pytz
import struct
import sqlite3
import codecs
import sys
import configparser
from dateutil import parser
from dateutil.tz import gettz
import baostock as bs
import baostock as bs

# 设置编码环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 调试控制
DEBUG_MODE = os.environ.get('TDX_DEBUG', 'false').lower() == 'true'

def debug_print(*args, **kwargs):
    """调试输出函数，只有在DEBUG_MODE为True时才输出"""
    if DEBUG_MODE:
        print(*args, **kwargs)

# 读取配置文件
# 获取当前文件的目录
current_dir = os.path.dirname(__file__)

# 构建 config.json 的路径
config_path = os.path.join(current_dir, 'config.json')

# 加载 config.json
with open(config_path, 'r', encoding='utf-8') as f:
    config = json.load(f)

# ========== root路径机制 ===========
def find_project_root():
    """向上查找 backend 目录，再往上一层即为 root"""
    cur = os.path.abspath(os.path.dirname(__file__))
    while True:
        if os.path.isdir(os.path.join(cur, 'backend')):
            return cur
        parent = os.path.dirname(cur)
        if parent == cur:
            raise RuntimeError("未找到 backend 目录，无法定位项目根目录")
        cur = parent

PROJECT_ROOT = find_project_root()

def resolve_data_path(config_path):
    """将配置中的 root/xxx 替换为实际根目录路径"""
    if config_path.startswith('root/'):
        return os.path.join(PROJECT_ROOT, config_path[5:])
    return config_path
# ========== end root路径机制 ===========

# 获取TDX符号配置
tdx_symbols_path = resolve_data_path(config['tdx_symbols']['path'])
tdx_data_path = resolve_data_path(config['tdx_data']['path'])

# 定义美股数据库路径
uscodes_db_path = resolve_data_path('root/backend/_Providers/_Python/uscodes.sqlite')

def resolve_path(path):
    """解析任意相对路径（支持 ~、../、./）"""
    expanded = os.path.expanduser(path)  # 先处理 ~
    return os.path.abspath(expanded)     # 再转为绝对路径

def filter_main_board_stocks(code: str, exchange: str) -> bool:
    """过滤只保留主板股票"""
    # 上证主板：600开头
    if exchange == 'SSE' and code.startswith('600'):
        return True
    # 深证主板：000开头
    elif exchange == 'SZSE' and code.startswith('000'):
        return True
    return False

def get_stock_list(market_filter='all'):
    """获取股票列表，包括A股和香港股票
    Args:
        market_filter: 市场过滤器，可选值: 'all', 'a', 'sh', 'sz', 'hk'
    Returns:
        list: 股票列表，每个元素包含 code, name, market, exchange
    """
    try:
        # 使用通达信本地数据获取A股股票列表
        stocks = []
        sh_count = 0  # 上海股票计数
        sz_count = 0  # 深圳股票计数

        # 处理上海股票列表（A股或全部市场）
        if market_filter in ['all', 'a', 'sh']:
            try:
                sh_file_path = os.path.join(tdx_symbols_path, 'shs.tnf')
                sh_file_path = resolve_path(sh_file_path)
                debug_print(f"读取上海股票列表: {sh_file_path}")

                if os.path.exists(sh_file_path):
                    with open(sh_file_path, 'rb') as f:
                        f.seek(50)  # 跳过文件头
                        while True:
                            data = f.read(360)  # 修改为360字节
                            if not data or len(data) < 360:  # 检查数据长度
                                break

                            try:
                                code = data[0:6].decode('utf-8').strip()  # 股票代码
                                name = data[31:48].decode('gbk', errors='ignore').strip().replace('\x00', '')  # 从第32个字节开始读取股票名称，并清理\x00字符
                                if code and name:  # 确保代码和名称非空
                                    # 只保留主板股票
                                    if filter_main_board_stocks(code, 'SSE'):
                                        stocks.append({
                                            'code': code,
                                            'name': name,
                                            'market': 'STOCK',
                                            'exchange': 'SSE'
                                        })
                                        sh_count += 1
                            except UnicodeDecodeError:
                                continue  # 如果解码出错，跳过该记录

                    debug_print(f"成功读取上海股票列表，共 {sh_count} 只股票")
                else:
                    debug_print(f"上海股票列表文件不存在: {sh_file_path}")
            except Exception as e:
                debug_print(f"读取上海股票列表出错: {str(e)}")
                import traceback
                traceback.print_exc()

        # 处理深圳股票列表（A股或全部市场）
        if market_filter in ['all', 'a', 'sz']:
            try:
                sz_file_path = os.path.join(tdx_symbols_path, 'szs.tnf')
                sz_file_path = resolve_path(sz_file_path)
                debug_print(f"读取深圳股票列表: {sz_file_path}")

                if os.path.exists(sz_file_path):
                    with open(sz_file_path, 'rb') as f:
                        f.seek(50)  # 跳过文件头
                        while True:
                            data = f.read(360)  # 修改为360字节
                            if not data or len(data) < 360:  # 检查数据长度
                                break

                            try:
                                code = data[0:6].decode('utf-8').strip()  # 股票代码
                                name = data[31:48].decode('gbk', errors='ignore').strip().replace('\x00', '')  # 从第32个字节开始读取股票名称，并清理\x00字符
                                if code and name:  # 确保代码和名称非空
                                    # 只保留主板股票
                                    if filter_main_board_stocks(code, 'SZSE'):
                                        stocks.append({
                                            'code': code,
                                            'name': name,
                                            'market': 'STOCK',
                                            'exchange': 'SZSE'
                                        })
                                        sz_count += 1
                            except UnicodeDecodeError:
                                continue  # 如果解码出错，跳过该记录

                    debug_print(f"成功读取深圳股票列表，共 {sz_count} 只股票")
                else:
                    debug_print(f"深圳股票列表文件不存在: {sz_file_path}")
            except Exception as e:
                debug_print(f"读取深圳股票列表出错: {str(e)}")
                import traceback
                traceback.print_exc()

        # 处理香港股票列表（只在需要时添加）
        if market_filter in ['all', 'hk']:
            try:
                hk_file_path = os.path.join(tdx_symbols_path, 'code2targ.ini')
                hk_file_path = resolve_path(hk_file_path)
                debug_print(f"读取香港股票列表: {hk_file_path}")

                hk_count = 0  # 香港股票计数
                if os.path.exists(hk_file_path):
                    with open(hk_file_path, 'r', encoding='gbk', errors='ignore') as f:
                        for line in f: 
                            try:
                                # 分割CSV行
                                parts = line.strip().split(',')
                                if len(parts) >= 5:
                                    # 第一个字段是代码标识，第二个字段是名称，第三个字段是交易所代码
                                    # 第四个字段是实际代码
                                    code_id = parts[0].strip()
                                    name = parts[1].strip()
                                    exchange_code = parts[2].strip()
                                    actual_code = parts[3].strip()

                                    # 只处理香港交易所的股票（PH代表香港）
                                    if exchange_code == 'PH':
                                        stocks.append({
                                            'code': actual_code,
                                            'name': name,
                                            'market': 'STOCK',
                                            'exchange': 'HKEX'
                                        })
                                        hk_count += 1
                            except Exception as e:
                                debug_print(f"解析香港股票行出错: {line} - {str(e)}")
                                continue

                    debug_print(f"成功读取香港股票列表，共 {hk_count} 只股票")
                else:
                    debug_print(f"香港股票列表文件不存在: {hk_file_path}")
            except Exception as e:
                debug_print(f"读取香港股票列表出错: {str(e)}")
                import traceback
                traceback.print_exc()
        else:
            hk_count = 0

        debug_print(f"读取完成，共获取{len(stocks)}只股票（上海: {sh_count}, 深圳: {sz_count}, 香港: {hk_count}）")

        return stocks
    except Exception as e:
        debug_print(f"Error in get_stock_list: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

def get_future_list():
    """获取期货名称列表
    从 code2name.ini 文件读取期货代码、名称和交易所信息
    从 code2qhidx.ini 文件读取主连期货品种信息
    返回格式为 {code, name, market, exchange} 的列表，包含具体交割月份合约
    """
    try:
        # 交易所代码映射表 (通达信代码 -> 通用代码)
        exchange_map = {
            # 数字代码映射
            '47': 'CFFEX',  # 中金所
            '28': 'CZCE',   # 郑州商品
            '29': 'DCE',    # 大连商品
            '30': 'SHFE',   # 上海商品
            '66': 'GFE',    # 广州期货
            # 简称映射
            'CZ': 'CFFEX',  # 中金所
            'QZ': 'CZCE',   # 郑州商品
            'QD': 'DCE',    # 大连商品
            'QS': 'SHFE',   # 上海商品
            'GZ': 'GFE',    # 广州期货
            'QG': 'GFE',    # 广州期货 - 需要添加这个
            # 其他可能的简称
            'SHFE': 'SHFE',
            'DCE': 'DCE',
            'CZCE': 'CZCE',
            'CFFEX': 'CFFEX',
            'GFE': 'GFE'
        }

        futures = []
        # 初始化contract_futures变量，避免在文件不存在时引用错误
        contract_futures = []

        # 1. 从 code2name.ini 读取普通期货品种
        ds_file_path = os.path.join(tdx_symbols_path, 'code2name.ini')
        ds_file_path = resolve_path(ds_file_path)
        debug_print(f"读取期货列表: {ds_file_path}")

        # 品种基础信息存储在这里，用于后续生成具体合约
        future_base_info = {}

        if os.path.exists(ds_file_path):
            with open(ds_file_path, 'r', encoding='gbk', errors='ignore') as f:
                for line in f:
                    try:
                        # 分割CSV行
                        parts = line.strip().split(',')
                        if len(parts) >= 3:
                            code = parts[0].strip()
                            name = parts[1].strip()
                            exchange_code = parts[2].strip()

                            # 转换交易所代码
                            exchange = exchange_map.get(exchange_code, exchange_code)

                            if code and name:
                                # 保存品种基础信息
                                future_base_info[code] = {
                                    'name': name,
                                    'exchange': exchange
                                }

                                # 品种本身也加入列表
                                futures.append({
                                    'code': code,
                                    'name': name,
                                    'market': 'FUTURE',
                                    'exchange': exchange
                                })
                    except Exception as e:
                        debug_print(f"Error parsing line in futures file: {str(e)}")
                        continue

            debug_print(f"从code2name.ini加载了 {len(futures)} 个期货品种")

            # 2. 为每个品种生成具体交割月合约
            current_date = datetime.datetime.now().date()
            contract_futures = []

            for code, info in future_base_info.items():
                try:
                    # 生成未来交割月合约代码
                    contract_codes = generate_future_contract_codes(code, tdx_symbols_path, current_date)

                    # 将生成的合约添加到列表中
                    for contract_code in contract_codes:
                        # 提取年月部分 (YYMM)
                        if len(contract_code) > len(code):
                            yymm = contract_code[len(code):]
                            if len(yymm) == 4 and yymm.isdigit():
                                year = "20" + yymm[:2]
                                month = yymm[2:]

                                # 构建合约名称，例如：螺纹钢2406
                                contract_name = f"{info['name']}{year[-2:]}{month}"

                                contract_futures.append({
                                    'code': contract_code,
                                    'name': contract_name,
                                    'base_code': code,
                                    'market': 'FUTURE',
                                    'exchange': info['exchange'],
                                    'is_contract': True
                                })
                except Exception as e:
                    debug_print(f"为品种 {code} 生成合约时发生错误: {str(e)}")
                    continue

            debug_print(f"成功生成 {len(contract_futures)} 个期货具体交割合约")

            # 将具体合约添加到结果列表
            futures.extend(contract_futures)
        else:
            debug_print(f"期货代码文件不存在: {ds_file_path}")

        # 3. 从 code2qhidx.ini 读取主连期货品种
        main_futures = []
        qhidx_file_path = os.path.join(tdx_symbols_path, 'code2qhidx.ini')
        qhidx_file_path = resolve_path(qhidx_file_path)
        if os.path.exists(qhidx_file_path):
            debug_print(f"读取主连期货列表: {qhidx_file_path}")

            try:
                # 使用配置解析器读取INI文件
                config = configparser.ConfigParser()
                config.read(qhidx_file_path, encoding='gbk')

                # 检查是否有FL部分
                if 'FL' in config:
                    # 获取分类数量
                    category_qty = int(config['FL'].get('Num', '0'))

                    for i in range(1, category_qty + 1):
                        # 读取每个分类
                        name_key = f'Name{i}'
                        fl_key = f'FL{i}'

                        if name_key in config['FL'] and fl_key in config['FL']:
                            # 获取分类信息
                            name_info = config['FL'][name_key]
                            name_parts = name_info.split(',')
                            if len(name_parts) >= 1:
                                category_name = name_parts[0].strip()

                            # 获取期货代码列表
                            fl_codes = config['FL'][fl_key].split(',')

                            for code_str in fl_codes:
                                try:
                                    # 解析代码，格式: prefix_code
                                    if '_' in code_str:
                                        prefix, code = code_str.split('_')
                                        # 去掉可能的合约月份后缀，只保留品种代码部分
                                        product_code = ''.join([c for c in code if not c.isdigit()])

                                        # 获取交易所代码
                                        exchange = exchange_map.get(prefix, 'UNKNOWN')

                                        # 查找品种基本信息中的中文名称
                                        name = ""
                                        if product_code in future_base_info:
                                            name = future_base_info[product_code]['name']
                                        else:
                                            name = f"{product_code}"

                                        # 构建主连合约记录
                                        main_futures.append({
                                            'code': f"{product_code}0",  # 主连合约通常用0表示
                                            'name': f"{name}主连",
                                            'market': 'FUTURE',
                                            'exchange': exchange,
                                            'is_main': True
                                        })

                                        # 添加次连合约
                                        main_futures.append({
                                            'code': f"{product_code}L7",  # 次连合约用L7表示
                                            'name': f"{name}次连",
                                            'market': 'FUTURE',
                                            'exchange': exchange,
                                            'is_second': True
                                        })

                                        # 添加指数合约
                                        main_futures.append({
                                            'code': f"{product_code}L9",  # 指数合约用L9表示
                                            'name': f"{name}指数",
                                            'market': 'FUTURE',
                                            'exchange': exchange,
                                            'is_index': True
                                        })
                                except Exception as e:
                                    debug_print(f"解析主连期货代码出错: {code_str} - {str(e)}")
                                    continue
            except Exception as e:
                debug_print(f"解析主连期货文件出错: {str(e)}")
                import traceback
                traceback.print_exc()

        # 合并主连和普通期货列表
        all_futures = futures + main_futures
        debug_print(f"成功加载 {len(all_futures)} 个期货合约和品种 (品种: {len(futures) - len(contract_futures)}, 交割合约: {len(contract_futures)}, 主连/次连/指数: {len(main_futures)})")

        return all_futures
    except Exception as e:
        debug_print(f"Error in get_future_list: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

def get_us_stock_list():
    """从 uscodes.sqlite 数据库获取美股列表"""
    debug_print(f"[美股列表] 尝试从数据库获取美股列表: {uscodes_db_path}")
    us_stocks = []
    conn = None
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(uscodes_db_path):
            debug_print(f"[错误] 美股数据库文件不存在: {uscodes_db_path}")
            raise FileNotFoundError(f"US stock database file not found: {uscodes_db_path}")

        # 连接数据库 (只读模式)
        conn = sqlite3.connect(f'file:{uscodes_db_path}?mode=ro', uri=True)
        cursor = conn.cursor()

        # 查询数据
        cursor.execute("SELECT code, name, exchange, market FROM us_stocks")
        rows = cursor.fetchall()

        # 格式化数据
        for row in rows:
            us_stocks.append({
                'code': row[0],
                'name': row[1],
                'exchange': row[2],
                'market': row[3]
            })

        debug_print(f"[美股列表] 成功从数据库获取 {len(us_stocks)} 条美股记录")
        return us_stocks

    except sqlite3.Error as e:
        debug_print(f"[错误] 查询美股数据库时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e
    except Exception as e:
        debug_print(f"[错误] 获取美股列表时发生未知错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e
    finally:
        if conn:
            conn.close()
            debug_print("[美股列表] 数据库连接已关闭")

def get_stock_kline(symbol, period='1D', market='STOCK', exchange='', k_count=0):
    """获取股票/期货K线数据"""
    import time
    
    # 开始计时
    start_time = time.time()
    print(f"[tdx_kline_lib] 开始处理K线请求: {symbol}")
    
    try:
        # 再次打印处理后的参数，避免日志混淆
        debug_print(f"Processed parameters - symbol: {symbol}, period: {period}, market: {market}, exchange: {exchange}, k_count: {k_count}")

        if not symbol:
            raise ValueError("Symbol is required")

        # 交易所到通达信编码的映射
        exchange_to_tdx_code = {
            'SHFE': '30',    # 上海期货交易所
            'DCE': '29',     # 大连商品交易所
            'CZCE': '28',    # 郑州商品交易所
            'CFFEX': '47',   # 中国金融期货交易所
            'GFE': '66',     # 广州期货交易所
            'INE': '30',     # 上海国际能源交易中心(归属上期所)
            'SSE': 'sh',     # 上海证券交易所
            'SZSE': 'sz',    # 深圳证券交易所
            'BSE': 'bj'      # 北京证券交易所
        }

        # 期货市场处理
        if market == 'FUTURE':
            if not exchange:
                raise ValueError("Exchange is required for futures market")

            # 获取通达信交易所编码
            tdx_exchange = exchange_to_tdx_code.get(exchange)
            if not tdx_exchange:
                raise ValueError(f'Unknown exchange code: {exchange}')

            file_symbol = f"{tdx_exchange}#{symbol}"

            debug_print(f"处理期货K线: {symbol}, 文件符号: {file_symbol}, 周期: {period}")

            klines = []

            # 根据周期选择不同的处理逻辑
            if period == '1D':
                # 日线数据
                klines = read_tdx_day_data(tdx_data_path, "ds", "", file_symbol, is_future=True, kline_count=k_count)
            elif period == '1W':
                # 周线数据 - 合成自日线数据
                day_data = read_tdx_day_data(tdx_data_path, "ds", "", file_symbol, is_future=True, kline_count=k_count)
                klines = resample_day_data(day_data, 'week')
            elif period == '1M':
                # 月线数据 - 合成自日线数据
                day_data = read_tdx_day_data(tdx_data_path, "ds", "", file_symbol, is_future=True, kline_count=k_count)
                klines = resample_day_data(day_data, 'month')
            elif period in ['1m', '5m', '15m', '30m', '60m']:
                # 分钟线数据
                if period == '1m':
                    min_type = 1
                    klines = read_tdx_min_data(tdx_data_path, "ds", "", file_symbol, min_type, is_future=True, kline_count=k_count)
                elif period == '5m':
                    min_type = 5
                    klines = read_tdx_min_data(tdx_data_path, "ds", "", file_symbol, min_type, is_future=True, kline_count=k_count)
                else:
                    # 合成其他周期的分钟线
                    min5_data = read_tdx_min_data(tdx_data_path, "ds", "", file_symbol, 5, is_future=True, kline_count=k_count)
                    target_min = int(period.replace('m', ''))
                    klines = resample_min_data(min5_data, target_min)
            else:
                raise ValueError(f'Invalid period for futures: {period}')

            # 结束计时并打印
            end_time = time.time()
            duration = end_time - start_time
            print(f"[tdx_kline_lib] K线请求处理完成: {symbol}")
            print(f"[tdx_kline_lib] 总处理耗时: {duration:.3f}秒")
            print(f"[tdx_kline_lib] 返回K线数量: {len(klines)}")

            return klines

        # 股票市场处理 (原有代码)
        else:
            # 检查是否是指数代码（以.sh .sz .csi等结尾）
            if symbol.endswith(('.sh', '.sz', '.csi', '.SH', '.SZ', '.CSI')):
                # 去掉后缀
                if symbol.endswith(('.sh', '.SH')):
                    symbol = symbol[:-3]
                elif symbol.endswith(('.sz', '.SZ')):
                    symbol = symbol[:-3]
                elif symbol.startswith('60') or symbol.startswith('68') or symbol.startswith('900'):
                    symbol = symbol[:-4]

            try:
                # 解析股票代码，确定市场
                market_prefix = ''
                file_prefix = ''
                is_us_stock = False

                debug_print(f"交易所: {exchange}")

                # 如果指定了交易所，根据交易所设置前缀
                if exchange and exchange != '':
                    if exchange == 'SSE':
                        market_prefix = 'sh'
                        file_prefix = 'sh'
                    elif exchange == 'SZSE':
                        market_prefix = 'sz'
                        file_prefix = 'sz'
                    elif exchange == 'BSE':
                        market_prefix = 'bj'
                        file_prefix = 'bj'

                    # 港股应该不在这个目录里面，临时处理
                    elif exchange == 'HKEX': # 添加对香港交易所的处理
                        market_prefix = 'ds'
                        file_prefix = 'hk'

                    elif exchange in ['NASDAQ', 'NYSE', 'AMEX', 'ARCA', 'BATS']: # 再次确保修正美股的 file_symbol
                        debug_print("[美股处理] 检测到美股交易所:", exchange)
                        market_prefix = 'ds'        # 美股数据存放在 ds 目录
                        file_prefix = '74#'
                        is_us_stock = True
                        is_like_future = True       # 使用类似期货的文件名和解析方式
                    else:
                        debug_print(f"Unknown stock symbol: {symbol} exchange: {exchange}")

                        # 对于未知的交易所，可以返回错误或尝试默认逻辑
                        raise ValueError(f'Unknown stock symbol: {symbol} exchange: {exchange}')

                debug_print(f"处理股票K线: {symbol}, 周期: {period}, 前缀: {market_prefix}，美股：{is_us_stock}")

                klines = []

                # 根据周期选择不同的处理逻辑
                if period == '1D':
                    # 日线数据 美股的价格解析方式和期货是一样的
                    klines = read_tdx_day_data(tdx_data_path, market_prefix, file_prefix, symbol, is_future = is_us_stock, kline_count=k_count)
                elif period == '1W':
                    # 周线数据 - 合成自日线数据
                    day_data = read_tdx_day_data(tdx_data_path, market_prefix, file_prefix, symbol, is_future = is_us_stock, kline_count=k_count)
                    klines = resample_day_data(day_data, 'week')
                elif period == '1M':
                    # 月线数据 - 合成自日线数据
                    day_data = read_tdx_day_data(tdx_data_path, market_prefix, file_prefix, symbol, is_future = is_us_stock, kline_count=k_count)
                    klines = resample_day_data(day_data, 'month')
                elif period == '1m':
                    # 1分钟线
                    klines = read_tdx_min_data(tdx_data_path, market_prefix, file_prefix, symbol, 1, kline_count=k_count)
                elif period == '5m':
                    # 5分钟线
                    klines = read_tdx_min_data(tdx_data_path, market_prefix, file_prefix, symbol, 5, kline_count=k_count)
                elif period == '15m':
                    # 15分钟线 - 合成自5分钟数据
                    min5_data = read_tdx_min_data(tdx_data_path, market_prefix, file_prefix, symbol, 5, kline_count=k_count)
                    klines = resample_min_data(min5_data, 15)
                elif period == '30m':
                    # 30分钟线 - 合成自5分钟数据
                    min5_data = read_tdx_min_data(tdx_data_path, market_prefix, file_prefix, symbol, 5, kline_count=k_count)
                    klines = resample_min_data(min5_data, 30)
                elif period == '60m':
                    # 1小时线 - 合成自5分钟数据
                    min5_data = read_tdx_min_data(tdx_data_path, market_prefix, file_prefix, symbol, 5, kline_count=k_count)
                    klines = resample_min_data(min5_data, 60)
                else:
                    raise ValueError(f'Invalid period: {period}')

                # 如果是获取完整数据（k_count=0）且不是美股，进行除权处理
                if k_count == 0 and not is_us_stock and exchange in ['SSE', 'SZSE']:
                    try:
                        debug_print(f"开始获取除权数据并进行前复权处理: {symbol}")

                        # 获取除权除息数据
                        dividend_data = get_dividend_data(symbol, exchange)

                        if dividend_data:
                            # 应用前复权
                            klines = apply_forward_adjustment(klines, dividend_data)
                            debug_print(f"前复权处理完成，共处理 {len(dividend_data)} 个除权事件")
                        else:
                            debug_print(f"未获取到除权数据，跳过复权处理")

                    except Exception as e:
                        debug_print(f"除权处理出错，继续返回原始数据: {str(e)}")
                        import traceback
                        traceback.print_exc()

                # 结束计时并打印
                end_time = time.time()
                duration = end_time - start_time
                print(f"[tdx_kline_lib] K线请求处理完成: {symbol}")
                print(f"[tdx_kline_lib] 总处理耗时: {duration:.3f}秒")
                print(f"[tdx_kline_lib] 返回K线数量: {len(klines)}")

                return klines

            except Exception as e:
                debug_print(f"Error fetching data: {str(e)}")
                import traceback
                traceback.print_exc()
                raise e

    except Exception as e:
        debug_print(f"Unexpected error in get_stock_kline: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

def calculate_start_position(file_size, kline_count):
    """计算K线数据读取的起始位置
    
    Args:
        file_size: 文件大小（字节）
        kline_count: 需要的K线数量，0表示获取全部
        
    Returns:
        int: 起始位置（字节）
    """
    if kline_count <= 0:
        return 0
    
    # 计算需要的字节数
    needed_bytes = kline_count * 32
    
    # 计算起始位置
    start_position = file_size - needed_bytes
    
    # 确保起始位置是32的倍数
    start_position = (start_position // 32) * 32
    
    # 如果起始位置小于0，则设为0
    if start_position < 0:
        start_position = 0
    
    return start_position

def read_tdx_day_data(base_path, market_prefix, file_prefix, code,
                      is_future=False, kline_count=0):
    """
    读取通达信日线数据（只读需要的尾部，避免一次性读大文件）
    """
    import os, struct, datetime, pandas as pd
    import time

    # 开始计时
    start_time = time.time()
    print(f"[tdx_kline_lib] 开始读取日线数据: {code}")

    # 拼路径
    file_path = os.path.join(
        base_path, market_prefix, 'lday',
        f"{file_prefix}{code}.day"
    )
    file_path = os.path.abspath(file_path)

    if not os.path.exists(file_path):
        print(f"[tdx_kline_lib] 文件不存在: {file_path}")
        return []

    record_size = 32
    file_size   = os.path.getsize(file_path)

    # 计算要读的字节区间
    if kline_count <= 0:
        start_pos  = 0
        need_bytes = file_size
    else:
        need_bytes = min(kline_count * record_size, file_size)
        need_bytes = (need_bytes // record_size) * record_size   # 32 对齐
        start_pos  = file_size - need_bytes

    # 一次性读
    with open(file_path, 'rb') as f:
        f.seek(start_pos)
        buffer = f.read(need_bytes)

    record_count = len(buffer) // record_size
    klines = []

    for i in range(record_count):
        pos = i * record_size
        date_val = int.from_bytes(buffer[pos:pos+4], 'little')
        y, m, d  = date_val//10000, (date_val%10000)//100, date_val%100
        ts = int(pd.Timestamp(f'{y:04d}-{m:02d}-{d:02d}', tz='Asia/Shanghai').timestamp())

        if is_future:
            o, h, l, c, amt, vol = struct.unpack('<ffffff', buffer[pos+4:pos+28])
        else:
            is_etf = code.startswith(('51','511','513','518','56','588',
                                      '15','16','159','1590'))
            div    = 1000.0 if is_etf else 100.0
            o      = int.from_bytes(buffer[pos+4:pos+8],  'little') / div
            h      = int.from_bytes(buffer[pos+8:pos+12], 'little') / div
            l      = int.from_bytes(buffer[pos+12:pos+16],'little') / div
            c      = int.from_bytes(buffer[pos+16:pos+20],'little') / div
            amt    = struct.unpack('<f', buffer[pos+20:pos+24])[0]
            vol    = int.from_bytes(buffer[pos+24:pos+28], 'little')

        prev_close = klines[-1]['close'] if klines else c
        change     = c - prev_close
        change_pct = (change / prev_close * 100) if prev_close else 0.0

        klines.append(dict(
            time=ts, open=o, high=h, low=l, close=c,
            volume=float(vol), amount=float(amt),
            change=float(change), change_pct=float(change_pct)
        ))

    # 结束计时并打印
    end_time = time.time()
    duration = end_time - start_time
    print(f"[tdx_kline_lib] 日线数据读取完成: {code}")
    print(f"[tdx_kline_lib] 读取耗时: {duration:.3f}秒")
    print(f"[tdx_kline_lib] 读取记录数: {len(klines)}")
    # print(f"[tdx_kline_lib] 文件大小: {file_size}字节, 读取字节: {need_bytes}字节")
    # print(f"[tdx_kline_lib] 读取位置: {start_pos} -> {start_pos + need_bytes}")

    return klines

# default_timezone = config.get('default_timezone', 'Asia/Shanghai')  # 默认使用上海时区

default_timezone = 'Asia/Shanghai'
mytz = pytz.timezone(default_timezone)

def get_previous_workday(date):
    """获取上一个工作日的零点时间戳"""
    previous_day = date
    while True:
        previous_day -= datetime.timedelta(days=1)  # 逐天减去一天
        if previous_day.weekday() < 5:  # 0-4为周一到周五
            return int(datetime.datetime(previous_day.year, previous_day.month, previous_day.day, tzinfo=mytz).timestamp())

def read_tdx_min_data(base_path, market_prefix, file_prefix, code, min_type,
                      is_future=False, kline_count=0):
    """读取通达信分钟线数据（只读尾部，避免一次性读大文件）"""
    import os, struct, datetime, pandas as pd
    import time

    # 开始计时
    start_time = time.time()
    print(f"[tdx_kline_lib] 开始读取分钟线数据: {code}, 类型: {min_type}分钟")

    # 分钟线目录与扩展名
    if min_type == 1:
        min_dir, file_ext = "minline", "lc1"
    elif min_type == 5:
        min_dir, file_ext = "fzline", "lc5"
    else:
        raise ValueError(f"不支持的分钟线类型: {min_type}")

    file_path = os.path.join(base_path, market_prefix, min_dir,
                             f"{file_prefix}{code}.{file_ext}")
    file_path = os.path.abspath(file_path)

    if not os.path.exists(file_path):
        print(f"[tdx_kline_lib] 分钟线文件不存在: {file_path}")
        return []

    record_size = 32
    file_size = os.path.getsize(file_path)

    # 计算要读的字节区间
    if kline_count <= 0:
        start_pos = 0
        need_bytes = file_size
    else:
        need_bytes = min(kline_count * record_size, file_size)
        need_bytes = (need_bytes // record_size) * record_size
        start_pos = file_size - need_bytes

    with open(file_path, 'rb') as f:
        f.seek(start_pos)
        buffer = f.read(need_bytes)

    record_count = len(buffer) // record_size
    klines = []
    invalid_records = 0
    recentDaySessionDay = 0
    nightTradingCount = 0
    default_timezone = 'Asia/Shanghai'

    for i in range(record_count):
        pos = i * record_size

        # 日期时间解析
        date_time_bytes = buffer[pos:pos + 4]
        date_part = int.from_bytes(date_time_bytes[0:2], 'little')
        time_part = int.from_bytes(date_time_bytes[2:4], 'little')

        year  = (date_part // 2048) + 2004
        month = (date_part % 2048) // 100
        day   = (date_part % 2048) % 100
        hour  = time_part // 60
        minute = time_part % 60
        date_str = f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:00"

        try:
            isNightTrading = hour >= 17
            if hour < 17:
                recentDaySessionDay = int(pd.Timestamp(f"{year:04d}-{month:02d}-{day:02d}", tz=default_timezone).timestamp())
            elif recentDaySessionDay == 0:
                recentDaySessionDay = int((pd.Timestamp(f"{year:04d}-{month:02d}-{day:02d}", tz=default_timezone)
                                           - pd.Timedelta(days=1)).timestamp())

            dt = pd.to_datetime(date_str)
            dt = dt.tz_localize(default_timezone)
            timestamp = int(dt.timestamp())
            newTimestamp = (recentDaySessionDay + hour * 3600 + minute * 60
                            if isNightTrading else timestamp)

            if isNightTrading:
                nightTradingCount += 1

            # 价格与成交量
            open_price, high_price, low_price, close_price = struct.unpack('<ffff',
                                                                           buffer[pos+4:pos+20])
            amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
            volume = int.from_bytes(buffer[pos+24:pos+28], 'little')

            if amount > 0 and volume > 0 and amount / volume > close_price * 10:
                volume *= 100

            # ETF 判断
            is_etf = code.startswith(('51', '511', '513', '518', '56', '588',
                                      '15', '16', '159', '1590'))
            divisor = 1000.0 if is_etf else 1.0
            open_price /= divisor
            high_price /= divisor
            low_price  /= divisor
            close_price /= divisor

            # 涨跌幅
            if klines:
                prev_close = klines[-1]['close']
                change = close_price - prev_close
                change_pct = (change / prev_close) * 100 if prev_close else 0.0
            else:
                change = change_pct = 0.0

            klines.append({
                'time': newTimestamp,
                'open': float(open_price),
                'high': float(high_price),
                'low': float(low_price),
                'close': float(close_price),
                'volume': float(volume),
                'amount': float(amount),
                'change': float(change),
                'change_pct': float(change_pct)
            })

        except Exception as e:
            invalid_records += 1
            continue

    # 结束计时并打印
    end_time = time.time()
    duration = end_time - start_time
    print(f"[tdx_kline_lib] 分钟线数据读取完成: {code}")
    print(f"[tdx_kline_lib] 读取耗时: {duration:.3f}秒")
    print(f"[tdx_kline_lib] 读取记录数: {len(klines)}")
    print(f"[tdx_kline_lib] 无效记录数: {invalid_records}")
    print(f"[tdx_kline_lib] 夜盘记录数: {nightTradingCount}")
    print(f"[tdx_kline_lib] 文件大小: {file_size}字节, 读取字节: {need_bytes}字节")
    print(f"[tdx_kline_lib] 读取位置: {start_pos} -> {start_pos + need_bytes}")

    return klines

def adjust_night_trading_time(klines):
    """
    调整夜盘交易时间
    处理通达信数据中夜盘使用次日日期的问题

    Args:
        klines: K线数据列表，每个元素包含'time'键，值为秒级时间戳

    Returns:
        list: 调整后的K线数据列表
    """
    if not klines or len(klines) == 0:
        return klines

    result = []
    last_day_session_date = 0  # 初始为0
    adjusted_count = 0

    default_timezone = config.get('default_timezone', 'Asia/Shanghai')  # 默认使用上海时区
    mytz = pytz.timezone(default_timezone)

    def get_previous_workday(date):
        """获取上一个工作日的零点时间戳"""
        previous_day = date
        while True:
            previous_day -= datetime.timedelta(days=1)  # 逐天减去一天
            if previous_day.weekday() < 5:  # 0-4为周一到周五
                return int(datetime.datetime(previous_day.year, previous_day.month, previous_day.day, tzinfo=mytz).timestamp())

    for kline in klines:
        # 当前K线的时间戳（秒）
        time_sec = kline['time']
        dt = datetime.datetime.fromtimestamp(time_sec, tz=mytz)

        hour = dt.hour

        if hour >= 17:
            # 夜盘处理
            if last_day_session_date == 0:
                # 往前找上一个工作日
                previous_day = dt.date()  # 取日期部分
                last_day_session_date = get_previous_workday(previous_day)

            # 计算原时间在当天的秒数
            seconds_in_day = dt.hour * 3600 + dt.minute * 60 + dt.second
            # 调整为上一个工作日的日期 + 原时间的时分秒
            adjusted_time = last_day_session_date + seconds_in_day

            # 创建新K线对象
            adjusted_kline = kline.copy()
            adjusted_kline['time'] = adjusted_time
            result.append(adjusted_kline)
            adjusted_count += 1
        else:
            # 日盘，更新last_day_session_date为当前日期零点
            current_zero = datetime.datetime(dt.year, dt.month, dt.day, tzinfo=mytz)
            last_day_session_date = int(current_zero.timestamp())
            result.append(kline)

    debug_print(f"[tdx_kline_lib] 调整了 {adjusted_count} 条夜盘数据")
    return result

def resample_min_data(min_data, target_min):
    """将分钟数据重采样为目标周期

    Args:
        min_data: 分钟K线数据列表
        target_min: 目标分钟数 (15, 30, 60)

    Returns:
        list: 重采样后的K线数据列表
    """
    if not min_data:
        return []

    debug_print(f"将分钟数据重采样为{target_min}分钟数据")

    # 使用pandas进行重采样
    df = pd.DataFrame(min_data)

    # 确保数据按时间排序
    df = df.sort_values(by='time')

    # 转换时间为datetime并设置为亚洲/上海时区
    df['datetime'] = pd.to_datetime(df['time'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')

    # 计算对齐到目标周期的收盘时间点
    def align_to_close_time(dt, mins):
        """将时间对齐到指定分钟周期的收盘时间"""
        try:
            hour = dt.hour
            minute = dt.minute
            abs_minutes = hour * 60 + minute
            # 找到下一个符合周期的收盘时间点 (例如14:23 -> 14:30 for 15m/30m)
            # 修正: (abs_minutes // mins + 1) * mins 会导致 14:30 -> 15:00
            # 使用: ((abs_minutes + mins - 1) // mins) * mins
            next_period_end = ((abs_minutes + mins - 1) // mins) * mins

            # 检查是否跨天 (next_period_end >= 24 * 60)
            if next_period_end >= 1440: # 1440 minutes in a day
                new_hour = (next_period_end // 60) % 24 # Use modulo 24 to wrap around midnight
                new_minute = next_period_end % 60
                # Important: We need to return a Timestamp for the *next day* if we crossed midnight.
                # However, dt.replace doesn't handle date changes. Let pandas handle grouping.
                # For alignment purpose, let's clamp hour to 23:59 for replacement if it hits 24:00 exactly
                # Or maybe better, let the calculation proceed and rely on pandas grouping?
                # Let's log first and see the values.
                debug_print(f"[align_to_close_time DEBUG] Crossing midnight! dt={dt}, mins={mins}, abs_minutes={abs_minutes}, next_period_end={next_period_end}")
                # If next_period_end represents exactly midnight (1440), replace might fail.
                # Let's try letting the error happen and catch it, or adjust.
                # For now, let's see the calculated hours.
                calculated_hour = next_period_end // 60
                debug_print(f"  >> Calculated hour: {calculated_hour}, Calculated minute: {new_minute}")
                if calculated_hour >= 24:
                     debug_print(f"  >> ERROR condition met: new_hour >= 24")
                     # Decide on handling: error out, clamp, or return a special value?
                     # For now, let the potential error occur to confirm the log.
                     new_hour = calculated_hour # Keep the potentially invalid hour for logging/error
            else:
                new_hour = next_period_end // 60
                new_minute = next_period_end % 60

            # Log before replacing
            debug_print(f"[align_to_close_time DEBUG] dt={dt}, mins={mins} -> new_hour={new_hour}, new_minute={new_minute}")

            # Return the datetime object with replaced time parts
            return dt.replace(hour=new_hour, minute=new_minute, second=0, microsecond=0)

        except Exception as e:
             debug_print(f"[align_to_close_time ERROR] Failed for dt={dt}, mins={mins}. Error: {e}")
             # Decide how to handle error, e.g., return original dt or NaT
             return pd.NaT # Return Not-a-Time on error

    # 应用时间对齐，创建新的对齐时间列
    def safe_align_time(x):
        """安全的时间对齐函数，处理可能的错误"""
        try:
            result = align_to_close_time(x, target_min)
            # 检查结果是否为NaT
            if pd.isna(result):
                return None
            return result
        except Exception:
            return None
    
    df['aligned_time'] = df['datetime'].apply(lambda x: safe_align_time(x), axis=1)

    # 增加日志：打印对齐前后的样本数据
    debug_print("[resample_min_data DEBUG] Sample aligned times (Head):")
    debug_print(df[['datetime', 'aligned_time']].head())
    debug_print("[resample_min_data DEBUG] Sample aligned times (Tail):")
    debug_print(df[['datetime', 'aligned_time']].tail())

    # 移除对齐失败的行 (值为 NaT)
    original_count = len(df)
    df = df.dropna(subset=['aligned_time'])
    dropped_count = original_count - len(df)
    if dropped_count > 0:
        debug_print(f"[resample_min_data WARN] Dropped {dropped_count} rows due to alignment errors.")

    # 按对齐后的时间点分组
    grouped = df.groupby('aligned_time')

    resampled_data = []
    for aligned_time, group in grouped:
        if len(group) == 0:
            continue

        # 计算OHLC和成交量
        first_row = group.iloc[0]
        last_row = group.iloc[-1]

        # 必须使用第一行的open和最后一行的close，以及整个区间的最高最低
        if aligned_time is not None and not pd.isna(aligned_time) and hasattr(aligned_time, 'timestamp'):
            timestamp_value = int(aligned_time.timestamp())  # 使用对齐后的收盘时间
        else:
            # 如果aligned_time不是datetime对象或为None，跳过这条记录
            continue
            
        resampled_data.append({
            'time': timestamp_value,
            'open': float(first_row['open']),
            'high': float(group['high'].max()),
            'low': float(group['low'].min()),
            'close': float(last_row['close']),
            'volume': float(group['volume'].sum()),  # 直接求和，不做处理
            'amount': float(group['amount'].sum()),
            'change': float(last_row['close'] - first_row['open']),
            'change_pct': float((last_row['close'] / first_row['open'] - 1) * 100) if first_row['open'] > 0 else 0.0
        })

    # 确保按时间排序
    resampled_data.sort(key=lambda x: x['time'])

    debug_print(f"重采样后得到 {len(resampled_data)} 条{target_min}分钟线数据")

    # 验证时间点是否正确对齐到整点
    for i, item in enumerate(resampled_data[:5]):
        dt = datetime.datetime.fromtimestamp(item['time'], tz=datetime.timezone(datetime.timedelta(hours=8)))
        debug_print(f"#{i} 样本时间: {dt.strftime('%Y-%m-%d %H:%M:%S')} - 分钟: {dt.minute} - 小时: {dt.hour}")

        # 验证分钟是否对齐到正确的周期点
        if target_min == 15:
            assert dt.minute in [0, 15, 30, 45], f"15分钟K线时间点错误: {dt.minute}"
        elif target_min == 30:
            assert dt.minute in [0, 30], f"30分钟K线时间点错误: {dt.minute}"
        elif target_min == 60:
            assert dt.minute == 0, f"60分钟K线时间点错误: {dt.minute}"

    return resampled_data

def resample_day_data(day_data, target_period):
    """将日线数据重采样为周线或月线

    Args:
        day_data: 日线K线数据列表
        target_period: 目标周期 ('week' 或 'month')

    Returns:
        list: 重采样后的K线数据列表
    """
    if not day_data:
        return []

    debug_print(f"将日线数据重采样为{target_period}线数据")

    tz = config.get('default_timezone', 'Asia/Shanghai')

    # 使用pandas进行重采样
    df = pd.DataFrame(day_data)
    df['datetime'] = pd.to_datetime(df['time'], unit='s', utc=True).dt.tz_convert(tz)

    # 添加分组标识
    if target_period == 'week':
        # 使用日期的年份和周数作为分组标识
        df['period_group'] = df['datetime'].apply(lambda x: f"{x.year}-{x.isocalendar()[1]}")
    elif target_period == 'month':
        # 使用年月作为分组标识
        df['period_group'] = df['datetime'].apply(lambda x: f"{x.year}-{x.month}")
    else:
        raise ValueError(f"不支持的目标周期: {target_period}")

    # 按周期分组并聚合
    grouped = df.groupby('period_group')

    resampled_data = []
    for _, group in grouped:
        if len(group) == 0:
            continue

        # 确保按时间排序
        group = group.sort_values('datetime')

        # 计算OHLC
        first_row = group.iloc[0]
        last_row = group.iloc[-1]

        resampled_data.append({
            'time': int(group['datetime'].min().timestamp()),
            'open': float(first_row['open']),
            'high': float(group['high'].max()),
            'low': float(group['low'].min()),
            'close': float(last_row['close']),
            'volume': float(group['volume'].sum()),
            'amount': float(group['amount'].sum()),
            'change': float(last_row['close'] - first_row['open']),
            'change_pct': float((last_row['close'] / first_row['open'] - 1) * 100) if first_row['open'] > 0 else 0.0
        })

    # 按时间排序
    resampled_data.sort(key=lambda x: x['time'])

    period_name = "周" if target_period == 'week' else "月"
    debug_print(f"重采样后得到 {len(resampled_data)} 条{period_name}线数据")
    return resampled_data

def generate_future_contract_codes(symbol, tdx_symbols_path, current_date=None):
    """
    根据期货品种代码和当前日期，生成未来12个月的期货合约代码

    Args:
        symbol: 期货品种代码，如 RB, AU 等
        tdx_symbols_path: 通达信数据文件路径
        current_date: 当前日期，默认为今天

    Returns:
        list: 合约代码列表，格式为 [品种代码+YYMM, ...]
    """
    import datetime
    import re
    import calendar

    # 如果未提供当前日期，使用今天的日期
    if current_date is None:
        current_date = datetime.datetime.now().date()

    # 读取code2name.ini文件获取交割规则
    ds_file_path = os.path.join(tdx_symbols_path, 'code2name.ini')
    if not os.path.exists(ds_file_path):
        debug_print(f"找不到期货品种信息文件: {ds_file_path}")
        return []

    # 定义变量存储解析结果
    delivery_day = 15  # 默认交割日为15日
    available_months = list(range(1, 13))  # 默认为所有月份

    try:
        with open(ds_file_path, 'r', encoding='gbk', errors='ignore') as f:
            for line in f:
                parts = line.strip().split(',')
                if len(parts) < 5:
                    continue

                # 检查当前行是否匹配目标品种
                if parts[0].strip().upper() == symbol.upper():
                    # 获取最后一个字段，包含交割日期规则
                    rule_text = parts[-1]

                    # 解析交割日
                    day_match = re.search(r'(\d+)日', rule_text)
                    if day_match:
                        delivery_day = int(day_match.group(1))
                    elif '交易日' in rule_text:
                        # 如果是"10个交易日"这种格式，通常指每月月初开始的第10个交易日
                        trading_day_match = re.search(r'(\d+)个交易日', rule_text)
                        if trading_day_match:
                            # 简化处理，假设交易日约等于月初开始的天数
                            delivery_day = min(int(trading_day_match.group(1)) + 5, 28)
                    # 特殊处理"最后一个周一"这种格式
                    elif '最后' in rule_text and ('周一' in rule_text or '星期一' in rule_text):
                        # 使用特殊标记，表示要计算每月最后一个周一
                        delivery_day = -1  # 使用-1表示特殊规则：每月最后一个周一

                    # 解析月份 - 简化逻辑，直接查找"（数字、数字、...月）"格式
                    month_pattern = r'（([^）]+)月）'
                    if '（' not in rule_text:  # 处理全角括号和半角括号
                        month_pattern = r'\(([^)]+)月\)'

                    month_match = re.search(month_pattern, rule_text)

                    if month_match:
                        # 获取括号内的内容
                        month_text = month_match.group(1)
                        month_list = []

                        # 直接按"、"分隔
                        for m in month_text.split('、'):
                            if m.strip().isdigit():
                                month_list.append(int(m.strip()))

                        # 只有在成功解析到有效月份时才更新available_months
                        if month_list:
                            available_months = month_list
                            debug_print(f"成功解析到交易月份: {available_months}")
                        else:
                            debug_print(f"解析月份文本未找到有效月份，使用默认全部月份: {available_months}")
                    else:
                        debug_print(f"未找到月份描述，使用默认全部月份: {available_months}")

                    break

        debug_print(f"品种 {symbol} 的交割规则: 每月{delivery_day}日, 可用月份: {available_months}")
    except Exception as e:
        debug_print(f"解析期货品种交割规则出错: {str(e)}")
        import traceback
        traceback.print_exc()

    # 生成未来12个交割月的合约代码
    contract_codes = []
    year = current_date.year
    month = current_date.month

    # 获取当前月的最后一天
    _, last_day = calendar.monthrange(year, month)

    # 处理12个月
    for i in range(12):
        # 计算下一个月
        next_month = month + i
        next_year = year + (next_month - 1) // 12
        next_month = ((next_month - 1) % 12) + 1

        # 检查是否是可用交割月
        if next_month in available_months:
            # 生成合约到期日
            if delivery_day == -1:
                # 特殊处理：计算该月最后一个周一
                # 获取下个月的最后一天
                last_day = calendar.monthrange(next_year, next_month)[1]
                # 从月末向前查找第一个周一
                for day in range(last_day, 0, -1):
                    temp_date = datetime.date(next_year, next_month, day)
                    # 0表示周一
                    if temp_date.weekday() == 0:
                        delivery_date = temp_date
                        break
            else:
                # 标准处理：使用指定的交割日
                delivery_date = datetime.date(next_year, next_month, min(delivery_day, calendar.monthrange(next_year, next_month)[1]))

            # 如果当前日期已经超过了当月交割日，则跳过当月合约
            if i == 0 and current_date > delivery_date:
                continue

            # 生成YYMM格式
            contract_suffix = f"{next_year % 100:02d}{next_month:02d}"

            # 生成完整合约代码
            contract_code = f"{symbol}{contract_suffix}"
            contract_codes.append(contract_code)

    return contract_codes

def get_dividend_data(symbol, exchange, start_date=None, end_date=None):
    """
    获取股票的除权除息信息

    Args:
        symbol: 股票代码，如 '600000'
        exchange: 交易所代码，如 'SSE', 'SZSE'
        start_date: 开始日期，格式 'YYYY-MM-DD'，默认为None
        end_date: 结束日期，格式 'YYYY-MM-DD'，默认为None

    Returns:
        list: 除权除息信息列表，每个元素包含除权日期和复权因子等信息
    """
    try:
        # 转换交易所代码为baostock格式
        if exchange == 'SSE':
            bs_code = f"sh.{symbol}"
        elif exchange == 'SZSE':
            bs_code = f"sz.{symbol}"
        else:
            debug_print(f"不支持的交易所: {exchange}")
            return []

        debug_print(f"获取除权除息数据: {bs_code}")

        # 登录baostock
        lg = bs.login()
        if lg.error_code != '0':
            debug_print(f"baostock登录失败: {lg.error_msg}")
            return []

        # 获取复权因子数据
        rs_list = []
        rs_factor = bs.query_adjust_factor(code=bs_code, start_date=start_date, end_date=end_date)

        while (rs_factor.error_code == '0') & rs_factor.next():
            rs_list.append(rs_factor.get_row_data())

        # 登出baostock
        bs.logout()

        if not rs_list:
            debug_print(f"未获取到除权除息数据: {bs_code}")
            return []

        # 转换为DataFrame并处理
        df = pd.DataFrame(rs_list, columns=rs_factor.fields)

        # 转换数据类型
        df['dividOperateDate'] = pd.to_datetime(df['dividOperateDate'])
        df['foreAdjustFactor'] = pd.to_numeric(df['foreAdjustFactor'], errors='coerce')
        df['backAdjustFactor'] = pd.to_numeric(df['backAdjustFactor'], errors='coerce')
        df['adjustFactor'] = pd.to_numeric(df['adjustFactor'], errors='coerce')

        # 按除权日期排序
        df = df.sort_values('dividOperateDate')

        # 转换为列表返回
        dividend_data = []
        for _, row in df.iterrows():
            dividend_data.append({
                'date': row['dividOperateDate'].strftime('%Y-%m-%d'),
                'timestamp': int(row['dividOperateDate'].timestamp()),
                'fore_adjust_factor': float(row['foreAdjustFactor']),
                'back_adjust_factor': float(row['backAdjustFactor']),
                'adjust_factor': float(row['adjustFactor'])
            })

        debug_print(f"获取到 {len(dividend_data)} 条除权除息记录")
        return dividend_data

    except Exception as e:
        debug_print(f"获取除权除息数据出错: {str(e)}")
        import traceback
        traceback.print_exc()
        # 确保登出
        try:
            bs.logout()
        except:
            pass
        return []

def apply_forward_adjustment(klines, dividend_data):
    """
    对K线数据进行前复权处理

    Args:
        klines: K线数据列表，每个元素包含 time, open, high, low, close, volume, amount 等字段
        dividend_data: 除权除息数据列表，每个元素包含 timestamp, fore_adjust_factor 等字段

    Returns:
        list: 前复权后的K线数据列表
    """
    if not klines or not dividend_data:
        return klines

    debug_print(f"开始前复权处理，K线数量: {len(klines)}, 除权记录数量: {len(dividend_data)}")

    # 将除权数据按时间戳排序（从早到晚）
    dividend_data = sorted(dividend_data, key=lambda x: x['timestamp'])

    # 复制K线数据，避免修改原数据
    adjusted_klines = []

    for kline in klines:
        kline_time = kline['time']

        # 计算累积复权因子
        cumulative_factor = 1.0

        # 找到所有在当前K线时间之后的除权事件
        for dividend in dividend_data:
            if dividend['timestamp'] > kline_time:
                # 使用前复权因子
                cumulative_factor *= dividend['fore_adjust_factor']

        # 应用复权因子到价格数据
        adjusted_kline = kline.copy()
        adjusted_kline['open'] = float(kline['open'] * cumulative_factor)
        adjusted_kline['high'] = float(kline['high'] * cumulative_factor)
        adjusted_kline['low'] = float(kline['low'] * cumulative_factor)
        adjusted_kline['close'] = float(kline['close'] * cumulative_factor)

        # 成交量和成交额不需要复权
        # 重新计算涨跌幅
        if adjusted_klines:
            prev_close = adjusted_klines[-1]['close']
            adjusted_kline['change'] = float(adjusted_kline['close'] - prev_close)
            adjusted_kline['change_pct'] = float((adjusted_kline['change'] / prev_close * 100) if prev_close else 0.0)
        else:
            adjusted_kline['change'] = 0.0
            adjusted_kline['change_pct'] = 0.0

        adjusted_klines.append(adjusted_kline)

    debug_print(f"前复权处理完成，调整了 {len(adjusted_klines)} 条K线数据")
    return adjusted_klines