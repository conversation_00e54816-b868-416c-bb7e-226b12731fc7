import pandas as pd
import datetime
import pytz
import os

# 方法1: 检查pandas当前默认的时区
timestamp = pd.Timestamp.now()
print(f"pandas当前时间: {timestamp}")
print(f"pandas默认时区: {timestamp.tz}")

# 方法2: 检查没有时区信息时的时间戳转换
date_str = "2025-02-27"
ts_no_tz = pd.Timestamp(date_str)
timestamp_value = int(ts_no_tz.timestamp())
print(f"\n不指定时区时:")
print(f"日期字符串: {date_str}")
print(f"转换的时间戳: {timestamp_value}")
print(f"时间戳对应的UTC时间: {datetime.datetime.utcfromtimestamp(timestamp_value)}")
print(f"时间戳对应的本地时间: {datetime.datetime.fromtimestamp(timestamp_value)}")

# 方法3: 获取系统的当前时区
try:
    system_tz = datetime.datetime.now(datetime.timezone.utc).astimezone().tzinfo
    print(f"\n系统当前时区: {system_tz}")
except:
    print("\n无法获取系统时区")

# 方法4: 检查环境变量中的时区设置
tz_env = os.environ.get('TZ')
print(f"环境变量TZ设置: {tz_env if tz_env else '未设置'}")

# 方法5: 显式指定不同时区时的结果
shanghai_ts = pd.Timestamp(date_str, tz='Asia/Shanghai')
utc_ts = pd.Timestamp(date_str, tz='UTC')

print(f"\n上海时区时间戳值: {int(shanghai_ts.timestamp())}")
print(f"UTC时区时间戳值: {int(utc_ts.timestamp())}")

# 方法6: 获取当前本地时区名称(安全版)
try:
    local_tzname = datetime.datetime.now().astimezone().tzname()
    print(f"\n本地时区名称: {local_tzname}")
except:
    print("\n无法获取本地时区名称")