{"openapi": "3.0.0", "info": {"title": "Python Data Service API", "description": "Python 数据服务 API 文档，提供 akshare 数据访问接口", "version": "1.0.0"}, "servers": [{"url": "http://localhost:5000", "description": "Python 数据服务"}], "components": {"schemas": {"Period": {"type": "string", "enum": ["1m", "5m", "15m", "30m", "60m", "1D", "1W", "1M"], "description": "K线周期:\n- 1m: 1分钟\n- 5m: 5分钟\n- 15m: 15分钟\n- 30m: 30分钟\n- 60m: 1小时\n- 1D: 日线\n- 1W: 周线\n- 1M: 月线", "default": "1D"}, "Adjust": {"type": "string", "enum": ["qfq", "hfq", ""], "description": "复权方式:\n- qfq: 前复权\n- hfq: 后复权\n- '': 不复权", "default": "qfq"}, "Market": {"type": "string", "enum": ["STOCK", "FUTURE", "CRYPTO"], "description": "市场类型:\n- STOCK: 股票市场\n- FUTURE: 期货市场\n- CRYPTO: 加密货币市场"}, "Exchange": {"type": "string", "enum": ["SSE", "SZSE"], "description": "交易所:\n- SSE: 上海证券交易所\n- SZSE: 深圳证券交易所"}, "StockInfo": {"type": "object", "required": ["code", "name", "market", "exchange"], "properties": {"code": {"type": "string", "description": "股票代码", "example": "000001", "pattern": "^[0-9]{6}$"}, "name": {"type": "string", "description": "股票名称", "example": "平安银行"}, "market": {"$ref": "#/components/schemas/Market"}, "exchange": {"$ref": "#/components/schemas/Exchange"}}}, "KLineData": {"type": "object", "required": ["time", "open", "high", "low", "close", "volume"], "properties": {"time": {"type": "integer", "description": "时间戳（秒）", "example": 1705708800}, "open": {"type": "number", "description": "开盘价", "example": 10.5, "minimum": 0}, "high": {"type": "number", "description": "最高价", "example": 10.8, "minimum": 0}, "low": {"type": "number", "description": "最低价", "example": 10.2, "minimum": 0}, "close": {"type": "number", "description": "收盘价", "example": 10.6, "minimum": 0}, "volume": {"type": "number", "description": "成交量", "example": 1234567, "minimum": 0}}}, "ApiResponse": {"type": "object", "required": ["success"], "properties": {"success": {"type": "boolean", "description": "请求是否成功"}, "error": {"type": "string", "description": "错误信息，仅在 success 为 false 时存在"}}}}}, "paths": {"/health": {"get": {"summary": "健康检查", "description": "检查服务是否正常运行", "operationId": "healthCheck", "tags": ["系统"], "responses": {"200": {"description": "服务正常", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok"], "example": "ok"}, "timestamp": {"type": "string", "format": "date-time", "example": "2024-01-20T12:00:00Z"}}}}}}}}}, "/stock/list": {"get": {"summary": "获取A股股票列表", "description": "使用 akshare 获取A股所有股票的基本信息", "operationId": "getStockList", "tags": ["股票"], "responses": {"200": {"description": "成功获取股票列表", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "required": ["code", "name", "market", "exchange"], "properties": {"code": {"type": "string", "description": "股票代码", "example": "000001", "pattern": "^[0-9]{6}$"}, "name": {"type": "string", "description": "股票名称", "example": "平安银行"}, "market": {"type": "string", "description": "市场类型 (STOCK=股票市场, FUTURE=期货市场, CRYPTO=加密货币市场)", "enum": ["STOCK", "FUTURE", "CRYPTO"]}, "exchange": {"type": "string", "description": "交易所 (SSE=上海证券交易所, SZSE=深圳证券交易所)", "enum": ["SSE", "SZSE"]}}}}}}]}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/stock/kline": {"get": {"summary": "获取股票K线数据", "description": "使用 akshare 获取指定股票的K线数据，支持从分钟级别到月线的多个周期", "operationId": "getStockKLine", "tags": ["股票"], "parameters": [{"name": "symbol", "in": "query", "description": "股票代码", "required": true, "schema": {"type": "string", "pattern": "^[0-9]{6}$", "example": "000001"}}, {"name": "period", "in": "query", "description": "K线周期 (1m=1分钟, 5m=5分钟, 15m=15分钟, 30m=30分钟, 60m=1小时, 1D=日线, 1W=周线, 1M=月线)", "required": false, "schema": {"type": "string", "enum": ["1m", "5m", "15m", "30m", "60m", "1D", "1W", "1M"], "default": "1D"}}, {"name": "adjust", "in": "query", "description": "复权方式 (qfq=前复权, hfq=后复权)", "required": false, "schema": {"type": "string", "enum": ["qfq", "hfq", ""], "default": "qfq"}}], "responses": {"200": {"description": "成功获取K线数据", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/KLineData"}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}}