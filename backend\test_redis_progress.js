/**
 * Redis进度测试脚本
 * 用于测试Redis进度消息的发布和订阅
 */

const redis = require('redis');
const config = require('./config.json');

async function testRedisProgress() {
    console.log('[Redis测试] 开始测试Redis进度消息...');
    
    try {
        // 创建发布客户端
        const publisher = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        
        // 创建订阅客户端
        const subscriber = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        
        // 连接Redis
        await publisher.connect();
        await subscriber.connect();
        console.log('[Redis测试] Redis连接成功');
        
        // 订阅进度频道
        await subscriber.pSubscribe('stock_selection_progress:*', (message, channel) => {
            console.log(`[Redis测试] 收到消息 - 频道: ${channel}, 内容: ${message}`);
        });
        console.log('[Redis测试] 已订阅 stock_selection_progress:* 频道');
        
        // 模拟发布进度消息
        const testTaskId = 'test-task-123';
        const progressData = {
            taskId: testTaskId,
            stage: '测试阶段',
            current: 50,
            total: 100,
            selectedCount: 10,
            symbolInfo: '测试品种',
            progress: 50,
            timestamp: Date.now()
        };
        
        console.log('[Redis测试] 发布测试进度消息...');
        const channel = `stock_selection_progress:${testTaskId}`;
        await publisher.publish(channel, JSON.stringify(progressData));
        console.log(`[Redis测试] 已发布消息到频道: ${channel}`);
        
        // 等待一段时间观察结果
        setTimeout(async () => {
            console.log('[Redis测试] 测试完成，关闭连接');
            await publisher.disconnect();
            await subscriber.disconnect();
            process.exit(0);
        }, 3000);
        
    } catch (error) {
        console.error('[Redis测试] 测试失败:', error);
        process.exit(1);
    }
}

// 运行测试
testRedisProgress();
