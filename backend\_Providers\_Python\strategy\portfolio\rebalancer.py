# backend/_Providers/_Python/strategy/portfolio/rebalancing.py
import logging
from datetime import datetime, timedelta
from typing import Optional

class Rebalancer:
    """
    负责根据配置的调仓周期判断是否需要执行调仓。
    """
    def __init__(self, interval: str, logger: Optional[logging.Logger] = None):
        """
        初始化调仓器。

        Args:
            interval (str): 调仓周期 ('daily', 'weekly', 'monthly', 'Nd', e.g., '5d').
            logger (Optional[logging.Logger]): 日志记录器实例。
        """
        self.interval = interval.lower()
        self.logger = logger if logger else logging.getLogger(__name__)
        self._parse_interval()

    def _parse_interval(self):
        """解析调仓周期字符串。"""
        self.interval_type = None
        self.interval_days = 0

        if self.interval == 'daily':
            self.interval_type = 'daily'
        elif self.interval == 'weekly':
            self.interval_type = 'weekly'
        elif self.interval == 'monthly':
            self.interval_type = 'monthly'
        elif self.interval.endswith('d'):
            try:
                days = int(self.interval[:-1])
                if days > 0:
                    self.interval_type = 'ndays'
                    self.interval_days = days
                else:
                    self.logger.warning(f"Invalid day count in interval: {self.interval}. Defaulting to monthly.")
                    self.interval = 'monthly'
                    self.interval_type = 'monthly'
            except ValueError:
                self.logger.warning(f"Invalid format for N-day interval: {self.interval}. Defaulting to monthly.")
                self.interval = 'monthly'
                self.interval_type = 'monthly'
        else:
            self.logger.warning(f"Unsupported rebalance interval: {self.interval}. Defaulting to monthly.")
            self.interval = 'monthly'
            self.interval_type = 'monthly'

        self.logger.info(f"Rebalancer initialized with interval type: {self.interval_type}, days: {self.interval_days if self.interval_days else 'N/A'}")

    def should_rebalance(self, current_date_int: int, last_rebalance_date_int: int) -> bool:
        """
        判断当前日期是否需要调仓。

        Args:
            current_date_int (int): 当前日期 (YYYYMMDD).
            last_rebalance_date_int (int): 上次调仓日期 (YYYYMMDD).

        Returns:
            bool: True 如果需要调仓，否则 False.
        """
        if last_rebalance_date_int == 0: # 首次运行总需要调仓
            self.logger.debug("First run, rebalance required.")
            return True

        try:
            current_dt = datetime.strptime(str(current_date_int), '%Y%m%d')
            last_dt = datetime.strptime(str(last_rebalance_date_int), '%Y%m%d')
        except ValueError:
            self.logger.error(f"Invalid date format received: current={current_date_int}, last={last_rebalance_date_int}")
            return False # 日期格式错误，不调仓

        if current_dt <= last_dt:
             # self.logger.debug(f"Current date {current_date_int} not after last rebalance date {last_rebalance_date_int}. No rebalance.")
             return False # 防止日期回拨或同一天重复计算

        if self.interval_type == 'daily':
            should = current_date_int > last_rebalance_date_int
            # self.logger.debug(f"[Daily] Current: {current_date_int}, Last: {last_rebalance_date_int}. Rebalance: {should}")
            return should

        elif self.interval_type == 'weekly':
            # 简化逻辑：如果当前日期是一周的第一天（周一），并且距离上次调仓超过5天
            # 注意：这没有考虑节假日，更健壮的方法需要交易日历
            is_monday = current_dt.weekday() == 0
            days_passed = (current_dt - last_dt).days
            should = days_passed >= 5 # 至少过去5天（允许周末），更精确需要交易日判断
            # self.logger.debug(f"[Weekly] Current: {current_date_int}, Last: {last_rebalance_date_int}, Monday: {is_monday}, Days passed: {days_passed}. Rebalance: {should}")
            # 实际触发通常是看是否进入了新的周，或者周一是否过去了
            # 简化：只要过去了足够的天数就调仓
            return should

        elif self.interval_type == 'monthly':
            # 简化逻辑：如果当前月份与上次调仓月份不同
            # 注意：应该在月初第一个交易日调仓，这里简化为月份变化即触发
            should = current_dt.year > last_dt.year or current_dt.month > last_dt.month
            # self.logger.debug(f"[Monthly] Current: {current_date_int}, Last: {last_rebalance_date_int}. Rebalance: {should}")
            return should

        elif self.interval_type == 'ndays':
            # 简化逻辑：如果过去的日历日数量达到 N 天
            # 注意：应该计算交易日间隔，这里简化为日历日
            days_passed = (current_dt - last_dt).days
            should = days_passed >= self.interval_days
            # self.logger.debug(f"[Ndays={self.interval_days}] Current: {current_date_int}, Last: {last_rebalance_date_int}, Days passed: {days_passed}. Rebalance: {should}")
            return should

        return False # 默认不调仓 