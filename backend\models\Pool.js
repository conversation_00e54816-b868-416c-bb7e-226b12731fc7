'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Pool extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // 定义关联关系
      // 一个池子可能属于一个用户 (当 user_id 不为 NULL)
      Pool.belongsTo(models.User, {
        foreignKey: 'user_id',
        as: 'user', // 别名
        constraints: false // 由于 user_id 可以为 NULL，显式设置约束为 false，让数据库外键处理 NULL
                         // 实际的外键约束推荐通过 migration 添加
      });

      // 一个系统池子 (user_id is NULL) 对应一个元数据记录
      Pool.hasOne(models.SystemPoolMetadata, {
        foreignKey: 'pool_id',
        as: 'metadata' // 别名
                        // ON DELETE CASCADE 推荐通过 migration 添加
      });
      
      // 移除与分类的直接关联，改为通过中间表实现单向关联
    }
  }

  Pool.init({
    pool_id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '池子唯一ID'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true, // 允许 NULL，表示系统池
      comment: '关联的用户ID。NULL 表示系统池。非 NULL 值逻辑上关联 users.id。'
      // references 和 ON DELETE CASCADE 推荐通过 migration 添加
    },
    name: {
      type: DataTypes.STRING(128),
      allowNull: false,
      comment: '池子名称或内部标识符'
      // 唯一性约束需单独创建
    },
    description: {
      type: DataTypes.STRING(128),
      allowNull: true,
      defaultValue: '',
      comment: '池子描述'
    },
    is_public: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否公开 (主要对用户池有效)'
    },
    // 新增: JSON字段存储所有的股票列表
    symbols_json: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '以JSON格式存储的股票列表。格式为数组，例如: [{"symbol": "NASDAQ.STOCK.AAPL", "added_at": "2023-01-01T00:00:00Z", "notes": "看好"}]'
    }
    // 移除 category_id 字段
  }, {
    sequelize,
    modelName: 'Pool',
    tableName: 'pools',
    timestamps: false, // 不使用 Sequelize 的时间戳管理
    comment: '股票池主表 (用户自选池和系统预定义池)',
    // indexes: [] // 复杂的索引 (部分唯一, 覆盖) 推荐通过 migration 创建
  });

  return Pool;
}; 