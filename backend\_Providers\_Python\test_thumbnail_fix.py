#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试缩略图数据修复
验证5分钟级别数据的时间格式化是否正确
"""

import sys
import os
import json

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_min5_strategy():
    """测试5分钟级别策略的缩略图数据"""
    
    print("=== 测试5分钟级别策略缩略图数据修复 ===")
    
    try:
        from strategy.portfolio.portfolio_center import do_run_backtest
        
        # 运行5分钟级别回测
        yaml_file = "strategy/portfolio/kf_roc1_min5.yaml"
        print(f"运行回测: {yaml_file}")
        
        results = do_run_backtest(yaml_file)
        
        if results and 'equity_curve_thumbnail_data' in results:
            thumbnail_data = results['equity_curve_thumbnail_data']
            print(f"\n缩略图数据点数: {len(thumbnail_data)}")
            
            if len(thumbnail_data) >= 5:
                print("\n前5个数据点:")
                for i, point in enumerate(thumbnail_data[:5]):
                    print(f"  {i+1}. 日期: {point['date']}, 净值: {point['equity']}")
                
                print("\n后5个数据点:")
                for i, point in enumerate(thumbnail_data[-5:]):
                    print(f"  {len(thumbnail_data)-4+i}. 日期: {point['date']}, 净值: {point['equity']}")
                
                # 检查时间格式
                first_date = thumbnail_data[0]['date']
                if ' ' in first_date and ':' in first_date:
                    print(f"\n✅ 时间格式正确: 包含时分秒信息 ({first_date})")
                else:
                    print(f"\n❌ 时间格式错误: 缺少时分秒信息 ({first_date})")
                
                # 检查是否有重复的时间点
                dates = [point['date'] for point in thumbnail_data]
                unique_dates = set(dates)
                if len(dates) == len(unique_dates):
                    print(f"✅ 无重复时间点: {len(dates)} 个唯一时间点")
                else:
                    print(f"❌ 存在重复时间点: {len(dates)} 总点数, {len(unique_dates)} 唯一点数")
                    
            else:
                print("数据点太少，无法进行详细分析")
                
        else:
            print("❌ 未找到缩略图数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_min5_strategy()
