import numpy as np
import talib
from typing import Optional

def calculate_value(opens: Optional[np.ndarray] = None,
                    highs: Optional[np.ndarray] = None,
                    lows: Optional[np.ndarray] = None,
                    closes: Optional[np.ndarray] = None,
                    volumes: Optional[np.ndarray] = None,
                    **kwargs) -> Optional[float]:
    """
    计算价格变化率 (Rate of Change - ROC) 在最后一个时间点的数值。
    符合 '正常计算模式的优化效能因子框架.md' 规范。

    Args:
        closes (Optional[np.ndarray]): 收盘价序列 (长度 >= period + 1)。
        **kwargs:
            period (int): ROC 的计算周期 (默认 10)。
            column (str): 指定使用哪个价格序列，应为 'close' (默认).

    Returns:
        Optional[float]: 计算出的最后一个时间点的 ROC 值，或 np.nan。
    """
    period = kwargs.get('period', 10)
    column = kwargs.get('column', 'close') # 确认使用 closes

    # --- Input Validation ---
    if column != 'close' or closes is None:
        # print(f"[因子计算:ROC] 输入错误: 需要 closes 数据。")
        return np.nan # 返回 NaN 更符合数值计算习惯
    if not isinstance(period, int) or period <= 0:
        # print(f"[因子计算:ROC] 参数错误: period ({period}) 必须是正整数。")
        return np.nan
    # TA-Lib 的 ROC 需要 period + 1 个数据点才能产生第一个有效值
    min_required_len = period + 1
    if len(closes) < min_required_len:
        # print(f"[因子计算:ROC] 数据不足: 需要至少 {min_required_len} 条数据，实际只有 {len(closes)} 条。")
        return np.nan
    # --- End Validation ---

    try:
        # 使用 TA-Lib 计算 ROC 序列
        # talib.ROC 需要 float64 输入
        roc_series = talib.ROC(closes.astype(np.float64), timeperiod=period)
        
        # 返回最后一个值 (TA-Lib 输出长度与输入相同，最后的值即最新值)
        # 如果最后一个值是 NaN（可能因为输入数据末尾有问题），则返回 NaN
        last_roc = roc_series[-1]
        
        # 将ROC值除以100转换为正确的百分比
        return float(last_roc) / 100 if not np.isnan(last_roc) else np.nan
    except Exception as e:
        # print(f"[因子计算:ROC] 计算 ROC({column}, {period}) 时出错: {e}")
        return np.nan # 计算出错返回 NaN

# --- Vectorized function removed/commented out as per new spec ---
# def calculate_series_vectorized(all_bars_data: pd.DataFrame, column: str, **kwargs) -> pd.Series:
#     """
#     计算变化率 (Rate of Change, ROC) 的向量化历史序列。
#     (此函数为旧版预计算框架设计，在实时计算模式下不使用)
#     ... (original implementation) ...
#     """
#     period = kwargs.get('period', 20)
#     factor_name = f"ROC{period}@{column}"
#     # ... (rest of the implementation)

# --- 旧函数已移除 ---
# def calculate_series(...) -> ...:
#     pass
# def calculate_value(...) -> ...:
#     pass

