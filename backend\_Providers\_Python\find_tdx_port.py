import psutil
import subprocess
import logging

# 配置日志 (可选，增加一些调试信息)
logging.basicConfig(level=logging.INFO, format='[%(asctime)s] %(message)s')

def get_tdx_pid():
    """通过进程名或路径特征获取通达信进程的PID"""
    logging.info("开始查找 tdxw.exe 进程...")
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            proc_name = proc.info['name']
            exe_path = proc.info['exe'] or ''

            # 匹配条件1：精确匹配进程名
            if proc_name and 'tdxw.exe' == proc_name.lower():
                logging.info(f"找到 tdxw.exe 进程 (名称匹配), PID: {proc.pid}")
                return proc.pid

            # 匹配条件2：路径特征匹配
            # 确保路径不为空再检查
            if exe_path and 'new_tdx' in exe_path and 'tdxw.exe' in exe_path:
                logging.info(f"找到 tdxw.exe 进程 (路径匹配), PID: {proc.pid}")
                return proc.pid

        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
        except Exception as e_find: # 捕获其他可能的异常
             logging.warning(f"查找进程时遇到错误 (PID: {proc.pid if 'proc' in locals() else 'N/A'}): {e_find}")
             continue
    logging.info("未找到 tdxw.exe 进程。")
    return None

def find_ip_by_pid(pid, port):
    """通过PID和端口号查找对应的网络连接信息"""
    if pid is None:
        logging.warning(f"PID 为空，无法查找端口 {port} 的连接。")
        return []

    # 构建命令
    cmd = f'netstat -ano | findstr "{pid}" | findstr ":{port}\\>"' # 更精确匹配端口号
    logging.info(f"执行命令: {cmd}")
    try:
        # 执行命令并捕获输出
        result = subprocess.check_output(cmd, shell=True, text=True, stderr=subprocess.PIPE) # 捕获错误流
        # 按行分割并解析每部分信息
        lines = [line.split() for line in result.strip().split('\n')]
        
        # 过滤掉不完整的行 (至少需要5部分：proto, local, remote, state, pid)
        valid_lines = [parts for parts in lines if len(parts) >= 5]
        
        parsed_data = [{
            'proto': parts[0],
            'local': parts[1],
            'remote': parts[2],
            'state': parts[3],
            'pid': parts[4]
        } for parts in valid_lines if parts[4] == str(pid)] # 再次确认PID匹配

        logging.info(f"找到 {len(parsed_data)} 条 PID={pid}, Port={port} 的连接。")
        return parsed_data
    except subprocess.CalledProcessError as e:
        # 命令执行失败或 findstr 未找到匹配项
        if e.returncode == 1 and not e.stderr: # findstr 没找到，正常情况
             logging.info(f"未找到 PID={pid}, Port={port} 的连接。")
        else: # 其他错误
             logging.error(f"执行 netstat 命令时出错: {e.stderr or e}")
        return []
    except Exception as e_exec: # 捕获其他可能的异常
         logging.error(f"查找连接时发生未知错误: {e_exec}")
         return []


# 主执行流程
if __name__ == "__main__":
    pid = get_tdx_pid()

    if pid:
        print(f"\n--- 正在查找与 PID {pid} 相关的行情连接 ---")

        # 查找普通行情端口 7709
        print("\n[普通行情接口]")
        connections_7709 = find_ip_by_pid(pid, 7709)
        if connections_7709:
            for conn in connections_7709:
                print(f"  - 服务器地址: {conn['remote']} (状态: {conn.get('state', '未知')})")
        else:
            print("  - 未找到连接")

        # 查找扩展行情端口 7727
        print("\n[扩展行情接口]")
        connections_7727 = find_ip_by_pid(pid, 7727)
        if connections_7727:
            for conn in connections_7727:
                print(f"  - 服务器地址: {conn['remote']} (状态: {conn.get('state', '未知')})")
        else:
            print("  - 未找到连接")

        # (可选) 查找其他可能端口, 如 7721 (交易?)
        # print("\n[其他端口 7721 (可能交易)]")
        # connections_7721 = find_ip_by_pid(pid, 7721)
        # if connections_7721:
        #      for conn in connections_7721:
        #          print(f"  - 服务器地址: {conn['remote']} (状态: {conn.get('state', '未知')})")
        # else:
        #      print("  - 未找到连接")

    else:
        print("错误：未能找到正在运行的通达信客户端进程 (tdxw.exe)。请确保通达信已启动。")
