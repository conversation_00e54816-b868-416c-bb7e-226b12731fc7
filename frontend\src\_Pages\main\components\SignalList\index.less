.signal-list-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    border-radius: 0;

    .list-content {
        flex: 1;
        overflow: auto;
        padding: 0 8px;
    }

    .signal-item {
        padding: 8px 16px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
            background-color: #f5f5f5;
        }

        .signal-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            .signal-time {
                color: #666;
                font-size: 12px;
            }

            .signal-type {
                padding: 2px 8px;
                border-radius: 2px;
                font-size: 12px;

                &.buy {
                    color: #52c41a;
                    background: #f6ffed;
                }

                &.sell {
                    color: #ff4d4f;
                    background: #fff2f0;
                }
            }

            .signal-price {
                font-weight: 500;
            }
        }
    }

    .signal-table {
        .signal-table-row {
            cursor: pointer;
            transition: background-color 0.3s;
            
            &:hover {
                background-color: var(--hover-color);
            }
        }
        
        .signal-price {
            font-weight: 500;
        }
        
        .signal-time {
            color: rgba(0, 0, 0, 0.65);
            font-size: 12px;
        }
    }
}

.signal-list-trigger {
  position: fixed;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  width: 40px;
  height: 40px;
  border-radius: 0 4px 4px 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ant-primary-color);
  border: none;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);

  &:hover {
    left: 25px;
  }
}

.signal-list-drawer {
  .signal-table {
    .ant-table {
      margin: -8px -16px;
    }

    .ant-table-thead > tr > th {
      padding: 8px 16px;
      height: 40px;
    }

    .ant-table-tbody > tr > td {
      padding: 6px 16px;
      height: 36px;
    }

    .signal-table-row {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--ant-primary-1);
        td {
          background-color: transparent !important;
        }
      }
    }
  }

  .signal-price {
    font-weight: 500;
    color: var(--ant-primary-color);
  }

  .signal-time {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
  }

  :global(.ant-drawer-content-wrapper) {
    [data-theme='dark'] & {
      background: #141414;

      .signal-time {
        color: rgba(255, 255, 255, 0.45);
      }

      .signal-table-row {
        &:hover {
          background-color: rgba(255, 255, 255, 0.08);
          td {
            background-color: transparent !important;
          }
        }
      }
    }
  }
} 