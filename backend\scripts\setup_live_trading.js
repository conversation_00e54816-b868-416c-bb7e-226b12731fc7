/**
 * 设置实盘交易目录结构和配置
 */

const fs = require('fs').promises;
const path = require('path');
const GroupConfigGenerator = require('../services/GroupConfigGenerator');

async function setupLiveTradingStructure() {
    console.log('=== 设置实盘交易目录结构 ===\n');

    try {
        const baseDir = path.join(process.cwd(), 'live_trading');
        
        // 1. 创建基础目录结构
        console.log('1. 创建目录结构...');
        const directories = [
            baseDir,
            path.join(baseDir, 'groups'),
            path.join(baseDir, 'common'),
            path.join(baseDir, 'templates'),
            path.join(baseDir, 'logs')
        ];

        for (const dir of directories) {
            try {
                await fs.mkdir(dir, { recursive: true });
                console.log(`✅ 创建目录: ${dir}`);
            } catch (error) {
                if (error.code !== 'EEXIST') {
                    throw error;
                }
                console.log(`📁 目录已存在: ${dir}`);
            }
        }

        // 2. 生成公共配置文件
        console.log('\n2. 生成公共配置文件...');
        const configGenerator = new GroupConfigGenerator();
        await configGenerator._generateCommonConfigs();
        console.log('✅ 公共配置文件生成完成');

        // 3. 创建README文件
        console.log('\n3. 创建说明文件...');
        const readmeContent = `# 实盘交易目录结构

## 目录说明

### groups/
用户组合目录，每个用户有独立的子目录：
- user_1/ - 用户1的交易组合
- user_2/ - 用户2的交易组合
- ...

每个用户目录包含：
- config.yaml - 组合配置文件
- strategies/ - 策略文件目录
- logs/ - 日志目录
- data/ - 数据目录

### common/
公共配置文件：
- parsers.yaml - OpenCTP行情配置
- traders.yaml - OpenCTP交易配置
- contracts.json - 合约信息

### templates/
配置模板文件

### logs/
系统日志文件

## 架构说明

本系统采用Wonder Trader官方的组合架构：
- 一个用户 = 一个组合 = 一个Wonder Trader引擎进程
- 组合包含多个策略，策略生成目标头寸
- 组合汇总策略头寸，轧平后提交给执行器
- 使用OpenCTP作为数据源和交易接口

## 使用方式

1. 前端操作保持不变
2. 后端透明重启组合实现策略启停
3. 策略状态自动保存和恢复
4. 组合级别的持仓和盈亏管理
`;

        await fs.writeFile(path.join(baseDir, 'README.md'), readmeContent, 'utf8');
        console.log('✅ README.md 创建完成');

        // 4. 创建示例用户组合 - 按照Wonder Trader标准结构
        console.log('\n4. 创建示例用户组合...');
        const exampleUserDir = path.join(baseDir, 'groups', 'user_1');
        const exampleGroupDir = path.join(exampleUserDir, 'group_1');
        const exampleDirs = [
            exampleUserDir,     // live_trading/groups/user_1/
            exampleGroupDir     // live_trading/groups/user_1/group_1/
        ];

        for (const dir of exampleDirs) {
            try {
                await fs.mkdir(dir, { recursive: true });
                console.log(`✅ 创建示例目录: ${dir}`);
            } catch (error) {
                if (error.code !== 'EEXIST') {
                    throw error;
                }
                console.log(`📁 示例目录已存在: ${dir}`);
            }
        }

        // 创建示例配置文件
        const exampleConfig = `# Wonder Trader 组合配置示例
# 用户: 1
# 组合: 示例组合

name: "用户1的交易组合"
description: "示例交易组合"

strategies:
  cta: []

# 数据源配置
parsers: "../common/parsers.yaml"

# 交易接口配置  
traders: "../common/traders.yaml"

# 合约配置
contracts: "../common/contracts.json"

# 风险配置
risk:
  max_drawdown: 0.1
  max_position_size: 0.2
  stop_loss: 0.05

# 资金配置
capital:
  initial: 100000.0
  current: 100000.0

# 日志配置
logging:
  level: "INFO"
  path: "./logs/"
  console: true
`;

        await fs.writeFile(path.join(exampleUserDir, 'config.yaml'), exampleConfig, 'utf8');
        console.log('✅ 示例配置文件创建完成');

        console.log('\n=== 设置完成 ===');
        console.log(`✅ 实盘交易目录结构已创建: ${baseDir}`);
        console.log('✅ OpenCTP配置已生成（使用模拟用户密码）');
        console.log('✅ 示例用户组合已创建');
        
        console.log('\n📋 下一步：');
        console.log('1. 修改 common/parsers.yaml 和 common/traders.yaml 中的真实用户密码');
        console.log('2. 根据需要调整合约配置 common/contracts.json');
        console.log('3. 启动后端服务测试新架构');

    } catch (error) {
        console.error('❌ 设置失败:', error.message);
        throw error;
    }
}

// 如果直接运行此文件，则执行设置
if (require.main === module) {
    setupLiveTradingStructure().then(() => {
        console.log('\n设置完成');
        process.exit(0);
    }).catch(error => {
        console.error('设置异常:', error);
        process.exit(1);
    });
}

module.exports = { setupLiveTradingStructure };
