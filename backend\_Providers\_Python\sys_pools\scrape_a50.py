# -*- coding: utf-8 -*-
import logging
import re
from bs4 import BeautifulSoup, Tag
from scraper_utils import fetch_url, format_stock_data, save_to_json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Note: This URL points to FTSE China 50, which is closely related to A50 but might differ slightly.
# Finding a reliable, easily scrapable public source specifically for FTSE A50 can be tricky.
# This URL was chosen as a readily available proxy.
URL = "http://finance.sina.com.cn/realstock/company/fushi50/成分股.shtml"
OUTPUT_FILENAME = "A50.json"

def scrape_a50():
    """Scrape FTSE China A50 (or similar like FTSE 50) constituents and save to JSON."""
    logging.info("Starting FTSE China A50 constituents scrape...")
    html = fetch_url(URL)
    if not html:
        logging.error("Failed to fetch A50 page. Aborting.")
        return

    soup = BeautifulSoup(html, 'html.parser')
    all_stocks = []

    # Find the correct table - inspect the page source for the table ID or class
    # Let's assume the table might be the first one with a specific structure
    # or use a known ID/class if available (e.g., id="list_table")
    # Inspecting the page shows a table within a div with id="sub01_c1"
    data_div = soup.find('div', id='sub01_c1')
    if not isinstance(data_div, Tag):
        logging.error("Could not find the main data container div ('#sub01_c1'). Aborting.")
        return
        
    stock_table = data_div.find('table') # Find the first table within the div
    if not isinstance(stock_table, Tag):
        logging.error("Could not find the stock table within the data container div. Aborting.")
        return

    rows = stock_table.find_all('tr')
    if len(rows) <= 1:
        logging.warning("No data rows found in the A50 table.")
        return

    # Skip header row(s) - check table structure
    for row in rows[1:]: 
        if not isinstance(row, Tag): continue
        cells = row.find_all('td')
        
        # Check the expected number of cells
        # On this page, code seems to be in the first cell, name in the second
        if len(cells) >= 2:
            code_cell_tag = cells[0] # Code is directly in the cell or an inner tag like <a>
            name_cell_tag = cells[1] # Name is directly in the cell or an inner tag like <a>

            # Extract code and name, handling potential inner tags like <a>
            code_tag = code_cell_tag.find('a') if isinstance(code_cell_tag, Tag) else code_cell_tag
            name_tag = name_cell_tag.find('a') if isinstance(name_cell_tag, Tag) else name_cell_tag
            
            if isinstance(code_tag, Tag) and isinstance(name_tag, Tag):
                code = code_tag.text.strip()
                name = name_tag.text.strip()
                
                # Validate code format (6 digits)
                if re.fullmatch(r'\d{6}', code):
                    formatted_data = format_stock_data(code, name)
                    if formatted_data:
                        all_stocks.append(formatted_data)
                else:
                    logging.warning(f"Skipping invalid code format found in A50 table: '{code}'")
            else:
                logging.warning("Could not find valid code/name tags within A50 table cells.")
        else:
            logging.warning("Row in A50 table does not have enough cells.")

    if all_stocks:
        save_to_json(all_stocks, OUTPUT_FILENAME)
    else:
        logging.warning("No valid A50 stocks were scraped. JSON file not saved.")

if __name__ == "__main__":
    scrape_a50() 