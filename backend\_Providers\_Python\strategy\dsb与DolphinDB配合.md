https://zhuanlan.zhihu.com/p/692348824

历史数据导入DolphinDB
实时数据存储解决了以后，就需要考虑如何将以前datakit落地的历史数据导入到DolphinDB了。（如果datakit也是全新使用，那么就直接导入其他历史数据就可以了）
WonderTrader存储的历史数据文件，都是.dsb格式的。.dsb格式是WonderTrader自定义的数据格式，其组织形式可以概括为：文件头+数据块。文件头包含了数据类型、压缩数据块的大小，以及压缩以前的数据大小。压缩数据块是将数据数组，如WTSBarStruct[]和WTSTickStruct[]，使用zstd压缩算法进行压缩，压缩后的数据大小大致为原数据的5%-10%。
我们将.dsb数据导入DolphinDB当然不是要大家自己去解压缩.dsb文件，我们只需要使用WtDataHelper组件，直接读取.dsb文件，就可以得到未压缩的数据块。部分代码如下：

def trans_dsb_bars(exchg:str, code:str, filename:str, period:str, pool:ddb.DBConnectionPool):
    npBars = dtHelper.read_dsb_bars(filename, period=='d1')
    df_bars = npBars.to_df()
    df_bars.rename(columns={
        "date":"trading_date",
        "diff":"diff_interest"
    }, inplace=True)
    df_bars["exchange"] = exchg
    df_bars["code"] = code
    df_bars["period"] = 0 if period=='d1' else 1 if period=='m1' else 2
    df_bars = df_bars[ddb_kline_order]

    df_bars["trading_date"] = df_bars["trading_date"].apply(lambda x : datetime.datetime.strptime(str(x), "%Y%m%d"))
    if period=='d1':
        df_bars["bartime"] = df_bars["trading_date"].copy()
    else:
        df_bars["bartime"] = df_bars["bartime"].apply(lambda x : datetime.datetime.strptime(str(x), "%Y%m%d%H%M"))

    appender = ddb.PartitionedTableAppender(dbPath="dfs://FUT_KLINES", tableName="Klines", partitionColName="trading_date", dbConnectionPool=pool)
    appender.append(df_bars)

def trans_dsb_ticks(exchg:str, code:str, filename:str, pool:ddb.DBConnectionPool):
    npTicks = dtHelper.read_dsb_ticks(filename)
    df_ticks = npTicks.to_df()
    # 这里省略的部分代码
    df_ticks["exchange"] = exchg
    df_ticks["code"] = code
    df_ticks["action_time"] = df_ticks["time"].apply(lambda x : datetime.datetime(x//10000000000000, x%10000000000000//100000000000, x%100000000000//1000000000,
                                                                                  x%1000000000//10000000, x%10000000//100000, x%100000//1000, x%1000*1000))
    df_ticks["trading_date"] = df_ticks["trading_date"].apply(lambda x : datetime.datetime.strptime(str(x), "%Y%m%d"))
    df_ticks.drop(columns=["time","action_date","pre_close","pre_settle","pre_interest"], inplace=True)

    appender = ddb.PartitionedTableAppender(dbPath="dfs://FUT_TICKS", tableName="Ticks", partitionColName="trading_date", dbConnectionPool=pool)
    appender.append(df_ticks[ddb_tick_order])

完整代码，可以参考wtpy/demos/test_dolphindb/dsb_to_ddb.py，如果master分支没有，可以切换到dev分支。

使用DolphinDB中的数据
当增量数据存储和历史数据导入完成以后，我们就可以使用数据进行回测和交易了。wtpy中提供了ExtDataLoader组件，如果回测框架或者实盘框架注册了ExtDataloader，那么框架就会优先调用ExtDataLoader的接口加载历史数据，如果加载失败，则会再加载datakit落地的历史数据。实时数据，还是通过mmap文件读取，这样才能在生产环境以最高效的方式拿到最新的数据。
针对DolphinDB的ExtDataLoader部分代码如下：

class DDBDataLoader(BaseExtDataLoader):
    def __init__(self, host:str, port:int, user:str, pwd:str):
        BaseExtDataLoader.__init__(self)
        self.ddb_params = dict(
            host=host, 
            port=port, 
            userid=user, 
            password=pwd
            )

        self._check_table()

    def _check_table(self):
        # 这里省略的部分代码
        pass
    
    def load_final_his_bars(self, stdCode:str, period:str, feeder) -> bool:
        '''
        加载历史K线（回测、实盘）
        @stdCode    合约代码，格式如CFFEX.IF.2106
        @period     周期，m1/m5/d1
        @feeder     回调函数，feed_raw_bars(bars:POINTER(WTSBarStruct), count:int, factor:double)
        '''
        print(f"loading {period} bars of {stdCode} from extended dolphindb loader")

        ay = stdCode.split('.')
        exchg = ay[0]
        if len(ay) > 2:
            pid = ay[1]
            month = ay[2]
            if month in ['HOT','2ND']:
                code = f"{pid}.{month}" # 这里主要针对主力合约
            else:
                code = f"{pid}{month}"  #郑商所的月份是少一位的，但是我们要在数据库里处理成4位，不然时间长了就会有问题            
        else:
            code = ay[1]    # 如果只有两段，那么就直接使用

        period = 0 if period=='d1' else 1 if period=='m1' else 2

        s = ddb.session()
        s.connect(**self.ddb_params)
        script = f'''
        db = database( "dfs://FUT_KLINES");
        tb = loadTable(db, `Klines);
        select * from tb where exchange='{exchg}' and code='{code}' and period={period};
        '''
        df = s.run(script)
        df['date'] = df['trading_date'].astype('datetime64[ns]').dt.strftime('%Y%m%d').astype('int64')
        df['time'] = df['bartime'].astype('datetime64[ns]').dt.strftime('%Y%m%d%H%M').astype('int64')-199000000000
        df.rename(columns={
            'diff_interest':'diff'
            }, inplace=True)
        df.drop(columns=['trading_date','bartime','period','exchange','code'], inplace=True)

        BUFFER = WTSBarStruct*len(df)
        buffer = BUFFER()

        def assign(procession, buffer):
            tuple(map(lambda x: setattr(buffer[x[0]], procession.name, x[1]), enumerate(procession)))

        df.apply(assign, buffer=buffer)

        feeder(buffer, len(df))
        return True

    def load_his_ticks(self, stdCode:str, uDate:int, feeder) -> bool:
        '''
        加载历史K线（只在回测有效，实盘只提供当日落地的）
        @stdCode    合约代码，格式如CFFEX.IF.2106
        @uDate      日期，格式如yyyymmdd
        @feeder     回调函数，feed_raw_ticks(ticks:POINTER(WTSTickStruct), count:int)
        '''
        print(f"loading ticks on {uDate} of {stdCode} from extended dolphindb loader")

        ay = stdCode.split('.')
        exchg = ay[0]
        if len(ay) > 2:
            pid = ay[1]
            month = ay[2]
            if month in ['HOT','2ND']:
                code = f"{pid}.{month}" # 这里主要针对主力合约
            else:
                code = f"{pid}{month}"  #郑商所的月份是少一位的，但是我们要在数据库里处理成4位，不然时间长了就会有问题            
        else:
            code = ay[1]    # 如果只有两段，那么就直接使用

        uDate = str(uDate)

        s = ddb.session()
        s.connect(**self.ddb_params)

        script = f'''
        db = database( "dfs://FUT_TICKS");
        tb = loadTable(db, `Ticks);
        select * from tb where trading_date={uDate[:4]}.{uDate[4:6]}.{uDate[6:]} and exchange='{exchg}' and code='{code}';
        '''
        df = s.run(script)
        # 这里省略的部分代码
        df['exchg'] = df['exchg'].apply(lambda x: x.encode('utf-8'))
        df['code'] = df['code'].apply(lambda x: x.encode('utf-8'))
        df['trading_date'] = df['trading_date'].astype('datetime64[ns]').dt.strftime('%Y%m%d').astype('int64')
        df['action_date'] = df['action_time'].astype('datetime64[ns]').dt.strftime('%Y%m%d').astype('int64')
        df['action_time'] = df['action_time'].astype('datetime64[ns]').dt.strftime('%H%M%S%f').astype('int64')//1000

        BUFFER = WTSTickStruct*len(df)
        buffer = BUFFER()

        def assign(procession, buffer):
            tuple(map(lambda x: setattr(buffer[x[0]], procession.name, x[1]), enumerate(procession)))

        df.apply(assign, buffer=buffer)

        feeder(buffer, len(df))
        return True
DDBDataLoader的使用方法如下：

def test_in_bt():
    engine = WtBtEngine(EngineType.ET_CTA)

    # 初始化之前，向回测框架注册加载器
    engine.set_extended_data_loader(loader=DDBDataLoader(host="127.0.0.1", port=8900, user='admin', pwd='123456'), bAutoTrans=False)

    engine.init('../common/', "configbt.yaml")

    engine.configBacktest(201909100930,201912011500)
    engine.configBTStorage(mode="csv", path="../storage/")
    engine.commitBTConfig()

    straInfo = StraDualThrust(name='pydt_IF', code="CFFEX.IF.HOT", barCnt=50, period="m5", days=30, k1=0.1, k2=0.1, isForStk=False)
    engine.set_cta_strategy(straInfo)

    engine.run_backtest()

    analyst = WtBtAnalyst()
    analyst.add_strategy("pydt_IF", folder="./outputs_bt/pydt_IF/", init_capital=500000, rf=0.02, annual_trading_days=240)
    analyst.run()

    kw = input('press any key to exit\n')
    engine.release_backtest()

def test_in_rt():
    engine = WtEngine(EngineType.ET_CTA)

    # 初始化之前，向实盘框架注册加载器
    engine.set_extended_data_loader(DDBDataLoader(host="127.0.0.1", port=8900, user='admin', pwd='123456'))

    engine.init('../common/', "config.yaml")
    
    straInfo = StraDualThrust(name='pydt_au', code="SHFE.au.HOT", barCnt=50, period="m5", days=30, k1=0.2, k2=0.2, isForStk=False)
    engine.add_cta_strategy(straInfo)

    engine.run()

    print('press ctrl-c to exit')
    try:
    	while True:
            time.sleep(1)
    except KeyboardInterrupt as e:
    	exit(0)
完整代码，可以参考wtpy/demos/test_dataexts/test_ddb_loader.py，如果master分支没有，可以切换到dev分支。

话题延伸
虽说本文的主要目的是介绍WonderTrader结合DolphinDB的使用攻略，实际上DolphinDB在这样的场景中，只是作为一个扩展存储引擎的角色。横向来看，如果有其他的存储引擎，如clickhouse、mysql、tdengine、sqlite等各类数据库，又或者是parquet、pkl等文件格式，其实都可以参考文中提到的几个demo，轻松实现数据对接。
不过实际上DolphinDB的功能，远不止一个存储引擎。利用DolphinDB的并行运算的能力，可以相对高效的做很多数据相关的工作。具体的用法，大家可以去DolphinDB的官网自行查阅，本文就不做赘述了。

结束语
通过前面的介绍，相信大家已经对WonderTrader如何结合DolphinDB有了一个直观的认识，如果还有一些细节上的疑问，可以参考前文中提到的demo的代码。
下一个版本v0.9.10，已经酝酿很久了,确实也做了很多修改，方方面面都有涉及：性能优化、功能升级、bug修复等。愿意尝试的朋友，可以拉取dev分支的代码来体验一下，如果发现问题，也欢迎反馈，推动v0.9.10尽早发布。
WonderTrader旨在给各位量化从业人员提供更好的轮子，将技术相关的东西都封装在平台中，力求给策略研发带来更好的策略开发体验。笔者的水平有限，也希望有更多人关注到这个项目，参与到这个项目中来，这样才能让WonderTrader更加完善，更加高效。

最后再安利一下WonderTrader

WonderTrader的github地址：

https://github.com/wondertrader/wondertrader
​github.com/wondertrader/wondertrader
WonderTrader的gitee地址：

wondertrader/wondertrader
​gitee.com/wondertrader/wondertrader

wtpy的github地址：

https://github.com/wondertrader/wtpy
​github.com/wondertrader/wtpy
wtpy的gitee地址：

wondertrader/wtpy
​gitee.com/wondertrader/wtpy

WonderTrader官方文档地址：

https://docs.wondertrader.com
​docs.wondertrader.com/
@ZzzzHeJ的WonderTrader学习笔记

WonderTrader Learning Notes · WonderTrader的个人学习笔记
​zzzzhej.github.io/WonderTrader-Learning-Notes/
@dumengru的WonderTrader学习笔记

https://dumengru.github.io/docs_wondertrader/
​dumengru.github.io/docs_wondertrader/