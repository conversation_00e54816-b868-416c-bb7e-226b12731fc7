import requests
import pandas as pd
import time
import os
from datetime import datetime

# Binance API 配置
# 提供多个可能的API端点，按优先级排序
API_ENDPOINTS = [
    "https://api.binance.com/api/v3",  # 国际站
    "https://api1.binance.com/api/v3",  # 备用1
    "https://api2.binance.com/api/v3",  # 备用2
    "https://api3.binance.com/api/v3",  # 备用3
    "https://api.binance.us/api/v3"     # 美国站点
]
BASE_URL = API_ENDPOINTS[0]  # 默认使用第一个端点

# 代理设置（如果需要）
USE_PROXY = False  # 设置为True启用代理
PROXIES = {
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890'
}

MAX_RETRIES = 5  # 增加重试次数
REQUEST_DELAY = 1.0  # 增加请求间隔
TIMEOUT = 30  # 请求超时时间（秒）
DATA_DIR = "binance_daily_data"  # 数据保存目录

# 创建数据目录
os.makedirs(DATA_DIR, exist_ok=True)

def get_24h_stats():
    """获取所有交易对的24小时统计数据"""
    url = f"{BASE_URL}/ticker/24hr"
    response = requests.get(url)
    return response.json()

def get_top_symbols(min_volume_usd=5000000, min_trades=10000):
    """获取交易量达到一定阈值的USDT交易对

    Args:
        min_volume_usd: 最小24小时交易量（美元），默认500万美元
        min_trades: 最小24小时交易笔数，默认1万笔

    Returns:
        符合条件的交易对列表
    """
    # 获取交易所信息（用于过滤状态）
    exchange_info_url = f"{BASE_URL}/exchangeInfo"
    exchange_info = requests.get(exchange_info_url).json()

    # 创建一个集合，包含所有状态为TRADING的USDT交易对
    valid_symbols = {
        s["symbol"] for s in exchange_info["symbols"]
        if s["quoteAsset"] == "USDT" and s["status"] == "TRADING"
    }

    # 获取24小时统计数据
    stats = get_24h_stats()

    # 筛选符合条件的交易对
    top_symbols = []
    for item in stats:
        symbol = item["symbol"]
        # 确保是USDT交易对且状态为TRADING
        if symbol in valid_symbols and symbol.endswith("USDT"):
            volume_usd = float(item["quoteVolume"])  # USDT交易量
            trades = int(item["count"])  # 交易笔数

            if volume_usd >= min_volume_usd and trades >= min_trades:
                top_symbols.append({
                    "symbol": symbol,
                    "volume_usd": volume_usd,
                    "trades": trades
                })

    # 按交易量排序
    top_symbols.sort(key=lambda x: x["volume_usd"], reverse=True)

    # 打印筛选结果
    print(f"🔍 筛选条件: 24小时交易量 >= ${min_volume_usd/1000000:.1f}百万美元, 交易笔数 >= {min_trades}")
    print(f"📊 符合条件的交易对: {len(top_symbols)}/{len(stats)}")

    # 打印前10个交易对的详细信息
    if top_symbols:
        print("\n📈 交易量前10的交易对:")
        for i, item in enumerate(top_symbols[:10], 1):
            print(f"  {i}. {item['symbol']}: ${item['volume_usd']/1000000:.2f}百万美元, {item['trades']}笔交易")
        print()  # 空行

    # 只返回交易对名称列表
    return [item["symbol"] for item in top_symbols]

def fetch_klines(symbol, interval="1d", start_time=None, end_time=None):
    """获取K线数据（支持分页）"""
    url = f"{BASE_URL}/klines"
    params = {
        "symbol": symbol,
        "interval": interval,
        "limit": 1000,  # 每次最多1000条
    }
    if start_time:
        params["startTime"] = int(start_time.timestamp() * 1000)
    if end_time:
        params["endTime"] = int(end_time.timestamp() * 1000)

    for _ in range(MAX_RETRIES):
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"请求失败: {e}, 重试中...")
            time.sleep(2)
    return None

def save_to_csv(symbol, data):
    """保存为CSV"""
    # 将symbol转换为code格式，例如BTCUSDT转换为BTC-USDT
    if 'USDT' in symbol:
        base_asset = symbol.replace('USDT', '')
        code = f"{base_asset}-USDT"
    else:
        # 对于其他交易对，尝试在最后4个字符前添加'-'
        # 例如ETHBTC转换为ETH-BTC
        code = f"{symbol[:-4]}-{symbol[-4:]}" if len(symbol) > 4 else symbol

    # 新的文件名格式：BINANCE.CRYPTO.{code}_d.csv
    filename = f"{DATA_DIR}/BINANCE.CRYPTO.{code}_d.csv"

    df = pd.DataFrame(data, columns=[
        "timestamp", "open", "high", "low", "close", "volume",
        "close_time", "quote_asset_volume", "trades",
        "taker_buy_base", "taker_buy_quote", "ignore"
    ])
    df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")
    df.to_csv(filename, index=False)
    print(f"✅ 已保存: {filename}")

def download_daily_data(symbol):
    """下载某个交易对的全量日线数据"""
    print(f"⏳ 开始下载: {symbol}")
    klines = []
    end_time = datetime.now()

    while True:
        data = fetch_klines(symbol, interval="1d", end_time=end_time)
        if not data:
            print(f"❌ 获取数据失败: {symbol}")
            return

        klines.extend(data)
        if len(data) < 1000:  # 如果数据不足1000条，说明已经到最早时间
            break

        # 更新 end_time 为最早的时间戳（往前推）
        end_time = datetime.fromtimestamp(data[0][0] / 1000 - 1)
        time.sleep(REQUEST_DELAY)  # 避免请求过快

    if klines:
        save_to_csv(symbol, klines)
    else:
        print(f"⚠️ 无数据: {symbol}")

def get_top_n_symbols(n=20):
    """获取交易额最高的前N个USDT交易对

    Args:
        n: 返回的交易对数量，默认20个

    Returns:
        交易额最高的前N个交易对列表
    """
    # 获取24小时统计数据
    stats = get_24h_stats()

    # 筛选USDT交易对并计算交易额
    usdt_pairs = []
    for item in stats:
        symbol = item["symbol"]
        if symbol.endswith("USDT"):
            try:
                volume_usd = float(item["quoteVolume"])  # USDT交易量
                usdt_pairs.append({
                    "symbol": symbol,
                    "volume_usd": volume_usd
                })
            except (KeyError, ValueError):
                continue

    # 按交易额排序
    usdt_pairs.sort(key=lambda x: x["volume_usd"], reverse=True)

    # 获取前N个交易对
    top_pairs = usdt_pairs[:n]

    # 打印筛选结果
    print(f"📊 币安共有 {len(usdt_pairs)} 个USDT交易对")
    print(f"🔝 选取交易额最高的前 {n} 个交易对:")

    # 打印选中的交易对详细信息
    for i, item in enumerate(top_pairs, 1):
        volume_millions = item["volume_usd"] / 1000000
        print(f"  {i}. {item['symbol']}: ${volume_millions:.2f}百万美元")

    # 只返回交易对名称列表
    return [item["symbol"] for item in top_pairs]

if __name__ == "__main__":
    # 获取交易额最高的前20个USDT交易对
    symbols = get_top_n_symbols(20)
    print(f"📊 将下载 {len(symbols)} 个交易对的历史数据")

    for symbol in symbols:
        download_daily_data(symbol)
        time.sleep(REQUEST_DELAY)  # 避免频繁请求

    print("🎉 所有数据下载完成!")