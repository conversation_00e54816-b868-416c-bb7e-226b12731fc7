import './models/useUser';  // 确保 useUser 模块被加载
import { EventBus } from './events/eventBus';
import './_Pages/main/components/DrawingTools';  // 确保 DrawingTools 模块被加载
import { initRealtimeKline } from './_Pages/main/components/KLineChartPanel/realtimeKline';  // 导入实时数据初始化函数
import chartDispatcher from './_Dispatchers/ChartDispatcher';
import { initialDrawingLinesAtom } from './store/state';
import { jotaiStore } from './store/state';
import { initializeStrategyDispatcher } from './_Dispatchers/StrategyDispatcher';
import { initializeLiveStrategyDispatcher } from './_Dispatchers/LiveStrategyDispatcher';
import poolDispatcher from './_Dispatchers/poolDispatcher';
import { initializeSymbolSelectDispatcher } from './_Dispatchers/SymbolSelectDispatcher';
import { initializeStockPoolData } from './_Widgets/StockPoolWidget';  // 导入股票池初始化函数

export const initializeApp = () => {
  console.log('[App] Initializing app...');

  // 确保 jotai store 和 atoms 已经初始化
  console.log('[App] 初始化全局状态...');
  jotaiStore.set(initialDrawingLinesAtom, []);

  // 初始化实时数据模块
  console.log('[App] 初始化实时数据模块...');
  initRealtimeKline();
  chartDispatcher.initialize();
  poolDispatcher.initialize();

  // 初始化股票池模块
  console.log('[App] 初始化股票池模块...');
  initializeStockPoolData().then(({ sysData, userData, db }) => {
    console.log('[App] 股票池模块初始化完成');
    console.log(`[App] 系统池索引: ${sysData.length} 个分类`);
    console.log(`[App] 用户池索引: ${userData.length} 个分类`);
    if (db) {
      console.log('[App] IndexDB连接已建立');
    }
  }).catch(error => {
    console.error('[App] 股票池模块初始化失败:', error);
  });

  // 初始化K线图表面板
  console.log('[App] 初始化K线图表面板...');

  // 初始化策略模块
  console.log('[App] 初始化策略模块...');
  initializeStrategyDispatcher();

  // 初始化实盘策略模块
  console.log('[App] 初始化实盘策略模块...');
  initializeLiveStrategyDispatcher();

  // 初始化选股分发器
  console.log('[App] 初始化选股分发器...');
  initializeSymbolSelectDispatcher();
};