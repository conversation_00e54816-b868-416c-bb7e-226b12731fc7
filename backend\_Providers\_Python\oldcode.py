#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一的Wonder Trader ExtDataLoader
支持从本地数据源加载历史数据，包括：
- 股票/ETF: 通过通达信数据
- 期货: 通过通达信数据  
- 虚拟货币: 通过本地CSV文件
- 美股: 通过通达信数据
"""

import os
import sys
import json
import pandas as pd
import pytz
import datetime
from typing import List, Dict, Optional

# 尝试导入wtpy相关模块
try:
    from ctypes import POINTER
    from wtpy.ExtModuleDefs import BaseExtDataLoader
    from wtpy.WtCoreDefs import WTSBarStruct, WTSTickStruct
    WTPY_AVAILABLE = True
except ImportError:
    print("警告: wtpy模块不可用，将使用模拟模式")
    WTPY_AVAILABLE = False

    # 创建模拟的基类和结构
    class BaseExtDataLoader:
        pass

    class MockBarStruct:
        def __init__(self):
            self.time = 0
            self.open = 0.0
            self.high = 0.0
            self.low = 0.0
            self.close = 0.0
            self.vol = 0.0
            self.money = 0.0
            self.hold = 0.0
            self.add = 0.0

    class MockTickStruct:
        def __init__(self):
            self.time = 0
            self.price = 0.0
            self.volume = 0.0

    WTSBarStruct = MockBarStruct
    WTSTickStruct = MockTickStruct

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入现有的数据处理模块
from prepare_csv import (
    load_config, read_tdx_day_data, read_tdx_min5_data, 
    get_tdx_file_path, is_etf, DEFAULT_TIMEZONE
)
from prepare_csv_cpt import get_crypto_source_path

class UnifiedExtDataLoader(BaseExtDataLoader):
    """
    统一的扩展数据加载器
    支持股票、ETF、期货、虚拟货币等多种品种
    """
    
    def __init__(self):
        """初始化加载器"""
        super().__init__()
        self.config = None
        self.timezone = None
        self._load_config()
        
    def _load_config(self):
        """加载配置"""
        try:
            self.config = load_config()
            if not self.config:
                print("[ExtDataLoader] 警告: 无法加载配置文件，使用默认设置")
                return
                
            timezone_str = self.config.get('default_timezone', DEFAULT_TIMEZONE)
            self.timezone = pytz.timezone(timezone_str)
            print(f"[ExtDataLoader] 配置加载成功，时区: {timezone_str}")
            
        except Exception as e:
            print(f"[ExtDataLoader] 配置加载失败: {e}")
            self.timezone = pytz.timezone(DEFAULT_TIMEZONE)
    
    def _parse_wt_code(self, stdCode: str) -> Dict[str, str]:
        """
        解析Wonder Trader标准代码
        
        Args:
            stdCode: 标准代码，如 "SSE.600000", "CRYPTO.CPT.BTC-USDT"
            
        Returns:
            解析结果字典
        """
        parts = stdCode.split('.')
        
        if len(parts) >= 2:
            exchange = parts[0].upper()
            
            # 虚拟货币
            if exchange == "CRYPTO" and len(parts) >= 3 and parts[1].upper() == "CPT":
                return {
                    'type': 'crypto',
                    'exchange': exchange,
                    'category': parts[1],
                    'symbol': parts[2] if len(parts) > 2 else '',
                    'full_code': stdCode
                }
            
            # 传统金融品种
            else:
                return {
                    'type': 'traditional',
                    'exchange': exchange,
                    'symbol': '.'.join(parts[1:]) if len(parts) > 1 else '',
                    'full_code': stdCode
                }
        
        return {
            'type': 'unknown',
            'full_code': stdCode
        }
    
    def _convert_period(self, period: str) -> str:
        """
        转换Wonder Trader周期到内部格式

        Args:
            period: WT周期，如 "m1", "m5", "d1"

        Returns:
            内部周期格式
        """
        period_map = {
            'm1': 'min1',
            'm5': 'min5',
            'd1': 'day',
            'day': 'day'
        }
        return period_map.get(period.lower(), 'day')

    def _convert_to_wt_time(self, date_int: int, time_int: int = 930) -> int:
        """
        将日期和时间转换为Wonder Trader时间格式

        Args:
            date_int: YYYYMMDD格式的日期整数
            time_int: HHMM格式的时间整数，默认930(09:30)

        Returns:
            Wonder Trader时间格式: (YYYYMMDD - 19900000) * 10000 + HHMM
        """
        if date_int == 0:
            return 0

        try:
            # 按照Wonder Trader格式计算: (YYYYMMDD - 19900000) * 10000 + HHMM
            wt_time = (date_int - 19900000) * 10000 + time_int
            return wt_time

        except Exception as e:
            print(f"[ExtDataLoader] 时间转换失败 date:{date_int}, time:{time_int}: {e}")
            return 0

    def _read_tdx_data_for_wt(self, file_path: str, period: str, numeric_code: str) -> List[Dict]:
        """
        直接读取通达信数据并按照Wonder Trader规范处理

        Args:
            file_path: 通达信文件路径
            period: 数据周期
            numeric_code: 数字代码（用于ETF判断）

        Returns:
            包含date和time_hhmm字段的K线数据列表
        """
        print(f"[数据准备] 读取{period}文件: {file_path}")

        if not os.path.exists(file_path):
            print(f"[数据准备] 文件不存在: {file_path}")
            return []

        klines = []

        try:
            with open(file_path, 'rb') as f:
                buffer = f.read()

                if period == 'day':
                    # 日线数据：32字节每条记录
                    record_size = 32
                    record_count = len(buffer) // record_size

                    for i in range(record_count):
                        pos = i * record_size
                        if pos + record_size > len(buffer):
                            break

                        # 读取日期 (4字节整型)
                        date_value = int.from_bytes(buffer[pos:pos+4], byteorder='little')
                        year = date_value // 10000
                        month = (date_value % 10000) // 100
                        day = date_value % 100

                        if not (1990 <= year <= 2030 and 1 <= month <= 12 and 1 <= day <= 31):
                            continue

                        # 构造YYYYMMDD格式的日期
                        date_int = year * 10000 + month * 100 + day

                        # 读取价格数据
                        divisor = 1000.0 if self._is_etf(numeric_code) else 100.0
                        open_price = int.from_bytes(buffer[pos+4:pos+8], byteorder='little') / divisor
                        high_price = int.from_bytes(buffer[pos+8:pos+12], byteorder='little') / divisor
                        low_price = int.from_bytes(buffer[pos+12:pos+16], byteorder='little') / divisor
                        close_price = int.from_bytes(buffer[pos+16:pos+20], byteorder='little') / divisor

                        # 读取成交额和成交量
                        import struct
                        amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
                        volume = int.from_bytes(buffer[pos+24:pos+28], byteorder='little')

                        # 检查成交量是否需要调整
                        if volume > 0:
                            approx_price = amount / volume
                            if approx_price > close_price * 10:
                                volume = volume * 100

                        # 检查数据合理性
                        if not (open_price > 0 and high_price > 0 and low_price > 0 and close_price > 0):
                            continue

                        # 按照官方例子的格式：保存原始的date和time，不预先计算
                        klines.append({
                            'date': f"{year}/{month}/{day}",  # 保存为字符串格式，模拟CSV的日期格式
                            'time': "09:30:00",  # 日线数据使用09:30:00
                            'open': float(open_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'close': float(close_price),
                            'vol': float(volume),
                            'money': float(amount)
                        })

                elif period == 'min5':
                    # 5分钟数据：32字节每条记录
                    record_size = 32
                    record_count = len(buffer) // record_size

                    for i in range(record_count):
                        pos = i * record_size
                        if pos + record_size > len(buffer):
                            break

                        # 读取日期时间 (前2字节日期，后2字节分钟)
                        date_time_bytes = buffer[pos:pos+4]
                        date_part = int.from_bytes(date_time_bytes[0:2], byteorder='little')
                        time_part = int.from_bytes(date_time_bytes[2:4], byteorder='little')

                        # 计算年月日
                        year = (date_part // 2048) + 2004
                        month = (date_part % 2048) // 100
                        day = (date_part % 2048) % 100
                        # 计算时分
                        hour = time_part // 60
                        minute = time_part % 60

                        if not (1990 <= year <= 2030 and 1 <= month <= 12 and 1 <= day <= 31 and 0 <= hour <= 23 and 0 <= minute <= 59):
                            continue

                        # 构造YYYYMMDD和HHMM格式
                        date_int = year * 10000 + month * 100 + day
                        time_int = hour * 100 + minute

                        # 读取价格数据
                        divisor = 1000.0 if self._is_etf(numeric_code) else 100.0
                        open_price = int.from_bytes(buffer[pos+4:pos+8], byteorder='little') / divisor
                        high_price = int.from_bytes(buffer[pos+8:pos+12], byteorder='little') / divisor
                        low_price = int.from_bytes(buffer[pos+12:pos+16], byteorder='little') / divisor
                        close_price = int.from_bytes(buffer[pos+16:pos+20], byteorder='little') / divisor

                        # 读取成交额和成交量
                        import struct
                        amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
                        volume = int.from_bytes(buffer[pos+24:pos+28], byteorder='little')

                        # 检查成交量是否需要调整
                        if volume > 0:
                            approx_price = amount / volume
                            if approx_price > close_price * 10:
                                volume = volume * 100

                        # 检查数据合理性
                        if not (open_price > 0 and high_price > 0 and low_price > 0 and close_price > 0):
                            continue

                        # 按照官方例子的格式：保存原始的date和time，不预先计算
                        klines.append({
                            'date': f"{year}/{month}/{day}",  # 保存为字符串格式，模拟CSV的日期格式
                            'time': f"{hour:02d}:{minute:02d}:00",  # 保存为HH:MM:SS格式
                            'open': float(open_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'close': float(close_price),
                            'vol': float(volume),
                            'money': float(amount)
                        })

        except Exception as e:
            print(f"[ExtDataLoader] 读取通达信文件失败 {file_path}: {e}")
            return []

        print(f"[数据准备] 成功读取{period}数据: {file_path}, 共 {len(klines)} 条记录")
        return klines

    def _is_etf(self, code: str) -> bool:
        """判断是否为ETF"""
        if not code:
            return False
        # ETF代码通常以51、15、16开头
        return code.startswith(('51', '15', '16'))
    
    def _load_traditional_data(self, code_info: Dict[str, str], period: str) -> List[Dict]:
        """
        加载传统金融品种数据（股票、ETF、期货等）
        
        Args:
            code_info: 代码解析信息
            period: 数据周期
            
        Returns:
            K线数据列表
        """
        if not self.config:
            print(f"[ExtDataLoader] 配置未加载，无法获取{code_info['full_code']}数据")
            return []
            
        try:
            # 获取通达信数据根目录
            tdx_data_root = self.config.get('tdx_data', {}).get('path')
            if not tdx_data_root:
                print(f"[ExtDataLoader] 通达信数据路径未配置")
                return []
                
            # 转换为绝对路径
            if not os.path.isabs(tdx_data_root):
                config_dir = os.path.dirname(os.path.abspath(__file__))
                tdx_data_root = os.path.abspath(os.path.join(config_dir, tdx_data_root))
            
            # 获取通达信文件路径
            tdx_file_path = get_tdx_file_path(code_info['full_code'], period, tdx_data_root)
            if not tdx_file_path or not os.path.exists(tdx_file_path):
                print(f"[ExtDataLoader] 通达信文件不存在: {tdx_file_path}")
                return []
            
            # 提取数字代码用于ETF判断
            symbol_parts = code_info['symbol'].split('.')
            numeric_code = symbol_parts[-1] if symbol_parts else ""
            
            # 使用新的读取函数，直接按Wonder Trader规范处理
            klines = self._read_tdx_data_for_wt(tdx_file_path, period, numeric_code)
                
            print(f"[ExtDataLoader] 成功加载{code_info['full_code']}数据: {len(klines)}条")
            return klines
            
        except Exception as e:
            print(f"[ExtDataLoader] 加载传统数据失败 {code_info['full_code']}: {e}")
            return []
    
    def _load_crypto_data(self, code_info: Dict[str, str], period: str) -> List[Dict]:
        """
        加载虚拟货币数据
        
        Args:
            code_info: 代码解析信息
            period: 数据周期
            
        Returns:
            K线数据列表
        """
        if not self.config:
            print(f"[ExtDataLoader] 配置未加载，无法获取{code_info['full_code']}数据")
            return []
            
        try:
            # 获取虚拟货币数据根目录
            crypto_data_root = self.config.get('crypto_data', {}).get('path')
            if not crypto_data_root:
                print(f"[ExtDataLoader] 虚拟货币数据路径未配置")
                return []
                
            # 转换为绝对路径
            if not os.path.isabs(crypto_data_root):
                config_dir = os.path.dirname(os.path.abspath(__file__))
                crypto_data_root = os.path.abspath(os.path.join(config_dir, crypto_data_root))
            
            # 获取虚拟货币文件路径
            crypto_file_path = get_crypto_source_path(code_info['full_code'], period, crypto_data_root)
            if not crypto_file_path or not os.path.exists(crypto_file_path):
                print(f"[ExtDataLoader] 虚拟货币文件不存在: {crypto_file_path}")
                return []
            
            # 读取CSV文件
            df = pd.read_csv(crypto_file_path)
            if df.empty:
                print(f"[ExtDataLoader] 虚拟货币文件为空: {crypto_file_path}")
                return []
            
            # 转换为标准格式
            klines = []
            for _, row in df.iterrows():
                try:
                    # 假设CSV格式包含: timestamp, open, high, low, close, volume
                    klines.append({
                        'time': int(row.get('timestamp', row.get('time', 0))),
                        'open': float(row.get('open', 0)),
                        'high': float(row.get('high', 0)),
                        'low': float(row.get('low', 0)),
                        'close': float(row.get('close', 0)),
                        'volume': float(row.get('volume', 0)),
                        'amount': float(row.get('amount', row.get('turnover', 0)))
                    })
                except Exception as e:
                    print(f"[ExtDataLoader] 解析虚拟货币数据行失败: {e}")
                    continue
            
            print(f"[ExtDataLoader] 成功加载{code_info['full_code']}数据: {len(klines)}条")
            return klines
            
        except Exception as e:
            print(f"[ExtDataLoader] 加载虚拟货币数据失败 {code_info['full_code']}: {e}")
            return []
    
    def _convert_to_wt_bars(self, klines: List[Dict], feeder) -> bool:
        """
        将K线数据转换为Wonder Trader格式并推送
        完全按照官方示例的方式处理

        Args:
            klines: K线数据列表
            feeder: Wonder Trader回调函数

        Returns:
            是否成功
        """
        if not klines:
            return False

        try:
            if WTPY_AVAILABLE:
                # 步骤1: 创建DataFrame (模拟官方的 pd.read_csv)
                df = pd.DataFrame(klines)

                # 步骤2: 重命名列 (模拟官方的 df.rename)
                # 我们的数据已经是正确的列名，不需要重命名

                # 步骤3: 按照官方逻辑处理时间
                df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y%m%d').astype('int64')
                df['time'] = (df['date'] - 19900000) * 10000 + df['time'].str.replace(':', '').str[:-2].astype('int')

                # 步骤4: 创建WTSBarStruct数组
                BUFFER = WTSBarStruct * len(df)
                buffer = BUFFER()

                # 步骤5: 使用官方的assign函数逻辑
                def assign(procession, buffer):
                    tuple(map(lambda x: setattr(buffer[x[0]], procession.name, x[1]), enumerate(procession)))

                # 步骤6: 应用到每一列
                df.apply(assign, buffer=buffer)

                # 步骤7: 调试输出 (模拟官方的 print)
                print(f"[ExtDataLoader] DataFrame处理完成:")
                print(df.head(3))
                if len(buffer) > 0:
                    print(f"[ExtDataLoader] buffer[0] 字段:")
                    if hasattr(buffer[0], 'to_dict'):
                        print(buffer[0].to_dict)
                    else:
                        print(f"  date: {getattr(buffer[0], 'date', 'N/A')}")
                        print(f"  time: {getattr(buffer[0], 'time', 'N/A')}")
                        print(f"  open: {getattr(buffer[0], 'open', 'N/A')}")
                        print(f"  close: {getattr(buffer[0], 'close', 'N/A')}")

                # 步骤8: 调用feeder
                feeder(buffer, len(df))
                return True
            else:
                # 模拟模式：简化处理
                print(f"[ExtDataLoader] 模拟模式处理数据")
                buffer = []
                for i, kline in enumerate(klines):
                    bar = WTSBarStruct()
                    # 模拟处理时间
                    import datetime as dt
                    date_str = kline.get('date', '1990/1/1')
                    time_str = kline.get('time', '09:30:00')

                    # 转换日期
                    date_obj = dt.datetime.strptime(date_str, '%Y/%m/%d')
                    date_int = int(date_obj.strftime('%Y%m%d'))

                    # 转换时间
                    time_parts = time_str.replace(':', '')[:-2]  # 去掉秒
                    time_int = int(time_parts)

                    # 计算WT时间
                    wt_time = (date_int - 19900000) * 10000 + time_int

                    bar.date = date_int
                    bar.time = wt_time
                    bar.open = float(kline.get('open', 0))
                    bar.high = float(kline.get('high', 0))
                    bar.low = float(kline.get('low', 0))
                    bar.close = float(kline.get('close', 0))
                    bar.vol = float(kline.get('vol', 0))
                    bar.money = float(kline.get('money', 0))
                    buffer.append(bar)

                # 调用模拟回调
                feeder(buffer, len(klines))
                return True

        except Exception as e:
            print(f"[ExtDataLoader] 转换WTSBarStruct失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_final_his_bars(self, stdCode: str, period: str, feeder) -> bool:
        """
        加载历史K线数据（回测、实盘）
        
        Args:
            stdCode: 合约代码，格式如SSE.600000, CRYPTO.CPT.BTC-USDT
            period: 周期，m1/m5/d1
            feeder: 回调函数，feed_raw_bars(bars:POINTER(WTSBarStruct), count:int, factor:double)
            
        Returns:
            是否成功加载
        """
        print(f"[ExtDataLoader] 加载{period}周期的{stdCode}历史K线数据")
        
        try:
            # 解析代码
            code_info = self._parse_wt_code(stdCode)
            if code_info['type'] == 'unknown':
                print(f"[ExtDataLoader] 无法解析代码格式: {stdCode}")
                return False
            
            # 转换周期
            internal_period = self._convert_period(period)
            
            # 根据类型加载数据
            klines = []
            if code_info['type'] == 'crypto':
                klines = self._load_crypto_data(code_info, internal_period)
            elif code_info['type'] == 'traditional':
                klines = self._load_traditional_data(code_info, internal_period)
            
            if not klines:
                print(f"[ExtDataLoader] 未能加载到{stdCode}的数据")
                return False
            
            # 转换并推送数据
            return self._convert_to_wt_bars(klines, feeder)
            
        except Exception as e:
            print(f"[ExtDataLoader] 加载历史K线失败 {stdCode}: {e}")
            return False
    
    def load_his_ticks(self, stdCode: str, uDate: int, feeder) -> bool:
        """
        加载历史Tick数据（只在回测有效，实盘只提供当日落地的）
        
        Args:
            stdCode: 合约代码
            uDate: 日期，格式如yyyymmdd
            feeder: 回调函数，feed_raw_ticks(ticks:POINTER(WTSTickStruct), count:int)
            
        Returns:
            是否成功加载
        """
        print(f"[ExtDataLoader] 加载{stdCode}在{uDate}的Tick数据")
        
        # 目前暂不实现Tick数据加载
        # 可以根据需要扩展
        print(f"[ExtDataLoader] Tick数据加载暂未实现")
        return False


# 创建全局实例
unified_loader = UnifiedExtDataLoader()

def get_unified_ext_data_loader():
    """获取统一的扩展数据加载器实例"""
    return unified_loader


def test_feeder_bars(bars_buffer, count):
    """测试K线数据回调函数"""
    print(f"  📊 收到K线数据: {count}条")

    if count > 0:
        # 打印前几条数据，显示WT的date和time字段
        print("  前3条K线数据:")
        for i in range(min(3, count)):
            bar = bars_buffer[i]
            # 显示WT结构中的date和time字段
            wt_date = getattr(bar, 'date', 'N/A')
            wt_time = getattr(bar, 'time', 'N/A')
            print(f"    [{i}] WT.date:{wt_date}, WT.time:{wt_time}, 开:{bar.open:.2f}, 高:{bar.high:.2f}, 低:{bar.low:.2f}, 收:{bar.close:.2f}, 量:{bar.vol}")

def test_feeder_ticks(ticks_buffer, count):
    """测试Tick数据回调函数"""
    print(f"  📈 收到Tick数据: {count}条")

    if count > 0:
        # 打印前几条数据
        print("  前3条Tick数据:")
        for i in range(min(3, count)):
            tick = ticks_buffer[i]
            print(f"    [{i}] 时间:{tick.time}, 价格:{tick.price:.2f}, 量:{tick.volume}")

if __name__ == "__main__":
    print("=" * 60)
    print("测试统一扩展数据加载器的核心函数")
    print("=" * 60)

    loader = get_unified_ext_data_loader()

    # 测试品种代码
    test_cases = [
        ("SSE.STK.600000", "d1", "股票日线"),
        ("SSE.STK.600000", "m5", "股票5分钟线"),
        ("SSE.ETF.510300", "d1", "ETF日线"),
        ("CRYPTO.CPT.BTC-USDT", "d1", "虚拟货币日线"),
        ("SHFE.fu.HOT", "d1", "期货日线"),
    ]

    print("\n🔍 测试 load_final_his_bars 函数:")
    print("-" * 40)

    for stdCode, period, description in test_cases:
        print(f"\n📈 测试 {description}: {stdCode} ({period})")
        try:
            result = loader.load_final_his_bars(stdCode, period, test_feeder_bars)
            if result:
                print(f"  ✅ {description} 加载成功")
            else:
                print(f"  ❌ {description} 加载失败")
        except Exception as e:
            print(f"  💥 {description} 加载异常: {e}")
            import traceback
            traceback.print_exc()

    print("\n🔍 测试 load_his_ticks 函数:")
    print("-" * 40)

    # 测试Tick数据加载
    tick_test_cases = [
        ("SSE.600000", 20231201, "股票Tick"),
        ("CRYPTO.CPT.BTC-USDT", 20231201, "虚拟货币Tick"),
    ]

    for stdCode, uDate, description in tick_test_cases:
        print(f"\n📊 测试 {description}: {stdCode} ({uDate})")
        try:
            result = loader.load_his_ticks(stdCode, uDate, test_feeder_ticks)
            if result:
                print(f"  ✅ {description} 加载成功")
            else:
                print(f"  ❌ {description} 加载失败 (当前未实现)")
        except Exception as e:
            print(f"  💥 {description} 加载异常: {e}")

    print("\n" + "=" * 60)
    print("✅ 核心函数测试完成!")
    print("=" * 60)
