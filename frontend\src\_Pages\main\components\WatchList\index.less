.watch-list-table {
  :global {
    .ant-table-tbody > tr.selected-row > td {
      background-color: #e6f4ff;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f0f7ff !important;
    }

    .ant-table-cell {
      padding: 8px !important;
    }
  }
}

.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 24px;

  :global {
    .ant-empty {
      margin: 32px 0;
    }

    .ant-empty-description {
      color: rgba(0, 0, 0, 0.45);
    }
  }
} 