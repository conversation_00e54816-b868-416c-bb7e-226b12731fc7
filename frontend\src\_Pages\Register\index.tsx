import React, { useState, useEffect } from 'react';
import { message, Space, Form } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  LoginForm,
  ProFormText,
  ProFormInstance,
  ProFormCaptcha,
} from '@ant-design/pro-components';
import { 
  UserOutlined, 
  LockOutlined,
  MailOutlined,
  GithubOutlined,
} from '@ant-design/icons';
import { setToken } from '../../utils/auth';
import { EventBus } from '../../events/eventBus';
import { UserEvents } from '../../events/events';
import { FrontendConfig } from '@/shared_types/enviorment';
import { useTheme } from '@/models/useTheme';
import { useAtom } from 'jotai';
import { globalLoadingAtom } from '@/components/GlobalLoading';

const Register: React.FC = () => {
  const formRef = React.useRef<ProFormInstance>();
  const navigate = useNavigate();
  const { theme, isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [, setGlobalLoading] = useAtom(globalLoadingAtom);

  const [config, setConfig] = useState<FrontendConfig | null>(null);

  useEffect(() => {
    // 从public目录获取配置文件
    fetch('/config.json')
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        setConfig(data); // 设置配置数据
      })
      .catch(error => {
        console.error('Error fetching config:', error);
      });
  }, []);

  useEffect(() => {
    const handleRegisterResult = (result: UserEvents.ResultPayload) => {
      setGlobalLoading(false);
      if (result.success) {
        message.success('注册成功！');
        setTimeout(() => navigate('/login'), 1500);
      } else {
        message.error(result.error || '注册失败，请重试');
      }
    };

    const handleEmailCodeResult = (result: UserEvents.SMSCodeResultPayload) => {
      if (result.success) {
        message.success('验证码已发送');
      } else {
        message.error(result.error || '发送验证码失败');
      }
    };

    const subscription = EventBus.on(UserEvents.Types.REGISTER_RESULT, handleRegisterResult);
    EventBus.on(UserEvents.Types.SMS_CODE_RESULT, handleEmailCodeResult);
    return () => {
      subscription.unsubscribe();
      EventBus.off(UserEvents.Types.SMS_CODE_RESULT, handleEmailCodeResult);
    };
  }, [navigate, setGlobalLoading]);

  const handleSubmit = async (values: any) => {
    const { username, password, email, verificationCode } = values;
    const formCode = formRef.current?.getFieldValue('verificationCode');
    
    if (verificationCode !== formCode) {
      message.error('验证码错误');
      return;
    }

    setGlobalLoading(true);
    EventBus.emit(UserEvents.Types.REGISTER, {
      username,
      password,
      email
    });
  };

  const generateVerificationCode = () => {
    return Math.floor(1000 + Math.random() * 9000).toString();
  };

  const handleLogin = () => {
    setGlobalLoading(true);
    navigate('/login');
  };

  return (
    <div style={{ 
      height: '100vh',
      background: isDarkMode ? '#141414' : '#f0f2f5',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <LoginForm
        formRef={formRef}
        logo={<GithubOutlined style={{ fontSize: 48, color: isDarkMode ? '#fff' : '#000' }} />}
        title={<div style={{ color: isDarkMode ? '#fff' : '#000' }}>{config?.title || "QuantQuart"}</div>}
        subTitle={<div style={{ color: isDarkMode ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.45)' }}>量化交易策略平台</div>}
        style={{
          backgroundColor: isDarkMode ? '#1f1f1f' : '#fff',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: isDarkMode 
            ? '0 2px 8px rgba(0, 0, 0, 0.45)' 
            : '0 2px 8px rgba(0, 0, 0, 0.15)'
        }}
        submitter={{
          searchConfig: {
            submitText: '注册',
          },
        }}
        onFinish={handleSubmit}
        actions={
          <Space style={{ color: isDarkMode ? '#fff' : '#000' }}>
            已有账号？
            <a onClick={handleLogin} style={{ color: isDarkMode ? '#1890ff' : '#1677ff' }}>返回登录</a>
          </Space>
        }
      >
        <ProFormText
          name="username"
          fieldProps={{
            size: 'large',
            prefix: <UserOutlined />,
          }}
          placeholder="用户名"
          rules={[
            {
              required: true,
              message: '请输入用户名',
            },
          ]}
        />
        <ProFormText
          name="email"
          fieldProps={{
            size: 'large',
            prefix: <MailOutlined />,
          }}
          placeholder="邮箱"
          rules={[
            {
              required: true,
              message: '请输入邮箱',
            },
            {
              type: 'email',
              message: '请输入正确的邮箱格式',
            },
          ]}
        />
        <ProFormCaptcha
          name="verificationCode"
          fieldProps={{
            size: 'large',
          }}
          placeholder="验证码"
          rules={[
            {
              required: true,
              message: '请输入验证码',
            },
          ]}
          onGetCaptcha={async () => {
            try {
              await formRef.current?.validateFields(['email']);
              const email = formRef.current?.getFieldValue('email');
              const code = generateVerificationCode();
              EventBus.emit(UserEvents.Types.SEND_EMAIL_CODE, { email, code });
              formRef.current?.setFieldValue('verificationCode', '');
              message.success('验证码已发送');
            } catch (error) {
              message.error('请输入正确的邮箱');
              return Promise.reject();
            }
          }}
        />
        <ProFormText.Password
          name="password"
          fieldProps={{
            size: 'large',
            prefix: <LockOutlined />,
          }}
          placeholder="密码"
          rules={[
            {
              required: true,
              message: '请输入密码',
            },
            {
              min: 6,
              message: '密码长度至少6位',
            },
          ]}
        />
        <ProFormText.Password
          name="confirm"
          fieldProps={{
            size: 'large',
            prefix: <LockOutlined />,
          }}
          placeholder="确认密码"
          dependencies={['password']}
          rules={[
            {
              required: true,
              message: '请确认密码',
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        />
      </LoginForm>
    </div>
  );
};

export default Register; 