# TDX Server Socket.IO 升级说明

## 概述

本次升级为 `tdxserver.py` 添加了 Socket.IO 支持，实现了品种订阅和实时 tick 数据推送功能。升级后的服务器可以同时支持：

1. **HTTP API 服务**：原有的 K线和报价数据接口（端口 5003）
2. **Socket.IO 服务**：新增的实时 tick 数据订阅服务（端口 5004）

## 主要功能

### 1. 品种订阅管理
- 支持多个客户端同时订阅同一品种
- 自动管理订阅关系和客户端连接
- 客户端断开时自动清理订阅

### 2. 实时 tick 数据推送
- 基于通达信 API 获取实时报价数据
- 每秒推送一次数据给订阅的客户端
- 支持沪深股票、港股、期货等多种市场

### 3. 异步架构
- 使用 asyncio + Socket.IO 实现高并发
- 独立的数据推送线程，不阻塞主服务
- 支持多客户端并发连接

## 技术架构

### 服务器架构
```
┌─────────────────┐    ┌─────────────────┐
│   Flask HTTP    │    │   Socket.IO     │
│   服务器         │    │   服务器         │
│   (端口 5003)   │    │   (端口 5004)   │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────────┐
         │   通达信 API 层      │
         │   (TdxHq/TdxExHq)   │
         └─────────────────────┘
```

### 数据流程
```
客户端订阅 → 订阅管理器 → tick数据线程 → 通达信API → 数据广播 → 订阅客户端
```

## 安装和配置

### 1. 安装依赖
```bash
pip install aiohttp==3.9.3
```

或者使用 requirements.txt：
```bash
pip install -r requirements.txt
```

### 2. 启动服务器
```bash
python tdxserver.py
```

启动后会看到：
```
TDX 数据服务启动完成:
  - HTTP API 服务: http://127.0.0.1:5003
  - Socket.IO 服务: http://127.0.0.1:5004
  - Socket.IO 连接路径: /socket.io
```

## 客户端使用

### 1. 安装客户端依赖
```bash
pip install python-socketio
```

### 2. 基本使用示例

```python
import socketio
import time

# 创建 Socket.IO 客户端
sio = socketio.Client()

# 设置事件处理器
@sio.event
def connect():
    print("已连接到 TDX Socket.IO 服务器")

@sio.event
def tick_data(data):
    symbol = data.get('symbol')
    tick_info = data.get('data', {})
    print(f"收到 {symbol} tick 数据: 价格={tick_info.get('price')}")

# 连接到服务器
sio.connect('http://127.0.0.1:5004')

# 订阅品种
sio.emit('subscribe_tick', {
    'market': 'sh',
    'symbol': '000001'
})

# 等待接收数据
time.sleep(30)

# 取消订阅
sio.emit('unsubscribe_tick', {
    'market': 'sh', 
    'symbol': '000001'
})

# 断开连接
sio.disconnect()
```

### 3. 测试脚本
运行提供的测试脚本：
```bash
python test_tdx_socketio_client.py
```

## Socket.IO 事件

### 客户端发送事件

#### subscribe_tick
订阅 tick 数据
```javascript
{
    "market": "sh",     // 市场代码 (sh/sz/hk等)
    "symbol": "000001"  // 品种代码
}
```

#### unsubscribe_tick
取消订阅 tick 数据
```javascript
{
    "market": "sh",
    "symbol": "000001"
}
```

### 服务器发送事件

#### connected
连接成功确认
```javascript
{
    "message": "TDX 数据服务连接成功"
}
```

#### subscribed
订阅成功确认
```javascript
{
    "symbol": "000001",
    "market": "sh",
    "full_symbol": "sh:000001",
    "message": "成功订阅 sh:000001"
}
```

#### tick_data
实时 tick 数据
```javascript
{
    "symbol": "sh:000001",
    "timestamp": 1703123456789,
    "data": {
        "code": "000001",
        "market": "sh",
        "name": "上证指数",
        "price": 3000.00,
        "last_close": 2995.50,
        "open": 2998.00,
        "high": 3005.00,
        "low": 2990.00,
        "volume": 1234567,
        "amount": 12345678900,
        "bid1": 2999.50,
        "bid_vol1": 100,
        "ask1": 3000.50,
        "ask_vol1": 200,
        "time": "14:30:15"
    }
}
```

#### error
错误消息
```javascript
{
    "message": "错误描述"
}
```

## 支持的市场

| 市场代码 | 说明 | API类型 |
|---------|------|---------|
| sh | 上海A股/指数 | HQ |
| sz | 深圳A股 | HQ |
| bj | 北京A股 | HQ |
| hk | 香港股票 | ExHQ |
| us | 美国股票 | ExHQ |
| shfe | 上期所 | ExHQ |
| dce | 大商所 | ExHQ |
| czce | 郑商所 | ExHQ |
| cffex | 中金所 | ExHQ |

## 性能特点

- **并发支持**：支持多个客户端同时连接
- **资源优化**：只有在有订阅时才获取数据
- **自动清理**：客户端断开时自动清理订阅
- **错误处理**：完善的异常处理和日志记录
- **推送频率**：每秒推送一次数据

## 注意事项

1. **通达信客户端**：确保通达信客户端正在运行
2. **端口占用**：确保端口 5003 和 5004 未被占用
3. **网络连接**：确保能够连接到通达信服务器
4. **数据频率**：当前设置为每秒推送一次，可根据需要调整
5. **资源使用**：大量订阅可能增加系统资源使用

## 故障排除

### 1. 连接失败
- 检查服务器是否正常启动
- 确认端口 5004 未被占用
- 检查防火墙设置

### 2. 无数据推送
- 确认通达信客户端正在运行
- 检查品种代码和市场代码是否正确
- 查看服务器日志输出

### 3. 订阅失败
- 检查订阅参数格式
- 确认市场代码在支持列表中
- 查看错误事件消息

## 扩展开发

### 1. 添加新的事件类型
在 `tdxserver.py` 中添加新的 `@sio.event` 处理器

### 2. 修改推送频率
调整 `tick_data_worker` 函数中的 `time.sleep(1)` 参数

### 3. 添加数据过滤
在 `get_tick_data` 函数中添加数据过滤逻辑

### 4. 集成到现有系统
可以将 Socket.IO 服务集成到现有的端口池管理系统中
