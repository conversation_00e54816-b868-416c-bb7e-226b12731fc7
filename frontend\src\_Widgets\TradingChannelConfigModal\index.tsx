// frontend/src/_Widgets/TradingChannelConfigModal/index.tsx
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, message as antMessage, Divider, Select } from 'antd';
import { EventBus } from '@/events/eventBus';
import { StrategyEvents } from '@/events/events';

const TradingChannelConfigModal: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [channelType, setChannelType] = useState<string>('');
  const [channelName, setChannelName] = useState<string>('');
  const [isEditMode, setIsEditMode] = useState(false);

  // 监听显示配置对话框事件
  useEffect(() => {
    const handleShowConfig = (payload: StrategyEvents.ShowTradingChannelConfigPayload) => {
      console.log('[交易通道配置] 显示配置对话框:', payload);
      
      setChannelType(payload.channelType);
      setChannelName(payload.channelName);
      setIsEditMode(!!payload.existingConfig);
      
      // 重置表单
      form.resetFields();
      
      // 如果有现有配置，填充表单
      if (payload.existingConfig) {
        form.setFieldsValue({
          configName: payload.existingConfig.configName,
          broker: payload.existingConfig.broker,
          username: payload.existingConfig.username,
          password: payload.existingConfig.password,
          appid: payload.existingConfig.appid || '',
          authcode: payload.existingConfig.authcode || '',
          frontAddress: payload.existingConfig.frontAddress || getDefaultFrontAddress(payload.channelType)
        });
      } else {
        // 设置默认值
        form.setFieldsValue({
          configName: `${payload.channelName}配置`,
          broker: getDefaultBroker(payload.channelType),
          frontAddress: getDefaultFrontAddress(payload.channelType),
          appid: '',
          authcode: ''
        });
      }
      
      setVisible(true);
    };

    const subscription = EventBus.on(StrategyEvents.Types.SHOW_TRADING_CHANNEL_CONFIG, handleShowConfig);

    return () => {
      subscription.unsubscribe();
    };
  }, [form]);

  // 获取默认经纪商代码
  const getDefaultBroker = (type: string): string => {
    switch (type) {
      case 'openctp':
        return '9000';
      case 'miniQMT':
        return '0000';
      default:
        return '';
    }
  };

  // 获取默认前置地址
  const getDefaultFrontAddress = (type: string): string => {
    switch (type) {
      case 'openctp':
        return 'tcp://************:4400';
      case 'miniQMT':
        return 'tcp://127.0.0.1:8080';
      default:
        return '';
    }
  };

  // 处理取消
  const handleCancel = () => {
    setVisible(false);
  };

  // 处理提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 构建配置对象
      const config: StrategyEvents.TradingChannelConfig = {
        channelType: channelType,
        configName: values.configName,
        broker: values.broker || '', // 允许为空字符串
        username: values.username,
        password: values.password,
        appid: values.appid || '', // 允许为空字符串
        authcode: values.authcode || '', // 允许为空字符串
        frontAddress: values.frontAddress
      };

      console.log('[交易通道配置] 保存配置:', config);

      // 发送保存配置事件
      EventBus.emit(StrategyEvents.Types.SAVE_TRADING_CHANNEL_CONFIG, {
        config: config,
        callback: (success: boolean, message?: string) => {
          setLoading(false);
          
          if (success) {
            setVisible(false);
            antMessage.success(isEditMode ? '配置已更新' : '配置已保存');
            // 刷新交易通道列表
            EventBus.emit(StrategyEvents.Types.GET_AVAILABLE_TRADING_CHANNELS, {
              callback: (refreshSuccess: boolean, channels: StrategyEvents.AvailableTradingChannel[]) => {
                if (refreshSuccess) {
                  console.log('[交易通道配置] 交易通道列表已刷新');
                }
              }
            });
          } else {
            antMessage.error(`保存失败: ${message || '未知错误'}`);
          }
        }
      });
    } catch (error) {
      setLoading(false);
      console.error('[交易通道配置] 表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={`${channelName} - ${isEditMode ? '编辑配置' : '新建配置'}`}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="back" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          {isEditMode ? '更新配置' : '保存配置'}
        </Button>,
      ]}
      width={600}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        requiredMark="optional"
      >
        <Divider orientation="left">基本信息</Divider>

        <Form.Item
          name="configName"
          label="配置名称"
          rules={[{ required: true, message: '请输入配置名称' }]}
        >
          <Input placeholder="请输入配置名称，用于区分不同的账户" />
        </Form.Item>

        <Form.Item
          name="frontAddress"
          label="前置地址"
          rules={[{ required: true, message: '请输入前置地址' }]}
        >
          <Input placeholder="例如: tcp://************:4400" />
        </Form.Item>

        <Form.Item
          name="broker"
          label="经纪商代码"
          tooltip="可以为空，为空时将使用空字符串"
        >
          <Input placeholder="请输入经纪商代码（可选）" />
        </Form.Item>

        <Divider orientation="left">账户信息</Divider>

        <Form.Item
          name="username"
          label="交易账户"
          rules={[{ required: true, message: '请输入交易账户' }]}
        >
          <Input placeholder="请输入您的交易账户" />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[{ required: true, message: '请输入密码' }]}
        >
          <Input.Password placeholder="请输入密码" />
        </Form.Item>

        <Divider orientation="left">高级设置</Divider>

        <Form.Item
          name="appid"
          label="应用ID"
          tooltip="可以为空，为空时将使用空字符串"
        >
          <Input placeholder="应用ID（可选）" />
        </Form.Item>

        <Form.Item
          name="authcode"
          label="认证码"
          tooltip="可以为空，为空时将使用空字符串"
        >
          <Input placeholder="认证码（可选）" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TradingChannelConfigModal;
