const { ChatSession, ChatSessionMember, ChatMessage } = require('../models');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const config = require('../config.json');

// JWT密钥从配置文件获取
const JWT_SECRET = config.jwt.secret;

// 聊天文件上传配置
const chatFileUpload = multer({
  storage: multer.diskStorage({
    destination: './uploads/chat/',
    filename: (req, file, cb) => {
      // 聊天文件命名规则
    }
  }),
  limits: {
    fileSize: 50 * 1024 * 1024  // 50MB
  }
});

class ChatHandler {
  constructor() {
    console.log('[ChatHandler] Constructor');
    this.sessions = new Map(); // sessionId -> Set<WebSocket>
  }

  // 实现一个console.log包装函数
  // 允许无限参数，就像console.log一样
  log(...args) {
    // 如果message以[WebSocket]开头，则不输出
    if (!args[0].startsWith('[WebSocket]')) {
      console.log('[ChatHandler] ' + args.join(' '));
    }
  }

  // 初始化WebSocket连接
  initWebSocket(wss) {
    this.log('[ChatHandler] initWebSocket');
    this.wss = wss;
    this.wss.on('connection', async (ws, req) => {
      try {
        // 在连接开始时就设置事件处理器
        ws.on('close', (code, reason) => {
          this.log('[WebSocket] Connection closed with code:', code, 'reason:', JSON.stringify(reason));
        });

        ws.on('error', (error) => {
          console.error('[WebSocket] Connection error:', error);
        });

        this.log('[WebSocket] New connection attempt:', req.url);
        
        // 从 WebSocket protocol 中获取 token
        const token = req.headers['sec-websocket-protocol'];
        this.log('[WebSocket] Protocol:', token);
        
        if (!token) {
          console.error('[WebSocket] No token provided');
          ws.close(4000, 'No token provided');
          return;
        }

        try {
          const decoded = jwt.verify(token, JWT_SECRET);
          ws.userId = decoded.id;
          this.log('[WebSocket] User authenticated:', decoded.id);
        } catch (error) {
          console.error('[WebSocket] Invalid token:', error);
          ws.close(4001, 'Invalid token');
          return;
        }

        // 从 URL 查询参数中获取 sessionId
        const urlStr = req.url || '';
        const queryStr = urlStr.split('?')[1] || '';
        const searchParams = new URLSearchParams(queryStr);
        const sessionId = parseInt(searchParams.get('sessionId') || '');
        
        this.log('[WebSocket] Session ID:', sessionId);

        if (!sessionId) {
          console.error('[WebSocket] Invalid session ID');
          ws.close(4002, 'Invalid session ID');
          return;
        }

        // 将连接添加到session
        this.addToSession(sessionId, ws);
        
        // 发送历史消息
        //await this.sendChatHistory(ws, sessionId);

        // 处理消息
        ws.on('message', async (data) => {
          try {
            const message = JSON.parse(data);
            this.log('[WebSocket] 服务端接受到WebSocket消息:', message);
            
            switch (message.type) {
              case 'ping':
                if (message.isHeartbeat) {
                  ws.send(JSON.stringify({ type: 'pong' }));
                }
                break;
                
              case 'message':
                if (!message.data) {
                  console.error('[WebSocket] Message data is missing');
                  ws.close(4004, 'Message data is missing');
                  return;
                }
                
                // 处理普通消息
                message.data.userId = ws.userId;
                const savedMessage = await this.handleChatMessage(sessionId, message.data);
                if (savedMessage) {
                  this.broadcast(sessionId, {
                    type: 'message',
                    message: savedMessage
                  });
                }
                break;
                
              case 'get_history':
                this.log('[WebSocket] case get_history call sendChatHistory');
                // 直接调用已有的发送历史消息方法
                await this.sendChatHistory(ws, sessionId);
                break;

              default:
                console.warn(`[WebSocket] Unknown message type: ${message.type}`);
                ws.close(4005, 'Unknown message type');
            }
          } catch (error) {
            console.error('[WebSocket] Error processing message:', error);
            ws.close(4006, 'Error processing message');
          }
        });

        ws.onopen = (event) => {
          this.log('[WebSocket] Connection opened');
        };

        ws.onclose = (event) => {
          this.log('[WebSocket] Connection closed with code:', event.code, 'reason:', JSON.stringify(event.reason));
          this.removeFromSession(sessionId, ws);
        };

      } catch (error) {
        console.error('[WebSocket] Connection error:', error);
        ws.close(4003, 'Internal server error');
      }
    });
  }

  addToSession(sessionId, ws) {
    console.log('[ChatHandler] addToSession');
    if (!this.sessions.has(sessionId)) {
      this.sessions.set(sessionId, new Set());
    }
    this.sessions.get(sessionId).add(ws);
  }

  removeFromSession(sessionId, ws) {
    console.log('[ChatHandler] removeFromSession');
    const session = this.sessions.get(sessionId);
    if (session) {
      session.delete(ws);
      if (session.size === 0) {
        this.sessions.delete(sessionId);
      }
    }
  }

  broadcast(sessionId, data) {
    console.log('[ChatHandler] broadcast');
    const session = this.sessions.get(sessionId);
    if (session) {
      const message = JSON.stringify(data);
      session.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(message);
        }
      });
    }
  }

  // 获取或创建默认会话
  async getDefaultSession(userId) {
    console.log('[ChatHandler] getDefaultSession');
    try {
      let session = await ChatSession.findOne({
        where: { type: 'default' },
        order: [['id', 'ASC']]
      });

      if (!session) {
        console.log('[ChatHandler] 创建 default session');

        session = await ChatSession.create({
          name: 'Default Chat',
          type: 'default',
          createdat: new Date()
        });

        // 创建会话结构
        await this.createSessionStructure(session.id);
        
        // 添加创建者为会话成员
        await ChatSessionMember.create({
          sessionid: session.id,
          userid: userId,
          role: 'owner',
          lastreadat: new Date()
        });
      } else {
        console.log('[ChatHandler] 检查用户是否已经是会话成员 ID=',userId);

        // 检查用户是否已经是会话成员
        const existingMember = await ChatSessionMember.findOne({
          where: {
            sessionid: session.id,
            userid: userId
          }
        });

        // 如果用户不是会话成员，则添加
        if (!existingMember) {
          await ChatSessionMember.create({
            sessionid: session.id,
            userid: userId,
            role: 'member',
            lastreadat: new Date()
          });
        }
      }

      return session;
    } catch (error) {
      console.error('[ChatHandler] Failed to get default session:', error);
      throw error;
    }
  }

  // 创建会话目录结构
  async createSessionStructure(sessionId) {
    console.log('[ChatHandler] createSessionStructure');
    try {
      // 创建文件上传目录
      const uploadDir = path.join(__dirname, `../database/uploadfiles/${sessionId}`);
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
    } catch (error) {
      console.error('[ChatHandler] Failed to create session structure:', error);
      throw error;
    }
  }

  // 处理消息发送
  async handleMessage(messageData) {
    console.log('[ChatHandler] handleMessage');
    try {
      const sessionId = messageData.sessionId;
      const userId = messageData.senderId;
      
      console.log('[ChatHandler] Processing message:', {
        sessionId,
        userId,
        content: messageData.content,
        type: messageData.type
      });

      // 验证用户是否是会话成员
      const member = await ChatSessionMember.findOne({
        where: {
          sessionid: sessionId,
          userid: userId
        }
      });

      if (!member) {
        throw new Error('User is not a member of this session');
      }

      // 保存消息并获取最新消息列表
      const latestMessages = await ChatMessage.Service.create(sessionId, {
        senderid: userId,
        type: messageData.type,
        content: messageData.content,
        fileurl: messageData.fileurl,
        filename: messageData.filename,
        filesize: messageData.filesize,
        filetype: messageData.filetype
      });

      return latestMessages; // 直接返回服务层提供的最新消息数组

    } catch (error) {
      console.error('[ChatHandler] Failed to handle message:', error);
      throw error;
    }
  }

  // 聊天文件上传处理
  async handleFileUpload(req, res) {
    console.log('[ChatHandler] handleFileUpload');
    try {
      const file = req.file;
      const fileUrl = `/uploads/chat/${file.filename}`;
      
      // 处理聊天文件上传逻辑
      
      res.json({
        success: true,
        url: fileUrl
      });
    } catch (error) {
      // 错误处理
    }
  }

  // 在需要操作消息的地方使用新的服务
  async saveMessage(sessionId, messageData) {
    console.log('[ChatHandler] saveMessage');
    try {
      return await ChatMessage.Service.create(sessionId, {
        senderid: messageData.senderId,
        type: messageData.type,
        content: messageData.content,
        // ... other fields
      });
    } catch (error) {
      console.error('[ChatHandler] Failed to save message:', error);
      throw error;
    }
  }

  async getSessionMessages(sessionId, options = {}) {
    console.log('[ChatHandler] getSessionMessages');
    try {
      return await ChatMessage.Service.findAll(sessionId, options);
    } catch (error) {
      console.error('[ChatHandler] Failed to get session messages:', error);
      throw error;
    }
  }

  async sendChatHistory(ws, sessionId) {
    console.log('[ChatHandler] sendChatHistory');
    try {
      // 获取最近的聊天记录 (例如最近 50 条)
      const messages = await ChatMessage.Service.findAll(sessionId, { limit: 50 });
      
      this.log(`[WebSocket] Sending ${messages.length} history messages to user`, ws.userId, 'for session', sessionId);

      // 格式化历史消息
      const historyData = {
        type: 'history',
        messages: messages
      };

      // 发送历史消息
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(historyData));
        this.log('[WebSocket] History sent successfully.');
      } else {
        this.log('[WebSocket] WebSocket not open, cannot send history.');
      }
    } catch (error) {
      console.error('[WebSocket] Failed to send chat history:', error);
      // 可以考虑向客户端发送错误消息
      if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({ type: 'error', message: 'Failed to load chat history.' }));
      }
    }
  }

  async handleChatMessage(sessionId, messageData) {
    console.log('[ChatHandler] handleChatMessage');
    try {
      // 保存消息到数据库
      const savedMessage = await ChatMessage.Service.create(sessionId, {
        senderid: messageData.userId,
        type: messageData.type,
        content: messageData.content,
        fileurl: messageData.fileurl,
        filename: messageData.filename,
        filesize: messageData.filesize,
        filetype: messageData.filetype,
      });
      
      this.log('[ChatHandler] Message saved:', savedMessage);
      return savedMessage;

    } catch (error) {
      console.error('[ChatHandler] Failed to handle chat message:', error);
      return null;
    }
  }
}

module.exports = new ChatHandler(); 