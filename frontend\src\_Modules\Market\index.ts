import { KLineData } from '@/shared_types/market';
import { Symbol } from '@/shared_types/market';

/**
 * 市场数据过滤条件接口
 */
export interface MarketFilter {
  market?: string;          // 市场类型
  symbol?: string;          // 品种代码
  startTime?: number;       // 开始时间
  endTime?: number;         // 结束时间
  [key: string]: any;       // 其他过滤条件
}

/**
 * 市场扫描结果接口
 */
export interface ScanResult {
  symbol: string;           // 品种代码
  market: string;          // 市场类型
  score: number;           // 匹配分数
  data?: any;              // 附加数据
  time: number;            // 扫描时间
}

/**
 * 市场扫描器接口
 */
export interface MarketScanner {
  name: string;                    // 扫描器名称
  description: string;             // 扫描器描述
  scan(data: KLineData[]): ScanResult;  // 扫描方法
}

/**
 * 市场管理器
 * 提供全市场数据的管理、过滤、扫描等功能
 */
export class MarketManager {
  private symbols: Map<string, Symbol> = new Map();  // 品种信息映射
  private scanners: Map<string, MarketScanner> = new Map();  // 扫描器映射
  private static instance: MarketManager;  // 单例实例

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): MarketManager {
    if (!MarketManager.instance) {
      MarketManager.instance = new MarketManager();
    }
    return MarketManager.instance;
  }

  /**
   * 注册品种信息
   * @param symbolInfo - 品种信息
   */
  registerSymbol(symbolInfo: Symbol): void {
    this.symbols.set(symbolInfo.code, symbolInfo);
  }

  /**
   * 注册扫描器
   * @param scanner - 市场扫描器
   */
  registerScanner(scanner: MarketScanner): void {
    this.scanners.set(scanner.name, scanner);
  }

  /**
   * 获取品种信息
   * @param symbol - 品种代码
   */
  getSymbolInfo(symbol: string): Symbol | undefined {
    return this.symbols.get(symbol);
  }

  /**
   * 获取所有品种信息
   * @param filter - 过滤条件
   */
  getSymbols(filter?: MarketFilter): Symbol[] {
    let result = Array.from(this.symbols.values());
    
    if (filter) {
      if (filter.market) {
        result = result.filter(info => info.market === filter.market);
      }
      if (typeof filter.symbol === 'string') {
        result = result.filter(info => info.code.includes(filter.symbol as string));
      }
      // 可以添加更多过滤条件
    }
    
    return result;
  }

  /**
   * 执行市场扫描
   * @param scannerName - 扫描器名称
   * @param filter - 过滤条件
   */
  async scan(scannerName: string, filter?: MarketFilter): Promise<ScanResult[]> {
    const scanner = this.scanners.get(scannerName);
    if (!scanner) {
      throw new Error(`Scanner ${scannerName} not found`);
    }

    const symbols = this.getSymbols(filter);
    const results: ScanResult[] = [];

    // TODO: 实现并行扫描逻辑
    for (const symbol of symbols) {
      try {
        // TODO: 从数据服务获取K线数据
        const klineData: KLineData[] = []; // 需要实现数据获取逻辑
        const result = scanner.scan(klineData);
        results.push(result);
      } catch (error) {
        console.error(`Scan failed for ${symbol.code}:`, error);
      }
    }

    return results.sort((a, b) => b.score - a.score);
  }

  /**
   * 清除所有数据
   */
  clear(): void {
    this.symbols.clear();
    this.scanners.clear();
  }
} 