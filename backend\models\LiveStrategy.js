const { DataTypes } = require('sequelize');
const { STRATEGY_TYPE_MAP } = require('../shared_types/strategy');

module.exports = (sequelize, DataTypes) => {
  /**
   * 实盘策略模型
   * @typedef {object} LiveStrategyAttributes
   * @property {string} id - 实盘策略ID (主键, UUID)
   * @property {number} userId - 用户ID (外键)
   * @property {string} username - 用户名
   * @property {string} strategyId - 原策略ID
   * @property {string} name - 策略名称
   * @property {string} strategyType - 策略类型
   * @property {'running'|'stopped'|'error'|'deploying'} status - 状态
   * @property {string} accountId - 交易账户ID
   * @property {number} initialCapital - 初始资金
   * @property {number} commissionRate - 手续费率
   * @property {object} riskSettings - 风险设置
   * @property {Date} createdAt - 创建时间
   * @property {Date} startTime - 启动时间
   * @property {Date} lastUpdateTime - 最后更新时间
   * @property {string} yaml - 策略YAML内容
   */

  /** @type {import('sequelize').ModelStatic<import('sequelize').Model<LiveStrategyAttributes>>} */
  const LiveStrategy = sequelize.define('LiveStrategy', {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      field: 'id',
      allowNull: false,
      comment: '实盘策略ID (UUID)'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'userid',
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '用户ID'
    },
    username: {
      type: DataTypes.STRING(50),
      field: 'username',
      allowNull: false,
      comment: '用户名'
    },
    strategyId: {
      type: DataTypes.STRING(100),
      field: 'strategyid',
      allowNull: false,
      comment: '原策略ID'
    },
    name: {
      type: DataTypes.STRING(100),
      field: 'name',
      allowNull: false,
      comment: '策略名称'
    },
    strategyType: {
      type: DataTypes.STRING(50),
      field: 'strategytype',
      allowNull: false,
      defaultValue: STRATEGY_TYPE_MAP.PORTFOLIO,
      comment: '策略类型'
    },
    status: {
      type: DataTypes.ENUM('running', 'stopped', 'error', 'deploying'),
      field: 'status',
      allowNull: false,
      defaultValue: 'stopped',
      comment: '策略状态'
    },
    accountId: {
      type: DataTypes.STRING(100),
      field: 'accountid',
      allowNull: false,
      comment: '交易账户ID'
    },
    initialCapital: {
      type: DataTypes.DECIMAL(15, 2),
      field: 'initialcapital',
      allowNull: false,
      comment: '初始资金'
    },
    commissionRate: {
      type: DataTypes.DECIMAL(10, 6),
      field: 'commissionrate',
      allowNull: false,
      defaultValue: 0.0003,
      comment: '手续费率'
    },
    riskSettings: {
      type: DataTypes.JSON,
      field: 'risksettings',
      allowNull: false,
      comment: '风险设置 JSON'
    },
    timeframe: {
      type: DataTypes.STRING(16),
      field: 'timeframe',
      allowNull: true,
      comment: 'K线级别'
    },
    commodity: {
      type: DataTypes.STRING(10),
      field: 'commodity',
      allowNull: false,
      comment: '品种'
    },
    createdAt: {
      type: DataTypes.DATE,
      field: 'createdat',
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    startTime: {
      type: DataTypes.DATE,
      field: 'starttime',
      allowNull: true,
      comment: '启动时间'
    },
    lastUpdateTime: {
      type: DataTypes.DATE,
      field: 'lastupdatetime',
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '最后更新时间'
    },
    yaml: {
      type: DataTypes.TEXT,
      field: 'yaml',
      allowNull: false,
      comment: '策略YAML内容'
    }
  }, {
    tableName: 'live_strategies',
    timestamps: false, // 不使用 Sequelize 自动添加的 createdAt 和 updatedAt 字段
    indexes: [
      {
        name: 'idx_live_strategies_user_id',
        fields: ['userId']
      },
      {
        name: 'idx_live_strategies_strategy_id',
        fields: ['strategyId']
      }
    ]
  });

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  LiveStrategy.associate = function(models) {
    // 与用户模型关联
    LiveStrategy.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });

    // 与组合策略关联表关联
    LiveStrategy.hasMany(models.GroupStrategy, {
      foreignKey: 'strategyid',
      as: 'groupStrategies'
    });
  };

  return LiveStrategy;
};
