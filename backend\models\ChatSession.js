const { Sequelize, DataTypes } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const ChatSession = sequelize.define('ChatSession', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false
    },
    createdat: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'chatsessions',
    timestamps: false
  });

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  ChatSession.associate = function(models) {
    // 一个会话有多个成员
    ChatSession.hasMany(models.ChatSessionMember, {
      foreignKey: 'sessionid',
      as: 'members'
    });
    // 一个会话有多条消息
    ChatSession.hasMany(models.ChatMessage, {
        foreignKey: 'sessionid',
        as: 'messages'
      });
  };

  return ChatSession;
}; 