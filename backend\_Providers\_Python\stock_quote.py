# -*- coding: utf-8 -*-
"""
OpenCTP股票行情获取程序
获取A股实时行情数据，如600000浦发银行

使用OpenCTP兼容接口连接CTP实盘行情前置
"""
import time
from openctp_ctp import thostmduserapi as mdapi

class StockMdSpi(mdapi.CThostFtdcMdSpi):
    def __init__(self, api):
        super().__init__()
        self.api = api

    def OnFrontConnected(self):
        print("股票行情服务器连接成功")
        req = mdapi.CThostFtdcReqUserLoginField()
        req.BrokerID = ""
        req.UserID = ""      # 股票行情通常不需要用户名
        req.Password = ""    # 股票行情通常不需要密码
        self.api.ReqUserLogin(req, 1)

    def OnRspUserLogin(self, pRspUserLogin, pRspInfo, nRequestID, bIsLast):
        print("股票行情登录成功")
        # 订阅多只股票
        instruments = [
            "600000",  # 浦发银行
            "000001",  # 平安银行
            "600036",  # 招商银行
            "000002"   # 万科A
        ]
        print(f"订阅股票: {', '.join(instruments)}")
        self.api.SubscribeMarketData([i.encode('gbk') for i in instruments], len(instruments))

    def OnRtnDepthMarketData(self, pDepthMarketData):
        symbol = pDepthMarketData.InstrumentID.decode('gbk')
        last_price = pDepthMarketData.LastPrice
        volume = pDepthMarketData.Volume
        turnover = pDepthMarketData.Turnover
        update_time = pDepthMarketData.UpdateTime.decode('gbk')
        
        print(f"[股票行情] {symbol}: 价格={last_price:.2f}, 成交量={volume}, 成交额={turnover:.0f}, 时间={update_time}")

# 创建API
api = mdapi.CThostFtdcMdApi.CreateFtdcMdApi()
spi = StockMdSpi(api)

# 连接到CTP实盘行情前置（支持股票）
api.RegisterFront("tcp://**************:42213")
api.RegisterSpi(spi)
api.Init()

print("股票行情客户端启动中...")
print("正在获取600000浦发银行等股票行情...")

# 保持运行
while True:
    time.sleep(1)
