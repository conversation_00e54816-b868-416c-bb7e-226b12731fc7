import { init, OverlayTemplate, registerOverlay, OverlayFigure, Coordinate, Point, Chart, Overlay, Axis } from 'klinecharts';

function isNumber (value: unknown): value is number {
  return typeof value === 'number' && Number.isFinite(value)
}

// 注册测量线段覆盖物
export const registerMeasure = () => {
  registerOverlay({
    name: 'measure',
    totalStep: 4,  // 总共需要4个步骤来完成测量线段的绘制
    needDefaultPointFigure: true,  // 启用定点显示
    needDefaultXAxisFigure: true,  // 启用X轴图形
    needDefaultYAxisFigure: true,  // 启用Y轴图形

    // 处理移动时的实时预览 (注释掉)
    /*
    performEventMoveForDrawing: ({ currentStep, points, performPoint }) => {
      if (currentStep === 2 || currentStep === 4) {
        // 更新最后一个点的位置
        points[points.length - 1].timestamp = performPoint.timestamp;
        points[points.length - 1].dataIndex = performPoint.dataIndex;
      }
    },
    */

    // 处理点的编辑 (注释掉)
    /*
    performEventPressedMove: ({ points, performPoint }) => {
      // 只更新被拖动的点
      const point = points[performPoint.dataIndex];
      if (point) {
        point.timestamp = performPoint.timestamp;
        point.dataIndex = performPoint.dataIndex;
      }
    },
    */

    // 修正签名：移除 points，因为它通过 overlay.points 访问，修正 yAxis 类型
    createPointFigures: ({ chart, overlay, coordinates, bounding, yAxis }: { chart: Chart, overlay: Overlay, coordinates: Coordinate[], bounding: any, yAxis: Axis | null }) => {
      const figures: OverlayFigure[] = [];
      const points = overlay.points; // 获取原始点信息以访问 value

      // 第一步：确定起点，不绘制任何图形
      if (coordinates.length === 1) {
        return figures;
      }

      // 第二步：绘制第一条线段
      if (coordinates.length >= 2) {
        const startPoint = coordinates[0];
        const secondPoint = coordinates[1];

        figures.push({
          type: 'line',
          attrs: {
            coordinates: [startPoint, secondPoint]
          },
          styles: {
            style: 'stroke' // 保持实线
          }
        });
      }

      // 第三步及以后：绘制第二条线段及辅助线
      if (coordinates.length >= 3) {
        const secondPoint = coordinates[1];
        const thirdPoint = coordinates[2];

        figures.push({
          type: 'line',
          attrs: {
            coordinates: [secondPoint, thirdPoint]
          },
          styles: {
            style: 'stroke' // 保持实线
          }
        });

        // 计算delta - 需要原始点的 value
        const startPointOriginal = points[0];
        const secondPointOriginal = points[1];
        const thirdPointOriginal = points[2]; // 需要第三个原始点的值

        // 确保所有需要的原始点和值存在
        if (!startPointOriginal?.value || !secondPointOriginal?.value || !thirdPointOriginal?.value) {
          return figures; // 如果缺少值，则不绘制后续内容
        }

        const delta = secondPointOriginal.value - startPointOriginal.value;
        const deltaAbs = Math.abs(delta);
        const valueThird = thirdPointOriginal.value;

        // 计算点A的Y坐标 (像素)
        const deltaYPixel = coordinates[0].y - coordinates[1].y; // Y轴方向相反
        const pointAY = thirdPoint.y - deltaYPixel; // 统一使用减法
        const pointA = {
          x: thirdPoint.x,
          y: pointAY
        };

        // 计算点B的Y坐标 (像素)
        const pointBY = pointA.y - deltaYPixel; // 统一使用减法
        const pointB = {
          x: thirdPoint.x,
          y: pointBY
        };

        // 绘制点A相关垂直线段 - 改为虚线
        figures.push({
          type: 'line',
          attrs: {
            coordinates: [thirdPoint, pointA]
          },
          styles: {
            style: 'stroke',
            strokeDasharray: [4, 4] // 设置为虚线
          }
        });

        // 绘制点A的水平线 - 保持虚线
        figures.push({
          type: 'line',
          attrs: {
            coordinates: [
              { x: thirdPoint.x - 30, y: pointA.y },
              { x: thirdPoint.x + 30, y: pointA.y }
            ]
          },
          styles: {
            style: 'stroke',
            strokeDasharray: [4, 4]
          }
        });

        // 绘制点B相关垂直线段 - 改为虚线
        figures.push({
          type: 'line',
          attrs: {
            coordinates: [pointA, pointB]
          },
          styles: {
            style: 'stroke',
            strokeDasharray: [4, 4] // 设置为虚线
          }
        });

        // 绘制点B的水平线 - 保持虚线
        figures.push({
          type: 'line',
          attrs: {
            coordinates: [
              { x: thirdPoint.x - 30, y: pointB.y },
              { x: thirdPoint.x + 30, y: pointB.y }
            ]
          },
          styles: {
            style: 'stroke',
            strokeDasharray: [4, 4]
          }
        });

        // --- 添加价格文本 ---
        // 确定精度 - 模仿 fibonacciLine
        let precision = 2; // 默认精度
        // 假设此覆盖物主要用于价格轴
        precision = chart.getPrecision().price ?? 2;

        // 计算pointA和pointB的价格值 (基于第三点的值和差值)
        const valueA = valueThird + (delta > 0 ? deltaAbs : -deltaAbs);
        const valueB = valueA + (delta > 0 ? deltaAbs : -deltaAbs); // 或者 valueThird + 2 * (delta > 0 ? deltaAbs : -deltaAbs)

        // 格式化价格文本 - 模仿 fibonacciLine
        const textA = isNumber(valueA)
          ? chart.getDecimalFold().format(
              chart.getThousandsSeparator().format(valueA.toFixed(precision))
            )
          : 'N/A';
        const textB = isNumber(valueB)
          ? chart.getDecimalFold().format(
              chart.getThousandsSeparator().format(valueB.toFixed(precision))
            )
          : 'N/A';

        // 添加pointA的价格文本
        figures.push({
          type: 'text',
          attrs: {
            x: thirdPoint.x - 35, // 放置在水平线左侧
            y: pointA.y,
            text: textA,
            align: 'right', // 右对齐
            baseline: 'middle'
          },
          styles: {
            style: 'stroke_fill', // 恢复 stroke_fill
            strokeColor: 'rgba(150, 150, 150, 0.8)', // 灰色边框，80%透明度
            color: '#333333', // 使用 'color' 属性设置深灰色文本
            borderSize: 1, // 边框宽度
            backgroundColor: 'transparent', // 显式设置透明背景
            paddingLeft: 4,
            paddingRight: 4,
            paddingTop: 3, // 增加顶部内边距
            paddingBottom: 1 // 减少底部内边距
          }
        });

         // 添加pointB的价格文本
         figures.push({
          type: 'text',
          attrs: {
            x: thirdPoint.x - 35, // 放置在水平线左侧
            y: pointB.y,
            text: textB,
            align: 'right', // 右对齐
            baseline: 'middle'
          },
          styles: {
            style: 'stroke_fill', // 恢复 stroke_fill
            strokeColor: 'rgba(150, 150, 150, 0.8)', // 灰色边框，80%透明度
            color: '#333333', // 使用 'color' 属性设置深灰色文本
            borderSize: 1, // 边框宽度
            backgroundColor: 'transparent', // 显式设置透明背景
            paddingLeft: 4,
            paddingRight: 4,
            paddingTop: 3, // 增加顶部内边距
            paddingBottom: 1 // 减少底部内边距
          }
        });

        // 显示原始的距离文本 (格式化)
        const startPointCoord = coordinates[0]; // 使用坐标点计算文本位置
        const deltaText = chart.getDecimalFold().format(
          chart.getThousandsSeparator().format(deltaAbs.toFixed(precision))
        );
        figures.push({
          type: 'text',
          attrs: {
            x: (startPointCoord.x + thirdPoint.x) / 2,
            y: (startPointCoord.y + thirdPoint.y) / 2,
            text: deltaText, // 使用格式化后的差值文本
            align: 'center',
            baseline: 'middle'
          },
          styles: {
            style: 'fill'
          }
        });
      }

      return figures;
    }
  });
};