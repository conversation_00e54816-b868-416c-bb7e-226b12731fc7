import { init, OverlayTemplate, registerOverlay } from 'klinecharts';

export const registerPriceChannel = () => {
  registerOverlay({
    name: 'rectangle',
    totalStep: 3,
    needDefaultPointFigure: true,
    needDefaultXAxisFigure: true,
    needDefaultYAxisFigure: true,
    createPointFigures: ({ coordinates }) => {
      if (coordinates.length >= 2) {
        const startPoint = coordinates[0];
        const endPoint = coordinates[1];
        
        // 计算矩形的位置和大小
        const x = Math.min(startPoint.x, endPoint.x);
        const y = Math.min(startPoint.y, endPoint.y);
        const width = Math.abs(endPoint.x - startPoint.x);
        const height = Math.abs(endPoint.y - startPoint.y);
        
        return {
          type: 'rect',
          attrs: {
            x,
            y,
            width,
            height
          },
          styles: {
            style: 'stroke_fill'
          }
        }
      }
      return [];
    }
  });
}
