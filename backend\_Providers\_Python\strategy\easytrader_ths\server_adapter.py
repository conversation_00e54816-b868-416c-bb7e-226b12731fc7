#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths服务端适配器

该模块实现了服务端适配器，用于接收客户端的反向连接，
并提供API接口供后端策略调用。
"""

import os
import json
import time
import logging
import threading
import requests
from datetime import datetime

logger = logging.getLogger("EasyTrader-THS-ServerAdapter")

class ServerAdapter:
    """同花顺交易服务端适配器"""
    
    def __init__(self):
        """初始化服务端适配器"""
        self.clients = {}  # 用户名 -> 客户端信息
        self.client_lock = threading.Lock()
        self.last_cleanup = time.time()
        self.cleanup_interval = 300  # 5分钟清理一次过期客户端
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_expired_clients, daemon=True)
        self.cleanup_thread.start()
        
        logger.info("初始化同花顺交易服务端适配器")
    
    def register_client(self, username, client_ip, client_port, client_type, timestamp):
        """
        注册客户端
        
        Args:
            username: 用户名
            client_ip: 客户端IP
            client_port: 客户端端口
            client_type: 客户端类型
            timestamp: 时间戳
        
        Returns:
            bool: 是否注册成功
        """
        with self.client_lock:
            client_url = f"http://{client_ip}:{client_port}"
            
            # 检查客户端连接
            try:
                response = requests.get(f"{client_url}/api/status", timeout=5)
                if response.status_code != 200:
                    logger.error(f"客户端连接失败: {client_url}")
                    return False
            except Exception as e:
                logger.error(f"客户端连接失败: {client_url}, 错误: {str(e)}")
                return False
            
            # 注册客户端
            self.clients[username] = {
                'username': username,
                'client_ip': client_ip,
                'client_port': client_port,
                'client_url': client_url,
                'client_type': client_type,
                'last_heartbeat': timestamp,
                'registered_at': timestamp
            }
            
            logger.info(f"注册客户端成功: username={username}, client_url={client_url}")
            return True
    
    def update_client_heartbeat(self, username, timestamp):
        """
        更新客户端心跳
        
        Args:
            username: 用户名
            timestamp: 时间戳
        
        Returns:
            bool: 是否更新成功
        """
        with self.client_lock:
            if username not in self.clients:
                logger.warning(f"更新心跳失败: 客户端不存在, username={username}")
                return False
            
            self.clients[username]['last_heartbeat'] = timestamp
            return True
    
    def get_client(self, username):
        """
        获取客户端信息
        
        Args:
            username: 用户名
        
        Returns:
            dict: 客户端信息，如果不存在则返回None
        """
        with self.client_lock:
            return self.clients.get(username)
    
    def get_all_clients(self):
        """
        获取所有客户端信息
        
        Returns:
            list: 客户端信息列表
        """
        with self.client_lock:
            return list(self.clients.values())
    
    def remove_client(self, username):
        """
        移除客户端
        
        Args:
            username: 用户名
        
        Returns:
            bool: 是否移除成功
        """
        with self.client_lock:
            if username in self.clients:
                del self.clients[username]
                logger.info(f"移除客户端: username={username}")
                return True
            return False
    
    def _cleanup_expired_clients(self):
        """清理过期客户端"""
        while True:
            time.sleep(60)  # 每分钟检查一次
            
            now = time.time()
            if now - self.last_cleanup < self.cleanup_interval:
                continue
            
            self.last_cleanup = now
            expired_usernames = []
            
            with self.client_lock:
                for username, client in self.clients.items():
                    # 如果超过10分钟没有心跳，则认为客户端已过期
                    if now - client['last_heartbeat'] > 600:
                        expired_usernames.append(username)
            
            # 移除过期客户端
            for username in expired_usernames:
                self.remove_client(username)
                logger.info(f"清理过期客户端: username={username}")
    
    def execute_trade(self, username, action, params):
        """
        执行交易
        
        Args:
            username: 用户名
            action: 交易动作，如'buy', 'sell', 'cancel'
            params: 交易参数
        
        Returns:
            dict: 交易结果
        """
        client = self.get_client(username)
        if not client:
            raise ValueError(f"客户端不存在: username={username}")
        
        client_url = client['client_url']
        
        try:
            if action == 'buy':
                response = requests.post(
                    f"{client_url}/api/buy",
                    json=params,
                    timeout=10
                )
            elif action == 'sell':
                response = requests.post(
                    f"{client_url}/api/sell",
                    json=params,
                    timeout=10
                )
            elif action == 'cancel':
                response = requests.post(
                    f"{client_url}/api/cancel",
                    json=params,
                    timeout=10
                )
            else:
                raise ValueError(f"不支持的交易动作: {action}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data")
                else:
                    raise Exception(result.get("message", "未知错误"))
            else:
                raise Exception(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"执行交易失败: username={username}, action={action}, error={str(e)}")
            raise
    
    def get_account_info(self, username):
        """
        获取账户信息
        
        Args:
            username: 用户名
        
        Returns:
            dict: 账户信息
        """
        client = self.get_client(username)
        if not client:
            raise ValueError(f"客户端不存在: username={username}")
        
        client_url = client['client_url']
        
        try:
            response = requests.get(f"{client_url}/api/balance", timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data")
                else:
                    raise Exception(result.get("message", "未知错误"))
            else:
                raise Exception(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"获取账户信息失败: username={username}, error={str(e)}")
            raise
    
    def get_positions(self, username):
        """
        获取持仓信息
        
        Args:
            username: 用户名
        
        Returns:
            list: 持仓信息列表
        """
        client = self.get_client(username)
        if not client:
            raise ValueError(f"客户端不存在: username={username}")
        
        client_url = client['client_url']
        
        try:
            response = requests.get(f"{client_url}/api/position", timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data")
                else:
                    raise Exception(result.get("message", "未知错误"))
            else:
                raise Exception(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"获取持仓信息失败: username={username}, error={str(e)}")
            raise
    
    def get_today_entrusts(self, username):
        """
        获取今日委托
        
        Args:
            username: 用户名
        
        Returns:
            list: 今日委托列表
        """
        client = self.get_client(username)
        if not client:
            raise ValueError(f"客户端不存在: username={username}")
        
        client_url = client['client_url']
        
        try:
            response = requests.get(f"{client_url}/api/today_entrusts", timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data")
                else:
                    raise Exception(result.get("message", "未知错误"))
            else:
                raise Exception(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"获取今日委托失败: username={username}, error={str(e)}")
            raise
    
    def get_today_trades(self, username):
        """
        获取今日成交
        
        Args:
            username: 用户名
        
        Returns:
            list: 今日成交列表
        """
        client = self.get_client(username)
        if not client:
            raise ValueError(f"客户端不存在: username={username}")
        
        client_url = client['client_url']
        
        try:
            response = requests.get(f"{client_url}/api/today_trades", timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data")
                else:
                    raise Exception(result.get("message", "未知错误"))
            else:
                raise Exception(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"获取今日成交失败: username={username}, error={str(e)}")
            raise
    
    def refresh_client(self, username):
        """
        刷新客户端
        
        Args:
            username: 用户名
        
        Returns:
            bool: 是否刷新成功
        """
        client = self.get_client(username)
        if not client:
            raise ValueError(f"客户端不存在: username={username}")
        
        client_url = client['client_url']
        
        try:
            response = requests.post(f"{client_url}/api/refresh", timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return True
                else:
                    raise Exception(result.get("message", "未知错误"))
            else:
                raise Exception(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"刷新客户端失败: username={username}, error={str(e)}")
            raise
