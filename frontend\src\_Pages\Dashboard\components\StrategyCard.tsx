import React from 'react';
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import { Typography, Tag, Button, Space, Row, Col, Image, Tooltip } from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  PlayCircleOutlined,
  ExperimentOutlined,
  StopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  LineChartOutlined,
  ArrowDownOutlined,
  RiseOutlined, // 用于夏普比率
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Statistic } = StatisticCard;

// 定义策略数据结构（可以从 shared_types 引入）
interface StrategyPerformance {
  profitRate?: number; // 收益率
  maxDrawdown?: number; // 最大回撤
  sharpeRatio?: number; // 夏普比率
  // 可以添加更多指标...
}

interface Strategy {
  id: string;
  name: string;
  configName?: string; // 参数配置名称
  type: 'live' | 'backtest'; // 策略类型：实盘或回测
  status: 'running' | 'stopped' | 'error' | 'finished' | 'pending'; // 策略状态
  performance?: StrategyPerformance; // 策略表现
  thumbnail?: string; // 效果缩略图 URL
  description?: string; // 策略描述（可选）
}

interface StrategyCardProps {
  strategy: Strategy;
}

// 状态标签映射
const statusTagMap: Record<Strategy['status'], React.ReactNode> = {
  running: <Tag icon={<SyncOutlined spin />} color="processing">运行中</Tag>,
  stopped: <Tag icon={<StopOutlined />} color="warning">已停止</Tag>,
  error: <Tag icon={<CloseCircleOutlined />} color="error">错误</Tag>,
  finished: <Tag icon={<CheckCircleOutlined />} color="success">已完成</Tag>,
  pending: <Tag color="default">待运行</Tag>,
};

// 类型标签映射
const typeTagMap: Record<Strategy['type'], React.ReactNode> = {
  live: <Tag color="blue">实盘</Tag>,
  backtest: <Tag color="green">回测</Tag>,
};

const StrategyCard: React.FC<StrategyCardProps> = ({ strategy }) => {
  const { id, name, configName, type, status, performance, thumbnail, description } = strategy;

  // 根据状态决定操作按钮
  const renderActions = () => {
    const actions = [
      <Tooltip title="查看详情" key="view"><Button type="text" icon={<EyeOutlined />} /></Tooltip>,
      <Tooltip title="编辑参数" key="edit"><Button type="text" icon={<EditOutlined />} /></Tooltip>,
    ];
    if (type === 'live') {
      if (status === 'running') {
        actions.push(<Tooltip title="停止策略" key="stop"><Button type="text" danger icon={<StopOutlined />} /></Tooltip>);
      } else {
        actions.push(<Tooltip title="运行策略" key="run"><Button type="text" icon={<PlayCircleOutlined />} /></Tooltip>);
      }
    } else { // backtest
      actions.push(<Tooltip title="运行回测" key="backtest"><Button type="text" icon={<ExperimentOutlined />} /></Tooltip>);
    }
    return actions;
  };

  return (
    <ProCard
      hoverable
      bordered
      style={{ height: '100%' }} // 确保卡片高度一致
      actions={renderActions()}
    >
      <Row gutter={8} align="middle" style={{ marginBottom: 8 }}>
        <Col flex="auto">
          <Text strong style={{ fontSize: '16px', marginRight: 8 }} ellipsis={{ tooltip: name }}>
            {name}
          </Text>
        </Col>
        <Col>
          {typeTagMap[type]}
        </Col>
        <Col>
          {statusTagMap[status]}
        </Col>
      </Row>
      {configName && (
        <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: 8 }} ellipsis={{ tooltip: configName }}>
          配置: {configName}
        </Text>
      )}
      {description && (
         <Paragraph type="secondary" ellipsis={{ rows: 2, expandable: true, symbol: '更多' }} style={{ marginBottom: 12 }}>
           {description}
         </Paragraph>
      )}

      <Row gutter={8} style={{ marginBottom: 16 }}>
        {/* 效果缩略图 */}
        <Col span={10}>
          <Image
            width="100%"
            height={80} // 固定高度
            src={thumbnail || 'https://via.placeholder.com/150x80?text=No+Chart'} // 占位图
            alt={`${name} performance chart`}
            preview={false} // 不需要预览大图
            style={{ objectFit: 'cover' }}
          />
        </Col>
        {/* 关键指标 */}
        <Col span={14}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <Statistic
                title="收益率"
                value={performance?.profitRate ?? '-'}
                precision={1}
                suffix="%"
                valueStyle={{ color: (performance?.profitRate ?? 0) >= 0 ? '#52c41a' : '#f5222d', fontSize: '14px' }}
                prefix={<LineChartOutlined />}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="最大回撤"
                value={performance?.maxDrawdown ?? '-'}
                precision={1}
                suffix="%"
                valueStyle={{ fontSize: '14px' }} // 回撤通常看绝对值，不标红绿
                prefix={<ArrowDownOutlined />}
              />
            </Col>
            <Col span={12}>
               <Statistic
                 title="夏普比率"
                 value={performance?.sharpeRatio ?? '-'}
                 precision={2}
                 valueStyle={{ fontSize: '14px' }}
                 prefix={<RiseOutlined />} // 使用趋势上升图标表示夏普比率
               />
             </Col>
             {/* 可以添加更多指标 */}
          </Row>
        </Col>
      </Row>
    </ProCard>
  );
};

export default StrategyCard; 