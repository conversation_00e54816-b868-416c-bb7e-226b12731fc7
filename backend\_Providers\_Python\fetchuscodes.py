# fetch_us_stocks.py
import sqlite3
import csv
import io
import os
import datetime
import ftplib # Re-enabled for FTP
import requests # Need requests for Alpha Vantage API
from urllib.parse import urlparse # Re-enabled for FTP

# --- 配置 ---
# NASDAQ 股票列表文件 URL (Re-enabled)
NASDAQ_LISTED_URL = "ftp://ftp.nasdaqtrader.com/SymbolDirectory/nasdaqlisted.txt"
# OTHER_LISTED_URL = "ftp://ftp.nasdaqtrader.com/SymbolDirectory/otherlisted.txt" # This source wasn't working reliably

# Alpha Vantage API Configuration
ALPHA_VANTAGE_API_URL = "https://www.alphavantage.co/query"
# !!! 重要: 请在此处替换为您自己的 Alpha Vantage API 密钥 !!!
ALPHA_VANTAGE_API_KEY = "1P7CR44GUO6MOJ1S"

# 数据库文件路径 (相对于脚本所在目录)
DB_FILE = "uscodes.sqlite"
TABLE_NAME = "us_stocks"

# Target Exchanges for Alpha Vantage (Non-NASDAQ)
TARGET_EXCHANGES_AV = {"NYSE", "AMEX", "ARCA", "BATS"}

# --- 函数定义 ---

def download_and_parse_nasdaq_ftp(url, exchange_override='NASDAQ'):
    """
    下载并解析 NASDAQ 提供的 nasdaqlisted.txt 文件 (处理 FTP URL).
    (Refactored from previous version)

    Args:
        url (str): nasdaqlisted.txt 文件的 URL (应为 ftp://...).
        exchange_override (str): 指定交易所名称.

    Returns:
        list: 包含字典的列表，每个字典代表一只股票
              {'code': str, 'name': str, 'exchange': str, 'market': 'STOCK'}
              如果下载或解析失败则返回空列表.
    """
    stocks = []
    ftp = None
    try:
        # Parse FTP URL
        parsed_url = urlparse(url)
        server = parsed_url.netloc
        path = parsed_url.path
        directory = os.path.dirname(path)
        filename = os.path.basename(path)

        print(f"[信息][NASDAQ FTP] 正在连接 FTP 服务器: {server}")
        ftp = ftplib.FTP(server, timeout=60)
        ftp.login()
        print(f"[信息][NASDAQ FTP] FTP 连接成功. 切换目录到: {directory}")
        ftp.cwd(directory)

        print(f"[信息][NASDAQ FTP] 正在下载文件: {filename}")
        file_content_bytes = io.BytesIO()
        ftp.retrbinary(f'RETR {filename}', file_content_bytes.write)
        ftp.quit()
        ftp = None
        print(f"[信息][NASDAQ FTP] 文件下载完成. FTP 连接已关闭.")

        file_content_bytes.seek(0)
        try:
            content_text = file_content_bytes.read().decode('utf-8')
        except UnicodeDecodeError:
            print("[警告][NASDAQ FTP] UTF-8 解码失败，尝试使用 latin-1")
            file_content_bytes.seek(0)
            content_text = file_content_bytes.read().decode('latin-1')

        content = content_text.strip().split('\n')
        if len(content) < 3:
            print(f"[警告][NASDAQ FTP] 文件内容过少，跳过处理: {url}")
            return []

        data_reader = csv.reader(content[1:-1], delimiter='|')
        print(f"[信息][NASDAQ FTP] 正在解析数据...")
        count = 0
        skipped_etf = 0
        skipped_test = 0
        for row in data_reader:
            if not row: continue
            try:
                # nasdaqlisted.txt format: Symbol|Security Name|Market Category|Test Issue|Financial Status|Round Lot Size|ETF|NextShares
                if len(row) < 8: continue
                code = row[0].strip()
                name = row[1].strip()
                is_etf = row[6].strip() == 'Y'
                is_test = row[3].strip() == 'Y'
                exchange = exchange_override

                if is_etf:
                    skipped_etf += 1
                    continue
                if is_test:
                    skipped_test += 1
                    continue

                if code and name and exchange:
                    stocks.append({
                        'code': code,
                        'name': name,
                        'exchange': exchange,
                        'market': 'STOCK'
                    })
                    count += 1
            except IndexError as e:
                print(f"[警告][NASDAQ FTP] 解析行时出错: {row} - {e}")
                continue

        print(f"[信息][NASDAQ FTP] 解析完成: 共找到 {count} 只股票 (跳过 {skipped_etf} ETF, {skipped_test} 测试)")

    except ftplib.all_errors as e:
        print(f"[错误][NASDAQ FTP] FTP 操作失败: {url} - {e}")
    except Exception as e:
        print(f"[错误][NASDAQ FTP] 处理文件时发生未知错误: {url} - {e}")
    finally:
        if ftp:
            try: ftp.quit()
            except ftplib.all_errors: pass

    return stocks

def fetch_alpha_vantage_listings(api_key):
    """
    使用 Alpha Vantage API (LISTING_STATUS) 获取非纳斯达克股票代码列表.
    (Function remains largely the same, added logging prefix)
    Args:
        api_key (str): Alpha Vantage API 密钥.

    Returns:
        list: 包含字典的列表...
              仅包含 TARGET_EXCHANGES_AV 中定义的交易所的活跃股票 ('Active', 'Stock').
    """
    stocks = []
    if not api_key or api_key == "YOUR_ALPHA_VANTAGE_API_KEY":
        print("[错误][AlphaVantage] 未配置 Alpha Vantage API 密钥. 请修改脚本并填入您的密钥.")
        return []

    params = {
        "function": "LISTING_STATUS",
        "apikey": api_key,
        "state": "active"
    }
    print(f"[信息][AlphaVantage] 正在从 Alpha Vantage 获取符号列表 (LISTING_STATUS)...")

    try:
        response = requests.get(ALPHA_VANTAGE_API_URL, params=params, timeout=180)
        response.raise_for_status()
        csv_data = response.text
        print(f"[信息][AlphaVantage] 从 Alpha Vantage 成功获取 CSV 数据.")

        csvfile = io.StringIO(csv_data)
        data_reader = csv.DictReader(csvfile)

        count = 0
        skipped_asset = 0
        skipped_exchange = 0
        skipped_status = 0

        for row in data_reader:
            try:
                asset_type = row.get('assetType')
                exchange = row.get('exchange')
                status = row.get('status')
                code = row.get('symbol')
                name = row.get('name')

                # Filter for non-NASDAQ active stocks
                if asset_type == 'Stock' and exchange in TARGET_EXCHANGES_AV and status == 'Active':
                    if code and name:
                        stocks.append({
                            'code': code,
                            'name': name,
                            'exchange': exchange,
                            'market': 'STOCK'
                        })
                        count += 1
                    else:
                         skipped_asset +=1
                elif asset_type != 'Stock':
                    skipped_asset += 1
                elif exchange not in TARGET_EXCHANGES_AV:
                    skipped_exchange += 1 # This will count NASDAQ and others
                elif status != 'Active':
                    skipped_status += 1

            except Exception as row_e:
                print(f"[警告][AlphaVantage] 解析行时出错: {row} - {row_e}")
                continue

        print(f"[信息][AlphaVantage] 解析完成: 共找到 {count} 只目标交易所的活跃股票.")
        print(f"[信息][AlphaVantage] 跳过 {skipped_asset} 条非股票记录, 跳过 {skipped_exchange} 条非目标交易所(含NASDAQ)记录, 跳过 {skipped_status} 条非活跃记录.")

    except requests.exceptions.RequestException as e:
        print(f"[错误][AlphaVantage] 请求 Alpha Vantage API 时出错: {e}")
    except Exception as e:
        print(f"[错误][AlphaVantage] 处理 Alpha Vantage 响应时发生未知错误: {e}")

    return stocks


def init_db(db_path):
    """ (No changes needed) """
    print(f"[信息] 正在连接数据库: {db_path}")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute(f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code TEXT PRIMARY KEY,
        name TEXT,
        exchange TEXT,
        market TEXT
    )
    """)
    conn.commit()
    print(f"[信息] 数据库表 '{TABLE_NAME}' 已准备就绪.")
    return conn, cursor

def update_stock_data(conn, cursor, stocks):
    """ (No changes needed, uses INSERT OR REPLACE) """
    if not stocks:
        print("[警告] 没有要插入的股票数据.")
        return

    print(f"[信息] 正在清空旧数据 (表: {TABLE_NAME})...")
    cursor.execute(f"DELETE FROM {TABLE_NAME}")
    conn.commit()
    print(f"[信息] 旧数据已清空.")

    print(f"[信息] 正在插入 {len(stocks)} 条新数据 (使用 INSERT OR REPLACE)...")
    data_to_insert = [
        (s['code'], s['name'], s['exchange'], s['market']) for s in stocks
    ]
    cursor.executemany(f"""
    INSERT OR REPLACE INTO {TABLE_NAME} (code, name, exchange, market)
    VALUES (?, ?, ?, ?)
    """, data_to_insert)
    conn.commit()
    print(f"[信息] 数据插入完成.")

# --- 主程序 ---
if __name__ == "__main__":
    print(f"--- 开始获取 **完整** 美国股票列表 (NASDAQ + Alpha Vantage) ({datetime.datetime.now()}) ---")

    # 确定数据库文件的绝对路径
    script_dir = os.path.dirname(__file__)
    db_path = os.path.join(script_dir, DB_FILE)
    print(f"[信息] 数据库文件将保存在: {db_path}")

    # 1. 获取 NASDAQ 列表 (FTP)
    print("\n[步骤 1/3] 尝试从 NASDAQ FTP 获取符号...")
    nasdaq_stocks = download_and_parse_nasdaq_ftp(NASDAQ_LISTED_URL)

    # 2. 从 Alpha Vantage 获取其他交易所符号
    print("\n[步骤 2/3] 尝试从 Alpha Vantage 获取其他交易所符号...")
    other_stocks = fetch_alpha_vantage_listings(ALPHA_VANTAGE_API_KEY)

    # 3. 合并和去重
    print("\n[步骤 3/3] 合并列表并去重...")
    combined_stocks = nasdaq_stocks + other_stocks
    print(f"[信息] 合并前总数 (NASDAQ: {len(nasdaq_stocks)}, 其他: {len(other_stocks)}): {len(combined_stocks)}")

    # 基于股票代码去重 (保留第一次出现的记录)
    unique_stocks_dict = {}
    for stock in combined_stocks:
        code = stock.get('code')
        if code and code not in unique_stocks_dict:
            unique_stocks_dict[code] = stock

    all_stocks = list(unique_stocks_dict.values())
    removed_count = len(combined_stocks) - len(all_stocks)
    print(f"[信息] 去重后总数: {len(all_stocks)} (移除了 {removed_count} 条重复记录)")


    if not all_stocks:
        print("\n[错误] 未能从任何来源获取到股票数据.")
    else:
        print(f"\n[信息] 共获取到 {len(all_stocks)} 只唯一的股票信息.")
        # 初始化数据库
        conn, cursor = init_db(db_path)

        # 更新数据库 (使用完整的合并列表)
        update_stock_data(conn, cursor, all_stocks)

        # 关闭数据库连接
        conn.close()
        print("[信息] 数据库连接已关闭.")

    print(f"\n--- 获取 **完整** 美国股票列表完成 ({datetime.datetime.now()}) ---")
