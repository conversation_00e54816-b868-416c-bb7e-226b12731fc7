# test_tdx_sh_index.py
from pytdx.hq import TdxHq_API
import pandas as pd

# TDX 服务器信息 (可以使用常见的行情服务器)
TDX_HOST = '**************'
TDX_PORT = 7709

# 指数信息
INDEX_CODE = '000001' # 上证指数代码
MARKET_ID = 1         # 市场 ID (1 代表上海)
KLINE_PERIOD = 4      # K 线周期 (4 代表日线)
DATA_COUNT = 50       # 获取数据条数

def fetch_sh_index_daily_kline():
    """
    连接通达信行情服务器，获取上证指数日线数据。
    """
    api = TdxHq_API()
    print(f"尝试连接通达信服务器: {TDX_HOST}:{TDX_PORT}...")

    try:
        # 连接服务器
        if api.connect(TDX_HOST, TDX_PORT):
            print("连接成功！")

            # 获取 K 线数据
            print(f"正在获取上证指数 ({INDEX_CODE}) 最近 {DATA_COUNT} 条日线数据...")
            # 参数: K线周期, 市场ID, 证券代码, 起始位置, 获取数量
            data = api.get_index_bars(KLINE_PERIOD, MARKET_ID, INDEX_CODE, 0, DATA_COUNT)

            if data:
                print("数据获取成功！正在转换为 DataFrame...")
                # 转换为 DataFrame
                df = api.to_df(data)

                # --- 打印原始日期时间 ---
                if 'datetime' in df.columns:
                    print("原始 datetime 列（未格式化）:")
                    print(df['datetime'])
                # --- 格式化日期 --- 
                if 'datetime' in df.columns:
                    try:
                        # 确保是 datetime 对象，然后格式化为 'YYYY-MM-DD'
                        df['datetime'] = pd.to_datetime(df['datetime']).dt.strftime('%Y-%m-%d')
                        print("日期列已格式化为 YYYY-MM-DD")
                    except Exception as fmt_err:
                        print(f"格式化日期时出错: {fmt_err}. 将显示原始日期时间。")
                # --- 结束格式化日期 ---

                print("最近 50 条上证指数日线数据:")
                # 打印 DataFrame (可以根据需要调整显示选项)
                with pd.option_context('display.max_rows', None, 'display.max_columns', None): # 显示所有行和列
                    print(df)
            else:
                print("未能获取到数据。")

            # 断开连接
            api.disconnect()
            print("连接已断开。")
        else:
            print("连接失败！请检查服务器地址和端口，或网络连接。")

    except Exception as e:
        print(f"发生错误: {e}")
        # 确保在异常时也尝试断开连接
        if api and getattr(api, 'client', None):
            api.disconnect()
            print("连接已断开（异常处理）。")

if __name__ == '__main__':
    fetch_sh_index_daily_kline()