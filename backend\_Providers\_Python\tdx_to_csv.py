import struct
import datetime
import os
import sys
import csv
from pathlib import Path

def convert_date(date_int):
    """将通达信的日期格式转换为可读格式"""
    year = date_int // 10000
    month = (date_int % 10000) // 100
    day = date_int % 100
    return f"{year}-{month:02d}-{day:02d}"

def read_day_data(file_path):
    """
    读取通达信格式的日线数据文件
    返回一个包含所有数据的列表，每个元素是一个包含日期、价格等信息的字典
    """
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件 '{file_path}' 不存在!")
        return []
        
    data_list = []
    try:
        with open(file_path, 'rb') as f:
            while True:
                data = f.read(32)  # 读取一天的数据
                if not data or len(data) < 32:  # 如果没有数据或数据不完整，则退出循环
                    break
                    
                # 解析数据
                try:
                    date, open_price, high, low, close, amount, vol, _ = struct.unpack('iiiiifii', data)
                    data_list.append({
                        'date_int': date,  # 保留原始整数日期
                        'date': convert_date(date),  # 添加可读日期
                        'open': open_price / 100.0,
                        'high': high / 100.0,
                        'low': low / 100.0,
                        'close': close / 100.0,
                        'amount': amount,
                        'vol': vol
                    })
                except struct.error as e:
                    print(f"解析数据错误: {e}")
                    continue  # 跳过这条记录，继续读取
    except IOError as e:
        print(f"读取文件错误: {e}")
        return []
    
    return data_list

def get_stock_filepath(base_dir, market, stock_code):
    """
    根据市场和股票代码获取对应的股票数据文件路径
    
    参数:
        base_dir: 数据根目录
        market: 市场类型，'sh' 或 'sz'
        stock_code: 股票代码
    
    返回:
        股票数据文件的完整路径
    """
    market_prefix = market.lower()
    market_dir = 'sh' if market_prefix == 'sh' else 'sz'
    
    file_path = os.path.join(base_dir, 'hsjday', market_dir, 'lday', f"{market_prefix}{stock_code}.day")
    return file_path

def export_to_csv(data, output_file, stock_code='', stock_name=''):
    """
    将股票数据导出为CSV文件
    
    参数:
        data: 股票数据列表
        output_file: 输出CSV文件路径
        stock_code: 股票代码
        stock_name: 股票名称
    """
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['date', 'code', 'name', 'open', 'high', 'low', 'close', 'amount', 'vol']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for item in data:
                row = {
                    'date': item['date'],
                    'code': stock_code,
                    'name': stock_name,
                    'open': item['open'],
                    'high': item['high'],
                    'low': item['low'],
                    'close': item['close'],
                    'amount': item['amount'],
                    'vol': item['vol']
                }
                writer.writerow(row)
                
        print(f"成功导出数据到 {output_file}")
    except Exception as e:
        print(f"导出CSV文件错误: {e}")

def batch_process_stocks(stock_list, base_dir, output_dir, limit=None):
    """
    批量处理多只股票数据
    
    参数:
        stock_list: 字典，{股票代码: 股票名称}
        base_dir: 数据根目录
        output_dir: 输出目录
        limit: 限制处理的股票数量
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    count = 0
    for code, name in stock_list.items():
        if limit and count >= limit:
            break
            
        # 根据股票代码判断市场
        market = 'sh' if code.startswith('6') else 'sz'
        file_path = get_stock_filepath(base_dir, market, code)
        
        print(f"\n处理 [{code}] {name}...")
        
        # 读取股票数据
        data = read_day_data(file_path)
        if not data:
            print(f"未能读取 {code} 的数据，跳过")
            continue
            
        # 输出基本信息
        print(f"  数据起始日期: {data[0]['date']}")
        print(f"  数据结束日期: {data[-1]['date']}")
        print(f"  数据总条数: {len(data)}")
        
        # 导出为CSV
        output_file = os.path.join(output_dir, f"{market}_{code}.csv")
        export_to_csv(data, output_file, code, name)
        
        count += 1
    
    print(f"\n共处理了 {count} 只股票的数据")

def main():
    # 默认路径
    data_base_dir = 'D:/projects/quantquart/stockdata'
    output_dir = 'D:/projects/quantquart/output'
    
    # 命令行参数处理
    if len(sys.argv) > 1:
        data_base_dir = sys.argv[1]
    if len(sys.argv) > 2:
        output_dir = sys.argv[2]
    
    # 单个股票处理模式
    if len(sys.argv) > 3 and sys.argv[3] == '--single':
        stock_code = sys.argv[4] if len(sys.argv) > 4 else '605066'
        market = 'sh' if stock_code.startswith('6') else 'sz'
        
        file_path = get_stock_filepath(data_base_dir, market, stock_code)
        print(f"开始读取数据文件: {file_path}...")
        
        data = read_day_data(file_path)
        
        # 检查是否成功读取到数据
        if not data:
            print("未能读取到数据，请检查文件路径是否正确")
            return
            
        # 打印数据信息
        print(f"\n数据起始日期: {data[0]['date']}")
        print(f"数据结束日期: {data[-1]['date']}")
        print(f"数据总条数: {len(data)}")
        
        print("\n最近20条数据:")
        print("日期\t\t开盘\t最高\t最低\t收盘\t成交量")
        print("-" * 70)
        for item in data[-20:]:
            print(f"{item['date']}\t{item['open']:.2f}\t{item['high']:.2f}\t{item['low']:.2f}\t{item['close']:.2f}\t{item['vol']}")
        
        # 导出到CSV
        output_file = os.path.join(output_dir, f"{market}_{stock_code}.csv")
        export_to_csv(data, output_file, stock_code)
    
    # 批量处理模式
    else:
        try:
            # 尝试导入tdxsymbols模块
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from tdxsymbols import read_stock_mapping
            
            print("开始批量处理股票数据...")
            
            # 读取通达信股票代码表
            tdx_base_path = r"D:\new_tdx\T0002\hq_cache"
            sh_file_path = os.path.join(tdx_base_path, "shs.tnf")
            sz_file_path = os.path.join(tdx_base_path, "szs.tnf")
            
            sh_stocks = read_stock_mapping(sh_file_path)
            sz_stocks = read_stock_mapping(sz_file_path)
            
            # 合并沪深两市的股票
            all_stocks = {**sh_stocks, **sz_stocks}
            
            if not all_stocks:
                print("未能读取到股票列表，请检查通达信安装路径是否正确")
                return
                
            print(f"共读取到 {len(all_stocks)} 只股票")
            
            # 批量处理股票数据
            limit = int(sys.argv[3]) if len(sys.argv) > 3 else 10  # 默认处理10只股票
            batch_process_stocks(all_stocks, data_base_dir, output_dir, limit)
            
        except ImportError:
            print("未找到tdxsymbols模块，无法批量处理")
            print("将运行单个股票处理模式...")
            
            # 使用默认方式处理单个股票
            file_path = os.path.join(data_base_dir, 'hsjday/sh/lday/sh605066.day')
            print(f"开始读取数据文件: {file_path}...")
            
            data = read_day_data(file_path)
            
            if not data:
                print("未能读取到数据，请检查文件路径是否正确")
                return
                
            print(f"\n数据起始日期: {data[0]['date']}")
            print(f"数据结束日期: {data[-1]['date']}")
            print(f"数据总条数: {len(data)}")
            
            print("\n最近20条数据:")
            print("日期\t\t开盘\t最高\t最低\t收盘\t成交量")
            print("-" * 70)
            for item in data[-20:]:
                print(f"{item['date']}\t{item['open']:.2f}\t{item['high']:.2f}\t{item['low']:.2f}\t{item['close']:.2f}\t{item['vol']}")

if __name__ == "__main__":
    main()
