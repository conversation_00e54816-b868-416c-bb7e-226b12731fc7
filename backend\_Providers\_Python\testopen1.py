from openctp_ctp import mdapi, traderapi
import threading

class CtpClient:
    def __init__(self, broker_id, user_id, password):
        self.broker_id = broker_id
        self.user_id = user_id
        self.password = password
        self.td_api = None
        self.md_api = None
        self.event = threading.Event()

    # 行情SPI实现
    class MdSpi(mdapi.CThostFtdcMdSpi):
        def __init__(self, gateway):
            super().__init__()
            self.gateway = gateway

        def OnFrontConnected(self):
            print("行情服务器连接成功")
            req = mdapi.CThostFtdcReqUserLoginField()
            req.BrokerID = self.gateway.broker_id
            req.UserID = self.gateway.user_id
            req.Password = self.gateway.password
            self.ReqUserLogin(req, 0)

        def OnRspUserLogin(self, pRspUserLogin, pRspInfo, nRequestID, bIsLast):
            if pRspInfo.ErrorID == 0:
                print("行情登录成功")
                instruments = ["rb2401"]  # 订阅螺纹钢主力合约
                self.SubscribeMarketData([i.encode('utf-8') for i in instruments])
            else:
                print(f"行情登录失败: ErrorID={pRspInfo.ErrorID}, Msg={pRspInfo.ErrorMsg}")

        def OnRtnDepthMarketData(self, pDepthMarketData):
            print(f"""
            合约: {pDepthMarketData.InstrumentID.decode('gbk')}
            最新价: {pDepthMarketData.LastPrice}
            买一价: {pDepthMarketData.BidPrice1}
            卖一价: {pDepthMarketData.AskPrice1}
            成交量: {pDepthMarketData.Volume}
            """)

    # 交易SPI实现
    class TdSpi(traderapi.CThostFtdcTraderSpi):
        def __init__(self, gateway):
            super().__init__()
            self.gateway = gateway

        def OnFrontConnected(self):
            print("交易服务器连接成功")
            req = traderapi.CThostFtdcReqUserLoginField()
            req.BrokerID = self.gateway.broker_id
            req.UserID = self.gateway.user_id
            req.Password = self.gateway.password
            self.ReqUserLogin(req, 0)

        def OnRspUserLogin(self, pRspUserLogin, pRspInfo, nRequestID, bIsLast):
            if pRspInfo.ErrorID == 0:
                print("交易登录成功")
                # 查询账户
                req = traderapi.CThostFtdcQryTradingAccountField()
                req.BrokerID = self.gateway.broker_id
                req.InvestorID = self.gateway.user_id
                self.ReqQryTradingAccount(req, 0)
            else:
                print(f"交易登录失败: ErrorID={pRspInfo.ErrorID}, Msg={pRspInfo.ErrorMsg}")

        def OnRspQryTradingAccount(self, pTradingAccount, pRspInfo, nRequestID, bIsLast):
            if pRspInfo.ErrorID == 0:
                print(f"""
                账户资金查询成功:
                可用资金: {pTradingAccount.Available}
                持仓盈亏: {pTradingAccount.PositionProfit}
                """)
            self.gateway.event.set()  # 通知主线程

    def connect(self, md_front, td_front):
        """连接行情和交易服务器"""
        # 初始化行情接口
        self.md_api = mdapi.CThostFtdcMdApi.CreateFtdcMdApi()
        self.md_spi = self.MdSpi(self)
        self.md_api.RegisterSpi(self.md_spi)
        self.md_api.RegisterFront(md_front)
        self.md_api.Init()

        # 初始化交易接口
        self.td_api = traderapi.CThostFtdcTraderApi.CreateFtdcTraderApi()
        self.td_spi = self.TdSpi(self)
        self.td_api.RegisterSpi(self.td_spi)
        self.td_api.RegisterFront(td_front)
        self.td_api.Init()

        # 等待查询完成
        self.event.wait()
        print("CTP客户端初始化完成")

if __name__ == "__main__":
    client = CtpClient(
        broker_id="",
        user_id="12362",
        password="123456"
    )
    # 使用SimNow仿真环境地址
    client.connect(
        md_front="tcp://************:31313",  # 行情前置
        td_front="tcp://*************:20002"   # 交易前置
    )