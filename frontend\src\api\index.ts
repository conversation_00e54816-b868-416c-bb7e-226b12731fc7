import axios from 'axios';
import { getToken } from '@/utils/auth';

const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器：添加 token
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    console.log('[api\\index.ts: API] Request:', {
      method: config.method,
      url: config.url,
      hasToken: !!token
    });
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default api; 