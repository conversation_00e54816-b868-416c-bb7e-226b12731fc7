.stock-pool-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* 增加全局样式，确保股票池管理面板显示在其他对话框上面 */
:global(.ant-modal-wrap) {
  z-index: 1050 !important;
}

:global(.ant-modal-mask) {
  z-index: 1049 !important;
}

.pool-header {
  display: flex;
  padding: 8px;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.pool-content {
  flex: 1;
  overflow: auto;
}

.pool-tabs {
  .ant-tabs-tab {
    padding: 8px 12px;
  }
}

.category-card {
  cursor: pointer;
  margin-bottom: 8px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  &.active {
    border-color: #1890ff;
  }

  .ant-card-body {
    padding: 12px;
  }
}

.stock-list-table {
  .ant-table-row {
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.pool-action-buttons {
  position: absolute;
  right: 12px;
  top: 12px;
  z-index: 1;
}

.pool-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
}