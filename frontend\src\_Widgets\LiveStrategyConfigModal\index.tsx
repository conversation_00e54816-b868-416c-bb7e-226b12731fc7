import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Select, Divider, Row, Col, Button, message as antMessage } from 'antd';
import { EventBus } from '@/events/eventBus';
import { StrategyEvents } from '@/events/events';
import { STRATEGY_TYPE_OPTIONS, STRATEGY_TYPE } from '@/shared_types/strategy';

interface LiveStrategyConfigModalProps {
  // 组件不需要props，通过事件总线接收数据
}

// --- 新增: 定义可用账户类型 ---
interface AvailableAccount {
  id: string; // 例如 'user1_easytrader_ths'
  name: string; // 例如 'EasyTrader THS (user1)'
}

// --- 新增: 回测基准资金常量 ---
const BACKTEST_CAPITAL_BASE = 100000;

const LiveStrategyConfigModal: React.FC<LiveStrategyConfigModalProps> = () => {
  // 状态管理
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [strategyId, setStrategyId] = useState('');
  const [strategyName, setStrategyName] = useState('');
  const [isEditMode, setIsEditMode] = useState(false); // 新增: 编辑模式标识
  const [liveStrategyId, setLiveStrategyId] = useState(''); // 新增: 实盘策略ID (编辑模式使用)
  // --- 新增: 可用账户状态 ---
  const [accountsLoading, setAccountsLoading] = useState(false);
  const [availableAccounts, setAvailableAccounts] = useState<StrategyEvents.AvailableTradingChannel[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<StrategyEvents.AvailableTradingChannel | null>(null);
  const [forceUpdate, setForceUpdate] = useState(0); // 用于强制更新组件

  // 表单实例
  const [form] = Form.useForm();

  // 监听显示配置对话框事件
  useEffect(() => {
    const handleShowLiveConfig = (payload: StrategyEvents.ShowLiveConfigPayload) => {
      console.log('[实盘配置] 显示配置对话框:', payload);
      setStrategyId(payload.strategyId);
      setStrategyName(payload.strategyName);

      // 判断是否为编辑模式 - 如果传入了liveStrategyId，则为编辑模式
      const editMode = !!payload.liveStrategyId;
      setIsEditMode(editMode);

      if (editMode && payload.liveStrategyId) {
        setLiveStrategyId(payload.liveStrategyId);
        console.log(`[实盘配置] 编辑模式，实盘策略ID: ${payload.liveStrategyId}`);
      } else {
        setLiveStrategyId('');
        console.log('[实盘配置] 新建模式');
      }

      // --- 新增: 获取可用交易通道 ---
      fetchAvailableAccounts();

      // 重置表单
      form.resetFields();

      // --- 修改: 设置默认值，考虑已有配置 ---
      // 如果 payload 包含 logicalCapital，说明是修改或重启，用它做初始资金
      const defaultInitialCapital = payload.currentLogicalCapital !== undefined && payload.currentLogicalCapital !== null
                                    ? payload.currentLogicalCapital
                                    : 100000; // 否则用标准默认值
      // 如果 payload 包含 commissionRate，用它做默认值
      const defaultCommissionRate = payload.currentCommissionRate !== undefined && payload.currentCommissionRate !== null
                                    ? payload.currentCommissionRate
                                    : 0.0002; // 否则用标准默认值 (万分之二)
      // 如果 payload 包含 accountId，设置默认选项
      const defaultAccountId = payload.currentAccountId || undefined;

      // 添加详细日志
      console.log('[实盘配置] 设置表单默认值:', {
        mode: editMode ? '编辑' : '新建',
        strategyId: payload.strategyId,
        liveStrategyId: payload.liveStrategyId,
        accountId: defaultAccountId,
        initialCapital: defaultInitialCapital,
        commissionRate: defaultCommissionRate,
        maxOrderSize: payload.currentMaxOrderSize || 100,
        maxDailyTrades: payload.currentMaxDailyTrades || 20,
        stopLossPercent: payload.currentStopLossPercent || 5
      });

      form.setFieldsValue({
        strategyType: payload.currentStrategyType || STRATEGY_TYPE.PORTFOLIO, // 默认策略类型
        accountId: defaultAccountId, // 默认选择账户 (需要等列表加载后才有效)
        initialCapital: defaultInitialCapital, // 默认初始资金
        commissionRate: defaultCommissionRate, // 默认手续费率
        // 风控设置 - 暂时保留默认值，也可从 payload 读取
        maxOrderSize: payload.currentMaxOrderSize || 100,
        maxDailyTrades: payload.currentMaxDailyTrades || 20,
        stopLossPercent: payload.currentStopLossPercent || 5,
      });
      // --- 结束修改 ---

      // 显示对话框
      setVisible(true);
    };

    const subscription = EventBus.on(StrategyEvents.Types.SHOW_LIVE_CONFIG, handleShowLiveConfig);

    return () => {
      subscription.unsubscribe();
    };
  }, [form]);

  // 处理账户选择变化
  const handleAccountChange = (accountId: string) => {
    const account = availableAccounts.find(acc => acc.id === accountId);
    setSelectedAccount(account || null);
    console.log('[实盘配置] 选择账户:', account);

    // 重要：更新表单字段值
    form.setFieldsValue({ accountId: accountId });
  };

  // 处理配置通道
  const handleConfigChannel = () => {
    if (!selectedAccount) return;

    // 从最新的availableAccounts中获取账户信息，确保状态是最新的
    const currentAccountId = form.getFieldValue('accountId') || selectedAccount.id;
    const latestAccount = availableAccounts.find(acc => acc.id === currentAccountId);

    if (!latestAccount) {
      console.error('[实盘配置] 未找到当前选择的账户:', currentAccountId);
      return;
    }

    console.log('[实盘配置] 配置通道 (最新状态):', latestAccount);

    // 如果通道已配置，需要获取现有配置
    if (latestAccount.configured) {
      // 发射事件获取现有配置
      EventBus.emit(StrategyEvents.Types.GET_TRADING_CHANNEL_CONFIG, {
        channelType: latestAccount.type,
        callback: (success: boolean, config?: StrategyEvents.TradingChannelConfig) => {
          console.log('[实盘配置] 获取配置结果:', success, config);
          // 发射显示配置对话框事件
          EventBus.emit(StrategyEvents.Types.SHOW_TRADING_CHANNEL_CONFIG, {
            channelType: latestAccount.type,
            channelName: latestAccount.name,
            existingConfig: config
          });
        }
      });
    } else {
      // 新建配置
      EventBus.emit(StrategyEvents.Types.SHOW_TRADING_CHANNEL_CONFIG, {
        channelType: latestAccount.type,
        channelName: latestAccount.name,
        existingConfig: undefined
      });
    }
  };

  // --- 修改: 获取可用交易账户函数, 使用事件总线 ---
  const fetchAvailableAccounts = () => {
    setAccountsLoading(true);
    console.log('[实盘配置] 发射事件获取可用交易账户...');
    console.log('事件类型常量:', StrategyEvents.Types.GET_AVAILABLE_TRADING_CHANNELS);
    EventBus.emit(StrategyEvents.Types.GET_AVAILABLE_TRADING_CHANNELS, {
       callback: (success: boolean, channels: StrategyEvents.AvailableTradingChannel[]) => {
           setAccountsLoading(false);
           if (success) {
               setAvailableAccounts(channels);
               console.log('[实盘配置] 成功获取账户:', channels);

               // 尝试重新应用默认值
               const currentDefault = form.getFieldValue('accountId');
               console.log('[实盘配置] 当前表单中的accountId:', currentDefault);

               if (currentDefault && channels.some(acc => acc.id === currentDefault)) {
                   // 找到匹配的账户，重新设置表单值并更新selectedAccount
                   form.setFieldsValue({ accountId: currentDefault });
                   const matchedAccount = channels.find(acc => acc.id === currentDefault);
                   setSelectedAccount(matchedAccount || null);
                   setForceUpdate(prev => prev + 1); // 强制更新组件
                   console.log('[实盘配置] 重新设置选中账户:', matchedAccount);
               } else if (channels.length > 0) {
                   // 如果没有默认值或默认值不在列表中，选择第一个
                   const firstAccount = channels[0];
                   form.setFieldsValue({ accountId: firstAccount.id });
                   setSelectedAccount(firstAccount);
                   setForceUpdate(prev => prev + 1); // 强制更新组件
                   console.log('[实盘配置] 设置第一个账户为默认:', firstAccount);
               }
           } else {
               console.error('[实盘配置] 获取可用交易账户失败');
               antMessage.error('获取可用交易账户列表失败');
               setAvailableAccounts([]); // 清空列表
           }
       }
    });
    // --- 移除模拟数据逻辑 ---
    /*
    setTimeout(() => {
      const mockAccounts: AvailableAccount[] = [
        { id: 'user1_easytrader_ths', name: '同花顺EasyTrader (user1)' },
        { id: 'user1_ctp_sim', name: 'CTP仿真 (user1)' },
        { id: 'admin_xtp_prod', name: 'XTP实盘 (admin)' },
      ];
      setAvailableAccounts(mockAccounts);
      setAccountsLoading(false);
      console.log('[实盘配置] 模拟获取账户完成:', mockAccounts);
      // 如果之前有设置默认账户ID，尝试再次设置 (因为列表加载可能晚于 setFieldsValue)
      const currentDefault = form.getFieldValue('accountId');
      if (currentDefault && mockAccounts.some(acc => acc.id === currentDefault)) {
          form.setFieldsValue({ accountId: currentDefault });
      } else if (mockAccounts.length > 0) {
          // 如果没有默认值或默认值不在新列表里，选择第一个作为新的默认值？
          // form.setFieldsValue({ accountId: mockAccounts[0].id });
      }
    }, 1000); // 模拟网络延迟
    */
  };

  // 处理取消
  const handleCancel = () => {
    setVisible(false);
  };

  // 处理提交
  const handleSubmit = async () => {
    try {
      // 表单验证
      const values = await form.validateFields();

      // 设置加载状态
      setLoading(true);

      // 构建风控设置对象
      const riskSettings = {
        maxOrderSize: values.maxOrderSize,
        maxDailyTrades: values.maxDailyTrades,
        stopLossPercent: values.stopLossPercent,
      };

      // 根据模式决定发送的事件
      if (isEditMode) {
        // 编辑模式 - 更新现有实盘策略配置
        console.log(`[实盘配置] 编辑模式，更新实盘策略: ${liveStrategyId}`);

        // 构建更新配置对象
        const updateConfig = {
          strategyType: values.strategyType,
          accountId: values.accountId,
          initialCapital: values.initialCapital,
          commissionRate: values.commissionRate,
          riskSettings: riskSettings,
        };

        // 发送更新事件
        EventBus.emit(StrategyEvents.Types.UPDATE_LIVE_STRATEGY, {
          liveStrategyId: liveStrategyId,
          config: updateConfig,
          callback: (success: boolean, message?: string) => {
            setLoading(false);

            if (success) {
              // 关闭对话框
              setVisible(false);
              // 显示成功消息
              antMessage.success('策略配置已更新');
              // 通知策略列表更新
              EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
            } else {
              // 显示错误消息
              antMessage.error(`更新失败: ${message || '未知错误'}`);
            }
          },
        });
      } else {
        // 新建模式 - 部署新的实盘策略
        console.log(`[实盘配置] 新建模式，部署策略: ${strategyId}`);

        // 构建部署配置对象
        const deployConfig: StrategyEvents.LiveStrategyConfig = {
          strategyId: strategyId,
          strategyType: values.strategyType,
          accountId: values.accountId,
          initialCapital: values.initialCapital,
          backtestCapital: BACKTEST_CAPITAL_BASE, // 固定回测资金
          commissionRate: values.commissionRate, // 手续费率
          riskSettings: riskSettings,
        };

        // 发送部署事件
        EventBus.emit(StrategyEvents.Types.DEPLOY_TO_LIVE, {
          config: deployConfig,
          callback: (success: boolean, message?: string) => {
            setLoading(false);

            if (success) {
              // 关闭对话框
              setVisible(false);
              // 显示成功消息
              antMessage.success('策略配置已保存并部署');
              // 通知策略列表更新
              EventBus.emit(StrategyEvents.Types.LIVE_STRATEGY_UPDATED, undefined);
            } else {
              // 显示错误消息
              antMessage.error(`部署失败: ${message || '未知错误'}`);
            }
          },
        });
      }
    } catch (error) {
      setLoading(false);
      console.error('[实盘配置] 表单验证或提交失败:', error);
      // 如果是表单验证错误，通常不需要额外提示
      if (error instanceof Error && !(error as any).errorFields) { // 简单判断非表单验证错误
        antMessage.error(`提交失败: ${error.message}`);
      }
    }
  }

  return (
    <Modal
      title={`实盘配置 - ${strategyName}`}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="back" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          {/* 根据是否有 strategyId 判断是新建还是修改? */}
          确认配置
        </Button>,
      ]}
      width={600}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        requiredMark="optional"
      >
        <Divider orientation="left">核心设置</Divider>

        <Form.Item
          name="strategyType"
          label="策略类型"
          rules={[{ required: true, message: '请选择策略类型' }]}
        >
          <Select placeholder="请选择策略类型">
            {STRATEGY_TYPE_OPTIONS.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="accountId"
          label="选择交易账户"
          rules={[{ required: true, message: '请选择交易账户' }]}
        >
          <div style={{ display: 'flex', gap: '8px' }}>
            <Select
              placeholder="请选择交易账户"
              loading={accountsLoading}
              style={{ flex: 1 }}
              value={form.getFieldValue('accountId')}
              onChange={handleAccountChange}
            >
              {availableAccounts.map(account => (
                <Select.Option key={account.id} value={account.id} disabled={account.status === 'offline'}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{account.name}</span>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {account.status === 'online' ? (
                        <span style={{ color: '#52c41a', fontSize: '12px' }}>●在线</span>
                      ) : (
                        <span style={{ color: '#ff4d4f', fontSize: '12px' }}>●离线</span>
                      )}
                      {account.needsConfig && !account.configured && (
                        <span style={{ color: '#fa8c16', fontSize: '12px' }}>需配置</span>
                      )}
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
            {selectedAccount && selectedAccount.needsConfig && (
              <Button
                type="default"
                onClick={handleConfigChannel}
                disabled={!selectedAccount}
              >
                {selectedAccount.configured ? '编辑配置' : '配置'}
              </Button>
            )}
          </div>
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="initialCapital"
              label="初始/当前逻辑资金"
              rules={[{ required: true, message: '请输入资金额' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0} // 允许从0开始?
                // max={*********} // 移除最大限制?
                formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => Number((value || '').replace(/\¥\s?|(,*)/g, '')) as any}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="commissionRate"
              label="手续费率 (小数)"
              tooltip="例如: 万分之二 输入 0.0002"
              rules={[{ required: true, message: '请输入手续费率' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={0.1} // 最多10%?
                step={0.0001}
                // precision={4} // 控制小数位数
                formatter={(value) => `${(value || 0) * 100}%`} // 显示为百分比?
                parser={(value) => Number(parseFloat((value || '').replace('%', '')) / 100) as any}
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">风控设置</Divider>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="maxOrderSize"
              label="单笔最大下单量"
              rules={[{ required: true, message: '请输入最大下单量' }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              name="maxDailyTrades"
              label="日最大交易次数"
              rules={[{ required: true, message: '请输入最大交易次数' }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              name="stopLossPercent"
              label="止损比例 (%) "
              tooltip="基于初始/当前逻辑资金计算的亏损比例"
              rules={[{ required: true, message: '请输入止损比例' }]}
            >
              <InputNumber
                min={0.1}
                max={100}
                step={0.1}
                style={{ width: '100%' }}
                 formatter={(value) => `${value}%`}
                 parser={(value) => Number((value || '').replace('%', '')) as any}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default LiveStrategyConfigModal;
