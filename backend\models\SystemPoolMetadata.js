'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SystemPoolMetadata extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // 定义关联关系
      // 一个元数据记录属于一个系统池
      SystemPoolMetadata.belongsTo(models.Pool, {
        foreignKey: 'pool_id',
        as: 'pool' // 别名
                   // 外键约束和 ON DELETE CASCADE 推荐通过 migration 添加
                   // 应用层需保证关联的 Pool.user_id 为 NULL
      });
    }
  }

  SystemPoolMetadata.init({
    pool_id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      allowNull: false,
      comment: '关联的系统池ID (pools.user_id IS NULL)'
      // references 和 ON DELETE CASCADE 推荐通过 migration 添加
    },
    market_type: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: '市场类型 (如 STOCK_CN, STOCK_US)'
    },
    region: {
      type: DataTypes.STRING(10),
      allowNull: true,
      comment: '地区代码 (如 CN, US, HK)'
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '板块/指数类别 (如 科技, 金融)'
    },
    display_name: {
      type: DataTypes.STRING(128),
      allowNull: false,
      comment: '前端显示名称'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '描述信息'
    },
    display_order: {
      type: DataTypes.INTEGER,
      allowNull: true, // 允许 NULL
      defaultValue: 0,
      comment: '显示排序'
    },
    is_featured: {
      type: DataTypes.BOOLEAN,
      allowNull: true, // 允许 NULL
      defaultValue: false,
      comment: '是否精选推荐'
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP'), // 使用数据库默认值
      comment: '元数据更新时间'
    }
  }, {
    sequelize,
    modelName: 'SystemPoolMetadata',
    tableName: 'system_pool_metadata',
    timestamps: true, // 使用 Sequelize 时间戳
    createdAt: false, // 没有 createdAt 字段
    updatedAt: 'updated_at', // 将 updatedAt 映射到 updated_at
    comment: '系统预定义池子的元数据'
    // 发现索引 (idx_system_pool_meta_discovery) 推荐通过 migration 创建
  });

  return SystemPoolMetadata;
}; 