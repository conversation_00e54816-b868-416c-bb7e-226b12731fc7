# testtdxrt.py
# 通过HTTP接口请求服务端/kline，获取K线数据
import requests

API_URL = "http://127.0.0.1:5003/kline"

test_cases = [
    ('sh', '510300', 'day'),
    ('sh', '600000', 'day'),
    ('sz', '000001', 'day'),
    ('sh', '510500', 'day'),
]

for market, code, period in test_cases:
    params = {
        'market': market,
        'code': code,
        'period': period,
        'count': 2000,
        'start': 0
    }
    print(f'测试: market={market}, code={code}, period={period}')
    try:
        resp = requests.get(API_URL, params=params, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        print(f'获取到{len(data)}条K线')
        if data:
            print('最后10条:')
            for row in data[-10:]:
                print(row)
    except Exception as e:
        print(f'接口请求失败: {e}')
    print('-' * 40)