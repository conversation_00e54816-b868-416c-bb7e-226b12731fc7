'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class PoolCategory extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // 定义关联关系
      // 一个分类可能属于一个用户 (当 user_id 不为 NULL 且 type 为 'user')
      PoolCategory.belongsTo(models.User, {
        foreignKey: 'user_id',
        as: 'user', // 别名
        constraints: false // 由于 user_id 可以为 NULL，显式设置约束为 false
      });

      // 将直接关联改为通过 pools_json 字段存储池列表
      // 一个分类包含多个池，但通过JSON字段存储，而不是外键关联
    }
  }

  PoolCategory.init({
    category_id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '分类唯一ID'
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '分类名称，如"A股"、"美股"、"量化策略"'
    },
    type: {
      type: DataTypes.ENUM('system', 'user'),
      allowNull: false,
      comment: '分类类型：system(系统) 或 user(用户)'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '所属用户ID（当type=user时有值）'
      // references 推荐通过 migration 添加
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '显示顺序'
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '可选描述'
    },
    icon: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '可选图标标识'
    },
    // 新增: 存储该分类下包含的池ID列表
    pools_json: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '以JSON格式存储的池列表。格式为数组，例如: [{"pool_id": 1, "pool_name": "沪深300"}]'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    }
  }, {
    sequelize,
    modelName: 'PoolCategory',
    tableName: 'pool_categories',
    timestamps: false, // 不使用 Sequelize 的时间戳管理
    comment: '股票池分类表',
    // indexes: [] // 复杂的索引推荐通过 migration 创建
  });

  return PoolCategory;
}; 