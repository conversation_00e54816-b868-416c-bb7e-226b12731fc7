/**
 * 选股处理器
 * 负责处理选股请求、启动选股引擎、监听Redis进度通知
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { EventEmitter } = require('events');
const redis = require('redis');
const config = require('../config.json');
const { v4: uuidv4 } = require('uuid');
const { spawn } = require('child_process');
const path = require('path'); 

// 创建简单的日志记录器
const logger = {
    info: (message) => console.log(`[SymbolSelectHandler INFO] ${message}`),
    warn: (message) => console.warn(`[SymbolSelectHandler WARN] ${message}`),
    error: (message) => console.error(`[SymbolSelectHandler ERROR] ${message}`),
    debug: (message) => console.log(`[SymbolSelectHandler DEBUG] ${message}`)
};

// 存储选股任务状态
const stockSelectionTasks = new Map(); // taskId -> { status, progress, result }

// 存储选股客户端连接
const symbolSelectClients = new Map(); // socketId -> { socket, subscribed_at, last_activity, subscribed }

// Redis客户端
let redisClient = null;
let redisSubscriber = null;

// 初始化Redis连接
function initRedis() {
    try {
        // 用于发布的客户端
        redisClient = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        redisClient.on('error', (err) => {
            logger.error(`Redis Publisher Error: ${err.message}`);
        });

        // 用于订阅的客户端
        redisSubscriber = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        redisSubscriber.on('error', (err) => {
            logger.error(`Redis Subscriber Error: ${err.message}`);
        });

        return true;
    } catch (error) {
        logger.error(`Redis初始化失败: ${error.message}`);
        return false;
    }
}

class SymbolSelectHandler extends EventEmitter {
    constructor() {
        super();
        this.router = express.Router();
        this.isInitialized = false;
        this.symbolSelectClients = symbolSelectClients; // 共享客户端Map
        // 立即开始初始化
        this.init().catch(error => {
            logger.error(`SymbolSelectHandler构造函数中初始化失败: ${error.message}`);
        });
    }

    /**
     * 初始化处理器
     */
    async init() {
        if (this.isInitialized) {
            return;
        }

        try {
            // 初始化Redis连接
            if (!initRedis()) {
                throw new Error('Redis初始化失败');
            }

            // 连接Redis
            await redisClient.connect();
            logger.info('Redis Publisher connected.');
            await redisSubscriber.connect();
            logger.info('Redis Subscriber connected.');

            // 订阅选股进度通知频道
            await redisSubscriber.pSubscribe('stock_selection_progress:*', (message, channel) => {
                this.handleProgressSignal(message, channel);
            });
            logger.info('Successfully subscribed to stock_selection_progress:*');

            // 订阅选股完成通知频道
            await redisSubscriber.pSubscribe('stock_selection_complete:*', (message, channel) => {
                this.handleCompleteSignal(message, channel);
            });
            logger.info('Successfully subscribed to stock_selection_complete:*');

            this.initRoutes();

            this.isInitialized = true;
            logger.info('SymbolSelectHandler初始化完成');
            // 触发初始化完成事件
            this.emit('initialized');
        } catch (error) {
            logger.error(`SymbolSelectHandler初始化失败: ${error.message}`);
        }
    }

    getRouter() {
        return this.router;
    }

    /**
     * 处理Redis进度信号
     */
    handleProgressSignal(message, channel) {
        try {
            logger.info(`[DEBUG] 收到Redis消息 - 频道: ${channel}, 消息: ${message}`);

            const taskId = channel.split(':')[1];
            const progressData = JSON.parse(message);

            logger.info(`收到选股进度通知: ${taskId} - ${progressData.progress}% - 阶段: ${progressData.stage}`);
            logger.info(`进度详情: 当前${progressData.current}/${progressData.total}, 已选中${progressData.selectedCount}`);

            // 更新任务状态
            this.updateTaskProgress(progressData);

            // 通过Socket.IO推送进度更新到所有订阅的客户端
            this.broadcastProgressUpdate(progressData);

            // 发送进度事件到前端
            this.emit('progress', progressData);

        } catch (error) {
            logger.error(`解析进度消息失败: ${error.message}`);
            logger.error(`原始消息: ${message}`);
            logger.error(`错误堆栈: ${error.stack}`);
        }
    }

    /**
     * 处理Redis完成信号
     */
    handleCompleteSignal(message, channel) {
        try {
            logger.info(`[DEBUG] 收到Redis完成信号 - 频道: ${channel}, 消息: ${message}`);
            const taskId = channel.split(':')[1];
            const completeData = JSON.parse(message);

            logger.info(`收到选股完成通知: ${taskId}`);
            logger.info(`完成详情: ${JSON.stringify(completeData)}`);

            // 根据action字段判断是完成还是中止
            const action = completeData.action || 'completed';
            
            // 更新任务状态
            const task = stockSelectionTasks.get(taskId);
            if (task) {
                if (action === 'stopped') {
                    task.status = 'stopped';
                    logger.info(`任务 ${taskId} 状态更新为中止`);
                } else {
                    task.status = 'completed';
                    logger.info(`任务 ${taskId} 状态更新为完成`);
                }
                task.completedAt = new Date();
                task.result = completeData; // 存储完成结果
            }

            // 通过Socket.IO推送完成通知到所有订阅的客户端
            this.broadcastCompleteUpdate(completeData);

            // 根据action发送不同的事件到前端
            if (action === 'stopped') {
                // 发送中止事件
                this.emit('stopped', completeData);
                logger.info(`已发送选股中止事件: ${taskId}`);
            } else {
                // 发送完成事件
                this.emit('complete', completeData);
                logger.info(`已发送选股完成事件: ${taskId}`);
            }

        } catch (error) {
            logger.error(`解析完成消息失败: ${error.message}`);
            logger.error(`原始消息: ${message}`);
            logger.error(`错误堆栈: ${error.stack}`);
        }
    }

    /**
     * 推送进度更新到所有订阅的客户端
     */
    broadcastProgressUpdate(progressData) {
        let sentCount = 0;
        const disconnectedClients = [];

        // 遍历所有订阅的客户端
        for (const [socketId, clientInfo] of this.symbolSelectClients.entries()) {
            try {
                if (clientInfo.socket && clientInfo.socket.connected) {
                    clientInfo.socket.emit('progress_update', progressData);
                    clientInfo.last_activity = Date.now();
                    sentCount++;
                    logger.debug(`已发送进度更新到客户端: ${socketId}`);
                } else {
                    // 标记为断开连接的客户端
                    disconnectedClients.push(socketId);
                    logger.warn(`客户端已断开连接，跳过推送: ${socketId}`);
                }
            } catch (error) {
                logger.error(`推送进度更新到客户端 ${socketId} 时出错: ${error.message}`);
                disconnectedClients.push(socketId);
            }
        }

        // 清理断开连接的客户端
        disconnectedClients.forEach(socketId => {
            this.symbolSelectClients.delete(socketId);
            logger.info(`清理断开连接的客户端: ${socketId}`);
        });

        logger.info(`进度更新推送完成: 成功发送到 ${sentCount} 个客户端，清理了 ${disconnectedClients.length} 个断开连接的客户端`);
    }

    /**
     * 推送完成通知到所有订阅的客户端
     */
    broadcastCompleteUpdate(completeData) {
        let sentCount = 0;
        const disconnectedClients = [];

        // 遍历所有订阅的客户端
        for (const [socketId, clientInfo] of this.symbolSelectClients.entries()) {
            try {
                if (clientInfo.socket && clientInfo.socket.connected) {
                    clientInfo.socket.emit('complete_update', completeData);
                    clientInfo.last_activity = Date.now();
                    sentCount++;
                    logger.debug(`已发送完成通知到客户端: ${socketId}`);
                } else {
                    // 标记为断开连接的客户端
                    disconnectedClients.push(socketId);
                    logger.warn(`客户端已断开连接，跳过推送: ${socketId}`);
                }
            } catch (error) {
                logger.error(`推送完成通知到客户端 ${socketId} 时出错: ${error.message}`);
                disconnectedClients.push(socketId);
            }
        }

        // 清理断开连接的客户端
        disconnectedClients.forEach(socketId => {
            this.symbolSelectClients.delete(socketId);
            logger.info(`清理断开连接的客户端: ${socketId}`);
        });

        logger.info(`完成通知推送完成: 成功发送到 ${sentCount} 个客户端，清理了 ${disconnectedClients.length} 个断开连接的客户端`);
    }

    /**
     * 更新任务进度
     */
    updateTaskProgress(progressData) {
        const { taskId } = progressData;
        const task = stockSelectionTasks.get(taskId);
        
        if (task) {
            task.progress = progressData.progress;
            task.current = progressData.current;
            task.total = progressData.total;
            task.selectedCount = progressData.selectedCount;
            task.symbolInfo = progressData.symbolInfo;
            task.stage = progressData.stage;
            
            logger.debug(`更新任务进度: ${taskId} - ${progressData.progress}%`);
        }
    }

    /**
     * 启动选股任务
     */
    async startStockSelection(req, res) {
            try {
                // 从前端获取taskId和其他参数
                const { taskId, candidate_type, strategy_module, strategy_parameters } = req.body;
                
                // 验证taskId
                if (!taskId) {
                    return res.status(400).json({ success: false, message: '缺少taskId参数' });
                }
                
                // 检查是否已有相同taskId的任务在运行
                const existingTask = stockSelectionTasks.get(taskId);
                if (existingTask && existingTask.status === 'running') {
                    return res.status(409).json({ 
                        success: false, 
                        message: `任务 ${taskId} 已在运行中` 
                    });
                }
                
                // 创建任务记录
                const task = {
                    taskId,
                    status: 'running',
                    progress: 0,
                    current: 0,
                    total: 0,
                    selectedCount: 0,
                    symbolInfo: '',
                    stage: '开始选股',
                    createdAt: new Date(),
                    result: null
                };
                
                stockSelectionTasks.set(taskId, task);
                
                logger.info(`启动选股任务: ${taskId}`);
                
                // 启动Python选股引擎，传递candidate_type
                await this.startPythonSelectionEngine(taskId, candidate_type, strategy_module, strategy_parameters);
                
                res.json({
                    success: true,
                    taskId: taskId,
                    message: '选股任务已启动'
                });
                
            } catch (error) {
                logger.error(`启动选股任务失败: ${error.message}`);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        }


    /**
     * 启动Python选股引擎
     */
    // 异步启动Python选股引擎
    async startPythonSelectionEngine(taskId, candidateType, strategyModule, strategyParameters) {
            try {
                // 检查是否已有相同taskId的进程在运行
                const existingTask = stockSelectionTasks.get(taskId);
                if (existingTask && existingTask.pythonProcess) {
                    logger.info(`发现已存在的进程，正在停止: ${taskId}`);
                    try {
                        existingTask.pythonProcess.kill('SIGTERM');
                        // 等待进程结束
                        await new Promise((resolve) => {
                            existingTask.pythonProcess.on('close', resolve);
                            setTimeout(resolve, 3000); // 3秒超时
                        });
                        logger.info(`已停止旧进程: ${taskId}`);
                    } catch (error) {
                        logger.error(`停止旧进程失败: ${error.message}`);
                    }
                }

                // Python脚本路径
                const scriptPath = path.join(__dirname, '../_Providers/_Python/stock_selection/engine.py');
                
                // 构建配置参数
                const engineConfig = {
                    candidate_type: candidateType,
                    strategy_module: strategyModule,
                    threshold: strategyParameters.threshold,
                    values: strategyParameters.values,
                    task_id: taskId,
                };
    
                console.log('[symbolSelectHandler] 传送给选股引擎的参数:', engineConfig);
                console.log('[symbolSelectHandler] strategyParameters:', strategyParameters);
                console.log('[symbolSelectHandler] values:', strategyParameters.values);
                console.log('[symbolSelectHandler] threshold:', strategyParameters.threshold);
                console.log('[symbolSelectHandler] candidateType:', candidateType);
                
                // 启动Python进程，通过命令行参数传递配置
                const pythonProcess = spawn('python', [scriptPath, JSON.stringify(engineConfig)], {
                    stdio: ['pipe', 'pipe', 'pipe']
                });
                
                // 监听输出
                pythonProcess.stdout.on('data', (data) => {
                    logger.info(`选股引擎输出: ${data.toString()}`);
                });
                
                pythonProcess.stderr.on('data', (data) => {
                    logger.error(`选股引擎错误: ${data.toString()}`);
                });
                
                pythonProcess.on('close', (code) => {
                    logger.info(`选股引擎进程结束，退出码: ${code}`);
                    
                    // 更新任务状态
                    const task = stockSelectionTasks.get(taskId);
                    if (task) {
                        task.status = code === 0 ? 'completed' : 'failed';
                        task.completedAt = new Date();
                    }
                });
                
                // 保存进程引用
                const task = stockSelectionTasks.get(taskId);
                if (task) {
                    task.pythonProcess = pythonProcess;
                }
                
            } catch (error) {
                logger.error(`启动Python选股引擎失败: ${error.message}`);
                throw error;
            }
        }



    /**
     * 获取选股任务状态
     */
    async getTaskStatus(req, res) {
        try {
            const { taskId } = req.params;
            
            const task = stockSelectionTasks.get(taskId);
            if (!task) {
                return res.status(404).json({
                    success: false,
                    error: '任务不存在'
                });
            }
            
            res.json({
                success: true,
                data: {
                    taskId: task.taskId,
                    status: task.status,
                    progress: task.progress,
                    current: task.current,
                    total: task.total,
                    selectedCount: task.selectedCount,
                    symbolInfo: task.symbolInfo,
                    stage: task.stage,
                    createdAt: task.createdAt,
                    completedAt: task.completedAt,
                    result: task.result
                }
            });
            
        } catch (error) {
            logger.error(`获取任务状态失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * 停止选股任务
     */
    async stopTask(req, res) {
        try {
            // 支持从URL参数或请求体获取taskId
            const taskId = req.params.taskId || req.body.taskId;
            
            if (!taskId) {
                return res.status(400).json({
                    success: false,
                    error: '缺少taskId参数'
                });
            }
            
            const task = stockSelectionTasks.get(taskId);
            if (!task) {
                return res.status(404).json({
                    success: false,
                    error: '任务不存在'
                });
            }
            
            logger.info(`收到停止选股任务请求: ${taskId}`);
            
            // 通过Redis发送停止信号给Python引擎
            if (redisClient) {
                const stopSignal = {
                    taskId: taskId,
                    action: 'stop',
                    timestamp: Date.now()
                };
                
                await redisClient.publish(`stock_selection_control:${taskId}`, JSON.stringify(stopSignal));
                logger.info(`已发送停止信号到Redis: stock_selection_control:${taskId}`);
                
                // 等待一段时间，让Python引擎有机会处理停止信号
                logger.info(`等待Python引擎处理停止信号...`);
                await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
            }
            
            // 停止Python进程
            if (task.pythonProcess) {
                task.pythonProcess.kill('SIGTERM');
                logger.info(`已停止选股任务进程: ${taskId}`);
            }
            
            // 更新任务状态
            task.status = 'stopped';
            task.completedAt = new Date();
            
            res.json({
                success: true,
                message: '选股任务已停止'
            });
            
        } catch (error) {
            logger.error(`停止任务失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * 获取所有任务列表
     */
    async getTaskList(req, res) {
        try {
            const tasks = Array.from(stockSelectionTasks.values()).map(task => ({
                taskId: task.taskId,
                status: task.status,
                progress: task.progress,
                selectedCount: task.selectedCount,
                stage: task.stage,
                createdAt: task.createdAt,
                completedAt: task.completedAt
            }));
            
            res.json({
                success: true,
                data: tasks
            });
            
        } catch (error) {
            logger.error(`获取任务列表失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * 测试选股进度功能
     */
    async testStockSelectionProgress(req, res) {
        try {
            // 从前端获取taskId
            const { taskId } = req.body;
            
            // 验证taskId
            if (!taskId) {
                return res.status(400).json({ success: false, message: '缺少taskId参数' });
            }
            
            // 创建测试任务记录
            const task = {
                taskId,
                status: 'running',
                progress: 0,
                current: 0,
                total: 100,
                selectedCount: 0,
                symbolInfo: '测试模式',
                stage: '开始测试',
                createdAt: new Date(),
                result: null
            };
            
            stockSelectionTasks.set(taskId, task);
            
            logger.info(`启动测试选股任务: ${taskId}`);
            
            // 启动测试Python进程
            await this.startTestPythonEngine(taskId);
            
            res.json({
                success: true,
                taskId: taskId,
                message: '测试选股任务已启动'
            });
            
        } catch (error) {
            logger.error(`启动测试选股任务失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * 启动测试Python引擎
     */
    async startTestPythonEngine(taskId) {
        try {
            // Python脚本路径
            const scriptPath = path.join(__dirname, '../_Providers/_Python/stock_selection/engine.py');
            
            // 构建测试配置参数
            const testConfig = {
                test_mode: true,  // 标记为测试模式
                task_id: taskId,
                candidate_type: 0,  // A股
                strategy_module: 'shape_matching',
                threshold: 0.8,
                values: [1, 2, 3, 4, 5]  // 简化的测试数据
            };
    
            console.log('[symbolSelectHandler] 传送给测试引擎的参数:', testConfig);
            
            // 启动Python进程，通过命令行参数传递配置
            const pythonProcess = spawn('python', [scriptPath, JSON.stringify(testConfig)], {
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            // 监听输出
            pythonProcess.stdout.on('data', (data) => {
                logger.info(`测试引擎输出: ${data.toString()}`);
            });
            
            pythonProcess.stderr.on('data', (data) => {
                logger.error(`测试引擎错误: ${data.toString()}`);
            });
            
            pythonProcess.on('close', (code) => {
                logger.info(`测试引擎进程结束，退出码: ${code}`);
                
                // 更新任务状态
                const task = stockSelectionTasks.get(taskId);
                if (task) {
                    task.status = code === 0 ? 'completed' : 'failed';
                    task.completedAt = new Date();
                }
            });
            
            // 保存进程引用
            const task = stockSelectionTasks.get(taskId);
            if (task) {
                task.pythonProcess = pythonProcess;
            }
            
        } catch (error) {
            logger.error(`启动测试Python引擎失败: ${error.message}`);throw error;
        }
    }

    /**
     * 初始化Socket.IO服务
     * @param {SocketIO.Namespace} namespace Socket.IO命名空间
     */
    initSocketIO(namespace) {
        if (!namespace) {
            logger.warn('尝试设置空的Socket.IO命名空间引用');
            return;
        }

        // 存储命名空间引用
        this.namespace = namespace;
        logger.info('选股处理器已设置Socket.IO命名空间引用');

        // 处理连接事件
        namespace.on('connection', (socket) => {
            logger.info(`选股进度命名空间收到新连接: ${socket.id}`);
            logger.debug(`[DEBUG] 客户端连接详情 - Socket ID: ${socket.id}, 命名空间: ${socket.nsp.name}`);

            // 存储客户端信息
            this.symbolSelectClients.set(socket.id, {
                socket: socket,
                subscribed_at: Date.now(),
                last_activity: Date.now(),
                subscribed: true
            });
            logger.info(`客户端 ${socket.id} 已订阅`);

            // 处理订阅任务进度
            socket.on('subscribe_task', (data) => {
                logger.info(`[DEBUG] 收到subscribe_task事件 - Socket ID: ${socket.id}`);
                logger.info(`客户端 ${socket.id} 订阅任务进度`);
                
                // 更新客户端订阅状态
                const clientInfo = this.symbolSelectClients.get(socket.id);
                if (clientInfo) {
                    clientInfo.subscribed = true;
                    clientInfo.last_activity = Date.now();
                }
                
                // 发送订阅确认
                socket.emit('subscribe_confirmed', { 
                    success: true, 
                    message: '订阅成功',
                    socketId: socket.id 
                });
            });

            // 处理取消订阅任务进度
            socket.on('unsubscribe_task', (data) => {
                logger.info(`[DEBUG] 收到unsubscribe_task事件 - Socket ID: ${socket.id}`);
                logger.info(`客户端 ${socket.id} 取消订阅任务进度`);
                
                // 标记客户端为未订阅
                const clientInfo = this.symbolSelectClients.get(socket.id);
                if (clientInfo) {
                    clientInfo.subscribed = false;
                    clientInfo.last_activity = Date.now();
                    logger.info(`客户端 ${socket.id} 标记为未订阅`);
                }
                
                // 发送取消订阅确认
                socket.emit('unsubscribe_confirmed', { 
                    success: true, 
                    message: '取消订阅成功',
                    socketId: socket.id 
                });
            });

            // 处理断开连接
            socket.on('disconnect', (reason) => {
                logger.info(`选股进度客户端断开连接: ${socket.id}, 原因: ${reason}`);
                logger.debug(`[DEBUG] 客户端断开连接详情 - Socket ID: ${socket.id}, 原因: ${reason}`);

                // 清理断开连接的客户端
                this.symbolSelectClients.delete(socket.id);
                logger.info(`清理断开连接的客户端: ${socket.id}`);
            });
        });
    }

    /**
     * 设置路由
     */
    initRoutes() {

        this.router.post('/start', authenticateToken, this.startStockSelection.bind(this));
        this.router.post('/test', authenticateToken, this.testStockSelectionProgress.bind(this));
        this.router.get('/status/:taskId', authenticateToken, this.getTaskStatus.bind(this));
        this.router.post('/stop/:taskId', authenticateToken, this.stopTask.bind(this));
        this.router.post('/stop', authenticateToken, this.stopTask.bind(this)); // 添加支持请求体的路由
        this.router.get('/tasks', authenticateToken, this.getTaskList.bind(this));
    }
}

// 创建处理器实例
const symbolSelectHandler = new SymbolSelectHandler();

module.exports = {
    router,
    symbolSelectHandler,
    symbolSelectClients: symbolSelectClients, // 兼容旧代码直接访问
    initSocketIO: (namespace) => symbolSelectHandler.initSocketIO(namespace)
};