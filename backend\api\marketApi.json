{"openapi": "3.0.0", "info": {"title": "Market API", "version": "1.0.0", "description": "市场数据相关API"}, "paths": {"/api/market/symbols": {"get": {"summary": "获取交易品种列表", "description": "根据市场类型获取交易品种列表，支持搜索", "security": [{"bearerAuth": []}], "parameters": [{"name": "market", "in": "query", "description": "市场类型 (STOCK/FUTURE/CRYPTO)", "required": true, "schema": {"type": "string", "enum": ["STOCK", "FUTURE", "CRYPTO"]}}, {"name": "search", "in": "query", "description": "搜索关键词", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功返回交易品种列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "description": "品种代码"}, "name": {"type": "string", "description": "品种名称"}, "market": {"type": "string", "description": "市场类型"}, "exchange": {"type": "string", "description": "交易所"}}}}}}}}}}}}, "/api/market/symbols/refresh": {"post": {"summary": "手动刷新股票列表缓存", "description": "手动触发更新股票列表的内存缓存", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功刷新缓存", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "description": "刷新结果描述"}}}}}}, "500": {"description": "刷新失败", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "error": {"type": "string", "description": "错误信息"}}}}}}}}}, "/api/market/klines": {"get": {"summary": "获取K线数据", "description": "获取指定品种的K线历史数据", "security": [{"bearerAuth": []}], "parameters": [{"name": "symbol", "in": "query", "description": "交易品种代码", "required": true, "schema": {"type": "string"}}, {"name": "market", "in": "query", "description": "市场类型", "required": true, "schema": {"type": "string", "enum": ["STOCK", "FUTURE", "CRYPTO"]}}, {"name": "interval", "in": "query", "description": "K线周期", "required": false, "schema": {"type": "string", "enum": ["1m", "5m", "15m", "30m", "1h", "1d", "1w", "1M"]}}, {"name": "startTime", "in": "query", "description": "开始时间戳", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "endTime", "in": "query", "description": "结束时间戳", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "limit", "in": "query", "description": "返回数据条数限制", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 1000}}], "responses": {"200": {"description": "成功返回K线数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object", "properties": {"time": {"type": "integer", "format": "int64", "description": "时间戳"}, "open": {"type": "number", "description": "开盘价"}, "high": {"type": "number", "description": "最高价"}, "low": {"type": "number", "description": "最低价"}, "close": {"type": "number", "description": "收盘价"}, "volume": {"type": "number", "description": "成交量"}}}}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}