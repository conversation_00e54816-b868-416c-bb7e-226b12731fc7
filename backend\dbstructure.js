/**
 * 数据库表结构定义
 */

module.exports = {
  MarketType: {
    tableName: 'market_types',
    columns: {
      id: {
        type: 'INTEGER',
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: 'TEXT',
        allowNull: false,
        unique: true
      },
      createdAt: {
        type: 'DATETIME',
        defaultValue: 'CURRENT_TIMESTAMP'
      },
      updatedAt: {
        type: 'DATETIME',
        defaultValue: 'CURRENT_TIMESTAMP'
      }
    }
  },

  SymbolInfo: {
    tableName: 'symbols',
    columns: {
      id: {
        type: 'INTEGER',
        primaryKey: true,
        autoIncrement: true
      },
      marketTypeId: {
        type: 'INTEGER',
        references: {
          model: 'market_types',
          key: 'id'
        }
      },
      symbol: {
        type: 'TEXT',
        allowNull: false
      },
      name: {
        type: 'TEXT',
        allowNull: false
      },
      createdAt: {
        type: 'DATETIME',
        defaultValue: 'CURRENT_TIMESTAMP'
      },
      updatedAt: {
        type: 'DATETIME',
        defaultValue: 'CURRENT_TIMESTAMP'
      }
    },
    indexes: [
      {
        unique: true,
        fields: ['marketTypeId', 'symbol']
      }
    ]
  }
}