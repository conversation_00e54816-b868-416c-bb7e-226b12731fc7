import { EventBus } from '../../events/eventBus';
import { ChartEvents } from '../../events/events';
import { getToken } from '@/utils/auth';
import { DrawingLineRecord } from '@/shared_types/chart';
import { DrawingLine } from '@/shared_types/chart';
import { ChartConfig, Symbol } from '@/shared_types/market';
import httpDispatcher from '../HttpDispatcher';
import { INDICATORS, IndicatorWrapper } from '../../_Modules/Indicators/IndicatorWrapper';
import indicatorListManager from '../../_Modules/Indicators/IndicatorListManager';
import { indicatorInstancesAtom, jotaiStore, subIndicatorsAtom, mainIndicatorsAtom, initialDrawingLinesAtom } from '@/store/state';

import jsonParse from './json_parse';

const chartDispatcher = {
  initialized: false,
  drawingLinesCache: new Map<string, DrawingLineRecord>(), // 修改缓存类型为 DrawingLineRecord

  initialize() {
    if (this.initialized) {
      console.log('[ChartDispatcher] 已经初始化，跳过');
      return;
    }

    // 订阅获取绘线信息请求
    this.subscribeToGetDrawingLines();

    // 订阅保存画线的事件
    this.subscribeToSaveDrawingLines();

    // 订阅删除所有画线的事件
    this.subscribeToDeleteAllDrawingLines();

    // 读取配置
    this.loadSavedConfig();

    this.initialized = true;
    console.log('[ChartDispatcher] 初始化完成');
  },

  // 获取 symbol 的缓存键
  getSymbolKey(symbol: Symbol): string {
    return `${symbol.market}/${symbol.exchange}/${symbol.code}`;
  },

  loadSavedConfig() {
    console.log("[配置] 配置加载已禁用，使用默认设置");
    // 不再从localStorage加载配置
    // 不再初始化指标列表管理器
  },

  // 监听获取绘线信息请求事件
  subscribeToGetDrawingLines() {
    EventBus.on(ChartEvents.Types.GET_DRAWING_LINES, async (payload: ChartEvents.GetDrawingLinesPayload) => {
      try {
        const { symbol } = payload;
        const symbolKey = this.getSymbolKey(symbol);

        // 获取绘线信息
        const drawingLines = await this.fetchDrawingLines(symbol);

        console.log('[ChartDispatcher] 从后端获取到的绘线数据', JSON.stringify(drawingLines));

        // 更新缓存
        //this.drawingLinesCache.set(symbolKey, drawingLines);

        // 发送绘线数据准备好的事件
        EventBus.emit(ChartEvents.Types.APPLY_DRAWING_LINES, {
          drawingLines
        });

        // 更新初始化画线的全局状态
        //jotaiStore.set(initialDrawingLinesAtom, drawingLines);

        console.log('[Drawing] 绘线数据已准备就绪');
      } catch (error) {
        console.error('[Drawing] 获取绘线数据错误1:', error);
        // 出错时发送空数据
        EventBus.emit(ChartEvents.Types.APPLY_DRAWING_LINES, {
          drawingLines: []
        });
      }
    });
  },

  // 监听保存画线事件
  subscribeToSaveDrawingLines() {
    EventBus.on(ChartEvents.Types.SAVE_DRAWING_LINES, async (payload: ChartEvents.SaveDrawingLinesPayload) => {
      console.log('[Drawing] 保存画线事件', payload);
      await this.saveDrawingLines(payload);
    });
  },

  // 监听删除所有画线的事件
  subscribeToDeleteAllDrawingLines() {
    EventBus.on(ChartEvents.Types.DELETE_ALL_DRAWING_LINES, async (payload: ChartEvents.DeleteAllDrawingLinesPayload) => {
      console.log('[Drawing] 删除所有画线事件', payload.symbol);
      if (await this.deleteAllDrawingLines(payload.symbol, payload.interval)) {
        payload.callback(true);
      } else {
        payload.callback(false);
      }
    });
  },

  // 获取绘线信息，返回该品种所有周期的画线数组
  async fetchDrawingLines(symbol: Symbol): Promise<DrawingLineRecord[] > {
    try {
      const token = getToken();

      const response = await httpDispatcher.get('/chart/drawinglines', {
        params: {
          market: symbol.market,
          exchange: symbol.exchange,
          code: symbol.code,
        },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('[ChartDispatcher] 从后端3 response:', JSON.stringify(response));

      if (response.data.success) {
        //TODO: 测量的第三个元素不正常，但是后端是正常的！
        return response.data.data;
      }
      return [];
    } catch (error) {
      console.error('[Drawing] 获取绘线数据错误2:', error);
      return [];
    }
  },

  // 保存画线数据
  async saveDrawingLines(payload: ChartEvents.SaveDrawingLinesPayload): Promise<boolean> {
    try {
      const { symbol, interval, overlays } = payload;
      const token = getToken();
      const symbolKey = this.getSymbolKey(symbol);

      console.log('[ChartDispatcher] 保存画线数据 saveDrawingLines ', symbol, interval, overlays);

      const response = await httpDispatcher.post('/chart/drawinglines', {
        symbol: JSON.stringify(symbol),
        interval,
        overlays
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        // 更新缓存
        /*this.drawingLinesCache.set(symbolKey, {
          userId: response.data.userId,
          symbol: JSON.stringify(symbol),
          interval,
          overlays
        });*/
        return true;
      }
      return false;
    } catch (error) {
      console.error('[Drawing] 保存画线数据失败:', error);
      return false;
    }
  },

  // 删除指定品种的所有画线
  async deleteAllDrawingLines(symbol: Symbol, interval: string): Promise<boolean> {
    try {
      const token = getToken();
      const symbolKey = this.getSymbolKey(symbol);

      const response = await httpDispatcher.delete('/chart/drawinglines', {
        params: {
          market: symbol.market,
          exchange: symbol.exchange,
          code: symbol.code,
          interval
        },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        // 清空缓存
        this.drawingLinesCache.delete(symbolKey);
        return true;
      }
    } catch (error) {
      console.error('[Drawing] 删除所有画线失败:', error);
    }
    return false;
  }
};

export default chartDispatcher;