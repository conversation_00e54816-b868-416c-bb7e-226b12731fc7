const CACHE_NAME = 'chat-image-cache-v1';

self.addEventListener('install', (event) => {
  console.log('[ServiceWorker] Installing...');
  event.waitUntil(self.skipWaiting());
});

self.addEventListener('activate', (event) => {
  console.log('[ServiceWorker] Activating...');
  event.waitUntil(self.clients.claim());
});

self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // 检查是否是 chatfiles 请求
  if (url.pathname.startsWith('/chatfiles/')) {
    event.respondWith(
      fetch(event.request.url, {
        headers: {
          // 根据文件扩展名设置不同的 Accept 头
          'Accept': getAcceptHeaderByExtension(url.pathname)
        }
      })
    );
  }
});

// 根据文件扩展名返回合适的 Accept 头
function getAcceptHeaderByExtension(pathname) {
  const ext = pathname.split('.').pop().toLowerCase();
  const acceptMap = {
    'jpg': 'image/*',
    'jpeg': 'image/*',
    'png': 'image/*',
    'gif': 'image/*',
    'log': 'text/plain,*/*',
    'txt': 'text/plain,*/*',
    'pdf': 'application/pdf,*/*',
    'doc': 'application/msword,*/*',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document,*/*'
  };
  
  return acceptMap[ext] || '*/*';
} 