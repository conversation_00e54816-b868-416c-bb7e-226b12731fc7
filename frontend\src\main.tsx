import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import { ProConfigProvider } from '@ant-design/pro-components';
import zhCN from 'antd/locale/zh_CN';
import App from './App';
import './index.less';
// 导入初始化文件，确保在应用启动时执行必要的初始化操作
import './init';

/**
 * 应用入口文件
 * 使用 ReactDOM.createRoot API 渲染应用
 * 使用 ConfigProvider 和 ProConfigProvider 提供全局配置
 */
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <ProConfigProvider>
    <ConfigProvider locale={zhCN}>
      <App />
    </ConfigProvider>
  </ProConfigProvider>
); 