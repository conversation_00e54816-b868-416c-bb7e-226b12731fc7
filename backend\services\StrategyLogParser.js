const fs = require('fs').promises;
const path = require('path');

// 创建myLogger模块
const myLogger = {
    log: function(message, ...args) {
        const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });     //输出北京时间
        const logMessage = `[${timestamp}] [INFO] ${message}`;
        console.log(logMessage, ...args);
        this.writeToFile(logMessage, args);
    },
    
    warn: function(message, ...args) {
        const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
        const logMessage = `[${timestamp}] [WARN] ${message}`;
        console.warn(logMessage, ...args);
        this.writeToFile(logMessage, args);
    },
    
    error: function(message, ...args) {
        const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
        const logMessage = `[${timestamp}] [ERROR] ${message}`;
        console.error(logMessage, ...args);
        this.writeToFile(logMessage, args);
    },
    
    info: function(message, ...args) {
        const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
        const logMessage = `[${timestamp}] [INFO] ${message}`;
        console.info(logMessage, ...args);
        this.writeToFile(logMessage, args);
    },
    
    writeToFile: function(message, args) {
        try {
            const logFilePath = path.join(__dirname, 'logparser.log');
            const fullMessage = args.length > 0 ? `${message} ${args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ')}` : message;
            const logEntry = fullMessage + '\n';
            
            // 异步写入文件，不阻塞主流程
            fs.appendFile(logFilePath, logEntry, 'utf8').catch(err => {
                console.error('写入日志文件失败:', err);
            });
        } catch (error) {
            console.error('日志写入失败:', error);
        }
    }
};

/**
 * 策略日志解析器
 * 负责将策略运行产生的技术日志"翻译"成前端可读的通用数据结构
 */
class StrategyLogParser {
    
    /**
     * 解析策略运行时数据
     * @param {string} strategyId - 策略ID
     * @param {string} userId - 用户ID
     * @param {string} strategyType - 策略类型 (portfolio/generic/multi_factors)
     * @param {number} initialCapital - 初始资金
     * @returns {Object} 解析结果
     */
    static async parseSingleStrategyRuntimeData(strategyId, userId, strategyType = 'generic', initialCapital = 100000) {
        try {
            
            // ========== 日志文件轮询主循环（由用户指令） ==========
            const results = [];
            // 获取所有日志文件（新到旧）
            const logFiles = await this.getLogFilesFromNewToOld(strategyId, userId);

            myLogger.log(`[StrategyLogParser] 开始解析策略运行时数据: strategyId=${strategyId}, userId=${userId}, type=${strategyType}, initialCapital=${initialCapital}, logFiles=${logFiles}`);

            for (const logFile of logFiles) {
                // 解析日志内容（假定parseLogContent已实现）
                const parseResult = await this.parseLogContent(strategyId, strategyType, initialCapital, logFile);
                
                // 将parseResult中的信号添加到results数组中
                if (parseResult && Array.isArray(parseResult)) {
                    results.push(...parseResult);
                }

                const opens = this.openCounts(results);
                myLogger.log(`[StrategyLogParser] 截止目前为止的结果个数： ${results.length} 其中开仓：${opens} 内容：${JSON.stringify(results)}`);
                // 判断累计开仓数
                if (opens >= 5) {
                    myLogger.log('[StrategyLogParser] 已达到10条开仓记录，停止解析');
                    break;
                }
            }
            // ========== 日志文件轮询主循环结束 ==========
            

            myLogger.log(`[StrategyLogParser] 解析完成: ${results.length} 条记录，开始进行时间转换`);

            // 时间已经是北京时间，不需要再转换
            // results.forEach(result => {
            //     if (result.timestamp) {
            //         result.timestamp = this.convertTimeToBeijing(result.timestamp);
            //     }
            // });

            // 所有的原始结果数据就在results中，可以按需处理
            // 开始将results根据需求转化为标准的策略运行结果数据结构，用于返回前端
            const runtimeData = this.convertToStandardRuntimeData(results);

            // 返回统一的数据结构
            return {
                success: true,
                data: runtimeData // 返回所有解析结果的集合
            };
            
        } catch (error) {
            myLogger.error('[StrategyLogParser] 解析策略运行时数据失败:', error);
            return {
                success: false,
                error: error.message,
                data: {
                    strategy_id: strategyId,
                    strategy_type: strategyType,
                    signals: [],
                    latest_signal: null,
                    total_signals: 0
                }
            };
        }
    }

    

    /**
         * 解析单个日志文件内容
         * @param {string} strategyId 策略ID
         * @param {string} strategyType 策略类型
         * @param {number} initialCapital 初始资金
         * @param {string} logFilePath 日志文件路径
         * @returns {Promise<Object>} 解析结果
         */
    static async parseLogContent(strategyId, strategyType, initialCapital, logFilePath) {
        try {
            myLogger.log(`[StrategyLogParser] 解析日志文件: ${logFilePath}, 策略类型: ${strategyType}`);
            
            // 读取日志文件内容
            const logContent = await this.readLogFile(logFilePath);
            if (!logContent) {
                myLogger.log(`[StrategyLogParser] 日志文件为空: ${logFilePath}`);
                return []
            }
            
            // 根据策略类型选择解析方法
            let parseResult;
            switch (strategyType) {
                case 'portfolio':
                    // 调用portfolio的单文件内容解析
                    parseResult = this.parseSinglePortfolioLogFile(strategyId, logContent, initialCapital);
                    break;
                case 'multi_factors':
                    // 调用多因子策略解析
                    parseResult = this.parseMultiFactorsLog(logContent, strategyId, initialCapital);
                    break;
                default:
                    // 调用通用策略解析
                    parseResult = await this.parseGenericLog(strategyId, logContent, userId);
                    break;
            }
            
            myLogger.log(`[StrategyLogParser] 解析完成: ${logFilePath}, 信号数: ${parseResult.total_signals || 0}`);
            return parseResult;
            
        } catch (error) {
            myLogger.error(`[StrategyLogParser] 解析日志文件失败: ${logFilePath}, 错误: ${error.message}`);
            /*return {
                strategy_id: strategyId,
                strategy_type: strategyType,
                signals: [],
                latest_signal: null,
                total_signals: 0,
                initial_capital: initialCapital,
                base_capital: 100000,
                ratio: initialCapital / 100000,
                error: `解析失败: ${error.message}`
            };*/
            return []
        }
    }
        
    /**
     * 获取策略日志文件路径
     * @param {string} strategyId 策略ID
     * @param {string} userId 用户ID
     * @returns {Promise<string|null>} 日志文件路径
     */
    static async getStrategyLogPath(strategyId, userId) {
        try {
            // 策略日志文件路径：backend/_Providers/_Python/strategy/live_trading/user_${userId}/logs/
            const basePath = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading');
            const userDir = path.join(basePath, `user_${userId}`);
            const logsDir = path.join(userDir, 'logs');
            
            // 获取logs目录下的所有日志文件
            try {
                const files = await fs.readdir(logsDir);
                const logFiles = files.filter(file => file.endsWith('.log')).sort().reverse(); // 按文件名倒序，最新的在前
                
                if (logFiles.length === 0) {
                    myLogger.warn(`[StrategyLogParser] 用户${userId}的logs目录下没有找到日志文件`);
                    return null;
                }
                
                // 使用最新的日志文件（按日期命名，如2025-07-18.log）
                const latestLogFile = logFiles[0];
                const logPath = path.join(logsDir, latestLogFile);
                
                myLogger.info(`[StrategyLogParser] 使用日志文件: ${logPath}`);
                return logPath;
                
            } catch (err) {
                myLogger.warn(`[StrategyLogParser] 读取logs目录失败: ${err.message}`);
                return null;
            }
        } catch (error) {
            myLogger.error(`[StrategyLogParser] 获取策略日志路径失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 读取日志文件
     * @param {string} logPath 日志文件路径
     * @returns {Promise<string|null>} 日志内容
     */
    static async readLogFile(logPath) {
        try {
            const content = await fs.readFile(logPath, 'utf8');
            return content;
        } catch (error) {
            myLogger.error(`[StrategyLogParser] 读取日志文件失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 根据策略类型解析日志
     * @param {string} strategyId 策略ID
     * @param {string} strategyType 策略类型
     * @param {string} logContent 日志内容
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 解析后的运行数据
     */
    static async parseLogByStrategyType(strategyId, strategyType, logContent, userId) {
        switch (strategyType) {
            case 'portfolio':
                // 使用新的文件收集和解析逻辑，需要从数据库获取初始资金
                const portfolioInitialCapital = 100000; // 默认值，实际应该从数据库获取
                return await this.parsePortfolioLogWithFileCollection(strategyId, userId, portfolioInitialCapital);
            case 'multi_factors':
                // 获取初始资金
                const initialCapitalMatch = logContent.match(/初始资金[：:]\s*(\d+\.?\d*)/);
                const initialCapital = initialCapitalMatch ? parseFloat(initialCapitalMatch[1]) : 100000; // 默认10万
                return this.parseMultiFactorsLog(logContent, strategyId, initialCapital);
            default:
                return await this.parseGenericLog(strategyId, logContent, userId);
        }
    }

    /**
     * 解析portfolio策略日志
     * @param {string} strategyId 策略ID
     * @param {string} logContent 日志内容
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 解析后的运行数据
     */
    static async parsePortfolioLog(strategyId, logContent, userId) {
        try {
            myLogger.log('[StrategyLogParser] 开始解析portfolio策略日志');
            
            // 解析日志内容，提取关键信息
            const lines = logContent.split('\n');
            
            // 从日志中提取策略信息
            const strategyInfo = this.extractPortfolioInfo(lines);
            
            // 构建与前端期望一致的数据结构
            const runtimeData = {
                strategy_id: strategyId,
                strategy_type: 'portfolio',
                signals: strategyInfo.signals || [],
                latest_signal: strategyInfo.latest_signal || null,
                total_signals: strategyInfo.total_signals || 0,
                initial_capital: strategyInfo.initial_capital || 100000,
                base_capital: 100000,
                ratio: strategyInfo.ratio || 1.0,
                error: null
            };

            myLogger.log('[StrategyLogParser] portfolio策略解析完成:', runtimeData);
            return runtimeData;

        } catch (error) {
            myLogger.error('[StrategyLogParser] 解析portfolio策略日志失败:', error);
            return {
                strategy_id: strategyId,
                strategy_type: 'portfolio',
                signals: [],
                latest_signal: null,
                total_signals: 0,
                initial_capital: 100000,
                base_capital: 100000,
                ratio: 1.0,
                error: error.message
            };
        }
    }

    /**
     * 解析单个portfolio日志文件
     * @param {string} strategyId 策略ID
     * @param {string} logContent 日志文件内容
     * @param {number} initialCapital 初始资金
     * @returns {Object} 解析结果
     */
    static parseSinglePortfolioLogFile(strategyId, logContent, initialCapital) {
        
        try {
            
            const lines = logContent.split('\n');
            
            // 从后到前定位包含"执行完毕，就绪状态已重置，等待下一周期"的语句
            const completionPositions = [];
            for (let i = lines.length - 1; i >= 0; i--) {
                if (lines[i].includes('执行完毕，就绪状态已重置，等待下一周期')) {

                    // 验证策略ID是否匹配
                    // 使用正则表达式匹配策略 ID
                    const match = lines[i].match(/策略 (\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\b)/);

                    if (match && match[1]) {
                        const theId = match[1];
                        if (theId == strategyId) completionPositions.push(i);
                    } else {
                        myLogger.log(`[StrategyLogParser] 从文本 ${lines[i]} 未找到策略 ID：${strategyId}`);
                    }
                }
            }
            
            myLogger.log(`[StrategyLogParser] 找到 ${completionPositions.length} 个执行完毕位置`);
            
            const signals = [];
            
            // 处理每个执行完毕位置
            for (const completionLineIndex of completionPositions) {
                const parseResult = this.parsePortfolioRound(lines, completionLineIndex, initialCapital);
                if (parseResult) {
                    signals.push(parseResult);
                }
            }
            
            // 构建返回结果
            /*const result = {
                strategy_id: null, // 从日志中提取
                strategy_type: 'portfolio',
                signals: signals,
                latest_signal: signals.length > 0 ? signals[0] : null,
                total_signals: signals.length,
                initial_capital: initialCapital,
                base_capital: 100000,
                ratio: initialCapital / 100000,
                error: null
            };*/
            
            myLogger.log(`[StrategyLogParser] 信号数: ${signals.length}`);
            return signals;
            
        } catch (error) {
            myLogger.error(`[StrategyLogParser] 错误: ${error.message}`);
            /*return {
                strategy_id: null,
                strategy_type: 'portfolio',
                signals: [],
                latest_signal: null,
                total_signals: 0,
                initial_capital: initialCapital,
                base_capital: 100000,
                ratio: initialCapital / 100000,
                error: `解析失败: ${error.message}`
            };*/
            return []
        }
    }

    /**
     * 解析一轮portfolio交易信息
     * @param {string[]} lines 日志行数组
     * @param {number} completionLineIndex 执行完毕行索引
     * @param {number} initialCapital 初始资金
     * @returns {Object|null} 解析结果
     */
    static parsePortfolioRound(lines, completionLineIndex, initialCapital) {
        myLogger.log(`[StrategyLogParser] 解析第 ${completionLineIndex} 行的交易轮次`);
        
        // 初始化需要获取的信息
        let targetSymbols = [];
        let logicalPositions = {};
        let closePositions = [];
        let openPositions = [];
        let commission = 0;
        let newPositionCost = 0;

        let ok1 = false, ok2 = false, ok3 = false, ok4 = false, ok5 = false, ok6 = false;

        let processOpenPositions = false;

        let roundGot = false;
        
        // 从执行完毕行往前逐行解析
        for (let i = completionLineIndex - 1; i >= 0; i--) {
            const line = lines[i];
            
            // 1. 解析选中品种
            if (line.includes('本轮目标品种:')) {
                const match = line.match(/本轮目标品种: (\[.*?\])/);
                if (match) {
                    try {
                        const jsonStr = this.convertPythonToJson(match[1]);
                        targetSymbols = JSON.parse(jsonStr);
                        ok1 = true;
                        myLogger.log(`[StrategyLogParser] 找到目标品种: ${targetSymbols}`);
                    } catch (e) {
                        myLogger.log(`[StrategyLogParser] 解析目标品种失败: ${e.message}`);
                    }
                }
                else
                    myLogger.log(`[StrategyLogParser] 未找到目标品种`);
            }
            
            // 2. 解析当前逻辑持仓
            if (line.includes('用于决策的引擎实际持仓 (同步时获取):')) {
                const match = line.match(/用于决策的引擎实际持仓 \(同步时获取\): (.*)/);
                if (match) {
                    try {
                        const jsonStr = this.convertPythonToJson(match[1]);
                        logicalPositions = JSON.parse(jsonStr);
                        ok2 = true;
                        myLogger.log(`[StrategyLogParser] 找到逻辑持仓: ${JSON.stringify(logicalPositions)}`);
                    } catch (e) {
                        myLogger.log(`[StrategyLogParser] 解析逻辑持仓失败: ${e.message}`);
                    }
                }
                else
                    myLogger.log(`[StrategyLogParser] 未找到逻辑持仓`);
            }
            
            // 3. 解析需要平仓品种
            if (line.includes('识别需平仓品种 (基于是否仍在目标列表):')) {
                const match = line.match(/识别需平仓品种 \(基于是否仍在目标列表\): (.*)/);
                if (match) {
                    try {
                        const jsonStr = this.convertPythonToJson(match[1]);
                        const closeSet = JSON.parse(jsonStr);
                        closePositions = Array.isArray(closeSet) ? closeSet : [];
                        ok3 = true;
                        myLogger.log(`[StrategyLogParser] 找到需平仓品种: ${closePositions}`);
                    } catch (e) {
                        myLogger.log(`[StrategyLogParser] 解析需平仓品种失败: ${e.message}`);
                    }
                } else {
                    myLogger.log(`[StrategyLogParser] 未找到需平仓品种`);
                }
            }

            // 4. 解析本轮所有新开仓记录
            if (!ok4) {
                if (line.includes('最终尝试设置新开仓目标的品种: set()')) {
                    openPositions = [];
                    ok4 = true;
                } else if (line.includes(': 准备开仓')) {
                    // 匹配所有准备开仓的记录
                    const openPosMatch = line.match(/\[逻辑余额\]\s+(\S+):\s+准备开仓\s+([\d.]+)\s+@\s+([\d.]+), Cost=([\d.]+), Comm=([\d.]+)/);
                    if (openPosMatch) {
                        const symbol = openPosMatch[1];
                        const quantity = parseFloat(openPosMatch[2]);
                        const price = parseFloat(openPosMatch[3]);
                        const fee = parseFloat(openPosMatch[5]);
                        openPositions.push({ symbol, quantity, price, fee });
                        ok4 = true;
                    }
                }
            }
            
            // 5. 解析开仓名义成本和手续费
            if (line.includes('[逻辑余额] 新开仓名义成本(近似):') && line.includes('手续费(近似):')) {
                const costMatch = line.match(/新开仓名义成本\(近似\): ([\d.]+)/);
                const commissionMatch = line.match(/手续费\(近似\): ([\d.]+)/);
                
                if (costMatch) {
                    newPositionCost = parseFloat(costMatch[1]);
                    ok5 = true;
                    myLogger.log(`[StrategyLogParser] 找到开仓名义成本: ${newPositionCost}`);
                }
                
                if (commissionMatch) {
                    commission = parseFloat(commissionMatch[1]);
                    ok6 = true;
                    myLogger.log(`[StrategyLogParser] 找到手续费: ${commission}`);
                }
            }
            
            // 检查是否所有信息都已获取
            if (ok1 && ok2 && ok3 && ok4 && ok5 && ok6) {
                myLogger.log(`[StrategyLogParser] 所有信息已获取，退出解析`);

                roundGot = true;
                break;
            }
        }

        if (!roundGot) {
            myLogger.log(`[StrategyLogParser] 未找到交易轮次`);
            return null;
        }

        // 从日志中提取时间戳
        let timestamp = new Date().toISOString(); // 默认使用当前时间
        for (let i = completionLineIndex; i >= Math.max(0, completionLineIndex - 10); i--) {
            const line = lines[i];
            const timestampMatch = line.match(/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]/);
            if (timestampMatch) {
                // 日志前缀的时间已经是北京时间，直接使用，不需要转换为UTC
                const logTime = timestampMatch[1];
                // 保持原始的北京时间格式，去掉毫秒部分
                const beijingTime = logTime.split('.')[0]; // 去掉毫秒部分
                timestamp = beijingTime;
                myLogger.log(`[StrategyLogParser] 从日志提取北京时间戳: ${logTime} -> ${timestamp}`);
                break;
            }
        }
        
        // 构建信号数据
        const signal = {
            strategy_id: null, // 从日志中提取
            timestamp: timestamp,
            target_symbols: targetSymbols,
            logical_positions: logicalPositions,
            close_positions: closePositions,
            open_positions: openPositions,
            commission: commission,
            new_position_cost: newPositionCost,
            ratio: initialCapital / 100000,
            actual_initial_capital: initialCapital,
            base_capital: 100000
        };
        
        myLogger.log(`[StrategyLogParser] 构建信号: ${JSON.stringify(signal)}`);
        return signal;
    }

    /**
     * 收集包含执行完毕标记的portfolio日志文件
     * @param {string} logsDir 日志目录路径
     * @returns {string[]} 包含执行完毕标记的日志文件路径列表
     */
    static collectPortfolioLogFilesWithExecution(logsDir) {
        myLogger.log(`[StrategyLogParser] 收集包含执行完毕标记的portfolio日志文件: ${logsDir}`);
        
        try {
            const fs = require('fs');
            const path = require('path');
            
            // 获取所有日志文件
            const files = fs.readdirSync(logsDir);
            const logFiles = files.filter(file => file.endsWith('.log')).sort().reverse(); // 按文件名倒序，最新的在前
            
            const validLogFiles = [];
            
            for (const logFile of logFiles) {
                const logFilePath = path.join(logsDir, logFile);
                try {
                    const logContent = fs.readFileSync(logFilePath, 'utf8');
                    
                    // 检查是否包含执行完毕标记
                    if (logContent.includes('执行完毕，就绪状态已重置，等待下一周期')) {
                        myLogger.log(`[StrategyLogParser] 找到包含执行完毕标记的日志文件: ${logFile}`);
                        validLogFiles.push(logFilePath);
                    }
                } catch (error) {
                    myLogger.log(`[StrategyLogParser] 读取日志文件失败: ${logFile}, 错误: ${error.message}`);
                }
            }
            
            myLogger.log(`[StrategyLogParser] 共找到 ${validLogFiles.length} 个包含执行完毕标记的日志文件`);
            return validLogFiles;
            
        } catch (error) {
            myLogger.log(`[StrategyLogParser] 收集日志文件失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 解析通用策略日志
     * @param {string} strategyId 策略ID
     * @param {string} logContent 日志内容
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 解析后的运行数据
     */
    static async parseGenericLog(strategyId, logContent, userId) {
        try {
            const lines = logContent.split('\n');
            
            // 提取基本信息
            const basicInfo = this.extractBasicInfo(lines);
            
            // 提取持仓信息
            const positions = this.extractPositions(lines);
            
            // 提取交易记录
            const trades = this.extractTrades(lines);
            
            // 构建通用数据结构
            const runtimeData = {
                strategyId: strategyId,
                strategyType: 'generic',
                status: {
                    isRunning: basicInfo.isRunning,
                    lastUpdateTime: basicInfo.lastUpdateTime,
                    uptime: basicInfo.uptime
                },
                performance: {
                    totalReturn: basicInfo.totalReturn || 0,
                    dailyReturn: basicInfo.dailyReturn || 0,
                    currentCapital: basicInfo.currentCapital || 0,
                    initialCapital: basicInfo.initialCapital || 0,
                    maxDrawdown: basicInfo.maxDrawdown || 0
                },
                positions: {
                    totalCount: positions.length,
                    totalValue: positions.reduce((sum, pos) => sum + (pos.marketValue || 0), 0),
                    unrealizedPnL: positions.reduce((sum, pos) => sum + (pos.unrealizedPnL || 0), 0),
                    positions: positions
                },
                risk: {
                    currentRisk: basicInfo.currentRisk || 0,
                    maxRisk: basicInfo.maxRisk || 0,
                    var95: basicInfo.var95 || 0,
                    sharpeRatio: basicInfo.sharpeRatio || 0
                },
                strategyInfo: {
                    generic: {
                        lastSignal: basicInfo.lastSignal,
                        signalCount: basicInfo.signalCount
                    }
                }
            };

            return runtimeData;

        } catch (error) {
            myLogger.error(`[StrategyLogParser] 解析通用策略日志失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 解析多因子策略日志
     * @param {string} logContent - 日志文件内容
     * @param {string} strategyId - 策略ID
     * @param {number} initialCapital - 初始资金
     * @returns {Object} 解析结果
     */
    static parseMultiFactorsLog(logContent, strategyId, initialCapital) {
        try {
            myLogger.log('[StrategyLogParser] 开始解析多因子策略日志');
            
            const lines = logContent.split('\n');
            const signals = [];
            
            // 1. 搜索所有"执行完毕，就绪状态已重置，等待下一周期"的位置
            const completionPositions = [];
            for (let i = 0; i < lines.length; i++) {
                if (lines[i].includes('执行完毕，就绪状态已重置，等待下一周期')) {
                    completionPositions.push(i);
                }
            }
            
            myLogger.log(`[StrategyLogParser] 找到 ${completionPositions.length} 个执行完毕位置`);
            
            // 2. 倒序处理每个执行完毕位置
            for (let i = completionPositions.length - 1; i >= 0; i--) {
                const completionLineIndex = completionPositions[i];
                const completionLine = lines[completionLineIndex];
                
                // 提取策略ID和时间戳
                const strategyIdMatch = completionLine.match(/策略 ([a-f0-9-]+) 执行完毕/);
                const timestampMatch = completionLine.match(/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]/);
                
                if (!strategyIdMatch || !timestampMatch) {
                    myLogger.log('[StrategyLogParser] 跳过无法解析的执行完毕行:', completionLine);
                    continue;
                }
                
                const signalStrategyId = strategyIdMatch[1];
                const timestamp = timestampMatch[1];
                
                // 3. 从执行完毕行往上查找目标品种
                let targetSymbols = [];
                let logicalPositions = {};
                let closePositions = new Set();
                let openPositions = new Set();
                let commission = 0;
                let finalBalance = 0;
                
                // 从执行完毕行往上搜索相关日志
                for (let j = completionLineIndex - 1; j >= 0; j--) {
                    const line = lines[j];
                    
                    // 查找目标品种
                    if (line.includes('[信号计算] 本轮目标品种:')) {
                        myLogger.log('[StrategyLogParser] 找到目标品种行:', line);
                        const match = line.match(/本轮目标品种: (\[.*?\])/);
                        if (match) {
                            myLogger.log('[StrategyLogParser] 目标品种匹配结果:', match[1]);
                            try {
                                const jsonStr = StrategyLogParser.convertPythonToJson(match[1]);
                                targetSymbols = JSON.parse(jsonStr);
                                myLogger.log('[StrategyLogParser] 解析后的目标品种:', targetSymbols);
                            } catch (e) {
                                myLogger.log('[StrategyLogParser] 解析目标品种失败:', e.message);
                            }
                        } else {
                            myLogger.log('[StrategyLogParser] 目标品种正则匹配失败');
                        }
                    }
                    
                    // 查找逻辑持仓
                    if (line.includes('[调仓执行] 用于决策的引擎实际持仓')) {
                        myLogger.log('[StrategyLogParser] 找到逻辑持仓行:', line);
                        const match = line.match(/用于决策的引擎实际持仓.*?: (.*)/);
                        if (match) {
                            myLogger.log('[StrategyLogParser] 逻辑持仓匹配结果:', match[1]);
                            try {
                                const jsonStr = StrategyLogParser.convertPythonToJson(match[1]);
                                logicalPositions = JSON.parse(jsonStr);
                                myLogger.log('[StrategyLogParser] 解析后的逻辑持仓:', logicalPositions);
                            } catch (e) {
                                myLogger.log('[StrategyLogParser] 解析逻辑持仓失败:', e.message);
                            }
                        } else {
                            myLogger.log('[StrategyLogParser] 逻辑持仓正则匹配失败');
                        }
                    }
                    
                    // 查找需平仓品种
                    if (line.includes('[调仓执行] 识别需平仓品种')) {
                        myLogger.log('[StrategyLogParser] 找到需平仓品种行:', line);
                        const match = line.match(/识别需平仓品种.*?: (.*)/);
                        if (match) {
                            myLogger.log('[StrategyLogParser] 需平仓品种匹配结果:', match[1]);
                            try {
                                const jsonStr = StrategyLogParser.convertPythonToJson(match[1]);
                                myLogger.log('[StrategyLogParser] 转换后的JSON字符串:', jsonStr);
                                const closeSet = JSON.parse(jsonStr);
                                closePositions = new Set(closeSet);
                                myLogger.log('[StrategyLogParser] 解析后的需平仓品种:', Array.from(closePositions));
                            } catch (e) {
                                myLogger.log('[StrategyLogParser] 解析需平仓品种失败:', e.message);
                                myLogger.log('[StrategyLogParser] 原始字符串:', match[1]);
                                myLogger.log('[StrategyLogParser] 转换后的字符串:', StrategyLogParser.convertPythonToJson(match[1]));
                            }
                        } else {
                            myLogger.log('[StrategyLogParser] 需平仓品种正则匹配失败');
                        }
                    }
                    
                    // 查找需开仓品种
                    if (line.includes('[调仓执行] 识别需新开仓品种')) {
                        myLogger.log('[StrategyLogParser] 找到需开仓品种行:', line);
                        const match = line.match(/识别需新开仓品种.*?: (.*)/);
                        if (match) {
                            myLogger.log('[StrategyLogParser] 需开仓品种匹配结果:', match[1]);
                            try {
                                const jsonStr = StrategyLogParser.convertPythonToJson(match[1]);
                                myLogger.log('[StrategyLogParser] 转换后的JSON字符串:', jsonStr);
                                const openSet = JSON.parse(jsonStr);
                                openPositions = new Set(openSet);
                                myLogger.log('[StrategyLogParser] 解析后的需开仓品种:', Array.from(openPositions));
                            } catch (e) {
                                myLogger.log('[StrategyLogParser] 解析需开仓品种失败:', e.message);
                                myLogger.log('[StrategyLogParser] 原始字符串:', match[1]);
                                myLogger.log('[StrategyLogParser] 转换后的字符串:', StrategyLogParser.convertPythonToJson(match[1]));
                            }
                        } else {
                            myLogger.log('[StrategyLogParser] 需开仓品种正则匹配失败');
                        }
                    }
                    
                    // 查找手续费
                    if (line.includes('[逻辑余额] 新开仓名义成本') && line.includes('手续费')) {
                        const match = line.match(/手续费\(近似\): ([\d.]+)/);
                        if (match) {
                            commission = parseFloat(match[1]);
                        }
                    }
                    
                    // 查找调仓后余额
                    if (line.includes('[逻辑余额] 调仓指令发送后逻辑余额')) {
                        const match = line.match(/调仓指令发送后逻辑余额.*?: ([\d.]+)/);
                        if (match) {
                            finalBalance = parseFloat(match[1]);
                        }
                    }
                    
                    // 如果找到了目标品种，说明已经找到了完整的调仓信息
                    if (targetSymbols.length > 0) {
                        break;
                    }
                }
                
                // 4. 计算资金比例
                const baseCapital = 100000;
                const ratio = initialCapital / baseCapital;
                
                // 5. 构建信号数据
                const signal = {
                    strategy_id: signalStrategyId,
                    timestamp: timestamp,
                    target_symbols: targetSymbols,
                    logical_positions: logicalPositions,
                    close_positions: Array.from(closePositions),
                    open_positions: Array.from(openPositions),
                    commission: commission,
                    final_balance: finalBalance,
                    ratio: ratio,
                    actual_initial_capital: initialCapital,
                    base_capital: baseCapital
                };
                
                signals.push(signal);
                myLogger.log(`[StrategyLogParser] 解析到信号: ${signalStrategyId} at ${timestamp}`);
            }
            
            // 6. 返回解析结果
            const result = {
                strategy_id: strategyId,
                strategy_type: 'multi_factors',
                signals: signals,
                latest_signal: signals.length > 0 ? signals[0] : null,
                total_signals: signals.length,
                initial_capital: initialCapital,
                base_capital: 100000,
                ratio: initialCapital / 100000
            };
            
            myLogger.log(`[StrategyLogParser] 多因子策略解析完成，共 ${signals.length} 个信号`);
            return result;
            
        } catch (error) {
            myLogger.error('[StrategyLogParser] 解析多因子策略日志失败:', error);
            return {
                strategy_id: strategyId,
                strategy_type: 'multi_factors',
                signals: [],
                latest_signal: null,
                total_signals: 0,
                error: error.message
            };
        }
    }

    /**
     * 提取基本信息
     * @param {string[]} lines 日志行数组
     * @returns {Object} 基本信息
     */
    static extractBasicInfo(lines) {
        const info = {
            isRunning: false,
            lastUpdateTime: new Date().toISOString(),
            uptime: '0天0小时0分钟',
            totalReturn: 0,
            dailyReturn: 0,
            currentCapital: 0,
            initialCapital: 0,
            maxDrawdown: 0,
            currentRisk: 0,
            maxRisk: 0,
            var95: 0,
            sharpeRatio: 0
        };

        // 解析日志行，提取关键信息
        for (const line of lines) {
            // 检查策略运行状态
            if (line.includes('策略运行中') || line.includes('running')) {
                info.isRunning = true;
            }
            
            // 提取收益率信息
            const returnMatch = line.match(/总收益率[：:]\s*([+-]?\d+\.?\d*)%/);
            if (returnMatch) {
                info.totalReturn = parseFloat(returnMatch[1]);
            }
            
            // 提取资金信息
            const capitalMatch = line.match(/当前资金[：:]\s*(\d+\.?\d*)/);
            if (capitalMatch) {
                info.currentCapital = parseFloat(capitalMatch[1]);
            }
            
            // 提取初始资金
            const initCapitalMatch = line.match(/初始资金[：:]\s*(\d+\.?\d*)/);
            if (initCapitalMatch) {
                info.initialCapital = parseFloat(initCapitalMatch[1]);
            }
        }

        return info;
    }

    /**
     * 提取持仓信息
     * @param {string[]} lines 日志行数组
     * @returns {Array} 持仓信息数组
     */
    static extractPositions(lines) {
        const positions = [];
        
        // 解析持仓信息
        for (const line of lines) {
            // 匹配持仓信息格式：持仓 SSE.600036 100股 成本32.5 现价33.2 盈亏+70.0
            const positionMatch = line.match(/持仓\s+(\S+)\s+(\d+)股\s+成本(\d+\.?\d*)\s+现价(\d+\.?\d*)\s+盈亏([+-]?\d+\.?\d*)/);
            if (positionMatch) {
                const [, symbol, quantity, avgPrice, currentPrice, pnl] = positionMatch;
                positions.push({
                    symbol: symbol,
                    name: this.getSymbolName(symbol),
                    quantity: parseInt(quantity),
                    avgPrice: parseFloat(avgPrice),
                    currentPrice: parseFloat(currentPrice),
                    unrealizedPnL: parseFloat(pnl),
                    marketValue: parseInt(quantity) * parseFloat(currentPrice),
                    weight: 0 // 权重需要根据总资金计算
                });
            }
        }

        // 计算权重
        const totalValue = positions.reduce((sum, pos) => sum + pos.marketValue, 0);
        positions.forEach(pos => {
            pos.weight = totalValue > 0 ? pos.marketValue / totalValue : 0;
        });

        return positions;
    }

    /**
     * 提取交易记录
     * @param {string[]} lines 日志行数组
     * @returns {Array} 交易记录数组
     */
    static extractTrades(lines) {
        const trades = [];
        
        // 解析交易记录
        for (const line of lines) {
            // 匹配交易记录格式：交易 BUY SSE.600036 100股 价格32.5 金额3250.0
            const tradeMatch = line.match(/交易\s+(BUY|SELL)\s+(\S+)\s+(\d+)股\s+价格(\d+\.?\d*)\s+金额(\d+\.?\d*)/);
            if (tradeMatch) {
                const [, action, symbol, quantity, price, amount] = tradeMatch;
                trades.push({
                    time: new Date().toISOString(), // 实际应该从日志中提取时间
                    symbol: symbol,
                    action: action,
                    quantity: parseInt(quantity),
                    price: parseFloat(price),
                    amount: parseFloat(amount),
                    commission: parseFloat(amount) * 0.001, // 估算手续费
                    reason: '策略信号'
                });
            }
        }

        return trades;
    }

    /**
     * 提取portfolio策略特定信息
     * @param {string[]} lines 日志行数组
     * @param {number} initialCapital 初始资金（可选，如果不提供则从日志中提取）
     * @returns {Object} portfolio策略信息
     */
    static extractPortfolioInfo(lines, initialCapital = null) {
        myLogger.log('[StrategyLogParser] 开始提取portfolio策略信息');
        
        const signals = [];
        let latest_signal = null;
        let initial_capital = initialCapital || 100000;
        let ratio = 1.0;
        
        // 如果没有提供初始资金，从日志中提取
        if (!initialCapital) {
            for (const line of lines) {
                const initCapitalMatch = line.match(/设置初始逻辑余额[：:]\s*(\d+\.?\d*)/);
                if (initCapitalMatch) {
                    initial_capital = parseFloat(initCapitalMatch[1]);
                    ratio = initial_capital / 100000;
                    myLogger.log(`[StrategyLogParser] 提取到初始资金: ${initial_capital}, 比例: ${ratio}`);
                    break;
                }
            }
        } else {
            ratio = initial_capital / 100000;
            myLogger.log(`[StrategyLogParser] 使用提供的初始资金: ${initial_capital}, 比例: ${ratio}`);
        }
        
        // 1. 搜索所有"执行完毕，就绪状态已重置，等待下一周期"的位置
        const completionPositions = [];
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('执行完毕，就绪状态已重置，等待下一周期')) {
                completionPositions.push(i);
            }
        }
        
        myLogger.log(`[StrategyLogParser] 找到 ${completionPositions.length} 个执行完毕位置`);
        
        // 2. 倒序处理每个执行完毕位置
        for (let i = completionPositions.length - 1; i >= 0; i--) {
            const completionLineIndex = completionPositions[i];
            const completionLine = lines[completionLineIndex];
            
            // 提取策略ID和时间戳
            const strategyIdMatch = completionLine.match(/策略 ([a-f0-9-]+) 执行完毕/);
            const timestampMatch = completionLine.match(/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]/);
            
            if (!strategyIdMatch || !timestampMatch) {
                myLogger.log('[StrategyLogParser] 跳过无法解析的执行完毕行:', completionLine);
                continue;
            }
            
            const signalStrategyId = strategyIdMatch[1];
            const timestamp = timestampMatch[1];
            
            // 3. 从执行完毕行往上查找目标品种
            let targetSymbols = [];
            let logicalPositions = {};
            let closePositions = new Set();
            let openPositions = new Set();
            let commission = 0;
            let finalBalance = 0;
            
            // 从执行完毕行往上搜索相关日志
            for (let j = completionLineIndex - 1; j >= 0; j--) {
                const line = lines[j];
                
                // 查找目标品种
                if (line.includes('[信号计算] 本轮目标品种:')) {
                    myLogger.log('[StrategyLogParser] 找到目标品种行:', line);
                    const match = line.match(/本轮目标品种: (\[.*?\])/);
                    if (match) {
                        myLogger.log('[StrategyLogParser] 目标品种匹配结果:', match[1]);
                        try {
                            const jsonStr = this.convertPythonToJson(match[1]);
                            targetSymbols = JSON.parse(jsonStr);
                            myLogger.log('[StrategyLogParser] 解析后的目标品种:', targetSymbols);
                        } catch (e) {
                            myLogger.log('[StrategyLogParser] 解析目标品种失败:', e.message);
                        }
                    } else {
                        myLogger.log('[StrategyLogParser] 目标品种正则匹配失败');
                    }
                }
                
                // 查找逻辑持仓
                if (line.includes('[调仓执行] 用于决策的引擎实际持仓')) {
                    myLogger.log('[StrategyLogParser] 找到逻辑持仓行:', line);
                    const match = line.match(/用于决策的引擎实际持仓.*?: (.*)/);
                    if (match) {
                        myLogger.log('[StrategyLogParser] 逻辑持仓匹配结果:', match[1]);
                        try {
                            const jsonStr = this.convertPythonToJson(match[1]);
                            logicalPositions = JSON.parse(jsonStr);
                            myLogger.log('[StrategyLogParser] 解析后的逻辑持仓:', logicalPositions);
                        } catch (e) {
                            myLogger.log('[StrategyLogParser] 解析逻辑持仓失败:', e.message);
                        }
                    } else {
                        myLogger.log('[StrategyLogParser] 逻辑持仓正则匹配失败');
                    }
                }
                
                // 查找需平仓品种
                if (line.includes('[调仓执行] 识别需平仓品种')) {
                    myLogger.log('[StrategyLogParser] 找到需平仓品种行:', line);
                    const match = line.match(/识别需平仓品种.*?: (.*)/);
                    if (match) {
                        myLogger.log('[StrategyLogParser] 需平仓品种匹配结果:', match[1]);
                        try {
                            const jsonStr = this.convertPythonToJson(match[1]);
                            const closeSet = JSON.parse(jsonStr);
                            closePositions = new Set(closeSet);
                            myLogger.log('[StrategyLogParser] 解析后的需平仓品种:', Array.from(closePositions));
                        } catch (e) {
                            myLogger.log('[StrategyLogParser] 解析需平仓品种失败:', e.message);
                        }
                    } else {
                        myLogger.log('[StrategyLogParser] 需平仓品种正则匹配失败');
                    }
                }
                
                // 查找需开仓品种
                if (line.includes('[调仓执行] 识别需新开仓品种')) {
                    myLogger.log('[StrategyLogParser] 找到需开仓品种行:', line);
                    const match = line.match(/识别需新开仓品种.*?: (.*)/);
                    if (match) {
                        myLogger.log('[StrategyLogParser] 需开仓品种匹配结果:', match[1]);
                        try {
                            const jsonStr = this.convertPythonToJson(match[1]);
                            const openSet = JSON.parse(jsonStr);
                            openPositions = new Set(openSet);
                            myLogger.log('[StrategyLogParser] 解析后的需开仓品种:', Array.from(openPositions));
                        } catch (e) {
                            myLogger.log('[StrategyLogParser] 解析需开仓品种失败:', e.message);
                        }
                    } else {
                        myLogger.log('[StrategyLogParser] 需开仓品种正则匹配失败');
                    }
                }
                
                // 查找手续费
                if (line.includes('[逻辑余额] 新开仓名义成本') && line.includes('手续费')) {
                    const match = line.match(/手续费\(近似\): ([\d.]+)/);
                    if (match) {
                        commission = parseFloat(match[1]);
                    }
                }
                
                // 查找调仓后余额
                if (line.includes('[逻辑余额] 调仓指令发送后逻辑余额')) {
                    const match = line.match(/调仓指令发送后逻辑余额.*?: ([\d.]+)/);
                    if (match) {
                        finalBalance = parseFloat(match[1]);
                    }
                }
                
                // 如果找到了目标品种，说明已经找到了完整的调仓信息
                if (targetSymbols.length > 0) {
                    break;
                }
            }
            
            // 4. 计算资金比例
            const baseCapital = 100000;
            const signalRatio = initial_capital / baseCapital;
            
            // 5. 构建信号数据
            const signal = {
                strategy_id: signalStrategyId,
                timestamp: timestamp,
                target_symbols: targetSymbols,
                logical_positions: logicalPositions,
                close_positions: Array.from(closePositions),
                open_positions: Array.from(openPositions),
                commission: commission,
                final_balance: finalBalance,
                ratio: signalRatio,
                actual_initial_capital: initial_capital,
                base_capital: baseCapital
            };
            
            signals.push(signal);
            myLogger.log(`[StrategyLogParser] 提取到portfolio信号: ${signalStrategyId} at ${timestamp}`);
        }
        
        // 设置最新信号
        if (signals.length > 0) {
            latest_signal = signals[0]; // 最新的信号在数组开头
        }
        
        const result = {
            signals: signals,
            latest_signal: latest_signal,
            total_signals: signals.length,
            initial_capital: initial_capital,
            ratio: ratio
        };
        
        myLogger.log(`[StrategyLogParser] portfolio策略信息提取完成，共 ${signals.length} 个信号`);
        return result;
    }

    /**
     * 将Python格式的字符串转换为JSON格式
     * @param {string} pythonStr Python格式的字符串
     * @returns {string} JSON格式的字符串
     */
    static convertPythonToJson(pythonStr) {
        try {
            // 处理空set() -> []
            if (pythonStr.trim() === 'set()') {
                myLogger.log('[StrategyLogParser] Python格式转换: set() -> []');
                return '[]';
            }
            
            // 处理非空set() -> 数组
            const setMatch = pythonStr.match(/^set\(\[(.*)\]\)$/);
            if (setMatch) {
                const arrayContent = setMatch[1];
                const jsonArray = `[${arrayContent}]`;
                myLogger.log('[StrategyLogParser] Python格式转换:', pythonStr, '->', jsonArray);
                return jsonArray;
            }
            
            // 处理set() 不带方括号的情况，如 set('item1', 'item2')
            const setMatch2 = pythonStr.match(/^set\(([^)]*)\)$/);
            if (setMatch2) {
                const content = setMatch2[1];
                if (content.trim() === '') {
                    myLogger.log('[StrategyLogParser] Python格式转换: set() -> []');
                    return '[]';
                } else {
                    // 分割内容并转换为数组
                    const items = content.split(',').map(item => item.trim().replace(/^['"]|['"]$/g, '"'));
                    const jsonArray = `[${items.join(',')}]`;
                    myLogger.log('[StrategyLogParser] Python格式转换:', pythonStr, '->', jsonArray);
                    return jsonArray;
                }
            }
            
            // 处理Python set格式: {'item1', 'item2'} -> ["item1", "item2"]
            const setMatch3 = pythonStr.match(/^\{([^}]*)\}$/);
            myLogger.log(`[StrategyLogParser] 检查Python set格式: ${pythonStr}, 正则匹配结果: ${setMatch3}`);
            if (setMatch3) {
                const content = setMatch3[1];
                myLogger.log(`[StrategyLogParser] 提取的内容: ${content}`);
                if (content.trim() === '') {
                    myLogger.log('[StrategyLogParser] Python格式转换: {} -> []');
                    return '[]';
                } else {
                    // 分割内容并转换为数组
                    const items = content.split(',').map(item => {
                        const trimmed = item.trim();
                        // 确保字符串用双引号包围
                        if (trimmed.startsWith("'") && trimmed.endsWith("'")) {
                            return `"${trimmed.slice(1, -1)}"`;
                        } else if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
                            return trimmed;
                        } else {
                            return `"${trimmed}"`;
                        }
                    });
                    const jsonArray = `[${items.join(',')}]`;
                    myLogger.log('[StrategyLogParser] Python格式转换:', pythonStr, '->', jsonArray);
                    return jsonArray;
                }
            }
            
            // 将单引号替换为双引号
            let jsonStr = pythonStr.replace(/'/g, '"');
            
            // 处理None -> null
            jsonStr = jsonStr.replace(/None/g, 'null');
            
            // 处理True -> true
            jsonStr = jsonStr.replace(/True/g, 'true');
            
            // 处理False -> false
            jsonStr = jsonStr.replace(/False/g, 'false');
            
            myLogger.log('[StrategyLogParser] Python格式转换:', pythonStr, '->', jsonStr);
            return jsonStr;
        } catch (e) {
            myLogger.log('[StrategyLogParser] Python格式转换失败:', e.message);
            return pythonStr;
        }
    }

    /**
     * 获取从新到旧排序的日志文件列表
     * @param {string} strategyId 策略ID
     * @param {string} userId 用户ID
     * @returns {Promise<string[]>} 日志文件路径列表（新到旧）
     */
    static async getLogFilesFromNewToOld(strategyId, userId) {
        try {
            myLogger.log(`[StrategyLogParser] 获取日志文件列表: strategyId=${strategyId}, userId=${userId}`);
            
            // 构建日志目录路径
            const basePath = path.join(__dirname, '..', '_Providers', '_Python', 'strategy', 'live_trading');
            const userDir = path.join(basePath, `user_${userId}`);
            const logsDir = path.join(userDir, 'logs');
            
            myLogger.log(`[StrategyLogParser] 日志目录: ${logsDir}`);
            
            // 检查目录是否存在
            try {
                await fs.access(logsDir);
            } catch (error) {
                myLogger.warn(`[StrategyLogParser] 日志目录不存在: ${logsDir}`);
                return [];
            }
            
            // 获取目录下的所有文件
            const files = await fs.readdir(logsDir);
            myLogger.log(`[StrategyLogParser] 目录下文件: ${files.join(', ')}`);
            
            // 过滤出.log文件并验证格式
            const logFiles = [];
            for (const file of files) {
                if (file.endsWith('.log')) {
                    // 验证文件名格式是否为 yyyy-mm-dd.log
                    const dateMatch = file.match(/^(\d{4})-(\d{2})-(\d{2})\.log$/);
                    if (dateMatch) {
                        const year = parseInt(dateMatch[1]);
                        const month = parseInt(dateMatch[2]);
                        const day = parseInt(dateMatch[3]);
                        
                        // 验证日期有效性
                        const date = new Date(year, month - 1, day);
                        if (date.getFullYear() === year && 
                            date.getMonth() === month - 1 && 
                            date.getDate() === day) {
                            logFiles.push({
                                filename: file,
                                filepath: path.join(logsDir, file),
                                date: date
                            });
                        } else {
                            myLogger.log(`[StrategyLogParser] 跳过无效日期格式文件: ${file}`);
                        }
                    } else {
                        myLogger.log(`[StrategyLogParser] 跳过非标准格式文件: ${file}`);
                    }
                }
            }
            
            // 按日期倒序排序（新到旧）
            logFiles.sort((a, b) => b.date.getTime() - a.date.getTime());
            
            // 提取文件路径
            const sortedLogFiles = logFiles.map(file => file.filepath);
            
            myLogger.log(`[StrategyLogParser] 找到 ${sortedLogFiles.length} 个有效日志文件:`);
            sortedLogFiles.forEach((filepath, index) => {
                myLogger.log(`[StrategyLogParser] ${index + 1}. ${path.basename(filepath)}`);
            });
            
            return sortedLogFiles;
            
        } catch (error) {
            myLogger.error(`[StrategyLogParser] 获取日志文件列表失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 计算累计开仓数量
     * @param {Array} results 解析结果数组
     * @returns {number} 累计开仓数量
     */
    static openCounts(results) {
        let totalOpenPositions = 0;
        
        myLogger.log(`[StrategyLogParser] 开始计算开仓数量，共有 ${results.length} 个结果`);
        
        for (let i = 0; i < results.length; i++) {
            const result = results[i];
            if (Array.isArray(result.open_positions)) {
                const count = result.open_positions.length;
                totalOpenPositions += count;
                if (count > 0) {
                    myLogger.log(`[StrategyLogParser] 第${i+1}个结果有 ${count} 个开仓: ${JSON.stringify(result.open_positions)}`);
                }
            }
        }
        
        myLogger.log(`[StrategyLogParser] 累计开仓数量: ${totalOpenPositions}`);
        return totalOpenPositions;
    }

    /**
     * 将解析结果转换为标准运行时数据结构
     * @param {Array} results 解析结果数组
     * @returns {Object} 标准运行时数据结构
     */
    static convertToStandardRuntimeData(results) {
        myLogger.log(`[StrategyLogParser] 转换 ${results.length} 个解析结果为标准数据结构`);
        
        // 现在results直接是信号数组，不需要合并
        const allSignals = results;
        const totalSignals = results.length;
        let latestSignal = null;
        let strategyId = null;
        let strategyType = 'portfolio'; // 默认为portfolio类型
        let initialCapital = 100000;
        let baseCapital = 100000;
        let ratio = 1.0;
        
        // 从第一个信号中获取策略信息（如果存在）
        if (results.length > 0) {
            const firstSignal = results[0];
            if (firstSignal.strategy_id) {
                strategyId = firstSignal.strategy_id;
            }
            if (firstSignal.actual_initial_capital) {
                initialCapital = firstSignal.actual_initial_capital;
            }
            if (firstSignal.base_capital) {
                baseCapital = firstSignal.base_capital;
            }
            if (firstSignal.ratio) {
                ratio = firstSignal.ratio;
            }
        }
        
        // 设置最新信号（第一个信号是最新的）
        if (allSignals.length > 0) {
            latestSignal = allSignals[0];
        }
        
        const standardData = {
            strategy_id: strategyId,
            strategy_type: strategyType,
            signals: allSignals,
            latest_signal: latestSignal,
            total_signals: totalSignals,
            initial_capital: initialCapital,
            base_capital: baseCapital,
            ratio: ratio
        };
        
        myLogger.log(`[StrategyLogParser] 标准数据结构转换完成: ${totalSignals} 个信号`);
        return standardData;
    }

    /**
     * 获取品种名称
     * @param {string} symbol 品种代码
     * @returns {string} 品种名称
     */
    static getSymbolName(symbol) {
        // 这里应该从数据库或配置文件中获取品种名称
        // 暂时返回代码作为名称
        return symbol;
    }

    /**
     * 将UTC时间转换为北京时间
     * @param {string} utcTimeString UTC时间字符串，格式如 "2025-07-13T18:23:42.490Z"
     * @returns {string} 北京时间字符串，格式如 "2025-07-14 02:23:42"
     */
    static convertTimeToBeijing(utcTimeString) {
        try {
            if (!utcTimeString) {
                return utcTimeString;
            }
            
            // 解析UTC时间
            const utcDate = new Date(utcTimeString);
            
            // 检查日期是否有效
            if (isNaN(utcDate.getTime())) {
                myLogger.log(`[StrategyLogParser] 无效的时间格式: ${utcTimeString}`);
                return utcTimeString;
            }
            
            // 转换为北京时间（UTC+8）
            const beijingDate = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);
            
            // 格式化为用户可读的格式 yyyy-mm-dd hh:mm:ss
            const year = beijingDate.getFullYear();
            const month = String(beijingDate.getMonth() + 1).padStart(2, '0');
            const day = String(beijingDate.getDate()).padStart(2, '0');
            const hours = String(beijingDate.getHours()).padStart(2, '0');
            const minutes = String(beijingDate.getMinutes()).padStart(2, '0');
            const seconds = String(beijingDate.getSeconds()).padStart(2, '0');
            
            const beijingTimeString = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            
            myLogger.log(`[StrategyLogParser] 时间转换: ${utcTimeString} -> ${beijingTimeString}`);
            
            return beijingTimeString;
            
        } catch (error) {
            myLogger.error(`[StrategyLogParser] 时间转换失败: ${error.message}`);
            return utcTimeString;
        }
    }
}

module.exports = StrategyLogParser; 