// backend/database/migrations/create_user_trading_configs.js
const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('user_trading_configs', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '配置ID'
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        comment: '用户ID'
      },
      channelType: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '通道类型: openctp, miniQMT 等'
      },
      configName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '用户自定义配置名称'
      },
      broker: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '经纪商代码，可为空'
      },
      username: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '交易账户'
      },
      password: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '密码（加密存储）'
      },
      appid: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '应用ID，可为空'
      },
      authcode: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '认证码，可为空'
      },
      frontAddress: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '前置地址'
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '是否激活'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    });

    // 添加唯一约束：每个用户每种通道类型只能有一个配置
    await queryInterface.addIndex('user_trading_configs', {
      fields: ['userId', 'channelType'],
      unique: true,
      name: 'unique_user_channel'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('user_trading_configs');
  }
};
