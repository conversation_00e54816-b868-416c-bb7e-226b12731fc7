#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths交易适配器

该模块实现了Wonder Trader框架所需的适配器接口，
将Wonder Trader的标准接口转换为easytrader_ths客户端的API调用。
"""

import os
import json
import logging
import requests
from datetime import datetime

logger = logging.getLogger("EasyTrader-THS-Adapter")

class TraderEasyTraderTHS:
    """
    同花顺交易适配器，实现Wonder Trader所需的XTP接口
    这个适配器将Wonder Trader的交易请求转发到我们的tradeHandler模块
    """

    def __init__(self, context, params):
        """
        初始化适配器

        Args:
            context: Wonder Trader上下文
            params: 配置参数，包含以下字段:
                - user: 用户名 (对应tdtraders.yaml中的user字段)
                - acckey: 客户端标识 (对应tdtraders.yaml中的acckey字段，即clientKey)
                - host: 后端服务器地址 (对应tdtraders.yaml中的host字段)
                - port: 后端服务器端口 (对应tdtraders.yaml中的port字段)
                - timeout: 请求超时时间(秒)
        """
        self.context = context
        self.params = params
        self.username = params.get('user', '')
        self.acckey = params.get('acckey', '')  # 这就是clientKey
        self.host = params.get('host', '127.0.0.1')
        self.port = params.get('port', 3000)
        self.timeout = params.get('timeout', 10)

        # 构建后端API基础URL
        self.backend_url = f"http://{self.host}:{self.port}/api/trade"

        # 检查参数
        if not self.username:
            raise ValueError("未指定用户名 (user)")

        if not self.acckey:
            raise ValueError("未指定客户端标识 (acckey)")

        logger.info(f"初始化同花顺交易适配器:")
        logger.info(f"  用户名: {self.username}")
        logger.info(f"  客户端标识: {self.acckey}")
        logger.info(f"  后端地址: {self.backend_url}")

        # 检查后端连接
        self._check_backend_connection()
    
    def _check_connection(self):
        """检查与客户端的连接"""
        try:
            response = requests.get(f"{self.client_url}/api/status", timeout=self.timeout)
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    status = result.get("data", {})
                    if status.get("trader_status") == "connected":
                        logger.info(f"成功连接到同花顺交易客户端: {self.client_url}")
                        return True
                    else:
                        logger.error(f"同花顺交易客户端状态异常: {status.get('trader_status')}")
                else:
                    logger.error(f"检查连接失败: {result.get('message')}")
            else:
                logger.error(f"检查连接失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"检查连接时发生错误: {str(e)}")
            raise ConnectionError(f"无法连接到同花顺交易客户端: {str(e)}")
    
    def _request(self, method, endpoint, data=None):
        """发送请求到客户端"""
        url = f"{self.client_url}{endpoint}"
        try:
            if method.lower() == 'get':
                response = requests.get(url, timeout=self.timeout)
            else:
                response = requests.post(url, json=data, timeout=self.timeout)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    return result.get("data")
                else:
                    error_msg = result.get("message", "未知错误")
                    logger.error(f"请求失败: {error_msg}")
                    raise Exception(error_msg)
            else:
                logger.error(f"请求失败，状态码: {response.status_code}")
                raise Exception(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"请求时发生错误: {str(e)}")
            raise
    
    # Wonder Trader接口实现
    def buy(self, code, price, volume):
        """买入股票"""
        logger.info(f"买入请求: code={code}, price={price}, volume={volume}")
        data = {
            "code": code,
            "price": price,
            "amount": volume
        }
        result = self._request('post', '/api/buy', data)
        # 记录订单信息
        self._log_order(code, "buy", price, volume, result)
        return result
    
    def sell(self, code, price, volume):
        """卖出股票"""
        logger.info(f"卖出请求: code={code}, price={price}, volume={volume}")
        data = {
            "code": code,
            "price": price,
            "amount": volume
        }
        result = self._request('post', '/api/sell', data)
        # 记录订单信息
        self._log_order(code, "sell", price, volume, result)
        return result
    
    def cancel_order(self, entrust_no):
        """撤销订单"""
        logger.info(f"撤单请求: entrust_no={entrust_no}")
        data = {
            "entrust_no": entrust_no
        }
        return self._request('post', '/api/cancel', data)
    
    def get_positions(self):
        """获取持仓信息"""
        logger.debug("获取持仓信息")
        positions_data = self._request('get', '/api/position')
        
        # 转换为Wonder Trader格式
        positions = []
        for pos in positions_data:
            # 提取股票代码
            code = pos.get('证券代码', '')
            
            # 构建持仓信息
            position = {
                'code': code,
                'volume': float(pos.get('股票余额', 0)),
                'available': float(pos.get('可用余额', 0)),
                'price': float(pos.get('成本价', 0)),
                'profit': float(pos.get('浮动盈亏', 0)),
                'margin': 0.0,  # 保证金，股票交易不适用
                'frozen': float(pos.get('股票余额', 0)) - float(pos.get('可用余额', 0))
            }
            positions.append(position)
        
        return positions
    
    def get_account(self):
        """获取账户信息"""
        logger.debug("获取账户信息")
        balance_data = self._request('get', '/api/balance')
        
        # 转换为Wonder Trader格式
        account = {
            'balance': float(balance_data.get('总资产', 0)),
            'available': float(balance_data.get('可用金额', 0)),
            'frozen': float(balance_data.get('冻结金额', 0)),
            'market_value': float(balance_data.get('股票市值', 0)),
            'profit': float(balance_data.get('浮动盈亏', 0))
        }
        
        return account
    
    def get_orders(self):
        """获取当日委托"""
        logger.debug("获取当日委托")
        orders_data = self._request('get', '/api/today_entrusts')
        
        # 转换为Wonder Trader格式
        orders = []
        for order in orders_data:
            # 提取委托状态
            status_map = {
                '已成': 'filled',
                '部成': 'partially_filled',
                '已报': 'submitted',
                '废单': 'cancelled',
                '未报': 'pending'
            }
            status = status_map.get(order.get('委托状态', ''), 'unknown')
            
            # 提取委托方向
            direction = 'buy' if '买' in order.get('操作', '') else 'sell'
            
            # 构建委托信息
            order_info = {
                'entrust_no': order.get('委托编号', ''),
                'code': order.get('证券代码', ''),
                'direction': direction,
                'price': float(order.get('委托价格', 0)),
                'volume': float(order.get('委托数量', 0)),
                'traded': float(order.get('成交数量', 0)),
                'status': status,
                'time': order.get('委托时间', '')
            }
            orders.append(order_info)
        
        return orders
    
    def get_trades(self):
        """获取当日成交"""
        logger.debug("获取当日成交")
        trades_data = self._request('get', '/api/today_trades')
        
        # 转换为Wonder Trader格式
        trades = []
        for trade in trades_data:
            # 提取交易方向
            direction = 'buy' if '买' in trade.get('操作', '') else 'sell'
            
            # 构建成交信息
            trade_info = {
                'trade_id': trade.get('成交编号', ''),
                'entrust_no': trade.get('委托编号', ''),
                'code': trade.get('证券代码', ''),
                'direction': direction,
                'price': float(trade.get('成交价格', 0)),
                'volume': float(trade.get('成交数量', 0)),
                'amount': float(trade.get('成交金额', 0)),
                'time': trade.get('成交时间', '')
            }
            trades.append(trade_info)
        
        return trades
    
    def refresh(self):
        """刷新接口"""
        logger.debug("刷新接口")
        return self._request('post', '/api/refresh')
    
    def _log_order(self, code, direction, price, volume, result):
        """记录订单信息"""
        try:
            # 创建日志目录
            log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # 日志文件路径
            log_file = os.path.join(log_dir, f"orders_{datetime.now().strftime('%Y%m%d')}.json")
            
            # 读取现有日志
            orders = []
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        orders = json.load(f)
                except Exception:
                    orders = []
            
            # 添加新订单
            order = {
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'code': code,
                'direction': direction,
                'price': price,
                'volume': volume,
                'result': result
            }
            orders.append(order)
            
            # 保存日志
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(orders, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logger.error(f"记录订单信息失败: {str(e)}")
