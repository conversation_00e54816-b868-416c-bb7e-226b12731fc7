import { useEffect } from 'react';
import { isLoggedIn } from '@/utils/auth';
import { EventBus } from '@/events/eventBus';
import { ChatEvents } from '@/events/events';
import userDispatcher from '@/_Dispatchers/UserDispatcher';
import { chatDispatcher } from '@/_Dispatchers/ChatDispatcher';

const InitialStateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    console.log('[InitialStateProvider] UserDispatcher initialized:', userDispatcher);
    console.log('[InitialStateProvider] ChatDispatcher initialized:', chatDispatcher);

    if (isLoggedIn()) {
      console.log('[InitialStateProvider] User is logged in, requesting default session');
      EventBus.emit(ChatEvents.Types.GET_DEFAULT_SESSION, undefined);
    }
  }, []);

  return <>{children}</>;
};

export default InitialStateProvider; 