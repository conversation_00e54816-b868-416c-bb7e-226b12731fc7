import { KLine, KLineData } from '@/shared_types/market';
import { BaseSignal, SignalParameter, SignalType } from '../BaseSignal';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';

/**
 * 双均线交叉信号
 * 使用快慢两条移动平均线的交叉来判断趋势转折
 * 当快线上穿慢线时产生买入信号
 * 当快线下穿慢线时产生卖出信号
 */
export class MACrossSignal extends BaseSignal {
  private fastPeriod: number;
  private slowPeriod: number;

  constructor(config: SignalConfig) {
    super(config);
    
    // 设置参数
    this.fastPeriod = this.parameters.fastPeriod;
    this.slowPeriod = this.parameters.slowPeriod;

    // 验证参数
    if (this.fastPeriod >= this.slowPeriod) {
      throw new Error('Fast period must be less than slow period');
    }
  }

  /**
   * 获取信号参数列表
   */
  static async getParameters(): Promise<SignalParameter[]> {
    return Promise.resolve([
      {
        name: 'shortPeriod',
        paramType: 'number',
        default: 5,
        description: '短期均线周期',
        minValue: 2,
        maxValue: 100,
        step: 1
      },
      {
        name: 'longPeriod',
        paramType: 'number',
        default: 20,
        description: '长期均线周期',
        minValue: 5,
        maxValue: 200,
        step: 1
      }
    ]);
  }

  /**
   * 计算移动平均线
   * @param data 收盘价数组
   * @param period 周期
   */
  private calculateMA(data: number[], period: number): number[] {
    const result: number[] = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
        continue;
      }
      
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += data[i - j];
      }
      result.push(sum / period);
    }
    return result;
  }

  calculateSignals(kline: KLine, params: Record<string, any>): MarketEvents.SignalItem[] {
    const signals: MarketEvents.SignalItem[] = [];
    const fastPeriod = params.longPeriod as number;
    const slowPeriod = params.shortPeriod as number;

    const data = kline.data;

    // 提取收盘价
    const closes = data.map(k => k.close);

    // 计算快慢均线
    const fastMA = this.calculateMA(closes, fastPeriod);
    const slowMA = this.calculateMA(closes, slowPeriod);

    // 从慢线周期开始遍历，寻找交叉点
    for (let i = slowPeriod; i < data.length; i++) {
      // 判断是否发生交叉
      const crossUp = fastMA[i - 1] <= slowMA[i - 1] && fastMA[i] > slowMA[i];
      const crossDown = fastMA[i - 1] >= slowMA[i - 1] && fastMA[i] < slowMA[i];

      if (crossUp) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.BUY,
          price: data[i].close,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      } else if (crossDown) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.SELL,
          price: data[i].close,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      }
    }

    return signals;
  }
} 