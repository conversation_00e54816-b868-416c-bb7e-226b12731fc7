# atr.py
import numpy as np
import talib
from typing import Optional

def calculate_value(opens: Optional[np.ndarray] = None,
                   highs: Optional[np.ndarray] = None,
                   lows: Optional[np.ndarray] = None,
                   closes: Optional[np.ndarray] = None,
                   volumes: Optional[np.ndarray] = None,
                   **kwargs) -> Optional[float]:
    """
    计算平均真实波幅 (Average True Range - ATR) 在最后一个时间点的数值。
    符合 '正常计算模式的优化效能因子框架.md' 规范。

    Args:
        highs (Optional[np.ndarray]): 最高价序列。
        lows (Optional[np.ndarray]): 最低价序列。
        closes (Optional[np.ndarray]): 收盘价序列。
        **kwargs:
            period (int): ATR 的计算周期 (默认 14)。

    Returns:
        Optional[float]: 计算出的最后一个时间点的 ATR 值，或 np.nan。
    """
    period = kwargs.get('period', 14)

    # --- Input Validation ---
    if highs is None or lows is None or closes is None:
        # print(f"[因子计算:ATR] 输入错误: 需要 highs, lows 和 closes 数据。")
        return np.nan
    if not isinstance(period, int) or period <= 0:
        # print(f"[因子计算:ATR] 参数错误: period ({period}) 必须是正整数。")
        return np.nan
    # TA-Lib 的 ATR 需要 period + 1 个数据点才能产生第一个有效值
    min_required_len = period + 1
    if len(closes) < min_required_len or len(highs) < min_required_len or len(lows) < min_required_len:
        # print(f"[因子计算:ATR] 数据不足: 需要至少 {min_required_len} 条数据。")
        return np.nan
    # --- End Validation ---

    try:
        # 使用 TA-Lib 计算 ATR 序列
        # talib.ATR 需要 float64 输入
        atr_series = talib.ATR(
            highs.astype(np.float64),
            lows.astype(np.float64),
            closes.astype(np.float64),
            timeperiod=period
        )
        
        # 返回最后一个值
        last_atr = atr_series[-1]
        
        return float(last_atr) if not np.isnan(last_atr) else np.nan
    except Exception as e:
        # print(f"[因子计算:ATR] 计算 ATR({period}) 时出错: {e}")
        return np.nan