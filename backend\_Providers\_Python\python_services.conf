[program:py_app]
command=/home/<USER>/projects/quantquart/backend/_Providers/_Python/venv/bin/gunicorn -w 4 -b 0.0.0.0:5000 app:app  # 使用Gunicorn运行Flask
directory=/home/<USER>/projects/quantquart/backend/_Providers/_Python/
user=douqing
autostart=true
autorestart=true
stderr_logfile=/var/log/duduapp.err.log
stdout_logfile=/var/log/duduapp.out.log
environment=PYTHONPATH="/home/<USER>/projects/quantquart/backend/_Providers/_Python/venv/lib/python3.8/site-packages"


[program:tdx_app]
command=/home/<USER>/projects/quantquart/backend/_Providers/_Python/venv/bin/gunicorn -w 4 -b 0.0.0.0:5001 tdxserver:app  # 使用Gunicorn运行Flask
directory=/home/<USER>/projects/quantquart/backend/_Providers/_Python/
user=douqing
autostart=true
autorestart=true
stderr_logfile=/var/log/tdxapp.err.log
stdout_logfile=/var/log/tdxapp.out.log
environment=PYTHONPATH="/home/<USER>/projects/quantquart/backend/_Providers/_Python/venv/lib/python3.8/site-packages"

[program:zip_monitor]
command=/usr/bin/python3 /home/<USER>/stockdata/vipdoc/unzip_monitor.py -i 60 ./
directory=/home/<USER>/stockdata/vipdoc/  ; 脚本所在目录
user=douqing                    ; 用您的用户名
autostart=true
autorestart=true
startsecs=10
stderr_logfile=/var/log/zip_monitor.err.log
stdout_logfile=/var/log/zip_monitor.out.log
environment=
    PYTHONPATH="/projects/quantquart/backend/_Providers/_Python/venv/lib/python3.X/site-packages"  ; 如有虚拟环境

