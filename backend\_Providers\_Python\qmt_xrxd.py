"""
一次性获取并打印除权除息（XR/XD）明细
"""
from xtquant import xtdata
import pandas as pd

def get_dividend(stock_code: str) -> pd.DataFrame:
    """返回除权除息 DataFrame，按日期升序"""
    # 1) 直接读本地缓存（已提前下载）
    data = xtdata.get_financial_data(
        stock_list=[stock_code],
        table_list=["exrights"]
    )

    # 2) 把“字段->数组”转 DataFrame
    raw = data.get(stock_code, {})
    if not raw:
        print("⚠️ 本地无除权数据")
        return pd.DataFrame()

    df = pd.DataFrame(raw).sort_values("date")
    return df[["date", "split_ratio", "cash_div", "bonus_ratio", "rights_issue"]]

if __name__ == "__main__":
    code = "603329.SH"
    df = get_dividend(code)
    if not df.empty:
        print(df.to_string(index=False))