# 实盘项目日志配置文件模板
# 用于配置Wonder Trader引擎和策略的日志输出

# 根日志配置
root:
    async: false                    # 同步日志输出
    level: info                     # 日志级别：debug/info/warn/error
    sinks:
    # 文件日志输出
    -   type: daily_file_sink
        filename: logs/Engine.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false
    
    # 控制台日志输出
    -   type: console_sink
        pattern: '[%m.%d %H:%M:%S - %^%-5l%$] %v'

# 策略日志配置
strategy:
    async: false                    # 同步日志输出
    level: info                     # 日志级别
    sinks:
    # 策略专用日志文件
    -   type: daily_file_sink
        filename: logs/Strategy.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false

# 交易日志配置
trader:
    async: false                    # 同步日志输出
    level: info                     # 日志级别
    sinks:
    # 交易专用日志文件
    -   type: daily_file_sink
        filename: logs/Trader.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false

# 执行器日志配置
executer:
    async: false                    # 同步日志输出
    level: info                     # 日志级别
    sinks:
    # 执行器专用日志文件
    -   type: daily_file_sink
        filename: logs/Executer.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false
