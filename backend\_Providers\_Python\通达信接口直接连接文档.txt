# 获取单只股票的实时行情
stock_data = api.get_security_quotes(1, '600519')

重要知识点：1 代表上海证券交易所 （6开头的股票，688、689 也填1）0 代表深证证券交易交所 （3、0 开头的股票）2 代表北京证券交易所 （4、8、9 开头的股票）如果要获取多个股票的代码使用如下格式数据：[ (市场代码1， 股票代码1)，(市场代码2， 股票代码2) ... (市场代码n， 股票代码n) ]

返回值解析返回值是一个数组，数组里是OrderedDict格式的数据，OrderedDict 是 Python 中 collections 模块提供的一种字典子类，它 保持键值对的插入顺序。与普通字典（dict）不同，OrderedDict 在遍历时会按照键值对插入的顺序返回数据，而不是随机顺序。 可以通过stock_data[0]['open'] 的形式获取具体的值开盘、最高、最低、收盘、成交量、成交额、买盘报价、卖盘报价都有非常不错。

[OrderedDict(
[ 
    ('market', 1), ('code', '600519'), ('active1', 2999),
    ('price', 1518.98), ('last_close', 1505.98), ('open', 1503.0), 
    ('high', 1528.36), ('low', 1503.0), ('servertime', '13:30:00.018'), 
    ('reversed_bytes0', 13300003), ('reversed_bytes1', -151898), 
    ('vol', 27472), ('cur_vol', 38), ('amount', 4162208768.0), 
    ('s_vol', 12340), ('b_vol', 15132), ('reversed_bytes2', 18), 
    ('reversed_bytes3', 379117), ('bid1', 1518.93), ('ask1', 1518.99),
    ('bid_vol1', 2), ('ask_vol1', 1), ('bid2', 1518.91), ('ask2', 1519.0), 
    ('bid_vol2', 8), ('ask_vol2', 17), ('bid3', 1518.84), ('ask3', 1519.03), 
    ('bid_vol3', 7), ('ask_vol3', 2), ('bid4', 1518.82), ('ask4', 1519.91), 
    ('bid_vol4', 8), ('ask_vol4', 1), ('bid5', 1518.8), ('ask5', 1520.0), 
    ('bid_vol5', 9), ('ask_vol5', 18), ('reversed_bytes4', (1356,)), 
    ('reversed_bytes5', 0), ('reversed_bytes6', 0), ('reversed_bytes7', 0),
    ('reversed_bytes8', 0), ('reversed_bytes9', -0.13), ('active2', 2999)
]
 )
]

# 获取日 K 线数据
k_data = api.get_security_bars(9, 1, '600519', 0, 10)
print(k_data)

这里的 9 表示日 K 线，1 代表上交所，'600519' 是股票代码，0, 10 代表从 0 开始获取 10 条数据，最多可获取800根K线。

K线种类
0 5分钟K线 
1 15分钟K线 
2 30分钟K线 
3 1小时K线 
4 日K线
5 周K线
6 月K线
7 1分钟
8 1分钟K线 
9 日K线
10 季K线
11 年K线

返回值解析返回值是一个数组，数组里是OrderedDict格式的数据 为了方便数据计算，可以通过以下方法转换成Dataframe格式