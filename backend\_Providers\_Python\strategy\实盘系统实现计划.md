# Wonder Trader 实盘系统实现计划

## 1. 系统概述

本文档详细描述了基于Wonder Trader框架的实盘系统实现计划，包括目录结构、配置生成、策略运行、XTP接入等方面的详细说明。

### 1.1 设计理念

1. **独立线程运行**：每个实盘用例都是持续运行的独立线程，确保稳定性和隔离性。
2. **多策略协同**：Wonder Trader鼓励同时运行多个策略，可以在引擎内部进行撮合，降低手续费并提升效率。
3. **配置文件优先**：根据官方Demo的实现，使用配置文件初始化引擎，而不是动态字符串。
4. **策略准备工作**：策略执行前需要准备工作，包括读取策略YAML、解析内容、提取品种列表，并将解析内容传递给策略类创建实例。
5. **实盘配置准备**：实盘模块根据策略YAML准备行情数据配置、交易通道和执行器配置，然后传递给策略类创建实例运行实盘。
6. **独立实例管理**：所有实盘项目都是独立实例运行在内存中，可以通过实盘面板进行交互，每个实例有自己的日志输出。

## 2. 目录结构

```
@backend\_Providers\_Python\strategy\
├── live_configs\                # 实盘配置目录
│   ├── templates\               # 配置模板
│   │   ├── config_template.yaml        # 主配置模板
│   │   ├── executers_template.yaml     # 执行器配置模板
│   │   ├── tdtraders_template.yaml     # 交易通道配置模板
│   │   └── tdparsers_template.yaml     # 行情解析器配置模板
│   └── instances\               # 实例配置目录
│       └── <strategy_uuid>\     # 每个策略实例一个目录
│           ├── config.yaml      # 实例特定配置
│           ├── executers.yaml   # 执行器配置
│           ├── tdtraders.yaml   # 交易通道配置
│           └── tdparsers.yaml   # 行情解析器配置
├── live_logs\                   # 实盘日志目录
│   └── <strategy_uuid>\         # 每个策略实例的日志
├── live_engine\                 # 实盘引擎代码
│   ├── live_strategy_server.py  # 实盘策略管理服务
│   ├── live_strategy_runner.py  # 实盘策略运行器
│   └── config_generator.py      # 配置生成器
├── common\                      # 共享定义文件(与回测共用)
├── portfolio\                   # 多因子策略目录(与回测共用)
└── ...                          # 其他目录
```

## 3. 实现流程

### 3.1 配置生成流程

1. 为策略实例创建目录
2. 读取配置模板
3. 根据策略YAML和账户信息替换模板中的变量
4. 将生成的配置保存到策略实例目录

### 3.2 策略部署流程

1. 接收前端的部署请求，包含策略ID、账户信息等
2. 读取策略YAML配置(从回测数据库或文件)
3. 生成唯一的策略实例UUID
4. 生成配置文件
5. 创建独立线程运行策略实例

### 3.3 策略运行流程

1. 加载配置文件
2. 初始化WtEngine，传递配置文件路径
3. 创建策略实例
4. 添加策略到引擎
5. 启动引擎

### 3.4 策略管理流程

1. 提供API接口(部署、启动、停止策略)
2. 为每个策略创建独立线程
3. 管理策略实例
4. 监控策略状态

## 4. 自定义XTP接入说明

### 4.1 XTP接口概述

XTP(eXchange Trading Protocol)是Wonder Trader支持的一种交易接口，用于连接不同的交易系统。在我们的实现中，使用自定义的easytraderXTP接口连接同花顺客户端进行交易。

### 4.2 easytraderXTP实现

easytraderXTP是基于BaseXTP实现的自定义交易接口，用于与同花顺客户端进行交互。主要特点：

1. 通过Socket.IO与客户端建立连接
2. 支持买入、卖出、撤单等交易操作
3. 支持查询账户余额、持仓、委托等信息

### 4.3 XTP配置说明

在tdtraders.yaml中配置XTP交易通道：

```yaml
traders:
-   active: true
    client: 1
    host: 127.0.0.1  # 客户端IP地址
    id: easytrader_ths  # 交易通道ID
    module: TraderXTP  # 模块名称
    user: username  # 用户名
    pass: password  # 密码
    port: 8888  # 端口号
    quick: true
    riskmon:
        active: true
        policy:
            default:
                cancel_stat_timespan: 10
                cancel_times_boundary: 20
                cancel_total_limits: 470
                order_stat_timespan: 10
                order_times_boundary: 20
```

### 4.4 Socket.IO连接流程

1. 客户端启动后，向服务端注册自己的信息（用户名、IP、端口等）
2. 客户端定期向服务端发送心跳，保持连接
3. 服务端接收到交易请求后，通过客户端注册的信息，向客户端发送请求
4. 客户端接收到请求后，控制同花顺客户端执行交易操作，并返回结果

### 4.5 行情数据接入

在tdparsers.yaml中配置行情数据源：

```yaml
parsers:
-   active: true    
    id: parser0
    module: ParserShm                     # 基于Sharememory的行情解析模块
    path: ../datakit_stk/exchange.membin  # memmap文件路径，需要和datakit匹配
    gpsize: 1000                          # 分组大小，主要用于控制日志的显示频率
    check_span: 2                         # 检查时间间隔，单位微秒，如果没有新的数据，则休眠若干时间
```

## 5. 实现计划

### 5.1 第一阶段：基础结构搭建

1. 创建目录结构
2. 创建配置模板文件
3. 实现配置生成器

### 5.2 第二阶段：核心功能实现

1. 实现实盘策略运行器
2. 实现实盘策略管理服务
3. 实现API接口

### 5.3 第三阶段：XTP接入集成

1. 配置easytraderXTP交易通道
2. 设置Socket.IO连接
3. 处理交易指令

### 5.4 第四阶段：测试与调试

1. 单元测试
2. 集成测试
3. 实盘测试

## 6. API接口设计

### 6.1 部署策略

```
POST /api/strategy/deploy
```

请求参数：
- strategyId: 策略ID
- accountId: 账户ID
- initialCapital: 初始资金
- commissionRate: 佣金率
- riskSettings: 风控设置

### 6.2 启动策略

```
POST /api/strategy/live/start/:id
```

请求参数：
- id: 策略实例ID

### 6.3 停止策略

```
POST /api/strategy/live/stop/:id
```

请求参数：
- id: 策略实例ID

### 6.4 获取策略列表

```
GET /api/strategy/live
```

### 6.5 获取策略详情

```
GET /api/strategy/live/:id
```

请求参数：
- id: 策略实例ID

## 7. 注意事项

1. 确保配置文件路径正确
2. 处理异常情况，如策略运行出错、配置生成失败等
3. 实现日志记录，方便调试和问题排查
4. 考虑资源释放，如停止策略时关闭引擎、释放内存等
5. 考虑并发问题，如多个策略同时运行时的资源竞争
