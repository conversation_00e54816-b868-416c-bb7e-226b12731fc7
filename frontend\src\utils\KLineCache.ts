import { Symbol, KLineInterval, KLine } from '@/shared_types/market';

/**
 * K线数据缓存项接口
 */
interface KLineCacheItem {
  symbol: Symbol;
  interval: KLineInterval;
  data: KLine;
  timestamp: number; // 缓存时间戳
}

/**
 * K线数据的IndexedDB缓存管理类
 * 用于在前端缓存K线数据，减少对服务器的请求
 */
export class KLineCache {
  private static DB_NAME = 'KLineCache';
  private static STORE_NAME = 'klines';
  private static VERSION = 1;

  /**
   * 打开IndexedDB数据库
   * @returns Promise<IDBDatabase>
   */
  private static async openDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.VERSION);

      request.onerror = () => {
        console.error('[KLineCache] 打开数据库失败:', request.error);
        reject(request.error);
      };
      
      request.onsuccess = () => {
        console.log('[KLineCache] 数据库打开成功');
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        console.log('[KLineCache] 数据库需要升级');
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          // 创建对象存储，使用复合键 "symbol.code_symbol.market_interval" 作为键
          const store = db.createObjectStore(this.STORE_NAME);
          console.log('[KLineCache] K线存储已创建');
        }
      };
    });
  }

  /**
   * 生成缓存键
   * @param symbol 交易品种
   * @param interval K线周期
   * @returns 缓存键
   */
  private static getCacheKey(symbol: Symbol, interval: KLineInterval): string {
    return `${symbol.code}_${symbol.market}_${interval}`;
  }

  /**
   * 从缓存获取K线数据
   * @param symbol 交易品种
   * @param interval K线周期
   * @returns 缓存的K线数据，如果不存在则返回null
   */
  static async get(symbol: Symbol, interval: KLineInterval): Promise<KLineCacheItem | null> {
    try {
      const cacheKey = this.getCacheKey(symbol, interval);
      console.log('[KLineCache] 尝试从缓存获取K线数据:', cacheKey);
      
      const db = await this.openDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(this.STORE_NAME, 'readonly');
        const store = transaction.objectStore(this.STORE_NAME);
        const request = store.get(cacheKey);

        request.onerror = () => {
          console.error('[KLineCache] 从缓存获取K线数据出错:', request.error);
          reject(request.error);
        };
        
        request.onsuccess = () => {
          if (request.result) {
            console.log('[KLineCache] 在缓存中找到K线数据:', cacheKey);
          } else {
            console.log('[KLineCache] 在缓存中未找到K线数据:', cacheKey);
          }
          resolve(request.result);
        };
      });
    } catch (error) {
      console.error('[KLineCache] 从缓存获取K线数据失败:', error);
      return null;
    }
  }

  /**
   * 将K线数据存入缓存
   * @param symbol 交易品种
   * @param interval K线周期
   * @param kline K线数据
   * @returns Promise<void>
   */
  static async set(symbol: Symbol, interval: KLineInterval, kline: KLine): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(symbol, interval);
      console.log('[KLineCache] 尝试缓存K线数据:', cacheKey);
      
      const cacheItem: KLineCacheItem = {
        symbol,
        interval,
        data: kline,
        timestamp: Date.now()
      };

      const db = await this.openDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(this.STORE_NAME, 'readwrite');
        const store = transaction.objectStore(this.STORE_NAME);
        const request = store.put(cacheItem, cacheKey);

        request.onerror = () => {
          console.error('[KLineCache] 缓存K线数据出错:', request.error);
          reject(request.error);
        };
        
        request.onsuccess = () => {
          console.log('[KLineCache] K线数据缓存成功:', cacheKey);
          resolve();
        };
      });
    } catch (error) {
      console.error('[KLineCache] 缓存K线数据失败:', error);
    }
  }

  /**
   * 检查缓存中是否有比某个时间更新的数据
   * @param symbol 交易品种
   * @param interval K线周期
   * @param timestamp 时间戳（毫秒）
   * @returns 如果缓存数据比给定时间更新，则返回true
   */
  static async isNewerThan(symbol: Symbol, interval: KLineInterval, timestamp: number): Promise<boolean> {
    try {
      const cacheItem = await this.get(symbol, interval);
      if (!cacheItem) return false;
      
      // 获取缓存中最后一条K线的时间
      const lastKLineTime = cacheItem.data.data.length > 0 
        ? cacheItem.data.data[cacheItem.data.data.length - 1].time * 1000 // 转换为毫秒
        : 0;
      
      return lastKLineTime > timestamp;
    } catch (error) {
      console.error('[KLineCache] 检查缓存数据是否更新出错:', error);
      return false;
    }
  }

  /**
   * 清除过期的缓存数据
   * @param maxAge 最大缓存时间（毫秒），默认7天
   */
  static async clearExpired(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const db = await this.openDB();
      const transaction = db.transaction(this.STORE_NAME, 'readwrite');
      const store = transaction.objectStore(this.STORE_NAME);
      const now = Date.now();

      return new Promise((resolve, reject) => {
        const request = store.openCursor();
        
        request.onerror = () => {
          console.error('[KLineCache] 清除过期缓存出错:', request.error);
          reject(request.error);
        };
        
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            const cacheItem = cursor.value as KLineCacheItem;
            if (now - cacheItem.timestamp > maxAge) {
              console.log('[KLineCache] 清除过期缓存:', cursor.key);
              cursor.delete();
            }
            cursor.continue();
          } else {
            console.log('[KLineCache] 过期缓存清理完成');
            resolve();
          }
        };
      });
    } catch (error) {
      console.error('[KLineCache] 清除过期缓存失败:', error);
    }
  }
} 