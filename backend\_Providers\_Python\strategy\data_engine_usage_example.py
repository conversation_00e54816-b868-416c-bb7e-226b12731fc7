#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据引擎管理使用示例
演示如何在实盘项目中使用数据引擎管理API
"""

import requests
import json
import time

class LiveProjectDataManager:
    """
    实盘项目数据管理器
    封装与中心数据引擎的交互
    """
    
    def __init__(self, strategy_server_url="http://localhost:5002"):
        self.strategy_server_url = strategy_server_url
        self.project_id = None
        self.subscribed_contracts = []
    
    def start_project(self, project_id: str, contract_codes: list):
        """
        启动实盘项目并订阅数据
        Args:
            project_id: 项目ID
            contract_codes: 需要订阅的合约代码列表
        """
        self.project_id = project_id
        self.subscribed_contracts = contract_codes
        
        print(f"启动实盘项目: {project_id}")
        print(f"订阅合约: {contract_codes}")
        
        try:
            # 一键更新并重启数据引擎
            response = requests.post(
                f"{self.strategy_server_url}/data_engine/update_and_restart",
                json={
                    "project_id": project_id,
                    "contract_codes": contract_codes
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✓ 数据引擎更新成功: {result.get('message')}")
                    return True
                else:
                    print(f"✗ 数据引擎更新失败: {result.get('error')}")
                    return False
            else:
                print(f"✗ 请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 启动项目时发生错误: {e}")
            return False
    
    def stop_project(self):
        """
        停止实盘项目并取消订阅
        """
        if not self.project_id:
            print("项目未启动")
            return True
        
        print(f"停止实盘项目: {self.project_id}")
        
        try:
            # 取消订阅
            response = requests.post(
                f"{self.strategy_server_url}/data_engine/unsubscribe",
                json={"project_id": self.project_id},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✓ 取消订阅成功: {result.get('message')}")
                    
                    # 重启数据引擎以应用更改
                    restart_response = requests.post(
                        f"{self.strategy_server_url}/data_engine/restart",
                        timeout=30
                    )
                    
                    if restart_response.status_code == 200:
                        restart_result = restart_response.json()
                        if restart_result.get('success'):
                            print("✓ 数据引擎重启成功")
                            self.project_id = None
                            self.subscribed_contracts = []
                            return True
                        else:
                            print(f"✗ 数据引擎重启失败: {restart_result.get('error')}")
                            return False
                    else:
                        print(f"✗ 重启请求失败: HTTP {restart_response.status_code}")
                        return False
                else:
                    print(f"✗ 取消订阅失败: {result.get('error')}")
                    return False
            else:
                print(f"✗ 请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 停止项目时发生错误: {e}")
            return False
    
    def get_engine_status(self):
        """
        获取数据引擎状态
        """
        try:
            response = requests.get(
                f"{self.strategy_server_url}/data_engine/status",
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return result.get('data')
                else:
                    print(f"✗ 获取状态失败: {result.get('error')}")
                    return None
            else:
                print(f"✗ 状态请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"✗ 获取状态时发生错误: {e}")
            return None
    
    def update_subscription(self, new_contract_codes: list):
        """
        更新订阅的合约列表
        Args:
            new_contract_codes: 新的合约代码列表
        """
        if not self.project_id:
            print("项目未启动")
            return False
        
        print(f"更新订阅: {new_contract_codes}")
        
        try:
            response = requests.post(
                f"{self.strategy_server_url}/data_engine/update_and_restart",
                json={
                    "project_id": self.project_id,
                    "contract_codes": new_contract_codes
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✓ 订阅更新成功: {result.get('message')}")
                    self.subscribed_contracts = new_contract_codes
                    return True
                else:
                    print(f"✗ 订阅更新失败: {result.get('error')}")
                    return False
            else:
                print(f"✗ 请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 更新订阅时发生错误: {e}")
            return False

def demo_usage():
    """
    演示使用方法
    """
    print("=== 数据引擎管理使用演示 ===\n")
    
    # 创建项目管理器
    manager = LiveProjectDataManager()
    
    # 模拟实盘项目1
    print("1. 启动实盘项目1...")
    project1_contracts = ["SSE.600036", "SZSE.000001", "SSE.510300"]
    success = manager.start_project("live_project_001", project1_contracts)
    
    if success:
        print("项目1启动成功\n")
        
        # 等待一段时间
        time.sleep(3)
        
        # 检查状态
        print("2. 检查数据引擎状态...")
        status = manager.get_engine_status()
        if status:
            print(f"引擎运行: {status.get('engine_running')}")
            print(f"总项目数: {status.get('total_projects')}")
            print(f"总合约数: {status.get('total_contracts')}")
            print(f"订阅合约: {status.get('subscribed_contracts')}")
        print()
        
        # 更新订阅
        print("3. 更新订阅合约...")
        new_contracts = project1_contracts + ["SSE.600519", "SZSE.300059"]
        manager.update_subscription(new_contracts)
        print()
        
        # 再次检查状态
        print("4. 更新后状态检查...")
        status = manager.get_engine_status()
        if status:
            print(f"总合约数: {status.get('total_contracts')}")
            print(f"订阅合约: {status.get('subscribed_contracts')}")
        print()
        
        # 停止项目
        print("5. 停止实盘项目...")
        manager.stop_project()
        print()
        
        # 最终状态检查
        print("6. 停止后状态检查...")
        status = manager.get_engine_status()
        if status:
            print(f"总项目数: {status.get('total_projects')}")
            print(f"总合约数: {status.get('total_contracts')}")
        
    else:
        print("项目1启动失败")
    
    print("\n=== 演示完成 ===")

if __name__ == "__main__":
    demo_usage()
