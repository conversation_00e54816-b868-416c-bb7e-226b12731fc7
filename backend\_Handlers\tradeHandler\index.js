/**
 * 交易通道处理器
 * 负责管理交易客户端的连接、心跳和状态
 */

const express = require('express');
const router = express.Router();
const axios = require('axios');
// --- 新增: 引入 User 模型 和 认证中间件 ---
const { User } = require('../../models'); // 假设 User 模型在 models 目录下定义
const { authenticateToken } = require('../../middleware/auth'); // 假设认证中间件路径
// --- 新增: 引入 jwt 和 config ---
const jwt = require('jsonwebtoken');
const config = require('../../config.json'); // 假设配置文件在根目录
const JWT_SECRET = config.jwt.secret; // 从配置获取JWT密钥
// --- 结束新增 ---

// 引入XTP工厂和Socket.IO初始化模块

const initSocketIO = require('./initSocketIO');
const { EventEmitter } = require('events');

// 创建简单的日志记录器
const logger = {
    info: (message) => console.log(`[TradeHandler INFO] ${message}`), // 添加前缀以区分日志来源
    warn: (message) => console.warn(`[TradeHandler WARN] ${message}`),
    error: (message) => console.error(`[TradeHandler ERROR] ${message}`),
    debug: (message) => console.log(`[TradeHandler DEBUG] ${message}`)
};

// --- 修改: 存储 Socket.IO 连接对象 ---
const tradingClients = new Map(); // 使用 Map 存储: clientKey -> { username, client_type, socket, registered_at, last_heartbeat }
// --- 结束修改 ---

// 清理过期客户端的间隔（毫秒）
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5分钟

// 初始化标志
let initialized = false;
let ioNamespace = null; // 用于存储 Socket.IO 命名空间

// 工具函数：生成唯一客户端key，现只用client_id
function buildClientKey(client_id) {
    return client_id;
}

class TradeHandler extends EventEmitter {
    constructor() {
        super();
        this.router = express.Router();
        this.tradingClients = new Map();
        this.initialized = false;
        this.CLEANUP_INTERVAL = 5 * 60 * 1000;
        this.initRoutes();
        setInterval(this.cleanupExpiredClients.bind(this), this.CLEANUP_INTERVAL);
    }

    initRoutes() {
        this.router.post('/test_trading_client', authenticateToken, this.testTradingClientHandler.bind(this));
        this.router.get('/test_connection', this.testConnectionHandler.bind(this));
        this.router.post('/execute_trade', authenticateToken, this.executeTradeHandler.bind(this));
        this.router.post('/remove_trading_client', authenticateToken, this.removeTradingClientHandler.bind(this));
        this.router.post('/register_trading_client', this.registerTradingClientHandler.bind(this));
        this.router.post('/trading_client_heartbeat', this.tradingClientHeartbeatHandler.bind(this));
    }

    getRouter() {
        return this.router;
    }

    // ================== 路由回调函数实现 ==================
    async testTradingClientHandler(req, res) {
        try {
             const targetClientId = req.body.client_id;
             const clientKey = buildClientKey(targetClientId);
            const clientInfo = this.tradingClients.get(clientKey);
             const requestingUsername = req.user?.username;
             logger.debug(`测试客户端连接请求 (来自用户: ${requestingUsername}): client_id=${targetClientId}`);

             if (clientInfo) {
                 const now = Date.now();
                const heartbeatTimeout = 2 * 60 * 1000;
                 let isActive = false;
                 if (clientInfo.socket) {
                     isActive = clientInfo.socket.connected;
                 } else {
                     isActive = (now - (clientInfo.last_heartbeat || clientInfo.registered_at)) < heartbeatTimeout;
                 }
                 if (isActive) {
                     logger.info(`测试客户端连接成功 (目标: ${clientKey}): 客户端在线`);
                     return res.json({
                         success: true,
                         message: '客户端已连接',
                         data: {
                             connection_type: clientInfo.socket ? 'socket' : 'http',
                             last_heartbeat: clientInfo.last_heartbeat ? new Date(clientInfo.last_heartbeat).toISOString() : null
                         }
                     });
                 }
             }
             logger.warn(`测试客户端连接失败 (目标: ${clientKey}): 客户端未找到或未连接`);
             return res.status(404).json({ success: false, message: '客户端未找到或未连接' });
        } catch (error) {
             logger.error(`测试交易客户端连接处理异常: ${error.message}`);
             return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    }

    testConnectionHandler(req, res) {
        res.json({
            success: true,
            message: 'tradeHandler连接正常',
            timestamp: Date.now()
        });
    }

    async executeTradeHandler(req, res) {
        try {
            const username = req.user?.username;
            // 兼容老payload，优先用client_key，如果没有则直接用channelId
            let client_key = req.body.client_key;
            const { channelId, action, params } = req.body;
            // 修正：只要有channelId就直接用，不再拼接username
            if (!client_key && channelId) {
                client_key = channelId;
            }
            logger.debug(`收到交易执行请求 (来自Token用户: ${username}): client_key=${client_key}, action=${action}`);

            // 新增：严格校验通道类型
            const { getTradingChannels } = require('../liveStrategyHandler');
            const tradingChannels = await getTradingChannels(username);
            const matchedChannel = tradingChannels.find(c => c.id === client_key);
            if (!matchedChannel) {
                logger.warn(`未找到匹配的交易通道: ${client_key}`);
                return res.status(404).json({ success: false, message: '未找到匹配的交易通道' });
            }
            if (matchedChannel.type !== 'easytrader_ths') {
                logger.warn(`通道 ${client_key} 类型为 ${matchedChannel.type}，不支持通过本接口下单`);
                return res.status(400).json({ success: false, message: `通道类型 ${matchedChannel.type} 不支持通过本接口下单` });
            }

            if (!username || !client_key || !action) {
                logger.warn('执行交易请求参数不完整或Token无效');
                return res.status(400).json({ success: false, message: '参数不完整或用户未认证' });
            }
            try {
                await this.executeTradeViaSocket(client_key, action, params);
                 logger.info(`交易指令 '${action}' 已通过 Socket 发送给客户端 '${client_key}'`);
                 return res.json({ success: true, message: '交易指令已发送' });
            } catch (tradeError) {
                 logger.error(`发送交易指令 '${action}' 失败 for '${client_key}': ${tradeError.message}`);
                 if (tradeError.message.includes('未找到') || tradeError.message.includes('未连接')) {
                     return res.status(404).json({ success: false, message: tradeError.message });
                 }
                return res.status(500).json({ success: false, message: tradeError.message || '发送交易指令时发生未知错误' });
            }
        } catch (error) {
             logger.error(`处理 /execute_trade 请求异常: ${error.message}`);
            return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    }

    async removeTradingClientHandler(req, res) {
        try {
            const targetClientId = req.body.client_id;
            const requestingUsername = req.user?.username;
             logger.debug(`收到移除客户端请求 (来自用户: ${requestingUsername}): client_id=${targetClientId}`);
            if (!targetClientId) {
                return res.status(400).json({ success: false, message: '参数不完整' });
            }
            const clientKey = buildClientKey(targetClientId);
            const clientInfo = this.tradingClients.get(clientKey);
            if (!clientInfo) {
                 logger.warn(`移除失败: 客户端 Key '${clientKey}' 不存在`);
                return res.status(404).json({ success: false, message: '客户端不存在' });
            }
            if (clientInfo.socket) {
                 logger.info(`正在断开客户端连接: Key=${clientKey}, SocketID=${clientInfo.socket.id}`);
                 clientInfo.socket.disconnect(true);
            } else {
                this.tradingClients.delete(clientKey);
            }
            logger.info(`交易客户端移除请求已处理: Key=${clientKey}`);
            return res.json({ success: true, message: '移除成功' });
        } catch (error) {
            logger.error(`移除交易客户端失败: ${error.message}`);
            return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    }

    async registerTradingClientHandler(req, res) {
        try {
            const { username, client_ip, client_port, client_type, client_id, timestamp } = req.body;
            logger.info(`收到客户端注册请求: client_id=${client_id}, ip=${client_ip}, port=${client_port}`);
            if (!client_id || !client_ip || !client_port || !timestamp) {
                logger.warn('客户端注册请求参数不完整');
                return res.status(400).json({ success: false, message: '参数不完整' });
            }
            const clientKey = buildClientKey(client_id);
            const existingClient = this.tradingClients.get(clientKey);
            if (existingClient && existingClient.socket && existingClient.socket.connected) {
                logger.warn(`客户端注册失败: 已存在相同的客户端连接 (${clientKey})`);
                return res.status(409).json({ success: false, message: '已存在相同的客户端连接' });
            }
            this.tradingClients.set(clientKey, {
                username,
                client_type,
                client_id,
                client_ip,
                client_port,
                registered_at: Date.now(),
                last_heartbeat: Date.now(),
                socket: null
            });
            logger.info(`客户端注册成功: ${clientKey}, ip=${client_ip}, port=${client_port}`);
            return res.json({ success: true, message: '注册成功' });
        } catch (error) {
            logger.error(`处理客户端注册请求时发生错误: ${error.message}`);
            return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    }

    tradingClientHeartbeatHandler(req, res) {
        try {
            const { client_id, client_ip, client_port, timestamp } = req.body;
            logger.debug(`收到客户端心跳: client_id=${client_id}`);
            if (!client_id || typeof client_id !== 'string' || !client_id.trim()) {
                logger.warn('客户端心跳请求参数不完整或 client_id 无效');
                return res.status(400).json({ success: false, message: '参数不完整或 client_id 无效' });
            }
            const clientKey = buildClientKey(client_id);
            const clientInfo = this.tradingClients.get(clientKey);
            if (!clientInfo) {
                logger.warn(`客户端心跳失败: 客户端 '${clientKey}' 不存在`);
                return res.status(404).json({ success: false, message: '客户端不存在' });
            }
            clientInfo.last_heartbeat = Date.now();
            if (client_ip) clientInfo.client_ip = client_ip;
            if (client_port) clientInfo.client_port = client_port;
            this.tradingClients.set(clientKey, clientInfo);
            logger.debug(`客户端心跳更新成功: ${clientKey}`);
            return res.json({ success: true, message: '心跳更新成功' });
        } catch (error) {
            logger.error(`处理客户端心跳请求时发生错误: ${error.message}`);
            return res.status(500).json({ success: false, message: '服务器内部错误' });
        }
    }

    cleanupExpiredClients() {
    const now = Date.now();
        const heartbeatTimeout = 2 * 60 * 1000;
    logger.debug('开始检查过期客户端 (基于Socket心跳)...');
        this.tradingClients.forEach((client, key) => {
            if (now - (client.last_heartbeat || client.registered_at) > heartbeatTimeout) {
            logger.warn(`客户端 '${key}' 检测为过期 (最后心跳: ${client.last_heartbeat ? new Date(client.last_heartbeat).toISOString() : 'N/A'}), 将断开连接并彻底清理.`);
            if (client.socket) {
                 client.socket.disconnect(true);
            }
            // 无论有无socket，最终都要彻底删除，防止僵尸状态
            this.tradingClients.delete(key);
            }
    });
    logger.debug('过期客户端检查完成');
}

    /**
     * 发送交易指令到指定客户端
     * @param {string} client_id 通道ID（client_id）
     * @param {string} action 交易动作
     * @param {object} params 交易参数
     * @throws {Error} 未找到客户端或未连接
     */
    sendTradeCommand(client_id, action, params) {
        const clientKey = buildClientKey(client_id);
        const clientInfo = this.tradingClients.get(clientKey);
        if (!clientInfo || !clientInfo.socket || !clientInfo.socket.connected) {
            logger.warn(`[sendTradeCommand] 未找到活跃客户端: ${clientKey}`);
            throw new Error(`交易客户端未找到或未连接: ${clientKey}`);
        }
        logger.info(`[sendTradeCommand] 发送交易指令到 ${clientKey}，action=${action}`);
        clientInfo.socket.emit('command', { action, params });
    }

    async executeTradeViaSocket(client_key, action, params) {
        logger.debug(`准备发送交易指令: client_key=${client_key}, action=${action}`);
        const clientInfo = this.tradingClients.get(client_key);
        if (!clientInfo) {
            logger.warn(`发送交易指令失败: 客户端 '${client_key}' 未找到`);
            logger.debug(`[TradeHandler DEBUG] 当前已注册客户端列表: ${Array.from(this.tradingClients.keys()).join(', ')}`);
            throw new Error(`交易客户端未找到: ${client_key}`);
        }
        const now = Date.now();
        const heartbeatTimeout = 2 * 60 * 1000;
        let isActive = false;
        if (clientInfo.socket) {
            isActive = clientInfo.socket.connected;
        } else {
            isActive = (now - (clientInfo.last_heartbeat || clientInfo.registered_at)) < heartbeatTimeout;
        }
        if (!isActive) {
            logger.warn(`发送交易指令失败: 客户端 '${client_key}' 不活跃`);
            throw new Error(`交易客户端不活跃: ${client_key}`);
        }
        // 新实现：直接调用实例方法
        this.sendTradeCommand(client_key, action, params);
        return Promise.resolve();
    }

    isClientOnline(clientKey) {
        const clientInfo = this.tradingClients.get(clientKey);
    return !!(clientInfo && clientInfo.socket && clientInfo.socket.connected);
}

    // SocketIO初始化接口
    initSocketIO(namespace) {
        return initSocketIO(namespace, this.tradingClients);
    }
}

const tradeHandler = new TradeHandler();
// 兼容旧逻辑，直接导出initSocketIO函数
function initSocketIOCompat(namespace) {
    return initSocketIO(namespace, tradeHandler.tradingClients);
}
module.exports = {
    tradeHandler,
    tradingClients: tradeHandler.tradingClients, // 兼容旧代码直接访问
    initSocketIO: initSocketIOCompat, // 新增：兼容旧用法
};