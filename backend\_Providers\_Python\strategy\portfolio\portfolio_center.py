import os
import sys
import json
import yaml
import pandas as pd
from typing import Union, Dict, Any
import datetime
from datetime import timezone, timedelta
import requests
import pytz

# --- 使用相对导入 ---
# sys.path manipulation might still be needed if run as script,
# but relative imports are preferred for package integrity.
import sys

# 引用上级目录，引用同级目录
script_path = os.path.abspath(__file__)
script_dir = os.path.dirname(script_path)
sys.path.insert(0, script_dir)
parent_dir = os.path.join(script_dir, '../../')
sys.path.insert(0, os.path.abspath(parent_dir))  # 必须转绝对路径

# 注释掉原来的CSV准备函数导入，改用ExtDataLoader
from prepare_csv import prepare_csv_files # Use relative import (..) to go up one level
from prepare_csv_cpt import prepare_csv_files_cpt # <<< 新增：导入加密货币处理函数

from wtpy import WtBtEngine,EngineType
from wtpy.apps import WtBtAnalyst
from strategy.portfolio.MultiFactorsCTA import MultiFactorsCTA # Use relative import (.) for same directory
from portfolio_analyzer import analyze_backtest_results

# 更改工作目录为 python1 所在的目录
os.chdir(script_dir)

show_log = True
log_file_handle = None

# 一个print的替代函数，允许开关显示
def print_log(message):
    if show_log:
        beijing_tz = pytz.timezone('Asia/Shanghai')
        now = datetime.datetime.now(beijing_tz)
        prefix = now.strftime("[%Y-%m-%d %H:%M:%S]")
        print(f"{prefix} {message}")

# 写一个函数，传入相对路径，使用script_dir作为根目录，返回绝对路径

# 转化为 set_sel_strategy 能够识别的方式 min, d, w, m
# 返回 单位 和 数量
def convert_period1(period:str) -> tuple[str, int]:
    if period == "1m":
        return "min", 1
    elif period == "5m":
        return "min", 5
    elif period == "15m":
        return "min", 15
    elif period == "30m":
        return "min", 30
    elif period == "1h":
        return "min", 60
    elif period == "1D":
        return "d", 1
    elif period == "1W":
        return "w", 1
    elif period == "1M":
        return "m", 1
    else:
        return period, 1

# 根据策略配置文件（对于回测，commodities.json中一定要有回测的品种，contracts.json无所谓）
# a. common基础文件：品种与合约须手动检查，与runBT.py对应；
# 其他的可以用demo的模板，详解见基础文件详解章节
# b. storage数据：存放历史数据
def do_run_backtest(strategy_id, strategy_yaml = ""):
    """
    运行回测引擎
    @param strategy_id: 策略名称，例如trend_roc_vol_etf
    @return: 回测结果
    """
    print_log(f"[策略回测] 开始回测策略: {strategy_id}")

    # 构建并读取策略配置文件
    if strategy_yaml == "":
        config_path = os.path.join(f"./{strategy_id}/index.yaml")
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"[策略回测] 策略配置文件不存在: {config_path}")

        # 获取策略目录的绝对路径，用于正确解析相对路径
        strategy_dir = os.path.abspath(os.path.join(".", strategy_id))

        with open(config_path, 'r', encoding='utf-8') as f:
            strategy_config = yaml.safe_load(f)
    else:
        strategy_config = yaml.safe_load(strategy_yaml)
        strategy_config['strategy_id'] = strategy_id    # 对于定制策略，strategy_name必须是uuid
        strategy_dir = os.getcwd() # Assuming yaml passed as string runs from current dir

    # 警告：强烈推荐，一个python进程初始化一种类型的引擎，例如针对股票，例如针对ETF，例如针对期权，不要混用 !!!!!!之后的初始化引擎不会起效！
    # 创建一个运行环境，并加入策略
    engine = WtBtEngine(EngineType.ET_CTA)
    engine.clear_cache()

    # 从配置中读取交易类型
    trading_type = strategy_config.get('trading_type', 'etf') # 默认为etf

    # 根据交易类型确定文件名
    if "stock" in trading_type.lower(): # 如果包含stock字样，则引用股票
        comm_file = "stk_comms.json"
        contract_file = "stocks.json"
    # TODO: 合约品种具有特殊性，暂时不做特殊处理
    elif trading_type == "option":
        comm_file = "ifopt_comms.json" # 示例，根据实际期权类型调整
        contract_file = "if_options.json" # 示例，根据实际期权类型调整
    elif "etf" in trading_type.lower():
        comm_file = "stk_comms.json"    # etf也是用股票的类别定义，否则针对股票/etf策略进行回测时，会造成成交量倍数等混乱
        contract_file = "etfs.json"
    elif "crypto" in trading_type.lower():
        comm_file = "cypto_comms.json"
        contract_file = "stocks.json"   # 这个应该没有作用，随便写
    else:
        # 如果类型未知，使用默认或抛出错误
        print_log(f"[策略回测] 警告: 未知的 trading_type '{trading_type}', 将使用默认文件")
        comm_file = "stk_comms.json"
        contract_file = "stocks.json"

    print_log(f"[策略回测] 根据交易类型 '{trading_type}' 加载: commfile='{comm_file}', contractfile='{contract_file}'")

    # 使用动态确定的文件名初始化引擎
    engine.init('../common/', "configbt.yaml", commfile=comm_file, contractfile=contract_file)

    # 从index.yaml中获取回测时间范围
    backtest_start = strategy_config['backtest']['stime']
    # etime 允许为空或不存在，表示运行到最新数据
    backtest_end = strategy_config['backtest'].get('etime', None)
    if backtest_end == "": backtest_end = None # Handle empty string case

    # 先配置存储路径，以便检查数据可用范围
    # 正确解析data.path（相对于策略目录）
    data_path = strategy_config['data']['path']
    if not os.path.isabs(data_path):
        # 将相对路径转为绝对路径（基于策略目录）
        storage_base_path = os.path.normpath(os.path.join(strategy_dir, data_path))
    else:
        storage_base_path = data_path


    # 添加csv子目录
    storage_csv_path = os.path.join(storage_base_path, "csv")

    # 确保csv目录存在
    os.makedirs(storage_csv_path, exist_ok=True)

    print_log(f"[策略回测] 数据文件将保存到: {storage_csv_path}")

    # 调用prepare_csv函数，根据品种数组准备csv文件
    codes = [code for code in strategy_config['universe']]

    # --- 修改：分别调用不同的 prepare 函数 --- 
    # 准备基于通达信的数据 (股票, ETF 等)
    print_log(f"[数据准备] 调用 prepare_csv 处理非加密货币品种...")
    csvLoaded = prepare_csv_files(codes, strategy_config['data_freq'], storage_csv_path)
    # 如果prepare_csv_files失败，尝试从tdxserver获取并保存csv
    if not csvLoaded:
        print_log(f"[数据准备] prepare_csv_files失败，尝试从tdxserver获取K线数据...")
        for code in codes:
            try:
                df = fetch_kline_from_rt(code, strategy_config['data_freq'], count=800, start=0)
                if not df.empty:
                    # 构造csv文件名，保持与prepare_csv_files一致
                    freq_suffix_map = {'day': '_d', '1D': '_d', 'min5': '_m5', '5m': '_m5', 'min1': '_m1', '1m': '_m1'}
                    suffix = freq_suffix_map.get(strategy_config['data_freq'], '_d')
                    csv_file = os.path.join(storage_csv_path, f"{code}{suffix}.csv")
                    # 保证第二列为timestamp
                    cols = list(df.columns)
                    if cols[1] != 'timestamp' and 'timestamp' in cols:
                        cols.remove('timestamp')
                        cols.insert(1, 'timestamp')
                        df = df[cols]
                    df.to_csv(csv_file, index=False, encoding='utf-8')
                    print_log(f"[数据准备] 已从tdxserver获取并保存: {csv_file}")
                else:
                    print_log(f"[数据准备] tdxserver未返回数据: {code}")
            except Exception as e:
                print_log(f"[数据准备] 获取或保存{code}数据失败: {e}")
    # --- 修改结束 ---

    # 检查是否存在加密货币代码，如果存在则调用对应的准备函数
    has_crypto = any(code.startswith("CRYPTO.CPT.") for code in codes)
    if has_crypto:
        print_log(f"[数据准备] 检测到加密货币品种，调用 prepare_csv_cpt 处理...")
        prepare_csv_files_cpt(codes, strategy_config['data_freq'], storage_csv_path)
    else:
        print_log(f"[数据准备] 未检测到加密货币品种，跳过 prepare_csv_cpt 调用。")

    # 配置引擎使用正确的存储路径 (在所有CSV准备好之后配置)
    engine.configBTStorage(mode=strategy_config['data']['mode'], path=storage_base_path)

    # 检查实际数据的可用范围，调整回测开始时间
    bar_count = strategy_config['bar_count']
    data_freq = strategy_config['data_freq']

    # 不再使用临时引擎实例（因为WtBtEngine没有stra_get_bars方法）
    # 改为直接读取CSV文件获取最早日期
    earliest_dates = []
    data_file_info = {}  # 存储每个品种的数据文件信息

    print_log("\n" + "="*80)
    print_log(f"[策略回测] 开始检查数据文件可用时间范围，当前策略周期: {data_freq}")

    overall_latest_date = 0 # 新增：记录所有文件中的最晚日期    

    try:
        # 为每个品种获取数据，检查最早日期
        for code in codes:
            # 获取品种的代码部分（去掉交易所前缀）
            code_parts = code.split('.')
            if len(code_parts) > 1:
                # 动态确定后缀
                freq_suffix_map = {'day': '_d', '1D': '_d', 'min5': '_m5', '5m': '_m5', 'min1': '_m1', '1m': '_m1'}
                suffix = freq_suffix_map.get(data_freq, '_d')
                possible_paths = [
                    os.path.join(storage_csv_path, f"{code}{suffix}.csv"),  # 完整代码+周期后缀
                    os.path.join(storage_csv_path, f"{code}.csv"),  # 完整代码
                ]

                print_log(f"[策略回测] 正在查找{code}的数据文件")
                print_log(f"[策略回测] 查找路径: {possible_paths}")

                csv_file = None
                for path in possible_paths:
                    if os.path.exists(path):
                        csv_file = path
                        print_log(f"[策略回测] 找到数据文件: {path}")
                        break

                if csv_file is not None:
                    # 读取CSV文件并获取最早日期
                    try:
                        df = pd.read_csv(csv_file)
                        print_log(f"[策略回测] 成功读取文件，数据形状：{df.shape}")
                        print_log(f"[策略回测] CSV列名：{list(df.columns)}")
                        print_log(f"[策略回测] 前3行数据示例：\n{df.head(3)}")

                        # 检查CSV文件格式并处理时间数据
                        if 'date' in df.columns and not df.empty:
                            print_log(f"[策略回测] 检测到Wonder Trader标准格式，使用date字段")
                            
                            # 转换date字段为日期整数的函数
                            def parse_date_to_int(date_str):
                                """将yyyy/mm/dd格式转换为YYYYMMDD整数"""
                                try:
                                    # 处理可能的格式：yyyy/mm/dd 或 yyyy-mm-dd
                                    if '/' in date_str:
                                        parts = date_str.split('/')
                                    elif '-' in date_str:
                                        parts = date_str.split('-')
                                    else:
                                        raise ValueError(f"无法识别的日期格式: {date_str}")
                                    
                                    year, month, day = parts
                                    return int(f"{year}{month.zfill(2)}{day.zfill(2)}")
                                except Exception as e:
                                    print_log(f"[策略回测] 日期解析错误: {date_str} -> {e}")
                                    return None

                            # 获取最早和最晚日期
                            first_date_str = str(df['date'].iloc[0])
                            last_date_str = str(df['date'].iloc[-1])
                            
                            earliest_date = parse_date_to_int(first_date_str)
                            latest_date = parse_date_to_int(last_date_str)
                            
                            if earliest_date is None or latest_date is None:
                                print_log(f"[策略回测] 错误：无法解析日期格式")
                                continue

                            # 更新全局最晚日期
                            if latest_date > overall_latest_date:
                                overall_latest_date = latest_date

                            print_log(f"[策略回测] {code} 数据时间范围：")
                            print_log(f"  - 最早日期：{earliest_date} ({first_date_str})")
                            print_log(f"  - 最晚日期：{latest_date} ({last_date_str})")
                            print_log(f"  - 总记录数：{len(df)}条K线")
                            
                            # 检查是否有time字段（日内数据）
                            if 'time' in df.columns:
                                print_log(f"  - 数据类型：日内数据（包含time字段）")
                            else:
                                print_log(f"  - 数据类型：日线及以上数据（仅date字段）")

                            # 考虑技术指标所需的历史数据
                            if len(df) > bar_count:
                                # 跳过bar_count个K线后的日期
                                safe_date_str = str(df['date'].iloc[bar_count])
                                safe_start_date = parse_date_to_int(safe_date_str)
                                if safe_start_date is not None:
                                    print_log(f"  - 安全起始日期：{safe_start_date} (跳过{bar_count}根K线)")
                                else:
                                    safe_start_date = latest_date
                                    print_log(f"[策略回测] 警告：无法解析安全起始日期，使用最晚日期")
                            else:
                                safe_start_date = latest_date
                                print_log(f"[策略回测] 警告：数据量不足（仅{len(df)}根K线），不足{bar_count}根！")

                            earliest_dates.append(safe_start_date)

                            # 存储数据文件信息
                            data_file_info[code] = {
                                'file_path': csv_file,
                                'earliest_date': earliest_date,
                                'latest_date': latest_date,
                                'safe_start_date': safe_start_date,
                                'total_bars': len(df)
                            }
                        elif 'time' in df.columns and not df.empty:
                            # 兼容旧格式：Unix时间戳
                            print_log(f"[策略回测] 检测到旧格式，使用time字段（Unix时间戳）")
                            
                            # 获取最早和最晚时间戳
                            earliest_timestamp = df['time'].iloc[0]
                            latest_timestamp = df['time'].iloc[-1]

                            # 转换Unix时间戳为日期字符串 (中国时间 UTC+8)
                            def timestamp_to_date(ts):
                                # 转换为datetime对象，考虑中国时区
                                dt = datetime.datetime.fromtimestamp(ts, tz=timezone(timedelta(hours=8)))
                                return dt.strftime('%Y%m%d')

                            earliest_date_str = timestamp_to_date(earliest_timestamp)
                            latest_date_str = timestamp_to_date(latest_timestamp)

                            # 转换为整数
                            earliest_date = int(earliest_date_str)
                            latest_date = int(latest_date_str)

                            # 更新全局最晚日期
                            if latest_date > overall_latest_date:
                                overall_latest_date = latest_date

                            print_log(f"[策略回测] {code} 数据时间范围：")
                            print_log(f"  - 最早日期：{earliest_date} ({earliest_timestamp})")
                            print_log(f"  - 最晚日期：{latest_date} ({latest_timestamp})")
                            print_log(f"  - 总记录数：{len(df)}条K线")

                            # 考虑技术指标所需的历史数据
                            if len(df) > bar_count:
                                # 跳过bar_count个K线后的时间戳
                                safe_timestamp = df['time'].iloc[bar_count]
                                safe_date_str = timestamp_to_date(safe_timestamp)
                                safe_start_date = int(safe_date_str)
                                print_log(f"  - 安全起始日期：{safe_start_date} (跳过{bar_count}根K线)")
                            else:
                                safe_start_date = latest_date
                                print_log(f"[策略回测] 警告：数据量不足（仅{len(df)}根K线），不足{bar_count}根！")

                            earliest_dates.append(safe_start_date)

                            # 存储数据文件信息
                            data_file_info[code] = {
                                'file_path': csv_file,
                                'earliest_date': earliest_date,
                                'latest_date': latest_date,
                                'safe_start_date': safe_start_date,
                                'total_bars': len(df)
                            }
                        else:
                            print_log(f"[策略回测] 错误：数据文件未包含'date'或'time'列，或文件为空")
                            if not df.empty:
                                print_log(f"[策略回测] 实际列名：{list(df.columns)}")
                                print_log(f"[策略回测] 期望格式：包含'date'字段（yyyy/mm/dd）或'time'字段（Unix时间戳）")
                    except Exception as e:
                        print_log(f"[策略回测] 读取{code}的CSV文件时出错: {str(e)}")
                        import traceback
                        print_log(traceback.format_exc())
                else:
                    print_log(f"[策略回测] 警告: 未找到{code}的数据文件")
                    print_log(f"  - 尝试过的路径: {possible_paths}")
            else:
                print_log(f"[策略回测] 无法解析{code}的交易所和代码部分")
    except Exception as e:
        print_log(f"[策略回测] 检查数据可用范围时出错: {e}")

    print_log(f"[策略回测] 数据检查完成")
    print_log("="*80 + "\n")

    # 确定实际回测起始日期
    actual_start = backtest_start
    if earliest_dates:
        # 将所有日期转为整数便于比较
        actual_start_int = int(str(backtest_start)[:8])  # 取YYYYMMDD部分
        safe_earliest = max(earliest_dates)

        if safe_earliest > actual_start_int:
            # 实际数据起始日期晚于配置的起始日期，使用更晚的日期
            # 将YYYYMMDD转回原始格式（保留时分秒部分，如果有）
            suffix = str(backtest_start)[8:] if len(str(backtest_start)) > 8 else ""
            actual_start = int(str(safe_earliest) + suffix)

            # 增强的日志输出，突出时间调整信息
            print_log("\n" + "="*80)
            print_log(f"[策略回测] !!! 警告：回测起始时间已调整 !!!")
            print_log(f"[策略回测] 原始配置的起始日期: {backtest_start}")
            print_log(f"[策略回测] 调整后的起始日期: {actual_start}")
            print_log(f"[策略回测] 调整原因: 确保有足够的历史数据用于指标计算")
            print_log(f"[策略回测] 技术指标所需历史K线数量: {bar_count}")

            # 输出各品种的最早可用日期信息
            print_log("[策略回测] 各品种最早可用日期(考虑barcount后):")
            for i, code in enumerate(codes):
                if i < len(earliest_dates):
                    print_log(f"[策略回测]   - {code}: {earliest_dates[i]}")
            print_log("="*80 + "\n")

    # --- 确定最终的回测结束时间 ---
    actual_end = 0
    config_end = strategy_config['backtest'].get('etime', None)

    # 获取所有数据文件中的最晚日期
    data_latest_date = 0
    if data_file_info:
        for code, info in data_file_info.items():
            if 'latest_date' in info and info['latest_date'] > data_latest_date:
                data_latest_date = info['latest_date']
        print_log(f"[策略回测] 所有数据文件中的最晚日期: {data_latest_date}")

    if config_end and str(config_end).strip() != "":
        # 如果配置了 etime，使用配置的值
        try:
            actual_end = int(config_end)
            print_log(f"[策略回测] 使用配置文件中的结束时间: {actual_end}")

            # 检查配置的结束时间是否超过了数据的最晚日期
            if data_latest_date > 0:
                config_end_date = int(str(actual_end)[:8])  # 取YYYYMMDD部分
                if config_end_date > data_latest_date:
                    print_log(f"[策略回测] 警告: 配置的结束时间 {config_end_date} 超过了可用数据的最晚日期 {data_latest_date}")
                    print_log(f"[策略回测] 回测将在数据结束时停止，无法继续到配置的结束时间")
        except ValueError:
             print_log(f"[策略回测] 警告: 配置文件中的 etime '{config_end}' 格式无效，将尝试使用数据最晚日期。")
             config_end = None # 标记为无效，以便后续使用数据最晚日期

    if actual_end == 0: # 如果配置为空或无效
        # 如果有数据最晚日期，优先使用数据最晚日期
        if data_latest_date > 0:
            # 将YYYYMMDD转为YYYYMMDDHHMM格式
            actual_end = data_latest_date * 10000 + 1500  # 加上15:00作为当天结束时间
            print_log(f"[策略回测] 未配置结束时间，使用数据最晚日期作为结束时间: {actual_end}")
        else:
            # 直接获取当前时间并格式化
            try:
                now = datetime.datetime.now()
                # 格式化为 YYYYMMDDHHMM 整数
                actual_end = int(now.strftime("%Y%m%d%H%M"))
                print_log(f"[策略回测] 未配置结束时间且无法确定数据最晚日期，使用当前系统时间作为结束时间: {actual_end}")
            except Exception as e:
                 print_log(f"[策略回测] 错误: 获取或格式化当前系统时间失败: {e}")
                 raise ValueError("无法设置回测结束时间为当前时间") from e

    # 使用调整后的起始日期和确定的结束日期重新配置回测
    print_log(f"[策略回测] 最终回测时间范围: {actual_start} - {actual_end}")


    # 配置回测时间
    engine.configBacktest(actual_start, actual_end)
    engine.commitBTConfig()

    # 创建策略实例
    straInfo = MultiFactorsCTA(
        name=strategy_id,
        codes=codes,
        barCnt=strategy_config['bar_count'],
        period=strategy_config['data_freq'],
        # 新增参数传递
        order_by_config=strategy_config.get('order_by', {}),
        buy_rules_config=strategy_config.get('buy_rules', {}),
        sell_rules_config=strategy_config.get('sell_rules', {}),
        top_n=strategy_config.get('top_n', 1),
        weighting_scheme=strategy_config.get('weighting_scheme', 'equal'),
        rebalance_interval=strategy_config.get('rebalance_interval', 'daily')
    )

    # 使用data_freq决定数据级别，而不是基于调仓周期
    period = strategy_config['data_freq']  # 直接使用配置中的数据频率

    # @strategy   策略对象
    # @date       日期,根据周期变化,每日为0,每周为0~6,对应周日到周六, c每月为1~31,每年为0101~1231
    # @time       时间,精确到分钟
    # @period	    时间周期,可以是分钟min、天d、周w、月m、年y
    # @trdtpl     交易日历模板,默认为CHINA
    # @session    交易时间模板,默认为TRADING
    # @slippage   滑点大小
    # @isRatioSlp 滑点是否是比例, 默认为False, 如果为True, 则slippage为万分比
    # 策略设置应用
    period_unit, period_count = convert_period1(period)

    # 设置策略
    engine.set_cta_strategy(straInfo)

    print_log(f"[策略回测 Caller] 开始回测 ...........")
    engine.run_backtest()

    # 释放回测资源
    engine.release_backtest()
    print_log(f"[策略回测] 回测资源已释放: {strategy_id}")

    # 尝试显式删除对象并触发垃圾回收
    try:
        del engine
        import gc
        gc.collect()
        print_log(f"[策略回测] 引擎对象已尝试删除并执行垃圾回收: {strategy_id}")
    except NameError:
        print_log(f"[策略回测] 引擎对象 'engine' 可能已不存在，无需删除。")

    # 返回策略品种品类和是否成功
    result = {
        "trading_type": trading_type,
        "success": True
    }

        # 添加数据可用性信息
    if data_file_info:
        data_latest_date = 0
        for code, info in data_file_info.items():
            if 'latest_date' in info and info['latest_date'] > data_latest_date:
                data_latest_date = info['latest_date']

        if data_latest_date > 0:
            result["data_latest_date"] = data_latest_date

            # 检查配置的结束时间是否超过了数据的最晚日期
            if actual_end > 0:
                config_end_date = int(str(actual_end)[:8])  # 取YYYYMMDD部分
                if config_end_date > data_latest_date:
                    result["data_limited"] = True
                    result["data_end_reason"] = f"回测在数据结束时停止，最后一个交易日: {data_latest_date}"
                    print_log(f"[策略回测] 警告: 回测在数据结束时停止，最后一个交易日: {data_latest_date}")

    return result



def run_backtest(strategy_id, strategy_yaml = "", output_dir="./outputs_bt") -> dict:
    import sys
    # portfolio_center.py 中添加
    print_log(f"[数据加载] 当前工作目录: {os.getcwd()}")

    global log_file_handle
    log_file_handle = None
    strategy_output_dir = os.path.join(output_dir, strategy_id)
    os.makedirs(strategy_output_dir, exist_ok=True)
    log_path = os.path.join(strategy_output_dir, "backtest.log")
    try:
        log_file_handle = open(log_path, "a", encoding="utf-8")
    except Exception as e:
        print_log(f"[日志警告] 无法打开日志文件: {log_path}, 错误: {e}")
        log_file_handle = None

    # ========== 新增：重定向标准输出到日志文件 ==========
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    try:
        if log_file_handle is not None:
            sys.stdout = log_file_handle
            sys.stderr = log_file_handle
    except Exception:
        pass
    # ========== END ==========

    try:
        # 执行回测
        # 不再接收 initial_capital
        result = do_run_backtest(strategy_id, strategy_yaml)

        print_log(f"[回测结果] 回测返回的结果: {result}")

        # 检查回测结果是否包含必要的信息
        if not isinstance(result, dict) or 'success' not in result or not result.get('success', False):
            error_msg = result.get('error', '未知错误') if isinstance(result, dict) else '回测结果格式不正确'
            print_log(f"[回测错误] 回测执行失败: {error_msg}")
            return {"success": False, "error": error_msg}

        # 构造分析需要的完整输出目录路径
        strategy_output_dir = os.path.join(output_dir, strategy_id)

        # 检查输出目录是否存在
        if not os.path.exists(strategy_output_dir):
            print_log(f"[回测警告] 输出目录不存在: {strategy_output_dir}，尝试创建...")
            try:
                os.makedirs(strategy_output_dir, exist_ok=True)
            except Exception as e:
                print_log(f"[回测错误] 创建输出目录失败: {e}")
                return {"success": False, "error": f"创建输出目录失败: {e}"}

        # 检查是否有回测结果文件
        result_files = [f for f in os.listdir(strategy_output_dir) if f.endswith('.json') or f.endswith('.csv')]
        if not result_files:
            print_log(f"[回测警告] 输出目录中没有找到回测结果文件: {strategy_output_dir}")
            print_log(f"[回测警告] 这可能是因为回测提前结束，例如因为数据不足")
            return {
                "success": False,
                "error": "回测未生成结果文件，可能是因为数据不足或其他原因导致回测提前结束",
                "trading_type": result.get('trading_type', 'unknown'),
                "strategy_id": strategy_id,
                "output_directory": strategy_output_dir
            }

        # 调用分析模块获取结果
        print_log(f"[回测分析] 开始分析回测结果，输出目录: {strategy_output_dir}")
        try:
            analysis_result = analyze_backtest_results(strategy_output_dir, result['trading_type'])

            # 处理分析结果
            if "error" in analysis_result:
                print_log(f"[回测分析] 分析失败: {analysis_result['error']}")
                return {"success": False, "error": f"回测分析失败: {analysis_result['error']}"}

            # 补充基础信息到返回结果 (移除 initial_capital)
            analysis_result.update({
                "output_directory": strategy_output_dir,
                "strategy_id": strategy_id,
                "success": True
            })

            print_log(f"[回测完成] 回测和分析成功完成: {strategy_id}")
            return analysis_result

        except Exception as e:
            print_log(f"[回测分析] 分析过程中发生异常: {e}")
            import traceback
            print_log(traceback.format_exc())
            return {"success": False, "error": f"回测分析过程中发生异常: {e}"}

    except Exception as e:
        print_log(f"[回测错误] 回测执行过程中发生异常: {e}")
        import traceback
        print_log(traceback.format_exc())
        return {"success": False, "error": f"回测执行过程中发生异常: {e}"}
    finally:
        # 恢复标准输出
        try:
            sys.stdout = old_stdout
            sys.stderr = old_stderr
        except Exception:
            pass
        if log_file_handle is not None:
            try:
                log_file_handle.close()
            except Exception:
                pass
            log_file_handle = None


def find_backend_dir(start_dir, max_depth=10):
    """
    向上查找backend目录
    Args:
        start_dir: 开始查找的目录
        max_depth: 最大查找深度，默认10层
    Returns:
        str: backend目录的绝对路径，如果找不到返回None
    """
    current = start_dir
    for _ in range(max_depth):
        if os.path.basename(current) == 'backend':
            return current
        parent = os.path.dirname(current)
        if parent == current:
            break
        current = parent
    return None


def fetch_kline_from_rt(symbol: str, period: str = '1m', count: int = 800, start: int = 0, tdxserver_url: str = '') -> pd.DataFrame:
    """
    从 tdxserver 获取指定品种、周期的K线数据。
    参数：
        symbol: 形如 'SSE.ETF.510500' 或 'SZSE.STOCK.000001'
        period: '1m', '5m', 'day' 等
        count: 返回K线数量
        start: 起始位置
        tdxserver_url: tdxserver服务地址（如未指定则自动查找config.json）
    返回：
        DataFrame，第二列为 timestamp
    """
    # 自动查找tdxserver_url
    if tdxserver_url == '':
        # 查找backend目录
        start_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = find_backend_dir(start_dir)
        tdxserver_url = 'http://127.0.0.1:5003'  # 默认
        try:
            if backend_dir:
                config_path = os.path.join(backend_dir, 'config.json')
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        tdx_cfg = config.get('tdx_service', {})
                        host = tdx_cfg.get('host', '127.0.0.1')
                        port = tdx_cfg.get('port', 5003)
                        tdxserver_url = f"http://{host}:{port}"
        except Exception as e:
            print_log(f"[行情获取] 读取config.json失败，使用默认tdxserver_url: {e}")

    # 解析 symbol，支持 'SSE.ETF.510500' 或 'SZSE.STOCK.000001' 等格式
    parts = symbol.split('.')
    if len(parts) < 3:
        raise ValueError(f"symbol格式不正确: {symbol}")
    market_map = {'SSE': 'sh', 'SZSE': 'sz', 'SH': 'sh', 'SZ': 'sz', 'HK': 'hk', 'US': 'us'}
    market = market_map.get(parts[0].upper(), parts[0].lower())
    code = parts[2]

    params = {
        'market': market,
        'code': code,
        'period': period,
        'count': count,
        'start': start
    }
    url = f"{tdxserver_url}/kline"
    try:
        resp = requests.get(url, params=params, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        if 'error' in data:
            raise RuntimeError(f"tdxserver返回错误: {data['error']}")
        # 兼容返回为list或dict的情况
        kline_list = data if isinstance(data, list) else data.get('data', [])
        if not kline_list:
            raise RuntimeError(f"tdxserver未返回K线数据: {data}")
        df = pd.DataFrame(kline_list)
        # 如果有'datetime'字段且格式为'yyyy-mm-dd hh:mm:ss'，则拆分为'date'和'time'字段
        if 'datetime' in df.columns:
            # 检查前几行是否为'yyyy-mm-dd hh:mm:ss'格式
            sample = df['datetime'].astype(str).head(3)
            if all((' ' in s and len(s.split(' ')) == 2) for s in sample):
                df['date'] = df['datetime'].apply(lambda x: str(x).split(' ')[0])
                df['time'] = df['datetime'].apply(lambda x: str(x).split(' ')[1])
        return df
    except Exception as e:
        print_log(f"[行情获取] 从tdxserver获取K线失败: {e}")
        return pd.DataFrame()


if __name__ == "__main__":
    # 测试运行，使用命令行参数指定策略名称
    strategy_id = sys.argv[1] if len(sys.argv) > 1 else "trend_roc_vol_etf"

    # 执行回测 (不再接收返回的 capital)
    do_run_backtest(strategy_id)
    print_log(f"[策略回测] 回测完成: {strategy_id}")

    # 将回测的输出数据目录传递给绩效分析模块
    analyst = WtBtAnalyst()

    # 为 Analyst 读取初始资金 (从配置文件)
    config_path = os.path.join(f"./{strategy_id}/index.yaml")
    the_init_capital = 1000000 # Default if config fails
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                strategy_config = yaml.safe_load(f)
                the_init_capital = strategy_config['backtest']['initial_capital']
        except Exception as e:
            print_log(f"[策略回测] 无法从配置 {config_path} 读取初始资金用于分析: {e}")
    else:
        print_log(f"[策略回测] 配置文件 {config_path} 不存在，为分析器使用默认初始资金 {the_init_capital}")

    analyst.add_strategy(strategy_id, folder="./outputs_bt/",
                        init_capital=the_init_capital,
                        rf=0.02, annual_trading_days=240)
    # 运行绩效模块
    analyst.run_new()