1、每一个实盘/组合创建一个目录
2、所有实盘项目共用一个数据中心
3、自定义品种订阅。运行实盘项目时，通过定制接口在数据中心订阅品种，数据中心获取到新的tick时，发送UDP广播。所有实盘项目的tdparser 配置为 UDP 广播接收。
4、


执行过程描述：

数据中心，接收tick，在每一分钟、5分钟，日线k线关闭时，写入。
所以，需要一个数据中心loader，接受客户订阅，根据订阅更新数据中心的 contracts.json，然后重启 runDT.py。
所以，全局仅有一个runDT.py，通过重启来更新订阅。而这个runDT需要一个辅助helper，用来接收订阅，以及长期runDT.py
把这个helper内置在strategy_server.py里面。

实盘项目通过配置tdparser接收UDP广播，内部通过 parepare_bars 登记需要的品种
实盘项目通过配置 traders 进行交易通道的设置

