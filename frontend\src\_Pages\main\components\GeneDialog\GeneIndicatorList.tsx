import React from 'react';
import { Card, List, Typography } from 'antd';
import VirtualList from 'rc-virtual-list';
import { useAtom } from 'jotai';
import { selectedIndicatorAtom as selectedSignalAtom } from '../../models/geneState';
import { SIGNAL_DEFINITIONS } from '@/_Modules/Signal';

const { Text } = Typography;
const ContainerHeight = 530;

/**
 * 基因指标列表组件
 * 显示所有可用的信号
 */
const SignalList: React.FC = () => {
    const [selectedSignal, setSelectedSignal] = useAtom(selectedSignalAtom);

    // 将信号转换为统一格式
    const signals = SIGNAL_DEFINITIONS.map((signal, index) => ({
        id: index + 1,  // 使用索引作为id
        name: signal.name,
        description: signal.description,
        type: signal.name.includes('Future') ? 'future' as const : 'past' as const,
        signalClassName: signal.signalClassName  // 添加信号类名
    }));

    // 按类型分组
    const allSignals = [
        ...(signals.filter(item => item.type === 'past')),
        ...(signals.filter(item => item.type === 'future'))
    ];

    return (
        <Card 
            title="信号列表" 
            size="small"
            bodyStyle={{ 
                padding: 0,
                height: ContainerHeight
            }}
            style={{ height: '100%' }}
        >
            <List>
                <VirtualList
                    data={allSignals}
                    height={ContainerHeight}
                    itemHeight={72}  // 预估每个项的高度
                    itemKey="id"
                >
                    {(item) => (
                        <List.Item
                            key={item.id}
                            onClick={() => setSelectedSignal(item)}
                            style={{ 
                                cursor: 'pointer',
                                backgroundColor: selectedSignal?.id === item.id ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
                                padding: '12px',
                                borderRadius: '4px',
                                margin: '4px 8px'
                            }}
                        >
                            <div style={{ width: '100%' }}>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Text strong>{item.name}</Text>
                                    <Text type="secondary" style={{ fontSize: '12px' }}>
                                        {item.type === 'future' ? '未来指标' : '技术指标'}
                                    </Text>
                                </div>
                                {item.description && (
                                    <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: '4px' }}>
                                        {item.description}
                                    </Text>
                                )}
                            </div>
                        </List.Item>
                    )}
                </VirtualList>
            </List>
        </Card>
    );
};

export default SignalList;