import sqlite3
import json
from collections import defaultdict
import os

# --- Configuration ---
# Adjust this path if your database file is located elsewhere
db_path = os.path.join(os.path.dirname(__file__), '../database/database.sqlite') 
# Use the exact table names from your database
browse_history_table = 'browse_history'
pools_table = 'pools'
users_table = 'users' # Needed to check if user exists (optional but good practice)
# --- End Configuration ---

def migrate_browse_history():
    """
    Migrates data from browse_history table to pools table.
    Groups history by userId and creates one '浏览历史' pool per user.
    """
    conn = None
    print(f"开始迁移浏览历史数据从 {browse_history_table} 到 {pools_table}...")
    print(f"数据库路径: {os.path.abspath(db_path)}")

    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row # Access columns by name
        cursor = conn.cursor()

        # --- Step 1: Fetch all browse history data ---
        print("正在从 browse_history 表中获取数据...")
        cursor.execute(f"""
            SELECT
                userId, market, exchange, code, name, interval, viewedAt
            FROM {browse_history_table}
            ORDER BY userId, viewedAt DESC -- Order by user and time (desc for recent first)
        """)
        history_rows = cursor.fetchall()
        print(f"获取到 {len(history_rows)} 条浏览历史记录。")

        if not history_rows:
            print("没有需要迁移的浏览历史数据。")
            return

        # --- Step 2: Group history data by userId and transform ---
        print("正在按 userId 分组并转换数据结构...")
        user_histories = defaultdict(list)

        for row in history_rows:
            try:
                # Construct the new symbol format
                new_symbol_code = f"{row['market']}.{row['exchange']}.{row['code']}"

                # Create the JSON element for the array
                history_item = {
                    "symbol": new_symbol_code,
                    "name": row['name'],
                    "interval": row['interval'],
                    "viewedAt": row['viewedAt'] # Keep the original timestamp
                    # "notes": None # Add notes field if needed in the future
                }
                user_histories[row['userId']].append(history_item)
            except Exception as e:
                print(f"  [警告] 处理 userId={row['userId']} 的记录时出错: {e} - 跳过此条记录")
                print(f"      问题数据: market={row['market']}, exchange={row['exchange']}, code={row['code']}")


        print(f"数据已按 {len(user_histories)} 个用户分组。")

        # --- Step 3: Insert grouped data into pools table ---
        print(f"正在将分组数据插入 {pools_table} 表...")
        inserted_count = 0
        skipped_count = 0

        # Optional: Check if user exists before inserting
        cursor.execute(f"SELECT id FROM {users_table}")
        existing_user_ids = {row['id'] for row in cursor.fetchall()}
        print(f"从 {users_table} 表获取到 {len(existing_user_ids)} 个用户ID。")


        for user_id, history_list in user_histories.items():
            if not history_list: # Skip if no valid items were processed for the user
                skipped_count += 1
                continue

            # Optional: Skip if user no longer exists
            if user_id not in existing_user_ids:
                print(f"  [跳过] 用户 ID {user_id} 在 {users_table} 表中不存在。")
                skipped_count += 1
                continue
                
            # Convert the list of history items to a JSON string
            symbols_json_str = json.dumps(history_list, ensure_ascii=False) # ensure_ascii=False for Chinese characters

            try:
                # Insert a new record into the pools table for this user
                # pool_id is auto-incrementing
                cursor.execute(f"""
                    INSERT INTO {pools_table} (user_id, name, is_public, symbols_json)
                    VALUES (?, ?, ?, ?)
                """, (user_id, '浏览历史', 0, symbols_json_str)) # 0 represents false for is_public
                inserted_count += 1
                if inserted_count % 100 == 0:
                     print(f"  已插入 {inserted_count} 个用户的浏览历史池...")

            except sqlite3.IntegrityError as e:
                 # Handle potential unique constraint violations if a '浏览历史' pool already exists for this user
                 print(f"  [警告] 插入 userId={user_id} 的 '浏览历史' 池失败 (可能已存在): {e}")
                 skipped_count +=1
            except Exception as e:
                 print(f"  [错误] 插入 userId={user_id} 的数据时发生未知错误: {e}")
                 skipped_count += 1


        # --- Step 4: Commit changes ---
        conn.commit()
        print(f"\n迁移完成！")
        print(f"  成功为 {inserted_count} 个用户创建了 '浏览历史' 池。")
        print(f"  跳过了 {skipped_count} 个用户 (不存在或插入时出错)。")


    except sqlite3.Error as e:
        print(f"\n数据库操作失败: {e}")
        if conn:
            conn.rollback() # Roll back changes on error
    except FileNotFoundError:
        print(f"\n错误：数据库文件未找到于: {os.path.abspath(db_path)}")
    except Exception as e:
        print(f"\n发生意外错误: {e}")
    finally:
        if conn:
            conn.close()
            print("数据库连接已关闭。")

# --- Run the migration ---
if __name__ == "__main__":
    # Add confirmation step
    confirm = input(f"即将迁移 {browse_history_table} 数据到 {pools_table} 表，为每个用户创建一个 '浏览历史' 池。\n此操作通常是不可逆的。\n请输入 'yes' 确认执行: ")
    if confirm.lower() == 'yes':
        migrate_browse_history()
    else:
        print("操作已取消。")