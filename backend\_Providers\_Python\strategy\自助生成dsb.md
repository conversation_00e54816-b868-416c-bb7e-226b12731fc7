自主生成dsb
csv格式的数据，在实盘框架下就不支持了。为什么呢？一方面如上面提到的，csv解析速度太慢了；另一方面，实盘框架下的数据，要配合datakit落地，收盘作业的时候会把当日最新的数据和历史数据进行合并，如果使用csv格式，合并起来就非常不方便。
鉴于这种情况，WonderTrader底层提供了一个WtDtHelper模块，用于将数据转换为dsb格式。可以参考wtpy/demos/test_datahelper

from wtpy.wrapper import WtDataHelper
from wtpy.WtCoreDefs import WTSBarStruct, WTSTickStruct
from ctypes import POINTER
import pandas as pd

dtHelper = WtDataHelper()

def test_store_bars():
    df = pd.read_csv('../storage/csv/CFFEX.IF.HOT_m5.csv')

    # ......

    BUFFER = WTSBarStruct*len(df)
    buffer = BUFFER()

    # ......

    dtHelper.store_bars(barFile="./CFFEX.IF.HOT_m5.bin", 
                            firstBar=buffer, count=len(df), period="m5")

def test_store_ticks():

    df = pd.read_csv('./storage/csv/rb主力连续_20201030.csv')
    BUFFER = WTSTickStruct*len(df)
    buffer = BUFFER()

    tags = ["一","二","三","四","五"]

    for i in range(len(df)):
        curTick = buffer[i]

        curTick.exchg = b"SHFE"
        curTick.code = b"SHFE.rb.HOT"

        curTick.price = float(df[i]["最新价"])
        curTick.open = float(df[i]["今开盘"])
        curTick.high = float(df[i]["最高价"])
        curTick.low = float(df[i]["最低价"])
        curTick.settle = float(df[i]["本次结算价"])

        # ......

    dtHelper.store_ticks(tickFile="./SHFE.rb.HOT_ticks.dsb", 
                            firstTick=buffer, count=len(df))
dsb数据生成好了以后，就可以和datakit配合使用，这样数据就可以滚动更新起来，一个实盘环境的数据准备工作就全部做好了，策略也就可以放心运行了。

三方数据接入
上文介绍了如何将外部数据转储为WonderTrader支持的快速读取数据格式dsb，那么到底哪些第三方数据可以方便的进行转储呢？

wtpy.datahelper
为了方便用户处理历史数据，wtpy中内置了一个datahelper模块，该模块以工厂模式的方式,分别封装了tushare、baostock和rqdata三个不同的数据API，用户可以很方便的就从三个不同的平台拉取到相应的数。可以参考wtpy/demos/test_datafactory

from wtpy.apps.datahelper import DHFactory as DHF

hlper = DHF.createHelper("baostock")
hlper.auth()

# tushare
# hlper = DHF.createHelper("tushare")
# hlper.auth(**{"token":"xxxxxxxxxxx", "use_pro":True})

# rqdata
# hlper = DHF.createHelper("rqdata")
# hlper.auth(**{"username":"00000000", "password":"0000000"})

# 落地股票列表
# hlper.dmpCodeListToFile("stocks.json")

# 下载K线数据
# hlper.dmpBarsToFile(folder='./', codes=["CFFEX.IF.HOT","CFFEX.IC.HOT"], period='min1')
# hlper.dmpBarsToFile(folder='./', codes=["CFFEX.IF.HOT","CFFEX.IC.HOT"], period='min5')
hlper.dmpBarsToFile(folder='./', 
    codes=["SZSE.399005","SZSE.399006","SZSE.399303"], period='day')

# 下载复权因子
# hlper.dmpAdjFactorsToFile(
#    codes=["SSE.600000",'SZSE.000001'], filename="./adjfactors.json")
ExtDataLoader
wtpy.datahelper可以方便的从不同的平台，将相应的数据拉取下来，并存成csv。但是我们在实际的场景中，会希望系统能够根据需要自动拉取数据，而不是预先将可能用到的数据初始化好（因为可能会出现覆盖不全的情况）。而ExtDataLoader就是为了更好的解决这个问题！
ExtDataLoader会自动的根据底层的需要，从自定义的数据源拉取数据，然后直接传递给底层，底层再根据需要进行处理成相应的周期提供个策略使用。ExtDataLoader是wtpy中的一个模块，用户在使用的时候只需要按照接口实现相应的读取数据的逻辑即可。可以参考wtpy/demos/test_dataexts

from wtpy.ExtModuleDefs import BaseExtDataLoader
from ctypes import POINTER
from wtpy.WtCoreDefs import WTSBarStruct, WTSTickStruct

from wtpy import WtEngine,WtBtEngine,EngineType
from wtpy.apps import WtBtAnalyst
from Strategies.DualThrust import StraDualThrust

import pandas as pd
import random

class MyDataLoader(BaseExtDataLoader):
    
    def load_final_his_bars(self, stdCode:str, period:str, feeder) -> bool:
        '''
        加载历史K线（回测、实盘）
        @stdCode    合约代码，格式如CFFEX.IF.2106
        @period     周期，m1/m5/d1
        @feeder     回调函数，feed_raw_bars(bars:POINTER(WTSBarStruct), count:int, factor:double)
        '''
        print("loading %s bars of %s from extended loader" % (period, stdCode))

        df = pd.read_csv('../storage/csv/CFFEX.IF.HOT_m5.csv')
        
        # ......

        BUFFER = WTSBarStruct*len(df)
        buffer = BUFFER()

        def assign(procession, buffer):
            tuple(map(lambda x: setattr(buffer[x[0]], procession.name, x[1]), 
                enumerate(procession)))

        df.apply(assign, buffer=buffer)
        feeder(buffer, len(df))
        return True

    def load_his_ticks(self, stdCode:str, uDate:int, feeder) -> bool:
        '''
        加载历史K线（只在回测有效，实盘只提供当日落地的）
        @stdCode    合约代码，格式如CFFEX.IF.2106
        @uDate      日期，格式如yyyymmdd
        @feeder     回调函数，feed_raw_ticks(ticks:POINTER(WTSTickStruct), count:int)
        '''
        print("loading ticks on %d of %s from extended loader" % (uDate, stdCode))

        df = pd.read_csv('./storage/csv/rb主力连续_20201030.csv')
        BUFFER = WTSTickStruct*len(df)
        buffer = BUFFER()

        tags = ["一","二","三","四","五"]

        for i in range(len(df)):
            curTick = buffer[i]

            curTick.exchg = b"SHFE"
            curTick.code = b"SHFE.rb.HOT"

            # ......

        feeder(buffer, len(df))

def test_in_bt():
    engine = WtBtEngine(EngineType.ET_CTA)

    # 初始化之前，向回测框架注册加载器
    # 如果bAutoTrans为True，回测框架则会把ExtDataLoader传入的数据转储为dsb文件
    # 下次就直接从转储的dsb读取历史数据了
    engine.set_extended_data_loader(loader=MyDataLoader(), bAutoTrans=False)

    engine.init('../common/', "configbt.yaml")

    engine.configBacktest(201909100930,201912011500)
    engine.configBTStorage(mode="csv", path="../storage/")
    engine.commitBTConfig()

    straInfo = StraDualThrust(
        name='pydt_IF', code="CFFEX.IF.HOT", 
        barCnt=50, period="m5", days=30, 
        k1=0.1, k2=0.1, isForStk=False)
    engine.set_cta_strategy(straInfo)

    engine.run_backtest()
    engine.release_backtest()

def test_in_rt():
    engine = WtEngine(EngineType.ET_CTA)

    # 初始化之前，向实盘框架注册加载器
    engine.set_extended_data_loader(MyDataLoader())

    engine.init('../common/', "config.yaml")
    
    straInfo = StraDualThrust(
        name='pydt_au', code="SHFE.au.HOT", 
        barCnt=50, period="m5", days=30, 
        k1=0.2, k2=0.2, isForStk=False)
    engine.add_cta_strategy(straInfo)

    engine.run()

    kw = input('press any key to exit\n')
通过ExtDataLoader，就可以很方便地在WonderTrader上直接使用用户原有的数据存储引擎，如数据库、parquet文件，甚至是直连第三方数据供应商的API，在迁移到WonderTrader的时候就不用担心数据转换带来的额外的工作量，除了策略之外，其他都可以做到无缝切换。

数据转储工具
虽然通过ExtDataLoader解决了WonderTrader读取自有数据存储引擎的问题，但是在生产环境中，还有另外一个问题：每天新增的数据是否能够较为方便的转储到自有的数据存储引擎中。
为了解决这个问题，wtpy提供了一个专用的数据转储模块——ExtDataDumper。datakit每天做收盘作业的时候，会检查是否注册了ExtDataDumper，如果注册了ExtDataDumper，则会将当个交易日新增的数据通过ExtDataDumper推送到python中，而用户则通过实现ExtDataDumper把收盘作业推送出来的当日新增数据转储到目标数据存储引擎中。可以参考wtpy/demos/test_dataexts

from wtpy import WtDtEngine
from wtpy.ExtModuleDefs import BaseExtDataDumper

class MyDataDumper(BaseExtDataDumper):
    def __init__(self, id:str):
        BaseExtDataDumper.__init__(self, id)

    def dump_his_bars(self, stdCode:str, period:str, bars, count:int) -> bool:
        '''
        加载历史K线（回测、实盘）
        @stdCode    合约代码，格式如CFFEX.IF.2106
        @period     周期，m1/m5/d1
        @bars       回调函数，WTSBarStruct的指针
        @count      数据条数
        '''
        print("dumping %s bars of %s via extended dumper" % (period, stdCode))
        return True

    def dump_his_ticks(self, stdCode:str, uDate:int, ticks, count:int) -> bool:
        '''
        加载历史K线（只在回测有效，实盘只提供当日落地的）
        @stdCode    合约代码，格式如CFFEX.IF.2106
        @uDate      日期，格式如yyyymmdd
        @ticks      回调函数，WTSTickStruct的指针
        @count      数据条数
        '''
        print("dumping ticks on %d of %s via extended dumper" % (uDate, stdCode))
        return True

def test_ext_dumper():
    #创建一个运行环境，并加入策略
    engine = WtDtEngine()
    engine.add_extended_data_dumper(MyDataDumper("dumper"))
    engine.initialize("dtcfg.yaml", "logcfgdt.yaml")
    
    engine.run()

    kw = input('press any key to exit\n')
到这个时候，从datakit落地数据的引用，到回测需要的csv数据格式的支持，再到自有数据存储引擎的接入与转储，WonderTrader中的数据运用就形成了一个完整的闭环，完整覆盖了回测和实盘的各个环节。

投研数据运用
前面介绍了回测和实盘中如何运用各个模块对接数据，实际上还是漏了一个很重要的环节：投研环节。为什么要把投研和回测区分开呢？因为回测针对的是基本定型的策略，而投研则更多针对的是想法的验证。
那么投研环节对数据的需求和回测有什么不一样呢？投研和回测对数据的需求最大的不同就在于投研时会灵活地根据需要读取不通过标的不同时间段的数据，再基于这些数据进行再加工，然后分析因子或者逻辑的有效性，而回测则只是读取回测时间区间的数据然后逐条回放。

WtDtServo组件简介
WonderTrader针对投研环节也提供了相应的解决方案：WtDtServo组件。WtDtServo组件，只提供了6个数据接口：

def get_bars(self, stdCode:str, period:str, 
    fromTime:int = None, dataCount:int = None, endTime:int = 0) -> WtBarRecords:
    '''
    获取K线数据
    @stdCode    标准合约代码
    @period     基础K线周期, m1/m5/d
    @fromTime   开始时间, 日线数据格式yyyymmdd, 分钟线数据为格式为yyyymmddHHMM
    @endTime    结束时间, 日线数据格式yyyymmdd, 分钟线数据为格式为yyyymmddHHMM, 为0则读取到最后一条
    '''
    pass

def get_ticks(self, stdCode:str, 
    fromTime:int = None, dataCount:int = None, endTime:int = 0) -> WtTickRecords:
    '''
    获取tick数据
    @stdCode    标准合约代码
    @fromTime   开始时间, 格式为yyyymmddHHMM
    @endTime    结束时间, 格式为yyyymmddHHMM, 为0则读取到最后一条
    '''
    pass

def get_ticks_by_date(self, stdCode:str, iDate:int) -> WtTickRecords:
    '''
    按日期获取tick数据
    @stdCode    标准合约代码
    @iDate      日期, 格式为yyyymmdd
    '''
    pass

def get_sbars_by_date(self, stdCode:str, iSec:int, iDate:int) -> WtTickRecords:
    '''
    按日期获取秒线数据
    @stdCode    标准合约代码
    @iSec       周期, 单位s
    @iDate      日期, 格式为yyyymmdd
    '''
    pass

def get_bars_by_date(self, stdCode:str, period:str, iDate:int) -> WtTickRecords:
    '''
    按日期获取K线数据
    @stdCode    标准合约代码
    @period     周期，只支持分钟线
    @iDate      日期, 格式为yyyymmdd
    '''
    pass
用户可以根据需要调用不同的接口，获取不同的时段不同的标的的数据。底层的数据接口命名为IRdmDtReader，即随机读取数据的接口。WtDtServo的使用可以参考wtpy/demos/test_dtservo

from wtpy import WtDtServo

dtServo = WtDtServo()
dtServo.setBasefiles(folder="../common/")
dtServo.setStorage(path='../storage/')

# 读取IF主力合约的前复权数据
bars = dtServo.get_bars("CFFEX.IF.HOT-", "m5", fromTime=202205010930, endTime=202205281500).to_df()
bars.to_csv("CFFEX.IF.HOT-.csv")

# 读取IF主力合约的后复权数据
bars = dtServo.get_bars("CFFEX.IF.HOT+", "m5", fromTime=202205010930, endTime=202205281500).to_df()
bars.to_csv("CFFEX.IF.HOT+.csv")

# 读取IF主力合约的原始拼接数据
bars = dtServo.get_bars("CFFEX.IF.HOT", "m5", fromTime=202205010930, endTime=202205281500).to_df()
bars.to_csv("CFFEX.IF.HOT.csv")

# 读取IF主力合约的tick数据
bars = dtServo.get_ticks("CFFEX.IF.HOT", fromTime=202207250930, endTime=202207291500).to_df()
bars.to_csv("CFFEX.IF.HOT_ticks.csv")
WtDtServo的运用
事实上，WtDtServo只提供了一个访问底层数据的工具，以便于我们的投研工作，可以基于这个数据访问工具展开。WonderTrader也有很多其他的组件，依赖于WtDtServo提供的数据访问能力。
回测查看器WtBtSnooper中的信号分析模块的K线数据，就是调用WtDtServo的接口获取的，所以在WtBtSnooper中还需要配置WtDtServo，可以参考wtpy/demos/test_btsnooper

from wtpy.monitor import WtBtSnooper
from wtpy import WtDtServo

def testBtSnooper():    

    dtServo = WtDtServo()
    dtServo.setBasefiles(folder="E:\\gitlocal\\MyStras\\CTA\\common\\")
    dtServo.setStorage(path='E:/storage/')

    snooper = WtBtSnooper(dtServo)
    snooper.run_as_server(port=8081)

testBtSnooper()
# 运行了服务以后，在浏览器打开以下网址即可使用
# http://127.0.0.1:8081/backtest/backtest.html
运行起来以后，效果图如下：


回测查看器


除了回测查看器以外呢，笔者团队内部还改造了一下alphalens，做了一个时序高频因子的研究框架wtfactor，也是利用WtDtServo访问数据。

from wtpy import WtDtServo

from pandas import DataFrame
from numpy import ndarray

from wtfactor.datamgr import DataMgr
from wtfactor.generator import FactorGeneratorID, FactorGenerator
from wtfactor.factor import BarFactor, TickFactor
from wtfactor.tears import create_returns_tear_sheet,create_portfolio_returns_tear_sheet

#配置WtDtServo
dtServo = WtDtServo()
dtServo.setBasefiles(folder="./common/", commfile="stk_comms.json", contractfile='stocks.json')
dtServo.setStorage(path='./storage/')

#将WtDtServo注册给DataMgr
dtMgr = DataMgr()
dtMgr.set_data_servo(dtServo)

#MicroPrice/Spread Tick因子
class FactorMidPriceDevidedSpread(TickFactor):
    def calc(self, ticks:DataFrame)->ndarray:
        pxInThry = (ticks["bid_price_0"]*ticks["ask_qty_0"] + ticks["ask_price_0"]*ticks["bid_qty_0"]) / (ticks["ask_qty_0"] + ticks["bid_qty_0"])
        return (pxInThry-ticks["price"])/(ticks["ask_price_0"]-ticks["bid_price_0"])

#DualThrust Bar因子
class FactorDualThrust(BarFactor):
    def calc(self, bars:DataFrame)->ndarray:
        hh = bars["high"].rolling(4).max()
        hc = bars["close"].rolling(4).max()
        ll = bars["low"].rolling(4).min()
        lc = bars["close"].rolling(4).min()
        bars["range1"] = hh-lc
        bars["range2"] = hc-ll
        range = bars.apply(lambda x: max(x['range1'],x['range2']),axis=1)
        factor = (bars["close"]-bars["open"])/range
        return factor

def test_id_mp_with_ticks(codes:list):
    '''
    用tick测试日内MicroPrice因子
    '''
    factorGen = FactorGeneratorID(dtMgr)
    factorGen.set_dates([20210104,20210105,20210106])
    myFactor = FactorMidPriceDevidedSpread()
    factorGen.set_tick_factor(myFactor)
    factorGen.set_codes(codes)
    factor_data = factorGen.generate_tick_factor_data(quantiles=3, periods=(3,5,10))
    create_returns_tear_sheet(factor_data, title="microprice/spread因子分析")

def test_id_dt_with_sbars(codes:list):
    '''
    用秒线测试日内DualThrust因子
    '''
    factorGen = FactorGeneratorID(dtMgr)
    factorGen.set_dates([20210104,20210105,20210106])
    myFactor = FactorDualThrust()
    factorGen.set_bar_factor(myFactor)
    factorGen.set_codes(codes)
    factor_data = factorGen.generate_bar_factor_data(period=60, quantiles=3, periods=(1,2,4,8))
    create_portfolio_returns_tear_sheet(factor_data, title="DualThrust因子分析")

def test_id_dt_with_bars(codes:list):
    '''
    用1分钟线测试日内DualThrust因子
    '''
    factorGen = FactorGeneratorID(dtMgr)
    factorGen.set_dates([20210104,20210105,20210106])
    myFactor = FactorDualThrust()
    factorGen.set_bar_factor(myFactor)
    factorGen.set_codes(codes)
    factor_data = factorGen.generate_bar_factor_data(period='m1', quantiles=3, periods=(3,5,10))
    create_portfolio_returns_tear_sheet(factor_data, title="DualThrust因子分析")

def test_intraday_factors():
    '''
    测试日内因子
    '''
    # test_id_mp_with_ticks(['SSE.STK.600000','SZSE.STK.000001'])
    test_id_dt_with_sbars(['SSE.STK.600000','SZSE.STK.000001'])
    # test_id_dt_with_bars(['CFFEX.IF.HOT','CFFEX.IC.HOT'])

test_intraday_factors()
input("press enter key to exit\n")
运行起来以后，效果图如下：

