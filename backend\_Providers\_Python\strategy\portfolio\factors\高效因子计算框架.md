# 高效因子计算框架设计

本文档旨在为量化策略平台（特别是追求高性能的框架，如 WonderTrader）设计一套高效的因子计算框架，并为单个因子计算文件（位于本目录下）提供统一的编写规范。

## 核心思想

为了在处理大量品种、长回测周期和复杂因子组合时获得极致性能，本框架遵循以下核心思想：

1.  **批量预计算 (Batch Pre-calculation):** 在策略执行前（回测）或初始化阶段（实盘），一次性计算出策略所需的所有基础因子在整个相关时间范围内的**完整历史序列**。
2.  **向量化计算 (Vectorized Computation):** 利用 Pandas/NumPy（在 Python 验证阶段）或 TA-Lib C 接口/C++ 数值库（在 C++ 实现阶段）的向量化能力，对所有品种的数据进行批量并行计算。
3.  **避免运行时重复计算:** 在策略运行的每个时间步长（如 `on_bar`），因子获取应简化为对预计算结果的快速内存查找，而不是重新计算历史序列。

## 数据结构约定 (Python 层面)

为了在 Python 环境中验证和设计因子计算逻辑，我们约定使用以下 Pandas 数据结构：

*   **输入数据 (`all_bars_data`)**:
    *   类型: `pandas.DataFrame`
    *   索引: `pandas.MultiIndex` (level 0: `datetime`, level 1: `asset_id`)
    *   列: 包含所有必需的基础 K 线数据，如 `open`, `high`, `low`, `close`, `volume` 等。
    ```python
    # 示例结构
    #                            open   high    low  close    volume
    # datetime   asset_id
    # 2023-01-01 TickerA    10.0   10.5    9.9   10.4   1000000
    #            TickerB    20.0   20.2   19.8   20.1    500000
    # 2023-01-02 TickerA    10.4   10.6   10.3   10.5   1200000
    #            TickerB    20.1   20.5   20.0   20.4    600000
    # ...
    ```

*   **输出因子序列**:
    *   类型: `pandas.Series`
    *   索引: `pandas.MultiIndex` (与输入 `all_bars_data` 的索引完全一致)
    *   值: 计算得到的因子值。
    ```python
    # 示例结构 (对应上述输入的 roc(close, 1) 结果)
    # datetime   asset_id
    # 2023-01-01 TickerA      NaN
    #            TickerB      NaN
    # 2023-01-02 TickerA     0.96...
    #            TickerB     1.49...
    # ...
    # Name: factor_value, dtype: float64
    ```

## 计算流程 (框架/策略引擎层面)

1.  **初始化阶段 (`on_init` 或等效阶段):**
    *   **加载基础数据:** 加载策略所需的全部时间范围、全部品种 (`universe`) 的基础 K 线数据，构建成上述 `all_bars_data` (MultiIndex DataFrame)。
    *   **解析所需因子:** 分析策略配置（如 `order_by`, `buy_rules`, `sell_rules` 公式），识别出所有需要计算的基础因子及其参数（例如：`roc(close, 20)`, `ma(volume, 5)`, `trend_score(close, 25)`）。
    *   **执行批量计算:**
        *   对于列表中的每一个基础因子（如 `roc`, `ma`, `trend_score`）:
            *   导入对应的因子计算文件（例如 `roc.py`）。
            *   调用其核心向量化计算函数（见下一节规范，如 `calculate_series_vectorized`），传入 `all_bars_data`、因子参数（如 `period=20`, `column='close'`）。
            *   获取返回的包含所有品种、所有时间的因子历史序列 (MultiIndex Series)。
    *   **存储预计算结果:** 将所有计算出的因子历史序列存储在高效的数据结构中（Python 中可以是字典 `{ "roc_20_close": series1, "ma_5_volume": series2, ... }`，C++ 中可以是 `std::map` 或类似结构）。

2.  **运行时阶段 (`on_bar` / `on_calculate` 或等效阶段):**
    *   获取当前时间点 `t`。
    *   **因子值获取:**
        *   需要评估复合公式（如 `trend_score * 0.2 + roc * 0.1`）时，直接从**预计算结果存储**中，根据因子标识（如 `"roc_20_close"`）和当前时间点 `t`，**查找**出该时刻**所有品种**的因子值（得到一个单索引 Series，索引为 `asset_id`）。
    *   **逻辑评估:** 使用这些**已存在**的因子值，通过向量化操作（Pandas Series 运算或 C++ 容器运算）快速评估复合公式、执行排序、应用买卖规则。
    *   **核心:** 严禁在此阶段再次调用因子计算函数来计算历史序列！

## 因子代码文件规范 (`*.py` in `factors` directory)

所有位于本目录下的因子计算文件（`.py`）必须遵循以下规范：

1.  **文件名:** 使用清晰、小写、下划线分隔的因子名称（例如：`moving_average.py`, `rate_of_change.py`, `trend_strength.py`）。
2.  **核心函数:**
    *   **必须**包含一个核心的向量化计算函数。
    *   **推荐名称:** `calculate_series_vectorized`。
    *   **函数签名:**
        ```python
        import pandas as pd
        import numpy as np # 或其他必要库
        from typing import Union # 可选

        def calculate_series_vectorized(all_bars_data: pd.DataFrame, column: str, **kwargs) -> pd.Series:
            """
            计算[因子名称]的向量化历史序列。

            Args:
                all_bars_data (pd.DataFrame): 包含所有品种、所有时间的基础 K 线数据 (MultiIndex: datetime, asset_id)。
                column (str): 用于计算因子的基础数据列名 (e.g., 'close', 'volume', 'high').
                **kwargs: 因子特定的参数 (e.g., period: int, window: int, fastperiod: int).

            Returns:
                pd.Series: 包含所有品种、所有时间计算出的因子值的 MultiIndex Series，索引与输入一致。
                           如果无法计算（例如输入数据不足），应在对应位置填充 NaN。
            """
            # --- 函数实现 ---
            pass
        ```
3.  **实现要求:**
    *   **向量化:** 必须使用 Pandas/NumPy 的向量化操作，避免显式循环品种或时间。
    *   **跨品种处理:** 对于需要在品种内部独立进行的操作（如 `shift`, `rolling`, `cumsum` 等），**必须**使用 `groupby(level='asset_id')`。
        ```python
        # 示例: 正确计算每个品种独立的 shift
        shifted_data = all_bars_data[column].groupby(level='asset_id').shift(kwargs.get('period', 1))
        ```
    *   **参数处理:** 使用 `kwargs.get('param_name', default_value)` 获取参数，提供合理的默认值。
    *   **边界与NaN处理:** 必须正确处理计算初期的 NaN 值（例如，`rolling` 或 `shift` 会产生 NaN）。如果因为数据不足导致整个计算无法进行，函数应能正常返回（例如返回填充了 NaN 的 Series），而不是抛出异常（除非是不可恢复的错误）。
    *   **文档字符串 (Docstring):** 必须包含清晰的文档字符串，说明因子作用、计算逻辑、输入参数 (`Args`) 和返回值 (`Returns`)。
    *   **日志:** 可以包含少量关键日志（例如，参数检查、重大警告），但避免在核心计算逻辑中大量打印，以免影响性能。使用统一格式，如 `print(f"[因子计算:{因子名}] 日志信息")`。
4.  **禁止内容:**
    *   **禁止包含 `calculate_value` 函数:** 获取最新值的任务由框架/策略引擎通过查找预计算结果完成。
    *   **禁止依赖特定上下文:** 代码应仅依赖输入数据和参数，不应访问外部策略状态或框架特定的上下文对象。
5.  **依赖:**
    *   核心依赖应为 `pandas` 和 `numpy`。
    *   如果需要，可依赖 `scipy` 等标准数值计算库。
    *   **不应**直接依赖 `talib` Python 包（因为最终目标是在 C++ 中使用 TA-Lib C 接口或 C++ 实现）。Python 文件主要用于定义逻辑和验证。

## 示例: `moving_average.py`

```python
import pandas as pd
import numpy as np

def calculate_series_vectorized(all_bars_data: pd.DataFrame, column: str, **kwargs) -> pd.Series:
    """
    计算移动平均线 (Moving Average) 的向量化历史序列。

    Args:
        all_bars_data (pd.DataFrame): 包含所有品种、所有时间的基础 K 线数据 (MultiIndex: datetime, asset_id)。
        column (str): 用于计算 MA 的基础数据列名 (e.g., 'close', 'volume').
        **kwargs:
            period (int): 移动平均的周期 (默认 20).

    Returns:
        pd.Series: 计算出的 MA 因子值的 MultiIndex Series。
    """
    period = kwargs.get('period', 20)
    if not isinstance(period, int) or period <= 0:
        print(f"[因子计算:MA] 参数错误: period ({period}) 必须是正整数。")
        # 返回与输入形状相同，但填充 NaN 的 Series
        return pd.Series(np.nan, index=all_bars_data.index)

    if column not in all_bars_data.columns:
        print(f"[因子计算:MA] 输入错误: 指定列 '{column}' 不在数据中。")
        return pd.Series(np.nan, index=all_bars_data.index)

    # 检查是否有足够数据进行滚动计算（可选，rolling 会自动处理）
    # min_required = period
    # if len(all_bars_data.index.levels[0]) < min_required: # 粗略检查时间长度
    #    print(f"[因子计算:MA] 数据警告: 时间序列长度不足 {min_required}，MA 结果可能大量为 NaN。")

    # 核心计算：按资产分组，然后应用滚动窗口计算均值
    try:
        ma_series = all_bars_data[column].groupby(level='asset_id') \
                                         .rolling(window=period, min_periods=period) \
                                         .mean()
        # rolling 操作后索引可能变化，需要重置或调整以匹配原始索引
        # .mean() 通常会保持 MultiIndex，但层级可能与原始不同或需要排序
        # 确保返回的 Series 索引与 all_bars_data.index 一致
        # 对于 .mean()，通常结果的 MultiIndex 是 (asset_id, datetime)，需要调整
        ma_series = ma_series.reset_index(level=0, drop=True).sort_index()

        # print(f"[因子计算:MA] MA({column}, {period}) 计算完成。")
        return ma_series
    except Exception as e:
        print(f"[因子计算:MA] 计算 MA({column}, {period}) 时出错: {e}")
        return pd.Series(np.nan, index=all_bars_data.index)

```

## 集成到 C++ 框架 (如 WonderTrader)

*   本目录下的 Python 文件主要用于 **定义计算逻辑、验证算法和统一规范**。
*   在 WonderTrader 的 C++ 策略中：
    *   参考相应 Python 文件中的向量化逻辑。
    *   在 `on_init` 中，使用 `WtDtHelper` 获取数据。
    *   调用 **TA-Lib C 接口**（用于标准指标如 MA, ROC）或 **自行实现的 C++ 函数**（用于自定义指标如 TrendScore，逻辑参考 Python 实现）进行批量预计算。
    *   将结果存储在 C++ 的高效数据结构中 (如 `std::map<std::string, std::map<uint64_t, double>>` 或更优结构，内层 map key 为 bar 的时间戳)。
    *   在 `on_bar` 中，通过时间戳和品种 ID 从 C++ 结构中快速**查找**因子值，进行后续评估。 