const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = {
  mode: 'production',
  entry: {
    main: './src/index.tsx'
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js',
    chunkFilename: '[name].[contenthash].chunk.js',
    clean: true
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true
          },
          format: {
            comments: false
          }
        },
        extractComments: false
      })
    ],
    splitChunks: {
      chunks: 'all',
      minSize: 20000,
      minChunks: 1,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      cacheGroups: {
        // React基础框架
        framework: {
          test: /[\\/]node_modules[\\/](react|react-dom|react-router-dom)[\\/]/,
          name: 'framework',
          priority: 40,
          reuseExistingChunk: true
        },
        // Ant Design相关
        antd: {
          test: /[\\/]node_modules[\\/](@ant-design|antd)[\\/]/,
          name: 'antd',
          priority: 30,
          reuseExistingChunk: true
        },
        // Pro Components
        proComponents: {
          test: /[\\/]node_modules[\\/]@ant-design[\\/]pro-components[\\/]/,
          name: 'pro-components',
          priority: 25,
          reuseExistingChunk: true
        },
        // 图表相关
        charts: {
          test: /[\\/]node_modules[\\/](lightweight-charts|@debut\/indicators)[\\/]/,
          name: 'charts',
          priority: 20,
          reuseExistingChunk: true
        },
        // 其他第三方库
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          priority: 10,
          reuseExistingChunk: true
        }
      }
    }
  },
  plugins: [
    // Gzip压缩
    new CompressionPlugin({
      test: /\.(js|css|html|svg)$/,
      algorithm: 'gzip',
      threshold: 10240, // 只压缩10kb以上的文件
      minRatio: 0.8
    }),
    // 打包分析
    
    new BundleAnalyzerPlugin({
        // 打开分析报告时的默认浏览器
        analyzerMode: 'server',
        analyzerPort: 8888, // 自定义端口号
        reportFilename: 'report.html', // 分析报告保存的文件名
        // 初始浏览器大小
        defaultSizes: 'parsed',
        // 其他可选配置
        openAnalyzer: true // 打包完成后自动打开浏览器
      })

  ].filter(Boolean)
};