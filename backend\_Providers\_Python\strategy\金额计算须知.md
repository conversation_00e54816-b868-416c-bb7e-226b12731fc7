# 策略回测金额计算须知

本文档旨在说明在解读 `WonderTrader (wtpy)` 回测引擎生成的 CSV 文件（特别是 `funds.csv` 和 `closes.csv`）时，关于金额计算需要注意的关键规则。

## 核心概念：`volscale`

*   **定义：** `volscale` 是定义在品种配置文件（如 `common/stk_comms.json`, `common/etf_comms.json`）中的一个重要参数。它代表了该品种的 **合约乘数** 或 **数量放大倍数**。
*   **作用：** 回测引擎在内部进行交易模拟和盈亏计算时，通常会使用 `volscale` 来标准化处理。这意味着：
    *   引擎记录的内部交易量可能是 `实际交易量 * volscale`。
    *   基于此计算得出的盈亏金额（如 `funds.csv` 中的 `closeprofit`, `positionprofit` 和 `closes.csv` 中的 `profit`）也是相应放大的。
*   **常见值（根据当前项目配置）：**
    *   **股票 (STK):** `volscale = 1` (定义于 `stk_comms.json`)
    *   **ETF:** `volscale = 10000` (定义于 `stk_comms.json` 或 `etf_comms.json`)

## 金额还原规则

由于回测输出的 CSV 文件中的金额数据是基于 `volscale` 放大后的结果，因此在分析、展示或解读这些数据时，**必须进行还原**才能得到真实的金额（例如，以"元"为单位）。

**还原公式：**

`真实金额 = CSV 文件中记录的金额 / 对应品种的 volscale`

**示例：**

*   对于 **ETF** 策略的回测结果（`volscale=10000`）：需要将 `funds.csv` 和 `closes.csv` 中的相关金额字段值 **除以 10000**。
*   对于 **股票** 策略的回测结果（`volscale=1`）：需要将 `funds.csv` 和 `closes.csv` 中的相关金额字段值 **除以 1** （即保持原值不变）。

## 当前实现注意 (`portfolio_analyzer.py`)

当前的绩效分析脚本 `portfolio/portfolio_analyzer.py` 在处理 `funds.csv` 和 `closes.csv` 中的金额字段时，**硬编码**了 `/ 10000.0` 的操作。

*   **适用性：** 这个硬编码操作 **仅适用于** `volscale` 为 10000 的品种（如本项目配置中的 ETF）。
*   **问题：** 当使用此脚本分析 **股票** 策略（`volscale=1`）的回测结果时，会导致计算出的绩效指标（如总收益、平均盈亏等）和权益曲线的金额值被 **错误地缩小 10000 倍**。

**后续改进建议：**

为了正确处理不同类型的策略回测，`portfolio_analyzer.py` 需要能够 **动态地** 获取并使用正确的 `volscale` 值进行金额还原。可能的实现方式包括：

1.  **传递参数：** 在调用 `analyze_backtest_results` 函数时，将策略对应的 `volscale` 或 `trading_type` 作为参数传入。
2.  **读取配置：** 让分析脚本从回测输出目录中读取某个标识文件（例如 `btenv.json` 或由 `portfolio_center.py` 生成的包含 `volscale` 或 `trading_type` 信息的文件）来确定正确的 `volscale`。

**请务必在解读和使用 `portfolio_analyzer.py` 输出的结果时，注意当前存在的这个限制，并根据实际回测的策略类型（股票或 ETF）来判断结果是否需要手动调整。** 