# WonderTrader (wtpy) 策略架构理解与策略编写指南

## 读者对象

本指南面向希望使用 WonderTrader (wtpy) 进行量化策略开发，但对该框架的设计理念和具体实践尚不熟悉的开发者或研究员。无论你是有经验的程序员还是策略建模专家，希望这份指南能帮助你快速入门。

## 核心目标：让策略开发回归本质

想象一下，你是一位顶级大厨（策略研究员），你的目标是创造出惊艳味蕾的菜肴（赚钱的策略）。你最关心的是食材的选择（因子挖掘）、烹饪方法（信号逻辑）和最终呈现（投资组合）。

而厨房的日常运作——比如采购（数据接入）、订单处理（交易执行）、库存管理（持仓跟踪）、成本核算（手续费滑点计算）——这些虽然重要，但不应该是大厨每天耗费心神去直接管理的琐事。应该有一个高效的厨房经理（量化框架）来搞定这一切。

**WonderTrader (wtpy) 的核心目标就是扮演好这位"厨房经理"的角色，让"大厨"可以专注于策略研发本身。** 它致力于将策略开发过程**极简化**，剥离所有与核心决策无关的底层复杂性。

## 核心设计理念：引擎与策略的明确分工 (关注点分离)

`wtpy` 的灵魂在于其**关注点分离 (Separation of Concerns)**的设计：

1.  **引擎 (厨房经理) 的职责：执行、跟踪、管理**
    *   **数据驱动**: 负责接入实时或历史行情数据，并将数据变化以标准化的**事件**形式（如新 Tick 到达、K 线完成）推送给策略。
    *   **交易执行**: 管理订单的生命周期，对接真实的交易接口（实盘）或进行模拟撮合（回测）。
    *   **持仓跟踪**: **精确地**管理和跟踪每个交易标的的**当前持仓数量 (`position`)**。这是引擎最核心的状态管理职责之一。
    *   **底层状态维护**: 维护交易通道、接口连接等底层状态。
    *   **日志记录**: 记录详细的操作日志、交易流水、资金变化等，供后续分析。

2.  **策略 (大厨) 的职责：决策、响应、声明意图**
    *   **响应事件**: 通过实现特定的 `on_xxx` 方法（如 `on_tick`, `on_bar`, `on_calculate`）来**被动地响应**引擎推送的事件。
    *   **数据分析与信号生成**: 在事件处理方法中，获取所需数据（通常通过 `Context` 对象），进行计算分析，最终形成**交易决策**或**交易信号**。
    *   **声明交易意图**: **关键点** - 策略**不直接执行**买卖操作。它通过 `Context` 对象向引擎**声明**它的**交易意图**。例如，"我想买入 X"、"我想将 Y 的仓位调整到 Z 手"。
    *   **聚焦核心逻辑**: 策略代码应该只包含信号生成、因子计算、目标状态确定等核心决策逻辑。

3.  **`Context` 对象：策略与引擎沟通的唯一桥梁**
    *   策略代码不能直接访问引擎内部。所有的交互都必须通过传递给 `on_xxx` 方法的 `Context` 对象进行。
    *   通过 `Context` 可以：
        *   获取数据: `context.stra_get_bars()`, `context.stra_get_ticks()`
        *   获取状态: `context.stra_get_position()` (获取引擎维护的当前持仓), `context.stra_get_comminfo()` (获取品种信息)
        *   **声明意图**: `context.stra_enter_long()`, `context.stra_set_position()`, `context.stra_log_text()` 等。

## 为何策略在运行时不依赖"实时动态权益"？

这是一个常见疑问，也是理解 `wtpy` 的关键。`wtpy` **不倾向于**在策略运行时通过 `context.stra_get_fund_data()` 提供一个实时更新、包含所有成本（手续费、滑点、盈亏）的精确"动态账户权益"。

**原因：**

*   **性能考量**: 实时精确计算动态权益非常复杂且耗时，会显著拖慢引擎的核心处理速度。
*   **关注点分离**: 引擎的核心是**持仓管理**和**交易执行**，而不是复杂的账户价值评估。
*   **后处理分析的严谨性**: 引擎会记录所有必要的交易流水和资金变动。在**回测结束后**，利用这些完整的原始数据进行绩效分析（如生成资金曲线、计算各种指标）是**更精确、更可靠**的方式。

## 策略编写实战指南

现在，让我们看看如何基于这些理念编写一个策略。

**1. 创建策略类**

你需要创建一个继承自 `wtpy` 基础策略类的 Python 类。最常用的是 `BaseCtaStrategy`（用于类似期货的 CTA 策略）或 `BaseSelStrategy`（用于选股类策略）。

```python
from wtpy import BaseCtaStrategy
from wtpy import CtaContext
import numpy as np
import pandas as pd

class MyAwesomeStrategy(BaseCtaStrategy):
    def __init__(self, name:str, param1:int, param2:float, codes:list, initial_capital:float, barCnt:int, period:str):
        BaseCtaStrategy.__init__(self, name) # 初始化基类

        # 存储策略参数和基本信息
        self.strategy_id = name
        self.param1 = param1
        self.param2 = param2
        self.codes = codes # 交易标的列表
        self.initial_capital = initial_capital # !!! 存储初始资金，非常重要 !!!
        self.bar_count = barCnt # 需要的K线数量
        self.period = period # K线周期

        # 其他策略内部状态变量
        self.some_internal_state = {}

    def on_init(self, context:CtaContext):
        """
        策略初始化回调，在引擎准备好后、开始处理数据前调用。
        适合做一些一次性的准备工作。
        """
        # 示例：预加载历史K线数据到内存，加速首次计算
        # 注意：CTA策略通常不需要在这里显式"订阅"K线，引擎会根据配置自动推送
        # 也不需要订阅Tick，除非你的策略逻辑真的需要处理Tick级数据
        print(f"[{self.strategy_id}] Strategy initializing...")
        for code in self.codes:
            try:
                # 预加载所需K线，isMain=True表示这是主要周期数据
                context.stra_prepare_bars(code, self.period, self.bar_count, isMain=True)
                print(f"[{self.strategy_id}] Prepared bars for {code}")
            except Exception as e:
                print(f"[{self.strategy_id}] Error preparing bars for {code}: {e}")
        
        # 可以从 context 加载用户保存的数据 (如果之前有保存过)
        # self.some_internal_state = context.user_load_data("my_state", {}) 
        
        print(f"[{self.strategy_id}] Initialization complete.")

    def on_calculate(self, context:CtaContext):
        """
        K线闭合时驱动的回调函数（或使用 on_bar）。
        这是策略核心逻辑的主要执行场所。
        """
        # 1. 获取数据
        # 假设我们处理 self.codes 中的第一个代码
        code = self.codes[0]
        try:
            # 获取预加载或引擎推送的K线数据 (WtNpKline 对象)
            df_bars = context.stra_get_bars(code, self.period, self.bar_count, isMain=True)
            if df_bars is None or len(df_bars) < self.bar_count:
                print(f"[{self.strategy_id}] Not enough bars for {code}")
                return # 数据不足，无法计算
        except Exception as e:
            print(f"[{self.strategy_id}] Error getting bars for {code}: {e}")
            return

        # 2. 计算信号/指标
        # 示例：简单移动平均线
        closes = df_bars.closes
        sma_short = np.mean(closes[-10:]) # 10周期均线
        sma_long = np.mean(closes[-30:]) # 30周期均线

        # 3. 获取当前状态
        current_pos = context.stra_get_position(code) # 获取该代码的当前持仓

        # 4. 生成交易决策并声明意图 (!!! 核心步骤 !!!)
        
        # === 交易意图声明方式一：事件驱动型 (适合简单信号触发) ===
        # if sma_short > sma_long and current_pos == 0: # 金叉且空仓
        #     target_units = 1 # 目标买入1手 (注意单位，股票通常是100的倍数)
        #     print(f"[{self.strategy_id}] Golden cross on {code}, entering long {target_units} units.")
        #     context.stra_enter_long(code, target_units, 'enterlong_signal') # 声明【买入开仓】意图
        # elif sma_short < sma_long and current_pos > 0: # 死叉且持有多仓
        #     print(f"[{self.strategy_id}] Death cross on {code}, exiting long {abs(current_pos)} units.")
        #     context.stra_exit_long(code, abs(current_pos), 'exitlong_signal') # 声明【卖出平多】意图
        
        # === 交易意图声明方式二：状态驱动型 (适合组合再平衡或目标仓位管理) ===
        target_units = 0 # 默认目标仓位为0
        if sma_short > sma_long: # 如果金叉，目标持有1手多仓
            target_units = 1 
        # (可以添加其他条件，比如死叉持有空仓 target_units = -1 等)

        if current_pos != target_units: # 只有当目标仓位与当前仓位不同时才操作
            print(f"[{self.strategy_id}] Signal on {code}. Current: {current_pos}, Target: {target_units}. Setting position.")
            context.stra_set_position(code, target_units, 'setpos_signal') # 声明【目标持仓】意图
            # 引擎会自动计算需要买入/卖出的差额来达到 target_units

    def on_tick(self, context:CtaContext, code:str, newTick:dict):
        """Tick数据驱动的回调（如果策略需要处理Tick）"""
        # 除非你的策略需要Tick级操作，否则通常可以留空
        pass

    def on_bar(self, context:CtaContext, code:str, period:str, newBar:dict):
        """另一种K线驱动的回调，在 on_calculate 之前触发"""
        # 通常使用 on_calculate 或 on_bar 其中一个即可
        pass

    # 其他可选的回调函数: on_order, on_trade, on_channel_ready, on_channel_lost 等
    # 这些用于处理订单状态、成交回报、通道状态等，但对于简化策略，通常不需要策略层面过多处理
```

**2. 资金与仓位管理的关键点（再次强调！）**

*   **回测中的仓位计算基准**: 当你需要根据总资金按比例计算买多少手时（例如，每个品种分配总资金的 10%），**请使用 `self.initial_capital`（你在 `__init__` 中存储的初始资金）作为计算基准**。
    ```python
    # 示例：在 __rebalance_portfolio__ 或 on_calculate 中
    total_value_basis = self.initial_capital # 使用初始资金作为基准
    target_capital_for_code = total_value_basis * 0.10 # 分配10%资金
    # ... 然后根据 target_capital_for_code 和价格计算 target_units ...
    # context.stra_set_position(code, target_units)
    ```
*   **不要依赖 `context.stra_get_fund_data()` 进行仓位计算**: 因为它在回测中可能不提供或不实时更新包含所有成本的动态权益。

**3. 理解 1+N 执行架构**

这是 `wtpy` 用于实盘交易和管理多个产品的核心架构。简单来说：

*   **"1" 是策略组合的净信号**: 平台可以运行多个策略，并将它们的交易信号（目标持仓）合并，计算出一个**最终的净目标头寸**。
*   **"N" 是执行通道/产品**: 这个净目标头寸可以被分发到 **N 个**不同的实盘账户或交易产品中去执行。
*   **差异化执行**: 每个执行通道 ("N" 中的一个) 可以独立配置：
    *   **头寸放大倍数**: 根据账户资金量调整实际交易手数。
    *   **执行算法**: 使用不同的下单逻辑（如滑点设置）优化成交。
*   **对策略的影响**: 策略开发者**不需要**关心这个分发过程。你只需要写好那个核心的 "1"（你的策略逻辑，产生目标持仓信号），平台会自动处理后续的 "N" 个执行。这再次体现了策略与执行的分离。

**4. 回测分析**

*   回测运行结束后，所有详细的交易记录、资金变化、持仓记录都会保存在 `outputs_bt` 目录下（通常是 CSV 文件）。
*   你需要**在回测后**分析这些文件（使用 Pandas、`WtBtAnalyst` 工具或其他分析库）来评估策略的真实表现，包括收益率、最大回撤、夏普比率、资金曲线（体现复利效果）等。

## 总结：wtpy 开发策略的核心要点

*   **理念**: 策略只负责决策（生成信号/目标状态），引擎负责执行。
*   **交互**: 通过 `Context` 对象与引擎沟通。
*   **资金基准 (回测)**: 使用 `initial_capital` 计算目标仓位大小。
*   **交易意图**: 使用 `stra_enter/exit` (简单事件) 或 `stra_set_position` (目标状态/再平衡)。
*   **分析**: 绩效评估主要在回测结束后进行。
*   **保持简单**: 专注于策略的核心逻辑，信任框架处理底层细节。

遵循这些原则，你就能更好地利用 WonderTrader 的优势，更高效地开发和测试你的量化交易策略。

**下一步**:

*   仔细阅读 `wtpy` 官方文档中的 API 说明。
*   研究官方提供的更多策略示例 (`demos` 目录)。
*   尝试运行简单的策略，并查看 `outputs_bt` 中的输出文件，理解它们的内容。

祝你使用 WonderTrader 进行策略开发顺利！ 