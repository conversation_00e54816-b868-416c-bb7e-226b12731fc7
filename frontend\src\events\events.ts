/**
 * 统一事件定义文件
 * 集中管理所有模块的事件类型和payload定义
 * 使用命名空间组织不同模块的事件
 */

import { IndicatorWrapper } from '@/_Modules/Indicators/IndicatorWrapper';
import { IndicatorLineConfig } from '@/shared_types/indicator';
import { Symbol, KLineData, MarketType, KLineInterval, ChartConfig, IndicatorType, KLine } from '@/shared_types/market';
import { UserInfo } from '@/utils/auth';

import { ChartTimeRange, DrawingLine, RangeSelectOperation } from '@/shared_types/chart';
import { ShapeConfig, ShapeConfigItem } from '@/shared_types/shape';
import { DrawingLineRecord } from '@/shared_types/chart';
import { StrategyListItem, TradeRecord, StrategyDetails, BaseBacktestMetrics, EquityPoint, BacktestResultThumbnail } from '../shared_types/strategy';
import { LiveStrategyInfo } from '@/shared_types/trade';

// 聊天模块事件
export namespace ChatEvents {
  export const Types = {
    SEND_MESSAGE: 'chat:send_message',
    MESSAGE_SENT: 'chat:message_sent',
    RECEIVE_MESSAGE: 'chat:receive_message',
    UPDATE_CHAT_HISTORY: 'chat:update_history',
    CHAT_HISTORY_RECEIVED: 'chat:chat_history_received',
    RECALL_MESSAGE: 'chat:recall_message',
    MESSAGE_RECALLED: 'chat:message_recalled',
    MARK_AS_READ: 'chat:mark_as_read',
    SEARCH_MESSAGES: 'chat:search_messages',
    SEARCH_RESULT: 'chat:search_result',
    UPLOAD_FILE: 'chat:upload_file',
    FILE_UPLOADED: 'chat:file_uploaded',
    GET_DEFAULT_SESSION: 'chat:get_default_session',
    SESSION_UPDATED: 'chat:session_updated',
    UPLOAD_STARTED: 'chat:upload_started',
    UPLOAD_PROGRESS: 'chat:upload_progress',
    UPLOAD_COMPLETED: 'chat:upload_completed',
    UPLOAD_FAILED: 'chat:upload_failed',
    TOGGLE_CHAT_WINDOW: 'chat:toggle_window',

  } as const;

  export interface Message {
    id?: number;
    content: string;
    type: 'text' | 'file' | 'image';
    userId?: number;
    username?: string;
    avatar?: string;
    timestamp?: string;
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
    fileType?: string;
    status?: 'sending' | 'sent' | 'failed';
    canRecall?: boolean;
    isRecalled?: boolean;
}

  export interface FileUpload {
    fileName: string;
    sessionId: number;
  }

  export interface FileProgress extends FileUpload {
    progress: number;
  }

  export interface FileCompleted extends FileUpload {
    url: string;
  }

  export interface FileUploaded {
    url: string;
    name: string;
    fileSize: number;
  }

  export type Payloads = {
    [Types.SEND_MESSAGE]: Message;
    [Types.MESSAGE_SENT]: Message;
    [Types.RECEIVE_MESSAGE]: Message;
    [Types.CHAT_HISTORY_RECEIVED]: Message[];
    [Types.RECALL_MESSAGE]: number;
    [Types.MESSAGE_RECALLED]: number;
    [Types.MARK_AS_READ]: null;
    [Types.SEARCH_MESSAGES]: string;
    [Types.SEARCH_RESULT]: Message[];
    [Types.UPLOAD_FILE]: File;
    [Types.FILE_UPLOADED]: FileUploaded;
    [Types.GET_DEFAULT_SESSION]: void;
    [Types.SESSION_UPDATED]: number;
    [Types.UPLOAD_STARTED]: FileUpload;
    [Types.UPLOAD_PROGRESS]: FileProgress;
    [Types.UPLOAD_COMPLETED]: FileCompleted;
    [Types.UPLOAD_FAILED]: FileUpload;
    [Types.TOGGLE_CHAT_WINDOW]: boolean;
  };
}

// 事件名称：同一种业务使用同样前缀，利于调试
// Drawing: 画线工具
export namespace ChartEvents {
  export const Types = {
    SYNC_CROSSHAIR: 'chart:sync_crosshair',
    CHART_RESIZE: 'chart:resize',
    PAUSE_SYNC: 'chart:pause_sync',
    SAVE_CHART_CONFIG: 'chart:save_config',
    CHART_CONFIG_READY: 'chart:config_ready',
    RANGE_SELECT_MODE_CHANGED: 'chart:range_select_mode_changed',
    RANGE_SELECTED: 'chart:range_selected',
    CHART_MOUSE_EVENT: 'chart:mouse_event',
    RANGE_OPERATION: 'chart:range_operation',
    RANGE_ANALYZE: 'chart:range_analyze',

    GET_DRAWING_LINES: 'Dawing:get_drawing_lines',

    ADD_DRAWING_LINE: 'Dawing:add_drawing_line',
    DELETE_DRAWING_LINE: 'Dawing:delete_drawing_line',
    DELETE_ALL_DRAWING_LINES: 'Dawing:delete_all_drawing_lines',
    UPDATE_DRAWING_LINE: 'Drawing:update_drawing_line',
    DRAWING_LINES_READY: 'Dawing:drawing_lines_ready',
    SAVE_DRAWING_LINES: 'Dawing:save_drawing_lines',
    APPLY_DRAWING_LINES: 'Dawing:apply_drawing_lines',
    SERIES_CREATED: 'chart:series_created',
    CLEAR_CHART_DRAWING_LINES: 'chart:clear_drawing_lines',
    TOGGLE_SIGNAL_LIST: 'chart:toggle_signal_list',
    TOGGLE_SYMBOLLIST: 'chart:toggle_symbol_list',
    CHART_DRAG_ENABLED: 'chart:drag_enabled',

    CHART_SCROLL_TO_INDEX: 'chart:scroll_to_index',
    DRAWING_TOOL_SELECTED: 'chart:drawing_tool_selected',
    DRAWING_COMPLETED: 'chart:drawing_completed',
    CREATE_MULTI_SYMBOL_OVERLAY: 'chart:create_multi_symbol_overlay',
    ENTER_PLAYBACK_MODE: 'chart:enter_playback_mode',
    EXIT_PLAYBACK_MODE: 'chart:exit_playback_mode',
    TOGGLE_PLAY_PAUSE: 'chart:toggle_play_pause',
    SET_PLAYBACK_STEP: 'chart:set_playback_step',
  } as const;

  export interface SyncCrosshairPayload {
    time: number;
    point: any;
    sourceChart: any;
  }

  export interface PauseSyncPayload {
    paused: boolean;
  }

  export interface ChartResizePayload {
    width: number;
  }

  export interface SaveChartConfigPayload {
    config: ChartConfig;
  }

  export interface ShowParamsModalPayload {
    id: string;
    type: IndicatorType;
    params: Record<string, number>;
    lines: IndicatorLineConfig[];
  }

  export interface RangeSelectModeChanged {
    enabled: boolean;
  }

  export interface RangeSelected {
    range: ChartTimeRange;
  }

  export interface RangeOperation {
    range: ChartTimeRange;
    operation: RangeSelectOperation;
  }

  export interface RangeAnalyzePayload {
    range: ChartTimeRange;
    analysisParams: {
      threshold: number;
      minBars: number;
    };
  }

  export interface ChartConfigReadyPayload {
    symbol: Symbol;
    interval: KLineInterval;
    savedIndicators: IndicatorWrapper[];
  }

  export interface GetDrawingLinesPayload {
    symbol: Symbol;
  }

  export interface AddDrawingLinePayload {
    drawingLine: DrawingLine;
  }

  export interface DeleteDrawingLinePayload {
    id: number;
  }

  export interface DeleteAllDrawingLinesPayload {
    symbol: Symbol;
    interval: KLineInterval;
    callback: ( success: boolean ) => void;
  }

  export interface RangeAnalyze {
    range: ChartTimeRange;
    analysisParams: {
      threshold: number;
      minBars: number;
    };
  }

  export interface ApplyDrawingLinesPayload {
    drawingLines: DrawingLineRecord[];
  }

  export interface UpdateDrawingLinePayload {
    drawingLine: DrawingLine;
  }

  export interface ToggleSignalListPayload {
    visible: boolean;
  }

  export interface ToggleSymbolListPayload {
    isMobile: boolean,
    visible: boolean;
  }

  export interface ChartScrollToIndexPayload {
    index: number;
  }

  export interface DrawingToolSelectedPayload {
    overlayName: string;  // klinechart 的 overlay 名称
  }

  export interface DrawingCompletedPayload {
    overlayName: string;  // 完成绘制的 overlay 名称
  }

  export interface CreateMultiSymbolOverlayPayload {
    symbols: string;  // 逗号分隔的品种代码串
  }

  export interface SaveDrawingLinesPayload {
    symbol: Symbol;
    interval: KLineInterval;
    overlays: any[];
  }

  export interface EnterPlaybackModePayload {}
  export interface ExitPlaybackModePayload {}
  export interface TogglePlayPausePayload { playing: boolean; }
  export interface SetPlaybackStepPayload { step: number; }

  export type Payloads = {
    [Types.SYNC_CROSSHAIR]: SyncCrosshairPayload;
    [Types.PAUSE_SYNC]: PauseSyncPayload;
    [Types.CHART_RESIZE]: ChartResizePayload;
    [Types.SAVE_CHART_CONFIG]: SaveChartConfigPayload;
    [Types.RANGE_SELECT_MODE_CHANGED]: RangeSelectModeChanged;
    [Types.RANGE_SELECTED]: RangeSelected;
    
    [Types.RANGE_OPERATION]: RangeOperation;
    [Types.UPDATE_DRAWING_LINE]: UpdateDrawingLinePayload;
    
    [Types.CHART_SCROLL_TO_INDEX]: ChartScrollToIndexPayload;
    [Types.DRAWING_TOOL_SELECTED]: DrawingToolSelectedPayload;
    [Types.DRAWING_COMPLETED]: DrawingCompletedPayload;
    [Types.CREATE_MULTI_SYMBOL_OVERLAY]: CreateMultiSymbolOverlayPayload;
    [Types.ENTER_PLAYBACK_MODE]: EnterPlaybackModePayload;
    [Types.EXIT_PLAYBACK_MODE]: ExitPlaybackModePayload;
    [Types.TOGGLE_PLAY_PAUSE]: TogglePlayPausePayload;
    [Types.SET_PLAYBACK_STEP]: SetPlaybackStepPayload;
  };
}

// 市场模块事件
export namespace MarketEvents {
  export const Types = {
    SEARCH_SYMBOLS: 'market:search_symbols',
    SEARCH_RESULT: 'market:search_result',
    SEARCH_ERROR: 'market:search_error',
    GET_KLINES: 'market:get_klines',
    KLINES_ERROR: 'market:klines_error',
    REFRESH_SYMBOLS: 'market:refresh_symbols',
    REFRESH_RESULT: 'market:refresh_result',
    REFRESH_ERROR: 'market:refresh_error',
    CALCULATE_SIGNALS: 'market:calculate_signals',

    SIGNALCALCULATED_RESULT: 'signal:signals_result',
    SIGNALS_ERROR: 'signal:signals_error',
    SIGNAL_MARKERS_VISIBLE: 'signal:signal_markers_visible',
    SIGNAL_REMOVEOPTION: 'signal:signal_remove_option',

    KLINES_UPDATED: 'market:klines_updated',
    KLINES_READY: 'market:klines_ready',
    CALCULATE_BARCOUNT: 'market:calculate_bar_count',
    BARS_COUNT_RESULT: 'market:bar_count_result',

    SAVE_SHAPECONFIGS: 'shape config:save_shape_configs',
    SAVE_SHAPECONFIGS_RESULT: 'shape config:save_shape_configs_result',
    SIGNAL_OPTIONREMOVED: 'signal:signal_option_removed',

    GET_SHAPECONFIGDETAILS: 'strategy:get_shape_config_details',
    GET_ALLSHAPECONFIGS: "strategy:get_all_shape_configs",
    GET_ALLSHAPECONFIGS_RESULT: "strategy:get_all_shape_configs_result",

    SYMBOLLIST_CHANGED: 'market:symbol_list_changed',
    SYMBOL_CHANGED: 'market:symbol_changed',
    REQUEST_SYMBOLLIST_DATA: 'market:request_symbol_list_data',

    UPDATE_SYMBOLLIST: 'market:update_symbollist',
    SYMBOLLIST_UPDATED: 'market:symbol_list_updated',

    // 新增：显示股票池管理面板事件
    STOCK_POOL_DATA_CHANGED: 'market:stock_pool_data_changed',
    SHOW_STOCK_POOL_MANAGER: 'market:show_stock_pool_manager'
  } as const;

  export interface SearchSymbols {
    keyword: string;
  }

  export interface SearchResult {
    data: Symbol[];
    keyword: string;
  }

  export interface SearchError {
    error: any;
    keyword: string;
  }

  export interface GetKLines {
    symbol: Symbol;
    interval: KLineInterval;
    options?: {
      startTime?: number;
      endTime?: number;
      limit?: number;
    };
  }

  export interface KLinesError {
    error: any;
    symbol: Symbol;
    interval: KLineInterval;
  }

  export interface RefreshSymbols {
    market?: MarketType;
  }

  export interface RefreshResult {
    market?: MarketType;
    count: number;
  }

  export interface RefreshError {
    error: any;
    market?: MarketType;
  }

  export interface CalculateSignals {
    symbol: Symbol;
    signalConfig: {
      name: string;
      parameters: Record<string, any>;
    };
  }

  export interface SignalItem {
    time: number;
    type: 'BUY' | 'SELL';
    price: number;
    index: number;
  }

  export interface SignalsCalculatedResultPayload {
    title: string;
    signals: SignalItem[];
  }

  export interface SignalsError {
    message: string;
  }

  export interface SignalMarkersVisible {
    signals: SignalItem[];
    visible: boolean;
  }

  export interface SignalRemoveOptionPayload {
    paramName: string;
    option: string;
  }

  export interface RealtimeDataValidationFailed {
    cachedLastTime: number;
    realtimeFirstTime: number;
    realtimeLastTime: number;
  }

  export interface AddSubIndicator {
    type: string;
    params?: Record<string, any>;
  }

  export interface KLinesReady {
    kline: KLine;
    /*data: KLineData[];
    symbol: Symbol;
    interval: KLineInterval;*/
  }

  export interface KLinesUpdated {
    kline: KLine;
    newDataIndex: number;
  }

  export interface BarCountResult {
    count: number;
    startIndex: number;
    endIndex: number;
  }

  export interface SaveShapeConfig {
    shapeConfig: ShapeConfig;
  }

  export interface SaveShapeConfigsResult {
    success: boolean;
    data: any;
    error?: string;
  }

  export interface GetShapeConfigDetailsPayload {
    name: string;
    callback: (config: ShapeConfigItem[]) => void;
  }

  export interface GetShapeConfigs {
    callback: (config: any) => void;
  }

  export interface GetShapeConfigsResultPayload {
    success: boolean;
    data: ShapeConfig[];
    error?: string;
  }

  export interface SignalOptionRemovedPayload {
    option: string;
    paramName: string;
  }

  export interface GetShapeConfigResult {
    success: boolean;
    data: any;
    error?: string;
  }

  // This interface represents the structure used IN THIS COMPONENT and local storage,
  // based on the backend transformation and user requirements.
  export interface SymbolListItem {
    symbol: string;  // Now a string like "SSE.STOCK.600010"
    name: string;    // Top-level name
    data: {          // Nested object for other data
      viewedAt?: string;
      interval?: KLineInterval; // Assuming KLineInterval is the correct type
      notes?: string;
      // Add other potential fields inside data as needed
      [key: string]: any; // Allow other arbitrary properties within data
    };
  }

  export interface SymbolListChanged {
    symbolListName: string;
    description: string;
    isSystemList: boolean;
    tag: number;
    theList: any[] | string;  // 可以是对象数组或 JSON 字符串
  }

  export interface SymbolChangedPayload {
    symbol: Symbol;
  }

  export interface RequestSymbolListDataPayload {
    symbolListName: string;
    isSystemList: boolean;
    tag: number;
  }

  export interface UpdateSymbolListPayload {
    listName: string;
    description?: string;
    theList: any;
    isSystemList: boolean;
  }

  export interface SymbolListUpdatedPayload {
    listName: string;
    description?: string;
    theList: any;
    isSystemList: boolean;
  }

  // 新增：显示股票池管理面板事件载荷
  export interface ShowStockPoolManagerPayload {
    visible: boolean;
    currentListName?: string;
    tag: number;
  }

  export type Payloads = {
    [Types.SEARCH_SYMBOLS]: SearchSymbols;
    [Types.SEARCH_RESULT]: SearchResult;
    [Types.SEARCH_ERROR]: SearchError;
    [Types.GET_KLINES]: GetKLines;
    [Types.KLINES_ERROR]: KLinesError;
    [Types.REFRESH_SYMBOLS]: RefreshSymbols;
    [Types.REFRESH_RESULT]: RefreshResult;
    [Types.REFRESH_ERROR]: RefreshError;
    [Types.CALCULATE_SIGNALS]: CalculateSignals;
    [Types.SIGNALCALCULATED_RESULT]: SignalsCalculatedResultPayload;
    [Types.SIGNALS_ERROR]: SignalsError;
    [Types.SIGNAL_MARKERS_VISIBLE]: SignalMarkersVisible;
    [Types.KLINES_READY]: KLinesReady;
    [Types.KLINES_UPDATED]: KLinesUpdated;


    [Types.SYMBOLLIST_CHANGED]: SymbolListChanged;
    [Types.SYMBOL_CHANGED]: SymbolChangedPayload;
    [Types.REQUEST_SYMBOLLIST_DATA]: RequestSymbolListDataPayload;
    [Types.UPDATE_SYMBOLLIST]: UpdateSymbolListPayload;
    [Types.SYMBOLLIST_UPDATED]: SymbolListUpdatedPayload;
    [Types.SHOW_STOCK_POOL_MANAGER]: ShowStockPoolManagerPayload;
  };
}

// 用户模块事件
export namespace UserEvents {
  export enum Types {
    LOGIN = 'user:login',
    LOGIN_RESULT = 'user:login_result',
    REGISTER = 'user:register',
    REGISTER_RESULT = 'user:register_result',
    UPDATE = 'user:update',
    UPDATE_RESULT = 'user:update_result',
    LOGOUT = 'user:logout',
    SEND_SMS_CODE = 'user:send_sms_code',
    SMS_CODE_RESULT = 'user:sms_code_result',
    SEND_EMAIL_CODE = 'user:send_email_code',
    APPROVE_USER = 'user:approve_user',
    APPROVE_USER_RESULT = 'user:approve_user_result',
    RESET_PASSWORD = 'user:reset_password',
    RESET_PASSWORD_RESULT = 'user:reset_password_result',
  }

  export interface SMSCodePayload {
    phone: string;
    code: string;
  }

  export interface SMSCodeResultPayload {
    success: boolean;
    error?: string;
  }

  export interface EmailCodePayload {
    email: string;
    code: string;
  }

  export interface ResetPasswordPayload {
    email: string;
    code: string;
    newPassword: string;
  }

  export interface ApproveUserPayload {
    usernametobeapproved: string;
  }

  export interface ApproveUserResultPayload {
    success: boolean;
    message?: string;
  }

  export interface Payload {
    username: string;
    password?: string;
    phone?: string;
    smsCode?: string;
    email?: string;
    avatar?: string;
  }

  export interface ResultPayload {
    success: boolean;
    data: UserInfo | null;
    error?: string;
  }

  export type Payloads = {
    [Types.LOGIN]: Payload;
    [Types.LOGIN_RESULT]: ResultPayload;
    [Types.REGISTER]: Payload;
    [Types.REGISTER_RESULT]: ResultPayload;
    [Types.UPDATE]: { username: string; [key: string]: any };
    [Types.UPDATE_RESULT]: ResultPayload;
    [Types.LOGOUT]: undefined;
    [Types.SEND_SMS_CODE]: SMSCodePayload;
    [Types.SMS_CODE_RESULT]: SMSCodeResultPayload;
    [Types.SEND_EMAIL_CODE]: EmailCodePayload;
    [Types.APPROVE_USER]: ApproveUserPayload;
    [Types.APPROVE_USER_RESULT]: ApproveUserResultPayload;
    [Types.RESET_PASSWORD]: ResetPasswordPayload;
    [Types.RESET_PASSWORD_RESULT]: ResultPayload;
  };
}

// 指标模块事件
export namespace IndicatorEvents {
  export enum Types {
    ADD_INDICATOR = 'ADD_INDICATOR',
    REMOVE_INDICATOR = 'REMOVE_INDICATOR',
    UPDATE_INDICATOR = 'UPDATE_INDICATOR',
    INDICATOR_PARAMS_EDITED = 'Indicator_Params_Edited',
    INDICATOR_CALCULATED = 'INDICATOR_CALCULATED',
    INDICATOR_NEWDATACALCULATED = 'INDICATOR_New_Data_Calculated',
    SHOW_INDICATOR_PARAMS = 'SHOW_INDICATOR_PARAMS',
    SHOW_PARAMS_MODAL = 'SHOW_PARAMS_MODAL'
  }

  export interface AddIndicatorPayload {
    indicator: IndicatorWrapper;
  }

  export interface RemoveIndicatorPayload {
    id: string;
  }

  export interface UpdateIndicatorPayload {
    id: string;
    params?: Record<string, number>;
  }

  export interface IndicatorCalculatedPayload {
    id: string;
    values: Record<string, number[]>;
    times: number[];
  }

  export interface IndicatorNewDataCalculatedPayload {
    id: string;
    values: Record<string, number[]>;
    times: number[];
  }

  export interface ShowIndicatorParamsPayload {
    id: string;
  }

  export interface ShowParamsModalPayload {
    id: string;
    type: IndicatorType;
    params: Record<string, number>;
    lines: IndicatorLineConfig[];
  }

  export type Payloads = {
    [Types.ADD_INDICATOR]: AddIndicatorPayload;
    [Types.REMOVE_INDICATOR]: RemoveIndicatorPayload;
    [Types.UPDATE_INDICATOR]: UpdateIndicatorPayload;
    [Types.INDICATOR_PARAMS_EDITED]: UpdateIndicatorPayload;
    [Types.SHOW_INDICATOR_PARAMS]: ShowIndicatorParamsPayload;
    [Types.SHOW_PARAMS_MODAL]: ShowParamsModalPayload;
  };
}

// 实时数据模块事件
export namespace RealtimeEvents {
  export enum Types {
    REALTIME_SUBSCRIBE = 'realtime:subscribe',
    REALTIME_UNSUBSCRIBE = 'realtime:unsubscribe',
    REALTIME_DATA_START = 'realtime:data_start',
    REALTIME_DATA_END = 'realtime:data_end',
    REALTIME_ERROR = 'realtime:error',
    REALTIME_RECONNECT = 'realtime:reconnect',
  }

  export interface SubscribePayload {
    symbol: Symbol;
    period: KLineInterval;
  }

  export interface DataUpdatePayload {
    symbol?: string;
    updateLast?: KLineData;
    newData: KLineData[];
  }

  export interface ConnectionStatus {
    connected: boolean;
    message?: string;
  }

  export interface ErrorPayload {
    message: string;
    detail?: string;
  }

  export type Payloads = {
    [Types.REALTIME_SUBSCRIBE]: SubscribePayload;
    [Types.REALTIME_UNSUBSCRIBE]: SubscribePayload;
    [Types.REALTIME_DATA_START]: undefined;
    [Types.REALTIME_DATA_END]: undefined;
    [Types.REALTIME_ERROR]: ErrorPayload;
    [Types.REALTIME_RECONNECT]: undefined;
  };
}

// 键盘事件命名空间
export namespace KeyboardEvents {
  export const Types = {
    KEY_PRESSED: 'keyboard:key_pressed',
    INPUT_STARTED: 'keyboard:input_started',
    INPUT_CANCELLED: 'keyboard:input_cancelled',
    INPUT_COMPLETE: 'keyboard:input_complete',
    SYMBOL_BUTTON_CLICKED: 'keyboard:symbol_button_clicked',
  } as const;

  // 键盘按键事件载荷
  export interface KeyPressed {
    key: string;           // 按下的键
    keyCode: number;       // 键码
    isAlphaNumeric: boolean; // 是否为字母或数字
    isModifier: boolean;   // 是否为修饰键 (Ctrl, Alt, Shift)
    timestamp: number;     // 时间戳
  }

  // 开始输入事件载荷
  export interface InputStarted {
    initialKey: string;    // 初始按键
    timestamp: number;     // 时间戳
  }

  // 取消输入事件载荷
  export interface InputCancelled {
    reason: string;        // 取消原因
    timestamp: number;     // 时间戳
  }

  // 完成输入事件载荷
  export interface InputComplete {
    code: string;         // 基础代码 (e.g., '159399')
    market: string;       // 市场类型 (e.g., 'ETF', 'STOCK')
    exchange: string;     // 交易所 (e.g., 'SZSE', 'SSE')
    timestamp: number;    // 时间戳
  }

  // 所有键盘事件载荷类型映射
  export interface Payloads {
    [Types.KEY_PRESSED]: KeyPressed;
    [Types.INPUT_STARTED]: InputStarted;
    [Types.INPUT_CANCELLED]: InputCancelled;
    [Types.INPUT_COMPLETE]: InputComplete;
  }
}

// 策略模块事件
export namespace StrategyEvents {
  export const Types = {
    GET_STRATEGIES_LIST: 'strategy:get_strategies_list',
    GET_STRATEGY_LIST: 'strategy:get_strategy_list',
    STRATEGY_LIST_UPDATED: 'strategy:strategy_list_updated',
    UPDATE_LIVE_STRATEGY: 'strategy:update_live_strategy',
    GET_STRATEGY_DETAILS: 'strategy:get_strategy_details',
    COPY_STRATEGY: 'strategy:copy_strategy',
    UPDATE_STRATEGY: 'strategy:update_strategy',
    DEPLOY_STRATEGY: 'strategy:deploy_strategy',
    STRATEGY_DEPLOYED: 'strategy:strategy_deployed',
    STRATEGY_UPDATED: 'strategy:strategy_updated',
    RUN_BACKTEST: 'strategy:run_backtest',
    RUN_STRATEGY_BACKTEST: 'strategy:run_strategy_backtest',
    BACKTEST_RESULT: 'strategy:backtest_result',
    GET_LIVE_STRATEGIES: 'strategy:get_live_strategies',
    LIVE_STRATEGY_LIST_UPDATED: 'strategy:live_strategy_list_updated',
    START_LIVE_STRATEGY: 'strategy:start_live_strategy',
    LIVE_STRATEGY_STARTED: 'strategy:live_strategy_started',
    DEPLOY_TO_LIVE: 'strategy:deploy_to_live',
    STOP_LIVE_STRATEGY: 'strategy:stop_live_strategy',
    LIVE_STRATEGY_STOPPED: 'strategy:live_strategy_stopped',
    GET_LIVE_STRATEGY_LOGS: 'strategy:get_live_strategy_logs',
    LIVE_STRATEGY_LOGS_UPDATED: 'strategy:live_strategy_logs_updated',
    SHOW_STRATEGY_DETAILS: 'strategy:show_strategy_details',
    DELETE_LIVE_STRATEGY: 'strategy:delete_live_strategy',
    SHOW_LIVE_CONFIG: 'strategy:show_live_config',
    LIVE_STRATEGY_UPDATED: 'strategy:live_strategy_updated',
    GET_AVAILABLE_TRADING_CHANNELS: 'strategy:get_available_trading_channels',
    GET_TRADING_CHANNEL_CONFIG: 'strategy:get_trading_channel_config',
    SHOW_TRADING_CHANNEL_CONFIG: 'strategy:show_trading_channel_config',
    SHOW_STRATEGY_EDITOR: 'strategy:show_strategy_editor', // 新增
    TEST_PROCESS_ACTIVITY: 'strategy:test_process_activity', // 新增：测试进程活动状态

    TEST_LIVE_RT_CONNECTION: 'strategy:test_live_rt_connection',
    TEST_TRADING_CHANNEL: 'strategy:test_trade_channel_connection',

    GET_RUNTIME_DATA: 'strategy:get_live_strategy_runtime_data',
  } as const;

  export interface GetStrategyListPayload {
    callback?: (strategies: any[]) => void;
  }

  export interface StrategyListUpdatedPayload {
    strategies: any[];
  }

  // 使用导入的 StrategyListItem
  export interface GetStrategiesListPayload {
    callback: (success: boolean, data: StrategyListItem[]) => void;
  }

  export interface DeployStrategyPayload {
    strategyId: string;
    channelType: string;
    channelConfig: any;
    callback?: (success: boolean, error?: string) => void;
  }

  export interface StrategyDeployedPayload {
    strategyId: string;
    success: boolean;
    error?: string;
  }

  export interface RunBacktestPayload {
    strategyId: string;
    config: any;
    callback?: (result: any) => void;
  }

  export interface StrategyUpdatePayload {
    id: string;
    yaml: string;
  }

  export interface RunStrategyBacktestPayload {
    strategyId: string;
    callback: (success: boolean, resultData?: BacktestResultThumbnail | null, message?: string) => void;
  }

  export interface BacktestResultPayload {
    strategyId: string;
    result: any;
  }

  // 使用导入的 StrategyDetails
  export interface GetStrategyDetailsPayload {
    strategyId: string;
    callback: (success: boolean, details?: StrategyDetails | null, message?: string) => void;
  }

  export interface GetLiveStrategyListPayload {
    callback?: (strategies: any[]) => void;
  }

  export interface LiveStrategyListUpdatedPayload {
    strategies: any[];
  }

  export interface StartLiveStrategyPayload {
    liveStrategyId: string;
    callback?: (success: boolean, error?: string) => void;
  }

  export interface LiveStrategyStartedPayload {
    liveStrategyId: string;
    success: boolean;
    error?: string;
  }

  // --- 新增: 可用交易通道信息接口 ---
  export interface AvailableTradingChannel {
    id: string;         // 唯一标识, 例如 'user1_easytrader_ths' 或 'openctp'
    name: string;       // 显示名称, 例如 '同花顺 (user1)' 或 'OpenCTP期货交易'
    type: string;       // 通道类型, 例如 'easytrader_ths', 'openctp', 'miniQMT'
    status: string;     // 状态: 'online' 或 'offline'
    needsConfig: boolean;   // 是否需要额外配置
    configured?: boolean;   // 是否已配置（可选，仅对需要配置的通道有效）
  }
  
  // --- 新增: 交易通道配置相关接口 ---
  export interface TradingChannelConfig {
    channelType: string;        // 'openctp', 'miniQMT' 等
    configName: string;         // 用户自定义配置名称
    broker: string;             // 经纪商代码
    username: string;           // 交易账户
    password: string;           // 密码
    appid?: string;             // 应用ID（可选）
    authcode?: string;          // 认证码（可选）
    frontAddress?: string;      // 前置地址（可选，有默认值）
  }

  // --- 新增: 获取可用交易通道Payload ---
  export interface GetAvailableTradingChannelsPayload {
    // 可能需要 userId 或 username 来过滤?
    callback: (success: boolean, channels: AvailableTradingChannel[]) => void;
  }

  
  // 获取交易通道配置Payload
  export interface GetTradingChannelConfigPayload {
    channelType: string;        // 要获取配置的通道类型
    callback: (success: boolean, config?: TradingChannelConfig) => void;
  }

  export interface StopLiveStrategyPayload {
    liveStrategyId: string;
    callback?: (success: boolean, error?: string) => void;
  }

  // 保存交易通道配置Payload
  export interface SaveTradingChannelConfigPayload {
    config: TradingChannelConfig;
    callback: (success: boolean, message?: string) => void;
  }

  // 显示交易通道配置对话框Payload
  export interface ShowTradingChannelConfigPayload {
    channelType: string;        // 通道类型，例如 'openctp', 'miniQMT'
    channelName: string;        // 通道显示名称，例如 'OpenCTP期货交易'
    existingConfig?: TradingChannelConfig; // 现有配置（编辑模式）或undefined（新建模式）
  }
  
  export interface LiveStrategyStoppedPayload {
    liveStrategyId: string;
    success: boolean;
    error?: string;
  }

  export interface GetLiveStrategyLogsPayload {
    liveStrategyId: string;
    callback?: (logs: string[]) => void;
  }

  export interface LiveStrategyLogsUpdatedPayload {
    liveStrategyId: string;
    logs: string[];
  }

  export interface ShowStrategyDetailsPayload {
    strategyId: string;
  }

  export interface CopyStrategyPayload {
    strategyId: string;
    newName: string;
  }

  export interface StrategyUpdatedPayload {
    strategyId: string;
    yaml: string;
  }

  export interface DeleteLiveStrategyPayload {
    liveStrategyId: string;
    callback?: (success: boolean, message?: string) => void;
  }

  export interface ShowLiveConfigPayload {
    strategyId: string;
    strategyName: string;
    liveStrategyId?: string; // 可选，新部署时为空，编辑时有值
    currentStrategyType?: string;
    currentAccountId?: string;
    currentLogicalCapital?: number;
    currentCommissionRate?: number;
    currentMaxOrderSize?: number;
    currentMaxDailyTrades?: number;
    currentStopLossPercent?: number;
  }

  // 启动/停止实盘策略Payload
  export interface ControlLiveStrategyPayload {
    liveStrategyId: string; // 注意这里用的是实盘策略实例的ID (LiveStrategyInfo.id)
    callback: (success: boolean, message?: string) => void;
  }
  
  // 实盘策略配置接口
  export interface LiveStrategyConfig {
    strategyId: string;
    strategyType: string;      // 策略类型
    accountId: string;         // 对应 AvailableTradingChannel 的 id
    initialCapital: number;
    // --- 新增: 回测资金和手续费率 ---
    backtestCapital: number; // 回测基准资金
    commissionRate: number;  // 手续费率 (小数形式, e.g., 0.0002)
    // --- 结束新增 ---
    riskSettings: {
      maxOrderSize: number;
      maxDailyTrades: number;
      stopLossPercent: number;
    };
  }

  // 更新实盘策略配置Payload
  export interface UpdateLiveStrategyPayload {
    liveStrategyId: string; // 实盘策略实例ID
    config: {
      strategyType: string;
      accountId: string;
      initialCapital: number;
      commissionRate: number;
      riskSettings: {
        maxOrderSize: number;
        maxDailyTrades: number;
        stopLossPercent: number;
      };
    };
    callback: (success: boolean, message?: string) => void;
  }

  // 部署到实盘事件Payload
  export interface DeployToLivePayload {
    config: LiveStrategyConfig;
    callback: (success: boolean, message?: string) => void;
  }
  
  export interface GetLiveStrategiesPayload {
    callback?: (strategies: any[]) => void;
  }

  export interface LiveStrategyUpdatedPayload {
    strategyId: string;
    strategyName: string;
    liveStrategyId: string;
    currentAccountId?: string;
    currentLogicalCapital?: number;
  }

  // 启动/停止实盘策略Payload
  export interface ControlLiveStrategyPayload {
    liveStrategyId: string; // 注意这里用的是实盘策略实例的ID (LiveStrategyInfo.id)
    callback: (success: boolean, message?: string) => void;
  }

  export interface StrategyDataForEdit {
    id: string;
    name: string;
    yaml: string;
    universe: string[];
    startDate: string | null;
    endDate: string | null;
  }

  // 测试进程活动状态Payload
  export interface TestProcessActivityPayload {
    liveStrategyId: string;
    callback: (success: boolean, data?: any, error?: string) => void;
  }

  export interface TestLiveRTConnectionPayload {
    liveStrategyId: string;
    callback: (success: boolean, result?: any) => void;
  }

  export interface TestTradingChannelPayload {
    channelId: string;
    callback: (success: boolean, result?: any) => void;
  }

  // 获取实盘策略运行时数据Payload
  export interface GetRuntimeDataPayload {
    liveStrategyId: string;
    callback: (success: boolean, data?: any, error?: string) => void;   // 返回的data是数组，每个元素是对象，对象包含strategy_id, strategy_type, signals, latest_signal, total_signals, initial_capital, base_capital, ratio
  }

  export type Payloads = {
    [Types.GET_STRATEGY_LIST]: GetStrategyListPayload;
    [Types.DEPLOY_STRATEGY]: DeployStrategyPayload;
    [Types.STRATEGY_DEPLOYED]: StrategyDeployedPayload;
    [Types.RUN_BACKTEST]: RunBacktestPayload;
    [Types.UPDATE_STRATEGY]: StrategyUpdatePayload;
    [Types.BACKTEST_RESULT]: BacktestResultPayload;
    [Types.GET_LIVE_STRATEGIES]: GetLiveStrategiesPayload;
    [Types.LIVE_STRATEGY_LIST_UPDATED]: LiveStrategyListUpdatedPayload;
    [Types.START_LIVE_STRATEGY]: StartLiveStrategyPayload;
    [Types.LIVE_STRATEGY_STARTED]: LiveStrategyStartedPayload;
    [Types.STRATEGY_LIST_UPDATED]: undefined;
    [Types.STOP_LIVE_STRATEGY]: StopLiveStrategyPayload;
    [Types.LIVE_STRATEGY_STOPPED]: LiveStrategyStoppedPayload;
    [Types.GET_LIVE_STRATEGY_LOGS]: GetLiveStrategyLogsPayload;
    [Types.LIVE_STRATEGY_LOGS_UPDATED]: LiveStrategyLogsUpdatedPayload;
    [Types.SHOW_STRATEGY_DETAILS]: ShowStrategyDetailsPayload;
    [Types.DELETE_LIVE_STRATEGY]: DeleteLiveStrategyPayload;
    [Types.SHOW_LIVE_CONFIG]: ShowLiveConfigPayload;
    [Types.GET_AVAILABLE_TRADING_CHANNELS]: GetAvailableTradingChannelsPayload;
    [Types.GET_TRADING_CHANNEL_CONFIG]: GetTradingChannelConfigPayload;
    [Types.SHOW_TRADING_CHANNEL_CONFIG]: ShowTradingChannelConfigPayload;
    [Types.SHOW_STRATEGY_EDITOR]: StrategyDataForEdit;
    [Types.TEST_PROCESS_ACTIVITY]: TestProcessActivityPayload;
    [Types.TEST_LIVE_RT_CONNECTION]: TestLiveRTConnectionPayload;
  };
}

// 合并所有事件类型到一个联合类型
export type EventTypes =
  | (typeof ChartEvents.Types)[keyof typeof ChartEvents.Types]
  | (typeof ChatEvents.Types)[keyof typeof ChatEvents.Types]
  | (typeof MarketEvents.Types)[keyof typeof MarketEvents.Types]
  | (typeof UserEvents.Types)[keyof typeof UserEvents.Types]
  | (typeof IndicatorEvents.Types)[keyof typeof IndicatorEvents.Types]
  | (typeof RealtimeEvents.Types)[keyof typeof RealtimeEvents.Types]
  | (typeof KeyboardEvents.Types)[keyof typeof KeyboardEvents.Types]
  | (typeof StrategyEvents.Types)[keyof typeof StrategyEvents.Types]
  | (typeof SymbolSelectEvents.Types)[keyof typeof SymbolSelectEvents.Types]; // 添加选股事件类型

// 合并所有事件的 Payload 定义到一个映射类型
export type EventPayloadMap =
  & ChatEvents.Payloads
  & MarketEvents.Payloads
  & UserEvents.Payloads
  & ChartEvents.Payloads
  & IndicatorEvents.Payloads
  & RealtimeEvents.Payloads
  & KeyboardEvents.Payloads
  & StrategyEvents.Payloads
  & SymbolSelectEvents.Payloads; // 添加选股事件payloads

// 最终导出的事件 Payload 联合类型
export type EventPayloads = EventPayloadMap[keyof EventPayloadMap];

// 选股模块事件
export namespace SymbolSelectEvents {
  export const Types = {
    START_STOCK_SELECTION: 'symbol_select:start',
    STOP_STOCK_SELECTION: 'symbol_select:stop',
    STOCK_SELECTION_STARTED: 'symbol_select:started',
    STOCK_SELECTION_STOPPED: 'symbol_select:stopped',
    STOCK_SELECTION_PROGRESS: 'symbol_select:progress',
    STOCK_SELECTION_COMPLETED: 'symbol_select:completed',
    STOCK_SELECTION_ERROR: 'symbol_select:error',
    STOCK_SELECTION_TEST: 'symbol_select:test',
  } as const;

  // 开始选股请求
  export interface StartStockSelectionPayload {
    candidateType: number; // 0=A股, 1=期货, 2=美股
    signalConfig: {
      signalClassName: string;
      parameters: Record<string, any>;
    };
  }

  // 停止选股请求
  export interface StopStockSelectionPayload {
    taskId: string; // 任务ID
  }

  // 选股开始响应
  export interface StockSelectionStartedPayload {
    taskId: string;
    message: string;
  }

  // 选股停止响应
  export interface StockSelectionStoppedPayload {
    taskId: string;
    message: string;
    selectedSymbols?: any[];
    totalSymbols?: number;
    processedSymbols?: number;
    selectedCount?: number;
    executionTime?: number;
  }

  // 选股进度更新
  export interface StockSelectionProgress {
    taskId: string;
    stage: string; // 阶段名称
    current?: number; // 当前进度
    total?: number; // 总数量
    selectedCount?: number; // 已选中数量
    symbolInfo?: string; // 当前处理的品种信息
    progress?: number; // 进度百分比 (0-100)
  }

  // 选股结果
  export interface StockSelectionResult {
    taskId: string;
    selectedSymbols: Array<{
      code: string;
      name: string;
      market: string;
      [key: string]: any;
    }>;
    totalSymbols: number;
    processedSymbols: number;
    selectedCount: number;
    executionTime: number; // 执行时间（毫秒）
  }

  // 选股状态
  export interface StockSelectionStatus {
    taskId: string;
    status: 'running' | 'completed' | 'stopped' | 'error';
    progress: StockSelectionProgress;
    result?: StockSelectionResult;
    error?: string;
  }

  // 选股错误
  export interface StockSelectionError {
    taskId?: string;
    message: string;
    details?: string;
  }

  export type Payloads = {
    [Types.START_STOCK_SELECTION]: StartStockSelectionPayload;
    [Types.STOP_STOCK_SELECTION]: StopStockSelectionPayload;
    [Types.STOCK_SELECTION_STARTED]: StockSelectionStartedPayload;
    [Types.STOCK_SELECTION_STOPPED]: StockSelectionStoppedPayload;
    [Types.STOCK_SELECTION_PROGRESS]: StockSelectionProgress;
    [Types.STOCK_SELECTION_COMPLETED]: StockSelectionResult;
    [Types.STOCK_SELECTION_ERROR]: StockSelectionError;
    [Types.STOCK_SELECTION_TEST]: void; // 测试事件不需要载荷
  };
}
