<template>
  <div class="stock-selection-progress">
    <div class="progress-bar">
      <el-progress :percentage="progress" />
    </div>
    <div class="progress-info">
      <span>当前: {{ current }}</span>
      <span>总数: {{ total }}</span>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    progress() {
      return this.$store.state.selectionProgress.progress;
    },
    current() {
      return this.$store.state.selectionProgress.current;
    },
    total() {
      return this.$store.state.selectionProgress.total;
    }
  }
};
</script>

<style scoped>
.stock-selection-progress {
  padding: 10px;
  border: 1px solid #ccc;
}

.progress-info {
  margin-top: 10px;
}
</style>