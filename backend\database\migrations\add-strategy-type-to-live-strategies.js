/**
 * 数据库迁移：为 live_strategies 表添加 strategytype 字段
 */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 添加 strategytype 字段
    await queryInterface.addColumn('live_strategies', 'strategytype', {
      type: Sequelize.STRING(50),
      allowNull: false,
      defaultValue: 'portfolio', // 默认值为 portfolio
      comment: '策略类型'
    });

    console.log('[数据库迁移] 已为 live_strategies 表添加 strategytype 字段');
  },

  down: async (queryInterface, Sequelize) => {
    // 删除 strategytype 字段
    await queryInterface.removeColumn('live_strategies', 'strategytype');
    
    console.log('[数据库迁移] 已从 live_strategies 表删除 strategytype 字段');
  }
}; 