#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
品种代码解析工具
解析 Wonder Trader 格式的品种代码
"""

import re
import logging
from typing import Dict, Optional, Any

class SymbolParser:
    """品种代码解析器"""

    def __init__(self):
        """初始化解析器"""
        self.logger = logging.getLogger(__name__)

        # 市场类型映射
        self.market_map = {
            'STOCK': 'stock',
            'ETF': 'etf',
            'FUTURE': 'future',
            'OPTION': 'option',
            'CRYPTO': 'crypto',
            'CPT': 'crypto',  # 别名
            'INDEX': 'index'
        }

        # 交易所映射
        self.exchange_map = {
            'SSE': 'sh',    # 上海证券交易所
            'SZSE': 'sz',   # 深圳证券交易所
            'SHFE': 'shfe', # 上海期货交易所
            'DCE': 'dce',   # 大连商品交易所
            'CZCE': 'czce', # 郑州商品交易所
            'CFFEX': 'cffex', # 中国金融期货交易所
            'INE': 'ine',   # 上海国际能源交易中心
            'GFEX': 'gfex', # 广州期货交易所
            'HKEX': 'hk',   # 香港交易所
            'NASDAQ': 'us', # 纳斯达克
            'NYSE': 'us',   # 纽约证券交易所
            'AMEX': 'us',   # 美国证券交易所
            'OKEX': 'okex', # OKEx交易所
            'BINANCE': 'binance', # 币安交易所
            'HUOBI': 'huobi' # 火币交易所
        }

    def parse(self, symbol_code: str) -> Optional[Dict[str, Any]]:
        """
        解析品种代码
        格式: EXCHANGE.MARKET.CODE
        例如: SHFE.FUTURE.rb2310
        """
        try:
            # 分割代码
            parts = symbol_code.split('.')
            if len(parts) != 3:
                self.logger.warning(f"无效的品种代码格式: {symbol_code}")
                return None

            exchange, market, code = parts

            # 转换市场类型
            market_type = self.market_map.get(market.upper())
            if not market_type:
                self.logger.warning(f"未知的市场类型: {market}")
                return None

            # 转换交易所
            exchange_code = self.exchange_map.get(exchange.upper())
            if not exchange_code:
                self.logger.warning(f"未知的交易所: {exchange}")
                return None

            # 返回解析结果
            return {
                'symbol': code,
                'market': market_type,
                'exchange': exchange_code,
                'original': symbol_code
            }

        except Exception as e:
            self.logger.error(f"解析品种代码时出错: {e}")
            return None