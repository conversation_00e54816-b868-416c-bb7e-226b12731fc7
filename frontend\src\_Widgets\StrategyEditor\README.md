# StrategyEditor Widget

## 概述

`StrategyEditor` 是一个独立的 React 组件，使用 Ant Design 的 `Modal` 实现，用于编辑策略的核心参数，如名称、品种列表和回测范围。

它通过全局事件总线 (`EventBus`) 与应用程序的其他部分进行通信，实现了低耦合。

## 功能

-   **显示**: 通过监听 `StrategyEvents.Types.SHOW_STRATEGY_EDITOR` 事件来显示编辑模态框，并加载传入的策略数据进行初始化。
-   **编辑**: 提供表单让用户修改策略名称、品种列表 (使用 Select tags 模式) 和回测日期范围 (使用 DatePicker RangePicker)。
-   **验证**: 在保存时对输入进行校验：
    -   策略名称不能为空。
    -   品种列表不能为空。
    -   回测开始日期不能晚于结束日期。
-   **保存**: 验证通过后，发出 `StrategyEvents.Types.UPDATE_AND_RUN_STRATEGY` 事件，携带更新后的策略数据。实际的保存和回测触发由该事件的监听者处理。
-   **取消**: 关闭模态框，重置表单。

## 事件

### 监听事件

-   `StrategyEvents.Types.SHOW_STRATEGY_EDITOR`: 
    -   **Payload**: `StrategyDataForEdit` ({ id: string; name: string; symbols: string[]; backtestStartDate: string | null; backtestEndDate: string | null; })
    -   **作用**: 触发编辑器的显示，并传入要编辑的策略的初始数据。

### 发出事件

-   `StrategyEvents.Types.UPDATE_AND_RUN_STRATEGY`: 
    -   **Payload**: `StrategyUpdatePayload` ({ id: string; name: string; symbols: string[]; backtestStartDate: string | null; backtestEndDate: string | null; })
    -   **作用**: 当用户点击保存且验证通过时发出，通知系统更新策略信息并触发该策略的回测。

## 使用

在需要触发策略编辑的地方，通过 `EventBus` 发出 `SHOW_STRATEGY_EDITOR` 事件并传递策略数据即可。

```javascript
import { EventBus } from '@/events/eventBus';
import { StrategyEvents } from '@/events/events';

// ...

const strategyDataToEdit = {
  id: 'some-uuid-or-id',
  name: 'Original Strategy Name',
  symbols: ['600036.SH', '000001.SZ'],
  backtestStartDate: '2023-01-01T00:00:00.000Z',
  backtestEndDate: '2024-01-01T00:00:00.000Z'
};

EventBus.emit(StrategyEvents.Types.SHOW_STRATEGY_EDITOR, strategyDataToEdit);
```

## 注意

-   组件依赖 `dayjs` 库来处理日期。确保项目中已安装并配置。
-   需要在使用此组件前，在 `StrategyEvents.Types` 中定义 `SHOW_STRATEGY_EDITOR` 和 `UPDATE_AND_RUN_STRATEGY` 事件常量。
-   `StrategyDataForEdit` 和 `StrategyUpdatePayload` 类型定义在 `@/events/events.ts` 的 `StrategyEvents` 命名空间下。
-   品种列表的校验目前比较基础，可以根据需要添加更复杂的格式校验。 