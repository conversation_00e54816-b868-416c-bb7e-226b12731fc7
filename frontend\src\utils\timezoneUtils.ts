/**
 * 时区工具函数
 * 提供与时区相关的实用函数
 */

/**
 * 获取指定时区的偏移量（单位：小时）
 * @param timezone 时区名称，如 'Asia/Shanghai', 'America/New_York'
 * @returns 时区偏移量（小时）
 */
export const getTimezoneOffset = (timezone: string): number => {
  try {
    // 创建当前时间的 Date 对象
    const now = new Date();
    // 获取指定时区的偏移量（单位：分钟）
    const offset = new Date(now.toLocaleString('en-US', { timeZone: timezone })).getTimezoneOffset();
    // 转换为小时并返回
    return -offset / 60;
  } catch (error) {
    // 如果时区无效，返回默认偏移量（中国标准时间 UTC+8）
    console.warn(`Invalid timezone: ${timezone}, using default offset (8)`);
    return 8;
  }
}; 