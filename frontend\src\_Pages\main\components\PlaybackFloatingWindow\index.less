.playback-floating-window {
  background: rgba(var(--component-background-rgb), 0.9);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  user-select: none;
  touch-action: none; // 防止触摸时触发页面滚动
  backdrop-filter: blur(8px); // 添加背景模糊效果

  .floating-window-content {
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;

    .ant-space {
      align-items: center;
    }

    select {
      background: rgba(var(--component-background-rgb), 0.8);
      color: var(--text-color);
      font-size: 12px;
      padding: 2px 4px;
      outline: none;

      &:focus {
        border-color: var(--primary-color);
      }

      option {
        background: var(--component-background);
        color: var(--text-color);
      }
    }
  }

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
} 