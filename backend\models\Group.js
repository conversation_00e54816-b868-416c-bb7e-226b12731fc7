module.exports = (sequelize, DataTypes) => {
  const Group = sequelize.define('Group', {
    groupid: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '组合ID'
    },
    userid: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '用户ID'
    },
    createdate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否激活'
    }
  }, {
    tableName: 'groups',
    timestamps: false
  });

  Group.associate = function(models) {
    Group.belongsTo(models.User, {
      foreignKey: 'userid',
      as: 'user'
    });
  };

  return Group;
};
