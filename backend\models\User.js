const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  /**
   * 用户模型
   * @typedef {object} UserAttributes
   * @property {number} id - 用户ID (主键)
   * @property {string} username - 用户名 (唯一, 3-20位字母数字)
   * @property {string} email - 邮箱 (唯一, 邮箱格式)
   * @property {string} password - 密码 (8-100位)
   * @property {'admin'|'user'} role - 角色 (默认 'user')
   * @property {'active'|'inactive'|'suspended'} status - 状态 (默认 'active')
   * @property {Date} createdat - 创建时间 (默认当前时间)
   * @property {Date} [lastLogin] - 最后登录时间
   * @property {string} avatar - 头像URL (默认 'default-avatar.png')
   */

  /** @type {import('sequelize').ModelStatic<import('sequelize').Model<UserAttributes>>} */
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 20],
        // isAlphanumeric: true // 暂时注释掉，以允许非字母数字用户名，如有需要可取消注释
      }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING(100),
      allowNull: false,
      // 密码长度验证建议在注册逻辑中处理，模型层面可以不强制
      // validate: {
      //   len: [8, 100]
      // }
    },
    role: {
      type: DataTypes.ENUM('admin', 'user'),
      defaultValue: 'user'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended'),
      defaultValue: 'active'
    },
    createdat: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    lastLogin: {
      type: DataTypes.DATE,
      allowNull: true
    },
    avatar: {
      type: DataTypes.STRING,
      defaultValue: 'default-avatar.png'
    }
  }, {
    tableName: 'users', // 明确指定表名
    timestamps: false, // 不使用 Sequelize 自动添加的 createdAt 和 updatedAt 字段
    hooks: {
      // 在保存前自动哈希密码
      beforeSave: async (user) => {
        if (user.changed('password')) {
          const salt = await bcrypt.genSalt(10);
          user.password = await bcrypt.hash(user.password, salt);
        }
      }
    }
  });

  /**
   * 比较候选密码与存储的哈希密码
   * @param {string} candidatePassword - 候选密码
   * @returns {Promise<boolean>} - 密码是否匹配
   */
  User.prototype.comparePassword = async function (candidatePassword) {
    // 检查 this.password 是否存在，以防用户对象不完整
    if (!this.password) {
      return false;
    }
    return await bcrypt.compare(candidatePassword, this.password);
  };

  /**
   * 定义模型关联的方法
   * @param {object} models - 包含所有模型的对象
   */
  User.associate = function(models) {
    // 例如: User.hasMany(models.Post, { foreignKey: 'userId', as: 'posts' });
    // 此处可以定义 User 与其他模型的关联
    User.hasMany(models.ChatMessage, { foreignKey: 'senderid', as: 'sentMessages' });
    User.hasMany(models.ChatSessionMember, { foreignKey: 'userid', as: 'sessionMemberships' });
    User.hasMany(models.StrategyOwnership, { foreignKey: 'owner_id', as: 'ownedStrategies' });
    User.hasMany(models.DrawingLine, { foreignKey: 'userId', as: 'drawingLines' });
    User.hasMany(models.Shape, { foreignKey: 'userId', as: 'shapes' });
    User.hasMany(models.IndicatorList, { foreignKey: 'userId', as: 'indicatorLists' });

    // 新增：一个用户可以拥有多个股票池
    User.hasMany(models.Pool, {
      foreignKey: 'user_id',
      as: 'pools' // 别名
                   // 这里不需要 ON DELETE CASCADE，因为 pools 表的外键定义会处理
    });

    // 新增：一个用户可以拥有多个实盘策略
    User.hasMany(models.LiveStrategy, {
      foreignKey: 'userId',
      as: 'liveStrategies'
    });
  };

  // 在 toJSON 中移除密码字段
  User.prototype.toJSON = function () {
    const values = { ...this.get() }; // 使用扩展运算符复制
    delete values.password;
    return values;
  };


  return User;
};
