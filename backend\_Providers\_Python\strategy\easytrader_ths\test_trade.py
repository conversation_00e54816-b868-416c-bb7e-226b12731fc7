#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths测试交易脚本

该脚本提供了测试easytrader_ths交易功能的方法。
"""

import requests
import json
import argparse
import sys

def test_trade(username, action, params=None):
    """
    测试交易功能
    
    Args:
        username: 用户名
        action: 操作，如'balance', 'position', 'buy', 'sell'等
        params: 操作参数，如买入/卖出时的代码、价格、数量等
    """
    # 发送测试请求
    try:
        response = requests.post(
            "http://localhost:8000/api/test_trade",
            json={
                "username": username,
                "action": action,
                "params": params
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"操作: {action}")
            if params:
                print(f"参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
            print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试easytrader_ths交易功能')
    parser.add_argument('--username', default='quantquart_user', help='用户名')
    parser.add_argument('--action', required=True, choices=[
        'balance', 'position', 'today_entrusts', 'today_trades',
        'buy', 'sell', 'cancel', 'refresh'
    ], help='操作')
    parser.add_argument('--code', help='股票代码，如sh600000')
    parser.add_argument('--price', type=float, help='价格')
    parser.add_argument('--amount', type=float, help='数量')
    parser.add_argument('--entrust_no', help='委托编号，用于撤单')
    
    args = parser.parse_args()
    
    # 构建参数
    params = None
    if args.action in ['buy', 'sell']:
        if not all([args.code, args.price, args.amount]):
            print(f"{args.action}操作需要指定code、price和amount参数")
            sys.exit(1)
        params = {
            "code": args.code,
            "price": args.price,
            "amount": args.amount
        }
    elif args.action == 'cancel':
        if not args.entrust_no:
            print("cancel操作需要指定entrust_no参数")
            sys.exit(1)
        params = {
            "entrust_no": args.entrust_no
        }
    
    # 执行测试
    test_trade(args.username, args.action, params)

if __name__ == "__main__":
    main()
