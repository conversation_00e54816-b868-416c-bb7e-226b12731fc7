# 框架测试策略配置
# 专门用于验证实盘框架是否正常工作

strategies:
  # 框架测试策略 - 使用5分钟线快速验证
  - id: "framework_test_5min"
    yaml: |
      type: "FrameworkTestCTA"
      name: "框架测试策略-5分钟"
      universe:
        - "SSE.600036"
        - "SZSE.000001"
      
      parameters:
        period: "min5"  # 5分钟线，快速验证
        bar_count: 30
        test_mode: true
  
  # 框架测试策略 - 使用日线（正式验证）
  - id: "framework_test_daily"
    yaml: |
      type: "FrameworkTestCTA"
      name: "框架测试策略-日线"
      universe:
        - "SSE.600036"
      
      parameters:
        period: "day"   # 日线
        bar_count: 50
        test_mode: true
