// 实盘策略信息接口
export interface LiveStrategyInfo {
    id: string; // 实盘策略实例的唯一ID
    strategyId: string; // 关联的策略模板ID
    name: string;
    strategyType: string; // 策略类型
    status: 'running' | 'stopped' | 'error';
    accountId: string; // 关联的交易通道ID
    initialCapital: number; // 初始资金
    startTime?: string;
    lastUpdateTime?: string;
    // --- 新增: 手续费率 (从配置来) ---
    commissionRate?: number;
    // --- 新增: 风控设置 ---
    riskSettings?: {
      maxOrderSize?: number;
      maxDailyTrades?: number;
      stopLossPercent?: number;
    };
    performance?: {
      totalReturn: number;
      dailyReturn: number;
      // --- 新增: 逻辑资本 (由后端计算更新) ---
      logicalCapital?: number;
      // --- 结束新增 ---
      positions: Array<{
        symbol: string;
        volume: number;
        price: number;
        profit: number;
      }>;
    };
    // 可以添加其他信息，如错误消息等
    errorMessage?: string;
  }