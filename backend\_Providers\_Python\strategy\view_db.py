# view_db.py
import duckdb
import json
import os
import pandas as pd # 使用 pandas 更好地展示
import argparse

# 数据库文件路径 (相对于脚本所在目录)
BACKTEST_DB = 'backtest_results.duckdb'
CUSTOM_DB = 'custom_strategy_list.duckdb'
BACKTEST_TABLE = 'backtest_results'
CUSTOM_TABLE = 'custom_strategies'

# 解析命令行参数
parser = argparse.ArgumentParser(description='查看策略数据库')
parser.add_argument('--backtest', action='store_true', help='查看回测数据库')
parser.add_argument('--custom', action='store_true', help='查看自定义策略数据库')
args = parser.parse_args()

# 确定要查看的数据库
db_file = BACKTEST_DB
table_name = BACKTEST_TABLE
if args.custom:
    db_file = CUSTOM_DB
    table_name = CUSTOM_TABLE
elif not args.backtest and not args.custom:
    parser.print_help()
    exit()

# 检查文件是否存在
if not os.path.exists(db_file):
    print(f"错误：数据库文件 '{db_file}' 不存在。请确保脚本在正确的目录下运行。")
    exit()

try:
    # 连接数据库
    con = duckdb.connect(database=db_file, read_only=True) # 以只读方式打开

    # --- 选择你想执行的查询 ---

    # 选项 A: 获取所有数据为 Pandas DataFrame (推荐，易于查看)
    print(f"\n--- 所有数据 (DataFrame) ---")
    df = con.execute(f"SELECT * FROM {table_name}").fetchdf()
    pd.set_option('display.max_rows', None) # 显示所有行
    pd.set_option('display.max_columns', None) # 显示所有列
    pd.set_option('display.width', 1000) # 更宽的显示
    
    # 添加行号
    df.index = df.index + 1
    print(df)
    
    # 统一交互式查询功能
    print("\n--- 交互式查询 ---")
    print("输入记录编号查看详情 (输入0退出):")
    while True:
        try:
            selection = int(input("请输入编号: "))
            if selection == 0:
                break
            if selection < 1 or selection > len(df):
                print(f"错误：编号 {selection} 超出范围 (1-{len(df)})")
                continue
            
            strategy_id = df.iloc[selection-1]['strategy_id']
            
            # 根据数据库类型显示不同内容
            if args.custom:
                result = con.execute(
                    "SELECT content_yaml FROM {} WHERE strategy_id = ?".format(table_name),
                    (strategy_id,)
                ).fetchone()
                content_field = 'content_yaml'
            else:
                result = con.execute(
                    "SELECT result_data FROM {} WHERE strategy_id = ? ORDER BY run_timestamp DESC LIMIT 1".format(table_name),
                    (strategy_id,)
                ).fetchone()
                content_field = 'result_data'
            
            if result and result[0]:
                print(f"\n--- 策略 '{strategy_id}' 的完整内容 ---")
                # 统一处理内容显示
                try:
                    result_dict = json.loads(result[0])
                    # 添加回测数据展示逻辑
                    if not args.custom:
                        run_time = df.iloc[selection-1]['run_timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                        print(f"\n=== 回测概要 ===")
                        print(f"执行时间: {run_time}")
                        # 其他回测指标展示...
                    print(result[0])
                except json.JSONDecodeError:
                    print(result[0])
            # 保留删除功能...
            # 添加删除选项
            print("\n输入 'd' 删除此记录，或输入其他内容继续:")
            delete_choice = input().strip().lower()
            if delete_choice == 'd':
                print(f"确认要删除策略 '{strategy_id}' 吗？(y/n)")
                confirm = input().strip().lower()
                if confirm == 'y':
                    try:
                        # 关闭只读连接，重新以读写模式打开
                        con.close()
                        con = duckdb.connect(database=db_file)
                        
                        # 执行删除操作
                        con.execute(
                            f"DELETE FROM {table_name} WHERE strategy_id = ?",
                            (strategy_id,)
                        )
                        print(f"策略 '{strategy_id}' 已删除。")
                        
                        # 刷新数据
                        df = con.execute(f"SELECT * FROM {table_name}").fetchdf()
                        df.index = df.index + 1
                        
                        # 重新设置为只读模式
                        con.close()
                        con = duckdb.connect(database=db_file, read_only=True)
                    except Exception as e:
                        print(f"删除记录时出错: {e}")
                        # 确保重新连接只读模式
                        con = duckdb.connect(database=db_file, read_only=True)
            else:
                print(f"未找到策略 '{strategy_id}' 的内容。")
        except ValueError:
            print("错误：请输入有效的数字编号")
        except Exception as e:
            print(f"查询时发生错误: {e}")

    # 选项 B: 获取特定策略最新的记录并解析 JSON
    strategy_to_view = 'etf_trend_rotation' # 修改为你想要查看的策略名
    if args.custom:
        print(f"\n--- 自定义策略 '{strategy_to_view}' 的内容 ---")
        result = con.execute(
            f"SELECT content_yaml FROM {table_name} WHERE strategy_id = ?",
            (strategy_to_view,)
        ).fetchone()
        if result and result[0]:
            print(result[0])
        else:
            print(f"未找到自定义策略 '{strategy_to_view}' 的记录。")
    else:
        # Only query result_data for backtest database
        print(f"\n--- 策略 '{strategy_to_view}' 最新记录的 result_data ---")
        result = con.execute(
            f"SELECT result_data FROM {table_name} WHERE strategy_id = ? ORDER BY run_timestamp DESC LIMIT 1",
            (strategy_to_view,)
        ).fetchone()

    if result and result[0]:
        try:
            # 处理回测数据
            result_dict = json.loads(result[0])
            
            # 提取关键信息
            run_time = df.iloc[selection-1]['run_timestamp'].strftime('%Y-%m-%d %H:%M:%S') if 'run_timestamp' in df.columns else '无时间记录'
            equity_data = result_dict.get('equity_curve_data', [])
            
            # 输出格式化信息
            print(f"\n=== 回测概要 ===")
            print(f"执行时间: {run_time}")
            
            if len(equity_data) >= 1:
                print(f"初始净值: {equity_data[0]['equity']}")
                print(f"最终净值: {equity_data[-1]['equity']}")
            
            # 保留其他关键指标
            keep_keys = ['annual_return', 'max_drawdown', 'sharpe_ratio']
            summary = {k: result_dict.get(k) for k in keep_keys if k in result_dict}
            print(json.dumps(summary, indent=2, ensure_ascii=False))
            
            # 显示缩略图数据样本
            if 'equity_curve_thumbnail_data' in result_dict:
                print(f"\n--- 净值曲线样本数据（共{len(result_dict['equity_curve_thumbnail_data'])}条）---")
                print(f"\n--- 缩略图数据 (前 5 条) ---")
                print(result_dict['equity_curve_thumbnail_data'][:5])
            else:
                    print("\n--- 缩略图数据: 未找到 ---")

        except json.JSONDecodeError:
            print("错误：无法解析 result_data JSON。")
        except Exception as e:
                print(f"处理数据时发生错误: {e}")
    else:
        print(f"未找到策略 '{strategy_to_view}' 的记录。")

    # 关闭连接
    con.close()

except Exception as e:
    print(f"访问数据库时出错: {e}")
