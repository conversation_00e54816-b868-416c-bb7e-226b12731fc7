const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config.json');

// JWT密钥从配置文件获取
const JWT_SECRET = config.jwt.secret;
const JWT_EXPIRES_IN = config.jwt.expiresIn;

/**
 * Generate JWT token for user
 * @param {import('../models/User').User} user - User instance
 * @returns {string} JWT token
 */
const generateToken = (user) => {
  return jwt.sign(
    { id: user.id, email: user.email },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
};

exports.register = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    //console.log('Registering, password=', password, ' hash=', hashedPassword);

    // Generate username from email
    const username = email.split('@')[0].replace(/[^a-zA-Z0-9]/g, '');
    
    // Create new user
    const user = await User.create({
      email,
      username,
      password: hashedPassword
    });

    // Generate token
    const token = generateToken(user);

    res.status(201).json({
      id: user.id,
      email: user.email,
      token
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Registration failed' });
  }
};

exports.login = async (req, res) => {
  try {
    const { email, password, client_type } = req.body;

    // Find user
    const user = await User.findOne({ where: { email } });
    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials 1' });
    }

    //console.log('输入的密码:', password);
    //console.log('数据库中的哈希密码:', user.password);

    // Check password
    //console.log('Compare password=', password, ' with hash=', user.password);
    
    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      return res.status(401).json({ message: 'Invalid credentials 2' });
    }

    // Generate token
    let token;
    if (client_type === 'easytrader_ths') {
      // 永久token（100年）
      token = jwt.sign(
        { id: user.id, email: user.email, client_type },
        JWT_SECRET,
        { expiresIn: '100y' }
      );
    } else {
      token = generateToken(user);
    }

    res.json({
      id: user.id,
      email: user.email,
      token
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Login failed' });
  }
};
