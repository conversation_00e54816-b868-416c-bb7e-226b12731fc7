import { IndicatorType } from '@/shared_types/market';
import { MACD, RSI, MA, EMA, BOLL, VOL, KDJ, SWMA, WMA } from './index_debutIndicatorsd_based';

// 定义指标映射
const indicatorCatalog: Record<string, any> = {
  [IndicatorType.MACD]: MACD,
  [IndicatorType.RSI]: RSI,
  [IndicatorType.MA]: MA,
  [IndicatorType.EMA]: EMA,
  [IndicatorType.BOLL]: BOLL,
  [IndicatorType.VOL]: VOL,
  [IndicatorType.KDJ]: KDJ,
  [IndicatorType.SWMA]: SWMA,
  [IndicatorType.WMA]: WMA,
  [IndicatorType.SHAPE]: {
    name: 'SHAPE',
    type: IndicatorType.SHAPE,
    lines: [
      { name: 'shape', color: '#FF6B6B' }
    ],
    createCalculator: () => ({}),
    calculate: () => ({}),
    // 特征提取方法：将K线数据转换为1-5的离散序列
    extractFeatures: (klineData: any[]): number[] => {
      if (!klineData || klineData.length === 0) return [];
      
      const closePrices = klineData.map(k => k.close);
      const minPrice = Math.min(...closePrices);
      const maxPrice = Math.max(...closePrices);
      const priceRange = maxPrice - minPrice;
      
      if (priceRange === 0) {
        // 如果价格范围为零，返回中间值3
        return new Array(klineData.length).fill(3);
      }
      
      // 将价格范围分为5个格子，映射到1-5
      return closePrices.map(price => {
        const normalizedPrice = (price - minPrice) / priceRange;
        const gridIndex = Math.floor(normalizedPrice * 5);
        // 确保结果在1-5范围内
        return Math.max(1, Math.min(5, gridIndex + 1));
      });
    }
  }
};

// 获取指标类的函数
export const getIndicatorByType = (type: string) => {
  const indicator = indicatorCatalog[type];
  if (!indicator) {
    throw new Error(`未找到类型为 ${type} 的指标`);
  }
  return indicator;
};
