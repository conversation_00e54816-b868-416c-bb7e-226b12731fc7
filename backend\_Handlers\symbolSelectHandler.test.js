/**
 * symbolSelectHandler.js 单元测试
 * 测试选股处理器的功能
 */

const { EventEmitter } = require('events');
const path = require('path');
const redis = require('redis');
const { spawn } = require('child_process');

// 模拟依赖
jest.mock('redis', () => {
  const mockRedisClient = {
    connect: jest.fn().mockResolvedValue(),
    on: jest.fn(),
    pSubscribe: jest.fn().mockImplementation((channel, callback) => Promise.resolve()),
    publish: jest.fn().mockImplementation((channel, message) => Promise.resolve(1)),
    disconnect: jest.fn().mockResolvedValue()
  };
  
  return {
    createClient: jest.fn().mockReturnValue(mockRedisClient)
  };
});

jest.mock('child_process', () => {
  const mockEventEmitter = {
    on: jest.fn(),
    kill: jest.fn()
  };
  
  return {
    spawn: jest.fn().mockImplementation(() => {
      return {
        stdout: { on: jest.fn() },
        stderr: { on: jest.fn() },
        on: jest.fn(),
        kill: jest.fn()
      };
    })
  };
});

jest.mock('express', () => {
  const mockRouter = {
    post: jest.fn(),
    get: jest.fn()
  };
  
  return {
    Router: jest.fn().mockReturnValue(mockRouter)
  };
});

// 模拟配置文件
jest.mock('../config.json', () => ({
  redis: {
    host: 'localhost',
    port: 6379,
    password: '',
    db: 0
  }
}), { virtual: true });

// 模拟中间件
jest.mock('../middleware/auth', () => ({
  authenticateToken: jest.fn((req, res, next) => next())
}));

// 重置模块缓存，以便重新加载模块
beforeEach(() => {
  jest.resetModules();
  jest.clearAllMocks();
});

describe('SymbolSelectHandler', () => {
  let symbolSelectHandler;
  let mockSocketNamespace;
  
  beforeEach(() => {
    // 模拟Socket.IO命名空间
    mockSocketNamespace = {
      on: jest.fn(),
      emit: jest.fn(),
      sockets: new Map()
    };
    
    // 导入模块
    const { symbolSelectHandler: handler } = require('./symbolSelectHandler');
    symbolSelectHandler = handler;
  });
  
  describe('initSocketIO', () => {
    test('应该正确设置Socket.IO命名空间', () => {
      // 执行
      symbolSelectHandler.initSocketIO(mockSocketNamespace);
      
      // 验证
      expect(mockSocketNamespace.on).toHaveBeenCalledWith('connection', expect.any(Function));
    });
    
    test('当命名空间为空时不应设置', () => {
      // 执行
      symbolSelectHandler.initSocketIO(null);
      
      // 验证
      expect(mockSocketNamespace.on).not.toHaveBeenCalled();
    });
    
    test('应该处理客户端连接事件', () => {
      // 准备
      const mockSocket = {
        id: 'socket-123',
        on: jest.fn(),
        emit: jest.fn(),
        nsp: { name: '/stock-progress' }
      };
      
      // 执行
      symbolSelectHandler.initSocketIO(mockSocketNamespace);
      
      // 获取连接回调
      const connectionCallback = mockSocketNamespace.on.mock.calls[0][1];
      connectionCallback(mockSocket);
      
      // 验证
      expect(mockSocket.on).toHaveBeenCalledWith('subscribe_task', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('unsubscribe_task', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function));
    });
    
    test('应该处理客户端断开连接事件', () => {
      // 准备
      const mockSocket = {
        id: 'socket-123',
        on: jest.fn(),
        emit: jest.fn(),
        nsp: { name: '/stock-progress' }
      };
      
      // 执行
      symbolSelectHandler.initSocketIO(mockSocketNamespace);
      
      // 获取连接回调
      const connectionCallback = mockSocketNamespace.on.mock.calls[0][1];
      connectionCallback(mockSocket);
      
      // 获取断开连接回调
      const disconnectCallback = mockSocket.on.mock.calls.find(call => call[0] === 'disconnect')[1];
      disconnectCallback('transport close');
      
      // 验证断开连接处理 (这里我们只能验证回调被调用，因为它只是记录日志)
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function));
    });
  });
  
  describe('initRoutes', () => {
    test('应该正确设置路由', () => {
      // 执行
      symbolSelectHandler.initRoutes();
      
      // 验证
      expect(symbolSelectHandler.router.post).toHaveBeenCalledWith('/start', expect.any(Function), expect.any(Function));
      expect(symbolSelectHandler.router.post).toHaveBeenCalledWith('/test', expect.any(Function), expect.any(Function));
      expect(symbolSelectHandler.router.get).toHaveBeenCalledWith('/status/:taskId', expect.any(Function), expect.any(Function));
      expect(symbolSelectHandler.router.post).toHaveBeenCalledWith('/stop/:taskId', expect.any(Function), expect.any(Function));
      expect(symbolSelectHandler.router.get).toHaveBeenCalledWith('/tasks', expect.any(Function), expect.any(Function));
    });
  });
  
  describe('handleProgressSignal', () => {
    test('应该正确处理进度信号并更新任务状态', () => {
      // 准备
      const taskId = 'test-task-123';
      const progressData = {
        taskId,
        progress: 50,
        current: 50,
        total: 100,
        selectedCount: 10,
        symbolInfo: '测试品种',
        stage: '测试阶段'
      };
      const message = JSON.stringify(progressData);
      const channel = `stock_selection_progress:${taskId}`;
      
      // 模拟任务存在
      const stockSelectionTasks = new Map();
      stockSelectionTasks.set(taskId, {
        taskId,
        status: 'running',
        progress: 0,
        current: 0,
        total: 0,
        selectedCount: 0,
        symbolInfo: '',
        stage: '开始选股'
      });
      
      // 执行
      symbolSelectHandler.handleProgressSignal(message, channel);
      
      // 验证事件发送
      expect(symbolSelectHandler.emit).toHaveBeenCalledWith('progress', expect.objectContaining({
        taskId,
        progress: 50
      }));
    });
    
    test('当Socket.IO命名空间存在时应该推送进度更新', () => {
      // 准备
      const taskId = 'test-task-123';
      const progressData = {
        taskId,
        progress: 50,
        current: 50,
        total: 100,
        selectedCount: 10,
        symbolInfo: '测试品种',
        stage: '测试阶段'
      };
      const message = JSON.stringify(progressData);
      const channel = `stock_selection_progress:${taskId}`;
      
      // 模拟Socket.IO命名空间和连接的socket
      const mockSocket = {
        id: 'socket-123',
        emit: jest.fn()
      };
      mockSocketNamespace.sockets.set('socket-123', mockSocket);
      symbolSelectHandler.initSocketIO(mockSocketNamespace);
      
      // 执行
      symbolSelectHandler.handleProgressSignal(message, channel);
      
      // 验证
      expect(mockSocket.emit).toHaveBeenCalledWith('progress_update', expect.objectContaining({
        taskId,
        progress: 50
      }));
    });
    
    test('应该处理JSON解析错误', () => {
      // 准备
      const invalidMessage = '{invalid-json';
      const channel = 'stock_selection_progress:test-task';
      
      // 执行 (不应抛出错误)
      expect(() => {
        symbolSelectHandler.handleProgressSignal(invalidMessage, channel);
      }).not.toThrow();
    });
  });
  
  describe('updateTaskProgress', () => {
    test('应该正确更新任务进度', () => {
      // 准备
      const taskId = 'test-task-123';
      const progressData = {
        taskId,
        progress: 75,
        current: 75,
        total: 100,
        selectedCount: 15,
        symbolInfo: '更新的测试品种',
        stage: '更新的测试阶段'
      };
      
      // 模拟任务存在
      const task = {
        taskId,
        status: 'running',
        progress: 50,
        current: 50,
        total: 100,
        selectedCount: 10,
        symbolInfo: '测试品种',
        stage: '测试阶段'
      };
      
      // 获取内部Map
      const stockSelectionTasks = new Map();
      stockSelectionTasks.set(taskId, task);
      
      // 执行
      symbolSelectHandler.updateTaskProgress(progressData);
      
      // 验证
      expect(task.progress).toBe(75);
      expect(task.current).toBe(75);
      expect(task.selectedCount).toBe(15);
      expect(task.symbolInfo).toBe('更新的测试品种');
      expect(task.stage).toBe('更新的测试阶段');
    });
    
    test('当任务不存在时不应更新', () => {
      // 准备
      const taskId = 'non-existent-task';
      const progressData = {
        taskId,
        progress: 75,
        current: 75,
        total: 100,
        selectedCount: 15,
        symbolInfo: '更新的测试品种',
        stage: '更新的测试阶段'
      };
      
      // 执行 (不应抛出错误)
      expect(() => {
        symbolSelectHandler.updateTaskProgress(progressData);
      }).not.toThrow();
    });
  });
});