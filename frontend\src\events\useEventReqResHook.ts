import { useEffect } from 'react';
import { EventBus, Events } from './eventBus';

function useEventRequestHook<K1 extends keyof Events, K2 extends keyof Events>({
  requestEvent,
  requestPayload,
  responseEvent,
  onResponse,
}: {
  requestEvent: K1; // 请求事件名称，必须是 Events 的键
  requestPayload: Events[K1]; // 请求事件的 payload，必须是 Events[K1] 的类型
  responseEvent: K2; // 回应事件名称，必须是 Events 的键
  onResponse: (responsePayload: Events[K2]) => void; // 监听回应事件的函数
}) {
  useEffect(() => {
    // 发出请求事件
    EventBus.emit(requestEvent, requestPayload);

    // 监听回应事件
    const handleResponse = (responsePayload: Events[K2]) => {
      onResponse(responsePayload);
    };
    EventBus.on(responseEvent, handleResponse);

    // 清理监听
    return () => {
      EventBus.off(responseEvent, handleResponse);
    };
  }, [requestEvent, requestPayload, responseEvent, onResponse]);
}

export default useEventRequestHook;