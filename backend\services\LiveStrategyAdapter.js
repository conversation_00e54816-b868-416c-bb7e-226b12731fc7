const LiveGroupManager = require('./LiveGroupManager');
const { LiveStrategy } = require('../models');

/**
 * 实盘策略适配器 - 保持前端API接口不变，底层使用新的组合架构
 */
class LiveStrategyAdapter {
  constructor() {
    // 用户组合管理器缓存
    this.groupManagers = new Map(); // userId -> LiveGroupManager
  }

  /**
   * 获取用户的组合管理器
   */
  async getUserGroupManager(userId, groupId) {
    const key = `${userId}_${groupId}`;
    if (!this.groupManagers.has(key)) {
      const manager = new LiveGroupManager(userId, groupId);
      await manager.initialize();
      this.groupManagers.set(key, manager);
    }
    return this.groupManagers.get(key);
  }

  /**
   * 获取用户的实盘策略列表（前端视角：单个策略列表）
   * 实际实现：从组合中获取策略列表
   */
  async getLiveStrategies(userId) {
    try {
      console.log(`[适配器] 获取用户${userId}的实盘策略列表`);

      // 从数据库获取用户的策略
      const strategies = await LiveStrategy.findAll({
        where: { userId },
        order: [['lastUpdateTime', 'DESC']]
      });

      // 转换为前端期望的格式
      const result = strategies.map(strategy => {
        const strategyData = strategy.toJSON();
        
        // 策略状态就是数据库中记录的状态
        const strategyStatus = strategyData.status || 'stopped';

        // 构建前端需要的结构
        const transformedData = {
          id: strategyData.id,
          strategyId: strategyData.strategyId,
          name: strategyData.name,
          strategyType: strategyData.strategyType,
          status: strategyStatus,
          accountId: strategyData.accountId,
          initialCapital: strategyData.initialCapital,
          startTime: strategyData.startTime,
          lastUpdateTime: strategyData.lastUpdateTime,
          commissionRate: strategyData.commissionRate,
          performance: {
            totalReturn: 0, // TODO: 从组合盈亏计算
            dailyReturn: 0,
            logicalCapital: strategyData.initialCapital,
            positions: [] // TODO: 从组合持仓筛选
          },
          timeframe: strategyData.timeframe
        };

        // 移除详细数据输出，只保留核心信息
        if (strategies.length > 0 && strategy === strategies[0]) {
          // 移除详细数据输出，只保留核心信息
        }

        return transformedData;
      });

      console.log(`[适配器] 返回${result.length}个策略`);
      return { success: true, data: result };

    } catch (error) {
      console.error(`[适配器] 获取策略列表失败:`, error);
      return { success: false, error: '获取策略列表失败' };
    }
  }

  /**
   * 启动策略（前端视角：启动单个策略）
   * 实际实现：透明重启组合
   */
  async startStrategy(userId, groupId, strategyId, token) {
    try {
      console.log(`[适配器] 用户${userId}组合${groupId}请求启动策略${strategyId}`);

      // 获取组合管理器
      const groupManager = await this.getUserGroupManager(userId, groupId);
      
      // 调用组合管理器的启动策略方法，传入token
      const result = await groupManager.startStrategy(strategyId, token);
      
      console.log(`[适配器] 策略${strategyId}启动结果:`, result);
      return result;

    } catch (error) {
      console.error(`[适配器] 启动策略${strategyId}失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 停止策略（前端视角：停止单个策略）
   * 实际实现：透明重启组合
   */
  async stopStrategy(userId, strategyId) {
    try {
      console.log(`[适配器] 用户${userId}请求停止策略${strategyId}`);

      // 首先查找策略所在的组合
      const { GroupStrategy } = require('../models');
      const groupStrategy = await GroupStrategy.findOne({
        where: { strategyid: strategyId },
        order: [['deployedat', 'DESC']] // 获取最近部署的记录
      });

      if (!groupStrategy) {
        throw new Error(`策略${strategyId}未部署到任何组合中`);
      }

      const groupId = groupStrategy.groupid;
      console.log(`[适配器] 找到策略${strategyId}所在的组合: ${groupId}`);

      // 获取组合管理器
      const groupManager = await this.getUserGroupManager(userId, groupId);
      
      // 调用组合管理器的停止策略方法
      const result = await groupManager.stopStrategy(strategyId);
      
      console.log(`[适配器] 策略${strategyId}停止结果:`, result);
      return result;

    } catch (error) {
      console.error(`[适配器] 停止策略${strategyId}失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 停止整个实盘组合项目
   * 实现：调用组合管理器的stopGroup方法，退订所有品种
   */
  async stopGroup(userId, groupId) {
    try {
      console.log(`[适配器] 用户${userId}请求停止整个实盘组合项目${groupId}`);

      // 获取组合管理器
      const groupManager = await this.getUserGroupManager(userId, groupId);
      
      // 调用组合管理器的停止组合方法
      const result = await groupManager.stopGroup();
      
      console.log(`[适配器] 组合${groupId}停止结果:`, result);
      return result;

    } catch (error) {
      console.error(`[适配器] 停止组合${groupId}失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取策略持仓（前端视角：单个策略的持仓）
   * 实际实现：从组合持仓中筛选
   */
  async getStrategyPositions(userId, groupId, strategyId) {
    try {
      console.log(`[适配器] 获取组合${groupId}策略${strategyId}的持仓`);

      // 获取组合管理器
      const groupManager = await this.getUserGroupManager(userId, groupId);
      
      // 调用组合管理器的获取策略持仓方法
      const positions = await groupManager.getStrategyPositions(strategyId);
      
      return { success: true, data: positions };

    } catch (error) {
      console.error(`[适配器] 获取策略${strategyId}持仓失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取策略盈亏（前端视角：单个策略的盈亏）
   * 实际实现：从组合盈亏中计算
   */
  async getStrategyPnl(userId, groupId, strategyId) {
    try {
      console.log(`[适配器] 获取组合${groupId}策略${strategyId}的盈亏`);

      // 获取组合管理器
      const groupManager = await this.getUserGroupManager(userId, groupId);
      
      // 调用组合管理器的获取策略盈亏方法
      const pnl = await groupManager.getStrategyPnl(strategyId);
      
      return { success: true, data: pnl };

    } catch (error) {
      console.error(`[适配器] 获取策略${strategyId}盈亏失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 部署策略到实盘（前端视角：部署单个策略）
   * 实际实现：创建策略记录，但不立即启动
   */
  async deployStrategy(userId, username, deployData) {
    try {
      const { strategyId, strategyType, accountId, initialCapital, commissionRate, riskSettings } = deployData;
      
      console.log(`[适配器] 用户${username}部署策略${strategyId}，类型：${strategyType}`);

      // 获取策略YAML（这里需要调用原有的逻辑）
      const strategyYaml = await this._getStrategyYaml(strategyId);
      if (!strategyYaml) {
        throw new Error(`无法获取策略${strategyId}的YAML配置`);
      }

      // 解析策略名称
      const strategyName = this._parseStrategyName(strategyYaml, strategyId);

      // 生成唯一ID
      const { v4: uuidv4 } = require('uuid');
      const liveStrategyId = uuidv4();

      // 解析策略YAML，提取 data_freq 字段作为 timeframe
      let timeframe = null;
      let commodity = null;
      try {
        const yaml = require('js-yaml');
        const parsedYaml = yaml.load(strategyYaml);
        if (parsedYaml && parsedYaml.data_freq) {
          timeframe = String(parsedYaml.data_freq).trim();
        }
        // === 新增：提取 trading_type 字段 ===
        if (parsedYaml && parsedYaml.trading_type) {
          commodity = String(parsedYaml.trading_type).trim(); // etf/stock/crypto/option
        } else {
          throw new Error('策略yaml缺少trading_type字段');
        }
      } catch (e) {
        console.warn('[适配器] 解析策略YAML提取data_freq或trading_type失败:', e);
        throw e; // 这里直接抛出，防止插入null
      }

      // 创建实盘策略记录
      const liveStrategy = {
        id: liveStrategyId,
        userId,
        username,
        strategyId,
        name: `实盘-${strategyName}`,
        strategyType,
        status: 'stopped',
        accountId,
        initialCapital,
        commissionRate: commissionRate || 0.0003,
        riskSettings: riskSettings || {
          maxOrderSize: 100,
          maxDailyTrades: 10,
          stopLossPercent: 5
        },
        createdAt: new Date(),
        lastUpdateTime: new Date(),
        yaml: strategyYaml,
        timeframe,
        commodity // === 新增 ===
      };

      // 保存到数据库
      await LiveStrategy.create(liveStrategy);

      console.log(`[适配器] 策略${liveStrategyId}部署成功`);
      return {
        success: true,
        message: '策略已成功部署到实盘',
        data: { id: liveStrategyId }
      };

    } catch (error) {
      console.error(`[适配器] 部署策略失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 删除策略
   */
  async deleteStrategy(userId, strategyId) {
    try {
      console.log(`[适配器] 用户${userId}删除策略${strategyId}`);

      // 检查策略是否存在且属于该用户
      const strategy = await LiveStrategy.findOne({
        where: { id: strategyId, userId }
      });

      if (!strategy) {
        throw new Error('策略不存在或无权操作');
      }

      // 如果策略正在运行，先停止
      if (strategy.status === 'running') {
        await this.stopStrategy(userId, strategyId);
      }

      // 先删除组合关联记录
      const { GroupStrategy } = require('../models');
      await GroupStrategy.destroy({
        where: { strategyId: strategyId }
      });
      console.log(`[适配器] 已删除策略${strategyId}的组合关联记录`);

      // 从数据库删除策略
      await LiveStrategy.destroy({
        where: { id: strategyId }
      });

      console.log(`[适配器] 策略${strategyId}删除成功`);
      return { success: true, message: '策略已成功删除' };

    } catch (error) {
      console.error(`[适配器] 删除策略${strategyId}失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取策略详情
   */
  async getStrategyDetails(userId, strategyId) {
    try {
      console.log(`[适配器] 获取策略${strategyId}详情`);

      // 从数据库获取策略
      const strategy = await LiveStrategy.findOne({
        where: { id: strategyId, userId }
      });

      if (!strategy) {
        throw new Error('策略不存在或无权查看');
      }

      // 查找策略所在的组合
      const { GroupStrategy } = require('../models');
      const groupStrategy = await GroupStrategy.findOne({
        where: { strategyid: strategyId },
        order: [['deployedat', 'DESC']] // 获取最近部署的记录
      });

      let groupStatus = null;
      if (groupStrategy) {
        // 如果策略已部署到组合，获取组合状态
        const groupId = groupStrategy.groupid;
        const groupManager = await this.getUserGroupManager(userId, groupId);
        groupStatus = await groupManager.getGroupStatus();
      }

      // 构建详情数据
      const details = {
        ...strategy.toJSON(),
        groupStatus: groupStatus,
        // TODO: 添加更多详情信息
      };

      return { success: true, data: details };

    } catch (error) {
      console.error(`[适配器] 获取策略${strategyId}详情失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取策略调试信息
   */
  async getStrategyDebugInfo(userId, strategyId) {
    try {
      console.log(`[适配器] 获取策略${strategyId}的调试信息`);

      // 从数据库获取策略
      const strategy = await LiveStrategy.findOne({
        where: { id: strategyId, userId }
      });

      if (!strategy) {
        throw new Error('策略不存在或无权查看');
      }

      // 获取策略YAML
      const strategyYaml = await this._getStrategyYaml(strategy.strategyId);

      // 查找策略所在的组合
      const { GroupStrategy } = require('../models');
      const groupStrategy = await GroupStrategy.findOne({
        where: { strategyid: strategyId },
        order: [['deployedat', 'DESC']] // 获取最近部署的记录
      });

      let configFiles = {};
      if (groupStrategy) {
        // 如果策略已部署到组合，获取配置文件
        const groupId = groupStrategy.groupid;
        const groupManager = await this.getUserGroupManager(userId, groupId);
        configFiles = await groupManager.getConfigFiles(strategyId);
      }

      return {
        success: true,
        data: {
          strategyYaml: strategyYaml,
          configFiles: configFiles
        }
      };

    } catch (error) {
      console.error(`[适配器] 获取策略${strategyId}调试信息失败:`, error);
      return { success: false, error: error.message };
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 获取策略YAML（与 strategy_server.py 保持一致的逻辑）
   */
  async _getStrategyYaml(strategyId) {
    const path = require('path');
    const fs = require('fs');
    const fsp = require('fs').promises;

    try {
      console.log(`[适配器] 开始获取策略${strategyId}的YAML配置`);

      // 1. 优先从系统策略文件读取（与 strategy_server.py 第416-423行逻辑一致）
      const portfolioDir = path.join(__dirname, '../_Providers/_Python/strategy/portfolio');
      const strategyPath = path.join(portfolioDir, strategyId, 'index.yaml');

      try {
        // 检查文件是否存在
        await fsp.access(strategyPath);
        const yamlContent = await fsp.readFile(strategyPath, 'utf8');
        console.log(`[适配器] 从系统策略文件获取策略${strategyId}的YAML成功: ${strategyPath}`);
        return yamlContent;
      } catch (fsError) {
        // 文件不存在或读取错误，继续尝试下一个方法
        console.log(`[适配器] 系统策略文件不存在或读取错误: ${fsError.message}`);
      }

      // 2. 如果系统策略文件不存在，尝试从自定义策略数据库获取（与 strategy_server.py 第427-429行逻辑一致）
      console.log(`[适配器] 尝试从自定义策略数据库获取: ${strategyId}`);

      // 调用 strategy_server.py 的逻辑来获取自定义策略YAML
      const axios = require('axios');
      const config = require('../config.json');
      const strategyServiceUrl = `http://${config.strategy_service.host}:${config.strategy_service.port}`;
      const detailsResponse = await axios.get(`${strategyServiceUrl}/strategy/details/${strategyId}`);

      if (detailsResponse.data && detailsResponse.data.success && detailsResponse.data.data.strategy_config_yaml) {
        const yamlContent = detailsResponse.data.data.strategy_config_yaml;
        console.log(`[适配器] 从自定义策略数据库获取策略${strategyId}的YAML成功`);
        return yamlContent;
      }

      throw new Error(`策略${strategyId}的YAML配置不存在（既不是系统策略也不在自定义策略数据库中）`);
    } catch (error) {
      console.error(`[适配器] 获取策略${strategyId}的YAML失败:`, error.message);
      throw error;
    }
  }

  /**
   * 解析策略名称
   */
  _parseStrategyName(yamlContent, defaultName) {
    try {
      const yaml = require('js-yaml');
      const yamlObj = yaml.load(yamlContent);
      
      if (yamlObj && yamlObj.strategy_name) {
        return yamlObj.strategy_name;
      } else if (yamlObj && yamlObj.name) {
        return yamlObj.name;
      }
      
      return defaultName;
    } catch (error) {
      console.warn(`[适配器] 解析策略名称失败:`, error.message);
      return defaultName;
    }
  }
}

// 创建单例
const liveStrategyAdapter = new LiveStrategyAdapter();

module.exports = liveStrategyAdapter;
