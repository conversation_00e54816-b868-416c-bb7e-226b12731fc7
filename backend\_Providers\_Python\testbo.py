import baostock as bs
import pandas as pd

def get_stock_daily_data(stock_code: str, start_date: str = "2020-01-01", end_date: str = "2025-06-06"):
    """
    获取指定股票的日线行情数据
    参数:
        stock_code: 股票代码（格式示例："sh.600000" 或 "sz.000001"）
        start_date: 开始日期（格式："YYYY-MM-DD"）
        end_date: 结束日期（格式："YYYY-MM-DD"）
    返回:
        Pandas DataFrame 包含日线数据
    """
    # 登录系统
    lg = bs.login()
    if lg.error_code != "0":
        print("登录失败:", lg.error_msg)
        return None

    # 查询日线数据
    rs = bs.query_history_k_data_plus(
        code=stock_code,
        fields="date,code,open,high,low,close,volume,amount,pctChg",
        start_date=start_date,
        end_date=end_date,
        frequency="d",  # 日线
        adjustflag="3"  # 不复权
    )

    # 转换为DataFrame
    data_list = []
    while rs.next():
        data_list.append(rs.get_row_data())
    df = pd.DataFrame(data_list, columns=rs.fields)

    # 数据类型转换
    numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
    df[numeric_cols] = df[numeric_cols].astype(float)
    df['date'] = pd.to_datetime(df['date'])

    # 登出系统
    bs.logout()
    return df

# 示例调用
if __name__ == "__main__":
    stock_code = "sh.518680"  # 浦发银行
    daily_data = get_stock_daily_data(stock_code)
    # 打印总行数
    print(f"总行数：{len(daily_data)}")
    print(f"{stock_code} 日线数据（前5行）：")
    print(daily_data.head())