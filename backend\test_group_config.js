#!/usr/bin/env node

/**
 * 测试组合配置生成
 */

const path = require('path');
const fs = require('fs').promises;
const GroupConfigGenerator = require('./services/GroupConfigGenerator');

async function testGroupConfig() {
  try {
    console.log('开始测试组合配置生成...');
    
    // 创建测试组合数据
    const mockGroup = {
      id: 'test_group_1',
      configPath: path.join(process.cwd(), 'live_trading', 'groups', 'user_1', 'group_1', 'config.yaml'),
      initialCapital: 100000,
      riskSettings: {
        stopLossPercent: 20.0
      },
      groupStrategies: [
        {
          strategy: {
            id: 'test_strategy_1',
            yaml: `
trading_type: etf
universe:
  - "SSE.ETF.510300"
  - "SSE.ETF.510500"
  - "SZSE.ETF.159919"
bar_count: 50
data_freq: "day"
order_by:
  formula: "roc_20 * 0.6 + vol_20 * 0.4"
buy_rules:
  formulas:
    - "roc_20 > 0.05"
    - "vol_20 < 0.3"
  at_least_count: 2
sell_rules:
  formulas:
    - "roc_20 < -0.03"
  at_least_count: 1
top_n: 2
weighting_scheme: "equal"
rebalance_interval: "daily"
            `
          }
        }
      ]
    };

    // 创建配置生成器
    const generator = new GroupConfigGenerator();
    
    // 生成组合特定的contracts.json
    console.log('\n1. 生成组合品种列表...');
    console.log('Mock group configPath:', mockGroup.configPath);
    console.log('Expected directory:', path.dirname(mockGroup.configPath));
    await generator._generateGroupContracts(mockGroup);
    
    // 生成主配置文件
    console.log('\n2. 生成主配置文件...');
    await generator._generateMainConfig(mockGroup);
    
    // 生成其他配置文件
    console.log('\n3. 生成执行器配置...');
    await generator._generateExecutersConfig(mockGroup);
    
    console.log('\n4. 生成行情配置...');
    await generator._generateTdParsersConfig(mockGroup);
    
    console.log('\n5. 生成交易配置...');
    await generator._generateTdTradersConfig(mockGroup);
    
    console.log('\n6. 生成开平策略配置...');
    await generator._generateActPolicyConfig(mockGroup);
    
    console.log('\n7. 生成过滤器配置...');
    await generator._generateFiltersConfig(mockGroup);
    
    console.log('\n8. 生成日志配置...');
    await generator._generateLogConfig(mockGroup);
    
    // 生成公共配置文件
    console.log('\n9. 生成公共配置文件...');
    await generator._generateCommonConfigs();
    
    console.log('\n✅ 所有配置文件生成完成！');
    
    // 检查生成的文件
    const groupDir = path.dirname(mockGroup.configPath);
    const commonDir = path.join(process.cwd(), 'live_trading', 'common');
    
    console.log('\n📁 生成的文件列表:');
    console.log('\n组合目录:', groupDir);
    try {
      const groupFiles = await fs.readdir(groupDir);
      groupFiles.forEach(file => console.log(`  - ${file}`));
    } catch (error) {
      console.log('  (目录不存在或为空)');
    }
    
    console.log('\n公共目录:', commonDir);
    try {
      const commonFiles = await fs.readdir(commonDir);
      commonFiles.forEach(file => console.log(`  - ${file}`));
    } catch (error) {
      console.log('  (目录不存在或为空)');
    }
    
    // 显示contracts.json内容
    const contractsPath = path.join(groupDir, 'contracts.json');
    try {
      const contractsContent = await fs.readFile(contractsPath, 'utf8');
      console.log('\n📄 contracts.json 内容:');
      console.log(contractsContent);
    } catch (error) {
      console.log('\n❌ 无法读取contracts.json:', error.message);
    }
    
    // 显示config.yaml内容
    try {
      const configContent = await fs.readFile(mockGroup.configPath, 'utf8');
      console.log('\n📄 config.yaml 内容:');
      console.log(configContent);
    } catch (error) {
      console.log('\n❌ 无法读取config.yaml:', error.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testGroupConfig();
}

module.exports = testGroupConfig;
