import pandas as pd
import numpy as np

def calculate_value(bars: pd.DataFrame) -> float:
    """
    计算最新的趋势评分：年化收益率 × R平方
    基于最后 period=25 天的对数收盘价线性回归。

    Args:
        bars (pd.DataFrame): 包含 'close' 列的K线数据

    Returns:
        float: 计算出的趋势评分，如果无法计算则返回 np.nan
    """
    period = 25
    required_length = period

    if bars is None or len(bars) < required_length:
        # print(f"[信号计算:trend_score] 数据不足: {len(bars) if bars is not None else 'None'} < {required_length}")
        return np.nan

    # 只取最后 period 条数据进行计算
    close_window = bars['close'].tail(period)
    
    # 检查窗口期内是否有无效数据 (NaN, 0, 或负数)
    if close_window.isnull().any() or (close_window <= 0).any():
         # print(f"[信号计算:trend_score] 窗口期 ({period}天) 内包含无效收盘价")
         return np.nan

    y = np.log(close_window.values.astype(float))
    x = np.arange(period).astype(float)

    try:
        # 计算线性回归参数 (斜率和截距)
        # slope 是对数价格关于时间的斜率
        slope, intercept = np.polyfit(x, y, 1)

        # 年化收益率 (对数收益转简单收益)
        # 假设一年 250 个交易日
        annualized_log_return = slope * 250
        # 防止 exp() 溢出
        if annualized_log_return > 700: # exp(700) 已经非常大
             print(f"[信号计算:trend_score] 年化对数收益率过高 ({annualized_log_return:.2f})，可能导致溢出，返回NaN")
             return np.nan
        annualized_return = np.exp(annualized_log_return) - 1

        # 计算 R 平方 (拟合优度)
        y_mean = np.mean(y)
        y_pred = slope * x + intercept
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - y_mean) ** 2)

        # 处理 ss_tot 为零的情况 (y 值恒定)
        if abs(ss_tot) < 1e-10: # 使用容差判断是否接近零
             r_squared = 1.0 if abs(ss_res) < 1e-10 else 0.0 # 如果残差也为0，则完美拟合；否则拟合度为0
        else:
            r_squared = 1 - (ss_res / ss_tot)
            # 确保 r_squared 在 [0, 1] 范围内 (理论上应该如此，但可能因浮点数误差略微超出)
            r_squared = max(0.0, min(1.0, r_squared))

        # 综合评分
        score = annualized_return * r_squared

        if np.isnan(score) or np.isinf(score):
            # print(f"[信号计算:trend_score] 计算得分无效 (NaN or Inf)")
            return np.nan

        # print(f"[信号计算:trend_score] 计算得分: {score:.4f}")
        return float(score) # 确保返回标准 float 类型

    except np.linalg.LinAlgError:
        # 处理 polyfit 可能出现的线性代数错误 (例如奇异矩阵)
        print(f"[信号计算:trend_score] 线性回归计算失败 (LinAlgError)")
        return np.nan
    except Exception as e:
        print(f"[信号计算:trend_score] 计算过程中发生错误: {e}")
        # import traceback # 调试时可以取消注释
        # print(traceback.format_exc()) # 调试时使用
        return np.nan

# --- 可选：如果需要这个因子对整个序列计算（通常不需要）---
# def calculate_series(bars: pd.DataFrame) -> Optional[pd.Series]:
#     """
#     计算趋势评分的序列（效率较低）。
#     """
#     period = 25
#     if bars is None or len(bars) < period:
#         return None
    
#     scores = pd.Series(index=bars.index, dtype=float)
#     # 使用 rolling apply 可能更高效，但为清晰起见这里用循环
#     for i in range(period - 1, len(bars)):
#         current_bars_window = bars.iloc[i - period + 1 : i + 1]
#         scores.iloc[i] = calculate_value(current_bars_window) # 复用单点计算逻辑
#     return scores 