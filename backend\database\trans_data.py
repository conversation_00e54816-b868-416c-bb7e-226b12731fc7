import sqlite3
import json
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [数据转换] %(message)s')

def transform_symbols_json(db_path='database.sqlite'):
    """
    Connects to the SQLite database, reads the pools table, transforms
    the symbols_json field for each row according to specific rules,
    and updates the table.

    Transformation rules:
    1. Correct symbol format: 'PART1.PART2.REST' -> 'PART2.PART1.REST'
       (e.g., 'STOCK.SSE.999999' -> 'SSE.STOCK.999999')
    2. Delete entries where PART1 == PART2 (e.g., 'STOCK.STOCK.CODE')
    3. Keep items that don't match the expected format or are missing keys, logging warnings.
    4. Move all keys except 'symbol' and 'name' into a nested 'data' dictionary.
    """
    if not os.path.exists(db_path):
        logging.error(f"数据库文件未找到: {db_path}")
        return

    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Fetch all pool_id and symbols_json from the pools table
        cursor.execute("SELECT pool_id, symbols_json FROM pools")
        rows = cursor.fetchall()

        logging.info(f"在 pools 表中找到 {len(rows)} 行。")
        update_count = 0
        processed_count = 0

        for pool_id, symbols_json_str in rows:
            processed_count += 1
            if not symbols_json_str:
                # logging.info(f"跳过 pool_id {pool_id}: symbols_json 为空。")
                continue

            try:
                # Parse the JSON string into a list
                original_list = json.loads(symbols_json_str)

                if not isinstance(original_list, list):
                    logging.warning(f"跳过 pool_id {pool_id}: symbols_json 不是一个 JSON 数组。")
                    continue

                transformed_list = []
                needs_update = False # Flag to check if transformation or deletion occurred

                items_to_remove_indices = set() # Store indices of items to remove

                for index, item in enumerate(original_list):
                    if not isinstance(item, dict):
                        logging.warning(f"跳过 pool_id {pool_id} 中的项目: 项目不是字典: {item}")
                        transformed_list.append(item) # Keep non-dict items
                        continue

                    # Ensure 'symbol' and 'name' exist before processing
                    if 'symbol' not in item or 'name' not in item:
                        logging.warning(f"跳过 pool_id {pool_id} 中的项目: 缺少 'symbol' 或 'name': {item}")
                        transformed_list.append(item) # Keep item as is
                        continue

                    original_symbol = item['symbol']
                    original_name = item['name']
                    data_dict = {}
                    new_symbol = original_symbol # Default to original symbol

                    # --- Symbol Transformation/Deletion Logic ---
                    symbol_parts = original_symbol.split('.')
                    item_symbol_changed = False
                    item_deleted = False

                    if len(symbol_parts) >= 3:
                        part1 = symbol_parts[0]
                        part2 = symbol_parts[1]
                        rest = '.'.join(symbol_parts[2:])

                        if part1 == part2:
                            # Mark for deletion if PART1 == PART2
                            logging.info(f"标记删除 pool_id {pool_id} 中的项目: symbol '{original_symbol}' 无效 (部分重复)。")
                            items_to_remove_indices.add(index)
                            needs_update = True # Mark that the list will change
                            item_deleted = True
                        # Check if the first part looks like a market (e.g., SSE, SZSE) and second like type (STOCK, FUTURE)
                        # This is a heuristic check, might need refinement based on actual possible values
                        elif part1 in ['STOCK', 'FUTURE', 'INDEX', 'ETF'] and part2 not in ['STOCK', 'FUTURE', 'INDEX', 'ETF']:
                             # Perform the swap: STOCK.SSE.CODE -> SSE.STOCK.CODE
                             new_symbol = f"{part2}.{part1}.{rest}"
                             if new_symbol != original_symbol:
                                 logging.info(f"转换 pool_id {pool_id} 中的 symbol: '{original_symbol}' -> '{new_symbol}'")
                                 item_symbol_changed = True
                                 needs_update = True
                        # else: symbol format is likely already correct or doesn't fit the pattern to swap

                    elif len(symbol_parts) < 3:
                        logging.warning(f"pool_id {pool_id} 中的 symbol '{original_symbol}' 格式不符合预期（少于3部分），保持不变。")
                    # --- End Symbol Logic ---

                    # --- Data Dictionary Logic ---
                    item_data_moved = False
                    if not item_deleted: # Only process data dict if item is not deleted
                        for key, value in item.items():
                            if key not in ('symbol', 'name'):
                                data_dict[key] = value
                                item_data_moved = True # Mark that we moved something into data

                        if item_data_moved and not needs_update: # If only data moved, still needs update
                            needs_update = True

                        transformed_item = {
                            "symbol": new_symbol, # Use the potentially transformed symbol
                            "name": original_name,
                            "data": data_dict
                        }
                        transformed_list.append(transformed_item) # Add potentially modified item
                    # --- End Data Dictionary Logic ---


                # Create the final list *after* iterating by excluding the marked indices
                final_transformed_list = [item for i, item in enumerate(transformed_list) if i not in items_to_remove_indices]


                # Only update if the list content actually changed (transformation or deletion)
                if needs_update:
                    # Check if the final list is different from the original *structure* (ignoring data dict changes for now)
                    # A more robust check might compare original structure vs final structure if needed
                    # For now, 'needs_update' flag covers transformations and deletions.

                    # Serialize the final transformed list back to a JSON string
                    new_symbols_json_str = json.dumps(final_transformed_list, ensure_ascii=False)

                    # Update the row in the database
                    cursor.execute("UPDATE pools SET symbols_json = ? WHERE pool_id = ?", (new_symbols_json_str, pool_id))
                    update_count += 1
                    logging.info(f"已更新 pool_id {pool_id} (已转换/删除条目)。")
                # else:
                    # logging.info(f"跳过更新 pool_id {pool_id}: 无需转换或删除。")


            except json.JSONDecodeError:
                logging.warning(f"跳过 pool_id {pool_id}: symbols_json 中包含无效 JSON。")
            except Exception as e:
                logging.error(f"处理 pool_id {pool_id} 时出错: {e}", exc_info=True) # Log traceback

        # Commit the changes
        if update_count > 0:
            conn.commit()
            logging.info(f"转换完成。共处理 {processed_count} 行，更新 {update_count} 行。")
        else:
            logging.info(f"转换完成。共处理 {processed_count} 行，无需更新任何行。")

    except sqlite3.Error as e:
        logging.error(f"数据库错误: {e}")
    except Exception as e:
        logging.error(f"发生意外错误: {e}", exc_info=True) # Log traceback
    finally:
        if conn:
            conn.close()
            logging.info("数据库连接已关闭。")

if __name__ == "__main__":
    # Ensure the script looks for the database in the correct relative path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_file_path = os.path.join(script_dir, 'database.sqlite') # Construct path relative to script

    # db_file_path = 'database.sqlite' # Use this if db is in the CWD where you run the script
    transform_symbols_json(db_file_path) 