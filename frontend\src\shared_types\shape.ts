import { IndicatorType, KLineData } from "./market";

export interface Shape {
  shapeId: number;      // 形态ID
  name: string;         // 形态名称
  userId: number;       // 用户ID
  createdAt: Date;      // 创建时间
} 

//形态元素设置项目，取自于指标
export interface ShapeConfigItem {
  type: IndicatorType; // 指标类型
  selectedLine: string; // 选中的线名称
  params: Record<string, any>; // 参数
  weight: number; // 权重
  values: number[]; // 经过处理的输出，范围从 0 到 1
}

export interface ShapeConfig {
  shapeConfigItems: ShapeConfigItem[];
  name: string;
  klineData: KLineData[];
}