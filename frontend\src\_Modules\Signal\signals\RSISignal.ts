import { KLine, KLineData } from '@/shared_types/market';
import { BaseSignal, SignalParameter, SignalType } from '../BaseSignal';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';


/**
 * RSI超买超卖信号
 * 使用RSI指标判断市场的超买超卖状态
 * 当RSI从超卖区上穿时产生买入信号
 * 当RSI从超买区下穿时产生卖出信号
 */
export class RSISignal extends BaseSignal {
  private period: number;
  private overbought: number;
  private oversold: number;

  constructor(config: SignalConfig) {
    super(config);
    
    // 设置参数
    this.period = this.parameters.period;
    this.overbought = this.parameters.overbought;
    this.oversold = this.parameters.oversold;

    // 验证参数
    if (this.oversold >= this.overbought) {
      throw new Error('Oversold level must be less than overbought level');
    }
  }

  /**
   * 获取信号参数列表
   */
  static async getParameters(): Promise<SignalParameter[]> {
    return Promise.resolve([
      {
        name: 'period',
        paramType: 'number',
        default: 14,
        description: 'RSI周期',
        minValue: 2,
        maxValue: 100,
        step: 1
      },
      {
        name: 'overbought',
        paramType: 'number',
        default: 70,
        description: '超买阈值',
        minValue: 50,
        maxValue: 100,
        step: 1
      },
      {
        name: 'oversold',
        paramType: 'number',
        default: 30,
        description: '超卖阈值',
        minValue: 0,
        maxValue: 50,
        step: 1
      }
    ]);
  }

  /**
   * 计算RSI指标
   * @param data 收盘价数组
   * @param period RSI周期
   */
  private calculateRSI(data: number[], period: number): number[] {
    const rsi: number[] = [];
    let gains = 0;
    let losses = 0;

    // 计算第一个RSI值
    for (let i = 1; i < period + 1; i++) {
      const diff = data[i] - data[i - 1];
      if (diff >= 0) {
        gains += diff;
      } else {
        losses -= diff;
      }
    }

    let avgGain = gains / period;
    let avgLoss = losses / period;
    
    // 计算第一个RSI
    rsi.push(100 - (100 / (1 + avgGain / avgLoss)));

    // 使用递推公式计算后续的RSI
    for (let i = period + 1; i < data.length; i++) {
      const diff = data[i] - data[i - 1];
      let currentGain = 0;
      let currentLoss = 0;

      if (diff >= 0) {
        currentGain = diff;
      } else {
        currentLoss = -diff;
      }

      avgGain = (avgGain * (period - 1) + currentGain) / period;
      avgLoss = (avgLoss * (period - 1) + currentLoss) / period;

      rsi.push(100 - (100 / (1 + avgGain / avgLoss)));
    }

    // 填充前期空值
    for (let i = 0; i < period; i++) {
      rsi.unshift(NaN);
    }

    return rsi;
  }

  calculateSignals(kline: KLine, params: Record<string, any>): MarketEvents.SignalItem[] {
    const signals: MarketEvents.SignalItem[] = [];
    const period = params.period as number;
    const overbought = params.overbought as number;
    const oversold = params.oversold as number;

    const data = kline.data;
    
    // 提取收盘价
    const closes = data.map(k => k.close);

    // 计算RSI
    const rsi = this.calculateRSI(closes, period);

    // 从period开始遍历，寻找交叉点
    for (let i = period + 1; i < data.length; i++) {
      // 判断是否发生交叉
      const crossUpOversold = rsi[i - 1] <= oversold && rsi[i] > oversold;
      const crossDownOverbought = rsi[i - 1] >= overbought && rsi[i] < overbought;

      if (crossUpOversold) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.BUY,
          price: data[i].close,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      } else if (crossDownOverbought) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.SELL,
          price: data[i].close,
          index: i
        };
        signals.push(signal);
        this.addSignalTrigger(signal);
      }
    }

    return signals;
  }
} 