import React, { useState, useEffect, useRef } from 'react';
import { Button, Space, Tooltip } from 'antd';
import { FaPlay, FaPause, FaStop } from 'react-icons/fa';
import { EventBus } from '@/events/eventBus';
import { ChartEvents } from '@/events/events';
import './index.less';

interface PlaybackFloatingWindowProps {
  visible: boolean;
  isPlaying: boolean;
  stepInterval: number;
  onClose: () => void;
}

const PlaybackFloatingWindow: React.FC<PlaybackFloatingWindowProps> = ({
  visible,
  isPlaying,
  stepInterval,
  onClose
}) => {
  const [position, setPosition] = useState({ x: 100, y: 100 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const windowRef = useRef<HTMLDivElement>(null);
  const stepOptions = [1, 2, 3, 4, 5, 10, 15, 20];

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    const rect = windowRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  // 处理触摸开始事件
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    const rect = windowRef.current?.getBoundingClientRect();
    if (rect && e.touches[0]) {
      setDragOffset({
        x: e.touches[0].clientX - rect.left,
        y: e.touches[0].clientY - rect.top
      });
    }
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      });
    }
  };

  // 处理触摸移动事件
  const handleTouchMove = (e: TouchEvent) => {
    if (isDragging && e.touches[0]) {
      e.preventDefault(); // 防止页面滚动
      setPosition({
        x: e.touches[0].clientX - dragOffset.x,
        y: e.touches[0].clientY - dragOffset.y
      });
    }
  };

  // 处理鼠标松开事件
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 处理触摸结束事件
  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isDragging, dragOffset]);

  // 处理退出回放
  const handleExitPlayback = () => {
    EventBus.emit(ChartEvents.Types.EXIT_PLAYBACK_MODE, {});
    onClose();
  };

  // 处理播放/暂停
  const handleTogglePlayPause = () => {
    EventBus.emit(ChartEvents.Types.TOGGLE_PLAY_PAUSE, { playing: !isPlaying });
  };

  // 处理步长变更
  const handleStepChange = (step: number) => {
    // 保存到sessionStorage
    sessionStorage.setItem('playbackStepInterval', step.toString());
    EventBus.emit(ChartEvents.Types.SET_PLAYBACK_STEP, { step });
  };

  if (!visible) return null;

  return (
    <div
      ref={windowRef}
      className="playback-floating-window"
      style={{
        position: 'fixed',
        left: position.x,
        top: position.y,
        zIndex: 1000,
        cursor: isDragging ? 'grabbing' : 'grab'
      }}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
    >
      <div className="floating-window-content">
        <Space size={8}>
          <Tooltip title="退出回放">
            <Button
              shape="circle"
              size="small"
              icon={<FaStop />}
              onClick={handleExitPlayback}
              type="default"
            />
          </Tooltip>
          
          <Tooltip title={isPlaying ? "暂停" : "播放"}>
            <Button
              shape="circle"
              size="small"
              icon={isPlaying ? <FaPause /> : <FaPlay />}
              onClick={handleTogglePlayPause}
              type="default"
            />
          </Tooltip>
          
          <Tooltip title="步长(秒)">
            <select
              value={stepInterval}
              style={{ 
                width: 50, 
                height: 28, 
                borderRadius: 4, 
                border: '1px solid var(--border-color)',
                fontSize: '12px'
              }}
              onChange={e => handleStepChange(Number(e.target.value))}
            >
              {stepOptions.map(opt => (
                <option key={opt} value={opt}>{opt}s</option>
              ))}
            </select>
          </Tooltip>
        </Space>
      </div>
    </div>
  );
};

export default PlaybackFloatingWindow; 