import React, { useEffect, useState, useCallback } from 'react';
import { useAtom } from 'jotai';
import ToolBar from '../../main/components/ToolBar';

import GeneList from '../../main/components/GeneList';
import KLineChartPanel from '../../main/components/KLineChartPanel';
import ToolPane from '../../main/components/ToolPane';
import ShapePicker from '../../main/components/ShapePicker';
import SignalList from '../../main/components/SignalList';
import chartDispatcher from '@/_Dispatchers/ChartDispatcher';
import { chatVisibleAtom, toolPaneVisibleAtom, toolPaneContentAtom } from '@/store/state';
import { EventBus } from '@/events/eventBus';
import { KeyboardEvents } from '@/events/events';
import KeyboardCoder from '@/_Widgets/KeyboardCoder';
import './index.less';

interface DashboardContentProps {
  pageType?: string;
}

const DashboardContent: React.FC<DashboardContentProps> = ({ pageType = 'klinechart' }) => {
  // 创建一个状态来控制是否显示KLineChartPanel
  const [klinePanelInitialized, setKlinePanelInitialized] = useState(false);
  // 添加键盘代码输入器的可见性状态
  const [keyboardCoderVisible, setKeyboardCoderVisible] = useState(false);
  
  // 获取全局状态
  const [chatVisible] = useAtom(chatVisibleAtom);
  const [toolPaneVisible, setToolPaneVisible] = useAtom(toolPaneVisibleAtom);
  const [, setToolPaneContent] = useAtom(toolPaneContentAtom);
  
  // 处理键盘事件的回调函数
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // 忽略输入框中的键盘事件
    if (
      e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement ||
      e.target instanceof HTMLSelectElement ||
      (e.target as HTMLElement).getAttribute('contenteditable') === 'true'
    ) {
      return;
    }
    
    // 忽略修饰键
    if (e.ctrlKey || e.altKey || e.metaKey) {
      return;
    }
    
    // 判断是否为字母或数字键
    const isAlphaNumeric = /^[a-zA-Z0-9]$/.test(e.key);
    
    // 创建键盘事件载荷
    const keyboardPayload: KeyboardEvents.KeyPressed = {
      key: e.key,
      keyCode: e.keyCode,
      isAlphaNumeric,
      isModifier: e.shiftKey || e.ctrlKey || e.altKey || e.metaKey,
      timestamp: Date.now()
    };
    
    // 发送键盘事件到事件总线
    EventBus.emit(KeyboardEvents.Types.KEY_PRESSED, keyboardPayload);
    
    // 如果是字母或数字键，并且键盘代码输入器未显示，发送开始输入事件
    if (isAlphaNumeric && !keyboardCoderVisible) {
      EventBus.emit(KeyboardEvents.Types.INPUT_STARTED, {
        initialKey: e.key,
        timestamp: Date.now()
      });
      
      // 显示键盘代码输入器
      setKeyboardCoderVisible(true);
      
      // 阻止键盘事件继续传播，避免重复输入
      e.preventDefault();
      e.stopPropagation();
    }
    
    // 如果按下ESC键，发送取消输入事件
    if (e.key === 'Escape' && keyboardCoderVisible) {
      EventBus.emit(KeyboardEvents.Types.INPUT_CANCELLED, {
        reason: 'user_cancelled',
        timestamp: Date.now()
      });
      
      // 隐藏键盘代码输入器
      setKeyboardCoderVisible(false);
    }
  }, [keyboardCoderVisible]);
  
  // 监听键盘输入完成事件
  const handleInputComplete = useCallback(() => {
    setKeyboardCoderVisible(false);
  }, []);
  
  useEffect(() => {
    console.log('DashboardContent 组件挂载');
    chartDispatcher.initialize();

    // 初始化KLineChartPanel
    console.log('初始化KLineChartPanel组件...');
    setKlinePanelInitialized(true);
    
    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown);
    
    // 订阅输入完成事件
    const subscription = EventBus.on(KeyboardEvents.Types.INPUT_COMPLETE, handleInputComplete);
    
    // 订阅品种按钮点击事件
    const symbolButtonSubscription = EventBus.on(KeyboardEvents.Types.SYMBOL_BUTTON_CLICKED, () => {
      console.log('[DashboardContent] 收到品种按钮点击事件，显示输入框');
      setKeyboardCoderVisible(true);
    });
    
    // 清理函数
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      subscription.unsubscribe();
      symbolButtonSubscription.unsubscribe();
    };
  }, [handleKeyDown, handleInputComplete]);

  return (
    <div className="main-container">
      {/* 删除工具栏，已移动到顶部栏 */}
      {/* <div className="toolbar-wrapper">
        <ToolBar />
      </div> */}
      <div className="content-wrapper">
        {/* 左侧图表区域 */}
        <div className="content-chart" style={{ 
          flex: 1, 
          height: '100%', 
          overflow: 'hidden',
          position: 'relative' // 为了GeneList的定位
        }}>
          {/* 始终渲染KLineChartPanel，但根据条件控制显示 */}
          <div style={{ display: pageType === 'klinechart' ? 'block' : 'none', width: '100%', height: '100%' }}>
            {klinePanelInitialized && <KLineChartPanel />}
          </div>
          
          {/* GeneList 需要显示在图表区域内 */}
          <GeneList />
          
          {/* 添加 ShapePicker 组件 */}
          <ShapePicker />
          
          {/* 添加 SignalList 组件，但设置为不可见 */}
          <div style={{ display: 'none' }}>
            <SignalList visible={false} />
          </div>
          
          {/* 键盘代码输入器 */}
          <div 
            className="keyboard-coder-container" 
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 999,
              background: 'transparent',
              display: keyboardCoderVisible ? 'block' : 'none'
            }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                EventBus.emit(KeyboardEvents.Types.INPUT_CANCELLED, {
                  reason: 'click_outside',
                  timestamp: Date.now()
                });
                setKeyboardCoderVisible(false);
              }
            }}
          >
            <div style={{
              position: 'absolute',
              top: '30%',
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 1000,
            }}>
              <KeyboardCoder />
            </div>
          </div>
        </div>
        
        {/* 右侧工具面板 */}
        <ToolPane />
      </div>
    </div>
  );
};

export default DashboardContent; 