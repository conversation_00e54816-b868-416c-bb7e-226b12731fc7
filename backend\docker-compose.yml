services:
  # Service 1: Main Python App
  app:
    image: python:3.9-slim  # 使用基础镜像，无需构建
    volumes:
      - ./_Providers/_Python:/app  # 挂载本地代码目录
      - ./_Providers/_Python/logs:/app/logs
      - ./_Providers/_Python/config.json:/app/config.json
      - ./_Providers/_Python/tdx_symbols:/app/tdx_symbols
      - ./_Providers/_Python/tdx_data:/app/tdx_data
      - ./_Providers/_Python/uscodes.sqlite:/app/uscodes.sqlite
    command: python /app/app.py  # 容器内执行命令
    ports:
      - "5000:5000"

  # Service 2: TDX Server
  tdx_server:
    image: python:3.9-slim
    volumes:
      - ./_Providers/_Python:/app
      - ./_Providers/_Python/logs:/app/logs
      - ./_Providers/_Python/tdx_servers.json:/app/tdx_servers.json
    command: python /app/tdxserver.py
    ports:
      - "5001:5001"

  # Service 3: Strategy Server
  strategy_server:
    image: python:3.9-slim
    volumes:
      - ./_Providers/_Python/strategy:/app
      - ./_Providers/_Python/strategy/db_data:/app/db_data
      - ./_Providers/_Python/portfolio:/app/portfolio
      - ./_Providers/_Python/storage:/app/storage
      - ./_Providers/_Python/common:/app/common
      - ./_Providers/_Python/strategy/logs:/app/logs
    command: python /app/strategy_server.py
    ports:
      - "5002:5002"

  # Service 4: Node.js App
  node_app:
    image: node:18-alpine  # 使用基础镜像
    volumes:
      - .:/usr/src/app  # 挂载整个项目目录（排除 node_modules）
      - /usr/src/app/node_modules  # 匿名卷持久化依赖
    command: npm run dev
    ports:
      - "3000:3000"