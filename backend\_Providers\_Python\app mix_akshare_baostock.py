from flask import Flask, jsonify, request
import adata as ad  # 替换 akshare
import akshare as ak
import pandas as pd
from datetime import datetime
import json
import os
import backtrader as bt
from strategy.gene.base import FactorType, FactorMode, SignalType
from strategy.gene.factors.ma_cross import MACrossFactor

# 读取配置文件
# 获取当前文件的目录
current_dir = os.path.dirname(__file__)

# 构建 config.json 的路径
config_path = os.path.join(current_dir, 'config.json')

# 加载 config.json
with open(config_path, 'r') as f:
    config = json.load(f)

# 获取Python服务配置
python_config = config['python_service']
HOST = python_config['host']
PORT = python_config['port']

# K线周期映射
PERIOD_MAP = {
    # AData周期映射（股票用）
    "STOCK": {
        "1D": "1",      # 日线
        "1W": "2",      # 周线
        "1M": "3",      # 月线
        "1Q": "4",      # 季线
        "5m": "5",      # 5分钟
        "15m": "15",     # 15分钟
        "30m": "30",     # 30分钟
        "60m": "60"      # 1小时
    }
}

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/stock/list', methods=['GET'])
def get_stock_list():
    """获取A股股票列表"""
    try:
        # 使用 adata 获取A股股票列表
        df = ad.stock.info.all_code()
        
        # 转换为所需格式
        stocks = []
        for _, row in df.iterrows():
            stocks.append({
                'code': row['stock_code'],  # 股票代码
                'name': row['short_name'],   # 股票简称
                'market': 'STOCK',           # 市场类型
                'exchange': 'SH' if row['exchange'] == 'SH' else 'SZ'  # 交易所
            })
        
        return jsonify({
            'success': True,
            'data': stocks
        })
    except Exception as e:
        print(f"Error in get_stock_list: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/stock/kline', methods=['GET'])
def get_stock_kline():
    """获取股票K线数据"""
    try:
        # 获取并打印所有请求参数
        print("Request parameters:", dict(request.args))
        
        symbol = request.args.get('symbol')
        period = request.args.get('period', '1D')  # 默认日K
        market = request.args.get('market', 'STOCK').upper()  # 默认STOCK
        
        if not symbol:
            return jsonify({
                'success': False,
                'error': 'Symbol is required'
            }), 400

        # 自动识别指数代码（以.SH或.SZ结尾）
        if symbol.endswith(('.SH', '.SZ', '.CSI', '.sh', '.sz')) or symbol.startswith(('sh', 'sz')):
            return get_index_kline()
        
        try:
            # 获取周期映射
            period_num = PERIOD_MAP['STOCK'].get(period)
            if period_num is None:
                return jsonify({
                    'success': False,
                    'error': f'Invalid period: {period}'
                }), 400
            
            print(f"calling AData.stock.market.get_market(stock_code={symbol}, k_type={period_num}, adjust_type=1)")
            # 使用AData获取股票数据
            df = ad.stock.market.get_market(
                stock_code=symbol,
                k_type=int(period_num),
                adjust_type=1  # 前复权
            )
            
            klines = []
            for _, row in df.iterrows():
                timestamp = int(pd.to_datetime(row['trade_time']).timestamp())
                klines.append({
                    'time': timestamp,
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': float(row['volume']),
                    'amount': float(row['amount']),
                    'change': float(row['change']),
                    'change_pct': float(row['change_pct'])
                })
            
            return jsonify({
                'success': True,
                'data': klines
            })
            
        except Exception as e:
            print(f"Error fetching data: {str(e)}")
            return jsonify({
                'success': False,
                'error': f"Failed to fetch data: {str(e)}"
            }), 500
            
    except Exception as e:
        print(f"Unexpected error in get_stock_kline: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/index/kline', methods=['GET'])
def get_index_kline():
    """获取指数K线数据"""
    try:
        print("\n=== 指数行情数据获取 ===")
        print(f"接收到的请求参数: {dict(request.args)}")
        
        symbol = request.args.get('symbol')
        period = request.args.get('period', '1D')  # 默认日K
        
        if not symbol:
            return jsonify({
                'success': False,
                'error': 'Symbol is required'
            }), 400

        try:
            # 不支持分钟级别数据
            if period in ['1m', '5m', '15m', '30m', '60m']:
                print(f"不支持的周期类型: {period}，指数只支持日线数据")
                return jsonify({
                    'success': False,
                    'error': 'Minute-level data is not supported for indices'
                }), 400

            # 只支持日线数据
            if period != '1D':
                print(f"不支持的周期类型: {period}，指数只支持日线数据")
                return jsonify({
                    'success': False,
                    'error': 'Only daily data is supported for indices'
                }), 400

            print(f"\n代码转换过程:")
            print(f"输入代码: {symbol}")
            
            # 解析指数代码
            if symbol.startswith(('sh', 'sz')):
                # 已经是正确格式，直接使用
                query_symbol = symbol
                print(f"代码已经是正确格式(sh/sz)，直接使用: {query_symbol}")
            elif symbol.endswith('.SH') or symbol.endswith('.sh'):
                # 转换 xxxxxx.SH 为 shxxxxxx
                query_symbol = f"sh{symbol[:-3]}"
                print(f"转换.SH后缀格式: {symbol} -> {query_symbol}")
            elif symbol.endswith('.SZ') or symbol.endswith('.sz'):
                # 转换 xxxxxx.SZ 为 szxxxxxx
                query_symbol = f"sz{symbol[:-3]}"
                print(f"转换.SZ后缀格式: {symbol} -> {query_symbol}")
            elif symbol.endswith('.CSI') or symbol.endswith('.csi'):
                # 转换 xxxxxx.CSI 为 csixxxxxx
                query_symbol = f"csi{symbol[:-4]}"
                print(f"转换.CSI后缀格式: {symbol} -> {query_symbol}")
            elif symbol.startswith('000') or symbol.startswith('399'):
                # 自动判断深沪
                prefix = 'sz' if symbol.startswith('399') else 'sh'
                query_symbol = f"{prefix}{symbol}"
                print(f"自动判断代码类型: {symbol} -> {query_symbol} (以{symbol[:3]}开头，使用{prefix}前缀)")
            else:
                # 中证指数
                query_symbol = f"csi{symbol}"
                print(f"转换为中证指数格式: {symbol} -> {query_symbol}")
            
            print(f"\nAPI调用:")
            print(f"ak.stock_zh_index_daily_em(symbol='{query_symbol}')")
            
            # 获取指数日线数据
            df = ak.stock_zh_index_daily_em(symbol=query_symbol)
            print(f"数据获取成功，共{len(df)}条记录")
            
            klines = []
            for _, row in df.iterrows():
                timestamp = int(pd.to_datetime(row['date']).timestamp())
                klines.append({
                    'time': timestamp,
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': float(row['volume']),
                    'amount': float(row['amount']) if 'amount' in row else 0.0,
                    'change': float(row['change']) if 'change' in row else 0.0,
                    'change_pct': float(row['pct_chg']) if 'pct_chg' in row else 0.0
                })
            
            print("=== 数据处理完成 ===\n")
            return jsonify({
                'success': True,
                'data': klines
            })
            
        except Exception as e:
            print(f"\n错误: 获取指数数据失败")
            print(f"错误信息: {str(e)}")
            return jsonify({
                'success': False,
                'error': f"Failed to fetch index data: {str(e)}"
            }), 500
            
    except Exception as e:
        print(f"\n错误: 处理请求时发生异常")
        print(f"错误信息: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 因子类型到类的映射
FACTOR_MAP = {
    'MA_CROSS': MACrossFactor,
    # 后续添加更多因子
}

@app.route('/gene/factors', methods=['GET'])
def get_factor_list():
    """获取可用的因子列表"""
    try:
        factors = []
        for factor_id, factor_class in FACTOR_MAP.items():
            # 获取因子的参数信息
            params = {}
            for name, default in factor_class.params._getitems():
                if name not in ['factor_type', 'mode']:  # 排除基类参数
                    params[name] = {
                        'default': default,
                        'type': type(default).__name__
                    }
            
            factors.append({
                'id': factor_id,
                'name': factor_class.__name__,
                'description': factor_class.__doc__,
                'parameters': params
            })
        
        return jsonify({
            'success': True,
            'data': factors
        })
    except Exception as e:
        print(f"Error in get_factor_list: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def load_data_with_info(cerebro, dataname, symbol_info, **kwargs):
    """
    加载数据并记录基本信息
    Args:
        cerebro: Backtrader Cerebro 实例
        dataname: DataFrame 或 CSV 文件路径
        symbol_info: 交易品种信息 {code, name, market, exchange}
        **kwargs: 数据加载参数
    """
    # 加载数据
    if isinstance(dataname, pd.DataFrame):
        data = bt.feeds.PandasData(dataname=dataname, **kwargs)
    else:
        data = bt.feeds.GenericCSVData(dataname=dataname, **kwargs)

    # 记录基本信息
    data.symbol_info = symbol_info
    data.params_info = {
        'dataname': str(dataname),
        'timeframe': kwargs.get('timeframe', bt.TimeFrame.Days),
        'compression': kwargs.get('compression', 1),
        'fromdate': kwargs.get('fromdate'),
        'todate': kwargs.get('todate'),
        'fields': ['open', 'high', 'low', 'close', 'volume', 'openinterest']
    }
    
    print(f"加载数据信息:")
    print(f"- 品种信息: {data.symbol_info}")
    print(f"- 参数信息: {data.params_info}")

    # 将数据添加到 Cerebro
    cerebro.adddata(data)
    return data

@app.route('/gene/locate', methods=['POST'])
def locate_gene():
    """基因定位服务"""
    try:
        data = request.get_json()
        
        # 验证必要参数
        required_fields = ['symbol', 'market', 'period', 'factor_id', 'parameters', 'klines']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # 获取因子类
        factor_class = FACTOR_MAP.get(data['factor_id'])
        if not factor_class:
            return jsonify({
                'success': False,
                'error': f'Invalid factor_id: {data["factor_id"]}'
            }), 400
        
        # 创建 Cerebro 引擎
        cerebro = bt.Cerebro()
        
        # 准备数据
        df = pd.DataFrame(data['klines'])
        df['datetime'] = pd.to_datetime(df['time'].apply(lambda x: datetime.fromtimestamp(x)))
        df.set_index('datetime', inplace=True)
        
        # 构建品种信息
        symbol_info = {
            'code': data['symbol'],
            'market': data['market'],
            'period': data['period']
        }
        
        # 加载数据
        load_data_with_info(
            cerebro=cerebro,
            dataname=df,
            symbol_info=symbol_info,
            timeframe=bt.TimeFrame.Minutes if data['period'].endswith('m') else bt.TimeFrame.Days,
            compression=int(data['period'].rstrip('mDWM')) if data['period'] != '1D' else 1
        )
        
        # 添加因子
        params = data.get('parameters', {})
        params.update({
            'factor_type': FactorType.TECHNICAL,
            'mode': FactorMode.RESEARCH
        })
        factor = cerebro.addindicator(factor_class, **params)
        
        # 运行回测
        cerebro.run()
        
        # 获取信号
        signals = []
        for signal in factor.get_signals():
            signals.append({
                'time': int(signal.time.timestamp()),
                'type': signal.type.value,
                'price': signal.price,
                'index': signal.index
            })
        
        return jsonify({
            'success': True,
            'data': {
                'signals': signals,
                'factor_values': factor.lines.factor.array.tolist()
            }
        })
        
    except Exception as e:
        print(f"Error in locate_gene: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(host=HOST, port=PORT) 