# 量化交易前端项目概述

## 项目结构

```
frontend/
├── src/
│   ├── _Modules/          # 核心业务模块
│   │   ├── Signal/        # 信号处理
│   │   ├── Market/        # 市场数据
│   │   ├── Indicators/    # 技术指标
│   ├── _Pages/            # 页面组件
│   ├── _Services/         # 服务层
│   ├── _Dispatchers/      # 状态管理
│   ├── config/            # 配置
│   ├── shared_types/      # 类型定义
│   ├── utils/             # 工具函数
```

## 核心模块

### 1. 市场数据模块
- `MarketService`: 处理市场数据获取
- `MarketManager`: 管理市场扫描器
- `VolumeSurgeScanner`: 成交量扫描器

### 2. 技术指标模块
- `BaseIndicator`: 指标基类
- `EMA`: EMA指标实现
- `IndicatorWrapper`: 指标包装器

### 3. 信号处理模块
- `BaseSignal`: 信号基类
- `MACDSignal`: MACD信号
- `RSISignal`: RSI信号
- `BollingerBandsSignal`: 布林带信号

### 4. 图表配置模块
- `ChartConfig`: 图表配置接口
- `DrawingLine`: 绘图线配置
- `IndicatorConfig`: 指标配置

### 5. 核心服务
- `WebSocketManager`: WebSocket管理
- `KLineCache`: K线数据缓存
- `UserDispatcher`: 用户相关操作

## 主要功能
- 实时K线图表展示
- 技术指标计算与展示
- 交易信号生成与提示
- 市场数据扫描与分析
- 用户交互与状态管理

## 技术栈
- TypeScript
- React
- WebSocket
- IndexedDB (用于本地缓存)
