// 声明全局类型
declare global {
  var printCustomStack: () => void;
}

function printCustomStack() {
  const stack = new Error().stack;
  const filteredStack = stack?.split('\n')
    .filter(line => !line.includes('node:internal') && !line.includes('(native)'))
    .join('\n');
  console.log('[调用栈]', filteredStack);
}

// 添加到全局对象
globalThis.printCustomStack = printCustomStack;

// 导出函数（可选，如果你还想通过模块方式使用）
export default printCustomStack;