{"name": "backend", "version": "1.0.0", "main": "index.js", "license": "MIT", "dependencies": {"axios": "^1.7.9", "bcryptjs": "^2.4.3", "bull": "^4.16.5", "dotenv": "^16.4.7", "express": "^4.21.2", "fast-deep-equal": "^3.1.3", "ioredis": "^5.5.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.47", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "pinyin": "^4.0.0-alpha.2", "redis": "^5.5.6", "rxjs": "^7.8.1", "sequelize": "^6.37.5", "socket.io": "^4.8.1", "sqlite3": "^5.1.7", "swagger-ui-express": "^5.0.1", "ws": "^8.18.0", "yahoo-finance2": "^2.13.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.11.19", "@types/sequelize": "^4.28.20", "eslint": "^9.20.1", "nodemon": "^3.1.9", "typescript": "^5.4.2"}, "scripts": {"start": "node index.js", "dev": "node index.js", "test": "echo \"Error: no test specified\" && exit 1", "type-check": "tsc --noEmit", "test:websocket": "jest websocket.test.js", "lint": "eslint .", "lint:fix": "eslint . --fix"}}