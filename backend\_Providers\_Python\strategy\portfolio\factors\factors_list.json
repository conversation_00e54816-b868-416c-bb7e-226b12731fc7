[{"name": "roc20", "description": "计算过去20个周期的收盘价变化率 (Rate of Change)", "module": "roc20", "params": [{"name": "period", "default": 20, "type": "int", "description": "计算 ROC 的周期长度"}], "output": "单个因子值 (float) 或因子序列 (pd.Series)", "functions": ["calculate_value", "calculate_series"]}, {"name": "slope25", "description": "计算过去25个周期收盘价的线性回归斜率", "module": "slope25", "params": [{"name": "period", "default": 25, "type": "int", "description": "计算线性回归斜率的周期长度"}], "output": "单个因子值 (float) 或因子序列 (pd.Series)", "functions": ["calculate_value", "calculate_series"]}, {"name": "complex_score_rule", "description": "计算复合排序得分: trend(26)*0.4 + (roc(5)+roc(10))*0.2 + ma(vol,5)/ma(vol,20)", "module": "complex_score_rule", "params": [], "output": "单个因子值 (float) 或因子序列 (pd.Series)", "functions": ["calculate_value", "calculate_series"]}]