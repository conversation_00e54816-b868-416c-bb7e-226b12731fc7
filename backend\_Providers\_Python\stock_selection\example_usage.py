#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选股引擎使用示例
"""

import os
import sys
from typing import Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from engine import StockSelectionEngine

def progress_callback(progress_data: Dict[str, Any]):
    """进度回调函数"""
    stage = progress_data['stage']
    current = progress_data.get('current')
    total = progress_data.get('total')
    selected_count = progress_data.get('selected_count')
    symbol_info = progress_data.get('symbol_info')
    
    if stage == '开始选股':
        print(f"[进度] {stage}")
    elif stage == '获取候选品种列表':
        print(f"[进度] {stage}")
    elif stage == '候选品种获取完成':
        print(f"[进度] {stage}，共 {total} 个品种")
    elif stage == '处理品种':
        progress = (current / total * 100) if total > 0 else 0
        print(f"[进度] {stage}: {current}/{total} ({progress:.1f}%) - 已选中: {selected_count} - {symbol_info}")
    elif stage == '选股完成':
        print(f"[进度] {stage}，共选中 {selected_count} 个品种")

def main():
    """主函数"""
    # 配置选股引擎
    config = {
        'candidate_type': 0,  # 0=A股, 1=期货, 2=美股
        'default_klines': 100,
        'strategy_module': 'strategies/ma_cross_strategy.py',
        'strategy_params': {
            'ma_short': 5,   # 短期均线周期
            'ma_long': 20    # 长期均线周期
        },
        'progress_callback': progress_callback
    }
    
    try:
        # 创建选股引擎
        engine = StockSelectionEngine(config)
        
        # 执行选股
        selected_symbols = engine.run_selection()
        
        # 输出结果
        print(f"\n=== 选股结果 ===")
        print(f"共选中 {len(selected_symbols)} 个品种:")
        
        for i, symbol in enumerate(selected_symbols, 1):
            print(f"{i}. {symbol['code']} - {symbol.get('name', '')}")
            
        # 获取详细结果
        result = engine.get_selection_result()
        print(f"\n=== 详细统计 ===")
        print(f"总候选品种: {result['total_symbols']}")
        print(f"已处理品种: {result['processed_symbols']}")
        print(f"选中品种数: {result['selected_count']}")
        
    except Exception as e:
        print(f"选股执行失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 