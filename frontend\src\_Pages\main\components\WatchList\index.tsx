import React, { useEffect, useState } from 'react';
import { Table, Empty } from 'antd';
import type { Symbol } from '@/shared_types/market';
import { getWatchList } from '@/_Services/mainService';
import { useAtom } from 'jotai';
import { currentSymbolAtom, isLoadingAtom } from '../../../../store/state';
import './index.less';

const WatchList: React.FC = () => {
  const [watchList, setWatchList] = useState<Symbol[]>([]);
  const [isLoading, setLoading] = useAtom(isLoadingAtom);
  const [currentSymbol, setCurrentSymbol] = useAtom(currentSymbolAtom);

  useEffect(() => {
    const loadWatchList = async () => {
      setLoading(true);
      try {
        const data = await getWatchList();
        setWatchList(data);
      } catch (error) {
        console.error('Failed to load watch list:', error);
      } finally {
        setLoading(false);
      }
    };

    loadWatchList();
  }, []);

  const columns = [
    {
      title: '代码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '市场',
      dataIndex: 'market',
      key: 'market',
      width: 60,
    },
  ];

  if (!isLoading && (!watchList || watchList.length === 0)) {
    return (
      <div className="empty-container">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无自选股，请添加"
        />
      </div>
    );
  }

  return (
    <Table
      className="watch-list-table"
      columns={columns}
      dataSource={watchList}
      rowKey="code"
      size="small"
      pagination={false}
      loading={isLoading}
      onRow={(record) => ({
        onClick: () => setCurrentSymbol(record),
        className: record.code === currentSymbol?.code ? 'selected-row' : '',
      })}
      locale={{
        emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无自选股，请添加" />
      }}
    />
  );
};

export default WatchList; 