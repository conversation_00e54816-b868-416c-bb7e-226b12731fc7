const { ChatMessage } = require('../models/ChatMessage');
const { User } = require('../models/User');

// 获取聊天记录
const getChatHistory = async (req, res) => {
  try {
    const messages = await ChatMessage.findAll({
      include: [{
        model: User,
        attributes: ['id', 'username', 'avatar']
      }],
      order: [['createdAt', 'ASC']],
      limit: 100
    });
    res.json(messages);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '获取聊天记录失败' });
  }
};

// 保存聊天消息
const saveMessage = async (message) => {
  try {
    const { userId, content } = message;
    await ChatMessage.create({
      userId,
      content,
      createdAt: new Date()
    });
  } catch (error) {
    console.error('保存聊天消息失败:', error);
  }
};

module.exports = {
  getChatHistory,
  saveMessage
};
