# 实盘策略与数据系统集成说明

## 整体架构

Wonder Trader采用M+1+N执行架构，支持多种交易接口。在实盘环境中，数据系统和策略系统的集成是关键环节。本文档详细说明了从策略部署到实盘运行的完整流程，以及各个组件的作用和关键细节。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  数据源(模拟/实时) │───>│  数据引擎系统   │───>│  策略引擎系统   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                      │                      │
        │                      │                      │
        ▼                      ▼                      ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ HTTP/Socket接口 │    │  共享内存/存储  │    │    交易接口     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 1. 策略部署到实盘流程

### 1.1 配置文件生成
- 通过`config_generator.py`为每个策略生成专用目录和配置文件
- 配置文件包括：
  - `config.yaml`：主配置文件
  - `tdparsers.yaml`：行情解析器配置
  - `tdtraders.yaml`：交易通道配置
  - `executers.yaml`：执行器配置
  - `actpolicy.yaml`：开平策略配置
  - `filters.yaml`：过滤器配置

### 1.2 关键配置点
- 确保所有配置使用统一的数据路径(`DATA_STORAGE_DIR`)
- 确保共享内存路径(`SHM_FILE_PATH`)一致
- 策略配置中的`data:store:path`必须与数据引擎的`writer:path`一致

### 1.3 策略启动流程
- `LiveStrategyRunner`负责策略的启动和停止
- 启动时自动从策略配置中提取需要订阅的品种代码
- 通过数据订阅客户端向数据系统发送订阅请求
- 初始化引擎并加载策略
- 设置交易回调函数记录交易信号

## 2. 数据获取流程

### 2.1 数据源
- 数据源可以是模拟数据(`mock_data_provider.py`)或真实市场数据
- 数据源通过HTTP API和Socket.IO接口对外提供服务
- 支持订阅/取消订阅特定品种的数据
- 提供数据状态查询接口

### 2.2 数据订阅
- 策略启动时，通过`data_subscription_client.py`向数据源发送订阅请求
- 订阅请求包含策略ID和需要订阅的品种代码列表
- 数据源根据订阅请求，提供相应品种的数据
- 策略停止时，自动取消订阅

### 2.3 数据传递
- 数据源将数据推送给ExtParser
- ExtParser将数据转换为Wonder Trader引擎能够理解的格式
- 数据引擎将数据写入共享内存和数据存储
- 策略引擎从共享内存和数据存储读取数据

## 3. ExtParser的作用

### 3.1 接口实现
- `mock_data_ext_parser.py`实现了Wonder Trader的ExtParser接口
- 主要方法包括：
  - `init`：初始化解析器
  - `connect`：连接到数据源
  - `disconnect`：断开连接
  - `subscribe`：订阅品种
  - `unsubscribe`：取消订阅
  - `release`：释放资源

### 3.2 数据处理
- 连接到数据源，接收原始数据
- 将原始数据转换为Wonder Trader引擎能够理解的格式
- 调用数据引擎的回调函数，将数据传递给引擎
- 处理连接断开和重连逻辑

## 4. 模拟和真实数据的切换

### 4.1 模块化设计
- 数据源与数据处理解耦，实现了依赖倒置原则
- 只需要替换`mock_data_provider.py`为真实数据源实现
- 其他组件(`run_mock_system.py`、`mock_data_ext_parser.py`等)不需要修改

### 4.2 切换步骤
1. 实现一个与`mock_data_provider.py`接口兼容的真实数据源
2. 确保新数据源提供相同的HTTP API和Socket.IO接口
3. 启动新数据源替代模拟数据源
4. 无需修改策略系统和数据引擎系统

## 5. 数据和实盘对接的关键和细节

### 5.1 共享内存
- 共享内存是数据引擎和策略引擎之间的关键接口点
- 数据引擎将实时数据写入共享内存
- 策略引擎从共享内存读取实时数据
- 共享内存路径必须一致，通常为`storage/shm/exchange.membin`

### 5.2 数据存储
- 数据引擎将历史数据写入数据存储
- 策略引擎从数据存储读取历史数据
- 数据存储路径必须一致，通常为`storage/`目录下的相应子目录

### 5.3 策略订阅
- 策略需要在`on_init`方法中通过以下方法订阅数据：
  - `context.stra_prepare_bars`：订阅K线数据
  - `context.stra_sub_ticks`：订阅tick数据
- 数据订阅客户端确保数据源知道需要为哪些品种提供数据

## 6. 整体数据流

1. **策略配置与启动**：
   - 生成配置文件 → 初始化策略 → 提取订阅代码 → 发送订阅请求

2. **数据获取与处理**：
   - 数据源 → ExtParser → 数据引擎 → 共享内存/数据存储

3. **策略执行与交易**：
   - 策略引擎 → 共享内存/数据存储 → 策略计算 → 交易信号 → 交易通道

## 7. 系统优势

- **高度模块化**：各组件职责明确，便于维护和扩展
- **灵活性**：数据源可以灵活替换，不影响其他组件
- **统一接口**：统一的数据接口确保系统各部分协同工作
- **无缝切换**：支持从模拟环境无缝切换到实盘环境
- **可扩展性**：可以轻松添加新的数据源和交易接口

## 8. 常见问题与解决方案

### 8.1 数据订阅失败
- 检查数据源是否正常运行
- 检查网络连接是否正常
- 检查订阅请求中的品种代码是否正确
- 查看数据订阅客户端和数据源的日志

### 8.2 策略无法获取数据
- 检查共享内存路径是否一致
- 检查数据存储路径是否一致
- 确认策略已正确订阅数据
- 查看数据引擎和策略引擎的日志

### 8.3 数据源切换问题
- 确保新数据源实现了与旧数据源相同的接口
- 确保新数据源提供的数据格式与旧数据源一致
- 先测试基本功能，再逐步扩展
