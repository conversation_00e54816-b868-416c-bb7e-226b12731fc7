# Strategy Module

## 实盘策略数据结构规范

### 设计原则

**前端与策略完全解耦**：前端实盘界面与具体策略逻辑完全分离，各自专注于自己的职责。

### 通用数据结构规范

#### 1. 实时监控数据 (Runtime Data)

```json
{
  "strategyId": "strategy_123",
  "strategyType": "portfolio",
  "status": {
    "isRunning": true,
    "lastUpdateTime": "2025-07-18T16:30:00Z",
    "uptime": "3天2小时15分钟"
  },
  "performance": {
    "totalReturn": 2.5,
    "dailyReturn": 0.8,
    "currentCapital": 5125.0,
    "initialCapital": 5000.0,
    "maxDrawdown": -1.2
  },
  "positions": {
    "totalCount": 3,
    "totalValue": 3200.0,
    "unrealizedPnL": 125.0,
    "positions": [
      {
        "symbol": "SSE.600036",
        "name": "招商银行",
        "quantity": 100,
        "avgPrice": 32.5,
        "currentPrice": 33.2,
        "unrealizedPnL": 70.0,
        "weight": 0.15
      }
    ]
  },
  "risk": {
    "currentRisk": 0.65,
    "maxRisk": 0.8,
    "var95": -2.1,
    "sharpeRatio": 1.2
  },
  "strategyInfo": {
    // 策略运行产生的特定信息，格式由策略类型决定
    "portfolio": {
      "targetSymbols": [...],
      "factorAnalysis": {...}
    }
  }
}
```

#### 2. 交易记录数据 (Trade Data)

```json
{
  "trades": {
    "today": [
      {
        "time": "2025-07-18T14:30:00Z",
        "symbol": "SSE.600036",
        "action": "BUY",
        "quantity": 100,
        "price": 32.5,
        "amount": 3250.0,
        "commission": 3.25,
        "reason": "策略信号"
      }
    ],
    "recent": [...],
    "statistics": {
      "totalTrades": 45,
      "winRate": 0.68,
      "avgTradeReturn": 0.5
    }
  }
}
```

#### 3. 持仓管理数据 (Position Data)

```json
{
  "positions": {
    "summary": {
      "totalCount": 3,
      "totalValue": 3200.0,
      "totalWeight": 0.65
    },
    "details": [
      {
        "symbol": "SSE.600036",
        "name": "招商银行",
        "quantity": 100,
        "avgPrice": 32.5,
        "currentPrice": 33.2,
        "marketValue": 3320.0,
        "unrealizedPnL": 70.0,
        "weight": 0.15,
        "holdingDays": 5
      }
    ],
    "sectors": {
      "金融": 0.4,
      "科技": 0.35,
      "消费": 0.25
    }
  }
}
```

#### 4. 策略信息数据 (Strategy Info Data)

```json
{
  "strategyInfo": {
    "portfolio": {
      "targetSymbols": [
        {
          "symbol": "SSE.600036",
          "name": "招商银行",
          "targetWeight": 0.15,
          "currentWeight": 0.15,
          "action": "HOLD",
          "reason": "权重符合目标，无需调仓"
        }
      ],
      "factorAnalysis": {
        "factors": [
          {
            "factorName": "momentum",
            "weight": 0.4,
            "topStocks": [...]
          }
        ],
        "factorPerformance": {
          "momentum": 0.8,
          "value": 0.5,
          "quality": 0.7
        }
      }
    }
  }
}
```

#### 5. 绩效分析数据 (Performance Data)

```json
{
  "performance": {
    "returns": {
      "daily": [...],
      "weekly": [...],
      "monthly": [...]
    },
    "metrics": {
      "totalReturn": 2.5,
      "annualizedReturn": 12.5,
      "volatility": 8.2,
      "sharpeRatio": 1.2,
      "maxDrawdown": -1.2,
      "calmarRatio": 10.4
    },
    "benchmark": {
      "benchmarkReturn": 1.8,
      "excessReturn": 0.7,
      "informationRatio": 0.85
    }
  }
}
```

### 数据结构设计原则

1. **通用性**：核心字段对所有策略类型都适用
2. **扩展性**：通过`strategyInfo`字段支持不同策略类型的特定信息
3. **策略无关**：不硬编码特定策略的逻辑
4. **信息分层**：通用信息 + 策略特定信息
5. **实时性**：数据包含时间戳，前端可以判断数据新鲜度
6. **可读性**：字段名清晰，数值有单位说明

### 数据流向

```
策略运行 → 技术日志 → 转接模块解析 → 通用数据结构 → 前端展示
```

### 实现要点

- 前端定义通用的实盘数据结构
- 后端策略输出技术日志
- 中间模块负责日志解析和数据转换
- 前端界面完全基于通用数据结构展示

### 架构优势

1. **解耦性**：前端不需要了解具体策略逻辑
2. **可扩展性**：新增策略类型只需在转接模块适配
3. **维护性**：前端界面和策略逻辑可以独立演进
4. **专业性**：前端专注用户体验，策略专注业务逻辑 