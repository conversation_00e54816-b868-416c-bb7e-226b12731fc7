import React from 'react';
import { Spin } from 'antd';
import { atom, useAtom } from 'jotai';
import './index.less';

// 创建全局状态
export const globalLoadingAtom = atom(false);

const GlobalLoading: React.FC = () => {
  const [isLoading] = useAtom(globalLoadingAtom);

  if (!isLoading) return null;

  return (
    <div className="global-loading-container">
      <Spin size="large" tip="..." spinning={true}>
        <div style={{ width: '100%', height: '100vh' }} />
      </Spin>
    </div>
  );
};

export default GlobalLoading; 