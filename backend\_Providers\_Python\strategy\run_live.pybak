#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Wonder Trader 实盘策略运行脚本
用于启动实盘策略，支持命令行参数配置
"""

import os
import sys
import io
import time
import argparse
import logging
from pathlib import Path

# 设置标准输出和标准错误的编码为UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 配置日志
def setup_logger(log_dir, strategy_id):
    """
    设置日志记录器

    Args:
        log_dir: 日志目录
        strategy_id: 策略ID

    Returns:
        logger: 日志记录器
    """
    log_file = os.path.join(log_dir, f"{strategy_id}.log")

    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)

    # 配置日志
    # 创建处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    stream_handler = logging.StreamHandler()

    # 设置格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    stream_handler.setFormatter(formatter)

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 添加新处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(stream_handler)

    return logging.getLogger(strategy_id)

def main():
    """
    主函数，解析命令行参数并启动策略
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Run Wonder Trader live strategy')
    parser.add_argument('--config_dir', required=True, help='Configuration directory')
    parser.add_argument('--log_dir', required=True, help='Log directory')
    parser.add_argument('--strategy_id', required=True, help='Strategy ID')

    args = parser.parse_args()

    # 设置日志
    logger = setup_logger(args.log_dir, args.strategy_id)

    try:
        logger.info(f"Starting live strategy: {args.strategy_id}")
        logger.info(f"Configuration directory: {args.config_dir}")

        # 获取配置目录的绝对路径
        config_dir_abs = os.path.abspath(args.config_dir)
        logger.info(f"Absolute configuration directory: {config_dir_abs}")

        # 导入数据订阅客户端
        get_data_client = None
        try:
            # 计算正确的路径：从当前文件到项目根目录
            current_file = os.path.abspath(__file__)
            # 向上5级：run_live.py -> strategy -> _Python -> _Providers -> backend -> quantquart
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file)))))
            ext_data_engine_path = os.path.join(project_root, 'utils', 'ext_data_engine')

            if ext_data_engine_path not in sys.path:
                sys.path.insert(0, ext_data_engine_path)

            logger.info(f"Added ext_data_engine path: {ext_data_engine_path}")

            from data_subscription_client import get_client as get_data_client
            logger.info("Successfully imported data subscription client")
        except Exception as e:
            logger.error(f"Failed to import data subscription client: {e}")
            get_data_client = None

        # 记录当前工作目录
        current_dir = os.getcwd()
        logger.info(f"Current working directory before change: {current_dir}")

        # 切换到策略目录的父目录，即 live_configs 目录
        # 这样相对路径 ../common 就会指向正确的位置
        parent_dir = os.path.dirname(config_dir_abs)
        logger.info(f"Changing working directory to: {parent_dir}")
        os.chdir(parent_dir)

        # 记录切换后的工作目录
        new_dir = os.getcwd()
        logger.info(f"Current working directory after change: {new_dir}")

        # 检查 ../common 目录是否存在
        common_path = os.path.abspath("../common")
        logger.info(f"Checking if ../common exists: {common_path}")
        if os.path.exists(common_path):
            logger.info(f"../common directory exists: {common_path}")
            # 列出 ../common 目录中的文件
            try:
                files = os.listdir(common_path)
                logger.info(f"Files in ../common: {files}")
            except Exception as e:
                logger.error(f"Error listing files in ../common: {e}")
        else:
            logger.error(f"../common directory does not exist: {common_path}")

        # 尝试导入 wtpy 模块
        try:
            # 添加wtpy路径
            wtpy_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'wtpy')
            sys.path.append(wtpy_path)
            logger.info(f"Added wtpy path: {wtpy_path}")

            # 导入wtpy模块
            from wtpy import WtEngine, EngineType
            logger.info("Successfully imported wtpy module")

            # 创建引擎实例
            logger.info("Creating engine instance...")
            engine = WtEngine(EngineType.ET_CTA)

            # 初始化引擎
            logger.info("Initializing engine...")

            # 尝试多个可能的common目录路径
            script_dir = Path(__file__).parent
            common_dir_candidates = [
                str(script_dir / 'common'),  # 当前脚本目录下的common
                str(script_dir.parent / 'common'),  # 上级目录下的common
                str(script_dir.parent.parent / 'common'),  # 再上级目录下的common
                str(script_dir.parent.parent.parent / '_Providers' / '_Python' / 'strategy' / 'common')  # 完整路径
            ]

            # 确保路径分隔符一致
            common_dir_candidates = [path.replace('\\', '/') for path in common_dir_candidates]

            common_dir = None
            for candidate in common_dir_candidates:
                if os.path.exists(candidate) and os.path.isdir(candidate):
                    common_dir = candidate
                    logger.info(f"Found common directory: {common_dir}")
                    break
                else:
                    logger.warning(f"Common directory candidate not found: {candidate}")

            if common_dir is None:
                common_dir = str(Path(__file__).parent / 'common')
                common_dir = common_dir.replace('\\', '/')
                logger.warning(f"Using default common directory (may not exist): {common_dir}")

            # 使用相对路径，因为我们已经切换到了父目录
            strategy_dir = os.path.basename(config_dir_abs)
            config_path = os.path.join(strategy_dir, "config.yaml")
            logger.info(f"Strategy directory: {strategy_dir}")

            # 确保路径分隔符一致
            config_path = config_path.replace('\\', '/')

            # 检查配置文件是否存在
            if os.path.exists(config_path):
                logger.info(f"Config file exists: {config_path}")
            else:
                logger.error(f"Config file does not exist: {config_path}")
                # 尝试使用绝对路径
                abs_config_path = os.path.join(config_dir_abs, "config.yaml")
                logger.info(f"Trying absolute path: {abs_config_path}")
                if os.path.exists(abs_config_path):
                    logger.info(f"Config file exists at absolute path: {abs_config_path}")
                    config_path = abs_config_path
                    config_path = config_path.replace('\\', '/')

            logger.info(f"Common directory: {common_dir}")
            logger.info(f"Config path: {config_path}")

            # 检查配置文件是否存在
            if not os.path.exists(config_path):
                logger.error(f"Config file not found: {config_path}")

                # 列出配置目录中的文件
                logger.info(f"Listing files in config directory: {args.config_dir}")
                try:
                    files = os.listdir(args.config_dir)
                    for file in files:
                        file_path = os.path.join(args.config_dir, file)
                        logger.info(f"  - {file} ({os.path.getsize(file_path)} bytes)")
                except Exception as e:
                    logger.error(f"Error listing files: {e}")

                # 检查common目录
                logger.info(f"Checking common directory: {common_dir}")
                if os.path.exists(common_dir):
                    try:
                        common_files = os.listdir(common_dir)
                        logger.info(f"Common directory contains {len(common_files)} files")
                        for file in common_files:
                            logger.info(f"  - {file}")
                    except Exception as e:
                        logger.error(f"Error listing common directory: {e}")
                else:
                    logger.error(f"Common directory does not exist: {common_dir}")

                raise FileNotFoundError(f"Config file not found: {config_path}")

            # 初始化引擎
            logger.info(f"Initializing engine with common_dir={common_dir}, config_path={config_path}")
            engine.init(common_dir, config_path)
            logger.info("Engine initialized successfully")

            # 添加策略路径
            strategy_path = os.path.join(os.path.dirname(__file__), 'portfolio')
            sys.path.append(strategy_path)
            logger.info(f"Added strategy path: {strategy_path}")

            # 设置工作目录为策略路径，确保相对导入正确
            original_dir = os.getcwd()
            os.chdir(strategy_path)
            logger.info(f"Changed working directory to strategy path: {strategy_path}")

            try:
                # 导入策略类
                from MultiFactorsCTA import MultiFactorsCTA
                logger.info("Successfully imported MultiFactorsCTA strategy module")

                # 恢复原始工作目录
                os.chdir(original_dir)
                logger.info(f"Restored working directory to: {original_dir}")

                # 读取策略配置
                # 使用相对路径，因为我们已经切换到了父目录
                strategy_dir = os.path.basename(args.config_dir)
                strategy_yaml_path = os.path.join(strategy_dir, "strategy.yaml")
                # 确保路径分隔符一致
                strategy_yaml_path = strategy_yaml_path.replace('\\', '/')
                logger.info(f"Reading strategy configuration from: {strategy_yaml_path}")

                if os.path.exists(strategy_yaml_path):
                    import yaml
                    with open(strategy_yaml_path, 'r', encoding='utf-8') as f:
                        strategy_yaml = f.read()
                        strategy_config = yaml.safe_load(strategy_yaml)
                    logger.info(f"Strategy configuration loaded successfully: {strategy_config.get('strategy_name', 'Unknown')}")

                    # 创建策略实例
                    logger.info("Creating strategy instance...")
                    strategy_instance = MultiFactorsCTA(
                        name=strategy_config.get('strategy_name', args.strategy_id),
                        codes=strategy_config.get('universe', []),
                        barCnt=strategy_config.get('bar_count', 50),
                        period=strategy_config.get('data_freq', 'day'),
                        # isForStk=strategy_config.get('is_for_stk', True),
                        order_by_config=strategy_config.get('order_by', {}),
                        buy_rules_config=strategy_config.get('buy_rules', {}),
                        sell_rules_config=strategy_config.get('sell_rules', {}),
                        top_n=strategy_config.get('top_n', 1),
                        weighting_scheme=strategy_config.get('weighting_scheme', 'equal'),
                        rebalance_interval=strategy_config.get('rebalance_interval', 'daily')
                    )
                    logger.info("Strategy instance created successfully")

                    # 订阅数据
                    if get_data_client is not None:
                        logger.info("Starting data subscription...")
                        try:
                            data_client = get_data_client()
                            codes = strategy_config.get('universe', [])
                            if codes:
                                logger.info(f"Subscribing to codes: {codes}")
                                success, message, data = data_client.subscribe(args.strategy_id, codes)
                                if success:
                                    logger.info(f"Data subscription successful: {message}")
                                else:
                                    logger.error(f"Data subscription failed: {message}")
                            else:
                                logger.warning("No codes to subscribe")
                        except Exception as e:
                            logger.error(f"Data subscription error: {e}")
                    else:
                        logger.warning("Data subscription client not available")

                    # 添加策略到引擎
                    logger.info("Adding strategy to engine...")
                    engine.add_cta_strategy(strategy_instance)
                    logger.info("Strategy added successfully")
                else:
                    logger.error(f"Strategy configuration file not found: {strategy_yaml_path}")
                    raise FileNotFoundError(f"Strategy configuration file not found: {strategy_yaml_path}")
            except Exception as e:
                logger.error(f"Failed to load and add strategy: {e}", exc_info=True)
                raise

            # 启动引擎
            logger.info("Starting engine...")
            engine.run()
            logger.info("Engine started successfully")

            # 保持进程运行，每30秒输出一次心跳
            count = 0
            while True:
                count += 1
                logger.info(f"Strategy heartbeat #{count}")

                # 这里可以添加性能数据收集逻辑
                # 例如，从引擎获取当前的持仓、盈亏等信息

                time.sleep(30)

        except ImportError as e:
            logger.error(f"Failed to import wtpy module: {e}")
            logger.info("Falling back to simulation mode")

            # 模拟策略运行，每10秒输出一条日志
            count = 0
            while True:
                count += 1
                logger.info(f"Strategy heartbeat (simulation) #{count}")
                time.sleep(10)

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down")
    except Exception as e:
        logger.exception(f"Error running strategy: {e}")
    finally:
        # 取消数据订阅
        if get_data_client is not None:
            logger.info("Unsubscribing from data...")
            try:
                data_client = get_data_client()
                success, message, _ = data_client.unsubscribe(args.strategy_id)
                if success:
                    logger.info(f"Data unsubscription successful: {message}")
                else:
                    logger.error(f"Data unsubscription failed: {message}")
            except Exception as e:
                logger.error(f"Data unsubscription error: {e}")

        logger.info(f"Strategy {args.strategy_id} stopped")

if __name__ == "__main__":
    main()
