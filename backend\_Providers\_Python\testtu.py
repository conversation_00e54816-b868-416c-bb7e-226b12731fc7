import argparse
import tushare as ts

def get_stock_data(ts_code: str, start_date: str, end_date: str) -> None:
    """
    获取指定股票的日线行情数据并打印结果
    参数:
        ts_code: 股票代码（格式示例：600000.SH 或 000001.SZ）
        start_date: 开始日期（格式：YYYYMMDD）
        end_date: 结束日期（格式：YYYYMMDD）
    """
    try:
        # 设置Token（使用用户提供的Token）
        ts.set_token('f5388bec620b339788d9d19249836512f62422adb4317a0c7a52167f')
        pro = ts.pro_api()

        # 调用日线接口
        df = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
        
        if df.empty:
            print(f"未找到 {ts_code} 在 {start_date} 至 {end_date} 之间的数据")
        else:
            print(f"\n{ts_code} 日线数据（共 {len(df)} 条）：")
            print(df[['trade_date', 'open', 'close', 'high', 'low', 'vol']].head())
    except Exception as e:
        print(f"获取数据时出错: {e}")

if __name__ == "__main__":
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='从Tushare获取股票日线数据')
    parser.add_argument('ts_code', type=str, help='股票代码（如600000.SH）')
    parser.add_argument('--start', type=str, default='20250101', help='开始日期（默认：20250101）')
    parser.add_argument('--end', type=str, default='20250606', help='结束日期（默认：20250606）')
    
    args = parser.parse_args()
    
    # 调用函数获取数据
    get_stock_data(args.ts_code, args.start, args.end)