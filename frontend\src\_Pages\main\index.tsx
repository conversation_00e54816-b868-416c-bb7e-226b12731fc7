import React, { useState, useCallback, useEffect } from 'react';
import { Drawer, AutoComplete } from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import ToolBar from './components/ToolBar';
import ChartPanel from './components/ChartPanel';
import GeneList from './components/GeneList';
import WatchList from './components/WatchList';
import GeneDialog from './components/GeneDialog';

import { marketService } from '../../_Services/marketService';
import chartDispatcher from '@/_Dispatchers/ChartDispatcher';
import indicatorListManager from '@/_Modules/Indicators/IndicatorListManager';
import ChatWindow from '@/_Pages/Dashboard/Chat/ChatWindow';
import { useAtom } from 'jotai';
import { chatVisibleAtom } from '@/store/state';
import { FrontendConfig } from '@/shared_types/enviorment';

import './index.less';

const MainPage: React.FC = () => {

  const [drawerVisible, setDrawerVisible] = useState<boolean>(true);
  const [selectedPanel, setSelectedPanel] = useState<'watchlist' | 'genelist'>('watchlist');
  const [signalListVisible, setSignalListVisible] = useState(false);
  const [sessionId, setSessionId] = useState<number | null>(null);
  const [chatVisible] = useAtom(chatVisibleAtom);
  const [config, setConfig] = useState<FrontendConfig | null>(null);

  const handleTimeSelect = useCallback((timestamp: number) => {
    console.log('Selected time:', timestamp);
    // TODO: 实现时间定位逻辑
  }, []);

  useEffect(() => {
    console.log('MainPage 组件挂载');

    // 从public目录获取配置文件
    fetch('/config.json')
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        setConfig(data); // 设置配置数据
      })
      .catch(error => {
        console.error('Error fetching config:', error);
      });

    // 初始化 chartDispatcher 分发器
    chartDispatcher.initialize();

    // 不再初始化指标列表管理器
  }, []);

  const renderSiderContent = () => {
    switch (selectedPanel) {
      case 'watchlist':
        return <WatchList />;
      case 'genelist':
        return <GeneList />;
      default:
        return null;
    }
  };

  return (
    <div className="main-container">
      {/* 顶部工具栏 */}
      <ProCard
        title={<ToolBar />}
        headerBordered
        bordered={false}
        style={{ marginBottom: 16 }}
      />

      {/* 行情研究主内容区域 */}
      <div className="main-content">
        {/* 左侧图表区域 */}
        <div className="chart-content">
          <ChartPanel />
          {/* Drawer 触发器 */}
          <div
            className="drawer-trigger"
            onClick={() => setDrawerVisible(!drawerVisible)}
          >
            {drawerVisible ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
          </div>
        </div>

      </div>

      <GeneDialog />

    </div>
  );
};

export default MainPage;