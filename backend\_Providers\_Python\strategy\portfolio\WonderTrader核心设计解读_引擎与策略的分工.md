# WonderTrader核心设计解读：引擎与策略的明确分工

## 引言：大厨与厨房经理

想象一个专业厨房：

*   **大厨 (策略)**：负责构思菜单（交易逻辑），决定什么时候上什么菜（交易信号），以及每道菜最终应该是什么样子（目标持仓）。
*   **厨房经理 (引擎)**：负责管理整个厨房的运作，确保食材充足（数据获取），处理订单（执行交易），准确记录库存（跟踪持仓），并保证一切流程顺畅高效。

大厨不需要亲自去管理每一分库存的精确成本变化，他只需要告诉经理："我需要最终得到 5 份牛排"，经理就会去协调采购、烹饪和库存调整。

WonderTrader (wtpy) 的核心设计就像这个厨房，它明确区分了"策略"和"引擎"的职责，理解这一点是高效使用 `wtpy` 的关键。

## 核心理念：关注点分离

`wtpy` 最重要的设计哲学是 **关注点分离 (Separation of Concerns)**：

1.  **引擎 (厨房经理) 的职责：底层执行与状态跟踪**
    *   **精确的持仓管理**: 引擎的核心任务是精确地跟踪每个交易品种的**持仓数量 (`position`)**。这是保证交易正确执行的基础。
    *   **可靠的交易执行**: 管理订单的发送、接收市场反馈（如成交回报 `on_trade`、订单状态 `on_order`），模拟撮合（回测）或对接实盘通道。
    *   **高效的数据驱动**: 处理实时或历史行情数据，并在恰当的时机（如新 Tick、K 线闭合）通知策略。
    *   **日志记录**: 详细记录所有交易活动和资金变化，用于后续分析。

2.  **策略 (大厨) 的职责：交易决策**
    *   **信号生成**: 根据市场数据和内部逻辑，判断何时是交易时机。
    *   **目标确定**: 决定要买入/卖出什么品种，以及最终**期望达到的持仓状态**是什么（持有多少手？或者完全清仓？）。
    *   **响应事件**: 通过实现 `on_tick`, `on_bar`, `on_calculate` 等方法来响应引擎推送的市场事件。
    *   **通过 Context 交互**: 策略通过 `Context` 对象这座"桥梁"与引擎沟通，获取必要数据 (`stra_get_bars`)、查询当前持仓 (`stra_get_position`)，并发送交易意图 (`stra_enter_long`, `stra_set_position` 等)。

## 为何引擎不实时提供"动态权益"给策略？

很多初学者可能会问：为什么我不能在策略运行的每时每刻通过 `context.stra_get_fund_data()` 轻松获取一个包含了实时盈亏、手续费、滑点计算在内的精确"动态账户权益"呢？

`wtpy` 的设计决策是：**引擎的核心职责在于精确跟踪"持仓"变化，而不是实时计算复杂的"动态权益"并暴露给策略 `Context`**。

*   **复杂性与性能**: 实时精确计算动态权益非常复杂，需要考虑每一笔交易的成本、滑点、手续费、持仓盈亏等，这会给引擎带来巨大的计算负担，可能影响核心的交易执行效率。
*   **关注点分离**: 让引擎专注于它最擅长的事情——交易执行和持仓管理。把复杂的、全局性的绩效计算放在**回测/交易结束之后**进行。
*   **后处理分析的精确性**: 引擎在运行期间会记录所有必要的基础数据（资金流水、交易记录）。结束后，可以使用这些完整的记录，通过 `WtBtAnalyst` 或自定义脚本进行**更精确、更全面**的绩效分析，生成资金曲线、计算夏普比率等，而不用担心实时计算可能存在的误差或性能瓶颈。

## 策略应该如何应对？（资金与仓位管理）

理解了引擎的分工，策略在编写时就应该：

1.  **聚焦决策**: 不要期望在 `on_calculate` 或 `on_bar` 中得到一个完美无缺的实时"账户总值"。策略的核心是**做出交易决策**。
2.  **计算目标仓位使用固定基准**: 当你需要计算目标买入多少手（比如根据总资金的百分比）时，你需要一个资金基准。最佳实践是：
    *   **回测中**: 使用**初始资金 (`initial_capital`)** 作为计算仓位的稳定基准。这个值在策略初始化时设定，贯穿整个回测过程。
    *   **实盘中**: 可以在交易日开始时获取一次账户的起始权益，或者根据预设的风险规则来确定每次调仓使用的资金基准。
3.  **选择合适的 API 函数**:
    *   **事件驱动型 (信号来了就买/卖)**: 使用 `stra_enter_long`, `stra_enter_short`, `stra_exit_long`, `stra_exit_short`。明确表达单一操作意图。
    *   **状态驱动型 (组合再平衡)**: 使用 `stra_set_position(code, target_units)`。这是核心！你告诉引擎："对于 `code`，我最终想要持有 `target_units` 手"。
        *   如果 `target_units` > 0，引擎会计算当前持仓与目标的差异，自动买入或卖出相应手数。
        *   如果 `target_units` == 0，引擎会执行必要的操作将该品种的仓位清零。
        *   这完美契合了"策略决定目标状态，引擎负责执行到位"的理念。

## 这样做的好处

*   **策略代码更清晰**: 专注于交易逻辑和目标设定。
*   **潜在的性能提升**: 引擎可以更高效地处理核心任务。
*   **精确的离线分析**: 基于完整的回测日志进行可靠的绩效评估。

希望这篇解读能帮助你更好地理解 WonderTrader 的设计哲学，并编写出更符合其理念的高效策略！ 