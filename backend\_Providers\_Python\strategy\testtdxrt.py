import requests

# tdxserver.py服务地址
BASE_URL = "http://127.0.0.1:5003/kline"

# 测试标的
test_cases = [
    # A股
    {"market": "sh", "code": "600000", "period": "1m"},
    {"market": "sz", "code": "000001", "period": "5m"},
    # ETF
    {"market": "sh", "code": "510300", "period": "1d"},
    {"market": "sh", "code": "510500", "period": "1d"},
]

for case in test_cases:
    params = {
        "market": case["market"],
        "code": case["code"],
        "period": case["period"],
        "count": 5
    }
    print(f"\n请求: {params}")
    try:
        resp = requests.get(BASE_URL, params=params, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        print("返回数据样例:", data[:2])  # 只打印前两条
    except Exception as e:
        print("请求失败:", e)