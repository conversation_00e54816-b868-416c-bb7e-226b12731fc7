root:
    async: false
    level: debug
    sinks:
    -   type: basic_file_sink
        filename: BtLogs/Runner.log
        pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
        truncate: false
        
    -   type: console_sink
        pattern: '[%m.%d %H:%M:%S - %^%-5l%$] %v'
        

dyn_pattern:
    strategy:
        async: false
        level: debug
        sinks:
        -   filename: BtLogs/Strategy_%s.log
            pattern: '[%Y.%m.%d %H:%M:%S - %-5l] %v'
            truncate: false
            type: basic_file_sink