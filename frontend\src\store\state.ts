import { atom, createStore } from 'jotai';
import { 
  ExchangeType, 
  Operator, 
  MarketType, 
  Symbol as MarketSymbol,
  IndicatorConfig,
  KLineData,
  KLineInterval,
  KLine
} from '@/shared_types/market';
import { Indicator } from '@/shared_types/indicator';
import { IndicatorWrapper } from '@/_Modules/Indicators/IndicatorWrapper';
import { DrawingLine, DrawingLineRecord, DrawingToolType } from '@/shared_types/chart';

export enum ToolbarButtonType {
  NONE = 'none',
  DRAWING = 'drawing',
  RANGE_SELECT = 'range_select'
}

// 默认上证指数
const defaultSymbol: MarketSymbol = {
  market: MarketType.INDEX,
  exchange: ExchangeType.SSE,
  code: '000001.SH',
  name: '上证指数',
};

// 默认日线周期
export const selectedSymbolAtom = atom<MarketSymbol>(defaultSymbol);
export const selectedPeriodAtom = atom<KLineInterval>(KLineInterval.DAY1);
export const currentSymbolAtom = atom<MarketSymbol>(defaultSymbol);
export const currentPeriodAtom = atom<KLineInterval>(KLineInterval.DAY1);
export const isChartSyncPauseAtom = atom<boolean>(false);
export const isLoadingAtom = atom<boolean>(false);

// ToolPane控制
export type ToolPaneContent = 'signal-list' | 'symbol-list' | null;
export const toolPaneContentAtom = atom<ToolPaneContent>(null);
export const toolPaneVisibleAtom = atom<boolean>(false); 

export const globalActiveChart = {
  instance: null as any,
  id: '' as string
};

export interface GeneConfig {
  id: string;
  name: string;
  condition: {
    indicator: Indicator;
    operator: Operator;
    value: number;
    params: {
      period: number;
    };
  };
}

export const currentGeneConfigAtom = atom<GeneConfig | null>(null);


// 主图指标数组
export const mainIndicatorsAtom = atom<IndicatorWrapper[]>([]);
export const chatVisibleAtom = atom<boolean>(false);
export const indicatorInstancesAtom = atom<IndicatorWrapper[]>([]);

// 副图指标数组
export const subIndicatorsAtom = atom<IndicatorWrapper[]>([]);

// K线数据状态
export const chartDataAtom = atom<KLineData[]>([]); 

// 初始化画线数据
export const initialDrawingLinesAtom = atom<DrawingLineRecord[]>([]);

export const klineAtom = atom<KLine>({
  id: '',
  data: [],
  symbol: defaultSymbol,
  period: KLineInterval.DAY1
});
export const jotaiStore = createStore();

// 添加画线工具状态
export const drawingToolAtom = atom<DrawingToolType>(DrawingToolType.NONE);

// 添加全局工具栏状态
export const activeToolbarButtonAtom = atom<ToolbarButtonType>(ToolbarButtonType.NONE);

export const symbolListNameAtom = atom<string>('浏览历史');
export const symbolListIsSystemAtom = atom<boolean>(false);

