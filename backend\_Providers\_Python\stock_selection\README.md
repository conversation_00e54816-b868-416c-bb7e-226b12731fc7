# 选股引擎

## 概述

选股引擎是一个基于Python的股票筛选系统，支持多种选股策略，能够从大量候选品种中筛选出符合条件的股票。

## 架构设计

### 核心组件

1. **StockSelectionEngine**: 选股引擎主类
2. **策略模块**: 可插拔的选股策略
3. **进度报告**: 实时进度回调机制
4. **数据获取**: 直接调用app.py函数获取K线数据

### 目录结构

```
stock_selection/
├── __init__.py                 # 模块初始化
├── engine.py                   # 选股引擎主文件
├── example_usage.py            # 均线交叉策略使用示例
├── example_pattern_usage.py    # 形态选股策略使用示例
├── strategies/                 # 策略模块目录
│   ├── __init__.py
│   ├── base_strategy.py        # 策略基类
│   ├── ma_cross_strategy.py    # 均线交叉策略示例
│   └── pattern_strategy.py     # 形态选股策略
└── README.md                   # 说明文档
```

## 功能特性

### 1. 支持多种市场
- **A股股票** (candidate_type=0)
- **期货** (candidate_type=1)
- **美股** (candidate_type=2)

### 2. 灵活的策略系统
- 动态加载策略模块
- 参数化配置
- 统一接口规范

### 3. 实时进度报告
- 阶段进度报告
- 处理进度百分比
- 选中品种统计

### 4. 高效数据获取
- 直接调用app.py函数
- 支持指定K线数量
- 避免HTTP请求开销

## 策略模块

### 1. 均线交叉策略 (ma_cross_strategy.py)
基于短期和长期移动平均线的交叉信号进行选股。

**参数配置:**
```python
strategy_params = {
    'ma_short': 5,    # 短期均线周期
    'ma_long': 20     # 长期均线周期
}
```

### 2. 形态选股策略 (pattern_strategy.py)
基于价格形态序列匹配的选股策略。

**策略原理:**
1. 将K线数据转换为形态序列
2. 与目标形态序列进行匹配度计算
3. 根据匹配度判断是否选中

**参数配置:**
```python
strategy_params = {
    'pattern': [5, 4, 3, 2, 2, 1, 1, 5],  # 形态定义数组
    'match_threshold': 0.4,                  # 匹配度阈值 (0-1)
    'grid_count': 5                          # 网格数量
}
```

**形态计算逻辑:**
1. 获取窗口内所有K线的最高价和最低价
2. 将价格区间分为grid_count个等份
3. 根据每根K线的收盘价落在哪个区间，生成形态序列
4. 与目标形态序列进行匹配度计算

## 使用方法

### 基本使用

```python
from stock_selection import StockSelectionEngine

# 配置引擎
config = {
    'candidate_type': 0,  # A股
    'default_klines': 100,
    'strategy_module': 'strategies/pattern_strategy.py',
    'strategy_params': {
        'pattern': [5, 4, 3, 2, 2, 1, 1, 5],
        'match_threshold': 0.4,
        'grid_count': 5
    },
    'progress_callback': your_progress_callback
}

# 创建引擎并执行选股
engine = StockSelectionEngine(config)
selected_symbols = engine.run_selection()
```

### 进度回调函数

```python
def progress_callback(progress_data):
    stage = progress_data['stage']
    current = progress_data.get('current')
    total = progress_data.get('total')
    selected_count = progress_data.get('selected_count')
    
    if stage == '处理品种':
        if current is not None and total is not None and total > 0:
            progress = (current / total * 100)
        else:
            progress = 0
        print(f"进度: {progress:.1f}% - 已选中: {selected_count}")
```

## 策略开发

### 策略接口规范

每个策略模块必须包含以下函数：

```python
def select_stock(klines: List[Dict[str, Any]], params: Dict[str, Any]) -> bool:
    """
    选股策略函数
    
    Args:
        klines: K线数据列表
        params: 策略参数字典
        
    Returns:
        bool: True表示选中，False表示不选中
    """
    pass

def get_required_klines() -> int:
    """
    获取策略需要的K线数量
    
    Returns:
        int: 需要的K线数量
    """
    pass
```

### K线数据格式

```python
klines = [
    {
        'time': timestamp,      # 秒级时间戳
        'open': float,          # 开盘价
        'high': float,          # 最高价
        'low': float,           # 最低价
        'close': float,         # 收盘价
        'volume': float,        # 成交量
        'amount': float,        # 成交额
        'change': float,        # 涨跌额
        'change_pct': float     # 涨跌幅(%)
    },
    # ... 更多K线数据
]
```

## 配置参数

### 引擎配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| candidate_type | int | 0 | 候选品种类型 (0=A股, 1=期货, 2=美股) |
| default_klines | int | 100 | 默认K线数量 |
| strategy_module | str | - | 策略模块文件路径 |
| strategy_params | dict | {} | 策略参数字典 |
| progress_callback | function | None | 进度回调函数 |

### 策略参数示例

```python
# 均线交叉策略参数
strategy_params = {
    'ma_short': 5,    # 短期均线周期
    'ma_long': 20     # 长期均线周期
}

# 形态选股策略参数
strategy_params = {
    'pattern': [5, 4, 3, 2, 2, 1, 1, 5],  # 形态定义
    'match_threshold': 0.4,                  # 匹配度阈值
    'grid_count': 5                          # 网格数量
}
```

## 运行示例

```bash
# 运行均线交叉策略示例
cd backend/_Providers/_Python/stock_selection
python example_usage.py

# 运行形态选股策略示例
python example_pattern_usage.py
```

## 扩展开发

### 添加新策略

1. 在 `strategies/` 目录下创建新的策略文件
2. 实现 `select_stock()` 和 `get_required_klines()` 函数
3. 在配置中指定策略文件路径

### 自定义进度报告

```python
def custom_progress_callback(progress_data):
    # 自定义进度处理逻辑
    pass

config['progress_callback'] = custom_progress_callback
```

## 注意事项

1. **数据路径**: 确保app.py中的数据路径配置正确
2. **策略模块**: 策略文件路径必须是绝对路径或相对于引擎的相对路径
3. **错误处理**: 单个品种的错误不会影响整体选股进程
4. **内存使用**: 大量品种选股时注意内存使用情况

## 性能优化

1. **数据缓存**: 可考虑添加K线数据缓存机制
2. **并行处理**: 可扩展为多进程并行处理
3. **增量更新**: 支持增量选股，只处理新增品种 