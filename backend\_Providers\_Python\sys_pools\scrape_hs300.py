# -*- coding: utf-8 -*-
import logging
import re
from bs4 import BeautifulSoup, Tag # Import Tag for type checking
from scraper_utils import fetch_url, format_stock_data, save_to_json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

BASE_URL = "http://vip.stock.finance.sina.com.cn/corp/view/vII_NewestComponent.php"
INDEX_ID = "000300" # CSI 300 Index
OUTPUT_FILENAME = "沪深300.json"

def get_total_pages(html_content: str) -> int:
    """Extract the total number of pages from the pagination."""
    soup = BeautifulSoup(html_content, 'html.parser')
    pager_div = soup.find('div', class_='pages')
    if not isinstance(pager_div, Tag): # Type check
        logging.warning("Pagination div not found or not a Tag, assuming 1 page.")
        return 1
    
    page_links = pager_div.find_all('a')
    if not page_links:
        logging.warning("No page links found in pagination, assuming 1 page.")
        return 1
        
    max_page = 1
    for link in page_links:
        href = link.get('href')
        if href and 'page=' in href:
            match = re.search(r'page=(\d+)', href)
            if match:
                try:
                    page_num = int(match.group(1))
                    max_page = max(max_page, page_num)
                except ValueError:
                    continue
                    
    logging.info(f"Detected {max_page} pages for index {INDEX_ID}.")
    return max_page

def scrape_page(page_num: int) -> list:
    """Scrape stock data from a single page."""
    page_url = f"{BASE_URL}?indexid={INDEX_ID}&page={page_num}"
    logging.info(f"Scrapping page {page_num}: {page_url}")
    html = fetch_url(page_url)
    if not html:
        return []

    soup = BeautifulSoup(html, 'html.parser')
    stock_list = []

    stock_table = soup.find('table', id='NewStockTable')
    if not isinstance(stock_table, Tag): # Type check
        logging.warning(f"Could not find stock table or it's not a Tag on page {page_num}.")
        return []
        
    rows = stock_table.find_all('tr')
    if len(rows) <= 1:
        logging.info(f"No data rows found on page {page_num}.")
        return []
        
    for row in rows[1:]:
        if not isinstance(row, Tag): continue # Skip non-Tag rows
        cells = row.find_all('td')
        if len(cells) >= 2:
            code_cell_tag = cells[0].find('div')
            name_cell_tag = cells[1].find('div')
            
            # Check if the found elements are indeed Tags before accessing text
            if isinstance(code_cell_tag, Tag) and isinstance(name_cell_tag, Tag):
                code = code_cell_tag.text.strip()
                name = name_cell_tag.text.strip()
                
                if re.fullmatch(r'\d{6}', code):
                    formatted_data = format_stock_data(code, name)
                    if formatted_data:
                        stock_list.append(formatted_data)
                else:
                     logging.warning(f"Skipping invalid code format: '{code}' on page {page_num}")
            else:
                logging.warning(f"Could not find valid code/name div Tags in cells on page {page_num}")
        else:
            logging.warning(f"Row does not have enough cells on page {page_num}")
            
    logging.info(f"Found {len(stock_list)} stocks on page {page_num}.")
    return stock_list

def scrape_hs300():
    """Scrape all pages for CSI 300 constituents and save to JSON."""
    logging.info("Starting CSI 300 (沪深300) constituents scrape...")
    first_page_url = f"{BASE_URL}?indexid={INDEX_ID}&page=1"
    first_page_html = fetch_url(first_page_url)
    if not first_page_html:
        logging.error("Failed to fetch the first page. Aborting.")
        return

    total_pages = get_total_pages(first_page_html)
    all_stocks = []

    logging.info("Processing page 1...")
    all_stocks.extend(scrape_page_content(first_page_html, 1))

    for page in range(2, total_pages + 1):
        all_stocks.extend(scrape_page(page))

    if all_stocks:
        unique_stocks = {item['symbol']: item for item in all_stocks}.values()
        save_to_json(list(unique_stocks), OUTPUT_FILENAME)
    else:
        logging.warning("No stocks were scraped. JSON file not saved.")

def scrape_page_content(html: str, page_num: int) -> list:
    """Helper to scrape stocks from already fetched HTML content."""
    soup = BeautifulSoup(html, 'html.parser')
    stock_list = []
    stock_table = soup.find('table', id='NewStockTable')
    if not isinstance(stock_table, Tag): # Type check
        logging.warning(f"Could not find stock table or it's not a Tag on page {page_num} (from content).")
        return []
    rows = stock_table.find_all('tr')
    if len(rows) <= 1:
        logging.info(f"No data rows found on page {page_num} (from content).")
        return []
    for row in rows[1:]:
        if not isinstance(row, Tag): continue # Skip non-Tag rows
        cells = row.find_all('td')
        if len(cells) >= 2:
            code_cell_tag = cells[0].find('div')
            name_cell_tag = cells[1].find('div')
            if isinstance(code_cell_tag, Tag) and isinstance(name_cell_tag, Tag):
                code = code_cell_tag.text.strip()
                name = name_cell_tag.text.strip()
                if re.fullmatch(r'\d{6}', code):
                    formatted_data = format_stock_data(code, name)
                    if formatted_data:
                        stock_list.append(formatted_data)
                else:
                     logging.warning(f"Skipping invalid code format: '{code}' on page {page_num} (from content)")
            else:
                logging.warning(f"Could not find valid code/name div Tags in cells on page {page_num} (from content)")
        else:
            logging.warning(f"Row does not have enough cells on page {page_num} (from content)")
    logging.info(f"Found {len(stock_list)} stocks on page {page_num} (from content).")
    return stock_list

if __name__ == "__main__":
    scrape_hs300() 