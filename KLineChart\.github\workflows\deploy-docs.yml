name: Deploy docs

# on:
#   push:
#     branches: [ "main" ]
#     paths:
#       - 'docs/**'

on:
  release:
    types: [created]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
      - uses: actions/setup-node@v4
        with:
          node-version: 21
          cache: pnpm
      - run: pnpm install
      - run: pnpm run docs:build
      - uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.DEPLOY_DOCS_TOKEN }}
          publish_dir: ./website