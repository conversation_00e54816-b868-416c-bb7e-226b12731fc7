import subprocess
import threading
import time
import signal
import os
import json
from typing import Dict, Optional
from dataclasses import dataclass, asdict
import logging

@dataclass
class ProcessInfo:
    pid: int
    project_id: str
    command: list
    cwd: str
    start_time: float
    status: str  # 'running', 'stopped', 'error', 'starting'
    last_check: float
    
    def to_dict(self):
        return asdict(self)

class LiveProcessManager:
    """
    实盘进程管理器 - 专为量化交易设计
    支持几百个并发进程，每个对应一个实盘组合
    """
    
    def __init__(self):
        self.processes: Dict[str, ProcessInfo] = {}
        self.subprocesses: Dict[str, subprocess.Popen] = {}
        self.lock = threading.RLock()
        self.running = True
        
        # 设置日志
        self.logger = logging.getLogger('LiveProcessManager')
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        # 注册信号处理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
    def start_process(self, project_id: str, command: list, cwd: str) -> tuple[bool, str, Optional[int]]:
        """
        启动新的实盘进程
        
        Args:
            project_id: 项目ID（唯一标识）
            command: 命令行参数列表，如 [sys.executable, 'run.py']
            cwd: 工作目录
            
        Returns:
            tuple[bool, str, Optional[int]]: (是否成功, 消息, PID)
        """
        with self.lock:
            try:
                # 检查是否已存在
                if project_id in self.processes:
                    existing = self.processes[project_id]
                    if existing.status == 'running':
                        return False, f"Process {project_id} already running (PID: {existing.pid})", existing.pid
                    else:
                        # 清理旧的已停止进程
                        self._cleanup_process(project_id)
                
                self.logger.info(f"Starting process for project {project_id}: {' '.join(command)}")
                
                # 启动进程
                process = subprocess.Popen(
                    command,
                    cwd=cwd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=0,  # 无缓冲，实时输出
                    preexec_fn=os.setsid if os.name != 'nt' else None  # Unix: 创建新进程组
                )
                
                # 记录进程信息
                current_time = time.time()
                process_info = ProcessInfo(
                    pid=process.pid,
                    project_id=project_id,
                    command=command,
                    cwd=cwd,
                    start_time=current_time,
                    status='running',
                    last_check=current_time
                )
                
                self.processes[project_id] = process_info
                self.subprocesses[project_id] = process
                
                self.logger.info(f"Process {project_id} started successfully (PID: {process.pid})")
                return True, f"Process started successfully", process.pid
                
            except Exception as e:
                self.logger.error(f"Error starting process {project_id}: {e}")
                return False, f"Error starting process: {e}", None
    
    def stop_process(self, project_id: str, force: bool = False) -> tuple[bool, str]:
        """
        停止指定进程
        
        Args:
            project_id: 项目ID
            force: 是否强制终止
            
        Returns:
            tuple[bool, str]: (是否成功, 消息)
        """
        with self.lock:
            try:
                if project_id not in self.processes:
                    return False, f"Process {project_id} not found"
                
                process_info = self.processes[project_id]
                subprocess_obj = self.subprocesses.get(project_id)
                
                if not subprocess_obj:
                    return False, f"Subprocess object not found for {project_id}"
                
                self.logger.info(f"Stopping process {project_id} (PID: {process_info.pid})")
                
                if force:
                    # 强制终止
                    subprocess_obj.kill()
                    subprocess_obj.wait(timeout=5)
                else:
                    # 优雅停止
                    subprocess_obj.terminate()
                    try:
                        subprocess_obj.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        self.logger.warning(f"Process {project_id} didn't stop gracefully, force killing")
                        subprocess_obj.kill()
                        subprocess_obj.wait()
                
                # 更新状态
                process_info.status = 'stopped'
                
                # 清理
                self._cleanup_process(project_id)
                
                self.logger.info(f"Process {project_id} stopped successfully")
                return True, f"Process stopped successfully"
                
            except Exception as e:
                self.logger.error(f"Error stopping process {project_id}: {e}")
                return False, f"Error stopping process: {e}"
    
    def get_process_status(self, project_id: str) -> Optional[dict]:
        """
        获取进程状态
        """
        with self.lock:
            if project_id in self.processes:
                return self.processes[project_id].to_dict()
            return None
    
    def list_all_processes(self) -> Dict[str, dict]:
        """
        列出所有进程
        """
        with self.lock:
            return {pid: info.to_dict() for pid, info in self.processes.items()}
    
    def get_running_count(self) -> int:
        """
        获取运行中进程数量
        """
        with self.lock:
            return sum(1 for info in self.processes.values() if info.status == 'running')
    
    def _monitor_loop(self):
        """
        监控循环 - 检查进程状态
        """
        while self.running:
            try:
                self._check_all_processes()
                time.sleep(5)  # 每5秒检查一次
            except Exception as e:
                self.logger.error(f"Error in monitor loop: {e}")
                time.sleep(10)
    
    def _check_all_processes(self):
        """
        检查所有进程状态
        """
        with self.lock:
            dead_processes = []
            
            for project_id, process_info in self.processes.items():
                if process_info.status != 'running':
                    continue
                    
                subprocess_obj = self.subprocesses.get(project_id)
                if not subprocess_obj:
                    continue
                
                # 检查进程是否还在运行
                poll_result = subprocess_obj.poll()
                process_info.last_check = time.time()
                
                if poll_result is not None:
                    # 进程已退出
                    self.logger.warning(f"Process {project_id} (PID: {process_info.pid}) exited with code {poll_result}")
                    process_info.status = 'stopped'
                    dead_processes.append(project_id)
            
            # 清理已死亡的进程
            for project_id in dead_processes:
                self._cleanup_process(project_id)
    
    def _cleanup_process(self, project_id: str):
        """
        清理进程资源
        """
        try:
            if project_id in self.subprocesses:
                subprocess_obj = self.subprocesses[project_id]
                if subprocess_obj.poll() is None:
                    subprocess_obj.terminate()
                del self.subprocesses[project_id]
                
            if project_id in self.processes:
                self.processes[project_id].status = 'stopped'
                # 可选择是否删除记录，这里保留历史记录
                
        except Exception as e:
            self.logger.error(f"Error cleaning up process {project_id}: {e}")
    
    def _signal_handler(self, signum, frame):
        """
        信号处理器 - 优雅关闭
        """
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.shutdown()
    
    def shutdown(self):
        """
        关闭管理器，停止所有进程
        """
        self.logger.info("Shutting down LiveProcessManager...")
        self.running = False
        
        with self.lock:
            # 停止所有运行中的进程
            for project_id in list(self.processes.keys()):
                if self.processes[project_id].status == 'running':
                    self.stop_process(project_id, force=True)
        
        self.logger.info("LiveProcessManager shutdown complete")

# 全局管理器实例
live_process_manager = LiveProcessManager()