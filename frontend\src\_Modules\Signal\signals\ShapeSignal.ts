import { <PERSON><PERSON>ineData, KLine } from '@/shared_types/market';
import { BaseSignal, SignalParameter, SignalType } from '../BaseSignal';
import { SignalConfig } from '@/shared_types/signal';
import { MarketEvents } from '@/events/events';
import { EventBus } from '@/events/eventBus';
import { getIndicatorByType } from '@/_Modules/Indicators/indicatorCatalog';
import { ShapeConfig, ShapeConfigItem } from '@/shared_types/shape';
import { IndicatorWrapper } from '@/_Modules/Indicators/IndicatorWrapper';

import { jotaiStore, klineAtom } from '@/store/state';
import { useRef } from 'react';

/**
 * 形态匹配信号
 * 通过匹配已保存的形态配置来判断当前市场状态
 * 当市场形态与保存的形态匹配度达到阈值时产生买入信号
 */
export class ShapeSignal extends BaseSignal {
  private shapeName: string;
  private matchThreshold: number;
  private shapeConfigs: ShapeConfigItem[] = [];
  private indicatorWrappers: Map<string, IndicatorWrapper> = new Map();
  private cachedKline: KLine | null = null;  // 添加 KLine 缓存

  private calculationPending = false;

  constructor(config: SignalConfig) {
    super(config);
    
    // 设置参数
    this.shapeName = this.parameters.shapeName;
    this.matchThreshold = this.parameters.matchThreshold;

    // 验证参数
    if (this.matchThreshold < 20 || this.matchThreshold > 100) {
      throw new Error('Match threshold must be between 20 and 100');
    }

    // 加载形态配置，获取该形态对应的信息数据，包括特征序列等
    this.loadShapeConfig();
  }

  /**
   * 获取信号参数列表
   */
  /**
   * 获取当前实例的形态特征序列（values），一个形态配置最多只能有一个形态特征序列
   * 一个形态一定有一个SHAPE配置，对应values就是特征序列
   */
  getShapeValues(): number[] {
    if (!this.shapeConfigs || this.shapeConfigs.length === 0) {
      console.warn('[ShapeSignal] 未加载形态配置，特征序列为空');
      return [];
    }
    
    // 遍历shapeConfigs数组，找到SHAPE配置，返回其values
    for (const config of this.shapeConfigs) {
      if (config.type == 'SHAPE') {
        return config.values || [];
      }
    }
    
    return [];
  }

  static async getParameters(): Promise<SignalParameter[]> {
    // 获取形态配置列表
    const shapeConfigs = await new Promise<ShapeConfig[]>((resolve) => {
      EventBus.emit(MarketEvents.Types.GET_ALLSHAPECONFIGS, {
        callback: (shapes: ShapeConfig[]) => {
          console.log('[ShapeSignal] 获取形态配置列表:', shapes);
          resolve(shapes);
        }
      });
    });

    // 提取形态名称作为选项
    const shapeNames = shapeConfigs.map(shape => ({
      name: shape.name,
      klines: shape.klineData
    }));

    console.log('[ShapeSignal].getParameters: 获取形态名称作为选项:', shapeNames);

    return [
      {
        name: 'shapeName',
        paramType: 'string',
        default: shapeNames[0].name || '',
        description: '形态配置名称',
        options: shapeNames
      },
      {
        name: 'matchThreshold',
        paramType: 'number',
        default: 80,
        description: '形态匹配度阈值（20-100）',
        minValue: 20,
        maxValue: 100,
        step: 1
      }
    ];
  }
  
  // 匹配算法，欧式距离=euclidean,DTW=dtw
  private matchAlgorithm = 'grid'; //欧式距离=euclidean,euclideanX欧式多次,DTW=dtw,simple=简单匹配，grid=格线匹配

  /**
   * 加载形态配置
   */
  private async loadShapeConfig(): Promise<void> {
    try {
      // 发送事件获取形态配置
      EventBus.emit(MarketEvents.Types.GET_SHAPECONFIGDETAILS, {
        name: this.shapeName,
        callback: (config: ShapeConfigItem[]) => {
          console.log('获取到形态配置具体数据:', config);
          this.shapeConfigs = config;
          let title = '形态: ' + this.shapeName;

          // 如果现在等待计算，并且有缓存的K线数据，则计算
          if (this.calculationPending && this.cachedKline) {
            const signals = this.calculateSignals(this.cachedKline);

            // 发布计算完成的事件
            EventBus.emit(MarketEvents.Types.SIGNALCALCULATED_RESULT, {
              title: title,
              signals: signals
            });
          }
        }
      });
    } catch (error) {
      console.error('加载形态配置失败:', error);
    }
  }

  /**
 * 标准化数组（将值缩放到0-1范围）
 */
private standardizeArray(arr: number[]): number[] {
  if (arr.length === 0) return arr;
  const min = Math.min(...arr);
  const max = Math.max(...arr);
  const range = max - min;
  return range === 0 
    ? arr.map(() => 0.5) // 所有值相同时返回中间值
    : arr.map(v => (v - min) / range);
}

  /**
   * 线性插值数组到指定长度
   * @param arr 原始数组
   * @param targetLength 目标长度
   * @returns 插值后的数组
   */
  private interpolateArray(arr: number[], targetLength: number): number[] {
    const result = new Array(targetLength);
    const originalLength = arr.length;
    
    // 如果原始长度为1，则填充相同的值
    if (originalLength === 1) {
      return new Array(targetLength).fill(arr[0]);
    }
    
    for (let i = 0; i < targetLength; i++) {
      // 计算在原始数组中的位置（可能是小数）
      const position = i * (originalLength - 1) / (targetLength - 1);
      
      // 获取左右两个最近的点
      const index = Math.floor(position);
      const nextIndex = Math.min(index + 1, originalLength - 1);
      
      // 计算插值权重
      const weight = position - index;
      
      // 线性插值
      result[i] = arr[index] * (1 - weight) + arr[nextIndex] * weight;
    }
    
    return result;
  }

  /**
   * 计算两个数组的相似度（0-1）
   * 使用欧氏距离计算相似度，支持不同长度的数组
   */
  private calculateEuclideanSimilarity(arr1: number[], arr2: number[]): number {
    if (arr1.length === 0 || arr2.length === 0) return 0;

    // 对两个数组进行标准化
    const standardized1 = this.standardizeArray(arr1);
    const standardized2 = this.standardizeArray(arr2);

    // 处理不同长度的数组
    let processedArr1 = standardized1;
    let processedArr2 = standardized2;
    
    // 如果长度不同，将较短的数组插值到较长数组的长度
    if (standardized1.length !== standardized2.length) {
      const targetLength = Math.max(standardized1.length, standardized2.length);
      
      if (standardized1.length < targetLength) {
        processedArr1 = this.interpolateArray(standardized1, targetLength);
      }
      
      if (standardized2.length < targetLength) {
        processedArr2 = this.interpolateArray(standardized2, targetLength);
      }
    }

    // 计算欧氏距离
    let sumSquaredDiff = 0;
    for (let i = 0; i < processedArr1.length; i++) {
      sumSquaredDiff += Math.pow(processedArr1[i] - processedArr2[i], 2);
    }

    const distance = Math.sqrt(sumSquaredDiff);
    const similarity = 1 / (1 + distance); // 转换为0-1范围
    return similarity;
  }

  /**
   * 使用多窗口欧式距离计算两个数组的相似度（0-1）
   * 支持不同长度的序列比较，通过多个滑动窗口取最佳匹配
   * @param arr1 形态模板数组
   * @param arr2 当前市场数据数组（通常比arr1长20%左右）
   * @returns 相似度（0-1范围）
   */
  private calculateMultiWindowEuclideanSimilarity(arr1: number[], arr2: number[]): number {
    if (arr1.length === 0 || arr2.length === 0) return 0;
    
    // 对数组进行标准化
    const standardized1 = this.standardizeArray(arr1);
    const standardized2 = this.standardizeArray(arr2);
    
    const L = standardized1.length;
    
    // 定义不同的窗口大小比例
    const windowSizeRatios = [0.8, 0.9, 1.0, 1.1, 1.2];
    let maxSimilarity = 0;
    
    let result = '';

    // 对每个窗口大小进行欧式距离计算，取最大相似度
    for (const ratio of windowSizeRatios) {
      const windowSize = Math.round(L * ratio);
      
      // 确保窗口大小有效
      if (windowSize <= 0 || windowSize > standardized2.length) continue;
      
      // 获取standardized2的最后windowSize个数据
      const windowData = standardized2.slice(-windowSize);
      
      // 计算当前窗口的相似度
      const similarity = this.calculateEuclideanSimilarity(standardized1, windowData);
      result += similarity.toFixed(4) + ',';
      // 更新最大相似度
      if (similarity > maxSimilarity) {
        maxSimilarity = similarity;
      }
      

    }

    console.log('欧式多次相似度：', result);

    return maxSimilarity;
  }

  /**
   * 使用DTW算法计算两个数组的相似度（0-1）
   * 支持不同长度的序列比较，通过多个滑动窗口取最佳匹配
   * @param arr1 形态模板数组
   * @param arr2 当前市场数据数组（通常比arr1长20%左右）
   * @returns 相似度（0-1范围）
   */
  private calculateSimilarityDTW(arr1: number[], arr2: number[]): number {
    if (arr1.length === 0 || arr2.length === 0) return 0;
    
    // 对数组进行标准化
    const standardized1 = this.standardizeArray(arr1);
    const standardized2 = this.standardizeArray(arr2);
    
    const L = standardized1.length;
    
    // 定义不同的窗口大小比例
    //const windowSizeRatios = [0.8, 0.9, 1.0, 1.1, 1.2];
    const windowSizeRatios = [1];
    let minDtwDistance = Number.MAX_VALUE;
    
    // 对每个窗口大小进行DTW计算，取最小距离
    for (const ratio of windowSizeRatios) {
      const windowSize = Math.round(L * ratio);
      
      // 确保窗口大小有效
      if (windowSize <= 0 || windowSize > standardized2.length) continue;
      
      // 对于较长的序列，尝试不同的起始位置
      const maxStartPos = standardized2.length - windowSize;
      
      for (let startPos = 0; startPos <= maxStartPos; startPos++) {
        // 提取当前窗口的数据
        const windowData = standardized2.slice(startPos, startPos + windowSize);
        
        // 计算DTW距离
        const dtwDist = this.computeDTW(standardized1, windowData);
        
        // 更新最小距离
        if (dtwDist < minDtwDistance) {
          minDtwDistance = dtwDist;
        }
      }
    }
    
    // 将DTW距离转换为相似度（0-1范围）
    // 使用 2*L-1 作为归一化因子，这是DTW可能的最大距离
    const normalizedDistance = minDtwDistance / (2 * L - 1);
    const similarity = 1 - Math.min(normalizedDistance, 1); // 确保相似度在0-1范围内
    
    return similarity;
  }
  
  /**
   * 计算两个序列的DTW距离
   * @param s1 第一个序列（标准化后）
   * @param s2 第二个序列（标准化后）
   * @returns DTW距离
   */
  private computeDTW(s1: number[], s2: number[]): number {
    const n = s1.length;
    const m = s2.length;
    
    // 创建DTW矩阵并初始化为无穷大
    const dtw: number[][] = Array(n + 1).fill(0).map(() => Array(m + 1).fill(Infinity));
    
    // 设置初始值
    dtw[0][0] = 0;
    
    // 填充DTW矩阵
    for (let i = 1; i <= n; i++) {
      for (let j = 1; j <= m; j++) {
        // 计算当前点的欧氏距离
        const cost = Math.pow(s1[i - 1] - s2[j - 1], 2);
        
        // DTW递推关系：取左、上、左上三个方向的最小值加上当前代价
        dtw[i][j] = cost + Math.min(
          dtw[i - 1][j],     // 插入
          dtw[i][j - 1],     // 删除
          dtw[i - 1][j - 1]  // 匹配
        );
      }
    }
    
    // 返回右下角的值，即两个序列的DTW距离
    return Math.sqrt(dtw[n][m]);
  }

  private calcShapeMatch(shapeMatchData: number[], currentValues: Record<string, number[]>, theTime: number): number {
    // 获取当前K线数据
    if (!this.cachedKline || !this.cachedKline.data) {
      console.warn('[形态匹配] 缺少K线数据');
      return 0;
    }

    // 找到当前时间对应的K线索引
    const currentIndex = this.cachedKline.data.findIndex(kline => kline.time === theTime);
    if (currentIndex === -1) {
      console.warn('[形态匹配] 未找到对应时间的K线数据');
      return 0;
    }

    // 获取与保存形态相同长度的K线数据窗口
    const windowSize = shapeMatchData.length;
    const startIndex = currentIndex - windowSize + 1;
    const endIndex = currentIndex;

    // 检查索引范围是否有效
    if (startIndex < 0 || endIndex >= this.cachedKline.data.length) {
      console.warn(`[形态匹配] 窗口索引超出范围: startIndex=${startIndex}, endIndex=${endIndex}, dataLength=${this.cachedKline.data.length}`);
      return 0;
    }

    // 提取当前窗口的K线数据
    const currentWindowData = this.cachedKline.data.slice(startIndex, endIndex + 1);
    console.log('[形态匹配] 当前窗口K线数据长度:', currentWindowData.length);

    // 使用SHAPE指标的特征提取方法计算1-5序列
    const shapeIndicator = getIndicatorByType('SHAPE');
    if (!shapeIndicator || !shapeIndicator.extractFeatures) {
      console.warn('[形态匹配] 未找到SHAPE指标或extractFeatures方法');
      return 0;
    }

    // 计算当前窗口的1-5特征序列
    const currentFeatures = shapeIndicator.extractFeatures(currentWindowData);
    console.log('[形态匹配] 当前窗口1-5序列:', currentFeatures);
    console.log('[形态匹配] 保存的形态1-5序列:', shapeMatchData);

    // 计算特征序列的相似度（完全一样为1，完全不一样为0）
    const similarity = this.calculateFeatureSimilarity(shapeMatchData, currentFeatures);
    console.log('[形态匹配] 相似度:', similarity);

    return similarity;
  }

  /**
   * 计算两个特征序列的相似度
   * @param savedFeatures 保存的形态特征序列（1-5）
   * @param currentFeatures 当前窗口的特征序列（1-5）
   * @returns 相似度（0-1）
   */
  private calculateFeatureSimilarity(savedFeatures: number[], currentFeatures: number[]): number {
    if (savedFeatures.length === 0 || currentFeatures.length === 0) {
      return 0;
    }

    if (savedFeatures.length !== currentFeatures.length) {
      console.warn(`[形态匹配] 特征序列长度不匹配: 保存=${savedFeatures.length}, 当前=${currentFeatures.length}`);
      return 0;
    }

    // 计算匹配的元素数量
    let matchCount = 0;
    for (let i = 0; i < savedFeatures.length; i++) {
      if (savedFeatures[i] === currentFeatures[i]) {
        matchCount++;
      }
    }

    // 返回匹配度（匹配元素数量 / 总长度）
    const similarity = matchCount / savedFeatures.length;
    console.log(`[形态匹配] 匹配元素: ${matchCount}/${savedFeatures.length}, 相似度: ${similarity}`);
    
    return similarity;
  }

  /**
   * 计算当前市场状态与保存形态的匹配度（0-100）
   */
  private calcDistanceMatch(currentValueSets: Record<string, number[]>, theTime: number): number {
    if (!this.shapeConfigs || this.shapeConfigs.length === 0) return 0;

    let totalMatchDegree = 0;

    console.log(this.matchAlgorithm == 'euclidean' ? '欧式距离：' : this.matchAlgorithm == 'euclideanX' ? '欧式多次：' : this.matchAlgorithm == 'dtw' ? 'DTW：' : '格线：', this.shapeConfigs);

    // 遍历形态配置中的每个指标
    for (const config of this.shapeConfigs) {
      const { type, selectedLine, values, weight } = config;

      //样本切片，使用全样本
      let windowSize = config.values.length;
      if (this.matchAlgorithm == 'dtw' || this.matchAlgorithm == 'euclideanX') {
        windowSize = Math.ceil(windowSize * 1.2);
      }
      
      // 获取当前市场该指标的值（取最后windowSize个数据点）
      const currentIndicatorValues = currentValueSets[`${type}.${selectedLine}`]?.slice(-windowSize);
      if (!currentIndicatorValues || currentIndicatorValues.length !== windowSize) {
        console.warn(`指标 ${type}.${selectedLine} 数据长度不匹配，预期 ${windowSize}，实际 ${currentIndicatorValues?.length}`);
        continue;
      }

      //console.log(`指标 ${type}.${selectedLine}.${config.params} 样本值:`, values);
      //console.log(`指标 ${type}.${selectedLine}.${config.params} 当前值:`, currentIndicatorValues);

      // 计算该指标的相似度并乘以权重
      let similarity = 
        type == 'SHAPE' ? this.calculateFeatureSimilarity(values, currentIndicatorValues) : 
        this.matchAlgorithm == 'euclidean' 
        ? this.calculateEuclideanSimilarity(values, currentIndicatorValues) 
        : this.matchAlgorithm == 'euclideanX' 
          ? this.calculateMultiWindowEuclideanSimilarity(values, currentIndicatorValues)
          : this.calculateSimilarityDTW(values, currentIndicatorValues);

      // 打印相似度以及当前所计算的k线时间，打印当前时区的时间
      console.log(`[形态] k线时间：${new Date(theTime < 10000000000 ? theTime * 1000 : theTime).toLocaleString()} 指标 ${type}.${selectedLine}.${JSON.stringify(config.params)} 相似度:`, similarity);

      const weightedSimilarity = similarity * weight;
      
      // 累加到总匹配度
      totalMatchDegree += weightedSimilarity;
    }

    // 将相似度转换为百分比（0-100）
    return Math.min(Math.max(totalMatchDegree * 100, 0), 100);
  }

  calculateSignals(kline: KLine): MarketEvents.SignalItem[] {
    // 缓存 kline 数据
    this.cachedKline = kline;
    console.log('形态计算：缓存kline', this.cachedKline);
    
    const signals: MarketEvents.SignalItem[] = [];
    const data = kline.data;

    this.calculationPending = true;
    console.log('计算形态：', this.shapeConfigs);

    // 如果没有形态配置数据，返回空信号
    if (!this.shapeConfigs || this.shapeConfigs.length === 0) {
      return signals;
    }

    //样本切片宽度是固定的
    let windowSize = this.shapeConfigs[0].values.length;
    // 如果使用DTW算法，样本切片宽度需要增加20%
    if (this.matchAlgorithm == 'dtw' || this.matchAlgorithm == 'euclideanX') {
      windowSize = Math.ceil(windowSize * 1.2);
    }

    console.log('windowSize', windowSize, ' 当前的k线数据长度：', data.length);
    // 获取当前K线数据
    if (!data || data.length < windowSize) return signals;

    // 使用变量记录 基础形态（SHAPE） 的数据序列
    let k_Shape_MatchData: number[] = [];

    // 为该形态的每个形态配置，所有的形态，一定有一个形态特征序列，对应k线的特征序列，也就是一定存在一个 SHAPE 配置，对应values就是特征序列
    // 创建对应的指标计算器，一个形态配置一般就对应一个指标
    // 对于 SHAPE 虚拟指标，直接使用特征序列，不需要创建指标计算器
    for (const config of this.shapeConfigs) {
      const { type, params } = config;

      // 对于SHAPE虚拟指标，values就是该形态的基础特征序列，每种形态必备
      if (type == 'SHAPE') {
        k_Shape_MatchData = config.values;
        continue;
      }

      const key = `${type}`;
      
      if (!this.indicatorWrappers.has(key)) {
        const Indicator = getIndicatorByType(type);
        const wrapper = new IndicatorWrapper(
          Indicator, 
          this.cachedKline,  // 使用缓存的 kline
          params
        );
        this.indicatorWrappers.set(key, wrapper);
      }
    }

    // ---执行到这个步骤，已经创建了指标计算器，并且缓存了kline数据，接下来就是遍历k线数据，计算形态匹配度

    // 遍历K线数据
    for (let i = windowSize; i < data.length; i++) {

      // 收集当前k线窗口的 所有 指标数据，包括 基础形态 的特征序列
      const currentValueSets: Record<string, number[]> = {};
      
      // 获取当前位置的每个指标的数据序列的切片
      for (const config of this.shapeConfigs) {
        const { type, selectedLine } = config;

        // 如果当前指标是 SHAPE，计算此K线片段的特征序列，否则计算该指标的特征序列

        if (type == 'SHAPE') {
          const shapeIndicator = getIndicatorByType('SHAPE');
          const shapeValues = shapeIndicator.extractFeatures(data.slice(i - windowSize + 1, i + 1));
          currentValueSets[`${type}.${selectedLine}`] = shapeValues;
          continue;
        }

        // 对于其他指标，使用指标计算器计算该指标的特征序列
        const wrapper = this.indicatorWrappers.get(`${type}`);
        
        if (wrapper) {
          const values = wrapper.getValues();
          
          if (values && values[selectedLine]) {
            // 获取与保存形态相同窗口大小的数据进行比较
            const windowValues = values[selectedLine].slice(i - windowSize + 1, i + 1);
            currentValueSets[`${type}.${selectedLine}`] = windowValues;
          }
        }
      }

      // 获取当前走势图切片区域的最后一根k线的时间
      const theTime = data[i].time;

      // 计算 基础形态 匹配度
      // currentValues 包含了当前位置的所有指标数值切片
      //const matchScore = this.calcDistanceMatch(currentValueSets, theTime);
      const matchScore = this.calcShapeMatch(k_Shape_MatchData, currentValueSets, theTime);

      console.log('形态匹配度：', matchScore, ' 阈值：', this.matchThreshold / 100);

      // 如果匹配度超过阈值，生成买入信号
      if (matchScore >= this.matchThreshold / 100) {
        const signal: MarketEvents.SignalItem = {
          time: data[i].time,
          type: SignalType.BUY,
          price: data[i].close,
          index: i
        };
        
        this.addSignalTrigger(signal);
        signals.push(signal);
        
        // 跳过一定数量的K线，避免信号过于密集
        i += Math.max(...this.shapeConfigs.map(c => windowSize || c.values.length)) || 20;
      }
    }

    this.calculationPending = false;
    return signals;
  }

  // 在组件销毁时清理资源
  public destroy(): void {
    // 清理所有指标包装器
    Array.from(this.indicatorWrappers.values()).forEach(wrapper => {
      wrapper.destroy();
    });
    this.indicatorWrappers.clear();
    
    // 清理缓存的K线数据
    this.cachedKline = null;

    console.log('形态计算：清理缓存的K线数据', this.cachedKline);
  }

  private static getShapeOptions(): Promise<ShapeConfig[]> {
    return new Promise((resolve, reject) => {
        // 发布事件请求形态配置
        EventBus.emit(MarketEvents.Types.GET_ALLSHAPECONFIGS, {
            callback: (shapes: ShapeConfig[]) => {
                if (shapes && shapes.length > 0) {
                    // 提取形态名称
                    const shapeNames = shapes.map(shape => shape.name);
                    resolve(shapes); // 返回形态名称列表
                } else {
                    resolve([]); // 如果没有形态配置，返回空数组
                }
            }
        });
    });
  }
} 