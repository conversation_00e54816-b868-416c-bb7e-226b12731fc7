### 修改策略 (仅限自定义策略)

通过此端点可以修改一个已存在的自定义策略的 YAML 配置。系统策略不允许修改。

*   **URL:** `/strategy/update`
*   **Method:** `PUT`
*   **Request Body (JSON):**
    ```json
    {
      "strategy_id": "<要修改的策略名称>",
      "strategy_yaml": "<新的策略配置YAML内容>"
    }
    ```
    *   `strategy_id`: 需要修改的自定义策略的名称（通常是 UUID）。
    *   `strategy_yaml`: 新的、完整的策略配置 YAML 字符串。

*   **Success Response (200 OK):**
    ```json
    {
      "success": true,
      "message": "Strategy '<strategy_id>' updated successfully."
    }
    ```

*   **Error Responses:**
    *   `400 Bad Request`: 请求体不是 JSON 格式，或缺少必要的字段 (`strategy_id`, `strategy_yaml`)。或者提供的 `strategy_yaml` 无效，或者 YAML 内部的 `strategy_id` 与请求的 `strategy_id` 不匹配。
    *   `403 Forbidden`: 尝试修改一个系统策略。
    *   `404 Not Found`: 指定的 `strategy_id` 在自定义策略数据库中未找到。
    *   `500 Internal Server Error`: 更新数据库或删除回测结果时发生意外错误。

*   **逻辑:**
    1.  接收 `strategy_id` 和 `strategy_yaml`。
    2.  检查 `strategy_id` 是否为系统策略。如果是，返回 403 错误。
    3.  查询 `custom_strategies` 表，检查是否存在 `strategy_id`。如果不存在，返回 404 错误。
    4.  校验 `strategy_yaml` 的格式是否合规。如果不合规，返回 400 错误。
    5.  解析 `strategy_yaml`，获取其内部的 `strategy_id` 字段值。
    6.  比较 YAML 内部的 `strategy_id` 和请求中的 `strategy_id`。如果不一致，返回 400 错误。
    7.  更新 `custom_strategies` 表中对应记录的 `content_yaml` 字段为新的 `strategy_yaml`。
    8.  调用内部函数删除 `backtest_results.duckdb` 中与该 `strategy_id` 相关的所有回测结果。
    9.  如果更新和删除（或尝试删除）完成，返回 200 成功消息。
