# backend/_Providers/_Python/strategy/account_manager.py
import datetime

class SimpleAccountManager:
    """
    一个简单的策略内部账户余额管理器。
    注意：这是一个简化的模型，不考虑利息、精确的保证金占用和释放、以及交易成本。
    """
    def __init__(self, initial_capital: float):
        """
        初始化账户管理器。

        Args:
            initial_capital: 初始资金。
        """
        if initial_capital < 0:
            # 允许初始资金为0，但不允许为负
            print(f"[账户管理] 错误：初始资金不能为负数 ({initial_capital})，将设置为0。")
            self._initial_capital = 0.0
        else:
            self._initial_capital = float(initial_capital)

        self._balance = self._initial_capital
        print(f"[账户管理] 账户初始化，初始资金: {self._initial_capital:.2f}, 当前余额: {self._balance:.2f}")

    def get_balance(self) -> float:
        """获取当前账户余额。"""
        return self._balance

    def deposit(self, amount: float, reason: str = "Deposit"):
        """
        向账户存入资金。

        Args:
            amount: 存入金额 (必须为正数)。
            reason: 存款原因（用于日志）。
        """
        if amount <= 0:
            print(f"[账户管理] 警告：尝试存入非正数金额 ({amount:.2f})，操作取消。")
            return

        old_balance = self._balance
        self._balance += amount
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}][账户管理] {reason}: +{amount:.2f}，余额从 {old_balance:.2f} 变为 {self._balance:.2f}")

    def withdraw(self, amount: float, reason: str = "Withdraw") -> bool:
        """
        尝试从账户提取资金。

        Args:
            amount: 提取金额 (必须为正数)。
            reason: 提款原因（用于日志）。

        Returns:
            bool: 如果余额充足则提取成功返回 True，否则返回 False。
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if amount <= 0:
            print(f"[{timestamp}][账户管理] 警告：尝试提取非正数金额 ({amount:.2f})，操作取消。")
            return False # 不能提取非正数

        if self._balance >= amount:
            old_balance = self._balance
            self._balance -= amount
            print(f"[{timestamp}][账户管理] {reason}: -{amount:.2f}，余额从 {old_balance:.2f} 变为 {self._balance:.2f}，提取成功。")
            return True
        else:
            print(f"[{timestamp}][账户管理] {reason}: 尝试提取 {amount:.2f}，但余额不足 ({self._balance:.2f})，提取失败。")
            return False

    def set_initial_capital(self, capital: float):
        """(可选) 重新设置初始资金并重置余额。"""
        if capital < 0:
            print(f"[账户管理] 错误：初始资金不能为负数 ({capital})，设置失败。")
            return
        self._initial_capital = float(capital)
        self._balance = self._initial_capital
        print(f"[账户管理] 重新设置初始资金为: {self._initial_capital:.2f}, 当前余额已重置。")
