/**
 * 根据 A 股或 ETF 的 6 位数字代码，补全完整的 symbol 字符串。
 * 注意：此函数不包含名称查找，名称直接使用代码本身。
 *       输入的 codes 数组应已通过验证。
 */

interface CompletedSymbol {
  symbol: string;
  name: string;
}

export function completeAShareCodes(codes: string[]): CompletedSymbol[] {
  const completedSymbols: CompletedSymbol[] = [];

  for (const code of codes) {
    if (typeof code !== 'string' || !/^[0-9]{6}$/.test(code)) {
      console.warn(`[A股代码补全] 跳过无效代码: ${code} (非6位数字字符串)`);
      continue;
    }

    let exchange = '';
    let category = '';

    if (code.startsWith('6')) {
      exchange = 'SSE';
      category = 'STOCK';
    } else if (code.startsWith('0') || code.startsWith('3')) {
      exchange = 'SZSE';
      category = 'STOCK';
    } else if (code.startsWith('8') || code.startsWith('4')) {
      exchange = 'BSE';
      category = 'STOCK';
    } else if (code.startsWith('5')) {
      exchange = 'SSE';
      category = 'ETF';
    } else if (code.startsWith('15')) {
      exchange = 'SZSE';
      category = 'ETF';
    } else {
      console.warn(`[A股代码补全] 跳过无法识别前缀的代码: ${code}`);
      continue; // Skip codes with unrecognized prefixes
    }

    completedSymbols.push({
      symbol: `${exchange}.${category}.${code}`,
      name: code, // Use code as name
    });
  }

  console.log(`[A股代码补全] 成功补全 ${completedSymbols.length} / ${codes.length} 个代码`);
  return completedSymbols;
} 