#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Wonder Trader XTP适配器

该模块实现了Wonder Trader所需的XTP接口，
将Wonder Trader的交易请求转发到我们的后端tradeHandler模块。

使用方法：
在tdtraders.yaml中配置：
traders:
-   active: true
    client: 1
    host: 127.0.0.1        # 后端服务器地址
    id: custom_xtp         # 交易通道标识符
    module: TraderXTP      # 使用XTP模块
    user: username         # 用户名
    pass: dummy_password   # 占位符密码
    acckey: user1_easytrader_ths  # clientKey，用于标识具体的交易客户端
    port: 3000             # 后端服务器端口
    quick: true
"""

import logging
import requests
import json
from typing import Dict, Any, Optional

logger = logging.getLogger("WTXTPAdapter")

class WTXTPAdapter:
    """
    Wonder Trader XTP适配器
    实现Wonder Trader所需的XTP接口，将交易请求转发到后端tradeHandler
    """
    
    def __init__(self, params: Dict[str, Any]):
        """
        初始化XTP适配器
        
        Args:
            params: 配置参数，来自tdtraders.yaml，包含：
                - user: 用户名
                - acckey: 客户端标识（clientKey）
                - host: 后端服务器地址
                - port: 后端服务器端口
        """
        self.username = params.get('user', '')
        self.acckey = params.get('acckey', '')  # 这就是clientKey
        self.host = params.get('host', '127.0.0.1')
        self.port = params.get('port', 3000)
        self.timeout = 10
        
        # 构建后端API基础URL
        self.backend_url = f"http://{self.host}:{self.port}/api/trade"
        
        # 检查参数
        if not self.username:
            raise ValueError("未指定用户名 (user)")
        
        if not self.acckey:
            raise ValueError("未指定客户端标识 (acckey)")
        
        logger.info(f"初始化Wonder Trader XTP适配器:")
        logger.info(f"  用户名: {self.username}")
        logger.info(f"  客户端标识: {self.acckey}")
        logger.info(f"  后端地址: {self.backend_url}")
        
        # 检查后端连接
        self._check_backend_connection()
    
    def _check_backend_connection(self) -> bool:
        """检查与后端tradeHandler的连接"""
        try:
            # 测试后端连接
            test_url = f"{self.backend_url}/test_connection"
            response = requests.get(test_url, timeout=self.timeout)
            if response.status_code == 200:
                logger.info(f"成功连接到后端tradeHandler: {self.backend_url}")
                return True
            else:
                logger.error(f"连接后端失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"检查后端连接异常: {e}")
            return False
    
    def _request_backend(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送请求到后端tradeHandler"""
        url = f"{self.backend_url}{endpoint}"
        try:
            # 添加认证信息
            request_data = {
                'acckey': self.acckey,  # 使用acckey标识客户端
                'username': self.username,
                **(data or {})
            }
            
            response = requests.post(url, json=request_data, timeout=self.timeout)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return result.get('data', {})
                else:
                    error_msg = result.get('message', '未知错误')
                    logger.error(f"后端请求失败: {error_msg}")
                    raise Exception(error_msg)
            else:
                logger.error(f"后端请求失败，状态码: {response.status_code}")
                raise Exception(f"后端请求失败，状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"请求后端时发生错误: {str(e)}")
            raise
    
    # ===== Wonder Trader XTP接口实现 =====
    
    def buy(self, code: str, price: float, volume: int) -> Dict[str, Any]:
        """买入操作"""
        logger.info(f"[XTP] 买入请求: code={code}, price={price}, volume={volume}")
        
        data = {
            'action': 'buy',
            'params': {
                'code': code,
                'price': float(price),
                'amount': int(volume)
            }
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            logger.info(f"[XTP] 买入成功: {result}")
            return result
        except Exception as e:
            logger.error(f"[XTP] 买入失败: {e}")
            raise
    
    def sell(self, code: str, price: float, volume: int) -> Dict[str, Any]:
        """卖出操作"""
        logger.info(f"[XTP] 卖出请求: code={code}, price={price}, volume={volume}")
        
        data = {
            'action': 'sell',
            'params': {
                'code': code,
                'price': float(price),
                'amount': int(volume)
            }
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            logger.info(f"[XTP] 卖出成功: {result}")
            return result
        except Exception as e:
            logger.error(f"[XTP] 卖出失败: {e}")
            raise
    
    def cancel_entrust(self, entrust_no: str) -> Dict[str, Any]:
        """撤单操作"""
        logger.info(f"[XTP] 撤单请求: entrust_no={entrust_no}")
        
        data = {
            'action': 'cancel',
            'params': {
                'entrust_no': entrust_no
            }
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            logger.info(f"[XTP] 撤单成功: {result}")
            return result
        except Exception as e:
            logger.error(f"[XTP] 撤单失败: {e}")
            raise
    
    def query_balance(self) -> Dict[str, Any]:
        """查询账户余额"""
        logger.debug(f"[XTP] 查询账户余额")
        
        data = {
            'action': 'query_balance',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[XTP] 查询余额失败: {e}")
            raise
    
    def query_position(self) -> Dict[str, Any]:
        """查询持仓"""
        logger.debug(f"[XTP] 查询持仓")
        
        data = {
            'action': 'query_position',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[XTP] 查询持仓失败: {e}")
            raise
    
    def query_entrusts(self) -> Dict[str, Any]:
        """查询委托"""
        logger.debug(f"[XTP] 查询委托")
        
        data = {
            'action': 'query_entrusts',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[XTP] 查询委托失败: {e}")
            raise
    
    def query_trades(self) -> Dict[str, Any]:
        """查询成交"""
        logger.debug(f"[XTP] 查询成交")
        
        data = {
            'action': 'query_trades',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[XTP] 查询成交失败: {e}")
            raise
    
    def refresh(self) -> Dict[str, Any]:
        """刷新数据"""
        logger.debug(f"[XTP] 刷新数据")
        
        data = {
            'action': 'refresh',
            'params': {}
        }
        
        try:
            result = self._request_backend('/wt_trade_api', data)
            return result
        except Exception as e:
            logger.error(f"[XTP] 刷新失败: {e}")
            raise

# Wonder Trader模块入口函数
def create_trader(params: Dict[str, Any]) -> WTXTPAdapter:
    """
    创建XTP交易适配器实例
    这是Wonder Trader调用的入口函数
    """
    return WTXTPAdapter(params)
