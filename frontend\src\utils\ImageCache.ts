export class ImageCache {
  private static DB_NAME = 'ImageCache';
  private static STORE_NAME = 'images';
  private static VERSION = 1;

  private static async openDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.VERSION);

      request.onerror = () => {
        console.error('[ImageCache] Failed to open database:', request.error);
        reject(request.error);
      };
      
      request.onsuccess = () => {
        console.log('[ImageCache] Database opened successfully');
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        console.log('[ImageCache] Database upgrade needed');
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          db.createObjectStore(this.STORE_NAME);
          console.log('[ImageCache] Image store created');
        }
      };
    });
  }

  static async get(url: string): Promise<Blob | null> {
    try {
      const cacheKey = url.includes('/database/sessions/uploadfiles') 
        ? url.replace('/database/sessions/uploadfiles', '/chatfiles')
        : url;

      console.log('[ImageCache] Attempting to get image from cache:', cacheKey);
      const db = await this.openDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(this.STORE_NAME, 'readonly');
        const store = transaction.objectStore(this.STORE_NAME);
        const request = store.get(cacheKey);

        request.onerror = () => {
          console.error('[ImageCache] Error getting image from cache:', request.error);
          reject(request.error);
        };
        
        request.onsuccess = () => {
          if (request.result) {
            console.log('[ImageCache] Image found in cache:', cacheKey);
          } else {
            console.log('[ImageCache] Image not found in cache:', cacheKey);
          }
          resolve(request.result);
        };
      });
    } catch (error) {
      console.error('[ImageCache] Failed to get image from cache:', error);
      return null;
    }
  }

  static async set(url: string, blob: Blob): Promise<void> {
    try {
      const cacheKey = url.includes('/database/sessions/uploadfiles') 
        ? url.replace('/database/sessions/uploadfiles', '/chatfiles')
        : url;

      console.log('[ImageCache] Attempting to cache image:', cacheKey);
      const db = await this.openDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(this.STORE_NAME, 'readwrite');
        const store = transaction.objectStore(this.STORE_NAME);
        const request = store.put(blob, cacheKey);

        request.onerror = () => {
          console.error('[ImageCache] Error caching image:', request.error);
          reject(request.error);
        };
        
        request.onsuccess = () => {
          console.log('[ImageCache] Image cached successfully:', cacheKey);
          resolve();
        };
      });
    } catch (error) {
      console.error('[ImageCache] Failed to cache image:', error);
    }
  }
} 