#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Wonder Trader组合运行脚本
用于启动单个或多个策略的组合
"""

import os
import sys
import yaml
import json
import argparse
import logging
from pathlib import Path

# 设置标准输出编码为UTF-8（Windows兼容性）
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 添加wtpy路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent.parent
wtpy_path = project_root / 'python_venv' / 'Lib' / 'site-packages'
sys.path.insert(0, str(wtpy_path))

# 添加当前策略目录到Python路径，以便导入portfolio模块
strategy_dir = Path(__file__).parent
sys.path.insert(0, str(strategy_dir))

# 添加portfolio目录到Python路径，以便MultiFactorsCTA.py能找到eval_formula.py
portfolio_dir = strategy_dir / 'portfolio'
sys.path.insert(0, str(portfolio_dir))

from wtpy import WtEngine, EngineType
from portfolio.MultiFactorsCTA import MultiFactorsCTA

def setup_logging(log_level='INFO'):
    """设置日志"""
    import io

    # 创建UTF-8编码的输出流
    utf8_stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(utf8_stdout),
            logging.FileHandler('group_runner.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def parse_strategy_yaml(yaml_content):
    """解析策略YAML配置"""
    try:
        if isinstance(yaml_content, str):
            config = yaml.safe_load(yaml_content)
        else:
            config = yaml_content
            
        return config
    except Exception as e:
        raise ValueError(f"解析策略YAML失败: {e}")

def create_strategy_instance(strategy_id, strategy_config):
    """创建策略实例"""
    logger = logging.getLogger(__name__)
    
    try:
        # 提取策略参数
        codes = strategy_config.get('universe', [])
        bar_count = strategy_config.get('bar_count', 50)
        data_freq = strategy_config.get('data_freq', 'day')
        order_by_config = strategy_config.get('order_by', {})
        buy_rules_config = strategy_config.get('buy_rules', {})
        sell_rules_config = strategy_config.get('sell_rules', {})
        top_n = strategy_config.get('top_n', 1)
        weighting_scheme = strategy_config.get('weighting_scheme', 'equal')
        rebalance_interval = strategy_config.get('rebalance_interval', 'daily')
        
        logger.info(f"创建策略实例: {strategy_id}")
        logger.info(f"品种列表: {codes}")
        logger.info(f"K线数量: {bar_count}, 数据频率: {data_freq}")
        logger.info(f"选股数量: {top_n}, 权重方案: {weighting_scheme}")
        
        # 创建MultiFactorsCTA实例
        strategy_instance = MultiFactorsCTA(
            name=strategy_id,
            codes=codes,
            barCnt=bar_count,
            period=data_freq,
            order_by_config=order_by_config,
            buy_rules_config=buy_rules_config,
            sell_rules_config=sell_rules_config,
            top_n=top_n,
            weighting_scheme=weighting_scheme,
            rebalance_interval=rebalance_interval
        )
        
        logger.info(f"策略实例创建成功: {strategy_id}")
        return strategy_instance
        
    except Exception as e:
        logger.error(f"创建策略实例失败: {e}")
        raise

def run_group(group_path, strategies_config):
    """运行组合"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"开始运行组合，组合目录: {group_path}")
        logger.info(f"策略数量: {len(strategies_config)}")
        logger.info(f"Python进程当前工作目录: {os.getcwd()}")

        # 检查配置文件（使用绝对路径）
        config_file = os.path.join(group_path, 'config.yaml')
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
            
        # 初始化Wonder Trader引擎
        logger.info("初始化Wonder Trader引擎...")
        engine = WtEngine(EngineType.ET_CTA)

        # 读取主配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            main_config = yaml.safe_load(f)

        # 确定商品配置文件
        commodity_file = main_config['basefiles']['commodity']
        contract_file = main_config['basefiles']['contract']

        logger.info(f"商品配置文件: {commodity_file}")
        logger.info(f"合约配置文件: {contract_file}")

        # 计算相对于组合目录的路径
        # 当前工作目录：D:\projects\quantquart\backend\live_trading\groups\user_5\group_1
        # 目标公共目录：D:\projects\quantquart\backend\live_trading\common
        # 相对路径：../../../common/

        common_dir = '../../../common/'  # 相对于 live_trading/groups/user_X/group_Y/ 的公共目录
        config_filename = 'config.yaml'  # 主配置文件在当前目录

        logger.info(f"使用公共配置目录: {common_dir}")
        logger.info(f"当前工作目录: {os.getcwd()}")

        # 验证公共配置目录是否存在
        common_abs_path = os.path.abspath(common_dir)
        logger.info(f"公共配置目录绝对路径: {common_abs_path}")
        if not os.path.exists(common_abs_path):
            raise FileNotFoundError(f"公共配置目录不存在: {common_abs_path}")

        # 初始化引擎
        engine.init(common_dir, config_filename, commfile="stk_comms.json", contractfile="contracts.json")
        logger.info("Wonder Trader引擎初始化完成")
        
        # 为每个策略创建实例并添加到引擎
        for strategy_info in strategies_config:
            strategy_id = strategy_info['id']
            strategy_yaml = strategy_info['yaml']
            
            logger.info(f"处理策略: {strategy_id}")
            
            # 解析策略配置
            strategy_config = parse_strategy_yaml(strategy_yaml)
            
            # 创建策略实例
            strategy_instance = create_strategy_instance(strategy_id, strategy_config)
            
            # 添加到引擎
            engine.add_cta_strategy(strategy_instance)
            logger.info(f"策略 {strategy_id} 已添加到引擎")
        
        logger.info("所有策略已添加完成，开始运行引擎...")
        
        # 运行引擎
        engine.run(True)  # True表示异步运行
        
        logger.info("组合运行完成")
        
    except Exception as e:
        import traceback
        logger.error(f"运行组合失败: {e}")
        logger.error(f"错误发生时的当前工作目录: {os.getcwd()}")
        logger.error(f"错误发生时的脚本所在目录: {Path(__file__).parent}")
        logger.error("完整的调用栈:")
        logger.error(traceback.format_exc())
        raise
    finally:
        # 清理资源
        try:
            if 'engine' in locals():
                engine.release()
                logger.info("引擎资源已释放")
        except:
            pass

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Wonder Trader组合运行器')
    parser.add_argument('--group_path', required=True, help='组合目录路径')
    parser.add_argument('--strategies', required=True, help='策略配置JSON字符串')
    parser.add_argument('--log_level', default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging(args.log_level)
    
    try:
        # 解析策略配置
        strategies_config = json.loads(args.strategies)
        
        logger.info("=" * 50)
        logger.info("Wonder Trader组合运行器启动")
        logger.info(f"组合路径: {args.group_path}")
        logger.info(f"策略数量: {len(strategies_config)}")
        logger.info("=" * 50)
        
        # 运行组合
        run_group(args.group_path, strategies_config)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
