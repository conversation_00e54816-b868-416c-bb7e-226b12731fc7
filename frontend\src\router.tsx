import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthRoute } from './components/AuthRoute';
import InitialStateProvider from './components/InitialStateProvider';
import { Spin } from 'antd';
import { isLoggedIn } from './utils/auth';

// 懒加载组件
const Home = lazy(() => import('./_Pages/Home'));
const Login = lazy(() => import('./_Pages/Login'));
const Register = lazy(() => import('./_Pages/Register'));
const Dashboard = lazy(() => import('./_Pages/Dashboard'));
const ApprovalPage = lazy(() => import('./_Pages/ApprovalPage'));

// Loading组件
const PageLoading = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh'
  }}>
    <Spin size="large" />
  </div>
);

const Router: React.FC = () => {
  return (
    <InitialStateProvider>
      <Routes>
        <Route path="/" element={
          <Suspense fallback={<PageLoading />}>
            {isLoggedIn() ? <Navigate to="/dashboard" /> : <Home />}
          </Suspense>
        } />
        <Route path="/login" element={
          <Suspense fallback={<PageLoading />}>
            {isLoggedIn() ? <Navigate to="/dashboard" /> : <Login />}
          </Suspense>
        } />
        <Route path="/register" element={
          <Suspense fallback={<PageLoading />}>
            {isLoggedIn() ? <Navigate to="/dashboard" /> : <Register />}
          </Suspense>
        } />
        <Route path="/approval" element={
          <AuthRoute> {/* 添加鉴权包裹 */}
            <Suspense fallback={<PageLoading />}>
              <ApprovalPage />
            </Suspense>
          </AuthRoute>
        } />
        <Route
          path="/dashboard"
          element={
            <AuthRoute>
              <Suspense fallback={<PageLoading />}>

                <Dashboard />

              </Suspense>
            </AuthRoute>
          }
        />
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
    </InitialStateProvider>
  );
};

export default Router; 