import { Navigate, useLocation } from 'react-router-dom';
import { isLoggedIn } from '../utils/auth';
import { useEffect } from 'react';

export const AuthRoute = ({ children }: { children: React.ReactNode }) => {
  const location = useLocation();

  useEffect(() => {
    // 记录路由保护检查，帮助调试
    console.log('[AuthRoute] 检查登录状态:', isLoggedIn(), '路径:', location.pathname);
  }, [location.pathname]);

  if (!isLoggedIn()) {
    console.log('[AuthRoute] 未登录，重定向到登录页面');
    // 使用state保存当前路径，以便登录后可以返回
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};