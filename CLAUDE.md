# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview
QuantQuart is a modular, event-driven stock strategy backtesting and trading platform with a React frontend and Node.js backend.

## Architecture
- **Frontend**: React + Ant Design Pro + Vite
- **Backend**: Node.js + Express + SQLite + Sequelize
- **Event System**: mitt-based event bus for frontend communication
- **Data Sources**: TDX, OKX, QMT/PTrade integration
- **Real-time**: WebSocket + Socket.IO for live data and trading

## Development Commands

### Frontend
```bash
cd frontend
yarn install
yarn dev          # Start dev server on :3005
yarn build        # Build for production
yarn test         # Run tests
```

### Backend
```bash
cd backend
yarn install
yarn dev          # Start dev server on :3000
yarn start        # Production start
yarn lint         # ESLint check
yarn type-check   # TypeScript check
```

### Data Services
```bash
# TDX real-time data (local)
cd backend/_Providers/_Python
python tdxserver.py

# OKX crypto data (public)
cd backend/_Providers/_HostServer
python okxdata.py

# Historical data processing
python unzip_monitor.py ~/stockdata/vipdoc
```

### Docker
```bash
docker-compose up -d --build
```

## Key Files & Directories

### Frontend Structure
- `frontend/src/_Dispatchers/` - Event dispatchers (market, strategy, user)
- `frontend/src/_Modules/` - Core modules (Indicators, Signals, Market)
- `frontend/src/_Pages/` - Main pages (Dashboard, Login, Register)
- `frontend/src/events/events.ts` - Centralized event definitions
- `frontend/src/shared_types/` - TypeScript shared types
- `frontend/vite.config.ts` - Vite configuration with custom aliases

### Backend Structure
- `backend/_Handlers/` - API handlers for different modules
- `backend/models/` - Sequelize database models
- `backend/database/` - Database management (dbstructure.js, dbupdate.js)
- `backend/_Providers/_Python/` - Python data providers
- `backend/config.json` - Service configuration

### Database Management
```bash
# Update database structure
node backend/database/dbupdate.js

# Structure definition in backend/database/dbstructure.js
```

## Service Architecture

### Event System Flow
```
Frontend Event → Dispatcher → Backend API → Data Engine → Response → Event Bus → UI Update
```

### Key Services
- **Market Service**: Real-time K-line data via TDX/OKX
- **Strategy Engine**: Backtesting and live trading
- **Trading Channels**: QMT/PTrade integration
- **Data Storage**: IndexedDB (frontend) + SQLite (backend)

## Configuration Files
- `backend/config.json` - Main service config (ports, APIs, Redis)
- `docker-compose.yml` - Docker services setup
- `frontend/vite.config.ts` - Frontend build and proxy config

## Environment Setup
1. Install Node.js 16+ and Python 3.8+
2. Install dependencies: `yarn install` in both frontend/ and backend/
3. Configure services in `backend/config.json`
4. Set up Redis: `requirepass quanTquarT2025@Fuzhou`
5. Start services in order: Redis → Backend → Frontend → Python services

## Development Notes
- All module communication via event bus (mitt)
- Database changes through `dbstructure.js` → `dbupdate.js`
- WebSocket namespaces: `/trade`, `/chat`
- Cache layers: IndexedDB (frontend), Redis (backend)
- Trading channels: easytrader_ths, openctp, miniQMT

## Testing
- Frontend tests: `yarn test` in frontend/
- Backend tests: Currently basic, expand as needed
- Integration tests: Manual testing via UI and API calls