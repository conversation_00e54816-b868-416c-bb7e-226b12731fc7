#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一的Wonder Trader ExtDataLoader
支持从本地数据源加载历史数据，包括：
- 股票/ETF: 通过通达信数据
- 期货: 通过通达信数据  
- 虚拟货币: 通过本地CSV文件
- 美股: 通过通达信数据
"""

import os
import sys
import json
import pandas as pd
import pytz
import datetime
import requests
from typing import List, Dict, Optional

# 尝试导入wtpy相关模块
try:
    from ctypes import POINTER
    from wtpy.ExtModuleDefs import BaseExtDataLoader
    from wtpy.WtCoreDefs import WTSBarStruct, WTSTickStruct
    WTPY_AVAILABLE = True
except ImportError:
    print("警告: wtpy模块不可用，将使用模拟模式")
    WTPY_AVAILABLE = False

    # 创建模拟的基类和结构
    class BaseExtDataLoader:
        pass

    class MockBarStruct:
        def __init__(self):
            self.time = 0
            self.open = 0.0
            self.high = 0.0
            self.low = 0.0
            self.close = 0.0
            self.vol = 0.0
            self.money = 0.0
            self.hold = 0.0
            self.add = 0.0

    class MockTickStruct:
        def __init__(self):
            self.time = 0
            self.price = 0.0
            self.volume = 0.0

    WTSBarStruct = MockBarStruct
    WTSTickStruct = MockTickStruct

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入现有的数据处理模块
from prepare_csv import (
    load_config, read_tdx_day_data, read_tdx_min5_data,
    get_tdx_file_path, is_etf, DEFAULT_TIMEZONE
)
from prepare_csv_cpt import get_crypto_source_path

# ===== 全局数据源配置 =====
# 数据源模式：'local' = 本地数据（当前模式），'provider' = 通过TDX服务接口获取
DATA_SOURCE_MODE = 'provider'  # 默认使用接口获取

# TDX服务配置（从backend/config.json读取）
TDX_SERVICE_CONFIG = {
    'host': '127.0.0.1',
    'port': 5003,
    'base_url': None  # 将在运行时构建
}

def find_relative_path_to_backend(start_dir=None, max_depth=10):
    """
    向上查找backend目录的绝对路径
    Args:
        start_dir: 起始目录，默认当前文件目录
        max_depth: 最大查找层数
    Returns:
        backend目录的绝对路径（str），找不到返回None
    """
    if start_dir is None:
        start_dir = os.path.dirname(os.path.abspath(__file__))
    current = start_dir
    for _ in range(max_depth):
        if os.path.basename(current) == 'backend':
            return current
        parent = os.path.dirname(current)
        if parent == current:
            break
        current = parent
    return None

def _load_backend_config():
    """
    从backend/config.json加载配置

    Returns:
        配置字典或None
    """
    try:
        # 获取backend目录路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        backend_dir = find_relative_path_to_backend(current_dir)
        if backend_dir is None:
            print("[ExtDataLoader] 未找到backend目录")
            return None
        config_path = os.path.join(backend_dir, 'config.json')

        if not os.path.exists(config_path):
            print(f"[ExtDataLoader] 配置文件不存在: {config_path}")
            return None

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        print(f"[ExtDataLoader] 成功加载backend配置: {config_path}")
        return config

    except Exception as e:
        print(f"[ExtDataLoader] 加载backend配置失败: {e}")
        return None

def _update_tdx_service_config():
    """更新TDX服务配置"""
    global TDX_SERVICE_CONFIG

    backend_config = _load_backend_config()
    if backend_config and 'tdx_service' in backend_config:
        tdx_config = backend_config['tdx_service']
        TDX_SERVICE_CONFIG['host'] = tdx_config.get('host', '127.0.0.1')
        TDX_SERVICE_CONFIG['port'] = tdx_config.get('port', 5003)
        TDX_SERVICE_CONFIG['base_url'] = None  # 重置URL
        print(f"[ExtDataLoader] TDX服务配置已更新: {TDX_SERVICE_CONFIG['host']}:{TDX_SERVICE_CONFIG['port']}")
    else:
        print(f"[ExtDataLoader] 未找到tdx_service配置，使用默认值")

def set_data_source_mode(mode: str):
    """
    设置数据源模式

    Args:
        mode: 'local' 或 'provider'
    """
    global DATA_SOURCE_MODE
    if mode in ['local', 'provider']:
        DATA_SOURCE_MODE = mode
        print(f"[ExtDataLoader] 数据源模式已设置为: {mode}")

        # 如果设置为provider模式，更新TDX服务配置
        if mode == 'provider':
            _update_tdx_service_config()
    else:
        print(f"[ExtDataLoader] 无效的数据源模式: {mode}，保持当前模式: {DATA_SOURCE_MODE}")

def get_data_source_mode() -> str:
    """获取当前数据源模式"""
    return DATA_SOURCE_MODE

def get_tdx_service_url() -> str:
    """获取TDX服务URL"""
    if TDX_SERVICE_CONFIG['base_url'] is None:
        host = TDX_SERVICE_CONFIG['host']
        port = TDX_SERVICE_CONFIG['port']
        TDX_SERVICE_CONFIG['base_url'] = f"http://{host}:{port}"
    return TDX_SERVICE_CONFIG['base_url']

# 初始化时更新配置
_update_tdx_service_config()

class UnifiedExtDataLoader(BaseExtDataLoader):
    """
    统一的扩展数据加载器
    支持股票、ETF、期货、虚拟货币等多种品种
    """

    def __init__(self):
        """初始化加载器"""
        super().__init__()
        self.config = None
        self.timezone = None
        self._load_config()
        
    def _load_config(self):
        """加载配置"""
        try:
            self.config = load_config()
            if not self.config:
                print("[ExtDataLoader] 警告: 无法加载配置文件，使用默认设置")
                return
                
            timezone_str = self.config.get('default_timezone', DEFAULT_TIMEZONE)
            self.timezone = pytz.timezone(timezone_str)
            print(f"[ExtDataLoader] 配置加载成功，时区: {timezone_str}")
            
        except Exception as e:
            print(f"[ExtDataLoader] 配置加载失败: {e}")
            self.timezone = pytz.timezone(DEFAULT_TIMEZONE)
    
    def _parse_wt_code(self, stdCode: str) -> Dict[str, str]:
        """
        解析Wonder Trader标准代码
        
        Args:
            stdCode: 标准代码，如 "SSE.600000", "CRYPTO.CPT.BTC-USDT"
            
        Returns:
            解析结果字典
        """
        parts = stdCode.split('.')
        
        if len(parts) >= 2:
            exchange = parts[0].upper()
            
            # 虚拟货币
            if exchange == "CRYPTO" and len(parts) >= 3 and parts[1].upper() == "CPT":
                return {
                    'type': 'crypto',
                    'exchange': exchange,
                    'category': parts[1],
                    'symbol': parts[2] if len(parts) > 2 else '',
                    'full_code': stdCode
                }
            
            # 传统金融品种
            else:
                return {
                    'type': 'traditional',
                    'exchange': exchange,
                    'symbol': '.'.join(parts[1:]) if len(parts) > 1 else '',
                    'full_code': stdCode
                }
        
        return {
            'type': 'unknown',
            'full_code': stdCode
        }
    
    def _convert_period(self, period: str) -> str:
        """
        转换Wonder Trader周期到内部格式

        Args:
            period: WT周期，如 "m1", "m5", "d1"

        Returns:
            内部周期格式
        """
        period_map = {
            'm1': 'min1',
            'm5': 'min5',
            'd1': '1D',
            'day': '1D'
        }
        return period_map.get(period.lower(), 'day')

    def _convert_to_wt_time(self, date_int: int, time_int: int = 930) -> int:
        """
        将日期和时间转换为Wonder Trader时间格式

        Args:
            date_int: YYYYMMDD格式的日期整数
            time_int: HHMM格式的时间整数，默认930(09:30)

        Returns:
            Wonder Trader时间格式: (YYYYMMDD - 19900000) * 10000 + HHMM
        """
        if date_int == 0:
            return 0

        try:
            # 按照Wonder Trader格式计算: (YYYYMMDD - 19900000) * 10000 + HHMM
            wt_time = (date_int - 19900000) * 10000 + time_int
            return wt_time

        except Exception as e:
            print(f"[ExtDataLoader] 时间转换失败 date:{date_int}, time:{time_int}: {e}")
            return 0

    def _read_tdx_data_for_wt(self, file_path: str, period: str, numeric_code: str) -> List[Dict]:
        """
        直接读取通达信数据并按照Wonder Trader规范处理

        Args:
            file_path: 通达信文件路径
            period: 数据周期
            numeric_code: 数字代码（用于ETF判断）

        Returns:
            包含date和time_hhmm字段的K线数据列表
        """
        print(f"[数据准备] 读取{period}文件: {file_path}")

        if not os.path.exists(file_path):
            print(f"[数据准备] 文件不存在: {file_path}")
            return []

        klines = []

        try:
            with open(file_path, 'rb') as f:
                buffer = f.read()

                if period == 'day':
                    # 日线数据：32字节每条记录
                    record_size = 32
                    record_count = len(buffer) // record_size

                    for i in range(record_count):
                        pos = i * record_size
                        if pos + record_size > len(buffer):
                            break

                        # 读取日期 (4字节整型)
                        date_value = int.from_bytes(buffer[pos:pos+4], byteorder='little')
                        year = date_value // 10000
                        month = (date_value % 10000) // 100
                        day = date_value % 100

                        if not (1990 <= year <= 2030 and 1 <= month <= 12 and 1 <= day <= 31):
                            continue

                        # 构造YYYYMMDD格式的日期
                        date_int = year * 10000 + month * 100 + day

                        # 读取价格数据
                        divisor = 1000.0 if self._is_etf(numeric_code) else 100.0
                        open_price = int.from_bytes(buffer[pos+4:pos+8], byteorder='little') / divisor
                        high_price = int.from_bytes(buffer[pos+8:pos+12], byteorder='little') / divisor
                        low_price = int.from_bytes(buffer[pos+12:pos+16], byteorder='little') / divisor
                        close_price = int.from_bytes(buffer[pos+16:pos+20], byteorder='little') / divisor

                        # 读取成交额和成交量
                        import struct
                        amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
                        volume = int.from_bytes(buffer[pos+24:pos+28], byteorder='little')

                        # 检查成交量是否需要调整
                        if volume > 0:
                            approx_price = amount / volume
                            if approx_price > close_price * 10:
                                volume = volume * 100

                        # 检查数据合理性
                        if not (open_price > 0 and high_price > 0 and low_price > 0 and close_price > 0):
                            continue

                        # 按照官方例子的格式：保存原始的date和time，不预先计算
                        klines.append({
                            'date': f"{year}/{month}/{day}",  # 保存为字符串格式，模拟CSV的日期格式
                            'time': "09:30:00",  # 日线数据使用09:30:00
                            'open': float(open_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'close': float(close_price),
                            'vol': float(volume),
                            'money': float(amount)
                        })

                elif period == 'min5':
                    # 5分钟数据：32字节每条记录
                    record_size = 32
                    record_count = len(buffer) // record_size

                    for i in range(record_count):
                        pos = i * record_size
                        if pos + record_size > len(buffer):
                            break

                        # 读取日期时间 (前2字节日期，后2字节分钟)
                        date_time_bytes = buffer[pos:pos+4]
                        date_part = int.from_bytes(date_time_bytes[0:2], byteorder='little')
                        time_part = int.from_bytes(date_time_bytes[2:4], byteorder='little')

                        # 计算年月日
                        year = (date_part // 2048) + 2004
                        month = (date_part % 2048) // 100
                        day = (date_part % 2048) % 100
                        # 计算时分
                        hour = time_part // 60
                        minute = time_part % 60

                        if not (1990 <= year <= 2030 and 1 <= month <= 12 and 1 <= day <= 31 and 0 <= hour <= 23 and 0 <= minute <= 59):
                            continue

                        # 构造YYYYMMDD和HHMM格式
                        date_int = year * 10000 + month * 100 + day
                        time_int = hour * 100 + minute

                        # 读取价格数据（与app.py保持一致）
                        import struct
                        open_price = struct.unpack('<f', buffer[pos+4:pos+8])[0]
                        high_price = struct.unpack('<f', buffer[pos+8:pos+12])[0]
                        low_price = struct.unpack('<f', buffer[pos+12:pos+16])[0]
                        close_price = struct.unpack('<f', buffer[pos+16:pos+20])[0]

                        # 读取成交额和成交量
                        import struct
                        amount = struct.unpack('<f', buffer[pos+20:pos+24])[0]
                        volume = int.from_bytes(buffer[pos+24:pos+28], byteorder='little')

                        # 检查成交量是否需要调整
                        if volume > 0:
                            approx_price = amount / volume
                            if approx_price > close_price * 10:
                                volume = volume * 100

                        # 检查数据合理性
                        if not (open_price > 0 and high_price > 0 and low_price > 0 and close_price > 0):
                            continue

                        # 按照官方例子的格式：保存原始的date和time，不预先计算
                        klines.append({
                            'date': f"{year}/{month}/{day}",  # 保存为字符串格式，模拟CSV的日期格式
                            'time': f"{hour:02d}:{minute:02d}:00",  # 保存为HH:MM:SS格式
                            'open': float(open_price),
                            'high': float(high_price),
                            'low': float(low_price),
                            'close': float(close_price),
                            'vol': float(volume),
                            'money': float(amount)
                        })

        except Exception as e:
            print(f"[ExtDataLoader] 读取通达信文件失败 {file_path}: {e}")
            return []

        print(f"[数据准备] 成功读取{period}数据: {file_path}, 共 {len(klines)} 条记录")
        return klines

    def _is_etf(self, code: str) -> bool:
        """判断是否为ETF"""
        if not code:
            return False
        # ETF代码通常以51、15、16开头
        return code.startswith(('51', '15', '16'))

    def _get_klines_from_provider(self, stdCode: str, period: str) -> List[Dict]:
        """
        从 TDX服务 获取K线数据

        Args:
            stdCode: 标准代码，如 "SSE.600000"
            period: 数据周期，如 "d1", "m5"

        Returns:
            K线数据列表
        """
        try:
            # 解析代码信息
            code_info = self._parse_wt_code(stdCode)
            if code_info['type'] == 'unknown':
                print(f"[ExtDataLoader] 无法解析代码格式: {stdCode}")
                return []

            # 转换为TDX服务需要的参数格式
            market_str = self._get_tdx_market_str(code_info)
            code_str = self._get_tdx_code_str(code_info)
            period_str = self._convert_period_for_tdx(period)

            if not market_str or not code_str:
                print(f"[ExtDataLoader] 无法转换为TDX格式: {stdCode}")
                return []

            # 构建TDX服务请求参数
            params = {
                'market': market_str,
                'code': code_str,
                'period': period_str,
                'count': 500  # 获取500条数据
            }

            # 发送请求到 TDX服务
            base_url = get_tdx_service_url()
            url = f"{base_url}/kline"

            print(f"[ExtDataLoader] 从TDX服务获取数据: {url}")
            print(f"[ExtDataLoader] 请求参数: {params}")

            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            # TDX服务直接返回数组格式的K线数据
            klines_data = response.json()
            if not isinstance(klines_data, list):
                print(f"[ExtDataLoader] TDX服务返回格式错误，期望数组格式")
                return []

            if not klines_data:
                print(f"[ExtDataLoader] TDX服务返回空数据")
                return []

            # 转换数据格式为内部格式
            klines = []
            for item in klines_data:
                try:
                    # TDX服务返回的数据格式: {time, open, high, low, close, volume, datetime}
                    # 转换为内部格式: {date, time, open, high, low, close, vol, money}
                    timestamp = item.get('time', 0)
                    datetime_str = item.get('datetime', '')

                    if timestamp:
                        # 将时间戳转换为日期和时间
                        dt = datetime.datetime.fromtimestamp(timestamp)
                        date_str = dt.strftime('%Y/%m/%d')
                        time_str = dt.strftime('%H:%M:%S')
                    elif datetime_str:
                        # 如果有datetime字符串，尝试解析
                        try:
                            dt = datetime.datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                            date_str = dt.strftime('%Y/%m/%d')
                            time_str = dt.strftime('%H:%M:%S')
                        except:
                            date_str = '1990/1/1'
                            time_str = '09:30:00'
                    else:
                        date_str = '1990/1/1'
                        time_str = '09:30:00'

                    volume = float(item.get('volume', 0))
                    close_price = float(item.get('close', 0))

                    klines.append({
                        'date': date_str,
                        'time': time_str,
                        'open': float(item.get('open', 0)),
                        'high': float(item.get('high', 0)),
                        'low': float(item.get('low', 0)),
                        'close': close_price,
                        'vol': volume,
                        'money': volume * close_price  # 估算成交额
                    })
                except Exception as e:
                    print(f"[ExtDataLoader] 转换数据行失败: {e}")
                    continue

            print(f"[ExtDataLoader] 从TDX服务成功获取{len(klines)}条K线数据")
            return klines

        except requests.exceptions.RequestException as e:
            print(f"[ExtDataLoader] 网络请求失败: {e}")
            return []
        except Exception as e:
            print(f"[ExtDataLoader] 从TDX服务获取数据失败: {e}")
            return []

    def _get_tdx_market_str(self, code_info: Dict[str, str]) -> str:
        """
        将内部市场类型转换为TDX服务需要的市场字符串

        Args:
            code_info: 代码解析信息

        Returns:
            TDX市场字符串 ('sz', 'sh', 'shfe', 'dce', 'czce', 'cffex', 'ine')
        """
        exchange = code_info.get('exchange', '').upper()

        # 根据交易所判断TDX市场字符串
        if exchange == 'SZSE':
            return 'sz'
        elif exchange == 'SSE':
            return 'sh'
        elif exchange == 'BSE':
            return 'bj'
        elif exchange == 'SHFE':
            return 'shfe'
        elif exchange == 'DCE':
            return 'dce'
        elif exchange == 'CZCE':
            return 'czce'
        elif exchange == 'CFFEX':
            return 'cffex'
        elif exchange == 'INE':
            return 'ine'
        else:
            print(f"[ExtDataLoader] 不支持的交易所: {exchange}")
            return ''

    def _get_tdx_code_str(self, code_info: Dict[str, str]) -> str:
        """
        将内部代码格式转换为TDX服务需要的代码字符串

        Args:
            code_info: 代码解析信息

        Returns:
            TDX代码字符串（提取数字代码部分）
        """
        symbol = code_info.get('symbol', '')

        # 对于 "ETF.512100" 这样的格式，提取最后的数字部分
        # 对于 "600000" 这样的格式，直接返回
        if '.' in symbol:
            parts = symbol.split('.')
            # 找到最后一个看起来像代码的部分（通常是数字）
            for part in reversed(parts):
                if part.isdigit() or (len(part) == 6 and part[:3].isdigit()):
                    return part
            # 如果没找到数字部分，返回最后一部分
            return parts[-1]
        else:
            return symbol

    def _convert_period_for_tdx(self, period: str) -> str:
        """
        将内部周期格式转换为TDX服务需要的格式

        Args:
            period: 内部周期，如 "d1", "m5"

        Returns:
            TDX周期格式
        """
        period_map = {
            'm1': '1m',
            'm5': '5m',
            'm15': '15m',
            'm30': '30m',
            'h1': '1h',
            'day': 'day',
            'min1': '1m',
            'min5': '5m'
        }
        return period_map.get(period.lower(), 'day')
    
    def _load_traditional_data(self, code_info: Dict[str, str], period: str) -> List[Dict]:
        """
        加载传统金融品种数据（股票、ETF、期货等）
        
        Args:
            code_info: 代码解析信息
            period: 数据周期
            
        Returns:
            K线数据列表
        """
        if not self.config:
            print(f"[ExtDataLoader] 配置未加载，无法获取{code_info['full_code']}数据")
            return []
            
        try:
            # 获取通达信数据根目录
            tdx_data_root = self.config.get('tdx_data', {}).get('path')
            if not tdx_data_root:
                print(f"[ExtDataLoader] 通达信数据路径未配置")
                return []
                
            # 转换为绝对路径
            if not os.path.isabs(tdx_data_root):
                config_dir = os.path.dirname(os.path.abspath(__file__))
                tdx_data_root = os.path.abspath(os.path.join(config_dir, tdx_data_root))
            
            # 获取通达信文件路径
            tdx_file_path = get_tdx_file_path(code_info['full_code'], period, tdx_data_root)
            if not tdx_file_path or not os.path.exists(tdx_file_path):
                print(f"[ExtDataLoader] 通达信文件不存在: {tdx_file_path}")
                return []
            
            # 提取数字代码用于ETF判断
            symbol_parts = code_info['symbol'].split('.')
            numeric_code = symbol_parts[-1] if symbol_parts else ""
            
            # 使用新的读取函数，直接按Wonder Trader规范处理
            klines = self._read_tdx_data_for_wt(tdx_file_path, period, numeric_code)
                
            print(f"[ExtDataLoader] 成功加载{code_info['full_code']}数据: {len(klines)}条")
            return klines
            
        except Exception as e:
            print(f"[ExtDataLoader] 加载传统数据失败 {code_info['full_code']}: {e}")
            return []
    
    def _load_crypto_data(self, code_info: Dict[str, str], period: str) -> List[Dict]:
        """
        加载虚拟货币数据
        
        Args:
            code_info: 代码解析信息
            period: 数据周期
            
        Returns:
            K线数据列表
        """
        if not self.config:
            print(f"[ExtDataLoader] 配置未加载，无法获取{code_info['full_code']}数据")
            return []
            
        try:
            # 获取虚拟货币数据根目录
            crypto_data_root = self.config.get('crypto_data', {}).get('path')
            if not crypto_data_root:
                print(f"[ExtDataLoader] 虚拟货币数据路径未配置")
                return []
                
            # 转换为绝对路径
            if not os.path.isabs(crypto_data_root):
                config_dir = os.path.dirname(os.path.abspath(__file__))
                crypto_data_root = os.path.abspath(os.path.join(config_dir, crypto_data_root))
            
            # 获取虚拟货币文件路径
            crypto_file_path = get_crypto_source_path(code_info['full_code'], period, crypto_data_root)
            if not crypto_file_path or not os.path.exists(crypto_file_path):
                print(f"[ExtDataLoader] 虚拟货币文件不存在: {crypto_file_path}")
                return []
            
            # 读取CSV文件
            df = pd.read_csv(crypto_file_path)
            if df.empty:
                print(f"[ExtDataLoader] 虚拟货币文件为空: {crypto_file_path}")
                return []
            
            # 转换为标准格式
            klines = []
            for _, row in df.iterrows():
                try:
                    # 假设CSV格式包含: timestamp, open, high, low, close, volume
                    klines.append({
                        'time': int(row.get('timestamp', row.get('time', 0))),
                        'open': float(row.get('open', 0)),
                        'high': float(row.get('high', 0)),
                        'low': float(row.get('low', 0)),
                        'close': float(row.get('close', 0)),
                        'volume': float(row.get('volume', 0)),
                        'amount': float(row.get('amount', row.get('turnover', 0)))
                    })
                except Exception as e:
                    print(f"[ExtDataLoader] 解析虚拟货币数据行失败: {e}")
                    continue
            
            print(f"[ExtDataLoader] 成功加载{code_info['full_code']}数据: {len(klines)}条")
            return klines
            
        except Exception as e:
            print(f"[ExtDataLoader] 加载虚拟货币数据失败 {code_info['full_code']}: {e}")
            return []
    
    def _convert_to_wt_bars(self, klines: List[Dict], feeder) -> bool:
        """
        将K线数据转换为Wonder Trader格式并推送
        完全按照官方示例的方式处理

        Args:
            klines: K线数据列表
            feeder: Wonder Trader回调函数

        Returns:
            是否成功
        """
        if not klines:
            return False

        try:
            if WTPY_AVAILABLE:
                # 步骤1: 创建DataFrame (模拟官方的 pd.read_csv)
                df = pd.DataFrame(klines)

                # 步骤2: 重命名列 (模拟官方的 df.rename)
                # 我们的数据已经是正确的列名，不需要重命名

                # 步骤3: 按照官方逻辑处理时间
                df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y%m%d').astype('int64')
                df['time'] = (df['date'] - 19900000) * 10000 + df['time'].str.replace(':', '').str[:-2].astype('int')

                # 步骤4: 创建WTSBarStruct数组
                BUFFER = WTSBarStruct * len(df)
                buffer = BUFFER()

                # 步骤5: 使用官方的assign函数逻辑
                def assign(procession, buffer):
                    tuple(map(lambda x: setattr(buffer[x[0]], procession.name, x[1]), enumerate(procession)))

                # 步骤6: 应用到每一列
                df.apply(assign, buffer=buffer)

                # 步骤7: 调试输出 (模拟官方的 print)
                print(f"[ExtDataLoader] DataFrame处理完成:")
                print(df.head(3))
                if len(buffer) > 0:
                    print(f"[ExtDataLoader] buffer[0] 字段:")
                    if hasattr(buffer[0], 'to_dict'):
                        print(buffer[0].to_dict)
                    else:
                        print(f"  date: {getattr(buffer[0], 'date', 'N/A')}")
                        print(f"  time: {getattr(buffer[0], 'time', 'N/A')}")
                        print(f"  open: {getattr(buffer[0], 'open', 'N/A')}")
                        print(f"  close: {getattr(buffer[0], 'close', 'N/A')}")

                # 步骤8: 调用feeder
                feeder(buffer, len(df))
                return True
            else:
                # 模拟模式：简化处理
                print(f"[ExtDataLoader] 模拟模式处理数据")
                buffer = []
                for i, kline in enumerate(klines):
                    bar = WTSBarStruct()
                    # 模拟处理时间
                    import datetime as dt
                    date_str = kline.get('date', '1990/1/1')
                    time_str = kline.get('time', '09:30:00')

                    # 转换日期
                    date_obj = dt.datetime.strptime(date_str, '%Y/%m/%d')
                    date_int = int(date_obj.strftime('%Y%m%d'))

                    # 转换时间
                    time_parts = time_str.replace(':', '')[:-2]  # 去掉秒
                    time_int = int(time_parts)

                    # 计算WT时间
                    wt_time = (date_int - 19900000) * 10000 + time_int

                    bar.date = date_int
                    bar.time = wt_time
                    bar.open = float(kline.get('open', 0))
                    bar.high = float(kline.get('high', 0))
                    bar.low = float(kline.get('low', 0))
                    bar.close = float(kline.get('close', 0))
                    bar.vol = float(kline.get('vol', 0))
                    bar.money = float(kline.get('money', 0))
                    buffer.append(bar)

                # 调用模拟回调
                feeder(buffer, len(klines))
                return True

        except Exception as e:
            print(f"[ExtDataLoader] 转换WTSBarStruct失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_final_his_bars(self, stdCode: str, period: str, feeder) -> bool:
        """
        加载历史K线数据（回测、实盘）

        Args:
            stdCode: 合约代码，格式如SSE.600000, CRYPTO.CPT.BTC-USDT
            period: 周期，m1/m5/d1
            feeder: 回调函数，feed_raw_bars(bars:POINTER(WTSBarStruct), count:int, factor:double)

        Returns:
            是否成功加载
        """
        print(f"[ExtDataLoader] 加载{period}周期的{stdCode}历史K线数据")
        print(f"[ExtDataLoader] 当前数据源模式: {DATA_SOURCE_MODE}")

        try:
            # 根据全局开关决定数据源
            klines = []

            if DATA_SOURCE_MODE == 'provider':
                # 使用 marketProvider 接口获取数据
                print(f"[ExtDataLoader] 使用Provider接口获取数据")
                klines = self._get_klines_from_provider(stdCode, period)

                # 如果Provider获取失败，回退到本地数据
                if not klines:
                    print(f"[ExtDataLoader] Provider获取失败，回退到本地数据源")
                    klines = self._load_data_from_local_source(stdCode, period)
            else:
                # 使用本地数据源（原有模式）
                print(f"[ExtDataLoader] 使用本地数据源")
                klines = self._load_data_from_local_source(stdCode, period)

            if not klines:
                print(f"[ExtDataLoader] 未能加载到{stdCode}的数据")
                return False

            # 转换并推送数据
            return self._convert_to_wt_bars(klines, feeder)

        except Exception as e:
            print(f"[ExtDataLoader] 加载历史K线失败 {stdCode}: {e}")
            return False

    def _load_data_from_local_source(self, stdCode: str, period: str) -> List[Dict]:
        """
        从本地数据源加载数据（原有逻辑）

        Args:
            stdCode: 合约代码
            period: 周期

        Returns:
            K线数据列表
        """
        try:
            # 【新增】检查并同步WT数据文件（不影响原流程）
            try:
                from tdx_to_wt_sync import ensure_wt_data_updated
                ensure_wt_data_updated(stdCode, period)
            except Exception as e:
                print(f"[ExtDataLoader] DSB同步失败，继续原流程: {e}")

            # 【原流程】解析代码
            code_info = self._parse_wt_code(stdCode)
            if code_info['type'] == 'unknown':
                print(f"[ExtDataLoader] 无法解析代码格式: {stdCode}")
                return []

            # 转换周期
            internal_period = self._convert_period(period)

            # 根据类型加载数据
            klines = []
            if code_info['type'] == 'crypto':
                klines = self._load_crypto_data(code_info, internal_period)
            elif code_info['type'] == 'traditional':
                klines = self._load_traditional_data(code_info, internal_period)

            return klines

        except Exception as e:
            print(f"[ExtDataLoader] 从本地数据源加载失败 {stdCode}: {e}")
            return []
    
    def load_his_ticks(self, stdCode: str, uDate: int, feeder) -> bool:
        """
        加载历史Tick数据（只在回测有效，实盘只提供当日落地的）
        
        Args:
            stdCode: 合约代码
            uDate: 日期，格式如yyyymmdd
            feeder: 回调函数，feed_raw_ticks(ticks:POINTER(WTSTickStruct), count:int)
            
        Returns:
            是否成功加载
        """
        print(f"[ExtDataLoader] 加载{stdCode}在{uDate}的Tick数据")
        
        # 目前暂不实现Tick数据加载
        # 可以根据需要扩展
        print(f"[ExtDataLoader] Tick数据加载暂未实现")
        return False


# 创建全局实例
unified_loader = UnifiedExtDataLoader()

def get_unified_ext_data_loader():
    """获取统一的扩展数据加载器实例"""
    return unified_loader

# ===== 便利函数 =====
def set_data_source_to_local():
    """设置数据源为本地模式"""
    set_data_source_mode('local')

def set_data_source_to_provider():
    """设置数据源为Provider接口模式"""
    set_data_source_mode('provider')

def configure_tdx_service(host: str = '127.0.0.1', port: int = 5003):
    """
    配置TDX服务地址

    Args:
        host: 服务器地址
        port: 服务器端口
    """
    global TDX_SERVICE_CONFIG
    TDX_SERVICE_CONFIG['host'] = host
    TDX_SERVICE_CONFIG['port'] = port
    TDX_SERVICE_CONFIG['base_url'] = None  # 重置URL，下次使用时重新构建
    print(f"[ExtDataLoader] TDX服务配置已更新: {host}:{port}")

def get_current_config():
    """
    获取当前配置信息

    Returns:
        配置信息字典
    """
    return {
        'data_source_mode': DATA_SOURCE_MODE,
        'tdx_service_config': TDX_SERVICE_CONFIG.copy()
    }


def test_feeder_bars(bars_buffer, count):
    """测试K线数据回调函数"""
    print(f"  📊 收到K线数据: {count}条")

    if count > 0:
        # 打印前几条数据，显示WT的date和time字段
        print("  前3条K线数据:")
        for i in range(min(3, count)):
            bar = bars_buffer[i]
            # 显示WT结构中的date和time字段
            wt_date = getattr(bar, 'date', 'N/A')
            wt_time = getattr(bar, 'time', 'N/A')
            print(f"    [{i}] WT.date:{wt_date}, WT.time:{wt_time}, 开:{bar.open:.2f}, 高:{bar.high:.2f}, 低:{bar.low:.2f}, 收:{bar.close:.2f}, 量:{bar.vol}")

def test_feeder_ticks(ticks_buffer, count):
    """测试Tick数据回调函数"""
    print(f"  📈 收到Tick数据: {count}条")

    if count > 0:
        # 打印前几条数据
        print("  前3条Tick数据:")
        for i in range(min(3, count)):
            tick = ticks_buffer[i]
            print(f"    [{i}] 时间:{tick.time}, 价格:{tick.price:.2f}, 量:{tick.volume}")

if __name__ == "__main__":
    print("=" * 60)
    print("测试统一扩展数据加载器的核心函数")
    print("=" * 60)

    # 显示当前配置
    print(f"\n📋 当前配置:")
    config = get_current_config()
    print(f"  数据源模式: {config['data_source_mode']}")
    print(f"  TDX服务: {config['tdx_service_config']['host']}:{config['tdx_service_config']['port']}")

    loader = get_unified_ext_data_loader()

    # 测试品种代码
    test_cases = [
        ("SSE.STK.600000", "d1", "股票日线"),
        ("SSE.STK.600000", "m5", "股票5分钟线"),
        ("SSE.ETF.510300", "d1", "ETF日线"),
        ("CRYPTO.CPT.BTC-USDT", "d1", "虚拟货币日线"),
        ("SHFE.fu.HOT", "d1", "期货日线"),
    ]

    print("\n🔍 测试代码解析功能:")
    print("-" * 40)

    # 测试代码解析
    test_codes = [
        "SSE.ETF.512100",
        "SZSE.ETF.159985",
        "SSE.600000",
        "SZSE.000001"
    ]

    for test_code in test_codes:
        code_info = loader._parse_wt_code(test_code)
        market_str = loader._get_tdx_market_str(code_info)
        code_str = loader._get_tdx_code_str(code_info)
        period_str = loader._convert_period_for_tdx('m5')
        print(f"  {test_code} -> market={market_str}, code={code_str}, period={period_str}")

    print("\n🔍 测试TDX服务接口获取数据:")
    print("-" * 40)

    # 测试TDX服务接口
    tdx_test_cases = [
        ("SSE.ETF.512100", "m5", "上证ETF 5分钟"),
        ("SZSE.ETF.159985", "m5", "深证ETF 5分钟"),
        ("SSE.600000", "d1", "上证股票 日线"),
        ("SZSE.000001", "d1", "深证股票 日线"),
    ]

    for stdCode, period, description in tdx_test_cases:
        print(f"\n📊 测试 {description}: {stdCode} ({period})")
        try:
            # 直接测试TDX服务接口函数
            klines = loader._get_klines_from_provider(stdCode, period)
            if klines:
                print(f"  ✅ {description} 获取成功，数据量: {len(klines)}条")
                if len(klines) > 0:
                    first_kline = klines[0]
                    last_kline = klines[-1]
                    print(f"  📈 首条数据: {first_kline['date']} {first_kline['time']} 收盘:{first_kline['close']}")
                    print(f"  📈 末条数据: {last_kline['date']} {last_kline['time']} 收盘:{last_kline['close']}")
            else:
                print(f"  ❌ {description} 获取失败或返回空数据")
        except Exception as e:
            print(f"  💥 {description} 获取异常: {e}")
            import traceback
            traceback.print_exc()

    print("\n🔍 测试数据源切换功能:")
    print("-" * 40)

    # 测试Provider模式
    print(f"\n🌐 测试Provider模式:")
    set_data_source_to_provider()

    for stdCode, period, description in test_cases[:2]:  # 只测试前两个
        print(f"\n📈 测试 {description}: {stdCode} ({period})")
        try:
            result = loader.load_final_his_bars(stdCode, period, test_feeder_bars)
            if result:
                print(f"  ✅ {description} 加载成功")
            else:
                print(f"  ❌ {description} 加载失败")
        except Exception as e:
            print(f"  💥 {description} 加载异常: {e}")

    # 测试本地模式
    print(f"\n💾 测试本地数据模式:")
    set_data_source_to_local()

    for stdCode, period, description in test_cases[:2]:  # 只测试前两个
        print(f"\n📈 测试 {description}: {stdCode} ({period})")
        try:
            result = loader.load_final_his_bars(stdCode, period, test_feeder_bars)
            if result:
                print(f"  ✅ {description} 加载成功")
            else:
                print(f"  ❌ {description} 加载失败")
        except Exception as e:
            print(f"  💥 {description} 加载异常: {e}")

    print("\n🔍 测试 load_final_his_bars 函数 (完整测试):")
    print("-" * 40)

    for stdCode, period, description in test_cases:
        print(f"\n📈 测试 {description}: {stdCode} ({period})")
        try:
            result = loader.load_final_his_bars(stdCode, period, test_feeder_bars)
            if result:
                print(f"  ✅ {description} 加载成功")
            else:
                print(f"  ❌ {description} 加载失败")
        except Exception as e:
            print(f"  💥 {description} 加载异常: {e}")
            import traceback
            traceback.print_exc()

    print("\n🔍 测试 load_his_ticks 函数:")
    print("-" * 40)

    # 测试Tick数据加载
    tick_test_cases = [
        ("SSE.600000", 20231201, "股票Tick"),
        ("CRYPTO.CPT.BTC-USDT", 20231201, "虚拟货币Tick"),
    ]

    for stdCode, uDate, description in tick_test_cases:
        print(f"\n📊 测试 {description}: {stdCode} ({uDate})")
        try:
            result = loader.load_his_ticks(stdCode, uDate, test_feeder_ticks)
            if result:
                print(f"  ✅ {description} 加载成功")
            else:
                print(f"  ❌ {description} 加载失败 (当前未实现)")
        except Exception as e:
            print(f"  💥 {description} 加载异常: {e}")

    print("\n" + "=" * 60)
    print("✅ 核心函数测试完成!")
    print("=" * 60)
