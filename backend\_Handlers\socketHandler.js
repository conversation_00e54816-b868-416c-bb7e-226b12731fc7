/**
 * WebSocket端口池管理处理器
 * 负责管理WebSocket连接端口的分配和回收
 */

const express = require('express');
const router = express.Router();
const http = require('http');
const socketIo = require('socket.io');
const { authenticateToken } = require('../middleware/auth');
const config = require('../config.json');
const { marketHandler } = require('./marketHandler');
const tradeHandler = require('./tradeHandler');
const symbolSelectHandler = require('./symbolSelectHandler');

// 创建简单的日志记录器
const logger = {
    info: (message) => console.log(`[SocketHandler INFO] ${message}`),
    warn: (message) => console.warn(`[SocketHandler WARN] ${message}`),
    error: (message) => console.error(`[SocketHandler ERROR] ${message}`),
    debug: (message) => console.log(`[SocketHandler DEBUG] ${message}`)
};

// 硬编码的命名空间列表
const NAMESPACES = {
    REALTIME: '/realtime',
    TRADE: '/trade',
    SYMBOL_SELECT: '/symbol_select'
};

// 端口池配置
const PORT_POOL_CONFIG = config.websocket_pool || {
    start_port: 8000,
    end_port: 8100
};

// 端口状态映射
const portStatus = new Map();

// Socket.IO服务器实例映射
const ioServers = new Map();

// 初始化端口池
function initPortPool() {
    logger.info(`初始化WebSocket端口池: ${PORT_POOL_CONFIG.start_port} - ${PORT_POOL_CONFIG.end_port}`);

    // 初始化所有端口为可用状态
    for (let port = PORT_POOL_CONFIG.start_port; port <= PORT_POOL_CONFIG.end_port; port++) {
        portStatus.set(port, {
            status: 'available', // available, in_use, error
            assigned_to: null,   // 分配给哪个用户
            assigned_at: null,   // 分配时间
            last_activity: null, // 最后活动时间
            error: null          // 错误信息
        });
    }
}

/**
 * 获取可用的WebSocket端口
 * @param {string} username 用户名
 * @returns {number|null} 可用的端口号，如果没有可用端口则返回null
 */
function getAvailablePort(username) {
    // 查找第一个可用的端口
    for (let port = PORT_POOL_CONFIG.start_port; port <= PORT_POOL_CONFIG.end_port; port++) {
        const status = portStatus.get(port);
        if (status && status.status === 'available') {
            // 标记为使用中
            portStatus.set(port, {
                status: 'in_use',
                assigned_to: username,
                assigned_at: Date.now(),
                last_activity: Date.now(),
                error: null
            });

            // 启动WebSocket服务器监听该端口
            startWebSocketServer(port);

            logger.info(`为用户 ${username} 分配WebSocket端口: ${port}`);
            return port;
        }
    }

    logger.warn(`[socketHandler] 无法为用户 ${username} 分配WebSocket端口: 端口池已满`);
    return null;
}

/**
 * 释放WebSocket端口
 * @param {number} port 要释放的端口号
 * @param {string} username 用户名 (用于验证)
 * @returns {boolean} 是否成功释放
 */
function releasePort(port, username) {
    const status = portStatus.get(port);

    // 检查端口是否存在且被该用户使用
    if (!status) {
        logger.warn(`[socketHandler] 释放端口失败: 端口 ${port} 不在端口池中`);
        return false;
    }

    if (status.status !== 'in_use') {
        logger.warn(`[socketHandler] 释放端口失败: 端口 ${port} 当前不在使用中 (状态: ${status.status})`);
        return false;
    }

    if (status.assigned_to !== username) {
        logger.warn(`[socketHandler] 释放端口失败: 端口 ${port} 不属于用户 ${username} (属于: ${status.assigned_to})`);
        return false;
    }

    // 停止WebSocket服务器
    stopWebSocketServer(port);

    // 标记为可用
    portStatus.set(port, {
        status: 'available',
        assigned_to: null,
        assigned_at: null,
        last_activity: null,
        error: null
    });

    logger.info(`[socketHandler] 用户 ${username} 释放了WebSocket端口: ${port}`);
    return true;
}

/**
 * 启动Socket.IO服务器
 * @param {number} port 端口号
 */
function startWebSocketServer(port) {
    try {
        // 检查是否已经有服务器在该端口上运行
        if (ioServers.has(port)) {
            logger.warn(`[socketHandler] 端口 ${port} 已有Socket.IO服务器运行`);
            return;
        }

        // 创建HTTP服务器
        const server = http.createServer((req, res) => {
            res.writeHead(200, { 'Content-Type': 'text/plain' });
            res.end('Socket.IO Server');
        });

        // 创建Socket.IO服务器
        const io = socketIo(server, {
            cors: {
                origin: "*", // 允许所有来源，生产环境应配置具体来源
                methods: ["GET", "POST"]
            }
        });

        // 创建命名空间并设置处理器
        const namespaces = {};

        // 1. 创建实时数据命名空间 (/realtime)
        const realtimeNamespace = io.of(NAMESPACES.REALTIME);
        namespaces[NAMESPACES.REALTIME] = realtimeNamespace;
        logger.info(`端口 ${port} 创建命名空间: ${NAMESPACES.REALTIME} (实时K线数据处理)`);

        // 使用 marketHandler 的 initSocketIO 方法初始化实时数据命名空间
        if (marketHandler && typeof marketHandler.initSocketIO === 'function') {
            marketHandler.initSocketIO(realtimeNamespace);
            logger.info(`[socketHandler] 端口 ${port} 的实时数据处理器Socket.IO服务初始化完成`);
        } else {
            logger.warn(`[socketHandler] 端口 ${port} 的实时数据处理器Socket.IO服务初始化失败: marketHandler.initSocketIO 不是一个函数`);
        }

        // 更新端口状态的最后活动时间
        realtimeNamespace.on('connection', (socket) => {
            logger.debug(`[socketHandler] 端口 ${port} 命名空间 【${NAMESPACES.REALTIME}】 收到新的Socket.IO连接: ${socket.id}`);

            // 更新最后活动时间
            const status = portStatus.get(port);
            if (status) {
                status.last_activity = Date.now();
                portStatus.set(port, status);
            }
        });

        // 2. 交易命名空间 (/trade)
        const tradeNamespace = io.of(NAMESPACES.TRADE);
        namespaces[NAMESPACES.TRADE] = tradeNamespace;
        logger.info(`端口 ${port} 创建命名空间: ${NAMESPACES.TRADE} (交易处理)`);

        // 初始化交易处理器的Socket.IO服务
        // 使用 tradeHandler 导出的 tradingClients Map
        const tradeHandler = require('./tradeHandler/index');

        // 初始化交易处理器的Socket.IO服务，使用共享的 tradingClients Map
        tradeHandler.initSocketIO(tradeNamespace);
        logger.info(`端口 ${port} 的交易处理器Socket.IO服务初始化完成`);

        // 更新端口状态的最后活动时间
        tradeNamespace.on('connection', (socket) => {
            logger.debug(`[socketHandler] 端口 ${port} 命名空间 【${NAMESPACES.TRADE}】 收到新的Socket.IO连接: ${socket.id}`);

            // 更新最后活动时间
            const status = portStatus.get(port);
            if (status) {
                status.last_activity = Date.now();
                portStatus.set(port, status);
            }
        });

        // 3. 选股进度命名空间 (/symbol_select)
        const symbolSelectNamespace = io.of(NAMESPACES.SYMBOL_SELECT);
        namespaces[NAMESPACES.SYMBOL_SELECT] = symbolSelectNamespace;
        logger.info(`[socketHandler] 端口 ${port} 创建命名空间: ${NAMESPACES.SYMBOL_SELECT} (选股进度处理)`);

        // 初始化选股进度处理器的Socket.IO服务
        symbolSelectHandler.initSocketIO(symbolSelectNamespace);
        logger.info(`[socketHandler] 端口 ${port} 的【选股进度处理器】Socket.IO服务初始化完成`);

        symbolSelectNamespace.on('connection', (socket) => {
            logger.debug(`[socketHandler] 端口 ${port} 命名空间 【${NAMESPACES.SYMBOL_SELECT}】 收到新的Socket.IO连接: ${socket.id}`);

            // 更新最后活动时间
            const status = portStatus.get(port);
            if (status) {
                status.last_activity = Date.now();
                portStatus.set(port, status);
            }
        });

        // 启动服务器
        server.listen(port, () => {
            logger.info(`[socketHandler] Socket.IO服务器已在端口 ${port} 上启动`);
        });

        // 存储服务器实例
        ioServers.set(port, { server, io, namespaces });
    } catch (error) {
        logger.error(`[socketHandler] 在端口 ${port} 上启动Socket.IO服务器失败: ${error.message}`);

        // 标记端口为错误状态
        const status = portStatus.get(port);
        if (status) {
            status.status = 'error';
            status.error = error.message;
            portStatus.set(port, status);
        }
    }
}

/**
 * 停止Socket.IO服务器
 * @param {number} port 端口号
 */
function stopWebSocketServer(port) {
    try {
        const serverInfo = ioServers.get(port);
        if (!serverInfo) {
            logger.warn(`[socketHandler] 端口 ${port} 没有运行中的Socket.IO服务器`);
            return;
        }

        // 关闭Socket.IO服务器
        if (serverInfo.io) {
            // 关闭所有命名空间的所有Socket.IO连接
            if (serverInfo.namespaces) {
                Object.entries(serverInfo.namespaces).forEach(([namespacePath, namespace]) => {
                    try {
                        // 获取命名空间中的所有socket
                        const sockets = namespace.sockets || {};

                        // 断开所有连接
                        Object.keys(sockets).forEach(socketId => {
                            try {
                                const socket = sockets[socketId];
                                if (socket && socket.connected) {
                                    logger.debug(`[socketHandler] 关闭端口 ${port} 命名空间 ${namespacePath} 的Socket连接: ${socketId}`);
                                    socket.disconnect(true);
                                }
                            } catch (socketError) {
                                logger.error(`[socketHandler] 关闭端口 ${port} 命名空间 ${namespacePath} 的Socket连接 ${socketId} 时出错: ${socketError.message}`);
                            }
                        });

                        logger.info(`[socketHandler] 端口 ${port} 命名空间 ${namespacePath} 的所有连接已关闭`);
                    } catch (namespaceError) {
                        logger.error(`[socketHandler] 关闭端口 ${port} 命名空间 ${namespacePath} 时出错: ${namespaceError.message}`);
                    }
                });
            }

            // 关闭Socket.IO服务器
            try {
                serverInfo.io.close();
                logger.info(`[socketHandler] 端口 ${port} 的Socket.IO服务器已关闭`);
            } catch (ioError) {
                logger.error(`[socketHandler] 关闭端口 ${port} 的Socket.IO服务器时出错: ${ioError.message}`);
            }
        }

        // 关闭HTTP服务器
        if (serverInfo.server) {
            serverInfo.server.close(() => {
                logger.info(`[socketHandler] 端口 ${port} 的HTTP服务器已关闭`);
            });
        }

        // 移除服务器实例
        ioServers.delete(port);
    } catch (error) {
        logger.error(`[socketHandler] 关闭端口 ${port} 的Socket.IO服务器失败: ${error.message}`);
    }
}

/**
 * 清理过期的端口分配
 * 定期检查长时间未活动的端口，并释放它们
 */
function cleanupInactivePorts() {
    const now = Date.now();
    const inactivityThreshold = 30 * 60 * 1000; // 30分钟

    logger.debug('[socketHandler] 开始清理不活跃的端口分配...');

    portStatus.forEach((status, port) => {
        if (status.status === 'in_use' && status.last_activity) {
            const inactiveTime = now - status.last_activity;

            if (inactiveTime > inactivityThreshold) {
                logger.info(`端口 ${port} 已超过 ${inactivityThreshold / 60000} 分钟未活动，自动释放`);

                // 停止WebSocket服务器
                stopWebSocketServer(port);

                // 标记为可用
                portStatus.set(port, {
                    status: 'available',
                    assigned_to: null,
                    assigned_at: null,
                    last_activity: null,
                    error: null
                });
            }
        }
    });

    logger.debug('[socketHandler] 端口清理完成');
}

// 初始化端口池
initPortPool();

// 设置定期清理任务
setInterval(cleanupInactivePorts, 10 * 60 * 1000); // 每10分钟清理一次

// API路由

// 获取可用的WebSocket端口
router.post('/get_port', authenticateToken, (req, res) => {
    try {
        const username = req.user?.username;

        if (!username) {
            return res.status(401).json({
                success: false,
                message: '未授权，请先登录'
            });
        }

        const port = getAvailablePort(username);

        if (port) {
            return res.json({
                success: true,
                port: port,
                message: '成功分配WebSocket端口'
            });
        } else {
            return res.status(503).json({
                success: false,
                message: '无可用的WebSocket端口'
            });
        }
    } catch (error) {
        logger.error(`[socketHandler] 获取WebSocket端口失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 释放WebSocket端口
router.post('/release_port', authenticateToken, (req, res) => {
    try {
        const { port } = req.body;
        const username = req.user?.username;

        if (!username) {
            return res.status(401).json({
                success: false,
                message: '未授权，请先登录'
            });
        }

        if (!port) {
            return res.status(400).json({
                success: false,
                message: '缺少端口参数'
            });
        }

        const success = releasePort(parseInt(port), username);

        if (success) {
            return res.json({
                success: true,
                message: '成功释放WebSocket端口'
            });
        } else {
            return res.status(400).json({
                success: false,
                message: '释放端口失败，可能端口不存在或不属于当前用户'
            });
        }
    } catch (error) {
        logger.error(`释放WebSocket端口失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取端口池状态 (仅管理员)
router.get('/pool_status', authenticateToken, (req, res) => {
    try {
        // TODO: 添加管理员权限检查

        const status = Array.from(portStatus.entries()).map(([port, info]) => ({
            port,
            ...info,
            assigned_at: info.assigned_at ? new Date(info.assigned_at).toISOString() : null,
            last_activity: info.last_activity ? new Date(info.last_activity).toISOString() : null
        }));

        return res.json({
            success: true,
            data: status
        });
    } catch (error) {
        logger.error(`[socketHandler] 获取端口池状态失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 导出模块
module.exports = {
    router,
    getAvailablePort,
    releasePort
};