# 多因子策略引擎

## 模块描述

`portfolio`模块是一个基于WonderTrader框架的多因子选股策略引擎，支持通过配置文件定义不同的多因子策略，进行回测和实盘交易。该引擎具有高度灵活性，可以组合各种不同的因子，定义不同的标的池和调仓规则，并通过简单的配置文件实现策略的完整定义。

## 核心功能

1. **因子动态加载**：从`factors`目录动态加载各类因子计算函数，支持单一因子和复合因子
2. **灵活的配置方式**：通过YAML配置文件定义策略参数，包括标的池、因子组合、调仓周期等
3. **智能调仓机制**：使用`Rebalancer`类管理不同类型的调仓周期（日、周、月、N天等）
4. **多因子选股**：支持多因子加权打分、排序和Top-N选择
5. **全局回测控制**：支持在配置文件中定义全局回测参数，如起止时间、初始资金等
6. **结果分析返回**：提供回测结果统计和返回机制，支持与调用者交互

## 主要组件

1. **portfolio_center.py**：多因子策略核心引擎，负责策略执行和调度
2. **rebalancer.py**：调仓周期管理器，根据配置判断是否触发调仓
3. **factors/**：因子库目录，包含各类因子计算函数
4. **配置文件（*.yaml）**：策略定义文件，如`kf_roc1.yaml`、`trend_roc_vol_etf.yaml`等

## 配置文件规范

配置文件采用YAML格式，主要包括以下部分：

1. **策略基础配置**：包括策略ID、名称和路径
    ```yaml
    strategy:
      id: strategy_unique_id
      name: MultiFactorStrategy
      path: ./portfolio/portfolio_center.py
    ```

2. **策略参数**：包括标的池、因子列表、调仓周期等
    ```yaml
    params:
      universe: 
        - SSE.510300  # 沪深300ETF
        - SZSE.159915 # 创业板ETF
      factors:
        - ["roc20", 1.0]  # [因子名称, 权重]
      rebalance_interval: monthly
      top_n: 3
      weighting_scheme: equal
      data_freq: day
      bar_count: 60
    ```

3. **回测环境配置**：包括引擎类型、数据存储和回测参数
    ```yaml
    env:
      engine: WtBtEngine
      data:
        store:
          path: ../../storage/
      backtest:
        stime: 201501010930  # 回测开始时间
        etime: 202312311500  # 回测结束时间
        initial_capital: 1000000
        commission_rate: 0.0002
        slippage_rate: 0.0001
    ```

## 对外接口

模块对外提供的主要接口是"执行回测并返回结果"，通过以下方式调用：

```python
from portfolio.portfolio_center import run_backtest

# 方式1: 通过配置文件路径执行
results = run_backtest('portfolio/kf_roc1.yaml')

# 方式2: 通过配置字典执行
config = {...}  # 配置字典
results = run_backtest(config_dict=config)
```

返回的结果包括：
- 收益率曲线
- 夏普比率
- 最大回撤
- 年化收益率
- 胜率
- 其他性能指标

## 因子编写规范

每个因子文件应实现以下两个函数：

1. **calculate_value(df_bars)**：计算因子当前值
2. **calculate_series(df_bars)**：计算因子历史序列

例如：
```python
def calculate_value(df_bars):
    """计算ROC(20)因子值"""
    # 计算逻辑...
    return value

def calculate_series(df_bars):
    """计算ROC(20)因子历史序列"""
    # 计算逻辑...
    return series
```

## 全局回测控制

回测的全局控制主要通过配置文件的`env.backtest`部分实现：

```yaml
env:
  backtest:
    stime: 201501010930  # 回测开始时间
    etime: 202312311500  # 回测结束时间
    initial_capital: 1000000  # 初始资金
    commission_rate: 0.0002  # 手续费率
    slippage_rate: 0.0001  # 滑点率
```

这些参数会在回测引擎初始化时被应用，控制回测的整体行为。

## 使用示例

1. **创建因子**：在`factors`目录下创建因子文件
2. **配置策略**：创建YAML配置文件定义策略
3. **执行回测**：调用`run_backtest`函数执行回测
4. **分析结果**：处理返回的回测结果数据

## 开发注意事项

1. 因子命名应保持一致性，避免重复定义
2. 所有配置文件参数应有明确的默认值
3. 策略应支持热更新配置
4. 回测结果应包含足够的统计信息用于策略评估
5. 实盘交易时应考虑流动性和交易成本 