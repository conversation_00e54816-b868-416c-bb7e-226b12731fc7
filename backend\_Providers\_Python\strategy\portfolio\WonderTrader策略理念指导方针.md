# WonderTrader (wtpy) 策略核心理念与实践指导方针

## 1. WonderTrader 核心设计理念

WonderTrader (wtpy) 是一个强大的量化交易框架，其设计哲学深刻影响着策略的编写方式。理解其核心理念对于开发高效、可靠且易于维护的策略至关重要。

**核心理念：关注点分离 (Separation of Concerns)**

*   **引擎负责底层执行与状态跟踪**: `wtpy` 的核心引擎（无论是回测 `WtBtEngine` 还是实盘引擎）主要职责是**精确地管理订单流、模拟/执行交易、跟踪标的持仓状态 (`position`) 的变化**。它保证了交易逻辑的准确性和持仓数据的一致性。
*   **策略负责决策逻辑**: 策略 (`BaseCtaStrategy`, `BaseSelStrategy` 等) 的核心职责是**根据市场数据和内部状态，决定何时以及如何调整仓位**。策略应该专注于生成交易决策信号，而不是深入到底层账户细节的实时计算。
*   **账户权益的后处理分析**: 与某些框架不同，`wtpy` 的回测引擎**不倾向于在策略运行时实时计算并向策略 `Context` 暴露一个包含完整盈亏、手续费、滑点等细节的动态权益 (`dynamic_balance`)**。引擎内部会记录这些数据，但其主要目的是在**回测结束后**生成详细的资金曲线 (`funds.csv`)、交易记录 (`trades.csv`) 等报告文件，供后续进行全面的绩效分析（例如使用 `WtBtAnalyst` 或自定义脚本）。

**实践意义：**

*   **策略简洁性**: 策略代码可以更专注于交易信号的产生和目标状态的确定，减少了对实时账户细节管理的复杂性。
*   **回测效率**: 引擎可以专注于核心的事件驱动和持仓管理，可能提高回测速度。
*   **分析的严谨性**: 将详细的绩效分析放在回测之后，可以利用引擎完整记录的数据进行更精确和全面的评估。

## 2. 策略类型与 API 选择

`wtpy` 支持不同类型的策略，主要交互方式也随之不同：

*   **事件驱动型策略 (如单信号触发的 CTA)**:
    *   通常在满足特定条件时执行**单一、明确**的交易动作（如价格突破、指标交叉）。
    *   倾向于使用 `context.stra_enter_long()`, `context.stra_enter_short()`, `context.stra_exit_long()`, `context.stra_exit_short()` 等函数。这些函数直接表达了"买入开仓"、"卖出平仓"等具体意图。
*   **状态驱动型策略 (如资产组合再平衡)**:
    *   目标是在特定时间点将投资组合调整到一个**预设的目标状态**（哪些资产持有多少）。
    *   倾向于使用 `context.stra_set_position(code, target_units, [tag])` 函数。
        *   该函数的核心是**声明最终期望的持仓手数 `target_units`**。
        *   引擎会根据当前实际持仓自动计算需要买入或卖出的**差额**来达到目标。
        *   当 `target_units` 设置为 0 时，即表示**清空该品种的持仓**，效果等同于平仓。
        *   这种方式非常适合需要管理多个头寸并将其调整至特定比例或数量的场景。

## 3. MultiFactorsCTA (多因子组合策略) 的设计实践

本目录下的 `MultiFactorsCTA` 策略是基于上述理念设计的资产组合再平衡策略。

**核心设计原则：**

1.  **策略类型**: 定义为 CTA 策略 (`BaseCtaStrategy`)，利用 `on_calculate` 在 K 线周期结束时进行计算和调仓决策。
2.  **调仓逻辑**: 采用**增量调仓**模式，而非"全平全开"。
3.  **目标状态计算**:
    *   在调仓日 (`__is_rebalance_time__`)，根据因子得分 (`__calculate_factor_scores__`) 和预设的权重规则 (`__weighting_scheme__`, `__top_n__`) 确定目标持有的品种 (`selected_codes`) 和各自的权重 (`weights`)。
    *   **关键**: 使用策略初始化时传入的**初始资金 (`self.__initial_capital`)** 作为计算目标持仓市值的**固定基准 (`total_portfolio_value`)**。这符合 `wtpy` 不在策略运行时提供精确动态权益的设计。
    *   根据目标市值、最新价格、保证金率（期货）或每手股数（股票）计算出每个目标品种**最终应持有的手数 (`target_units_map`)**。
4.  **执行调仓**:
    *   获取当前所有实际持仓 (`context.stra_get_all_position()`)。
    *   **平仓**: 对于当前持有但不在目标持仓中（或目标手数为 0）的品种，调用 `context.stra_set_position(code, 0)` 将其目标设为 0。
    *   **调仓/开仓**: 对于目标持仓中的品种，调用 `context.stra_set_position(code, target_units)` 设置其目标手数。
5.  **引擎处理细节**: `wtpy` 回测引擎接收到 `stra_set_position` 指令后，会自动处理买卖方向和数量，以最小化交易次数达到目标持仓状态。
6.  **绩效评估**: 策略运行期间不依赖引擎提供的动态权益。回测结束后，通过分析 `outputs_bt` 目录下的 `funds.csv`, `trades.csv`, `summary.csv` 等文件来评估策略的实际收益、风险、夏普比率等指标。

## 4. 指导方针总结

在 `wtpy` 框架下开发（尤其是组合）策略时，建议遵循以下实践：

*   **明确策略类型**: 是事件驱动还是状态驱动？选择合适的 API 函数。
*   **拥抱关注点分离**: 让策略专注于决策逻辑，信任引擎处理交易执行和基础状态跟踪。
*   **理解 `stra_set_position`**: 对于再平衡策略，它是核心工具，用于声明目标状态。
*   **固定资金基准**: 在回测中计算目标仓位时，若无法或不应依赖实时动态权益，则使用初始资金作为稳定可靠的计算基准。
*   **重视后期分析**: 策略的真实表现（特别是复利效果）需要通过回测后生成的报告进行详细分析。
*   **日志清晰**: 在策略关键节点（如计算得分、确定目标、执行调仓）添加明确的日志输出 (`print` 或 `context.stra_log_text`)，便于调试和理解策略行为。

遵循这些指导方针，有助于构建出更符合 `wtpy` 设计哲学、更健壮、更易于分析的量化交易策略。 