# Market Module (市场模块)

## 模块说明
市场模块提供全市场数据的管理和分析能力，包括品种信息管理、市场扫描、数据过滤等功能。

## 目录结构
```
Market/
├── README.md           # 本文档
├── index.ts           # 模块入口，定义核心接口和市场管理器
└── scanners/          # 扫描器目录
    └── VolumeSurgeScanner.ts  # 成交量放大扫描器示例
```

## 核心接口

### MarketFilter (市场过滤条件)
```typescript
interface MarketFilter {
  market?: string;      // 市场类型
  symbol?: string;      // 品种代码
  startTime?: number;   // 开始时间
  endTime?: number;     // 结束时间
  [key: string]: any;   // 其他过滤条件
}
```

### ScanResult (扫描结果)
```typescript
interface ScanResult {
  symbol: string;       // 品种代码
  market: string;       // 市场类型
  score: number;        // 匹配分数
  data?: any;          // 附加数据
  time: number;        // 扫描时间
}
```

### MarketScanner (市场扫描器)
```typescript
interface MarketScanner {
  name: string;                    // 扫描器名称
  description: string;             // 扫描器描述
  scan(data: KLineData[]): ScanResult;  // 扫描方法
}
```

## 核心类

### MarketManager (市场管理器)
- 单例模式实现
- 管理品种信息和扫描器注册
- 提供市场扫描和数据过滤功能

主要方法：
- `getInstance()`: 获取单例实例
- `registerSymbol(symbolInfo)`: 注册品种信息
- `registerScanner(scanner)`: 注册扫描器
- `getSymbols(filter?)`: 获取符合条件的品种列表
- `scan(scannerName, filter?)`: 执行市场扫描

## 扫描器实现

### VolumeSurgeScanner (成交量放大扫描器)
- 检测成交量突然放大的品种
- 可配置计算周期和放大倍数阈值
- 返回标准化的扫描结果

## 使用示例

```typescript
// 获取市场管理器实例
const marketManager = MarketManager.getInstance();

// 注册品种信息
marketManager.registerSymbol({
  symbol: '000001.SH',
  market: 'INDEX',
  name: '上证指数'
});

// 注册扫描器
const volumeScanner = new VolumeSurgeScanner(20, 3);
marketManager.registerScanner(volumeScanner);

// 执行市场扫描
const results = await marketManager.scan('VolumeSurge', {
  market: 'INDEX'
});
```

## 扩展指南

### 添加新的扫描器
1. 在 `scanners` 目录下创建新的扫描器文件
2. 实现 `MarketScanner` 接口
3. 在扫描器中实现具体的分析逻辑
4. 通过 `MarketManager` 注册和使用

### 添加新的过滤条件
1. 在 `MarketFilter` 接口中添加新的可选字段
2. 在 `getSymbols` 方法中实现相应的过滤逻辑 