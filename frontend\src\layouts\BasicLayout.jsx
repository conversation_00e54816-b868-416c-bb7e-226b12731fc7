import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { ProLayout } from '@ant-design/pro-components';
import { HomeOutlined, DashboardOutlined, UserOutlined, LogoutOutlined, SkinOutlined } from '@ant-design/icons';
import { Modal, Dropdown, Avatar, Form, Input, Button, message, theme } from 'antd';
import { getUserInfo, removeToken } from '@/utils/auth';
import { EventBus } from '@/events/eventBus';
import { USER_EVENTS } from '@/events/userEvents';
import { useTheme } from '@/models/useTheme';

const route = {
  path: '/',
  routes: [
    {
      path: '/',
      name: '首页',
      icon: <HomeOutlined />,
    },
    {
      path: '/dashboard',
      name: '控制台',
      icon: <DashboardOutlined />,
    },
  ],
};

const BasicLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isUserInfoModalVisible, setIsUserInfoModalVisible] = useState(false);
  const [isThemeModalVisible, setIsThemeModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const userInfo = getUserInfo();
  const { theme: selectedTheme, changeTheme } = useTheme();

  useEffect(() => {
    console.log('BasicLayout装载完成');
  }, []);

  useEffect(() => {
    // 当 userInfo 变化时，头像会自动更新
    const avatar = document.querySelector('.ant-avatar');
    if (avatar) {
      console.log('顶部区域头像更新，userInfo：', userInfo);
      avatar.setAttribute('src', userInfo?.avatar ? `/uploads/avatars/${userInfo.avatar}` : '');
    }
  }, [userInfo.avatar]);  // 依赖于 userInfo

  const handleLogout = () => {
    removeToken();
    eventBus.emit(USER_EVENTS.LOGOUT, undefined);
    message.success('已退出登录');
    navigate('/login');
  };

  const handleThemeChange = (themeKey) => {
    changeTheme(themeKey);
    message.success('主题切换成功');
    setIsThemeModalVisible(false);
  };

  const userMenuItems = {
    items: [
      {
        key: 'userInfo',
        icon: <UserOutlined />,
        label: '用户信息',
        onClick: () => setIsUserInfoModalVisible(true)
      },
      {
        key: 'theme',
        icon: <SkinOutlined />,
        label: '主题设置',
        onClick: () => setIsThemeModalVisible(true)
      },
      {
        key: 'divider',
        type: 'divider'
      },
      {
        key: 'logout',
        icon: <LogoutOutlined />,
        label: '退出登录',
        onClick: handleLogout
      }
    ]
  };

  const handleUpdateUser = async (values) => {
    try {
      eventBus.emit(USER_EVENTS.UPDATE, values);
      message.success('用户信息更新成功');
      setIsEditing(false);
    } catch (error) {
      message.error('更新失败，请重试');
    }
  };

  return (
    <ProLayout
      title="股票策略回测交易平台"
      logo="/logo.svg"
      layout="mix"
      contentWidth="Fixed"
      fixedHeader
      fixSiderbar
      siderWidth={180}
      route={route}
      location={{
        pathname: location.pathname,
      }}
      menuItemRender={(item, dom) => (
        <a
          onClick={() => {
            navigate(item.path);
          }}
        >
          {dom}
        </a>
      )}
      actionsRender={() => [
        <Dropdown key="user" menu={userMenuItems} placement="bottomRight">
          <span style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <Avatar 
              src={userInfo?.avatar ? `/uploads/avatars/${userInfo.avatar}` : undefined}
              icon={!userInfo?.avatar && <UserOutlined />} 
              style={{ marginRight: 8 }}
            />
            <span>{userInfo?.username}{userInfo?.role === 'admin' ? ' (*)' : ''}</span>
          </span>
        </Dropdown>
      ]}
    >
      <Outlet />

      <Modal
        title="用户信息"
        open={isUserInfoModalVisible}
        onCancel={() => {
          setIsUserInfoModalVisible(false);
          setIsEditing(false);
        }}
        footer={[
          isEditing ? (
            <>
              <Button key="cancel" onClick={() => setIsEditing(false)}>
                取消
              </Button>
              <Button key="submit" type="primary" onClick={() => form.submit()}>
                保存
              </Button>
            </>
          ) : (
            <Button key="edit" type="primary" onClick={() => setIsEditing(true)}>
              编辑
            </Button>
          )
        ]}
      >
        {isEditing ? (
          <Form
            form={form}
            initialValues={userInfo}
            onFinish={handleUpdateUser}
            layout="vertical"
          >
            <Form.Item
              label="用户名"
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label="邮箱"
              name="email"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item label="手机号码" name="phone">
              <Input />
            </Form.Item>
          </Form>
        ) : (
          <div className="user-info-content">
            <div className="info-item">
              <label>用户名</label>
              {console.log('[用户信息] 用户角色:', userInfo?.role)}
              <span>{userInfo?.username}{userInfo?.role === 'admin' ? ' (*)' : ''}</span>
            </div>
            <div className="info-item">
              <label>邮箱</label>
              <span>{userInfo?.email}</span>
            </div>
            <div className="info-item">
              <label>手机号码</label>
              <span>{userInfo?.phone || '未设置'}</span>
            </div>
            <div className="info-item">
              <label>注册时间</label>
              <span>{userInfo?.createdAt ? new Date(userInfo.createdAt).toLocaleString() : '未知'}</span>
            </div>
          </div>
        )}
      </Modal>

      <Modal
        title="主题设置"
        open={isThemeModalVisible}
        onCancel={() => setIsThemeModalVisible(false)}
        footer={null}
      >
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '16px' }}>
          {[
            { key: 'default', name: '默认主题', color: '#1890ff' },
            { key: 'dark', name: '暗黑主题', color: '#141414' },
            { key: 'blue', name: '深蓝主题', color: '#003366' },
            { key: 'green', name: '翠绿主题', color: '#237804' },
            { key: 'purple', name: '紫色主题', color: '#722ed1' },
            { key: 'red', name: '红色主题', color: '#cf1322' },
          ].map(theme => (
            <div
              key={theme.key}
              onClick={() => handleThemeChange(theme.key)}
              style={{
                padding: '16px',
                border: `2px solid ${selectedTheme === theme.key ? theme.color : '#d9d9d9'}`,
                borderRadius: '8px',
                cursor: 'pointer',
                backgroundColor: theme.color,
                color: '#fff',
                textAlign: 'center',
                transition: 'all 0.3s'
              }}
            >
              {theme.name}
            </div>
          ))}
        </div>
      </Modal>
    </ProLayout>
  );
};

export default BasicLayout; 