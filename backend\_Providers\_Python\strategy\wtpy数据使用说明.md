标的代码的规则
期货合约代码，标准格式为CFFEX.IF.2103，其中郑商所的合约，月份也要扩展为4位

期货主力合约，标准格式为CFFEX.IF.HOT，WonderTrader会根据一个主力合约规则文件自动映射到分月合约

证券代码，股票的标准格式为SSE.STK.600000，指数的标准格式为SZSE.IDX.399001

品种合约更新
WonderTrader需要定期维护品种和合约信息，即commodities.json和contracts.json文件，来更新至最新期货品种。 对此，WonderTrader提供了ctp_loader，从simnow拉取品种合约信息。 注意：如果出现新的品种，需要自己在map_future.ini中更新相对应的键值，否则会导致更新的基础文件出现对应问题，造成运行引擎WtEngine初始化时报错！（同时需要wtpy==0.9.8）

文件配置
run.py
config.ini
map_future.ini  # 期货品种映射文件，期权对应map_futopt.ini
config.ini
[ctp]
front=tcp://180.168.146.187:10201
broker=9999
user=simnow账号
pass=simnow密码
appid=simnow_client_test
authcode=0000000000000000

[config]
path=../common/
mask=1  # 1|2|4,1-fut期货,2-opt期权,4-stk股票
mapfiles=map_future.ini,map_futopt.ini
map_future.ini
[Name]  # 代码与名称的映射
IC=中证
IF=沪深
c=玉米
a=豆一
ag=沪银

[Session]  # 品种与开收盘时间段的映射，对应common中的session.json文件
CFFEX.IC=SD0930
CFFEX.IF=SD0930
DCE.c=FN2300
SHFE.ag=FN0230
主力合约维护
主力合约映射的规则，需要每天维护，即hots.json文件，WonderTrader会根据规则自动处理映射，用户只需要使用.HOT代码就可以了。

对于主力合约规则文件的更新，WonderTrader提供了hotpicker工具，有两种方式更新：

根据datakit的落地行情更新

从交易所官网爬取（不稳定）

主力合约规则文件如下：

{
    "CFFEX": {
        "IC": [
            {
                "date": 20191018,
                "from": "IC1910",
                "newclose": 4923.6,
                "oldclose": 5028.2,
                "to": "IC1912"
            },
            {
                "date": 20191219,
                "from": "IC1912",
                "newclose": 5208.6,
                "oldclose": 5244.4,
                "to": "IC2003"
            },
            {
                "date": 20200320,
                "from": "IC2003",
                "newclose": 5099.6,
                "oldclose": 5147.4,
                "to": "IC2004"
            }
        ]
    }
}
  WonderTrader在读取主力合约的历史数据时，会优先读取直接对应的历史数据文件。如存储模式为文件时会先读取名为CFFEX.IF_HOT.dsb的文件，然后再根据主力合约规则读取分月合约的数据进行拼接。而如果存储模式为数据库，则会优先读取代码为xx.HOT的数据，然后再根据主力合约规则读取分月合约的数据。