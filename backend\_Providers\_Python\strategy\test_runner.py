import os
import sys
import json
import pprint
import sys

# 引用下级目录
script_dir = os.path.dirname(os.path.abspath(__file__))
portfolio_path = os.path.join(script_dir, 'portfolio')
sys.path.insert(0, portfolio_path)  # 临时添加路径

# 现在可以直接导入 portfolio_center.py
import portfolio_center  # type: ignore


# --- Test Execution --- 
def main():
    # Choose a strategy ID that exists in the portfolio directory
    strategy_to_test = "annual_and_r2" 
    # strategy_to_test = "kf_roc1" # Another example if it exists
    
    print(f"\n[Test Runner] === Testing run_backtest for strategy: '{strategy_to_test}' ===")
    
    try:
        # Call the run_backtest function directly
        # Ensure portfolio_center's CWD expectations are met if necessary inside run_backtest
        # Our current run_backtest in portfolio_center expects to be run from portfolio dir
        
        # Mimic the server: change CWD before calling
        print(f"[Test Runner] Changing CWD to '{portfolio_path}' before calling run_backtest...")
        original_cwd = os.getcwd()
        os.chdir(portfolio_path)
        
        result = portfolio_center.run_backtest(strategy_to_test)
        
        # Change back CWD immediately after call
        os.chdir(original_cwd)
        print(f"[Test Runner] Restored CWD to: {original_cwd}")
        
        print("\n[Test Runner] === Backtest Result ===")
        if isinstance(result, dict):
            # Pretty print the dictionary result
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print("Received non-dictionary result:")
            print(result)
            
    except Exception as e:
        print(f"\n[Test Runner] === Error During Test ===")
        print(f"An error occurred while running the test: {e}")
        import traceback
        print(traceback.format_exc())
        # Ensure CWD is restored even if error occurs
        if 'original_cwd' in locals() and os.getcwd() != original_cwd:
            os.chdir(original_cwd)
            print(f"[Test Runner] Restored CWD after error to: {original_cwd}")

if __name__ == "__main__":
    main() 