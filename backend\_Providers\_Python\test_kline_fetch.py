import sys
import os
sys.path.insert(0, os.path.dirname(__file__))
from tdxserver import fetch_kline_data

if __name__ == '__main__':
    test_cases = [
        ('sh', '510300', 'day'),
        ('sh', '600000', 'day'),
        ('sz', '000001', 'day'),
        ('sh', '510500', 'day'),
    ]
    for market, code, period in test_cases:
        print(f'测试: market={market}, code={code}, period={period}')
        data = fetch_kline_data(market, code, period, count=20, start=0)
        print(f'获取到{len(data)}条K线')
        if data:
            for row in data[:3]:
                print(row)
        print('-' * 40)