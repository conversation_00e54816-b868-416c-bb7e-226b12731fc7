# backend/_Providers/_Python/strategy/portfolio/factors/complex_score_rule.py
import pandas as pd
import numpy as np
from typing import Optional

def calculate_trend_score(series: pd.Series, period: int) -> float:
    """
    计算序列在给定周期内的线性回归斜率。
    简化版实现，假设 trend_score 是斜率。
    """
    if len(series) < period:
        return np.nan
    y = series.tail(period).values.astype(float)  # 确保类型正确
    x = np.arange(period).astype(float)  # 明确指定为float类型
    # 使用 numpy 进行线性回归计算斜率
    # polyfit 返回 [斜率, 截距]
    slope, _ = np.polyfit(x, y, 1)
    return slope

def calculate_roc(series: pd.Series, period: int) -> float:
    """计算指定周期的变化率 (Rate of Change)。"""
    if len(series) < period + 1:
        return np.nan
    current_value = series.iloc[-1]
    previous_value = series.iloc[-(period + 1)]
    if previous_value == 0: # 避免除以零
        return np.nan
    return (current_value / previous_value - 1) * 100 # 通常 ROC 以百分比表示

def calculate_ma(series: pd.Series, period: int) -> float:
    """计算简单移动平均值 (SMA)。"""
    if len(series) < period:
        return np.nan
    return series.tail(period).mean()

def calculate_value(bars: pd.DataFrame) -> float:
    """
    计算复合排序得分：
    trend_score(close, 26) * 0.4 + (roc(close, 5) + roc(close, 10)) * 0.2 + ma(volume, 5) / ma(volume, 20)
    """
    required_length = 27 # 需要足够数据计算 trend(26), roc(10), ma(20)

    if bars is None or len(bars) < required_length:
        # print(f"[信号计算:复合得分] 数据不足: {len(bars) if bars is not None else 'None'} < {required_length}")
        return np.nan # 数据不足，无法计算

    close = bars['close']
    volume = bars['volume']

    # --- 计算各个部分 ---
    trend_26 = calculate_trend_score(close, 26)
    roc_5 = calculate_roc(close, 5)
    roc_10 = calculate_roc(close, 10)
    ma_vol_5 = calculate_ma(volume, 5)
    ma_vol_20 = calculate_ma(volume, 20)

    # --- 处理计算结果中的 NaN ---
    # 如果任何一个基础指标无法计算，则总分也无法计算
    if pd.isna(trend_26) or pd.isna(roc_5) or pd.isna(roc_10) or pd.isna(ma_vol_5) or pd.isna(ma_vol_20):
        # print(f"[信号计算:复合得分] NaN检测到: t={trend_26}, r5={roc_5}, r10={roc_10}, v5={ma_vol_5}, v20={ma_vol_20}")
        return np.nan

    # --- 处理除零错误 ---
    if ma_vol_20 == 0:
        volume_ratio = 0  # 如果20日均量为0，量比因子设为0，避免除零错误
        print(f"[信号计算:复合得分] 20日均量为零，设置量比因子为0")
    else:
        volume_ratio = ma_vol_5 / ma_vol_20

    # --- 合成最终得分 ---
    try:
        score = trend_26 * 0.4 + (roc_5 + roc_10) * 0.2 + volume_ratio
        # 处理可能的无穷大值
        if np.isinf(score):
             print(f"[信号计算:复合得分] 得分计算结果为无穷大")
             return np.nan # 或者返回一个极大的数，取决于你希望如何排序这种情况
        # print(f"[信号计算:复合得分] 计算得分: {score:.4f}")
        return score
    except Exception as e:
        print(f"[信号计算:复合得分] 计算最终得分错误: {e}")
        return np.nan

# --- 可选：实现 calculate_series，如果需要卡尔曼滤波或其他基于序列的操作 ---
def calculate_series(bars: pd.DataFrame) -> Optional[pd.Series]:
    """
    计算复合排序得分的序列。
    注意：这会比较慢，因为它会对每个时间点重新计算。
    通常在策略中心我们只需要最新的 calculate_value。
    如果确实需要序列（例如用于卡尔曼滤波这个复合指标），才需要实现。
    """
    # 这是一个简化且可能低效的实现，仅为示例
    scores = pd.Series(index=bars.index, dtype=float)
    min_periods = 27 # 同上
    for i in range(min_periods -1, len(bars)):
        current_bars = bars.iloc[:i+1] # 获取到当前行的所有数据
        scores.iloc[i] = calculate_value(current_bars)
    return scores 