#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 contracts.json 位置配置
验证 contracts.json 文件是否正确位于 live_DT 目录下
"""

import os
import sys
import json

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_contracts_location():
    """测试 contracts.json 文件位置"""
    
    print("=== contracts.json 位置测试 ===\n")
    
    try:
        # 导入数据引擎管理器
        from strategy_server import DataEngineManager
        
        # 创建管理器实例
        manager = DataEngineManager()
        
        print(f"1. 数据引擎管理器配置:")
        print(f"   live_DT 目录: {manager.live_dt_dir}")
        print(f"   contracts.json 路径: {manager.contracts_file}")
        
        # 检查路径是否正确
        expected_path = os.path.join(manager.live_dt_dir, 'contracts.json')
        if manager.contracts_file == expected_path:
            print(f"   ✓ contracts.json 路径配置正确")
        else:
            print(f"   ✗ contracts.json 路径配置错误")
            print(f"     期望: {expected_path}")
            print(f"     实际: {manager.contracts_file}")
        
        # 检查文件是否存在
        print(f"\n2. 文件存在性检查:")
        if os.path.exists(manager.contracts_file):
            print(f"   ✓ contracts.json 文件存在")
            
            # 读取文件内容
            with open(manager.contracts_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                print(f"   文件内容: {content}")
                
                if content == '{}':
                    print(f"   ✓ 初始文件为空对象，符合预期")
                else:
                    print(f"   ℹ 文件包含数据: {content}")
        else:
            print(f"   ✗ contracts.json 文件不存在")
            print(f"   正在创建空文件...")
            
            # 创建空文件
            os.makedirs(os.path.dirname(manager.contracts_file), exist_ok=True)
            with open(manager.contracts_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            print(f"   ✓ 已创建空的 contracts.json 文件")
        
        # 测试更新功能
        print(f"\n3. 测试配置文件更新功能:")
        
        # 添加测试订阅
        success, changed = manager.register_project_subscription("test_project", ["SSE.600036", "SZSE.000001"])
        print(f"   注册测试订阅: 成功={success}, 变化={changed}")
        
        # 更新配置文件
        update_success = manager.update_contracts_file()
        print(f"   更新配置文件: 成功={update_success}")
        
        # 检查更新后的文件
        if os.path.exists(manager.contracts_file):
            with open(manager.contracts_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"   更新后内容: {json.dumps(config, ensure_ascii=False, indent=2)}")
                
                # 验证内容
                if config:
                    print(f"   ✓ 配置文件包含合约数据")
                    
                    # 检查是否包含预期的合约
                    found_contracts = []
                    for exchange, contracts in config.items():
                        for code, details in contracts.items():
                            found_contracts.append(f"{exchange}.{code}")
                    
                    print(f"   发现的合约: {found_contracts}")
                    
                    expected_contracts = ["SSE.600036", "SZSE.000001"]
                    for contract in expected_contracts:
                        if contract in found_contracts:
                            print(f"   ✓ 找到预期合约: {contract}")
                        else:
                            print(f"   ✗ 未找到预期合约: {contract}")
                else:
                    print(f"   ✗ 配置文件为空")
        
        # 清理测试数据
        print(f"\n4. 清理测试数据:")
        success, changed = manager.unregister_project_subscription("test_project")
        print(f"   取消测试订阅: 成功={success}, 变化={changed}")
        
        update_success = manager.update_contracts_file()
        print(f"   清理配置文件: 成功={update_success}")
        
        print(f"\n🎯 contracts.json 位置测试完成！")
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_dtcfg_yaml():
    """测试 dtcfg.yaml 配置"""
    
    print("\n=== dtcfg.yaml 配置测试 ===\n")
    
    try:
        # 检查 dtcfg.yaml 文件
        current_strategy_dir = os.path.dirname(os.path.abspath(__file__))
        live_dt_dir = os.path.join(current_strategy_dir, 'portfolio', 'live_DT')
        dtcfg_file = os.path.join(live_dt_dir, 'dtcfg.yaml')
        
        print(f"dtcfg.yaml 路径: {dtcfg_file}")
        
        if os.path.exists(dtcfg_file):
            print(f"✓ dtcfg.yaml 文件存在")
            
            # 读取配置
            import yaml
            with open(dtcfg_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查 contract 配置
            basefiles = config.get('basefiles', {})
            contract_path = basefiles.get('contract', '')
            
            print(f"配置中的 contract 路径: {contract_path}")
            
            if contract_path == 'contracts.json':
                print(f"✓ contract 路径配置正确（相对于当前目录）")
            else:
                print(f"✗ contract 路径配置可能有问题")
                print(f"  期望: contracts.json")
                print(f"  实际: {contract_path}")
            
            # 检查其他依赖文件
            dependencies = {
                'commodity': basefiles.get('commodity', ''),
                'holiday': basefiles.get('holiday', ''),
                'session': basefiles.get('session', '')
            }
            
            print(f"\n其他依赖文件配置:")
            for key, path in dependencies.items():
                print(f"  {key}: {path}")
                
                # 检查文件是否存在
                if path.startswith('../../'):
                    full_path = os.path.join(live_dt_dir, path)
                    if os.path.exists(full_path):
                        print(f"    ✓ 文件存在")
                    else:
                        print(f"    ✗ 文件不存在: {full_path}")
                else:
                    print(f"    ℹ 相对路径，跳过检查")
        else:
            print(f"✗ dtcfg.yaml 文件不存在")
        
        print(f"\n🎯 dtcfg.yaml 配置测试完成！")
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_contracts_location()
    test_dtcfg_yaml()
