/**
 * Ant Design 5 主题兼容文件
 * 
 * 在 Ant Design 5.x 中，主题系统已经从 Less 变量切换到了 CSS 变量
 * 这个文件提供了一些兼容性变量，使旧的基于 Less 的导入仍然有效
 */

// 定义 colorPalette 函数 - 这是一个简化版，实际实现可能更复杂
// Less 不支持复杂的颜色处理，所以我们使用预定义的颜色代替动态计算
.colorPaletteDefinition() {
  @preset-primary-1: #e6f7ff; // lightest
  @preset-primary-2: #bae7ff;
  @preset-primary-3: #91d5ff;
  @preset-primary-4: #69c0ff;
  @preset-primary-5: #40a9ff; // hover
  @preset-primary-6: #1890ff; // primary
  @preset-primary-7: #096dd9; // active
  @preset-primary-8: #0050b3;
  @preset-primary-9: #003a8c;
  @preset-primary-10: #002766; // darkest
}
.colorPaletteDefinition();

// 定义基础颜色变量
@primary-color: #1890ff;
@info-color: #1890ff;
@success-color: #52c41a;
@processing-color: #1890ff;
@error-color: #f5222d;
@highlight-color: #f5222d;
@warning-color: #faad14;
@normal-color: #d9d9d9;
@white: #fff;
@black: #000;

// 字体和行高
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-sm: 12px;
@heading-1-size: 38px;
@heading-2-size: 30px;
@heading-3-size: 24px;
@heading-4-size: 20px;
@heading-5-size: 16px;
@line-height-base: 1.5715;
@border-radius-base: 2px;
@border-radius-sm: 2px;

// 边框相关
@border-color-base: #d9d9d9;
@border-color-split: #f0f0f0;
@border-width-base: 1px;
@border-style-base: solid;
@outline-blur-size: 0;
@outline-width: 2px;
@outline-color: @primary-color;

// 背景颜色
@background-color-light: #f5f5f5;
@background-color-base: #f5f5f5;

// 禁用状态相关
@disabled-color: rgba(0, 0, 0, 0.25);
@disabled-bg: #f5f5f5;
@disabled-color-dark: fade(#fff, 35%);

// 阴影
@shadow-color: rgba(0, 0, 0, 0.15);
@shadow-1-up: 0 -2px 8px @shadow-color;
@shadow-1-down: 0 2px 8px @shadow-color;
@shadow-1-left: -2px 0 8px @shadow-color;
@shadow-1-right: 2px 0 8px @shadow-color;
@shadow-2: 0 4px 12px @shadow-color;
@box-shadow-base: @shadow-1-down;

// 按钮相关
@btn-font-weight: 400;
@btn-border-radius-base: @border-radius-base;
@btn-border-radius-sm: @border-radius-base;
@btn-border-width: @border-width-base;
@btn-border-style: @border-style-base;
@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);

// padding 相关变量
@padding-lg: 24px; // 大号内边距
@padding-md: 16px; // 中号内边距
@padding-sm: 12px; // 小号内边距
@padding-xs: 8px;  // 超小号内边距
@padding-xss: 4px; // 迷你内边距

// margin 相关变量
@margin-lg: 24px; // 大号外边距
@margin-md: 16px; // 中号外边距
@margin-sm: 12px; // 小号外边距
@margin-xs: 8px;  // 超小号外边距
@margin-xss: 4px; // 迷你外边距

// 额外的间距变量
@height-base: 32px;
@height-lg: 40px;
@height-sm: 24px;

// 垂直方向节点间距
@vertical-gap: 16px;
@horizontal-gap: 16px;

// Layout
@layout-body-background: #f0f2f5;
@layout-header-background: #001529;
@layout-header-height: 64px;
@layout-header-padding: 0 50px;
@layout-header-color: @text-color;
@layout-footer-padding: 24px 50px;
@layout-footer-background: @layout-body-background;
@layout-sider-background: @layout-header-background;
@layout-trigger-height: 48px;
@layout-trigger-background: #002140;
@layout-trigger-color: #fff;
@layout-zero-trigger-width: 36px;
@layout-zero-trigger-height: 42px;

// 文本颜色
@text-color: rgba(0, 0, 0, 0.65);
@text-color-secondary: rgba(0, 0, 0, 0.45);
@text-color-inverse: @white;
@icon-color: inherit;
@icon-color-hover: rgba(0, 0, 0, 0.75);
@heading-color: rgba(0, 0, 0, 0.85);
@text-color-dark: rgba(255, 255, 255, 0.85);
@text-color-secondary-dark: rgba(255, 255, 255, 0.65);

// 链接颜色 - 使用预设值替代函数调用
@link-color: @primary-color;
@link-hover-color: @preset-primary-5; // 替代 color(~`colorPalette('@{link-color}', 5) `)
@link-active-color: @preset-primary-7; // 替代 color(~`colorPalette('@{link-color}', 7) `)
@link-decoration: none;
@link-hover-decoration: none;
@link-focus-decoration: none;
@link-focus-outline: 0;

// 动画
@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);
@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);
@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);
@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);
@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);
@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);
@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);
@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);
@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);

// 动画时长
@animation-duration-slow: 0.3s;
@animation-duration-base: 0.2s;
@animation-duration-fast: 0.1s;

// 屏幕断点
@screen-xs: 480px;
@screen-xs-min: @screen-xs;
@screen-xs-max: (@screen-sm-min - 1px);
@screen-sm: 576px;
@screen-sm-min: @screen-sm;
@screen-sm-max: (@screen-md-min - 1px);
@screen-md: 768px;
@screen-md-min: @screen-md;
@screen-md-max: (@screen-lg-min - 1px);
@screen-lg: 992px;
@screen-lg-min: @screen-lg;
@screen-lg-max: (@screen-xl-min - 1px);
@screen-xl: 1200px;
@screen-xl-min: @screen-xl;
@screen-xl-max: (@screen-xxl-min - 1px);
@screen-xxl: 1600px;
@screen-xxl-min: @screen-xxl;

// Card
@card-head-color: @heading-color;
@card-head-background: transparent;
@card-head-font-size: @font-size-lg;
@card-head-font-size-sm: @font-size-base;
@card-head-padding: 16px;
@card-head-padding-sm: @card-head-padding / 2;
@card-head-height: 48px;
@card-head-height-sm: 36px;
@card-inner-head-padding: 12px;
@card-padding-base: 24px;
@card-padding-base-sm: @card-padding-base / 2;
@card-actions-background: @background-color-light;
@card-actions-li-margin: 12px 0;
@card-skeleton-bg: #cfd8dc;
@card-background: @component-background;
@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12),
  0 5px 12px 4px rgba(0, 0, 0, 0.09);
@card-radius: @border-radius-base;
@card-head-tabs-margin-bottom: -17px;
@card-head-extra-color: @text-color;

// 额外添加的组件变量
@component-background: #fff; 