/**
 * 这个文件用于解决 Vite 中的 Ant Design Less 样式问题
 */
import path from 'path';
// Ant Design 5.x 中主题文件的正确路径
const themeVarPath = path.join(process.cwd(), 'node_modules/antd/es/theme/default.js');
// 导出配置
export default {
  modifyVars: {},
  javascriptEnabled: true,
  paths: [path.resolve(process.cwd(), 'node_modules')],
  alias: {
    '@ant-design/icons/lib/dist$': path.resolve(process.cwd(), './src/icons.tsx'),
    // 为Ant Design 5.x设置正确的主题文件路径别名
    'antd/es/style/themes/default.less': path.resolve(process.cwd(), './src/theme/antd5-theme.less'),
    'antd/lib/style/themes/default.less': path.resolve(process.cwd(), './src/theme/antd5-theme.less'),
  }
}; 