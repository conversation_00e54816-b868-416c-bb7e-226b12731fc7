.symbol-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
  
  .list-content {
    flex: 1;
    overflow: auto;
    padding: 0 8px;
  }
  
  .symbollist-table {
    .symbollist-table-row {
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: var(--hover-color);
      }
    }
  }
} 