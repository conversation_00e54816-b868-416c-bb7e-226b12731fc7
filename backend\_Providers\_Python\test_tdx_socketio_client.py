#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TDX Socket.IO 客户端测试脚本
用于测试 tdxserver.py 的 Socket.IO tick 数据订阅功能
"""

import socketio
import time
import asyncio
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='[%(asctime)s] [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class TDXSocketIOClient:
    def __init__(self, server_url='http://127.0.0.1:5004'):
        self.server_url = server_url
        self.sio = socketio.Client()
        self.connected = False
        self.setup_event_handlers()
    
    def setup_event_handlers(self):
        """设置事件处理器"""
        
        @self.sio.event
        def connect():
            logger.info("已连接到 TDX Socket.IO 服务器")
            self.connected = True
        
        @self.sio.event
        def disconnect():
            logger.info("与 TDX Socket.IO 服务器断开连接")
            self.connected = False
        
        @self.sio.event
        def connected(data):
            logger.info(f"收到连接确认: {data}")
        
        @self.sio.event
        def subscribed(data):
            logger.info(f"订阅成功: {data}")
        
        @self.sio.event
        def unsubscribed(data):
            logger.info(f"取消订阅成功: {data}")
        
        @self.sio.event
        def tick_data(data):
            symbol = data.get('symbol', 'Unknown')
            tick_info = data.get('data', {})
            timestamp = data.get('timestamp', 0)
            
            logger.info(f"收到 {symbol} tick 数据:")
            logger.info(f"  价格: {tick_info.get('price', 0)}")
            logger.info(f"  成交量: {tick_info.get('volume', 0)}")
            logger.info(f"  买一价: {tick_info.get('bid1', 0)}")
            logger.info(f"  卖一价: {tick_info.get('ask1', 0)}")
            logger.info(f"  时间: {tick_info.get('time', '')}")
            logger.info(f"  时间戳: {timestamp}")
            print("-" * 50)
        
        @self.sio.event
        def error(data):
            logger.error(f"收到错误消息: {data}")
    
    def connect_to_server(self):
        """连接到服务器"""
        try:
            logger.info(f"正在连接到 {self.server_url}...")
            self.sio.connect(self.server_url, wait_timeout=10)
            
            # 等待连接建立
            timeout = 5
            start_time = time.time()
            while not self.connected and time.time() - start_time < timeout:
                time.sleep(0.1)
            
            if self.connected:
                logger.info("连接成功!")
                return True
            else:
                logger.error("连接超时")
                return False
                
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False
    
    def subscribe_symbol(self, market, symbol):
        """订阅品种"""
        if not self.connected:
            logger.error("未连接到服务器")
            return False
        
        try:
            logger.info(f"订阅品种: {market}:{symbol}")
            self.sio.emit('subscribe_tick', {
                'market': market,
                'symbol': symbol
            })
            return True
        except Exception as e:
            logger.error(f"订阅失败: {e}")
            return False
    
    def unsubscribe_symbol(self, market, symbol):
        """取消订阅品种"""
        if not self.connected:
            logger.error("未连接到服务器")
            return False
        
        try:
            logger.info(f"取消订阅品种: {market}:{symbol}")
            self.sio.emit('unsubscribe_tick', {
                'market': market,
                'symbol': symbol
            })
            return True
        except Exception as e:
            logger.error(f"取消订阅失败: {e}")
            return False
    
    def disconnect_from_server(self):
        """断开连接"""
        if self.connected:
            logger.info("正在断开连接...")
            self.sio.disconnect()
        else:
            logger.info("已经断开连接")
    
    def wait_for_data(self, duration=30):
        """等待接收数据"""
        logger.info(f"等待 {duration} 秒接收数据...")
        time.sleep(duration)

def main():
    """主函数"""
    logger.info("TDX Socket.IO 客户端测试开始")
    
    # 创建客户端
    client = TDXSocketIOClient()
    
    try:
        # 连接到服务器
        if not client.connect_to_server():
            logger.error("无法连接到服务器，退出测试")
            return
        
        # 等待一下确保连接稳定
        time.sleep(1)
        
        # 订阅一些测试品种
        test_symbols = [
            ('sh', '000001'),  # 上证指数
            ('sz', '000001'),  # 平安银行
            ('sh', '600036'),  # 招商银行
        ]
        
        logger.info("开始订阅测试品种...")
        for market, symbol in test_symbols:
            client.subscribe_symbol(market, symbol)
            time.sleep(0.5)  # 间隔一下
        
        # 等待接收数据
        client.wait_for_data(30)
        
        # 取消部分订阅
        logger.info("取消部分订阅...")
        client.unsubscribe_symbol('sh', '000001')
        
        # 再等待一段时间
        client.wait_for_data(10)
        
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    finally:
        # 断开连接
        client.disconnect_from_server()
        logger.info("TDX Socket.IO 客户端测试结束")

if __name__ == '__main__':
    main()
