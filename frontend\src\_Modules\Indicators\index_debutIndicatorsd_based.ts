import { 
  MACD as DebutMACD, 
  RSI as DebutRSI,
  BollingerBands,
  SMA,
  WMA as DebutWMA,
  Stochastic    // 导入 KD 计算器
} from '@debut/indicators';
import { EMA as DebutEMA } from './debutEma';
import { KLineData, IndicatorType } from '@/shared_types/market';
import { IndicatorLineType } from '@/shared_types/indicator';

// 将K线数据转换为指标库需要的格式
const convertKLineData = (data: KLineData[]) => {
  return data.map(item => ({
    time: item.time,
    open: item.open,
    high: item.high,
    low: item.low,
    close: item.close,
    volume: item.volume
  }));
};

// MACD指标
export const MACD = {
  type: IndicatorType.MACD,
  isMain: false,
  defaultParams: {
    fast: 12,    // 快线周期
    slow: 26,    // 慢线周期
    signal: 9    // 信号线周期
  },
  lines: [
    { name: 'macd', type: IndicatorLineType.Histogram, color: '#4CAF50' },
    { name: 'dif', type: IndicatorLineType.Line, color: '#2196F3', lineWidth: 1 },
    { name: 'dea', type: IndicatorLineType.Line, color: '#FF9800', lineWidth: 1 },
  ],
  createCalculator: (params: Record<string, number>) => {
    const { fast = 12, slow = 26, signal = 9 } = params;
    return {
      calculator: new DebutMACD(fast, slow, signal),
      lastTime: 0  // 记录上次计算的时间
    };
  },
  calculate: (calculator: any, kline: KLineData, isLastBar: boolean = false) => {
    let result;
    
    if (isLastBar) {
      result = calculator.calculator.momentValue(kline.close);
    }
    else {
      if (calculator.lastTime === 0) {
        // 首次计算
        result = calculator.calculator.nextValue(kline.close);
      } else if (kline.time === calculator.lastTime) {
        // 同一根K线更新
        result = calculator.calculator.momentValue(kline.close);
      } else if (kline.time > calculator.lastTime) {
        // 新的K线
        result = calculator.calculator.nextValue(kline.close);
      }
    }

    calculator.lastTime = kline.time;  // 更新时间
    
    if (result === undefined || !result) {
      return {
        dif: undefined,
        dea: undefined,
        macd: undefined
      };
    }

    return {
      dif: result.macd,
      dea: result.signal,
      macd: result.histogram
    };
  }
};

// RSI指标
export const RSI = {
  type: IndicatorType.RSI,
  isMain: false,
  defaultParams: {
    period: 14,   // RSI计算周期
    ma: 14,      // WMA周期
    e: 5         // EMA周期
  },
  lines: [
    { name: 'rsi', type: IndicatorLineType.Line, color: '#FF9800', lineWidth: 1 },
    { name: 'rsima', type: IndicatorLineType.Line, color: '#2196F3', lineWidth: 1 }
  ],
  createCalculator: (params: Record<string, number>) => {
    const { period = 14, ma = 14, e = 5 } = params;
    return {
      calculator: new DebutRSI(period),
      wma: new DebutWMA(ma),
      ema: new DebutEMA(e),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData, isLastBar: boolean = false) => {
    let rsiValue;
    if (isLastBar) {
      rsiValue = calculator.calculator.momentValue(kline.close);
    } else {
      if (calculator.lastTime === 0) {
        rsiValue = calculator.calculator.nextValue(kline.close);
      } else if (kline.time === calculator.lastTime) {
        rsiValue = calculator.calculator.momentValue(kline.close);
      } else if (kline.time > calculator.lastTime) {
        rsiValue = calculator.calculator.nextValue(kline.close);
      }
    }
    
    // 计算平滑RSI
    let wmaValue, rsima;
    if (rsiValue !== undefined) {
      if (isLastBar) {
        wmaValue = calculator.wma.momentValue(rsiValue);
        rsima = calculator.ema.momentValue(wmaValue);
      } else {
        if (calculator.lastTime === 0) {
          wmaValue = calculator.wma.nextValue(rsiValue);
          rsima = calculator.ema.nextValue(wmaValue);
        } else if (kline.time === calculator.lastTime) {
          wmaValue = calculator.wma.momentValue(rsiValue);
          rsima = calculator.ema.momentValue(wmaValue);
        } else if (kline.time > calculator.lastTime) {
          wmaValue = calculator.wma.nextValue(rsiValue);
          rsima = calculator.ema.nextValue(wmaValue);
        }
      }
    }
    
    calculator.lastTime = kline.time;
    return { rsi: rsiValue, rsima };
  }
};

// MA指标
export const MA = {
  type: IndicatorType.MA,
  isMain: true,
  defaultParams: {
    period: 20
  },
  lines: [
    { name: 'ma', type: IndicatorLineType.Line, color: '#2196F3' }
  ],
  createCalculator: (params: Record<string, number>) => {
    const { period = 20 } = params;
    return {
      calculator: new SMA(period),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData, isLastBar: boolean = false) => {
    let result;
    if (isLastBar) {
      result = calculator.calculator.momentValue(kline.close);
    } else {
      if (calculator.lastTime === 0) {
        result = calculator.calculator.nextValue(kline.close);
      } else if (kline.time === calculator.lastTime) {
        result = calculator.calculator.momentValue(kline.close);
      } else if (kline.time > calculator.lastTime) {
        result = calculator.calculator.nextValue(kline.close);
      }
    }
    
    calculator.lastTime = kline.time;
    
    return { ma: result };
  }
};

// EMA指标
export const EMA = {
  type: IndicatorType.EMA,
  isMain: true,
  defaultParams: {
    period: 20
  },
  lines: [
    { name: 'ema', type: IndicatorLineType.Line, color: '#FF9800' }
  ],
  createCalculator: (params: Record<string, number>) => {
    const { period = 20 } = params;
    return {
      calculator: new DebutEMA(period),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData, isLastBar: boolean = false) => {
    let result;
    if (isLastBar) {
      result = calculator.calculator.momentValue(kline.close);
    } else {
      if (calculator.lastTime === 0) {
        result = calculator.calculator.nextValue(kline.close);
      } else if (kline.time === calculator.lastTime) {
        result = calculator.calculator.momentValue(kline.close);
      } else if (kline.time > calculator.lastTime) {
        result = calculator.calculator.nextValue(kline.close);
      }
    }
    
    calculator.lastTime = kline.time;
    
    return { ema: result };
  }
};

// BOLL指标
export const BOLL = {
  type: IndicatorType.BOLL,
  isMain: true,
  defaultParams: {
    period: 20,
    band: 2
  },
  lines: [
    { name: 'middle', type: IndicatorLineType.Line, color: '#2196F3' },
    { name: 'upper', type: IndicatorLineType.Line, color: '#FF9800' },
    { name: 'lower', type: IndicatorLineType.Line, color: '#E91E63' }
  ],
  createCalculator: (params: Record<string, number>) => {
    const { period = 20, band = 2 } = params;
    return {
      calculator: new BollingerBands(period, band),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData, isLastBar: boolean = false) => {
    let result;
    if (isLastBar) {
      result = calculator.calculator.momentValue(kline.close);
    } else {
      if (calculator.lastTime === 0) {
        result = calculator.calculator.nextValue(kline.close);
      } else if (kline.time === calculator.lastTime) {
        result = calculator.calculator.momentValue(kline.close);
      } else if (kline.time > calculator.lastTime) {
        result = calculator.calculator.nextValue(kline.close);
      }
    }
    
    calculator.lastTime = kline.time;

    if (result === undefined || !result) {
      return {
        middle: undefined,
        upper: undefined,
        lower: undefined
      };
    }

    return {
      middle: result.middle,
      upper: result.upper,
      lower: result.lower
    };
  }
};

// VOL指标
export const VOL = {
  type: IndicatorType.VOL,
  isMain: false,
  defaultParams: {},
  lines: [
    { name: 'volume', type: IndicatorLineType.Histogram, color: '#4CAF50' }
  ],
  createCalculator: () => ({}),
  calculate: (calculator: any, kline: KLineData) => {
    return { volume: kline.volume };
  }
};

// KDJ指标
export const KDJ = {
  type: IndicatorType.KDJ,
  defaultParams: {
    period: 9,    // RSV计算周期
    signalPeriod: 3,  // 信号周期
  },
  lines: [
    { name: 'k', type: IndicatorLineType.Line, color: '#2196F3' },
    { name: 'd', type: IndicatorLineType.Line, color: '#FF9800' },
    { name: 'j', type: IndicatorLineType.Line, color: '#E91E63' }
  ],
  createCalculator: (params: Record<string, number>) => {
    const { period = 9, signalPeriod = 3 } = params;
    return {
      calculator: new Stochastic(period, signalPeriod),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData, isLastBar: boolean = false) => {
    let result;
    if (isLastBar) {
      result = calculator.calculator.momentValue(kline.high, kline.low, kline.close);
    } else {
      if (calculator.lastTime === 0) {
        result = calculator.calculator.nextValue(kline.high, kline.low, kline.close);
      } else if (kline.time === calculator.lastTime) {
        result = calculator.calculator.momentValue(kline.high, kline.low, kline.close);
      } else if (kline.time > calculator.lastTime) {
        result = calculator.calculator.nextValue(kline.high, kline.low, kline.close);
      }
    }
    
    calculator.lastTime = kline.time;

    // KD 计算器返回 k 和 d 值，我们计算 j 值
    if (typeof result !== 'undefined') {
      const k = result.k;
      const d = result.d;
      const j = 3 * k - 2 * d;  // J=3K-2D
    
      return { k, d, j };
    }
    else {
      return { k: undefined, d: undefined, j: undefined };
    }
  }
};

// EWMA指标
export const SWMA = {
  type: IndicatorType.SWMA,
  isMain: true,
  defaultParams: {
    wmaLength: 30,
    emaLength: 20
  },
  lines: [
    { name: 'swma', type: IndicatorLineType.Line, color: '#2196F3' }
  ],
  createCalculator: (params: Record<string, number>) => {
    const { wmaLength = 30, emaLength = 5 } = params;
    return {
      calculator: {
        wma: new DebutWMA(wmaLength),
        ema: new DebutEMA(emaLength)
      },
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData, isLastBar: boolean = false) => {
    let wmaValue, swmaValue;
    
    if (isLastBar) {
      wmaValue = calculator.calculator.wma.momentValue(kline.close);
      swmaValue = calculator.calculator.ema.momentValue(wmaValue);
    } else {
      if (calculator.lastTime === 0) {
        // 首次计算
        wmaValue = calculator.calculator.wma.nextValue(kline.close);
        swmaValue = calculator.calculator.ema.nextValue(wmaValue);
      } else if (kline.time === calculator.lastTime) {
        // 同一根K线更新
        wmaValue = calculator.calculator.wma.momentValue(kline.close);
        swmaValue = calculator.calculator.ema.momentValue(wmaValue);
      } else if (kline.time > calculator.lastTime) {
        // 新的K线
        wmaValue = calculator.calculator.wma.nextValue(kline.close);
        swmaValue = calculator.calculator.ema.nextValue(wmaValue);
      }
    }

    calculator.lastTime = kline.time;

    return { swma: swmaValue };
  }
};

// WMA指标
export const WMA = {
  type: IndicatorType.WMA,
  isMain: true,
  defaultParams: {
    period: 20
  },
  lines: [
    { name: 'wma', type: IndicatorLineType.Line, color: '#4CAF50' }
  ],
  createCalculator: (params: Record<string, number>) => {
    const { period = 20 } = params;
    return {
      calculator: new DebutWMA(period),
      lastTime: 0
    };
  },
  calculate: (calculator: any, kline: KLineData, isLastBar: boolean = false) => {
    let result;
    if (isLastBar) {
      result = calculator.calculator.momentValue(kline.close);
    } else {
      if (calculator.lastTime === 0) {
        result = calculator.calculator.nextValue(kline.close);
      } else if (kline.time === calculator.lastTime) {
        result = calculator.calculator.momentValue(kline.close);
      } else if (kline.time > calculator.lastTime) {
        result = calculator.calculator.nextValue(kline.close);
      }
    }
    
    calculator.lastTime = kline.time;
    
    return { wma: result };
  }
};